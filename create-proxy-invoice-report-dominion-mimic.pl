#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Term::ANSIColor qw( colored );
use Time::Format qw(%time %strftime %manip);
use POSIX qw/ceil/;

no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];

# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;

my @header_section;
my @header_subsection;
my @header_endsection;
my @job_section;
my @grand_total_section;
my @grand_section;
my @grand_customer_section;
my @end_of_invoice_section;
my @invoice_note_section;

my ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

my ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

my ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

my ($sale_amount, $labor_cost, $parts_sale,  $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

my ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor, $customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $customer_phone, $other_phone, $work_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $vehicle_color, $mileage_out, $license_number, $tag_no, $delivery_date, $promised, $ready, $payment_code,
     $comments, $stock_no, $ro_ext, $ro_date, $ro_status )= (0)x33;

my ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
         $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale, $prt_count)= (0)x11;

my $job_hours = 0;
my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;

my ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours,
     $cause, $correction, $billing_type, $acct ) = (0)x12;
my ($tech_name, $job_tech, $act_time, $booked_time,$work_date,$start_time,$end_time,$time_taken,$work_note) = (0)x9;
my ($customer_acc, $internal_acc, $warranty_acc) = (0)x3;

my ($parts_entries, $misc_entries, $tax_entries, $gog_entries, $sublet_entries, $ded_entries, $comments_entries) = (0)x5;
my $lbr_type;
my $page_max_height = 34; # Maximum no of lines in page body
my $page_header_height = 8; # No of lines in header section
my $invoice_dealer_header = 5; # No of lines in the dealer header section (1st page)
my $total_header_height;
my $extra_tech_line = 0;
my $curr_page_height = 0;
my $invoice_note_lines = 4; # No of lines needed for invoice note
my $pages;
my $curr_page_num;
my $invoice_datetime;
my $cust_inv_head;
my $parts_total_sale;
my $customer_total_amount=0;
my $labor_total = 0;
my $parts_total = 0;
my $gog_total = 0;
my $sub_labor_total = 0;
my $sub_parts_total = 0;
my $misc_total = 0;
my $in_total = 0;
my $total_parts = 0;
my $total_cost = 0;
my $total_misc = 0;
my $total_gog = 0;
my $total_sublet = 0;
my $total_sale_amount=0;
my $total_parts_amount=0;
my $customer_total_tax=0;
my $warranty_total_tax=0;
my $internal_total_tax=0;
my $customer_labor_cost_grand_total = 0;
my $internal_labor_cost_grand_total = 0;
my $warranty_labor_cost_grand_total = 0;
my $customer_parts_cost_grand_total = 0;
my $internal_parts_cost_grand_total = 0;
my $warranty_parts_cost_grand_total = 0;
my $customer_labor_sale_grand_total = 0;
my $internal_labor_sale_grand_total = 0;
my $warranty_labor_sale_grand_total = 0;
my $customer_parts_sale_grand_total = 0;
my $internal_parts_sale_grand_total = 0;
my $warranty_parts_sale_grand_total = 0;
my  $customer_total_misc=0;
my  $warranty_total_misc=0;
my  $internal_total_misc=0;
my  $customer_total_gog=0;
my  $warranty_total_gog=0;
my  $internal_total_gog=0;
my  $customer_total_sublet=0;
my  $warranty_total_sublet=0;
my  $internal_total_sublet=0;
my  $customer_total_pay = 0;
my  $internal_total_pay = 0;
my  $warranty_total_pay = 0;
my  $page_count = 0;
my  $page_cont = '';

my $ro_qry = $conn->prepare("SELECT ro_number
                               FROM repair_order
                              ORDER BY ro_number");

$ro_qry->execute();
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number) = @ro_list;
    my $temp_ro_number = $ro_number;
    $temp_ro_number =~ s/^0+//;
    my $file = $temp_ro_number . ".txt";
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;
    my $file_inv_head = "inv-bg/".$temp_ro_number . "-inv-bg.txt";
    unless(open FILE, '>'.$file_inv_head) {
        die "\nUnable to create $file_inv_head\n";
    }
    print_ro_inv_bg_head();
    close FILE;

    load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
   my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
   $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
    $ro_doc_insert->finish;
}
  
# Subroutine to prepare HEADER section of the invoice
sub make_header {
     my $ro_header_qry = $conn->prepare("SELECT
                                to_char(ro.open_time, 'HH24:MI') || ' ' || to_char(ro.creation_date, 'ddMONyy') AS creation_date,
                                to_char(ro.completion_date, 'mm/dd/yyyy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                ro.advisor,
                                roc.customer_number,
                                roc.customer_name,
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip,
                                roc.customer_phone,
				                roc.other_phone,
			                   coalesce(roc.work_phone,'N/A') as work_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                rov.vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in,
                                SUBSTRING(rov.vehicle_color, 1, 9) AS vehicle_color,
                                rov.mileage_out,
                                roc.license_number,
                                ro.tag_no,
                                to_char(ro.delivery_date, 'mm/dd/yyyy') AS delivery_date,
                                to_char(ro.promised_time, 'HH24:MI') || ' ' || to_char(ro.promised_date, 'ddMONyy') AS promised,
                                to_char(ro.booked_time, 'HH24:MI') || ' ' || to_char(ro.booked_date, 'ddMONyy') AS ready,
                                ro.payment_code,
                                ro.comments,
				ro_ext,
                                to_char(ro.creation_date, 'mm/dd/yy') AS ro_date,
                                 ro.status
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
     ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor, $customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $customer_phone, $other_phone, $work_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $vehicle_color, $mileage_out, $license_number, $tag_no, $delivery_date, $promised, $ready, $payment_code, $comments, 
        $ro_ext, $ro_date, $ro_status ) = @header_row;
 $cust_inv_head = $ro_ext;

	push (@header_section, sprintf(border(">").'~font{HelveticaMono2}'."%-100s"."\n", ""));    
	push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%75s%4s%6s%10s".border("|")."\n",
           "","RO: ",$ro_number,""));  
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%69s%9s%13s%10s".border("|")."\n",
           "","Cashier: ","","")); 
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%68s%10s%10s%10s".border("|")."\n",
           "","Date Out: ",$completion_date,""));  
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%70s%8s%-13s%10s".border("|")."\n",
           "","Status: ",$ro_status,"")); 
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-2s%-77s%-13s%10s".border("|")."\n",
           		"",$page_cont,"IN:".$mileage_in."OUT:".$mileage_out,""));	
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-2s%-10s%-10s%-5s%-10s%-20s%-20s".border("|")."\n",
           "","Customer:",$customer_number,"","Stock #:","","VIN:".$vehicle_vin));  
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-2s%-10s%-10s%-38s%-35s".border("|")."\n",
           "","Retail",$customer_name,"",$vehicle_year." ".$vehicle_make." ".$vehicle_model)); 
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-12s%-10s%-32s%-35s".border("|")."\n",
           "",$customer_address,"","Miles-In: ".$mileage_in." Out: ".$mileage_out));   
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-12s%-10s%-32s%-35s".border("|")."\n",
           "",$customer_city. $customer_state. $customer_zip,"","Delivered: ".$ro_date));  
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-6s%-10s%-6s%-35s%-10s".border("|")."\n",
           "","Home: ".$customer_phone,"","Work: ".$work_phone,"")); 
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-3s%-25s%-3s%-4s%-22s%-19s%-10s".border("|")."\n",
           "","Advisor: ".$advisor,"","Hat:","","Date In:".$ro_date,""));
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-3s%-25s%-55s".border("|")."\n",
           "","Sold By: ".$advisor,""));  
	push (@header_section, sprintf(border(">").'~font{HelveticaMono2}'."%-100s"."\n", "")); 
	push (@header_section, sprintf(border(">").'~font{HelveticaMono2}'."%-100s"."\n", "")); 
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-30s%-39s%-30s".border("|")."\n",
          "","****** >>> INTERNAL USE ONLY <<< ******","")); 
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-2s%-3s%-3s%-5s%-4s%-5s%-3s%-6s%-4s%-27s%-7s%-9s%-3s%15s%-2s".border("|")."\n",
          "","OP","","Acct","","Tech","","Hours","","Complaint/Cause/Correction","","Per Unit","","Extended Price",""));
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-2s%-3s%-2s%-5s%-3s%-5s%-3s%-6s%-2s%-27s%-5s%-9s%-3s%15s%-2s".border("|")."\n",
         "","==","","======","","======","","=====","","===============================","","========","","==============",""));
      

        
  
}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {
    my $job_qry = $conn->prepare("SELECT
                                    rl.ro_line,
                                    rl.complaint,
                                    rj.ro_job_line,
                                    left(rj.billing_code, 1) AS billing_code,
                                    rj.billing_code          AS billing_labor_type,
                                    rj.op_code,
                                    rj.op_description,
                                    rj.sold_hours,
                                    rj.labor_cost,
                                    rj.sale_amount,
                                    rj.cause,
                                    rj.correction,
                                    -- null as parts_cost,
                                    case when rj.billing_code='Customer' then 'CUSTOMER PAY' 
 				         else UPPER(rj.billing_code)  
				    end as billing_type,
				    case when rj.billing_code='Customer' then 'CCDCH'
                                         when rj.billing_code='Internal' then 'ILOTB'
                                         when rj.billing_code='Warranty' then 'W'
                                         when rj.billing_code='Warranty' then 'XWDRO'
                                    end as acct
                                FROM repair_line rl
                                    JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ?");

    $job_qry->execute($ro_number);

     
     while (my @job_row = $job_qry->fetchrow_array) {
        my @job_header_section;
        ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours, $labor_cost, $sale_amount, $cause, $correction, $billing_type, $acct ) = @job_row;
        push (@job_header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-35s%15s%-48s".border("|")."\n",
            "","[".$billing_type."]","")); 
        my $techs_qry = $conn->prepare("SELECT
                          tech_name,
                          tech_id,
                          actual_hours,
                          booked_hours,
	                  to_char(work_date, 'mm/dd/yy') AS work_date,
                          work_start_time,
                          work_end_time,
                          booked_hours-actual_hours as time_taken,
                          work_note
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ?");
        $techs_qry->execute($ro_number, $ro_line);
        if($techs_qry->rows > 0){
        	while (my @job_tech_row = $techs_qry->fetchrow_array) {
            		($tech_name, $job_tech, $act_time, $booked_time,$work_date,$start_time,$end_time,$time_taken,$work_note) = @job_tech_row;
        		push (@job_header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-2s%-3s%-3s%-5s%-4s%-5s%-3s%6.2f%-4s%-6s%7.2f%-4s%-6s%7.2f%-2s%-13s%8.2f".border("|")."\n",
          			"",$ro_line,"",$acct,"",$tech_name,"",$act_time,"","Cost:",$labor_cost,"","Sale:",$sale_amount,"","Labor Total:",$sale_amount));
		}  
        } 
        push (@job_section, [@job_header_section]);
         # Job Complaint
        if ($complaint){
            my @job_complaint_section;
            $Text::Wrap::columns = 61;
            my $wrapped_complaint = fill('', '', expand($complaint));
            my @complaint_list = split "\n", $wrapped_complaint;
            push (@job_complaint_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-7s%-30s".border("|")."\n",
         		"",$complaint_list[0]));
            foreach my $i (1 .. $#complaint_list) {
                push (@job_complaint_section, sprintf(border(">")."%-7s%-30s".border("|")."\n", "", $complaint_list[$i]));
            }
            push (@job_section, [@job_complaint_section]);
        }

        # Job Cause
        if ($cause){
            my @job_cause_section;
            $Text::Wrap::columns = 61;
            my $wrapped_cause = fill('', '', expand($cause));
            my @cause_list = split "\n", $wrapped_cause;
            push (@job_cause_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-7s%-30s".border("|")."\n",
         		"",$cause_list[0]));
            foreach my $i (1 .. $#cause_list) {
                push (@job_cause_section, sprintf(border(">")."%-7s%-30s".border("|")."\n", "", $cause_list[$i]));
            }
            push (@job_section, [@job_cause_section]);
        }
        
         # Job Correction
        if ($correction){
            my @job_correction_section;
            $Text::Wrap::columns = 67;
            my $wrapped_correction = fill('', '', expand($correction));
            my @correction_list = split "\n", $wrapped_correction;
            push (@job_correction_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-7s%-30s".border("|")."\n",
         		"",$correction_list[0]));
            foreach my $i (1 .. $#correction_list) {
                push (@job_correction_section, sprintf(border(">")."%-7s%-30s".border("|")."\n", "", $correction_list[$i]));
            }
            push (@job_correction_section, sprintf(border(">")."%-16s".border("|")."\n", ""));
            push (@job_section, [@job_correction_section]);
        }

	my @parts_section = make_parts_section($ro_line, $ro_job_line);
        push (@job_section, [@parts_section]);

        my @total_parts_section;
        if(($total_parts > 0)){
            push (@total_parts_section, sprintf(border(">")."%-70s%16s%11.2f".border("|")."\n", "","Total Parts:",$total_parts));
            
        }
        push (@total_parts_section, sprintf(border(">")."%-7s%-12s%-7s%-44s%-16s%11.2f".border("|")."\n", "","Technician:",$job_tech,"","Operation Total:",($total_parts + $sale_amount)));
        push (@total_parts_section, sprintf(border("#").'~font{DejaVuSansMono9}'."%-2s%-98s".'~font{default}'.border("|")."\n","", "-"x98));
        push (@job_section, [@total_parts_section]);

	
	}

}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line) = @_;
    my @parts_section_array;
    $total_parts=0;
    $total_cost=0;

    my $parts_qry = $conn->prepare("SELECT DISTINCT ON (ro_part_line)
                                        ro_part_line,
                                        part_number,
                                        part_description,
                                        left(nullif(prt_billing_type, 'NA'), 1)          AS prt_billing_code,
                                        UPPER(nullif(prt_billing_type, 'NA'))            AS prt_billing_type,
                                        quantity_sold,
                                        unit_cost,
                                        unit_sale,
                                        unit_core_cost,
                                        unit_core_sale,
                                        COUNT(*) OVER (Partition BY rp.ro_number, rp.ro_line,
                                                 rp.ro_job_line, rp.ro_part_line) AS prt_count
                                    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND quantity_sold != 0
                                    ORDER BY ro_part_line");
    $parts_qry->execute($ro_number, $ro_line);  
    $parts_entries = $parts_qry->rows;
    if($parts_entries > 0){
      while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
              $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale, $prt_count) = @parts_row;
             $Text::Wrap::columns = 20;
            my $wrapped_part_description = fill('', '', expand($part_description));
            my @part_description_list = split "\n", $wrapped_part_description;
            if($ro_part_line == 1){
            	push (@parts_section_array, sprintf(border(">")."%-7s%-7s%-2s%-4s%-2s%-16s%-2s%-20s%-2s%10.2f%-2s%10.2f%-3s%10.2f".border("|")."\n",
                     "","Parts:","",$quantity_sold,"",$part_number,"",$part_description_list[0],"",$unit_cost,"",$unit_sale,"",($unit_sale*$quantity_sold)));
            }
            else{
                push (@parts_section_array, sprintf(border(">")."%-7s%-7s%-2s%-4s%-2s%-16s%-2s%-20s%-2s%10.2f%-2s%10.2f%-3s%10.2f".border("|")."\n",
                     "","","",$quantity_sold,"",$part_number,"",$part_description_list[0],"",$unit_cost,"",$unit_sale,"",($unit_sale*$quantity_sold)));
            }
            foreach my $i (1 .. $#part_description_list) {
                push (@parts_section_array, sprintf(border(">")."%40s%-34s%-30s".border("|")."\n", "", $part_description_list[$i], ""));
            }  
             $total_parts = $total_parts +  ($unit_sale*$quantity_sold);
             $total_cost = $total_cost +  ($unit_cost*$quantity_sold);
            }
    }
    return @parts_section_array;
}


# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_grand_total_section {
        @grand_total_section='';
        @grand_section='';
        @grand_customer_section='';
        my $grand_internal_amount = 0;
        my $grand_customer_amount = 0;
        my $grand_warranty_amount = 0;

	push (@grand_total_section, sprintf(border(">")."%-90s".border("|")."\n",
                                                    ""));
	push (@grand_total_section, sprintf(border(">")."%-90s".border("|")."\n",
                                                    ""));
        push (@grand_total_section, sprintf(border(">")."%-40s%-17s%-33s".border("|")."\n",
                                                    "","** PAY SUMMARY **",""));
     my $total_qry = $conn->prepare("with cte as(
					select 
						ro_number,
						sum(quantity_sold*unit_sale) as total ,
						prt_billing_type as pay_type
					from repair_part
					group by ro_number, prt_billing_type
 					union
					select 
						ro_number,
						sum(sale_amount) as total,
						billing_code as pay_type
					from repair_job
					group by ro_number, billing_code
					)
					select 
						sum(total) as total,
						pay_type
					from cte
                                        where ro_number = ?
					group by ro_number, pay_type"
				 );
    $total_qry->execute($ro_number);  
    my $total_entries = $total_qry->rows;
    if($total_entries > 0){
      push (@grand_section, sprintf(border(">")."%-40s".border("|")."\n",
                                                    ""));
      push (@grand_section, sprintf(border(">")."%-40s".border("|")."\n",
                                                    ""));
      push (@grand_section, sprintf(border(">")."%-40s".border("|")."\n",
                                                    ""));
      while (my @total_row = $total_qry->fetchrow_array) {
            my ( $total, $pay_type) = @total_row;
     	    if($total > 0){
       		push (@grand_total_section, sprintf(border(">")."%-20s%-50s%12.2f".border("|")."\n",
                                                    "",$pay_type,$total));
                if($pay_type eq 'Customer'){
                	push (@grand_section, sprintf(border(">")."%-43s%-40s%9.2f".border("|")."\n",
                                                    "",$pay_type." Pay Miscellaneous:",$total));
                	push (@grand_customer_section, sprintf(border(">")."%-43s%-40s%9.2f".border("|")."\n",
                                                    "","Customer Pay Subtotal: ",$total));
                }
                if( $pay_type eq 'Internal'){
                   $grand_internal_amount = $grand_internal_amount + $total;
                }
                if( $pay_type eq 'Customer'){
                   $grand_customer_amount = $grand_customer_amount + $total;
                }
                if( $pay_type eq 'Warranty'){
                   $grand_warranty_amount = $grand_warranty_amount + $total;
                }
            }
      }
   }   
   push (@grand_section, sprintf(border(">")."%-43s%-40s%9s".border("|")."\n",
                                                    "","","_"x7));
   my $other_qry = $conn->prepare("select 
				       case when item_type = 'TAX' then 'Sales Tax'
                                            when item_type = 'PAYSHOP' then 'Shop Charge'
                                            when item_type = 'MISC' then 'Misc'
                                            when item_type = 'FEE' then other_description
					end as other_type,
				       other_billing_type,
				       other_sale,
                                       item_type 
				    from repair_other
                                    where ro_number = ?
				 ");
    $other_qry->execute($ro_number);  
    my $other_entries = $other_qry->rows;
    if($other_entries > 0){
      while (my @other_row = $other_qry->fetchrow_array) {
            my ($other_type, $other_billing_type, $other_sale, $item_type) = @other_row;
           if($item_type ne 'FEE'){
     	    	push (@grand_total_section, sprintf(border(">")."%-20s%-50s%12.2f".border("|")."\n",
                             "",$other_billing_type." Paid ".$other_type,$other_sale));
           }
           else{
		push (@grand_total_section, sprintf(border(">")."%-20s%-50s%12.2f".border("|")."\n",
                             "",$other_type,$other_sale));
	  }
          if($other_billing_type eq 'Customer'){
                if($item_type ne 'FEE'){
			push (@grand_customer_section, sprintf(border(">")."%-43s%-40s%9.2f".border("|")."\n",
                                                    "",$other_type,$other_sale));
                }
                else{
			push (@grand_customer_section, sprintf(border(">")."%-43s%-40s%9.2f".border("|")."\n",
                                                    "","Customer Pay ".$other_type,$other_sale));
                }
          }
          if( $other_billing_type eq 'Internal'){
              $grand_internal_amount = $grand_internal_amount + $other_sale;
          }
          if( $other_billing_type eq 'Customer'){
             $grand_customer_amount = $grand_customer_amount + $other_sale;
          }
          if( $other_billing_type eq 'Warranty'){
             $grand_warranty_amount = $grand_warranty_amount + $other_sale;
          }

     }
   }
   if( $grand_internal_amount > 0){
	push (@grand_section, sprintf(border(">")."%-2s%-50s%12.2f".border("|")."\n",
                             "","Internal Charges Total:",$grand_internal_amount));
   }
   if( $grand_warranty_amount > 0){
	push (@grand_section, sprintf(border(">")."%-2s%-50s%12.2f".border("|")."\n",
                             "","Warranty Total:",$grand_warranty_amount));
   }
    push (@grand_customer_section, sprintf(border(">")."%-43s%-40s%9s".border("|")."\n",
                                                    "","","_"x7));
   push (@grand_customer_section, sprintf(border(">")."%-2s%-12s%-29s%-40s%9.2f".border("|")."\n",
                                                    "",$payment_code.": ".$grand_customer_amount,"","Customer Total Due:",$grand_customer_amount));         
   push (@grand_customer_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-30s%-39s%-30s".border("|")."\n",
          "","",""));   
}




# Subroutine to calculate the total no of pages in the invoice
# Update page count calculation section, if any modification is made in the body of the report
sub calculate_page_count {
    
    #my $page_height = 0;
    my $tot_page_lines = 0;
    # Add page count calculation of job section
    foreach my $job_sub_sections (@job_section){
        $tot_page_lines = $tot_page_lines + scalar(@$job_sub_sections);
    }

    # Add page count calculation of other sections
    $tot_page_lines = $tot_page_lines + scalar(@grand_total_section);
    $tot_page_lines = $tot_page_lines + scalar(@grand_section);
    $tot_page_lines = $tot_page_lines + scalar(@grand_customer_section);
    $tot_page_lines = $tot_page_lines + scalar(@end_of_invoice_section);

    $page_count = ceil($tot_page_lines / $page_max_height);

    return $page_count;
}


# Print Open/Void RO
sub print_open_ro_series {
    # $num_args = scalar(@_);
    for( $a = $_[0]; $a <= $_[1]; $a = $a + 1 ) {
        printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO# ".$a." * See Open and Void RO Report *");
    }
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")."%95s".border("|")."\n", "");
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
 sub print_header_section {
    my ($page_num) = @_;
    my %hash = (pg_num => $page_num );
    
    foreach (@header_section)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
 }
sub print_dealer_header_section {
    print_blank_line($invoice_dealer_header);
 }


sub paginate_and_print_segment {
    my (@section_array) = @_;
    # Print section that fit in the current page
    if(scalar(@section_array) <= ($page_max_height - $curr_page_height)){
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{
        for my $sub_section(@section_array){
            if($curr_page_height < $page_max_height)
            {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            } else{
                print FILE get_footer(0, $curr_page_num);
                $curr_page_num = $curr_page_num + 1;
		print_dealer_header_section();
                print_header_section ($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
            }
        }
    }
}

#Subroutine to prepare the FOOTER section of the invoice

sub get_footer {
    my @footer_section;
    my ($is_end, $page_number) = @_;
    push (@footer_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
    if($is_end) {
        # no newline after the form-feed since a last newline is coming from somewhere and
        # causing the PDF document to paginate a blank page at the end.
        push (@footer_section, sprintf(border("#").'~font{DejaVuSansMono9}'.'~color{0 0 0}'."%-3s%-25s%30s%35s".border("|")."\f",
               "","",
               "****** >>> INTERNAL USE ONLY <<< ******",
               ""));
    } else {
        push (@footer_section, sprintf(border("#").'~font{DejaVuSansMono9}'.'~color{0 0 0}'."%-3s %-25s %30s%40s%2s ".border("|")."\f",
               "","",
               ">>>> CONTINUED ON NEXT PAGE <<<<",
               "",
               ""));
    }
    return @footer_section;
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
   ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

    ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

    ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

    ($sale_amount, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

    $job_hours = 0;

    ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;
   # $curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @job_section;
    ########## Prepare invoice ##########
    make_header();
    $extra_tech_line = 0;
    make_job_section();
    make_grand_total_section();
    $pages = calculate_page_count();
    ########## Print invoice ##########
    print_dealer_header_section();
    $curr_page_height = 0;
    print_header_section ($curr_page_num);

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }
    
    # Pagination of Grand total section
    paginate_and_print_segment(@grand_total_section);

    # Pagination of Grand section
    paginate_and_print_segment(@grand_section);

    # Pagination of Grand Customer section
    paginate_and_print_segment(@grand_customer_section);

    if($curr_page_height == $page_max_height){
        print FILE get_footer(1, $curr_page_num);
    } else {
        print_blank_line($page_max_height - $curr_page_height);
        print FILE get_footer(1, $curr_page_num);
    }

   }

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}


sub print_ro_inv_bg_head {
    printf(FILE $cust_inv_head);
}


sub currency_format{
#use Number::Format qw(:subs);
my $precision=2;
my $symbol='';
my ($number) = @_;
my $formatted =format_price($number, $precision, $symbol);
return '$'.$formatted;
}
