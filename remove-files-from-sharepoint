#!/bin/bash

# Array of folder names
folders=("autosoft" "adam" "automate" "cdkflex" "dealerbuilt"  "dealertrack" "dominion" "fortellis" "mpk" "pbs" "reynoldsrci" "test" "tekion" "tekionapi" "quorum")

# Base path
base_path="sharepoint:production/Scheduler"
days="91d"
log_file="/etl/log/sharepoint-scheduler-files.txt"

# Start the log
echo "-------------------------------------------" >> "$log_file"

echo "$(date)" >> "$log_file"

# Loop through each folder
for folder in "${folders[@]}"; do
    dst="$base_path/$folder/"

    echo "--------------------- $folder -----------------------" >> "$log_file"
    echo "Listing files older than $days in $dst" >> "$log_file"

    # List files older than given days
    rclone lsf "$dst" --min-age "$days" >> "$log_file"

    # Delete files (only at level 1)
    rclone delete --min-age "$days" --max-depth 1 "$dst"
done

echo "--------------------- cdk3pa -----------------------" >> "$log_file"
rclone lsf "$base_path/cdk3pa/" --min-age "$days" >> "$log_file"
rclone delete --min-age "$days" --max-depth 1 "$base_path/cdk3pa/"
echo "--------------------- analysis -----------------------" >> "$log_file"
rclone lsf "$base_path/cdk3pa/analysis-rerun/" --min-age "$days" >> "$log_file"
rclone delete --min-age "$days" --max-depth 1 "$base_path/cdk3pa/analysis-rerun/"