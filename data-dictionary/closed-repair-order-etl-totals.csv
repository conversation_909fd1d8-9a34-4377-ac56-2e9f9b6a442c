field_name,is_array,has_children
ro<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>LSE
payType,<PERSON><PERSON><PERSON>,<PERSON>LS<PERSON>
roSale,FALS<PERSON>,<PERSON>LS<PERSON>
roCost,FALS<PERSON>,<PERSON>LS<PERSON>
laborSale,FALS<PERSON>,FALS<PERSON>
laborCost,FALS<PERSON>,<PERSON><PERSON><PERSON>
laborCount,FALS<PERSON>,<PERSON><PERSON><PERSON>
laborDiscount,FALS<PERSON>,FALSE
laborSalePostDed,FALS<PERSON>,FALSE
partsSale,FALS<PERSON>,FALS<PERSON>
partsCost,FALSE,FALSE
partsCount,FALS<PERSON>,FALSE
partsDiscount,FALSE,FALSE
partsSalePostDed,FALSE,FALS<PERSON>
miscSale,FALS<PERSON>,FALS<PERSON>
miscCost,FALSE,<PERSON><PERSON><PERSON>
miscCount,FALS<PERSON>,<PERSON>LS<PERSON>
lubeSale,FALS<PERSON>,<PERSON><PERSON><PERSON>
lubeCost,FALS<PERSON>,FALS<PERSON>
lubeCount,FALS<PERSON>,FALS<PERSON>
subletSale,FALSE,FALS<PERSON>
subletCost,FALSE,FALS<PERSON>
subletCount,FALSE,<PERSON>LS<PERSON>
shopChargeSale,<PERSON>LS<PERSON>,<PERSON><PERSON><PERSON>
shopChargeCost,<PERSON>LS<PERSON>,<PERSON>LS<PERSON>
roTax,<PERSON><PERSON><PERSON>,<PERSON>LS<PERSON>
stateTax,<PERSON>LS<PERSON>,<PERSON><PERSON><PERSON>
localTax,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>
supp2Tax,<PERSON>LS<PERSON>,<PERSON><PERSON><PERSON>
supp3Tax,<PERSON>LS<PERSON>,<PERSON><PERSON>E
supp4Tax,FALSE,FALSE
actualHours,FALSE,FALSE
soldHours,FALSE,FALSE
otherHours,FALSE,FALSE
timeCardHours,FALSE,FALSE
coreCost,FALSE,FALSE
coreSale,FALSE,FALSE
discount,FALSE,FALSE
flagHours,FALSE,FALSE
forcedShopCharge,FALSE,FALSE
roSalePostDed,FALSE,FALSE
