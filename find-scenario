#!/usr/bin/env bash
#
# Provided search criteria return some basic information for all
# matching scenarios.
#

#SQL_DISTINCT_ON="DISTINCT ON (s_id)"

set -o errexit

function do_find() {
psql "$1" --set=keyinput="$3" --set=source="${2:?Source Required}" <<SQL
\echo 'Querying magescenario by key'

SELECT ${SQL_DISTINCT_ON}
       :'source' AS src, scenariokey, substring(scenarioname, 1, 25) AS scenarioname
     , CASE WHEN isactive THEN 'A' ELSE 'I' END "@"
     , (SELECT count(*) + min(numberqualified)
         FROM magescenariobuildout bo WHERE bo.scenariokey = ms.scenariokey
       ) AS "#Q"
     , (SELECT (max(endingroclosedate) - min(startingroclosedate)) + 1
         FROM magescenariobuildout bo WHERE bo.scenariokey = ms.scenariokey
       ) AS "#D"
     , ((SELECT count(*) + min(numberqualified)
         FROM magescenariobuildout bo WHERE bo.scenariokey = ms.scenariokey
       ) / --divided by months
       ((SELECT (max(endingroclosedate) - min(startingroclosedate)) + 1
         FROM magescenariobuildout bo WHERE bo.scenariokey = ms.scenariokey
       )/30.0))::integer AS "Q/M"
     , dms_id, statecode AS sc, manufacturerid AS mfg
     , datastartingro AS data_s, rangestartingro AS range_s
     , sg_name
FROM magescenario_all ms LEFT JOIN store USING (s_id) LEFT JOIN storegroup USING (sg_id)
WHERE
CASE WHEN substring(:'keyinput', 1) = '['
     THEN scenariokey = :'keyinput'
     ELSE scenariokey ~ :'keyinput'
END
AND NOT isvoid
ORDER BY s_id ASC, scenariokey DESC
;
SQL
}

function display_extents() {
psql "$1" --set=keyinput="$3" --set=source="${2:?Source Required}" --set=startat="$4" --set=endat="$5" <<SQL
\echo 'Querying magescenario by key'

SELECT :'source' AS src, scenariokey
     , CASE WHEN isactive THEN 'A' ELSE 'I' END "@"
     , (SELECT count(*)
          FROM fixedopsserviceinvoicebase invsrc
         WHERE invsrc.s_id = store.s_id
           AND recordcreationdate > (now() - '6 months'::interval)) AS "Src#"
     , lms.m_invseq_count AS "InvSeq"
     , lms.m_invseq_alt_count AS "AltSeq"
     , lms.dms_store_id AS "R#"
     , datastartingro AS data_s, dataendingro AS data_e
     , rangestartingro AS range_s, rangeendingro AS range_e
     , (SELECT min(opendate)
          FROM mageinvoicesequencemaster seq
         WHERE seq.scenariokey = ms.scenariokey
           AND seq.invoicenumber = COALESCE(NULLIF(:'startat', ''), ms.rangestartingro))::date AS "startdate-r"
     , (SELECT max(opendate)
          FROM mageinvoicesequencemaster seq
         WHERE seq.scenariokey = ms.scenariokey
           AND seq.invoicenumber = COALESCE(NULLIF(:'endat', ''), ms.rangeendingro))::date AS "enddate-r"
     , (SELECT (invoicenumber, opendate::date)
          FROM mageinvoicesequencemaster seq
         WHERE seq.scenariokey = ms.scenariokey
               AND invoicesequence = (SELECT min(startingsequence)
                                        FROM magescenariobuildout bo
                                       WHERE bo.scenariokey = ms.scenariokey)) AS earliest_qualified
FROM magescenario_all ms LEFT JOIN store USING (s_id) LEFT JOIN storegroup USING (sg_id)
LEFT JOIN (SELECT scenariokey,
                  dms_store_id,
                  m_invseq_count,
                  m_invseq_alt_count
             FROM magescenario) lms USING (scenariokey)
WHERE
CASE WHEN substring(:'keyinput', 1) = '['
     THEN scenariokey = :'keyinput'
     ELSE scenariokey ~ :'keyinput'
END
AND NOT isvoid
ORDER BY scenariokey
;
SQL
}

function list_keys() {
psql "$1" --set=keyinput="$3" --set=source="${2:?Source Required}" --no-align --tuples-only <<SQL
       SELECT scenariokey
         FROM magescenario
        WHERE CASE WHEN substring(:'keyinput', 1) = '['
                   THEN scenariokey = :'keyinput'
                   ELSE scenariokey ~ :'keyinput'
               END
               AND NOT isvoid
               -- For now, manually uncomment if you want to restrict
               -- the query to only those keys created "today"
               -- Is nice for bulk importing and then running the copy-mapping
               -- function using the keys and xargs
               -- AND recordcreationdate > (now() - '24 hours'::interval)
     ORDER BY scenariokey
;
SQL
}

: "${1:?RegExp Key Required}"

if [[ "${2:---no-key-only}" = '--key-only' ]]; then
    list_keys "service=${GGS_PROD_PG_SERVICE}" "RDS" "$1"
elif [[ "${2:---no-key-only}" = '--extents' ]]; then
    display_extents "service=${GGS_PROD_PG_SERVICE}" "RDS" "$1" "${3:-}" "${4:-}"
else
    do_find "service=${GGS_PROD_PG_SERVICE}" "RDS" "$1"
fi

exit
