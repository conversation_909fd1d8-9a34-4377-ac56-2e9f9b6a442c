Mon Mar 20 2023 09:50:56 GMT+0530 : {"Dealer ID":"techmotors_4"}:<PERSON><PERSON> started
Mon Mar 20 2023 09:50:56 GMT+0530 : {"Dealer ID":"techmotors_4"}:perform extractBundle
Mon Mar 20 2023 09:50:58 GMT+0530 : {"Dealer ID":"techmotors_4"}:bundle type is initial
Mon Mar 20 2023 09:50:58 GMT+0530 : {"Dealer ID":"techmotors_4"}:Perform performAPICall
Mon Mar 20 2023 09:50:58 GMT+0530 : {"Dealer ID":"techmotors_4"}:Ready for  Closed RO Pull
Mon Mar 20 2023 09:50:58 GMT+0530 : {}:Date batch array of closed RO pull is [["10/01/2022","10/01/2022"]] 
Mon Mar 20 2023 09:50:58 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Mar 20 2023 09:50:58 GMT+0530 : {"DealerID":"techmotors_4"}:Date batch array is:10/01/2022,10/01/2022
Mon Mar 20 2023 09:50:58 GMT+0530 : {"DealerID":"techmotors_4"}:Closed RO pull Starting start date:2022-09-30T18:30:00.000Z  End date:2022-09-30T18:30:00.000Z
Mon Mar 20 2023 09:50:58 GMT+0530 : {}:executing performROPullWithDateRange
Mon Mar 20 2023 09:50:58 GMT+0530 : {}:executing pullCloseROdata
Mon Mar 20 2023 09:50:58 GMT+0530 : {}:executing pullOpenRO
Mon Mar 20 2023 09:50:59 GMT+0530 : {"DealerID":"techmotors_4"}:Closed RO pull finished start date:2022-09-30T18:30:00.000Z  End date:2022-09-30T18:30:00.000Z
Mon Mar 20 2023 10:15:15 GMT+0530 : {"Dealer ID":"techmotors_4"}:RO Pull started
Mon Mar 20 2023 10:15:15 GMT+0530 : {"Dealer ID":"techmotors_4"}:perform extractBundle
Mon Mar 20 2023 10:15:18 GMT+0530 : {"Dealer ID":"techmotors_4"}:bundle type is initial
Mon Mar 20 2023 10:15:18 GMT+0530 : {"Dealer ID":"techmotors_4"}:Perform performAPICall
Mon Mar 20 2023 10:15:18 GMT+0530 : {"Dealer ID":"techmotors_4"}:Ready for  Closed RO Pull
Mon Mar 20 2023 10:15:18 GMT+0530 : {}:Date batch array of closed RO pull is [["10/01/2022","10/01/2022"]] 
Mon Mar 20 2023 10:15:18 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Mar 20 2023 10:15:18 GMT+0530 : {"DealerID":"techmotors_4"}:Date batch array is:10/01/2022,10/01/2022
Mon Mar 20 2023 10:15:18 GMT+0530 : {"DealerID":"techmotors_4"}:Closed RO pull Starting start date:2022-09-30T18:30:00.000Z  End date:2022-09-30T18:30:00.000Z
Mon Mar 20 2023 10:15:18 GMT+0530 : {}:executing performROPullWithDateRange
Mon Mar 20 2023 10:15:18 GMT+0530 : {}:executing pullCloseROdata
Mon Mar 20 2023 10:15:18 GMT+0530 : {}:executing pullOpenRO
Mon Mar 20 2023 10:24:27 GMT+0530 : {"Dealer ID":"techmotors_4"}:RO Pull started
Mon Mar 20 2023 10:24:27 GMT+0530 : {"Dealer ID":"techmotors_4"}:perform extractBundle
Mon Mar 20 2023 10:24:29 GMT+0530 : {"Dealer ID":"techmotors_4"}:bundle type is initial
Mon Mar 20 2023 10:24:29 GMT+0530 : {"Dealer ID":"techmotors_4"}:Perform performAPICall
Mon Mar 20 2023 10:24:29 GMT+0530 : {"Dealer ID":"techmotors_4"}:Ready for  Closed RO Pull
Mon Mar 20 2023 10:24:29 GMT+0530 : {}:Date batch array of closed RO pull is [["10/01/2022","10/01/2022"]] 
Mon Mar 20 2023 10:24:29 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Mar 20 2023 10:24:29 GMT+0530 : {"DealerID":"techmotors_4"}:Date batch array is:10/01/2022,10/01/2022
Mon Mar 20 2023 10:24:29 GMT+0530 : {"DealerID":"techmotors_4"}:Closed RO pull Starting start date:2022-09-30T18:30:00.000Z  End date:2022-09-30T18:30:00.000Z
Mon Mar 20 2023 10:24:29 GMT+0530 : {}:executing performROPullWithDateRange
Mon Mar 20 2023 10:24:29 GMT+0530 : {}:executing pullCloseROdata
Mon Mar 20 2023 10:24:29 GMT+0530 : {}:executing pullOpenRO
Mon Mar 20 2023 10:28:38 GMT+0530 : {"Dealer ID":"techmotors_4"}:RO Pull started
Mon Mar 20 2023 10:28:38 GMT+0530 : {"Dealer ID":"techmotors_4"}:perform extractBundle
Mon Mar 20 2023 10:28:40 GMT+0530 : {"Dealer ID":"techmotors_4"}:bundle type is initial
Mon Mar 20 2023 10:28:40 GMT+0530 : {"Dealer ID":"techmotors_4"}:Perform performAPICall
Mon Mar 20 2023 10:28:40 GMT+0530 : {"Dealer ID":"techmotors_4"}:Ready for  Closed RO Pull
Mon Mar 20 2023 10:28:40 GMT+0530 : {}:Date batch array of closed RO pull is [["10/01/2022","10/01/2022"]] 
Mon Mar 20 2023 10:28:40 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Mar 20 2023 10:28:40 GMT+0530 : {"DealerID":"techmotors_4"}:Date batch array is:10/01/2022,10/01/2022
Mon Mar 20 2023 10:28:40 GMT+0530 : {"DealerID":"techmotors_4"}:Closed RO pull Starting start date:2022-09-30T18:30:00.000Z  End date:2022-09-30T18:30:00.000Z
Mon Mar 20 2023 10:28:40 GMT+0530 : {}:executing performROPullWithDateRange
Mon Mar 20 2023 10:28:40 GMT+0530 : {}:executing pullCloseROdata
Mon Mar 20 2023 10:28:40 GMT+0530 : {}:executing pullOpenRO
