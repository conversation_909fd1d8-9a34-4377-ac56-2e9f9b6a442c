Mon Apr 22 2024 18:59:00 GMT+0530 : {"Dealer ID":"12345"}:<PERSON><PERSON><PERSON> started
Mon Apr 22 2024 18:59:00 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 18:59:02 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 18:59:02 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 18:59:02 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 18:59:02 GMT+0530 : undefined:executing pullCloseROdata
Mon Apr 22 2024 18:59:02 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:00:41 GMT+0530 : {"Dealer ID":"12345"}:RO <PERSON>ull started
Mon Apr 22 2024 19:00:41 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:00:42 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:00:42 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:00:42 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 19:00:42 GMT+0530 : undefined:executing pullCloseROdata
Mon Apr 22 2024 19:00:42 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:02:54 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:02:54 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:02:56 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:02:56 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:02:56 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 19:02:56 GMT+0530 : undefined:executing pullCloseROdata
Mon Apr 22 2024 19:02:56 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:05:48 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:05:48 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:05:49 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:05:49 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:05:49 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 19:05:49 GMT+0530 : undefined:executing pullCloseROdata
Mon Apr 22 2024 19:05:49 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:07:49 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:07:49 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:07:50 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:07:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:07:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 19:07:50 GMT+0530 : undefined:executing pullCloseROdata
Mon Apr 22 2024 19:07:50 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:10:13 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:10:13 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:10:15 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:10:15 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:10:15 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 19:10:15 GMT+0530 : undefined:executing pullCloseROdata
Mon Apr 22 2024 19:10:15 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:11:10 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:11:10 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:11:12 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:11:12 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:11:12 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 19:11:12 GMT+0530 : undefined:executing pullCloseROdata
Mon Apr 22 2024 19:11:12 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:18:20 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:18:20 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:18:22 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:18:22 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:18:22 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Mon Apr 22 2024 19:18:22 GMT+0530 : undefined:executing make model
Mon Apr 22 2024 19:18:22 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:21:49 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:21:49 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:21:50 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:21:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:21:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Mon Apr 22 2024 19:21:50 GMT+0530 : undefined:executing make model
Mon Apr 22 2024 19:21:50 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:24:05 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:24:05 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:24:07 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:24:07 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:24:07 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:24:07 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:24:07 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:24:07 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:24:07 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:24:07 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:24:07 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:24:09 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:24:09 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:24:09 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:24:09 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:24:09 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:24:10 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:24:10 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:24:10 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:24:10 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:24:10 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:24:11 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:24:11 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:24:11 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:24:11 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:24:11 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:24:12 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:24:12 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:24:12 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:24:12 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:24:12 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:24:13 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:35:44 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:35:44 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:35:45 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:35:45 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:35:45 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:35:45 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:35:45 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:35:45 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:35:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:35:45 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:35:45 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:35:47 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:35:47 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:35:47 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:35:47 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:35:47 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:35:48 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:35:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:35:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:35:48 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:35:48 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:35:50 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:35:50 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:35:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:35:50 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:35:50 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:35:52 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:35:52 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:35:52 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:35:52 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:35:52 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:35:53 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:40:37 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:40:37 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:40:38 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:40:38 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:40:38 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:40:38 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:40:38 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:40:38 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:40:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:40:38 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:40:38 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:40:42 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:40:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:40:42 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:40:42 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:40:42 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:40:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:40:42 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:40:42 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:40:43 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:40:43 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:40:43 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:40:43 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:40:44 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:40:44 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:40:44 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:40:44 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:41:47 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:41:47 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:41:48 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:41:48 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:41:49 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:41:49 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:41:49 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:41:49 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:41:49 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:41:49 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:41:49 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:41:51 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:41:51 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:41:51 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:41:51 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:41:52 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:41:52 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:41:52 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:41:52 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:41:53 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:41:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:41:53 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:41:53 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:41:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:41:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:41:54 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:41:54 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:42:45 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:42:45 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:42:46 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:42:46 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:42:46 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:42:46 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:42:46 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:42:46 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:42:46 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:42:46 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:42:46 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:42:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:42:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:42:48 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:42:48 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:42:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:42:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:42:48 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:42:48 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:42:49 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:42:49 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:42:49 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:42:49 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:42:50 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:42:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:42:50 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:42:50 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:45:06 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:45:06 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:45:08 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:45:08 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:45:08 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:45:08 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:45:08 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:45:08 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:45:08 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:45:08 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:45:08 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:45:10 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:45:10 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:45:10 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:45:10 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:45:11 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:45:11 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:45:11 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:45:11 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:45:12 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:45:12 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:45:12 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:45:12 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:45:13 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:45:13 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:45:13 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:45:13 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:46:43 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:46:43 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:46:45 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:46:45 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:46:45 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:46:45 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:46:45 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:46:45 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:46:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:46:45 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:46:45 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:46:46 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:46:46 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:46:46 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:46:46 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:46:47 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:46:47 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:46:47 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:46:47 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:46:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:46:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:46:48 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:46:48 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:46:50 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:46:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:46:50 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:46:50 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:48:19 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:48:19 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:48:21 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:48:21 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:48:21 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:48:21 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:48:21 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:48:21 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:48:21 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:48:21 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:48:21 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:48:23 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:48:23 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:48:23 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:48:23 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:48:24 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:48:24 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:48:24 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:48:24 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:48:25 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:48:25 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:48:25 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:48:25 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:48:26 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:48:26 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:48:26 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:48:26 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:49:09 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:49:09 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:49:10 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:49:10 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:49:10 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:49:11 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:49:11 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:49:11 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:49:11 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:49:11 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:49:11 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:49:12 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:49:12 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:49:12 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:49:12 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:49:13 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:49:13 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:49:13 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:49:13 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:49:14 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:49:14 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:49:14 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:49:14 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:49:15 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:49:15 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:49:15 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:49:15 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:49:16 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:49:16 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 19:49:16 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:49:16 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:49:17 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:49:17 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Mon Apr 22 2024 19:49:17 GMT+0530 : {}:executing make model
Mon Apr 22 2024 19:49:17 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:50:06 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:50:06 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:50:08 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:50:08 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:50:08 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Mon Apr 22 2024 19:50:08 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:50:08 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 22 2024 19:50:08 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:50:08 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:50:08 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 22 2024 19:50:08 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:50:53 GMT+0530 : {"StoreID":"01b3d73c-0904-8fda-005f-0d03092c8aa2"}:executing pullOpenRO
Mon Apr 22 2024 19:50:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:50:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:50:54 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 22 2024 19:50:54 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:50:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:51:37 GMT+0530 : {"StoreID":"01b3d73c-0904-8fda-005f-0d03092c8abe"}:executing pullOpenRO
Mon Apr 22 2024 19:51:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:51:38 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:51:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:51:38 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 22 2024 19:51:38 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:52:20 GMT+0530 : {"StoreID":"01b3d73d-0904-9201-005f-0d03092c9462"}:executing pullOpenRO
Mon Apr 22 2024 19:52:22 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:52:22 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:52:22 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:52:22 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 22 2024 19:52:22 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:53:04 GMT+0530 : {"StoreID":"01b3d73e-0904-9201-005f-0d03092c948a"}:executing pullOpenRO
Mon Apr 22 2024 19:53:05 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:53:05 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:53:05 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:53:05 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 22 2024 19:53:05 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:53:47 GMT+0530 : {"StoreID":"01b3d73f-0904-9201-005f-0d03092c94b2"}:executing pullOpenRO
Mon Apr 22 2024 19:53:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:53:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:53:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 19:53:50 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 19:53:50 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 19:53:50 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:53:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 19:53:50 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:53:50 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:53:51 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:53:51 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 19:53:51 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:53:51 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:53:53 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:53:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 19:53:53 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:53:53 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:53:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:53:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 19:53:54 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:53:54 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:53:56 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 19:53:56 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 19:53:56 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 19:53:56 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:53:57 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:53:57 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 19:53:57 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 19:53:57 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:53:58 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:53:58 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Mon Apr 22 2024 19:53:58 GMT+0530 : {}:executing make model
Mon Apr 22 2024 19:53:58 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 19:56:31 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 19:56:31 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 19:56:33 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 19:56:33 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 19:56:33 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Mon Apr 22 2024 19:56:33 GMT+0530 : undefined:executing pullCloseROdata
Mon Apr 22 2024 19:56:33 GMT+0530 : {}:executing pullOpenRO
Mon Apr 22 2024 20:00:42 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:00:42 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:00:44 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:00:44 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:00:44 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:00:44 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:00:44 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:00:44 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:00:44 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:00:44 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:00:44 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:00:46 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dhhos5oVFq8mKDamr-0yxDhCJyyU2PbQ416m6Vf0vq7yW8VBEgVzbkjnJskbAMbiK9dIAe_6ohiapLDVsXRz3VDnnogA1vddp-14iB1-X1qa6TbiTF0ubk0qVPtAg9pcxe4DpYm67cXp5tt0_MalUTecbJBA82XaBQh1k2ymeDPPgU_kWojx1PW93CvsPWy-4v5QUjkI4XsrmoINTO0kZSqmPDulr5mb5KCfT-vTo9ZOgHmDw1If1IqiKiy8i2M8T5G7BsDWsP5Yl49GJ7yMOfhEQ3WOM6b_lR-azATQoMo4BS0q5hV53l61UFUQl33U2L7rIBIVPgfdh3iJU5UNiQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:00:46 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:00:46 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:00:46 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:00:46 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:00:47 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dhhos5oVFq8mKDamr-0yxDhCJyyU2PbQ416m6Vf0vq7yW8VBEgVzbkjnJskbAMbiK9dIAe_6ohiapLDVsXRz3VDnnogA1vddp-14iB1-X1qa6TbiTF0ubk0qVPtAg9pcxe4DpYm67cXp5tt0_MalUTecbJBA82XaBQh1k2ymeDPPgU_kWojx1PW93CvsPWy-4v5QUjkI4XsrmoINTO0kZSqmPDulr5mb5KCfT-vTo9ZOgHmDw1If1IqiKiy8i2M8T5G7BsDWsP5Yl49GJ7yMOfhEQ3WOM6b_lR-azATQoMo4BS0q5hV53l61UFUQl33U2L7rIBIVPgfdh3iJU5UNiQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:00:47 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:00:47 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:00:47 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:00:47 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:00:48 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dhhos5oVFq8mKDamr-0yxDhCJyyU2PbQ416m6Vf0vq7yW8VBEgVzbkjnJskbAMbiK9dIAe_6ohiapLDVsXRz3VDnnogA1vddp-14iB1-X1qa6TbiTF0ubk0qVPtAg9pcxe4DpYm67cXp5tt0_MalUTecbJBA82XaBQh1k2ymeDPPgU_kWojx1PW93CvsPWy-4v5QUjkI4XsrmoINTO0kZSqmPDulr5mb5KCfT-vTo9ZOgHmDw1If1IqiKiy8i2M8T5G7BsDWsP5Yl49GJ7yMOfhEQ3WOM6b_lR-azATQoMo4BS0q5hV53l61UFUQl33U2L7rIBIVPgfdh3iJU5UNiQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:00:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:00:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:00:48 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:00:48 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:00:49 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dhhos5oVFq8mKDamr-0yxDhCJyyU2PbQ416m6Vf0vq7yW8VBEgVzbkjnJskbAMbiK9dIAe_6ohiapLDVsXRz3VDnnogA1vddp-14iB1-X1qa6TbiTF0ubk0qVPtAg9pcxe4DpYm67cXp5tt0_MalUTecbJBA82XaBQh1k2ymeDPPgU_kWojx1PW93CvsPWy-4v5QUjkI4XsrmoINTO0kZSqmPDulr5mb5KCfT-vTo9ZOgHmDw1If1IqiKiy8i2M8T5G7BsDWsP5Yl49GJ7yMOfhEQ3WOM6b_lR-azATQoMo4BS0q5hV53l61UFUQl33U2L7rIBIVPgfdh3iJU5UNiQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:00:49 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:00:49 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:00:49 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:00:49 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:00:50 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dhhos5oVFq8mKDamr-0yxDhCJyyU2PbQ416m6Vf0vq7yW8VBEgVzbkjnJskbAMbiK9dIAe_6ohiapLDVsXRz3VDnnogA1vddp-14iB1-X1qa6TbiTF0ubk0qVPtAg9pcxe4DpYm67cXp5tt0_MalUTecbJBA82XaBQh1k2ymeDPPgU_kWojx1PW93CvsPWy-4v5QUjkI4XsrmoINTO0kZSqmPDulr5mb5KCfT-vTo9ZOgHmDw1If1IqiKiy8i2M8T5G7BsDWsP5Yl49GJ7yMOfhEQ3WOM6b_lR-azATQoMo4BS0q5hV53l61UFUQl33U2L7rIBIVPgfdh3iJU5UNiQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:05:32 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:05:32 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:05:34 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:05:34 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:05:34 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:05:34 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:05:34 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:05:34 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:05:34 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:05:34 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:05:34 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:05:36 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YxOvGaJqNe2-DEH5GzzGA1W6-A7WiVyIwfvUwiRf8GIFQ-AGTAIBAfKScorBjcRxSaWtM3UG0t7e8Bg7QX_WUHPCW-KE2uHxaZm3jtvkTpRcV-HNCgjEP91wD9VwEcDVDB_bLjnkcDr-gqG_IFIFhV02Hsv3FmEi_-sKlemDmqHdHmHjrIK-aKKhj4pldSlCBGGR1a7o4-TXAxeiDCiodyu2ZaBmkhjIFot8l82rEIdRNeTjrlX_WxWKb9sRmtUXvVbY1gEBd4L-33qYKxHNBxijdtgaRLx_l-_iB7EPLH31KuElAa5frnwos8EAdi_-nz-4DLlr7D-D1mKW-baHTA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:05:36 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:05:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:05:36 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:05:36 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:05:36 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YxOvGaJqNe2-DEH5GzzGA1W6-A7WiVyIwfvUwiRf8GIFQ-AGTAIBAfKScorBjcRxSaWtM3UG0t7e8Bg7QX_WUHPCW-KE2uHxaZm3jtvkTpRcV-HNCgjEP91wD9VwEcDVDB_bLjnkcDr-gqG_IFIFhV02Hsv3FmEi_-sKlemDmqHdHmHjrIK-aKKhj4pldSlCBGGR1a7o4-TXAxeiDCiodyu2ZaBmkhjIFot8l82rEIdRNeTjrlX_WxWKb9sRmtUXvVbY1gEBd4L-33qYKxHNBxijdtgaRLx_l-_iB7EPLH31KuElAa5frnwos8EAdi_-nz-4DLlr7D-D1mKW-baHTA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:05:36 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:05:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:05:36 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:05:36 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:05:37 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YxOvGaJqNe2-DEH5GzzGA1W6-A7WiVyIwfvUwiRf8GIFQ-AGTAIBAfKScorBjcRxSaWtM3UG0t7e8Bg7QX_WUHPCW-KE2uHxaZm3jtvkTpRcV-HNCgjEP91wD9VwEcDVDB_bLjnkcDr-gqG_IFIFhV02Hsv3FmEi_-sKlemDmqHdHmHjrIK-aKKhj4pldSlCBGGR1a7o4-TXAxeiDCiodyu2ZaBmkhjIFot8l82rEIdRNeTjrlX_WxWKb9sRmtUXvVbY1gEBd4L-33qYKxHNBxijdtgaRLx_l-_iB7EPLH31KuElAa5frnwos8EAdi_-nz-4DLlr7D-D1mKW-baHTA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:05:37 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:05:37 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:05:37 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:05:37 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:05:38 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YxOvGaJqNe2-DEH5GzzGA1W6-A7WiVyIwfvUwiRf8GIFQ-AGTAIBAfKScorBjcRxSaWtM3UG0t7e8Bg7QX_WUHPCW-KE2uHxaZm3jtvkTpRcV-HNCgjEP91wD9VwEcDVDB_bLjnkcDr-gqG_IFIFhV02Hsv3FmEi_-sKlemDmqHdHmHjrIK-aKKhj4pldSlCBGGR1a7o4-TXAxeiDCiodyu2ZaBmkhjIFot8l82rEIdRNeTjrlX_WxWKb9sRmtUXvVbY1gEBd4L-33qYKxHNBxijdtgaRLx_l-_iB7EPLH31KuElAa5frnwos8EAdi_-nz-4DLlr7D-D1mKW-baHTA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:05:38 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:05:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:05:38 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:05:38 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:05:39 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YxOvGaJqNe2-DEH5GzzGA1W6-A7WiVyIwfvUwiRf8GIFQ-AGTAIBAfKScorBjcRxSaWtM3UG0t7e8Bg7QX_WUHPCW-KE2uHxaZm3jtvkTpRcV-HNCgjEP91wD9VwEcDVDB_bLjnkcDr-gqG_IFIFhV02Hsv3FmEi_-sKlemDmqHdHmHjrIK-aKKhj4pldSlCBGGR1a7o4-TXAxeiDCiodyu2ZaBmkhjIFot8l82rEIdRNeTjrlX_WxWKb9sRmtUXvVbY1gEBd4L-33qYKxHNBxijdtgaRLx_l-_iB7EPLH31KuElAa5frnwos8EAdi_-nz-4DLlr7D-D1mKW-baHTA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:11:56 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:11:56 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:11:58 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:11:58 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:11:58 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:11:58 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:11:58 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:11:58 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:11:58 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:11:58 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:11:58 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:03 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"404","message":"Invalid Subscription-Id, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ScvAL3vGDqN3NloEQbFNCVNwazsCnwLHRgVNHVkwk9JYutCcfVq3tXfFgwYl9K0c8qwRy7VHWYDsqVRThfCcs4y-M6vAmMCeYkU7g3zJdFLlpwNzcsteV5ZGmYuftMdlECkLdhN_zCj0xhWvUgJAVLbsyYKk4ivqKlHnrwwU30O0-wntqSPL_dzLieqylVXSkFIvbNRTMQBIZ01O-RhfT5t7XGASjwlNeiPpLWvwmrxyAkForhQk_TXr0f-4xsDl3KffwDKMLEISwgJ1YOwUUg-0gks9aCvM43FbEdJNMnJ_XdjgXymeAIz7MJKUU_HYvPYZE1XHEBCL5CRf_5zfdA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:12:03 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:12:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:12:03 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:12:03 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:09 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"404","message":"Invalid Subscription-Id, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ScvAL3vGDqN3NloEQbFNCVNwazsCnwLHRgVNHVkwk9JYutCcfVq3tXfFgwYl9K0c8qwRy7VHWYDsqVRThfCcs4y-M6vAmMCeYkU7g3zJdFLlpwNzcsteV5ZGmYuftMdlECkLdhN_zCj0xhWvUgJAVLbsyYKk4ivqKlHnrwwU30O0-wntqSPL_dzLieqylVXSkFIvbNRTMQBIZ01O-RhfT5t7XGASjwlNeiPpLWvwmrxyAkForhQk_TXr0f-4xsDl3KffwDKMLEISwgJ1YOwUUg-0gks9aCvM43FbEdJNMnJ_XdjgXymeAIz7MJKUU_HYvPYZE1XHEBCL5CRf_5zfdA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:12:09 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:12:09 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:12:09 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:12:09 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:15 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"404","message":"Invalid Subscription-Id, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ScvAL3vGDqN3NloEQbFNCVNwazsCnwLHRgVNHVkwk9JYutCcfVq3tXfFgwYl9K0c8qwRy7VHWYDsqVRThfCcs4y-M6vAmMCeYkU7g3zJdFLlpwNzcsteV5ZGmYuftMdlECkLdhN_zCj0xhWvUgJAVLbsyYKk4ivqKlHnrwwU30O0-wntqSPL_dzLieqylVXSkFIvbNRTMQBIZ01O-RhfT5t7XGASjwlNeiPpLWvwmrxyAkForhQk_TXr0f-4xsDl3KffwDKMLEISwgJ1YOwUUg-0gks9aCvM43FbEdJNMnJ_XdjgXymeAIz7MJKUU_HYvPYZE1XHEBCL5CRf_5zfdA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:12:15 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:12:15 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:12:15 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:12:15 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:36 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"404","message":"Invalid Subscription-Id, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ScvAL3vGDqN3NloEQbFNCVNwazsCnwLHRgVNHVkwk9JYutCcfVq3tXfFgwYl9K0c8qwRy7VHWYDsqVRThfCcs4y-M6vAmMCeYkU7g3zJdFLlpwNzcsteV5ZGmYuftMdlECkLdhN_zCj0xhWvUgJAVLbsyYKk4ivqKlHnrwwU30O0-wntqSPL_dzLieqylVXSkFIvbNRTMQBIZ01O-RhfT5t7XGASjwlNeiPpLWvwmrxyAkForhQk_TXr0f-4xsDl3KffwDKMLEISwgJ1YOwUUg-0gks9aCvM43FbEdJNMnJ_XdjgXymeAIz7MJKUU_HYvPYZE1XHEBCL5CRf_5zfdA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:12:36 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:12:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:12:36 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:12:36 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:47 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"404","message":"Invalid Subscription-Id, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ScvAL3vGDqN3NloEQbFNCVNwazsCnwLHRgVNHVkwk9JYutCcfVq3tXfFgwYl9K0c8qwRy7VHWYDsqVRThfCcs4y-M6vAmMCeYkU7g3zJdFLlpwNzcsteV5ZGmYuftMdlECkLdhN_zCj0xhWvUgJAVLbsyYKk4ivqKlHnrwwU30O0-wntqSPL_dzLieqylVXSkFIvbNRTMQBIZ01O-RhfT5t7XGASjwlNeiPpLWvwmrxyAkForhQk_TXr0f-4xsDl3KffwDKMLEISwgJ1YOwUUg-0gks9aCvM43FbEdJNMnJ_XdjgXymeAIz7MJKUU_HYvPYZE1XHEBCL5CRf_5zfdA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:12:49 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:12:49 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:12:50 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:12:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:12:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:12:50 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:12:50 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:12:50 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:12:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:12:50 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:12:50 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:52 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PA2f6w_vqz-TTlmQp_OrHPw30380eqJWRuHRfz8-Mll1t2gVtu1UzE-iXGCb3DUc1AbIBuhr7Jjknqg1NuVhU0sT562UV5z9QXKIkAxsvzqaO2k8m1enEHZGG67tksMRtRCij8zlMZ3ltW9Fl5174iWnJ8e7U377e2hWeK-eNvM-70egTi-YeER7BRYp31A-8JZgfCXwAGsAqqtsNCcdLvoeGsA_gwkLBgddsv8Pc4P-c6i-xnu-waVCRb8Aa10FZJrC7Mn1zWVNdrrdnunNWO61eysajX7OQ59ui5_IAikhpQQTmX9Ia472ATWK-ydOSzsp-Dg2SMPSCVNu3Z7SWQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:12:52 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:12:52 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:12:52 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:12:52 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:53 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PA2f6w_vqz-TTlmQp_OrHPw30380eqJWRuHRfz8-Mll1t2gVtu1UzE-iXGCb3DUc1AbIBuhr7Jjknqg1NuVhU0sT562UV5z9QXKIkAxsvzqaO2k8m1enEHZGG67tksMRtRCij8zlMZ3ltW9Fl5174iWnJ8e7U377e2hWeK-eNvM-70egTi-YeER7BRYp31A-8JZgfCXwAGsAqqtsNCcdLvoeGsA_gwkLBgddsv8Pc4P-c6i-xnu-waVCRb8Aa10FZJrC7Mn1zWVNdrrdnunNWO61eysajX7OQ59ui5_IAikhpQQTmX9Ia472ATWK-ydOSzsp-Dg2SMPSCVNu3Z7SWQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:12:53 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:12:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:12:53 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:12:53 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:12:54 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PA2f6w_vqz-TTlmQp_OrHPw30380eqJWRuHRfz8-Mll1t2gVtu1UzE-iXGCb3DUc1AbIBuhr7Jjknqg1NuVhU0sT562UV5z9QXKIkAxsvzqaO2k8m1enEHZGG67tksMRtRCij8zlMZ3ltW9Fl5174iWnJ8e7U377e2hWeK-eNvM-70egTi-YeER7BRYp31A-8JZgfCXwAGsAqqtsNCcdLvoeGsA_gwkLBgddsv8Pc4P-c6i-xnu-waVCRb8Aa10FZJrC7Mn1zWVNdrrdnunNWO61eysajX7OQ59ui5_IAikhpQQTmX9Ia472ATWK-ydOSzsp-Dg2SMPSCVNu3Z7SWQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:12:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:12:54 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:12:54 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:55 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PA2f6w_vqz-TTlmQp_OrHPw30380eqJWRuHRfz8-Mll1t2gVtu1UzE-iXGCb3DUc1AbIBuhr7Jjknqg1NuVhU0sT562UV5z9QXKIkAxsvzqaO2k8m1enEHZGG67tksMRtRCij8zlMZ3ltW9Fl5174iWnJ8e7U377e2hWeK-eNvM-70egTi-YeER7BRYp31A-8JZgfCXwAGsAqqtsNCcdLvoeGsA_gwkLBgddsv8Pc4P-c6i-xnu-waVCRb8Aa10FZJrC7Mn1zWVNdrrdnunNWO61eysajX7OQ59ui5_IAikhpQQTmX9Ia472ATWK-ydOSzsp-Dg2SMPSCVNu3Z7SWQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:12:55 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:12:55 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:12:55 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:12:55 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:12:55 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PA2f6w_vqz-TTlmQp_OrHPw30380eqJWRuHRfz8-Mll1t2gVtu1UzE-iXGCb3DUc1AbIBuhr7Jjknqg1NuVhU0sT562UV5z9QXKIkAxsvzqaO2k8m1enEHZGG67tksMRtRCij8zlMZ3ltW9Fl5174iWnJ8e7U377e2hWeK-eNvM-70egTi-YeER7BRYp31A-8JZgfCXwAGsAqqtsNCcdLvoeGsA_gwkLBgddsv8Pc4P-c6i-xnu-waVCRb8Aa10FZJrC7Mn1zWVNdrrdnunNWO61eysajX7OQ59ui5_IAikhpQQTmX9Ia472ATWK-ydOSzsp-Dg2SMPSCVNu3Z7SWQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:13:26 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:13:26 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:13:27 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:13:27 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:13:27 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:13:27 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:13:27 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:13:27 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:13:27 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:13:27 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:13:27 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:14:35 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:14:35 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:14:37 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:14:37 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:14:37 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:14:37 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:14:37 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:14:37 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:14:37 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:14:37 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:14:37 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:14:39 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.A9GSlzMlwWJe0XuGCIjXLzVHjuu9aHKBpTPQqnHyoXe6sxebXO5DdNoA-FahXE43Rxa9FADb7iV7j5sId9VLK9DojBOTFWYXgBXHCi645fCE_vFFoIgC_YXi0Z1jCtfHNG97g2_VzI3UwZIdTyVjohle4J_NxsFPdFz2FEbNAVt1Otgq9fFVgtMf6kZy_GwJvAU_B52vQlpvjhO1CpiXKIbdlESjlp4FtPzFRqf3_qSOuOgNbW6AbRk4hTlW0rzTL6wUoMBhATd_9NCdfyKg7mPeS2i9xlia3sf6p1B5vFy4yTs91AbZozPzg_kW97UxhXawnSPRT_NeVPQCH55dhA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:14:39 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:14:39 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:14:39 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:14:39 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:14:41 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.A9GSlzMlwWJe0XuGCIjXLzVHjuu9aHKBpTPQqnHyoXe6sxebXO5DdNoA-FahXE43Rxa9FADb7iV7j5sId9VLK9DojBOTFWYXgBXHCi645fCE_vFFoIgC_YXi0Z1jCtfHNG97g2_VzI3UwZIdTyVjohle4J_NxsFPdFz2FEbNAVt1Otgq9fFVgtMf6kZy_GwJvAU_B52vQlpvjhO1CpiXKIbdlESjlp4FtPzFRqf3_qSOuOgNbW6AbRk4hTlW0rzTL6wUoMBhATd_9NCdfyKg7mPeS2i9xlia3sf6p1B5vFy4yTs91AbZozPzg_kW97UxhXawnSPRT_NeVPQCH55dhA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:14:41 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:14:41 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:14:41 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:14:41 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:14:42 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.A9GSlzMlwWJe0XuGCIjXLzVHjuu9aHKBpTPQqnHyoXe6sxebXO5DdNoA-FahXE43Rxa9FADb7iV7j5sId9VLK9DojBOTFWYXgBXHCi645fCE_vFFoIgC_YXi0Z1jCtfHNG97g2_VzI3UwZIdTyVjohle4J_NxsFPdFz2FEbNAVt1Otgq9fFVgtMf6kZy_GwJvAU_B52vQlpvjhO1CpiXKIbdlESjlp4FtPzFRqf3_qSOuOgNbW6AbRk4hTlW0rzTL6wUoMBhATd_9NCdfyKg7mPeS2i9xlia3sf6p1B5vFy4yTs91AbZozPzg_kW97UxhXawnSPRT_NeVPQCH55dhA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:14:42 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:14:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:14:42 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:14:42 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:14:44 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.A9GSlzMlwWJe0XuGCIjXLzVHjuu9aHKBpTPQqnHyoXe6sxebXO5DdNoA-FahXE43Rxa9FADb7iV7j5sId9VLK9DojBOTFWYXgBXHCi645fCE_vFFoIgC_YXi0Z1jCtfHNG97g2_VzI3UwZIdTyVjohle4J_NxsFPdFz2FEbNAVt1Otgq9fFVgtMf6kZy_GwJvAU_B52vQlpvjhO1CpiXKIbdlESjlp4FtPzFRqf3_qSOuOgNbW6AbRk4hTlW0rzTL6wUoMBhATd_9NCdfyKg7mPeS2i9xlia3sf6p1B5vFy4yTs91AbZozPzg_kW97UxhXawnSPRT_NeVPQCH55dhA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:14:44 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:14:44 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:14:44 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:14:44 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:14:45 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.A9GSlzMlwWJe0XuGCIjXLzVHjuu9aHKBpTPQqnHyoXe6sxebXO5DdNoA-FahXE43Rxa9FADb7iV7j5sId9VLK9DojBOTFWYXgBXHCi645fCE_vFFoIgC_YXi0Z1jCtfHNG97g2_VzI3UwZIdTyVjohle4J_NxsFPdFz2FEbNAVt1Otgq9fFVgtMf6kZy_GwJvAU_B52vQlpvjhO1CpiXKIbdlESjlp4FtPzFRqf3_qSOuOgNbW6AbRk4hTlW0rzTL6wUoMBhATd_9NCdfyKg7mPeS2i9xlia3sf6p1B5vFy4yTs91AbZozPzg_kW97UxhXawnSPRT_NeVPQCH55dhA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:16:55 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:16:55 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:16:56 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:16:56 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:16:56 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:16:56 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:16:56 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:16:56 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:16:56 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:16:56 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:16:56 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:17:35 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:17:35 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:17:36 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:17:36 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:17:36 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:17:36 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:17:36 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:17:36 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:17:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:17:36 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:17:36 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:18:13 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:18:13 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:18:14 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:18:14 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:18:14 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:18:14 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:18:14 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:18:14 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:18:14 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:18:14 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:18:14 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:18:16 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.flstbdpvQl6-noaQLF6SQ__Z5NauxIYj4xcL9wXq0lMCv7kcQ_P8lMup5CNyih5kZkeGxbUFzkP3nTTNBInifbpiDstMd2_ygsvSh1A1iONdmAQAfGnyWwk9XX2mlOX-PUnAEpt02j41VMjVaJv4d9P3oPWZoNTZWKukvGNOd0j7QAwevzMK5NVSOwQ_2rEmAaV3qRYZbu18isjrkLdN6OzQm3VJ3VZdXOmz-c4ape0jF5j8FPNC8xJdFw6O5U89i_eT7htZPr938Qt6IcEL8c_HmeNzm2Fc4gM0KEYmkZhUBq458vX38eIpwq2j4tLKq7PLPvRMaYnrBYC0If_OVw\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:18:16 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:18:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:18:16 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:18:16 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:18:16 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.flstbdpvQl6-noaQLF6SQ__Z5NauxIYj4xcL9wXq0lMCv7kcQ_P8lMup5CNyih5kZkeGxbUFzkP3nTTNBInifbpiDstMd2_ygsvSh1A1iONdmAQAfGnyWwk9XX2mlOX-PUnAEpt02j41VMjVaJv4d9P3oPWZoNTZWKukvGNOd0j7QAwevzMK5NVSOwQ_2rEmAaV3qRYZbu18isjrkLdN6OzQm3VJ3VZdXOmz-c4ape0jF5j8FPNC8xJdFw6O5U89i_eT7htZPr938Qt6IcEL8c_HmeNzm2Fc4gM0KEYmkZhUBq458vX38eIpwq2j4tLKq7PLPvRMaYnrBYC0If_OVw\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:18:16 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:18:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:18:16 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:18:16 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:18:17 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.flstbdpvQl6-noaQLF6SQ__Z5NauxIYj4xcL9wXq0lMCv7kcQ_P8lMup5CNyih5kZkeGxbUFzkP3nTTNBInifbpiDstMd2_ygsvSh1A1iONdmAQAfGnyWwk9XX2mlOX-PUnAEpt02j41VMjVaJv4d9P3oPWZoNTZWKukvGNOd0j7QAwevzMK5NVSOwQ_2rEmAaV3qRYZbu18isjrkLdN6OzQm3VJ3VZdXOmz-c4ape0jF5j8FPNC8xJdFw6O5U89i_eT7htZPr938Qt6IcEL8c_HmeNzm2Fc4gM0KEYmkZhUBq458vX38eIpwq2j4tLKq7PLPvRMaYnrBYC0If_OVw\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:18:17 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:18:17 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:18:17 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:18:17 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:18:18 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.flstbdpvQl6-noaQLF6SQ__Z5NauxIYj4xcL9wXq0lMCv7kcQ_P8lMup5CNyih5kZkeGxbUFzkP3nTTNBInifbpiDstMd2_ygsvSh1A1iONdmAQAfGnyWwk9XX2mlOX-PUnAEpt02j41VMjVaJv4d9P3oPWZoNTZWKukvGNOd0j7QAwevzMK5NVSOwQ_2rEmAaV3qRYZbu18isjrkLdN6OzQm3VJ3VZdXOmz-c4ape0jF5j8FPNC8xJdFw6O5U89i_eT7htZPr938Qt6IcEL8c_HmeNzm2Fc4gM0KEYmkZhUBq458vX38eIpwq2j4tLKq7PLPvRMaYnrBYC0If_OVw\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:18:18 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:18:18 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:18:18 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:18:18 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:18:19 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.flstbdpvQl6-noaQLF6SQ__Z5NauxIYj4xcL9wXq0lMCv7kcQ_P8lMup5CNyih5kZkeGxbUFzkP3nTTNBInifbpiDstMd2_ygsvSh1A1iONdmAQAfGnyWwk9XX2mlOX-PUnAEpt02j41VMjVaJv4d9P3oPWZoNTZWKukvGNOd0j7QAwevzMK5NVSOwQ_2rEmAaV3qRYZbu18isjrkLdN6OzQm3VJ3VZdXOmz-c4ape0jF5j8FPNC8xJdFw6O5U89i_eT7htZPr938Qt6IcEL8c_HmeNzm2Fc4gM0KEYmkZhUBq458vX38eIpwq2j4tLKq7PLPvRMaYnrBYC0If_OVw\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:19:58 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:19:58 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:19:59 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:19:59 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:19:59 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:19:59 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:19:59 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:19:59 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:19:59 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:19:59 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:19:59 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:01 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DdY2EiRPyj0jLd7udg3bWcE0neyHccafLx1XmQirn7cT5S9P_igFtYc14JgY4a8iNHjP2sZVu6XY3KfRkeEDkLa3n1nIARL0d_vqmtb24E0-uexHCxUVzrNxT43w_TwhGl8luIaWA2C2XJllmd_niIHxKsfUsIXJHcmB1Bw39CEqgHD0HD5sutzwiijzUep4-gpnaRZIbC7ChDFsWHU4slEAW1TulT3nZDL697kuDWWE0s-reNL4-9tkCQfE3MRjpfRNwxtHr3KgyVS2srnqAKZ2hw-crA9CcGsS-RJ6zFERjosVIP6wpUYpMhqZDwAZrLXhQc1g0vC-4MwmC6GPNQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:20:01 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:20:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:20:01 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:20:01 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:02 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DdY2EiRPyj0jLd7udg3bWcE0neyHccafLx1XmQirn7cT5S9P_igFtYc14JgY4a8iNHjP2sZVu6XY3KfRkeEDkLa3n1nIARL0d_vqmtb24E0-uexHCxUVzrNxT43w_TwhGl8luIaWA2C2XJllmd_niIHxKsfUsIXJHcmB1Bw39CEqgHD0HD5sutzwiijzUep4-gpnaRZIbC7ChDFsWHU4slEAW1TulT3nZDL697kuDWWE0s-reNL4-9tkCQfE3MRjpfRNwxtHr3KgyVS2srnqAKZ2hw-crA9CcGsS-RJ6zFERjosVIP6wpUYpMhqZDwAZrLXhQc1g0vC-4MwmC6GPNQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:20:02 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:20:02 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:20:02 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:20:02 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:03 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DdY2EiRPyj0jLd7udg3bWcE0neyHccafLx1XmQirn7cT5S9P_igFtYc14JgY4a8iNHjP2sZVu6XY3KfRkeEDkLa3n1nIARL0d_vqmtb24E0-uexHCxUVzrNxT43w_TwhGl8luIaWA2C2XJllmd_niIHxKsfUsIXJHcmB1Bw39CEqgHD0HD5sutzwiijzUep4-gpnaRZIbC7ChDFsWHU4slEAW1TulT3nZDL697kuDWWE0s-reNL4-9tkCQfE3MRjpfRNwxtHr3KgyVS2srnqAKZ2hw-crA9CcGsS-RJ6zFERjosVIP6wpUYpMhqZDwAZrLXhQc1g0vC-4MwmC6GPNQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:20:03 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:20:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:20:03 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:20:03 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:04 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DdY2EiRPyj0jLd7udg3bWcE0neyHccafLx1XmQirn7cT5S9P_igFtYc14JgY4a8iNHjP2sZVu6XY3KfRkeEDkLa3n1nIARL0d_vqmtb24E0-uexHCxUVzrNxT43w_TwhGl8luIaWA2C2XJllmd_niIHxKsfUsIXJHcmB1Bw39CEqgHD0HD5sutzwiijzUep4-gpnaRZIbC7ChDFsWHU4slEAW1TulT3nZDL697kuDWWE0s-reNL4-9tkCQfE3MRjpfRNwxtHr3KgyVS2srnqAKZ2hw-crA9CcGsS-RJ6zFERjosVIP6wpUYpMhqZDwAZrLXhQc1g0vC-4MwmC6GPNQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:20:04 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:20:04 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:20:04 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:04 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:20:05 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DdY2EiRPyj0jLd7udg3bWcE0neyHccafLx1XmQirn7cT5S9P_igFtYc14JgY4a8iNHjP2sZVu6XY3KfRkeEDkLa3n1nIARL0d_vqmtb24E0-uexHCxUVzrNxT43w_TwhGl8luIaWA2C2XJllmd_niIHxKsfUsIXJHcmB1Bw39CEqgHD0HD5sutzwiijzUep4-gpnaRZIbC7ChDFsWHU4slEAW1TulT3nZDL697kuDWWE0s-reNL4-9tkCQfE3MRjpfRNwxtHr3KgyVS2srnqAKZ2hw-crA9CcGsS-RJ6zFERjosVIP6wpUYpMhqZDwAZrLXhQc1g0vC-4MwmC6GPNQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:20:36 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:20:36 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:20:38 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:20:38 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:20:38 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:20:38 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:20:38 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:20:38 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:20:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:20:38 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:20:38 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:39 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KHtZYhLOqg3VJLKbJzuv-dgd0fN9IRmoKrtvsfXlyRqlqYwTWSHnHGPVRuvX5RqzMYgtF9PaoRkme5VgFYkQ8rHN11HEbD5j8UA6k6fh0_exIY13nquiJlD1Y3cYcqfgXXuDCkaMSMgycR44MZdGvzW8hCfPpJ92PmE3Liu_fILxT12_ECQV0n8I7iQfYbeclGsyRjVQewHaxK6mdJVwvDN7-6OHUEaCeE3GzAHtWf6P3ejeQLgQzYEnCSjuVFUXAMesPZZl8clsfsAIQGKVs-bsrWJ7KIz4zt6KKlX5XDQJK0t-TVLPbsIWRYfLXeFKZXC6ZnL8wwaUVKnM4hBQeg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:20:39 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:20:39 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:20:39 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:20:39 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:40 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KHtZYhLOqg3VJLKbJzuv-dgd0fN9IRmoKrtvsfXlyRqlqYwTWSHnHGPVRuvX5RqzMYgtF9PaoRkme5VgFYkQ8rHN11HEbD5j8UA6k6fh0_exIY13nquiJlD1Y3cYcqfgXXuDCkaMSMgycR44MZdGvzW8hCfPpJ92PmE3Liu_fILxT12_ECQV0n8I7iQfYbeclGsyRjVQewHaxK6mdJVwvDN7-6OHUEaCeE3GzAHtWf6P3ejeQLgQzYEnCSjuVFUXAMesPZZl8clsfsAIQGKVs-bsrWJ7KIz4zt6KKlX5XDQJK0t-TVLPbsIWRYfLXeFKZXC6ZnL8wwaUVKnM4hBQeg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:20:40 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:20:40 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:20:40 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:20:40 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:41 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KHtZYhLOqg3VJLKbJzuv-dgd0fN9IRmoKrtvsfXlyRqlqYwTWSHnHGPVRuvX5RqzMYgtF9PaoRkme5VgFYkQ8rHN11HEbD5j8UA6k6fh0_exIY13nquiJlD1Y3cYcqfgXXuDCkaMSMgycR44MZdGvzW8hCfPpJ92PmE3Liu_fILxT12_ECQV0n8I7iQfYbeclGsyRjVQewHaxK6mdJVwvDN7-6OHUEaCeE3GzAHtWf6P3ejeQLgQzYEnCSjuVFUXAMesPZZl8clsfsAIQGKVs-bsrWJ7KIz4zt6KKlX5XDQJK0t-TVLPbsIWRYfLXeFKZXC6ZnL8wwaUVKnM4hBQeg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:20:41 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:20:41 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:20:41 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:20:41 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:42 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KHtZYhLOqg3VJLKbJzuv-dgd0fN9IRmoKrtvsfXlyRqlqYwTWSHnHGPVRuvX5RqzMYgtF9PaoRkme5VgFYkQ8rHN11HEbD5j8UA6k6fh0_exIY13nquiJlD1Y3cYcqfgXXuDCkaMSMgycR44MZdGvzW8hCfPpJ92PmE3Liu_fILxT12_ECQV0n8I7iQfYbeclGsyRjVQewHaxK6mdJVwvDN7-6OHUEaCeE3GzAHtWf6P3ejeQLgQzYEnCSjuVFUXAMesPZZl8clsfsAIQGKVs-bsrWJ7KIz4zt6KKlX5XDQJK0t-TVLPbsIWRYfLXeFKZXC6ZnL8wwaUVKnM4hBQeg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:20:42 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:20:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:20:42 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:20:42 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:20:42 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KHtZYhLOqg3VJLKbJzuv-dgd0fN9IRmoKrtvsfXlyRqlqYwTWSHnHGPVRuvX5RqzMYgtF9PaoRkme5VgFYkQ8rHN11HEbD5j8UA6k6fh0_exIY13nquiJlD1Y3cYcqfgXXuDCkaMSMgycR44MZdGvzW8hCfPpJ92PmE3Liu_fILxT12_ECQV0n8I7iQfYbeclGsyRjVQewHaxK6mdJVwvDN7-6OHUEaCeE3GzAHtWf6P3ejeQLgQzYEnCSjuVFUXAMesPZZl8clsfsAIQGKVs-bsrWJ7KIz4zt6KKlX5XDQJK0t-TVLPbsIWRYfLXeFKZXC6ZnL8wwaUVKnM4hBQeg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:21:39 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:21:39 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:21:40 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:21:40 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:21:40 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:21:40 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:21:40 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:21:40 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:21:40 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:21:40 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:21:40 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:21:42 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S8-uLpyUM0Dw1mD0OKEjlq9YRXzSIOs93a620q9BiiS-9MSgjHv9QUqK21HwzmTr2OpJb7sdRkmeec1FOYmoj9_k62FT86bmvjARtyDRLIdCsAYO6DMVqsX6ET2X9MbKElVFixM5l67D1YrSLBnw4LuOQJ5mgd0Dxas3D4K1ANdTvrYSWh3y0yktww6sNDxtYHU1juo2SSgBBFeF3XOZ82MyrcTk_rJB9BcWyfdzwwrNfNl-NLecRCv-ROhYnQljx19hOiSp4xEZ6wYjRBwqveNxWtqkcTAfSll_RZfXGJYzE0nVGF0hFWOYhkCNzy9RdYrMAYrRGX8VTtVVVk6MFA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:21:42 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:21:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:21:42 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:21:42 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:21:43 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S8-uLpyUM0Dw1mD0OKEjlq9YRXzSIOs93a620q9BiiS-9MSgjHv9QUqK21HwzmTr2OpJb7sdRkmeec1FOYmoj9_k62FT86bmvjARtyDRLIdCsAYO6DMVqsX6ET2X9MbKElVFixM5l67D1YrSLBnw4LuOQJ5mgd0Dxas3D4K1ANdTvrYSWh3y0yktww6sNDxtYHU1juo2SSgBBFeF3XOZ82MyrcTk_rJB9BcWyfdzwwrNfNl-NLecRCv-ROhYnQljx19hOiSp4xEZ6wYjRBwqveNxWtqkcTAfSll_RZfXGJYzE0nVGF0hFWOYhkCNzy9RdYrMAYrRGX8VTtVVVk6MFA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:21:43 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:21:43 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:21:43 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:21:43 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:21:44 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S8-uLpyUM0Dw1mD0OKEjlq9YRXzSIOs93a620q9BiiS-9MSgjHv9QUqK21HwzmTr2OpJb7sdRkmeec1FOYmoj9_k62FT86bmvjARtyDRLIdCsAYO6DMVqsX6ET2X9MbKElVFixM5l67D1YrSLBnw4LuOQJ5mgd0Dxas3D4K1ANdTvrYSWh3y0yktww6sNDxtYHU1juo2SSgBBFeF3XOZ82MyrcTk_rJB9BcWyfdzwwrNfNl-NLecRCv-ROhYnQljx19hOiSp4xEZ6wYjRBwqveNxWtqkcTAfSll_RZfXGJYzE0nVGF0hFWOYhkCNzy9RdYrMAYrRGX8VTtVVVk6MFA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:21:44 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:21:44 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:21:44 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:21:44 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:21:45 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S8-uLpyUM0Dw1mD0OKEjlq9YRXzSIOs93a620q9BiiS-9MSgjHv9QUqK21HwzmTr2OpJb7sdRkmeec1FOYmoj9_k62FT86bmvjARtyDRLIdCsAYO6DMVqsX6ET2X9MbKElVFixM5l67D1YrSLBnw4LuOQJ5mgd0Dxas3D4K1ANdTvrYSWh3y0yktww6sNDxtYHU1juo2SSgBBFeF3XOZ82MyrcTk_rJB9BcWyfdzwwrNfNl-NLecRCv-ROhYnQljx19hOiSp4xEZ6wYjRBwqveNxWtqkcTAfSll_RZfXGJYzE0nVGF0hFWOYhkCNzy9RdYrMAYrRGX8VTtVVVk6MFA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:21:45 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:21:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:21:45 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:21:45 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:21:46 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.S8-uLpyUM0Dw1mD0OKEjlq9YRXzSIOs93a620q9BiiS-9MSgjHv9QUqK21HwzmTr2OpJb7sdRkmeec1FOYmoj9_k62FT86bmvjARtyDRLIdCsAYO6DMVqsX6ET2X9MbKElVFixM5l67D1YrSLBnw4LuOQJ5mgd0Dxas3D4K1ANdTvrYSWh3y0yktww6sNDxtYHU1juo2SSgBBFeF3XOZ82MyrcTk_rJB9BcWyfdzwwrNfNl-NLecRCv-ROhYnQljx19hOiSp4xEZ6wYjRBwqveNxWtqkcTAfSll_RZfXGJYzE0nVGF0hFWOYhkCNzy9RdYrMAYrRGX8VTtVVVk6MFA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:22:00 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:22:00 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:22:01 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:22:01 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:22:01 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:22:01 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:22:01 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:22:01 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:22:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:22:01 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:22:01 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:23:12 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:23:12 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:23:14 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:23:14 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:23:14 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:23:14 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:23:14 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:23:14 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:23:14 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:23:14 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:23:14 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:23:16 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eYfAqrwj8HjdqIFum3hdvUCjWlyI_WGgmtzOVXL4w95IfksYQQidF6aDqPrkn_m-JHsPUPdfgNFgebMZ4b3_wXHzCH2vmlayFiwFHHYY38XnCmdZkPNtTNGf0ARzNMWUycA0eNpUZ9kLrLSEiYUW9oLziV_FzE48WmZbyzBZIztkWOmvIMEe_uDVupIMMVXG_sN8t-Ottyfx7IzRG1ZB3TVHE4mrOo76TBiAR9hfL1_IhGiHCFCBlXgGd045lDPFqZJT-UjRdIzVH6zfqoDzxo7r8nh0I_4fSftp5YsB3k3V9GgqkI8iYmiyJnYwRGb-ArdCfKHPr_Qb5JaYWyorug\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:23:16 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:23:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:23:16 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:23:16 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:23:17 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eYfAqrwj8HjdqIFum3hdvUCjWlyI_WGgmtzOVXL4w95IfksYQQidF6aDqPrkn_m-JHsPUPdfgNFgebMZ4b3_wXHzCH2vmlayFiwFHHYY38XnCmdZkPNtTNGf0ARzNMWUycA0eNpUZ9kLrLSEiYUW9oLziV_FzE48WmZbyzBZIztkWOmvIMEe_uDVupIMMVXG_sN8t-Ottyfx7IzRG1ZB3TVHE4mrOo76TBiAR9hfL1_IhGiHCFCBlXgGd045lDPFqZJT-UjRdIzVH6zfqoDzxo7r8nh0I_4fSftp5YsB3k3V9GgqkI8iYmiyJnYwRGb-ArdCfKHPr_Qb5JaYWyorug\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:23:17 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:23:17 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:23:17 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:23:17 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:23:18 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eYfAqrwj8HjdqIFum3hdvUCjWlyI_WGgmtzOVXL4w95IfksYQQidF6aDqPrkn_m-JHsPUPdfgNFgebMZ4b3_wXHzCH2vmlayFiwFHHYY38XnCmdZkPNtTNGf0ARzNMWUycA0eNpUZ9kLrLSEiYUW9oLziV_FzE48WmZbyzBZIztkWOmvIMEe_uDVupIMMVXG_sN8t-Ottyfx7IzRG1ZB3TVHE4mrOo76TBiAR9hfL1_IhGiHCFCBlXgGd045lDPFqZJT-UjRdIzVH6zfqoDzxo7r8nh0I_4fSftp5YsB3k3V9GgqkI8iYmiyJnYwRGb-ArdCfKHPr_Qb5JaYWyorug\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:23:18 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:23:18 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:23:18 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:23:18 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:23:19 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eYfAqrwj8HjdqIFum3hdvUCjWlyI_WGgmtzOVXL4w95IfksYQQidF6aDqPrkn_m-JHsPUPdfgNFgebMZ4b3_wXHzCH2vmlayFiwFHHYY38XnCmdZkPNtTNGf0ARzNMWUycA0eNpUZ9kLrLSEiYUW9oLziV_FzE48WmZbyzBZIztkWOmvIMEe_uDVupIMMVXG_sN8t-Ottyfx7IzRG1ZB3TVHE4mrOo76TBiAR9hfL1_IhGiHCFCBlXgGd045lDPFqZJT-UjRdIzVH6zfqoDzxo7r8nh0I_4fSftp5YsB3k3V9GgqkI8iYmiyJnYwRGb-ArdCfKHPr_Qb5JaYWyorug\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:23:19 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:23:19 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:23:19 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:23:19 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:23:20 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eYfAqrwj8HjdqIFum3hdvUCjWlyI_WGgmtzOVXL4w95IfksYQQidF6aDqPrkn_m-JHsPUPdfgNFgebMZ4b3_wXHzCH2vmlayFiwFHHYY38XnCmdZkPNtTNGf0ARzNMWUycA0eNpUZ9kLrLSEiYUW9oLziV_FzE48WmZbyzBZIztkWOmvIMEe_uDVupIMMVXG_sN8t-Ottyfx7IzRG1ZB3TVHE4mrOo76TBiAR9hfL1_IhGiHCFCBlXgGd045lDPFqZJT-UjRdIzVH6zfqoDzxo7r8nh0I_4fSftp5YsB3k3V9GgqkI8iYmiyJnYwRGb-ArdCfKHPr_Qb5JaYWyorug\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:02 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:24:02 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:24:03 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:24:03 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:24:03 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:24:03 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:24:03 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:24:03 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:24:03 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:03 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:04 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HQOCWQxXm2un9Fhy4oljyTEPY5MmpT90v20qMoBHrW3VCmz-Gu8qBAa75P763Kj-lIGwuB8q8Kccio87iP2oGTwcAECmCvBSO29ZVblK7pAsjr6ebE4yBvO7UvYExeNhqV1IK8Hy1DPW8kViafNY6cL2dHk3gGEkP0r-LTevUXZS01CywOseOp_a52pbSOs6OrGCA6IssATwL9CENRukzH9ewMA0chTuFYMNKZ-zpRl5gHoLFo_8LKXKWJCTE5T9u6mB7aM1wwhQXCPFF08x31shKSQtGUN9EbLKWbuiwwQAOJTTw2PXhwsGTI_EiZL7xqW5BTaUtje1uxOCdz7hzA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:04 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:04 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:24:04 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:04 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:05 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HQOCWQxXm2un9Fhy4oljyTEPY5MmpT90v20qMoBHrW3VCmz-Gu8qBAa75P763Kj-lIGwuB8q8Kccio87iP2oGTwcAECmCvBSO29ZVblK7pAsjr6ebE4yBvO7UvYExeNhqV1IK8Hy1DPW8kViafNY6cL2dHk3gGEkP0r-LTevUXZS01CywOseOp_a52pbSOs6OrGCA6IssATwL9CENRukzH9ewMA0chTuFYMNKZ-zpRl5gHoLFo_8LKXKWJCTE5T9u6mB7aM1wwhQXCPFF08x31shKSQtGUN9EbLKWbuiwwQAOJTTw2PXhwsGTI_EiZL7xqW5BTaUtje1uxOCdz7hzA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:05 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:05 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:24:05 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:05 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:06 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HQOCWQxXm2un9Fhy4oljyTEPY5MmpT90v20qMoBHrW3VCmz-Gu8qBAa75P763Kj-lIGwuB8q8Kccio87iP2oGTwcAECmCvBSO29ZVblK7pAsjr6ebE4yBvO7UvYExeNhqV1IK8Hy1DPW8kViafNY6cL2dHk3gGEkP0r-LTevUXZS01CywOseOp_a52pbSOs6OrGCA6IssATwL9CENRukzH9ewMA0chTuFYMNKZ-zpRl5gHoLFo_8LKXKWJCTE5T9u6mB7aM1wwhQXCPFF08x31shKSQtGUN9EbLKWbuiwwQAOJTTw2PXhwsGTI_EiZL7xqW5BTaUtje1uxOCdz7hzA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:06 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:06 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:24:06 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:06 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:07 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HQOCWQxXm2un9Fhy4oljyTEPY5MmpT90v20qMoBHrW3VCmz-Gu8qBAa75P763Kj-lIGwuB8q8Kccio87iP2oGTwcAECmCvBSO29ZVblK7pAsjr6ebE4yBvO7UvYExeNhqV1IK8Hy1DPW8kViafNY6cL2dHk3gGEkP0r-LTevUXZS01CywOseOp_a52pbSOs6OrGCA6IssATwL9CENRukzH9ewMA0chTuFYMNKZ-zpRl5gHoLFo_8LKXKWJCTE5T9u6mB7aM1wwhQXCPFF08x31shKSQtGUN9EbLKWbuiwwQAOJTTw2PXhwsGTI_EiZL7xqW5BTaUtje1uxOCdz7hzA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:07 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:07 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:24:07 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:07 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:08 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HQOCWQxXm2un9Fhy4oljyTEPY5MmpT90v20qMoBHrW3VCmz-Gu8qBAa75P763Kj-lIGwuB8q8Kccio87iP2oGTwcAECmCvBSO29ZVblK7pAsjr6ebE4yBvO7UvYExeNhqV1IK8Hy1DPW8kViafNY6cL2dHk3gGEkP0r-LTevUXZS01CywOseOp_a52pbSOs6OrGCA6IssATwL9CENRukzH9ewMA0chTuFYMNKZ-zpRl5gHoLFo_8LKXKWJCTE5T9u6mB7aM1wwhQXCPFF08x31shKSQtGUN9EbLKWbuiwwQAOJTTw2PXhwsGTI_EiZL7xqW5BTaUtje1uxOCdz7hzA\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:20 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:24:20 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:24:22 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:24:22 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:24:22 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:24:22 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:24:22 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:24:22 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:22 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:24:22 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:22 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:23 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YB29NdE0JZh_eUwbIglByhs9W2ve5rzG-dZ_GYyDR8p4yzj5WrdpGddhszvABUpIYjER8nJtNtKjjNndWMua60X7VL2xAkFEGL2pAt0SB6zSPV3APG4EnJ-c5pqTgHsSUfWP-uW4dfwPHvETJ_1bdHdAtBNM53N30VfDXhyJQ4Y7U_j964WvWemIbsWinwptBplYojYaXZFWWXdc1Ozim3AvZ9msDb4Wb07fonavUrBhe0d3RVcrWKaNM8SXsUTov7ooDlgtPTMVLZ8mF4UzeQ2-zXFIl_wDpftGxwSyIqic8eDUOU6Nwf9ly9O3GDlYP4tYtHUC9VlIaKbMq7qjmg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:23 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:23 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:24:23 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:23 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:24 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YB29NdE0JZh_eUwbIglByhs9W2ve5rzG-dZ_GYyDR8p4yzj5WrdpGddhszvABUpIYjER8nJtNtKjjNndWMua60X7VL2xAkFEGL2pAt0SB6zSPV3APG4EnJ-c5pqTgHsSUfWP-uW4dfwPHvETJ_1bdHdAtBNM53N30VfDXhyJQ4Y7U_j964WvWemIbsWinwptBplYojYaXZFWWXdc1Ozim3AvZ9msDb4Wb07fonavUrBhe0d3RVcrWKaNM8SXsUTov7ooDlgtPTMVLZ8mF4UzeQ2-zXFIl_wDpftGxwSyIqic8eDUOU6Nwf9ly9O3GDlYP4tYtHUC9VlIaKbMq7qjmg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:24 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:24 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:24:24 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:24 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:25 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YB29NdE0JZh_eUwbIglByhs9W2ve5rzG-dZ_GYyDR8p4yzj5WrdpGddhszvABUpIYjER8nJtNtKjjNndWMua60X7VL2xAkFEGL2pAt0SB6zSPV3APG4EnJ-c5pqTgHsSUfWP-uW4dfwPHvETJ_1bdHdAtBNM53N30VfDXhyJQ4Y7U_j964WvWemIbsWinwptBplYojYaXZFWWXdc1Ozim3AvZ9msDb4Wb07fonavUrBhe0d3RVcrWKaNM8SXsUTov7ooDlgtPTMVLZ8mF4UzeQ2-zXFIl_wDpftGxwSyIqic8eDUOU6Nwf9ly9O3GDlYP4tYtHUC9VlIaKbMq7qjmg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:25 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:25 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:24:25 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:25 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:26 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YB29NdE0JZh_eUwbIglByhs9W2ve5rzG-dZ_GYyDR8p4yzj5WrdpGddhszvABUpIYjER8nJtNtKjjNndWMua60X7VL2xAkFEGL2pAt0SB6zSPV3APG4EnJ-c5pqTgHsSUfWP-uW4dfwPHvETJ_1bdHdAtBNM53N30VfDXhyJQ4Y7U_j964WvWemIbsWinwptBplYojYaXZFWWXdc1Ozim3AvZ9msDb4Wb07fonavUrBhe0d3RVcrWKaNM8SXsUTov7ooDlgtPTMVLZ8mF4UzeQ2-zXFIl_wDpftGxwSyIqic8eDUOU6Nwf9ly9O3GDlYP4tYtHUC9VlIaKbMq7qjmg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:24:26 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:24:26 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:24:26 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:24:26 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:24:27 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YB29NdE0JZh_eUwbIglByhs9W2ve5rzG-dZ_GYyDR8p4yzj5WrdpGddhszvABUpIYjER8nJtNtKjjNndWMua60X7VL2xAkFEGL2pAt0SB6zSPV3APG4EnJ-c5pqTgHsSUfWP-uW4dfwPHvETJ_1bdHdAtBNM53N30VfDXhyJQ4Y7U_j964WvWemIbsWinwptBplYojYaXZFWWXdc1Ozim3AvZ9msDb4Wb07fonavUrBhe0d3RVcrWKaNM8SXsUTov7ooDlgtPTMVLZ8mF4UzeQ2-zXFIl_wDpftGxwSyIqic8eDUOU6Nwf9ly9O3GDlYP4tYtHUC9VlIaKbMq7qjmg\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:25:35 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:25:35 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:25:36 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:25:36 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:25:36 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:25:36 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:25:36 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:25:36 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:25:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:25:36 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:25:36 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:25:38 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ez4rJ2fOhf30H7LfZmbq4_WsoZxIKBsNO5mNflL5huDAgHa0Y-1PAZlc7jrPkypoyHN39plkDXCGMJXJyQ4QqzjkMmJ30VSYbrhBx6Sz9x56-XCjt2siJ1r5WClqx0ZNlqEESZ-B8_sxZr2F-nl397LmGbSToqSvT_3Jn_laCGl4V9hD2pk-5_BMwS1OinZPJDxWTXuUjg5cbo9knCKk0KdFN6MJtNsF9cw65-Rgfkufc_QTWq0IxUehlzjiy85SjdkMWWsWQQ_qO2Wi1mGZz0q3oBaPP4A1H4ZsQ_qsqIfBjkBM_m1XXUyLRr1pt1KVp6kDbqDGVT59ng-FJSR5zQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:25:38 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:25:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:25:38 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:25:38 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:25:38 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ez4rJ2fOhf30H7LfZmbq4_WsoZxIKBsNO5mNflL5huDAgHa0Y-1PAZlc7jrPkypoyHN39plkDXCGMJXJyQ4QqzjkMmJ30VSYbrhBx6Sz9x56-XCjt2siJ1r5WClqx0ZNlqEESZ-B8_sxZr2F-nl397LmGbSToqSvT_3Jn_laCGl4V9hD2pk-5_BMwS1OinZPJDxWTXuUjg5cbo9knCKk0KdFN6MJtNsF9cw65-Rgfkufc_QTWq0IxUehlzjiy85SjdkMWWsWQQ_qO2Wi1mGZz0q3oBaPP4A1H4ZsQ_qsqIfBjkBM_m1XXUyLRr1pt1KVp6kDbqDGVT59ng-FJSR5zQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:25:38 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:25:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:25:38 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:25:38 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:25:39 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ez4rJ2fOhf30H7LfZmbq4_WsoZxIKBsNO5mNflL5huDAgHa0Y-1PAZlc7jrPkypoyHN39plkDXCGMJXJyQ4QqzjkMmJ30VSYbrhBx6Sz9x56-XCjt2siJ1r5WClqx0ZNlqEESZ-B8_sxZr2F-nl397LmGbSToqSvT_3Jn_laCGl4V9hD2pk-5_BMwS1OinZPJDxWTXuUjg5cbo9knCKk0KdFN6MJtNsF9cw65-Rgfkufc_QTWq0IxUehlzjiy85SjdkMWWsWQQ_qO2Wi1mGZz0q3oBaPP4A1H4ZsQ_qsqIfBjkBM_m1XXUyLRr1pt1KVp6kDbqDGVT59ng-FJSR5zQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:25:39 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:25:39 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:25:39 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:25:39 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:25:40 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ez4rJ2fOhf30H7LfZmbq4_WsoZxIKBsNO5mNflL5huDAgHa0Y-1PAZlc7jrPkypoyHN39plkDXCGMJXJyQ4QqzjkMmJ30VSYbrhBx6Sz9x56-XCjt2siJ1r5WClqx0ZNlqEESZ-B8_sxZr2F-nl397LmGbSToqSvT_3Jn_laCGl4V9hD2pk-5_BMwS1OinZPJDxWTXuUjg5cbo9knCKk0KdFN6MJtNsF9cw65-Rgfkufc_QTWq0IxUehlzjiy85SjdkMWWsWQQ_qO2Wi1mGZz0q3oBaPP4A1H4ZsQ_qsqIfBjkBM_m1XXUyLRr1pt1KVp6kDbqDGVT59ng-FJSR5zQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:25:40 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:25:40 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:25:40 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:25:40 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:25:41 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ez4rJ2fOhf30H7LfZmbq4_WsoZxIKBsNO5mNflL5huDAgHa0Y-1PAZlc7jrPkypoyHN39plkDXCGMJXJyQ4QqzjkMmJ30VSYbrhBx6Sz9x56-XCjt2siJ1r5WClqx0ZNlqEESZ-B8_sxZr2F-nl397LmGbSToqSvT_3Jn_laCGl4V9hD2pk-5_BMwS1OinZPJDxWTXuUjg5cbo9knCKk0KdFN6MJtNsF9cw65-Rgfkufc_QTWq0IxUehlzjiy85SjdkMWWsWQQ_qO2Wi1mGZz0q3oBaPP4A1H4ZsQ_qsqIfBjkBM_m1XXUyLRr1pt1KVp6kDbqDGVT59ng-FJSR5zQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:11 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:26:11 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:26:12 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:26:12 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:26:12 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:26:12 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:26:12 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:26:12 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:12 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:26:12 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:12 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:14 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VHOA1Gw5Yh2OJg-cIKqMG7kvTrEwWXCK0eKs3-GxcUtl_H-12kWsQaNqtUj7Y3MSGmNFwGIEWLR-SqqNlBJMZiOjxrOPMpbKAexIMQrDltEw6gzHwtvsc8yeESCuOKMAEI-u4hzvzlS3x7BFaibrZ-rlZuEI-Sjo68tx1SZU7GOYOzz6Y5eU4sC6RbDWgpkrlCM-VwVrakZ3zBVgtkZ50oResvjJFK6of0UJ3T8CeKbvkZ5e2U0xh4gQt5xcf4jlzZiG6nOjACPYDx8f6vrUaDqP1FnLsq-I7vNKyoDEU227-ybsIqiLU-JMXhxMT20VOyiC9qL0gb3TKdyV6Ar85w\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:14 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:14 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:26:14 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:14 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:14 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VHOA1Gw5Yh2OJg-cIKqMG7kvTrEwWXCK0eKs3-GxcUtl_H-12kWsQaNqtUj7Y3MSGmNFwGIEWLR-SqqNlBJMZiOjxrOPMpbKAexIMQrDltEw6gzHwtvsc8yeESCuOKMAEI-u4hzvzlS3x7BFaibrZ-rlZuEI-Sjo68tx1SZU7GOYOzz6Y5eU4sC6RbDWgpkrlCM-VwVrakZ3zBVgtkZ50oResvjJFK6of0UJ3T8CeKbvkZ5e2U0xh4gQt5xcf4jlzZiG6nOjACPYDx8f6vrUaDqP1FnLsq-I7vNKyoDEU227-ybsIqiLU-JMXhxMT20VOyiC9qL0gb3TKdyV6Ar85w\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:14 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:14 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:26:14 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:14 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:15 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VHOA1Gw5Yh2OJg-cIKqMG7kvTrEwWXCK0eKs3-GxcUtl_H-12kWsQaNqtUj7Y3MSGmNFwGIEWLR-SqqNlBJMZiOjxrOPMpbKAexIMQrDltEw6gzHwtvsc8yeESCuOKMAEI-u4hzvzlS3x7BFaibrZ-rlZuEI-Sjo68tx1SZU7GOYOzz6Y5eU4sC6RbDWgpkrlCM-VwVrakZ3zBVgtkZ50oResvjJFK6of0UJ3T8CeKbvkZ5e2U0xh4gQt5xcf4jlzZiG6nOjACPYDx8f6vrUaDqP1FnLsq-I7vNKyoDEU227-ybsIqiLU-JMXhxMT20VOyiC9qL0gb3TKdyV6Ar85w\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:15 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:15 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:26:15 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:15 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:16 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VHOA1Gw5Yh2OJg-cIKqMG7kvTrEwWXCK0eKs3-GxcUtl_H-12kWsQaNqtUj7Y3MSGmNFwGIEWLR-SqqNlBJMZiOjxrOPMpbKAexIMQrDltEw6gzHwtvsc8yeESCuOKMAEI-u4hzvzlS3x7BFaibrZ-rlZuEI-Sjo68tx1SZU7GOYOzz6Y5eU4sC6RbDWgpkrlCM-VwVrakZ3zBVgtkZ50oResvjJFK6of0UJ3T8CeKbvkZ5e2U0xh4gQt5xcf4jlzZiG6nOjACPYDx8f6vrUaDqP1FnLsq-I7vNKyoDEU227-ybsIqiLU-JMXhxMT20VOyiC9qL0gb3TKdyV6Ar85w\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:16 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:26:16 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:16 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:17 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VHOA1Gw5Yh2OJg-cIKqMG7kvTrEwWXCK0eKs3-GxcUtl_H-12kWsQaNqtUj7Y3MSGmNFwGIEWLR-SqqNlBJMZiOjxrOPMpbKAexIMQrDltEw6gzHwtvsc8yeESCuOKMAEI-u4hzvzlS3x7BFaibrZ-rlZuEI-Sjo68tx1SZU7GOYOzz6Y5eU4sC6RbDWgpkrlCM-VwVrakZ3zBVgtkZ50oResvjJFK6of0UJ3T8CeKbvkZ5e2U0xh4gQt5xcf4jlzZiG6nOjACPYDx8f6vrUaDqP1FnLsq-I7vNKyoDEU227-ybsIqiLU-JMXhxMT20VOyiC9qL0gb3TKdyV6Ar85w\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:51 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:26:51 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:26:53 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:26:53 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:26:53 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:26:53 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:26:53 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:26:53 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:26:53 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:53 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:54 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I-riSz10nbovq7RkGeLWmY6xwfoH15s58UluGnS1VqVPJgB8T4xg8gv4Bnk_14anVVVRxUTKyECEFVXO7DQT5Ny4TKAMSSYPDDwElI-LdjnkBJ0Audvr9lAzzESquhRmC18ZA1a_AtU9RGqXsN4oKMMOoWAeG2ynpAK9ro0YuzxWO2MVOIFyrSCyeY_G2zvGqsh_zxIlIYPuqUufemCjiPuYR3NoY6xsfPsk6Y1MZkoULsLMgMK4Jfan-HbfniST2ofYtsIMNkYQQCM7V38QQQ1Q6AMBUeNAAqkTz-vJ166lpmhVFebZrfAOGZduiwRgEJGzgIkjnK0gTr62HmjA7A\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:26:54 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:54 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:55 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I-riSz10nbovq7RkGeLWmY6xwfoH15s58UluGnS1VqVPJgB8T4xg8gv4Bnk_14anVVVRxUTKyECEFVXO7DQT5Ny4TKAMSSYPDDwElI-LdjnkBJ0Audvr9lAzzESquhRmC18ZA1a_AtU9RGqXsN4oKMMOoWAeG2ynpAK9ro0YuzxWO2MVOIFyrSCyeY_G2zvGqsh_zxIlIYPuqUufemCjiPuYR3NoY6xsfPsk6Y1MZkoULsLMgMK4Jfan-HbfniST2ofYtsIMNkYQQCM7V38QQQ1Q6AMBUeNAAqkTz-vJ166lpmhVFebZrfAOGZduiwRgEJGzgIkjnK0gTr62HmjA7A\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:55 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:55 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:26:55 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:55 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:56 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I-riSz10nbovq7RkGeLWmY6xwfoH15s58UluGnS1VqVPJgB8T4xg8gv4Bnk_14anVVVRxUTKyECEFVXO7DQT5Ny4TKAMSSYPDDwElI-LdjnkBJ0Audvr9lAzzESquhRmC18ZA1a_AtU9RGqXsN4oKMMOoWAeG2ynpAK9ro0YuzxWO2MVOIFyrSCyeY_G2zvGqsh_zxIlIYPuqUufemCjiPuYR3NoY6xsfPsk6Y1MZkoULsLMgMK4Jfan-HbfniST2ofYtsIMNkYQQCM7V38QQQ1Q6AMBUeNAAqkTz-vJ166lpmhVFebZrfAOGZduiwRgEJGzgIkjnK0gTr62HmjA7A\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:56 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:56 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:26:56 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:56 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:57 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I-riSz10nbovq7RkGeLWmY6xwfoH15s58UluGnS1VqVPJgB8T4xg8gv4Bnk_14anVVVRxUTKyECEFVXO7DQT5Ny4TKAMSSYPDDwElI-LdjnkBJ0Audvr9lAzzESquhRmC18ZA1a_AtU9RGqXsN4oKMMOoWAeG2ynpAK9ro0YuzxWO2MVOIFyrSCyeY_G2zvGqsh_zxIlIYPuqUufemCjiPuYR3NoY6xsfPsk6Y1MZkoULsLMgMK4Jfan-HbfniST2ofYtsIMNkYQQCM7V38QQQ1Q6AMBUeNAAqkTz-vJ166lpmhVFebZrfAOGZduiwRgEJGzgIkjnK0gTr62HmjA7A\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:26:57 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:26:57 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:26:57 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:26:57 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:26:58 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I-riSz10nbovq7RkGeLWmY6xwfoH15s58UluGnS1VqVPJgB8T4xg8gv4Bnk_14anVVVRxUTKyECEFVXO7DQT5Ny4TKAMSSYPDDwElI-LdjnkBJ0Audvr9lAzzESquhRmC18ZA1a_AtU9RGqXsN4oKMMOoWAeG2ynpAK9ro0YuzxWO2MVOIFyrSCyeY_G2zvGqsh_zxIlIYPuqUufemCjiPuYR3NoY6xsfPsk6Y1MZkoULsLMgMK4Jfan-HbfniST2ofYtsIMNkYQQCM7V38QQQ1Q6AMBUeNAAqkTz-vJ166lpmhVFebZrfAOGZduiwRgEJGzgIkjnK0gTr62HmjA7A\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:27:29 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 22 2024 20:27:29 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 22 2024 20:27:31 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 22 2024 20:27:31 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 22 2024 20:27:31 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Mon Apr 22 2024 20:27:31 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Mon Apr 22 2024 20:27:31 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Apr 22 2024 20:27:31 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:27:31 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Mon Apr 22 2024 20:27:31 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:27:31 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:27:32 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZVVqT3dc-fUtXL3WL_JECRmihCFeQiJ0Sfs8WmxtXHo8lLaDoIrkOD4FDp6r2J7tlH3ZFFmOrWx0d7l9Z0kpJYkKoGD1I2coItxAf43rEjTH8unnnAN0d7jlwTqzqs9740JXRo_2w6ubEWhO7y8c8BkAM4PvVGHMBC46l1I9ZNXoLbr8KWegb6lfj5G5BAt4IvSxVSGvv4hCnK6GqDpC9EvJRuyqUjWmQJWFNjnHwOzRb_vONh4wPrEXjWVbhIIR2AF4vVjMqPt2ooZfbF8KJEJOUb-TNgr1V_K8eAxitqAYBE_rnplAOdvLeuAHR3UI6AMVd49ocCjun4Ta5l_plQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:27:32 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Mon Apr 22 2024 20:27:32 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:27:32 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:27:32 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:27:33 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZVVqT3dc-fUtXL3WL_JECRmihCFeQiJ0Sfs8WmxtXHo8lLaDoIrkOD4FDp6r2J7tlH3ZFFmOrWx0d7l9Z0kpJYkKoGD1I2coItxAf43rEjTH8unnnAN0d7jlwTqzqs9740JXRo_2w6ubEWhO7y8c8BkAM4PvVGHMBC46l1I9ZNXoLbr8KWegb6lfj5G5BAt4IvSxVSGvv4hCnK6GqDpC9EvJRuyqUjWmQJWFNjnHwOzRb_vONh4wPrEXjWVbhIIR2AF4vVjMqPt2ooZfbF8KJEJOUb-TNgr1V_K8eAxitqAYBE_rnplAOdvLeuAHR3UI6AMVd49ocCjun4Ta5l_plQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:27:33 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:27:33 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Mon Apr 22 2024 20:27:33 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:27:33 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:27:34 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZVVqT3dc-fUtXL3WL_JECRmihCFeQiJ0Sfs8WmxtXHo8lLaDoIrkOD4FDp6r2J7tlH3ZFFmOrWx0d7l9Z0kpJYkKoGD1I2coItxAf43rEjTH8unnnAN0d7jlwTqzqs9740JXRo_2w6ubEWhO7y8c8BkAM4PvVGHMBC46l1I9ZNXoLbr8KWegb6lfj5G5BAt4IvSxVSGvv4hCnK6GqDpC9EvJRuyqUjWmQJWFNjnHwOzRb_vONh4wPrEXjWVbhIIR2AF4vVjMqPt2ooZfbF8KJEJOUb-TNgr1V_K8eAxitqAYBE_rnplAOdvLeuAHR3UI6AMVd49ocCjun4Ta5l_plQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:27:34 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:27:34 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Mon Apr 22 2024 20:27:34 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:27:34 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:27:35 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZVVqT3dc-fUtXL3WL_JECRmihCFeQiJ0Sfs8WmxtXHo8lLaDoIrkOD4FDp6r2J7tlH3ZFFmOrWx0d7l9Z0kpJYkKoGD1I2coItxAf43rEjTH8unnnAN0d7jlwTqzqs9740JXRo_2w6ubEWhO7y8c8BkAM4PvVGHMBC46l1I9ZNXoLbr8KWegb6lfj5G5BAt4IvSxVSGvv4hCnK6GqDpC9EvJRuyqUjWmQJWFNjnHwOzRb_vONh4wPrEXjWVbhIIR2AF4vVjMqPt2ooZfbF8KJEJOUb-TNgr1V_K8eAxitqAYBE_rnplAOdvLeuAHR3UI6AMVd49ocCjun4Ta5l_plQ\"}}"}}:executing pullOpenRO
Mon Apr 22 2024 20:27:35 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Mon Apr 22 2024 20:27:35 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Mon Apr 22 2024 20:27:35 GMT+0530 : {}:executing performGlPullWithDateRange
Mon Apr 22 2024 20:27:35 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 22 2024 20:27:36 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZVVqT3dc-fUtXL3WL_JECRmihCFeQiJ0Sfs8WmxtXHo8lLaDoIrkOD4FDp6r2J7tlH3ZFFmOrWx0d7l9Z0kpJYkKoGD1I2coItxAf43rEjTH8unnnAN0d7jlwTqzqs9740JXRo_2w6ubEWhO7y8c8BkAM4PvVGHMBC46l1I9ZNXoLbr8KWegb6lfj5G5BAt4IvSxVSGvv4hCnK6GqDpC9EvJRuyqUjWmQJWFNjnHwOzRb_vONh4wPrEXjWVbhIIR2AF4vVjMqPt2ooZfbF8KJEJOUb-TNgr1V_K8eAxitqAYBE_rnplAOdvLeuAHR3UI6AMVd49ocCjun4Ta5l_plQ\"}}"}}:executing pullOpenRO
