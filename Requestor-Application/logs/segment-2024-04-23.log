Tue Apr 23 2024 09:48:32 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 09:48:32 GMT+0530 : {"Dealer ID":"12345"}:R<PERSON> <PERSON><PERSON> started
Tue Apr 23 2024 09:48:34 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 09:48:34 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 09:48:34 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 09:48:34 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 09:48:34 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 09:48:34 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 09:48:34 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 09:48:34 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 09:48:34 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 09:48:36 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XwQEfP75anxIR-Jx0I6SkNUWP8QQJTeujrke5tlskHPaMgV7HQBiH12E1Csrh8yO3ZxFdE_G9Awv4yYC0awTRfeWRD2C93YIMqmHnVIFZ4A_KIpj2Q3Ol17vU1Q_wgH2sMSgnWvKTs6RjxLdp0Dpiwwf4hYaOuRnULBFd-eMtIlK92_taJ3C54rozFpgxk9eUg4iTfJ6UxPnN_uEoUKNllKIr1rFxmGoQL4AJZm3OcLe7zKGvxJZWUXQKaYDk7PdtgRrxOQusZgNSLYdieM4woU8GpI9Xztd6xgZSseukFtoyi_sXpwAv-WlqASASXFe_l1HF8vrMFruYFZnk9q4vQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 09:48:36 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 09:48:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 09:48:36 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 09:48:36 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 09:48:37 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XwQEfP75anxIR-Jx0I6SkNUWP8QQJTeujrke5tlskHPaMgV7HQBiH12E1Csrh8yO3ZxFdE_G9Awv4yYC0awTRfeWRD2C93YIMqmHnVIFZ4A_KIpj2Q3Ol17vU1Q_wgH2sMSgnWvKTs6RjxLdp0Dpiwwf4hYaOuRnULBFd-eMtIlK92_taJ3C54rozFpgxk9eUg4iTfJ6UxPnN_uEoUKNllKIr1rFxmGoQL4AJZm3OcLe7zKGvxJZWUXQKaYDk7PdtgRrxOQusZgNSLYdieM4woU8GpI9Xztd6xgZSseukFtoyi_sXpwAv-WlqASASXFe_l1HF8vrMFruYFZnk9q4vQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 09:48:37 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 09:48:37 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 09:48:37 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 09:48:37 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 09:48:38 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XwQEfP75anxIR-Jx0I6SkNUWP8QQJTeujrke5tlskHPaMgV7HQBiH12E1Csrh8yO3ZxFdE_G9Awv4yYC0awTRfeWRD2C93YIMqmHnVIFZ4A_KIpj2Q3Ol17vU1Q_wgH2sMSgnWvKTs6RjxLdp0Dpiwwf4hYaOuRnULBFd-eMtIlK92_taJ3C54rozFpgxk9eUg4iTfJ6UxPnN_uEoUKNllKIr1rFxmGoQL4AJZm3OcLe7zKGvxJZWUXQKaYDk7PdtgRrxOQusZgNSLYdieM4woU8GpI9Xztd6xgZSseukFtoyi_sXpwAv-WlqASASXFe_l1HF8vrMFruYFZnk9q4vQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 09:48:38 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 09:48:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 09:48:38 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 09:48:38 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 09:48:39 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XwQEfP75anxIR-Jx0I6SkNUWP8QQJTeujrke5tlskHPaMgV7HQBiH12E1Csrh8yO3ZxFdE_G9Awv4yYC0awTRfeWRD2C93YIMqmHnVIFZ4A_KIpj2Q3Ol17vU1Q_wgH2sMSgnWvKTs6RjxLdp0Dpiwwf4hYaOuRnULBFd-eMtIlK92_taJ3C54rozFpgxk9eUg4iTfJ6UxPnN_uEoUKNllKIr1rFxmGoQL4AJZm3OcLe7zKGvxJZWUXQKaYDk7PdtgRrxOQusZgNSLYdieM4woU8GpI9Xztd6xgZSseukFtoyi_sXpwAv-WlqASASXFe_l1HF8vrMFruYFZnk9q4vQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 09:48:39 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 09:48:39 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 09:48:39 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 09:48:39 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 09:48:40 GMT+0530 : {"StoreID":{"status":false,"data":{"code":"500","message":"Unexpected error encountered in processing request: No Department id found for the Subscription id !. Please try again later!, for Request-Id: 555555555544546464"},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XwQEfP75anxIR-Jx0I6SkNUWP8QQJTeujrke5tlskHPaMgV7HQBiH12E1Csrh8yO3ZxFdE_G9Awv4yYC0awTRfeWRD2C93YIMqmHnVIFZ4A_KIpj2Q3Ol17vU1Q_wgH2sMSgnWvKTs6RjxLdp0Dpiwwf4hYaOuRnULBFd-eMtIlK92_taJ3C54rozFpgxk9eUg4iTfJ6UxPnN_uEoUKNllKIr1rFxmGoQL4AJZm3OcLe7zKGvxJZWUXQKaYDk7PdtgRrxOQusZgNSLYdieM4woU8GpI9Xztd6xgZSseukFtoyi_sXpwAv-WlqASASXFe_l1HF8vrMFruYFZnk9q4vQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:26:23 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 10:26:23 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 10:26:24 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 10:26:24 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 10:26:24 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 10:26:24 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 10:26:24 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 10:26:24 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:26:24 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 10:26:24 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:26:24 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:26:26 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:26:26 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:26:26 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 10:26:26 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:26:26 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:26:27 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:26:27 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:26:27 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 10:26:27 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:26:27 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:26:28 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:26:28 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:26:28 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 10:26:28 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:26:28 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:26:29 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:26:29 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:26:29 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 10:26:29 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:26:29 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:26:30 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PfKVYsKN-Mt7QnnYVhpYF6_tGfX0LtvpTB4sjuporXRR5Z5vL5rSZcszJz-AMKnNBmDPc399a4BJ3y_H4znwTGmCXgyMKtIn5whkszgEy5Sc-47LfCT-U65MQe8m17HXGdS46l3by4TamzmF-zWJmWYlyqtOHGG3saEfJcdewsqUaNRpP4FIzkByMvA1CaXkkNul5iVgeZRE1KcN2sBmErXYnI11C9U6O7RhX5S4-ubkGaIv_ZnMxuqwyWIpT5d6e5yehpBm-LjbWTI7pxQdx7VUVTul0TzMdClv_9CFXycZRtf9yUQluphQGq1CqOYC0Bv4lBm-66QmhzyXPyuF_A\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:57:58 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 10:57:58 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 10:57:59 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 10:57:59 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 10:57:59 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 10:57:59 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 10:57:59 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 10:57:59 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:57:59 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 10:57:59 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:57:59 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:58:01 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:58:01 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:58:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 10:58:01 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:58:01 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:58:02 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:58:02 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:58:02 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 10:58:02 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:58:02 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:58:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 10:58:03 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:58:03 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:58:03 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:58:03 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:58:04 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 10:58:04 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 10:58:04 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 10:58:04 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 10:58:04 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 10:58:05 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LB0EaJvYv-5epxCkxMVpb0wpvhCzKPHeDz9_6h0S8bc_OSmOWjowIT3AeJXbbvsMPgJRJ3750AFG1wa0VKgIs_NGE06CfnMjYAdernVkhkus9bSZDn4sQAkhA9RLSsbpgmDx7CIKTRzyVQCA3Z727ASEqyAazpsfRyYWDCEkonA6mwTk5SRcVloO-e2OtVR1cfgYIAssO3FClDaWeDqwjHRfXvwDwnHC10-7Cwf7UaxfU4vwPL61tXFJ5B7G9s8zXITmkHqGMGT31UmOTJOQcK2YtkX-O7wLpB0Jvz1IY60ubmtO2zhs76XsnmFwwIXjnVvhaaE_1X_ACkXNZRaeww\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 11:03:01 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 11:03:01 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 11:03:03 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 11:03:03 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 11:03:03 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 23 2024 11:03:03 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 11:03:03 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 23 2024 11:03:03 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:03:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 11:03:03 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 11:03:03 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:03:46 GMT+0530 : {"StoreID":"01b3dacd-0904-90d4-005f-0d03092ef60e"}:executing pullOpenRO
Tue Apr 23 2024 11:03:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 11:03:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:03:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 11:03:48 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 11:03:48 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:04:30 GMT+0530 : {"StoreID":"01b3dacd-0904-90d9-005f-0d03092ee90e"}:executing pullOpenRO
Tue Apr 23 2024 11:04:32 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 11:04:32 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:04:32 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 11:04:32 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 11:04:32 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:05:14 GMT+0530 : {"StoreID":"01b3dace-0904-90d9-005f-0d03092ee92e"}:executing pullOpenRO
Tue Apr 23 2024 11:05:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 11:05:16 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:05:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 11:05:16 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 11:05:16 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:05:58 GMT+0530 : {"StoreID":"01b3dacf-0904-90d4-005f-0d03092ef682"}:executing pullOpenRO
Tue Apr 23 2024 11:05:59 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 11:05:59 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:05:59 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 11:05:59 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 11:05:59 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:06:41 GMT+0530 : {"StoreID":"01b3dad0-0904-90d9-005f-0d03092ee9f2"}:executing pullOpenRO
Tue Apr 23 2024 11:06:43 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 11:06:43 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 11:06:43 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 11:06:43 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 11:06:43 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 11:06:43 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:06:43 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 11:06:43 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 11:06:43 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:06:44 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 11:06:44 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:06:44 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 11:06:44 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 11:06:44 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:06:45 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 11:06:45 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:06:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 11:06:45 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 11:06:45 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:06:46 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 11:06:46 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:06:46 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 11:06:46 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 11:06:46 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:06:47 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 11:06:47 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 11:06:47 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 11:06:47 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 11:06:47 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:06:48 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XhMDzLGWU2jiNQdxAr0MHKe-y6u681ESu1-_FvpNWRqsYRRoAvVzLE8LTmvZ_lMFQRxapHTQEqRF2FfhsKxiX-qBRMG9nohhsW5dsH_9FKHkTj4cXWGdqnX5PSe2yMPvYxRUuMX6pu_LswA1ws-eCYU2DFLwUpb8XOBrko9Qa_ytp0GRdqTK8GbFBklyEwqHjagvnXvG0EV0gBrJupyqizsj66FVTZ7IUs1Q-7L87iibi02rKHGN9Dd4Cf4fJUL04gphCIu286EM8MpD6VgqB9kx2Cq1F1JuiKMt6OImiXt7-Vq8-Xq-HuTZpGdYKaJG1yRqnKqxdh6EWgcB7tGPdQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 11:06:48 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 11:06:48 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Tue Apr 23 2024 11:06:48 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 11:06:48 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 11:06:49 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 11:06:49 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Tue Apr 23 2024 11:06:49 GMT+0530 : {}:executing make model
Tue Apr 23 2024 11:06:49 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 11:06:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 11:06:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  coa data Pull
Tue Apr 23 2024 11:06:50 GMT+0530 : {}:executing COA pull
Tue Apr 23 2024 11:06:50 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 11:06:51 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 11:06:51 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 11:06:51 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 11:06:51 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 11:06:51 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 12:35:44 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 12:35:44 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 12:35:46 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 12:35:46 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 12:35:46 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 12:35:46 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 12:35:46 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 12:35:46 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:35:46 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 12:35:46 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:35:46 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:38:18 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 12:38:18 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 12:38:20 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 12:38:20 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 12:38:20 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 12:38:20 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 12:38:20 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 12:38:20 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:38:20 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 12:38:20 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:38:20 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:38:40 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 12:38:40 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 12:38:41 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 12:38:41 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 12:38:41 GMT+0530 : undefined:executing pullCloseROdata
Tue Apr 23 2024 12:38:41 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Tue Apr 23 2024 12:38:41 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 12:39:37 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 12:39:37 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 12:39:38 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 12:39:38 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 12:39:38 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Tue Apr 23 2024 12:39:38 GMT+0530 : undefined:executing pullCloseROdata
Tue Apr 23 2024 12:39:38 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 12:42:42 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 12:42:42 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 12:42:43 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 12:42:43 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 12:42:43 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Tue Apr 23 2024 12:42:43 GMT+0530 : undefined:executing pullCloseROdata
Tue Apr 23 2024 12:42:43 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 12:43:22 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 12:43:22 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 12:43:24 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 12:43:24 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 12:43:24 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 12:43:24 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 12:43:24 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 12:43:24 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:43:24 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 12:43:24 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:43:24 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:43:26 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:43:26 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:43:26 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 12:43:26 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:43:26 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:43:29 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:43:29 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:43:29 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 12:43:29 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:43:29 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:43:30 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:43:30 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:43:30 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 12:43:30 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:43:30 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:43:31 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:43:31 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:43:31 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 12:43:31 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:43:31 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:43:32 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B5a1AqalagGD02tUH2ZDxCNsJ4l2TvhSHoCyQc9V-NSISOqgDzQ1VN8owX2jDUDpzHHY2LeZMFavQ59wdeANX7X3ILMh--rvNWjIKLpIuQeg4d2JMZlWHkuRXAD9UX1Z4EBNPbsaUBmNJdIa18w8lkIJggApp87Cksl2yi5r_miGsxuGPRAiDNxY-Evh2pfl9BmsC0Lb73KNn0GZCsJc40dnCqkVgnn-w_pOksM1sd3CjDZWttRh1qe-8XPso5l0YKJ9ZrDY6h_0FuypAt9n0Thk4OZjy8HJvtVkyGm7JmlVjQBdC2H8uU83ML0lfci2TLweEwocPWJerABO4at5PA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:45:39 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 12:45:39 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 12:45:40 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 12:45:40 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 12:45:40 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 12:45:40 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 12:45:40 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 12:45:40 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:45:40 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 12:45:40 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:45:40 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:45:42 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:45:42 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:45:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 12:45:42 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:45:42 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:45:43 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:45:43 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:45:43 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 12:45:43 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:45:43 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:45:44 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:45:44 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:45:44 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 12:45:44 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:45:44 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:45:45 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:45:45 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:45:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 12:45:45 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:45:45 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:45:46 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xh1dSshLLfCvf72DukWENrc1PcAf4rMTZ7YxqyTSi05ejRHAVCmZ50lY5XxuGb4MvEyuowAEb4jUslc6ZYLXC5E7Gi5bH4mo3wrD3a6a73eKNUNJyZPj2JqEDdWfLED6nRS9ixd_nGS1yLUwrcfc473i4qP8r5JyYrDR567TiO1WBfWeiE8jUBL0jgNdMK1ks7Z11nP5n4tsSyzfoP8Usmy4M1q0BaXlQkrhwyEjebYlJ3QGys_nvK8vY6OTVwgN7MWZNIqnt8NdZPUWDB5NXp8suAsWitXYN6IYm-PkrF-HMlzB_652S_rms0QOMEGAFmiUN39XEmavJHkDeqlIPw\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:47:48 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 12:47:48 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 12:47:50 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 12:47:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 12:47:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 12:47:50 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 12:47:50 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 12:47:50 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:47:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 12:47:50 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:47:50 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:47:52 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:47:52 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 12:47:52 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:47:52 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:47:52 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:47:53 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:47:53 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:47:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 12:47:53 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:47:53 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:47:54 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:47:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:47:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 12:47:54 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:47:54 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:47:55 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 12:47:55 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 12:47:55 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 12:47:55 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 12:47:55 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 12:47:56 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IEza60rEA2L8uL2HdLEqCkQI9pb72kPOanSNsyfi6Jszwkbfgu78W-YW0ncVvgBDmtys5YsBHyXU9JRDBdcM0BCuKvyX7SlKCQpodwJuFYUnAEYmKQw5J5hspxXWqojipcPU2G5KxSu3S4pXNrELkoY7Ogf4u_zSwMYfSi6cfRVTy7CRbybalsg_wEyKimKGxyL3lnrdtOu8-DxKyaaR5BxNqwhIkFxKio7J8RBArfQFF0l5fvYoDC1L1cyMdgj9NCy12VlviN8tUEmRzA0qToafxnmQZgvrMR0n_h1wDBpQZ95dDflR0_qFK0uYcDIx7Es8QDb9vtGwltifvaPqdg\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:25:20 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:25:20 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:25:21 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:25:21 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:25:21 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:25:21 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:25:21 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:28:18 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:28:18 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:28:19 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:28:19 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:28:19 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:28:19 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:28:19 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:28:54 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:28:54 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:28:55 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:28:55 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:28:55 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:28:55 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:28:55 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:28:55 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:29:49 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:29:49 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:29:50 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:29:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:29:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:29:50 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:29:50 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:29:50 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:31:29 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:31:29 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:31:30 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:31:30 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:31:30 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:31:30 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:31:30 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:31:30 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:34:46 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:34:46 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:34:48 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:34:48 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:34:48 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:34:48 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:34:48 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:34:48 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:36:03 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:36:03 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:36:05 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:36:05 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:36:05 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:36:05 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:36:05 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:36:05 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:39:10 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:39:10 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:39:11 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:39:11 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:39:11 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:39:11 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:39:11 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:39:11 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:41:02 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:41:02 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:41:03 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:41:03 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:41:03 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 23 2024 14:41:03 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 14:41:03 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 23 2024 14:41:03 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:41:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 14:41:03 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 14:41:03 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:41:46 GMT+0530 : {"StoreID":"01b3dba7-0904-90d9-005f-0d03092f69b6"}:executing pullOpenRO
Tue Apr 23 2024 14:41:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 14:41:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:41:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 14:41:48 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 14:41:48 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:42:30 GMT+0530 : {"StoreID":"01b3dba7-0904-90d9-005f-0d03092f69d2"}:executing pullOpenRO
Tue Apr 23 2024 14:42:31 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 14:42:31 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:42:31 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 14:42:31 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 14:42:31 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:43:13 GMT+0530 : {"StoreID":"01b3dba8-0904-90d4-005f-0d03092f73ce"}:executing pullOpenRO
Tue Apr 23 2024 14:43:15 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 14:43:15 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:43:15 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 14:43:15 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 14:43:15 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:43:57 GMT+0530 : {"StoreID":"01b3dba9-0904-90d9-005f-0d03092f6a7a"}:executing pullOpenRO
Tue Apr 23 2024 14:43:58 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 14:43:58 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:43:58 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 14:43:58 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 23 2024 14:43:58 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:44:40 GMT+0530 : {"StoreID":"01b3dbaa-0904-90d9-005f-0d03092f6a92"}:executing pullOpenRO
Tue Apr 23 2024 14:44:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 14:44:42 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:44:42 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 14:44:42 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 14:44:42 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 14:44:42 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:44:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 14:44:42 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:44:42 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:44:44 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:44:44 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:44:44 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 14:44:44 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:44:44 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:44:45 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:44:45 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:44:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 14:44:45 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:44:45 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:44:47 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:44:47 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:44:47 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 14:44:47 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:44:47 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:44:48 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:44:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:44:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 14:44:48 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:44:48 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:44:49 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kQm-Dpwvq2MhQUkuqhJpbaWHgT_Lze9EEkkWOiKzlC8zM6pBPJ7yPFWnD9OUxkiY_VvnOauQ1NUyIMCoHr_inLu9xqjYhAPk3yBAkPpRaebfTWWtxQieZyenRA0PJbcN7yh3R3CKo5hcO4y-yzVEvSoT4do2ER8DdnnTpFFwKslrOyf5K7bTAVZGN5tFmRx03yEXZ1ZmCTgVLprMm1CxNN0NLS2-n8zB3k126XaqMz8k3tg7TuEGdQOt4Y0rPe46SqJUFtUVeqpbhzT_sGG3T0o44NT0ivcoIPSN-G2Hzo1XG20xZOqDDX6wp7lp-s37byfYwIZGfL80-xVvCN4GEQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:44:49 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:44:49 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Tue Apr 23 2024 14:44:49 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:44:49 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:44:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:44:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Tue Apr 23 2024 14:44:50 GMT+0530 : {}:executing make model
Tue Apr 23 2024 14:44:50 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:44:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:44:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:44:50 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:44:50 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:44:50 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:51:39 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:51:39 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:51:40 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:51:40 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:51:40 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 14:51:40 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 14:51:40 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 14:51:40 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:51:40 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 14:51:40 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:51:40 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:51:43 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:51:43 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:51:43 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 14:51:43 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:51:43 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:51:45 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:51:45 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:51:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 14:51:45 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:51:45 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:51:45 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:51:45 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:51:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 14:51:45 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:51:45 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:51:46 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:51:46 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:51:46 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 14:51:46 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:51:46 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:51:47 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bSld-zJROPhXtqKa1ZavfkV3AAchjvQ5-MSpq-aYlWW-W12kTZupfFCqaICNaqjwvb5wqEsjDzxN0CQMLnzQsxz6l8Qu2VqJKKBKn18mMLc-L2uJ6lzfdSRJc9maYzqfYWjBEnjkr4lif012WSankH-iZtf881QsWgnxwhQNuahE_g5mg9FEXIeDjs3BDp0u-9LkKSO2etFMqYNGZBcPOcylKnFxyUZrLwsZ8vfSCdZm9LhIXyVkdjTW2JczC-uIxHWBN3Lw7-pOOdHVFRKTOzYMH8VrNrS7Gfz9fwHBP-wwl36BUCdh9eI96t2iLt-U2rF2busEHB-g0aX5XOJEbQ\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:51:47 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:51:47 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:51:47 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:51:47 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:51:47 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 14:58:55 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 14:58:55 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 14:58:57 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 14:58:57 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:58:57 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 14:58:57 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 14:58:57 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 14:58:57 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:58:57 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 14:58:57 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:58:57 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:58:59 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:58:59 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:58:59 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 14:58:59 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:58:59 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:59:00 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:59:00 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:59:00 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 14:59:00 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:59:00 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:59:01 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:59:01 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:59:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 14:59:01 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:59:01 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:59:01 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:59:01 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 14:59:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 14:59:01 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 14:59:01 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 14:59:03 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2022-01-01T10:15:30&endDate=2022-05-01T10:15:30\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cQgggVVkDgKjx9Q85Lc3Hf-uGzafXNjjZ_DLPtdaigDjIo2xokHikd14WHmOMXmBR2x2tHZobGHQkt3On0ox9K2H2rKaq_S-9JBeuxAKFvUuBy-TBSRX4BxkWEjCBnUTkvR_flxe1AKw4cXV4zO3xcWLtVk6udnaGUDPxAezGLg47PzkCOIhLiZ9Q1BFXwhREwXKJdlnoeMO0x34hKCEAG_tScx5RjOetPHueVUAw-jESlFPtrX_zP6sTy5FStBLlSDjJhVQMjYjtO4AHohN4tVqUy6jNnli_V5u9We4ao476enV8TxHP1-iJTfPmAx-bHpGQSUb_1YiF3Bjbp2NgA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 14:59:03 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 14:59:03 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Tue Apr 23 2024 14:59:03 GMT+0530 : {}:executing performOpenROPull
Tue Apr 23 2024 14:59:03 GMT+0530 : {}:executing pullROdata
Tue Apr 23 2024 14:59:03 GMT+0530 : {}:executing pullOpenRO
Tue Apr 23 2024 15:01:10 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 15:01:10 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 15:01:12 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 15:01:12 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 15:01:12 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 15:01:12 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 15:01:12 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 15:01:12 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 15:01:12 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 15:01:12 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 15:01:12 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 15:01:14 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-04-30T18:30:00.000Z&endDate=2021-05-04T18:30:00.000Z","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-04-30T18:30:00.000Z&endDate=2021-05-04T18:30:00.000Z\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 15:01:14 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 15:01:14 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 15:01:14 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 15:01:14 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 15:01:15 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-03-31T18:30:00.000Z&endDate=2021-04-29T18:30:00.000Z","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-03-31T18:30:00.000Z&endDate=2021-04-29T18:30:00.000Z\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 15:01:15 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 15:01:15 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 15:01:15 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 15:01:15 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 15:01:16 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-02-28T18:30:00.000Z&endDate=2021-03-30T18:30:00.000Z","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-02-28T18:30:00.000Z&endDate=2021-03-30T18:30:00.000Z\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 15:01:16 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 15:01:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 15:01:16 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 15:01:16 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 15:01:17 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-01-31T18:30:00.000Z&endDate=2021-02-27T18:30:00.000Z","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-01-31T18:30:00.000Z&endDate=2021-02-27T18:30:00.000Z\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 15:01:17 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 15:01:17 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Tue Apr 23 2024 15:01:17 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 15:01:17 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 15:01:18 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2020-12-31T18:30:00.000Z&endDate=2021-01-30T18:30:00.000Z","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2020-12-31T18:30:00.000Z&endDate=2021-01-30T18:30:00.000Z\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CtCMdpRXjl_hIjeAWv5ZipB-y3bLUI7gkv2l1H5H9iIpzt4SX4vnNj3lq5xcQAdelhnf79V0OdxZQJHFTQPBQGxyYW-vvTlXLCS96HNuTJ7BNHnyb0V3t-pzB4SvQYykUKPsXV7rmmygyhtn8Ed0XtmEEnrNr1RUjQE_nlOVzKa4c7iDg9m08ElTr2cpo_p_7DIiqhqiTI-lJ0TUals_Y-ZLeSjsIdVu-8kFz_2lBBMnBegEaJOXAVZlxb91Zo8iuznEpteKQXR9w0f1uWHAim-QeOBzYEB5jKGUoxuODZdl-zG60GpSbWsjl7OVFy6lruGDFjsuewheeEBaY3hDqA\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 15:20:06 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 23 2024 15:20:06 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 23 2024 15:20:08 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 23 2024 15:20:08 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 23 2024 15:20:08 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Tue Apr 23 2024 15:20:08 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Tue Apr 23 2024 15:20:08 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Tue Apr 23 2024 15:20:08 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 15:20:08 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Tue Apr 23 2024 15:20:08 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 15:20:08 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 15:20:11 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-04-30T18:30:00.000Z&endDate=2021-05-04T18:30:00.000Z","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bgxUHqWWkOFiicas9SfOC5Yo1sKR_0gWRDfs15dUu43ueZfd07xAI4LAjVjLhrYanjVIyj1HMAO6H12qeNO9zInT2l7kdZEW8jSesspyfNPgjpbkqrCCNtUNbzNN5vcnqW7jaBFmNubq8YDRMcZ-hns7DUPY1Tmbmc07sfDkDioTWD-Aq0mirZ2ccHEMh9RDBee_DytJOEeAGahGXPL0hIes4WVHl5UzNMlywvq3saopNBYuPBmlRSfB7ZHzXANKESjayvItlbxjc00SVibDrt03DWlp2iWxemTjLYUddEXRog5bJRaCZ-P3yxrMkCGpm6gSapkz4JIUgfZxn8RnIw","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-04-30T18:30:00.000Z&endDate=2021-05-04T18:30:00.000Z\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bgxUHqWWkOFiicas9SfOC5Yo1sKR_0gWRDfs15dUu43ueZfd07xAI4LAjVjLhrYanjVIyj1HMAO6H12qeNO9zInT2l7kdZEW8jSesspyfNPgjpbkqrCCNtUNbzNN5vcnqW7jaBFmNubq8YDRMcZ-hns7DUPY1Tmbmc07sfDkDioTWD-Aq0mirZ2ccHEMh9RDBee_DytJOEeAGahGXPL0hIes4WVHl5UzNMlywvq3saopNBYuPBmlRSfB7ZHzXANKESjayvItlbxjc00SVibDrt03DWlp2iWxemTjLYUddEXRog5bJRaCZ-P3yxrMkCGpm6gSapkz4JIUgfZxn8RnIw\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 15:20:11 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 15:20:11 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Tue Apr 23 2024 15:20:11 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 15:20:11 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 15:20:12 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-03-31T18:30:00.000Z&endDate=2021-04-29T18:30:00.000Z","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bgxUHqWWkOFiicas9SfOC5Yo1sKR_0gWRDfs15dUu43ueZfd07xAI4LAjVjLhrYanjVIyj1HMAO6H12qeNO9zInT2l7kdZEW8jSesspyfNPgjpbkqrCCNtUNbzNN5vcnqW7jaBFmNubq8YDRMcZ-hns7DUPY1Tmbmc07sfDkDioTWD-Aq0mirZ2ccHEMh9RDBee_DytJOEeAGahGXPL0hIes4WVHl5UzNMlywvq3saopNBYuPBmlRSfB7ZHzXANKESjayvItlbxjc00SVibDrt03DWlp2iWxemTjLYUddEXRog5bJRaCZ-P3yxrMkCGpm6gSapkz4JIUgfZxn8RnIw","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-03-31T18:30:00.000Z&endDate=2021-04-29T18:30:00.000Z\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bgxUHqWWkOFiicas9SfOC5Yo1sKR_0gWRDfs15dUu43ueZfd07xAI4LAjVjLhrYanjVIyj1HMAO6H12qeNO9zInT2l7kdZEW8jSesspyfNPgjpbkqrCCNtUNbzNN5vcnqW7jaBFmNubq8YDRMcZ-hns7DUPY1Tmbmc07sfDkDioTWD-Aq0mirZ2ccHEMh9RDBee_DytJOEeAGahGXPL0hIes4WVHl5UzNMlywvq3saopNBYuPBmlRSfB7ZHzXANKESjayvItlbxjc00SVibDrt03DWlp2iWxemTjLYUddEXRog5bJRaCZ-P3yxrMkCGpm6gSapkz4JIUgfZxn8RnIw\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 15:20:12 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 15:20:12 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Tue Apr 23 2024 15:20:12 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 15:20:12 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 23 2024 15:20:13 GMT+0530 : {"StoreID":{"status":false,"data":{"message":"Request failed with status code 500","name":"Error","stack":"Error: Request failed with status code 500\n    at createError (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/createError.js:16:15)\n    at settle (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/core/settle.js:17:12)\n    at IncomingMessage.handleStreamEnd (/home/<USER>/2024/GIT/DMS-FORTELLIS/Requestor-Application/node_modules/axios/lib/adapters/http.js:269:11)\n    at IncomingMessage.emit (events.js:326:22)\n    at endReadableNT (_stream_readable.js:1241:12)\n    at processTicksAndRejections (internal/process/task_queues.js:84:21)","config":{"url":"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-02-28T18:30:00.000Z&endDate=2021-03-30T18:30:00.000Z","method":"get","headers":{"Accept":"application/json","Request-Id":"555555555544546464","Department-Id":"01","Subscription-Id":"********-2c5f-438d-a77d-88f1e94c6386","Authorization":"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bgxUHqWWkOFiicas9SfOC5Yo1sKR_0gWRDfs15dUu43ueZfd07xAI4LAjVjLhrYanjVIyj1HMAO6H12qeNO9zInT2l7kdZEW8jSesspyfNPgjpbkqrCCNtUNbzNN5vcnqW7jaBFmNubq8YDRMcZ-hns7DUPY1Tmbmc07sfDkDioTWD-Aq0mirZ2ccHEMh9RDBee_DytJOEeAGahGXPL0hIes4WVHl5UzNMlywvq3saopNBYuPBmlRSfB7ZHzXANKESjayvItlbxjc00SVibDrt03DWlp2iWxemTjLYUddEXRog5bJRaCZ-P3yxrMkCGpm6gSapkz4JIUgfZxn8RnIw","User-Agent":"axios/0.21.4"},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":null,"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false}}},"requestHeader":"{\"method\":\"get\",\"maxBodyLength\":null,\"url\":\"https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=2021-02-28T18:30:00.000Z&endDate=2021-03-30T18:30:00.000Z\",\"headers\":{\"Request-Id\":\"555555555544546464\",\"Department-Id\":\"01\",\"Accept\":\"application/json\",\"Subscription-Id\":\"********-2c5f-438d-a77d-88f1e94c6386\",\"Authorization\":\"Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bgxUHqWWkOFiicas9SfOC5Yo1sKR_0gWRDfs15dUu43ueZfd07xAI4LAjVjLhrYanjVIyj1HMAO6H12qeNO9zInT2l7kdZEW8jSesspyfNPgjpbkqrCCNtUNbzNN5vcnqW7jaBFmNubq8YDRMcZ-hns7DUPY1Tmbmc07sfDkDioTWD-Aq0mirZ2ccHEMh9RDBee_DytJOEeAGahGXPL0hIes4WVHl5UzNMlywvq3saopNBYuPBmlRSfB7ZHzXANKESjayvItlbxjc00SVibDrt03DWlp2iWxemTjLYUddEXRog5bJRaCZ-P3yxrMkCGpm6gSapkz4JIUgfZxn8RnIw\"}}"}}:executing pullOpenRO
Tue Apr 23 2024 15:20:13 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Tue Apr 23 2024 15:20:13 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Tue Apr 23 2024 15:20:13 GMT+0530 : {}:executing performGlPullWithDateRange
Tue Apr 23 2024 15:20:13 GMT+0530 : {}:executing pullCloseROdata
