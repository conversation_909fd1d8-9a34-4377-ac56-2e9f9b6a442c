Fri Apr 19 2024 09:55:52 GMT+0530 : {"Dealer ID":"12345"}:<PERSON><PERSON><PERSON> started
Fri Apr 19 2024 09:55:52 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 09:55:54 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 09:55:54 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:55:54 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Fri Apr 19 2024 09:55:54 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2021","01/05/2021"]] 
Fri Apr 19 2024 09:55:54 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 19 2024 09:55:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2021,01/05/2021
Fri Apr 19 2024 09:55:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 09:55:54 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 09:55:54 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 09:56:38 GMT+0530 : {"StoreID":"01b3c409-0904-90da-005f-0d030922a1e6"}:executing pullOpenRO
Fri Apr 19 2024 09:56:59 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 09:56:59 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:56:59 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 09:56:59 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2021","01/05/2021"]] 
Fri Apr 19 2024 09:56:59 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 09:56:59 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2021,01/05/2021
Fri Apr 19 2024 09:56:59 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 09:56:59 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 09:56:59 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 09:57:00 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 09:57:00 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:57:00 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Fri Apr 19 2024 09:57:00 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 09:57:00 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 09:57:02 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:57:02 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 09:57:02 GMT+0530 : {}:executing make model
Fri Apr 19 2024 09:57:02 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 09:57:07 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:57:07 GMT+0530 : {"Dealer ID":"12345"}:Ready for  coa data Pull
Fri Apr 19 2024 09:58:00 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 09:58:00 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 09:58:04 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 09:58:04 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:58:04 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Fri Apr 19 2024 09:58:04 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2021","01/05/2021"]] 
Fri Apr 19 2024 09:58:04 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 19 2024 09:58:04 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2021,01/05/2021
Fri Apr 19 2024 09:58:04 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 09:58:04 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 09:58:04 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 09:58:52 GMT+0530 : {"StoreID":"01b3c40c-0904-90b5-005f-0d030922959a"}:executing pullOpenRO
Fri Apr 19 2024 09:58:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 09:58:53 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:58:53 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 09:58:53 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2021","01/05/2021"]] 
Fri Apr 19 2024 09:58:53 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 09:58:53 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2021,01/05/2021
Fri Apr 19 2024 09:58:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 09:58:53 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 09:58:53 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 09:58:55 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 09:58:55 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:58:55 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Fri Apr 19 2024 09:58:55 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 09:58:55 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 09:58:57 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:58:57 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 09:58:57 GMT+0530 : {}:executing make model
Fri Apr 19 2024 09:58:57 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 09:59:07 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 09:59:07 GMT+0530 : {"Dealer ID":"12345"}:Ready for  coa data Pull
Fri Apr 19 2024 10:00:10 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 10:00:10 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 10:00:14 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 10:00:14 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 10:00:14 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Fri Apr 19 2024 10:00:14 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2021","01/05/2021"]] 
Fri Apr 19 2024 10:00:14 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 19 2024 10:00:14 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2021,01/05/2021
Fri Apr 19 2024 10:00:14 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 10:00:14 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 10:00:14 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:01:05 GMT+0530 : {"StoreID":"01b3c40e-0904-90b5-005f-0d03092295e2"}:executing pullOpenRO
Fri Apr 19 2024 10:01:09 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 10:01:09 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 10:01:09 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 10:01:09 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2021","01/05/2021"]] 
Fri Apr 19 2024 10:01:09 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 10:01:09 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2021,01/05/2021
Fri Apr 19 2024 10:01:09 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 10:01:09 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:01:09 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:01:11 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:01:11 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 10:01:11 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Fri Apr 19 2024 10:01:11 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:01:11 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:01:12 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 10:01:12 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 10:01:12 GMT+0530 : {}:executing make model
Fri Apr 19 2024 10:01:12 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:01:20 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 10:01:20 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 10:01:20 GMT+0530 : {}:executing performOpenROPull
Fri Apr 19 2024 10:01:20 GMT+0530 : {}:executing pullROdata
Fri Apr 19 2024 10:01:20 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:04:52 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 10:04:52 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 10:04:53 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 10:04:53 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 10:04:53 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 10:04:53 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2021","01/05/2021"]] 
Fri Apr 19 2024 10:04:53 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 10:04:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2021,01/05/2021
Fri Apr 19 2024 10:04:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-04T18:30:00.000Z
Fri Apr 19 2024 10:04:54 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:04:54 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:05:15 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 10:05:15 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 10:05:19 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 10:05:19 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 10:05:19 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 10:05:19 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 10:05:19 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 10:05:19 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:05:19 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 10:05:19 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:05:19 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:05:23 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:05:23 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:05:23 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 10:05:23 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:05:23 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:05:26 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:05:26 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:05:26 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 10:05:26 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:05:26 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:05:28 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:05:28 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:05:28 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 10:05:28 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:05:28 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:05:33 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:05:33 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:05:33 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 10:05:33 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:05:33 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:05:34 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:15:21 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 10:15:21 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 10:15:23 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 10:15:23 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 10:15:23 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Fri Apr 19 2024 10:15:23 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 10:15:23 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 10:15:23 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:15:23 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 10:15:23 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:15:23 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:15:25 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:15:25 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:15:25 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 10:15:25 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:15:25 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:15:26 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:15:26 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:15:26 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 10:15:26 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:15:26 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:15:28 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:15:28 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:15:28 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 10:15:28 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:15:28 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:15:29 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:15:29 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:15:29 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 10:15:29 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:15:29 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:15:30 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 10:30:12 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 10:30:12 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 10:30:14 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 10:30:14 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 10:30:14 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 10:30:14 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Fri Apr 19 2024 10:30:14 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 10:30:14 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:30:14 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 10:30:14 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:30:14 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:30:16 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:30:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 10:30:16 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:30:16 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:30:17 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:30:17 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 10:30:17 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:30:17 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:30:18 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:30:18 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 10:30:18 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:30:18 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 10:30:20 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 10:30:20 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 10:30:20 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 10:30:20 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:51:16 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 11:51:16 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 11:51:18 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 11:51:18 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 11:51:18 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Fri Apr 19 2024 11:51:18 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 11:51:18 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 19 2024 11:51:18 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:51:18 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 11:51:18 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 11:51:18 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:52:00 GMT+0530 : {"StoreID":"01b3c47d-0904-912e-005f-0d030922bd36"}:executing pullOpenRO
Fri Apr 19 2024 11:52:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 11:52:01 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:52:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 11:52:01 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 11:52:01 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:52:43 GMT+0530 : {"StoreID":"01b3c47e-0904-912e-005f-0d030922bd76"}:executing pullOpenRO
Fri Apr 19 2024 11:52:45 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:52:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 11:52:45 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 11:52:45 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 11:52:45 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:53:26 GMT+0530 : {"StoreID":"01b3c47e-0904-9201-005f-0d030922c84e"}:executing pullOpenRO
Fri Apr 19 2024 11:53:32 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 11:53:32 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:53:32 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 11:53:32 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 11:53:32 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:54:14 GMT+0530 : {"StoreID":"01b3c47f-0904-9201-005f-0d030922c85a"}:executing pullOpenRO
Fri Apr 19 2024 11:54:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 11:54:16 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:54:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 11:54:16 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 11:54:16 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:54:58 GMT+0530 : {"StoreID":"01b3c480-0904-90da-005f-0d030922d612"}:executing pullOpenRO
Fri Apr 19 2024 11:55:00 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 11:55:00 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 11:55:00 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Fri Apr 19 2024 11:55:00 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 11:55:00 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 11:55:00 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:55:00 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 11:55:00 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 11:55:00 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:55:01 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 11:55:01 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:55:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 11:55:01 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 11:55:01 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:55:01 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 11:55:01 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:55:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 11:55:01 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 11:55:01 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:55:02 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 11:55:02 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:55:02 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 11:55:02 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 11:55:02 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:55:04 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 11:55:04 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:55:04 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 11:55:04 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 11:55:04 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:55:05 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 11:55:05 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 11:55:05 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Fri Apr 19 2024 11:55:05 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:55:05 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 11:55:06 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 11:55:06 GMT+0530 : {"Dealer ID":"12345"}:Ready for  customer deltra  Pull
Fri Apr 19 2024 11:55:06 GMT+0530 : {}:executing make model
Fri Apr 19 2024 11:55:06 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 11:55:12 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 11:55:12 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 11:55:12 GMT+0530 : {}:executing make model
Fri Apr 19 2024 11:55:12 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 11:55:16 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 11:55:16 GMT+0530 : {"Dealer ID":"12345"}:Ready for  coa data Pull
Fri Apr 19 2024 11:57:36 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 11:57:36 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 11:57:38 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 11:57:38 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 11:57:38 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Fri Apr 19 2024 11:57:38 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 11:57:38 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 19 2024 11:57:38 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:57:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 11:57:38 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 11:57:38 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:58:21 GMT+0530 : {"StoreID":"01b3c483-0904-912e-005f-0d030922bf66"}:executing pullOpenRO
Fri Apr 19 2024 11:58:24 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 11:58:24 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:58:24 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 11:58:24 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 11:58:24 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:59:05 GMT+0530 : {"StoreID":"01b3c484-0904-9201-005f-0d030922ca8e"}:executing pullOpenRO
Fri Apr 19 2024 11:59:07 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 11:59:07 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:59:07 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 11:59:07 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 11:59:07 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 11:59:48 GMT+0530 : {"StoreID":"01b3c485-0904-90da-005f-0d030922d89a"}:executing pullOpenRO
Fri Apr 19 2024 11:59:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 11:59:50 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 11:59:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 11:59:50 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 11:59:50 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:00:31 GMT+0530 : {"StoreID":"01b3c485-0904-90da-005f-0d030922d8ae"}:executing pullOpenRO
Fri Apr 19 2024 12:00:33 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 12:00:33 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:00:33 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 12:00:33 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:00:33 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:01:14 GMT+0530 : {"StoreID":"01b3c486-0904-90da-005f-0d030922d8ba"}:executing pullOpenRO
Fri Apr 19 2024 12:01:15 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 12:01:15 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:01:16 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Fri Apr 19 2024 12:01:16 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 12:01:16 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 12:01:16 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:01:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 12:01:16 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:01:16 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:01:17 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:01:17 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:01:17 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 12:01:17 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:01:17 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:01:19 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:01:19 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:01:19 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 12:01:19 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:01:19 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:01:19 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:01:19 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:01:19 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 12:01:19 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:01:19 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:01:21 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:01:21 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:01:21 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 12:01:21 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:01:21 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:01:22 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:01:22 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:01:22 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Fri Apr 19 2024 12:01:22 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:01:22 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:01:23 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:01:23 GMT+0530 : {"Dealer ID":"12345"}:Ready for  customer deltra  Pull
Fri Apr 19 2024 12:01:23 GMT+0530 : {}:executing make model
Fri Apr 19 2024 12:01:23 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:01:26 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:01:26 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 12:01:26 GMT+0530 : {}:executing make model
Fri Apr 19 2024 12:01:26 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:01:29 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:01:29 GMT+0530 : {"Dealer ID":"12345"}:Ready for  coa data Pull
Fri Apr 19 2024 12:01:29 GMT+0530 : {}:executing COA pull
Fri Apr 19 2024 12:01:29 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:01:31 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:01:31 GMT+0530 : {"Dealer ID":"12345"}:Ready for  coa data Pull
Fri Apr 19 2024 12:02:51 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 12:02:51 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 12:02:53 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 12:02:53 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:02:53 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Fri Apr 19 2024 12:02:53 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 12:02:53 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 19 2024 12:02:53 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:02:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 12:02:53 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:02:53 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:03:35 GMT+0530 : {"StoreID":"01b3c488-0904-912e-005f-0d030922e072"}:executing pullOpenRO
Fri Apr 19 2024 12:03:37 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 12:03:37 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:03:37 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 12:03:37 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:03:37 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:04:18 GMT+0530 : {"StoreID":"01b3c489-0904-90da-005f-0d030922d94a"}:executing pullOpenRO
Fri Apr 19 2024 12:04:20 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 12:04:20 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:04:20 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 12:04:20 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:04:20 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:05:01 GMT+0530 : {"StoreID":"01b3c48a-0904-9201-005f-0d030922cb62"}:executing pullOpenRO
Fri Apr 19 2024 12:05:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 12:05:03 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:05:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 12:05:03 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:05:03 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:05:44 GMT+0530 : {"StoreID":"01b3c48b-0904-90da-005f-0d030922d98e"}:executing pullOpenRO
Fri Apr 19 2024 12:05:46 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 12:05:46 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:05:46 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 12:05:46 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:05:46 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:06:28 GMT+0530 : {"StoreID":"01b3c48b-0904-912e-005f-0d030922e0ba"}:executing pullOpenRO
Fri Apr 19 2024 12:06:29 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 12:06:29 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:06:29 GMT+0530 : {"Dealer ID":"12345"}:Ready for  GL Pull
Fri Apr 19 2024 12:06:29 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 12:06:29 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Fri Apr 19 2024 12:06:29 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:06:29 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 12:06:29 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:06:29 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:06:30 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:30 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:06:30 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 12:06:30 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:06:30 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:06:31 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:31 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:06:31 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 12:06:31 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:06:31 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:06:32 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:32 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:06:32 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 12:06:32 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:06:32 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:06:32 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:32 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:06:32 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 12:06:32 GMT+0530 : {}:executing performGlPullWithDateRange
Fri Apr 19 2024 12:06:32 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:06:33 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:33 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:06:33 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Fri Apr 19 2024 12:06:33 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:06:33 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:34 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:06:34 GMT+0530 : {"Dealer ID":"12345"}:Ready for  customer deltra  Pull
Fri Apr 19 2024 12:06:34 GMT+0530 : {}:executing make model
Fri Apr 19 2024 12:06:34 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:37 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:06:37 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 12:06:37 GMT+0530 : {}:executing make model
Fri Apr 19 2024 12:06:37 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:41 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:06:41 GMT+0530 : {"Dealer ID":"12345"}:Ready for  coa data Pull
Fri Apr 19 2024 12:06:41 GMT+0530 : {}:executing COA pull
Fri Apr 19 2024 12:06:41 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:42 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:06:42 GMT+0530 : {"Dealer ID":"12345"}:Ready for  coa data Pull
Fri Apr 19 2024 12:06:42 GMT+0530 : {}:executing EMPLOYEE pull
Fri Apr 19 2024 12:06:42 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:06:43 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:06:43 GMT+0530 : {"Dealer ID":"12345"}:Ready for  history RO Pull
Fri Apr 19 2024 12:06:43 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2021","05/05/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"]] 
Fri Apr 19 2024 12:06:43 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 19 2024 12:06:43 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:06:43 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 12:06:43 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:06:43 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:07:24 GMT+0530 : {"StoreID":"01b3c48c-0904-912e-005f-0d030922e0ce"}:executing pullOpenRO
Fri Apr 19 2024 12:07:26 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-04-30T18:30:00.000Z  End date:2021-05-04T18:30:00.000Z
Fri Apr 19 2024 12:07:26 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:07:26 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 12:07:26 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:07:26 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:08:07 GMT+0530 : {"StoreID":"01b3c48d-0904-912e-005f-0d030922e0da"}:executing pullOpenRO
Fri Apr 19 2024 12:08:10 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-03-31T18:30:00.000Z  End date:2021-04-29T18:30:00.000Z
Fri Apr 19 2024 12:08:10 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:08:10 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 12:08:10 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:08:10 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:08:51 GMT+0530 : {"StoreID":"01b3c48e-0904-912e-005f-0d030922e0ea"}:executing pullOpenRO
Fri Apr 19 2024 12:08:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-02-28T18:30:00.000Z  End date:2021-03-30T18:30:00.000Z
Fri Apr 19 2024 12:08:53 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:08:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 12:08:53 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:08:53 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:09:35 GMT+0530 : {"StoreID":"01b3c48e-0904-912e-005f-0d030922e10e"}:executing pullOpenRO
Fri Apr 19 2024 12:09:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-01-31T18:30:00.000Z  End date:2021-02-27T18:30:00.000Z
Fri Apr 19 2024 12:09:36 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2021,05/05/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021
Fri Apr 19 2024 12:09:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 12:09:36 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 19 2024 12:09:36 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 19 2024 12:10:18 GMT+0530 : {"StoreID":"01b3c48f-0904-912e-005f-0d030922e122"}:executing pullOpenRO
Fri Apr 19 2024 12:10:20 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-12-31T18:30:00.000Z  End date:2021-01-30T18:30:00.000Z
Fri Apr 19 2024 12:10:20 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:10:20 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 12:10:20 GMT+0530 : {}:executing performOpenROPull
Fri Apr 19 2024 12:10:20 GMT+0530 : {}:executing pullROdata
Fri Apr 19 2024 12:10:20 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:14:02 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 12:14:02 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 12:14:04 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 12:14:04 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:14:04 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 12:14:04 GMT+0530 : undefined:executing make model
Fri Apr 19 2024 12:14:04 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:14:08 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:14:08 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 12:14:08 GMT+0530 : {}:executing performOpenROPull
Fri Apr 19 2024 12:14:08 GMT+0530 : {}:executing pullROdata
Fri Apr 19 2024 12:14:08 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:18:06 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 12:18:06 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 12:18:08 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 12:18:08 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:18:08 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 12:18:08 GMT+0530 : undefined:executing make model
Fri Apr 19 2024 12:18:08 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:18:11 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:18:11 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 12:18:11 GMT+0530 : {}:executing performOpenROPull
Fri Apr 19 2024 12:18:11 GMT+0530 : {}:executing pullROdata
Fri Apr 19 2024 12:18:11 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:20:12 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 12:20:12 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 12:20:14 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 12:20:14 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:20:14 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 12:20:14 GMT+0530 : undefined:executing make model
Fri Apr 19 2024 12:20:14 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:20:16 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:20:16 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 12:20:16 GMT+0530 : {}:executing performOpenROPull
Fri Apr 19 2024 12:20:16 GMT+0530 : {}:executing pullROdata
Fri Apr 19 2024 12:20:16 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:21:01 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 12:21:01 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 12:21:02 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 12:21:02 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:21:02 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 12:21:02 GMT+0530 : undefined:executing make model
Fri Apr 19 2024 12:21:02 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:21:04 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:21:04 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 12:21:04 GMT+0530 : {}:executing performOpenROPull
Fri Apr 19 2024 12:21:04 GMT+0530 : {}:executing pullROdata
Fri Apr 19 2024 12:21:04 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:38:39 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 12:38:39 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 12:38:41 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 12:38:41 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:38:41 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 12:38:41 GMT+0530 : undefined:executing make model
Fri Apr 19 2024 12:38:41 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:38:44 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:38:44 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 12:38:44 GMT+0530 : {}:executing performOpenROPull
Fri Apr 19 2024 12:38:44 GMT+0530 : {}:executing pullROdata
Fri Apr 19 2024 12:38:44 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:42:25 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 12:42:25 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 12:42:27 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 12:42:27 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:42:27 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 12:42:27 GMT+0530 : undefined:executing make model
Fri Apr 19 2024 12:42:27 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:42:29 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:42:29 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 12:42:29 GMT+0530 : {}:executing performOpenROPull
Fri Apr 19 2024 12:42:29 GMT+0530 : {}:executing pullROdata
Fri Apr 19 2024 12:42:29 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:47:26 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 12:47:26 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 12:47:28 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 12:47:28 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:47:28 GMT+0530 : undefined:executing make model
Fri Apr 19 2024 12:47:28 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Fri Apr 19 2024 12:47:28 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 12:47:30 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 12:47:30 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Fri Apr 19 2024 12:47:30 GMT+0530 : {}:executing performOpenROPull
Fri Apr 19 2024 12:47:30 GMT+0530 : {}:executing pullROdata
Fri Apr 19 2024 12:47:30 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 14:40:26 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Fri Apr 19 2024 14:40:26 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Fri Apr 19 2024 14:40:28 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Fri Apr 19 2024 14:40:28 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Fri Apr 19 2024 14:40:28 GMT+0530 : {"Dealer ID":"12345"}:Ready for  coa data Pull
Fri Apr 19 2024 14:40:28 GMT+0530 : {}:executing pullOpenRO
Fri Apr 19 2024 14:40:28 GMT+0530 : undefined:executing COA pull
