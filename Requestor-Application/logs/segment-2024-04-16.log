Tue Apr 16 2024 09:44:12 GMT+0530 : {"Dealer ID":"12345"}:<PERSON><PERSON><PERSON> started
Tue Apr 16 2024 09:44:12 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 16 2024 09:44:13 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 16 2024 09:44:13 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 16 2024 09:44:13 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 16 2024 09:44:13 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Tue Apr 16 2024 09:44:13 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 16 2024 09:44:13 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Tue Apr 16 2024 09:44:13 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:44:13 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 16 2024 09:44:13 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 16 2024 09:44:16 GMT+0530 : {"StoreID":"01b3b31e-0904-8bf9-005f-0d0308d58cae"}:executing pullOpenRO
Tue Apr 16 2024 09:44:17 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:47:04 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 16 2024 09:47:04 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 16 2024 09:47:05 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 16 2024 09:47:05 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 16 2024 09:47:05 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 16 2024 09:47:05 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Tue Apr 16 2024 09:47:05 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 16 2024 09:47:05 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Tue Apr 16 2024 09:47:05 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:47:05 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 16 2024 09:47:05 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 16 2024 09:47:07 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:47:19 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 16 2024 09:47:19 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 16 2024 09:47:21 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 16 2024 09:47:21 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 16 2024 09:47:21 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 16 2024 09:47:21 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Tue Apr 16 2024 09:47:21 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 16 2024 09:47:21 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Tue Apr 16 2024 09:47:21 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:47:21 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 16 2024 09:47:21 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 16 2024 09:47:22 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:50:11 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 16 2024 09:50:11 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 16 2024 09:50:12 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 16 2024 09:50:12 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 16 2024 09:50:12 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 16 2024 09:50:12 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Tue Apr 16 2024 09:50:12 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 16 2024 09:50:12 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Tue Apr 16 2024 09:50:12 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:50:12 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 16 2024 09:50:12 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 16 2024 09:50:15 GMT+0530 : {}:executing pullOpenRO
Tue Apr 16 2024 09:50:16 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:52:49 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 16 2024 09:52:49 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 16 2024 09:52:50 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 16 2024 09:52:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 16 2024 09:52:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 16 2024 09:52:50 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Tue Apr 16 2024 09:52:50 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 16 2024 09:52:50 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Tue Apr 16 2024 09:52:50 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:52:50 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 16 2024 09:52:50 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 16 2024 09:53:32 GMT+0530 : {"StoreID":"01b3b326-0904-8ca6-005f-0d0308d59cea"}:executing pullOpenRO
Tue Apr 16 2024 09:53:33 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:56:59 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 16 2024 09:56:59 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 16 2024 09:57:01 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 16 2024 09:57:01 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 16 2024 09:57:01 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 16 2024 09:57:01 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Tue Apr 16 2024 09:57:01 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 16 2024 09:57:01 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Tue Apr 16 2024 09:57:01 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:57:01 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 16 2024 09:57:01 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 16 2024 09:57:43 GMT+0530 : {"StoreID":"01b3b32b-0904-8bf9-005f-0d0308d58f0e"}:executing pullOpenRO
Tue Apr 16 2024 09:57:44 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 09:59:59 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 16 2024 09:59:59 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 16 2024 10:00:00 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 16 2024 10:00:00 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 16 2024 10:00:00 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 16 2024 10:00:00 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Tue Apr 16 2024 10:00:00 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 16 2024 10:00:00 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Tue Apr 16 2024 10:00:00 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 10:00:00 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 16 2024 10:00:00 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 16 2024 10:00:42 GMT+0530 : {"StoreID":"01b3b32e-0904-8bf9-005f-0d0308d58f6a"}:executing pullOpenRO
Tue Apr 16 2024 10:02:31 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Tue Apr 16 2024 10:02:31 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Tue Apr 16 2024 10:02:32 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Tue Apr 16 2024 10:02:32 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Tue Apr 16 2024 10:02:32 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Tue Apr 16 2024 10:02:32 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Tue Apr 16 2024 10:02:32 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Tue Apr 16 2024 10:02:32 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Tue Apr 16 2024 10:02:32 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Tue Apr 16 2024 10:02:32 GMT+0530 : {}:executing performROPullWithDateRange
Tue Apr 16 2024 10:02:32 GMT+0530 : {}:executing pullCloseROdata
Tue Apr 16 2024 10:03:15 GMT+0530 : {"StoreID":"01b3b330-0904-8bf9-005f-0d0308d5a026"}:executing pullOpenRO
Tue Apr 16 2024 10:03:17 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
