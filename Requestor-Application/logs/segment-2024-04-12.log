Fri Apr 12 2024 09:16:58 GMT+0530 : {}:<PERSON><PERSON><PERSON> started
Fri Apr 12 2024 09:16:58 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 09:19:49 GMT+0530 : {}:R<PERSON> <PERSON><PERSON> started
Fri Apr 12 2024 09:19:49 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 09:34:28 GMT+0530 : {}:R<PERSON>ull started
Fri Apr 12 2024 09:34:28 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 09:34:29 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:44:21 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 09:44:21 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 09:44:26 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:45:04 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 09:45:04 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 09:45:06 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:51:26 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 09:51:26 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 09:51:27 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 09:51:27 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 09:51:27 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 09:51:27 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 09:51:27 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 09:51:27 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 09:51:27 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:51:27 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 09:51:27 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 09:52:17 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 09:52:17 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 09:52:18 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 09:52:18 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 09:52:18 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 09:52:18 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 09:52:18 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 09:52:18 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 09:52:18 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:52:18 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 09:52:18 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 09:54:41 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 09:54:41 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 09:54:43 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 09:54:43 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 09:54:43 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 09:54:43 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 09:54:43 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 09:54:43 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 09:54:43 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:54:43 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 09:54:43 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 09:54:46 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 09:54:46 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:56:26 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 09:56:26 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 09:56:27 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 09:56:27 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 09:56:27 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 09:56:27 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 09:56:27 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 09:56:27 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 09:56:27 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 09:56:27 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 09:56:27 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 09:56:30 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 09:56:30 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:13:08 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:13:08 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:13:10 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:13:10 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:13:10 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 10:13:10 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 10:13:10 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 10:13:10 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 10:13:10 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:13:10 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 10:13:10 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 10:13:12 GMT+0530 : {"StoreID":"01b39cbb-0904-86a8-005f-0d0308b5f0ea"}:executing pullOpenRO
Fri Apr 12 2024 10:13:13 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:14:03 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:14:03 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:14:05 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:14:05 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:14:05 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 10:14:05 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 10:14:05 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 10:14:05 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 10:14:05 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:14:05 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 10:14:05 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 10:14:07 GMT+0530 : {"StoreID":"01b39cbc-0904-8898-005f-0d0308b5efa2"}:executing pullOpenRO
Fri Apr 12 2024 10:14:07 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:14:39 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:14:39 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:14:41 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:14:41 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:14:41 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 10:14:41 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 10:14:41 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 10:14:41 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 10:14:41 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:14:41 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 10:14:41 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 10:14:43 GMT+0530 : {"StoreID":"01b39cbc-0904-8898-005f-0d0308b602aa"}:executing pullOpenRO
Fri Apr 12 2024 10:14:44 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:15:56 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:15:56 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:15:57 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:15:57 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:15:57 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 10:15:57 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 10:15:57 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 10:15:57 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 10:15:57 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:15:57 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 10:15:57 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 10:16:01 GMT+0530 : {"StoreID":"01b39cbe-0904-86a8-005f-0d0308b5f926"}:executing pullOpenRO
Fri Apr 12 2024 10:16:02 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:16:39 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:16:39 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:16:41 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:16:41 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:16:41 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 10:16:41 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 10:16:41 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 10:16:41 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 10:16:41 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:16:41 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 10:16:41 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 10:16:43 GMT+0530 : {"StoreID":"01b39cbe-0904-86a8-005f-0d0308b5f942"}:executing pullOpenRO
Fri Apr 12 2024 10:16:44 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:17:29 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:17:29 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:17:31 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:17:31 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:17:31 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 10:17:31 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 10:17:31 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 10:17:31 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 10:17:31 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:17:31 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 10:17:31 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 10:17:33 GMT+0530 : {"StoreID":"01b39cbf-0904-8898-005f-0d0308b602f6"}:executing pullOpenRO
Fri Apr 12 2024 10:17:48 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:33:48 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:33:48 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:33:50 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:33:50 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:33:50 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 10:33:50 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 10:33:50 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 10:33:50 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 10:33:50 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:33:50 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 10:33:50 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 10:33:54 GMT+0530 : {"StoreID":"01b39ccf-0904-8898-005f-0d0308b6061e"}:executing pullOpenRO
Fri Apr 12 2024 10:33:54 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:34:36 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:34:36 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:34:37 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:34:37 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:34:37 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 10:34:37 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 10:34:37 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 10:34:37 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 10:34:37 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:34:37 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 10:34:37 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 10:34:40 GMT+0530 : {"StoreID":"01b39cd0-0904-8898-005f-0d0308b6065e"}:executing pullOpenRO
Fri Apr 12 2024 10:34:41 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:37:27 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:37:27 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:37:29 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:37:29 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:37:29 GMT+0530 : {}:Ready for  Closed RO Pull
Fri Apr 12 2024 10:37:29 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Fri Apr 12 2024 10:37:29 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Fri Apr 12 2024 10:37:29 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Fri Apr 12 2024 10:37:29 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:37:29 GMT+0530 : {}:executing performROPullWithDateRange
Fri Apr 12 2024 10:37:29 GMT+0530 : {}:executing pullCloseROdata
Fri Apr 12 2024 10:37:31 GMT+0530 : {"StoreID":"01b39cd3-0904-8898-005f-0d0308b606da"}:executing pullOpenRO
Fri Apr 12 2024 10:37:32 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Fri Apr 12 2024 10:38:13 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:38:13 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:38:14 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:38:14 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:38:14 GMT+0530 : {}:Ready for  Customer Pull
Fri Apr 12 2024 10:38:14 GMT+0530 : undefined:executing pullCloseROdata
Fri Apr 12 2024 10:38:14 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 10:42:07 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 10:42:07 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 10:42:08 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 10:42:08 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 10:42:08 GMT+0530 : {}:Ready for  Customer Pull
Fri Apr 12 2024 10:42:08 GMT+0530 : undefined:executing pullCloseROdata
Fri Apr 12 2024 10:42:08 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 11:40:53 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 11:40:53 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 11:40:54 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 11:40:54 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 11:40:54 GMT+0530 : {}:Ready for  Customer Pull
Fri Apr 12 2024 11:40:54 GMT+0530 : undefined:executing pullCloseROdata
Fri Apr 12 2024 11:40:54 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 11:42:45 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 11:42:45 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 11:42:47 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 11:42:47 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 11:42:47 GMT+0530 : {}:Ready for  Customer Pull
Fri Apr 12 2024 11:42:47 GMT+0530 : undefined:executing pullCloseROdata
Fri Apr 12 2024 11:42:47 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:15:09 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:15:09 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:15:10 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:15:10 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:15:10 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:15:10 GMT+0530 : undefined:executing pullCloseROdata
Fri Apr 12 2024 12:15:10 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:15:27 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:15:27 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:15:28 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:15:28 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:15:28 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:15:28 GMT+0530 : undefined:executing pullCloseROdata
Fri Apr 12 2024 12:15:28 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:22:48 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:22:48 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:22:50 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:22:50 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:22:50 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:22:50 GMT+0530 : undefined:executing pullCloseROdata
Fri Apr 12 2024 12:22:50 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:23:25 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:23:25 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:23:27 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:23:27 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:23:27 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:23:27 GMT+0530 : undefined:executing pullCloseROdata
Fri Apr 12 2024 12:23:27 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:26:53 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:26:53 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:26:54 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:26:54 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:26:54 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:26:54 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:28:42 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:28:42 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:28:44 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:28:44 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:28:44 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:28:44 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:28:44 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:29:25 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:29:25 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:29:26 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:29:26 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:29:26 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:29:26 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:29:26 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:40:31 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:40:31 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:40:32 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:40:32 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:40:32 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:40:32 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:40:32 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:42:10 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:42:10 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:42:12 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:42:12 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:42:12 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:42:12 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:42:12 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:43:15 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:43:15 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:43:16 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:43:16 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:43:16 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:43:16 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:43:16 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:44:04 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:44:04 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:44:06 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:44:06 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:44:06 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:44:06 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:44:06 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:44:58 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:44:58 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:45:00 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:45:00 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:45:00 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:45:00 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:45:00 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:45:57 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:45:57 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:45:58 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:45:58 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:45:58 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:45:58 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:45:58 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 12:47:53 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 12:47:53 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 12:47:55 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 12:47:55 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 12:47:55 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 12:47:55 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 12:47:55 GMT+0530 : {}:executing pullOpenRO
Fri Apr 12 2024 15:12:40 GMT+0530 : {}:RO Pull started
Fri Apr 12 2024 15:12:40 GMT+0530 : {}:perform extractBundle
Fri Apr 12 2024 15:12:42 GMT+0530 : {}:bundle type is initial
Fri Apr 12 2024 15:12:42 GMT+0530 : {}:Perform performAPICall
Fri Apr 12 2024 15:12:42 GMT+0530 : {}:Ready for  makeModel Pull
Fri Apr 12 2024 15:12:42 GMT+0530 : undefined:executing make model
Fri Apr 12 2024 15:12:42 GMT+0530 : {}:executing pullOpenRO
