Mon Apr 15 2024 12:02:09 GMT+0530 : {}:<PERSON><PERSON><PERSON> started
Mon Apr 15 2024 12:02:09 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:06:06 GMT+0530 : {}:R<PERSON> <PERSON><PERSON> started
Mon Apr 15 2024 12:06:06 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:10:50 GMT+0530 : {}:R<PERSON> <PERSON>ull started
Mon Apr 15 2024 12:10:50 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:13:38 GMT+0530 : {}:R<PERSON> <PERSON>ull started
Mon Apr 15 2024 12:13:38 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:25:17 GMT+0530 : {}:R<PERSON> <PERSON>ull started
Mon Apr 15 2024 12:25:17 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:25:56 GMT+0530 : {}:R<PERSON> <PERSON>ull started
Mon Apr 15 2024 12:25:56 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:27:26 GMT+0530 : {}:<PERSON><PERSON><PERSON> started
Mon Apr 15 2024 12:27:26 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:28:03 GMT+0530 : {}:R<PERSON> <PERSON>ull started
Mon Apr 15 2024 12:28:03 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:28:04 GMT+0530 : {}:bundle type is initial
Mon Apr 15 2024 12:28:04 GMT+0530 : {}:Perform performAPICall
Mon Apr 15 2024 12:28:04 GMT+0530 : {}:Ready for  Closed RO Pull
Mon Apr 15 2024 12:28:04 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 12:28:04 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 12:28:04 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 12:28:04 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 12:28:04 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 12:28:04 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 12:28:07 GMT+0530 : {"StoreID":"01b3ae22-0904-8a74-005f-0d0308cdb9a6"}:executing pullOpenRO
Mon Apr 15 2024 12:28:08 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 12:28:30 GMT+0530 : {}:RO Pull started
Mon Apr 15 2024 12:28:30 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:28:32 GMT+0530 : {}:bundle type is initial
Mon Apr 15 2024 12:28:32 GMT+0530 : {}:Perform performAPICall
Mon Apr 15 2024 12:28:32 GMT+0530 : {}:Ready for  Closed RO Pull
Mon Apr 15 2024 12:28:32 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 12:28:32 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 12:28:32 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 12:28:32 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 12:28:32 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 12:28:32 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 12:28:34 GMT+0530 : {"StoreID":"01b3ae22-0904-86a8-005f-0d0308cdafc2"}:executing pullOpenRO
Mon Apr 15 2024 12:28:35 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 12:35:45 GMT+0530 : {}:RO Pull started
Mon Apr 15 2024 12:35:45 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:35:46 GMT+0530 : {}:bundle type is initial
Mon Apr 15 2024 12:35:46 GMT+0530 : {}:Perform performAPICall
Mon Apr 15 2024 12:35:46 GMT+0530 : {}:Ready for  Closed RO Pull
Mon Apr 15 2024 12:35:46 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 12:35:46 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 12:35:46 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 12:35:46 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 12:35:46 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 12:35:46 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 12:35:49 GMT+0530 : {"StoreID":"01b3ae29-0904-8914-005f-0d0308ce0806"}:executing pullOpenRO
Mon Apr 15 2024 12:35:50 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 12:42:28 GMT+0530 : {}:RO Pull started
Mon Apr 15 2024 12:42:28 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:42:29 GMT+0530 : {}:bundle type is initial
Mon Apr 15 2024 12:42:29 GMT+0530 : {}:Perform performAPICall
Mon Apr 15 2024 12:42:29 GMT+0530 : {}:Ready for  Closed RO Pull
Mon Apr 15 2024 12:42:29 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 12:42:29 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 12:42:29 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 12:42:29 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 12:42:29 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 12:42:29 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 12:42:32 GMT+0530 : {"StoreID":"01b3ae30-0904-86a8-005f-0d0308ce48d2"}:executing pullOpenRO
Mon Apr 15 2024 12:42:33 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 12:50:53 GMT+0530 : {}:RO Pull started
Mon Apr 15 2024 12:50:53 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 12:50:55 GMT+0530 : {}:bundle type is initial
Mon Apr 15 2024 12:50:55 GMT+0530 : {}:Perform performAPICall
Mon Apr 15 2024 12:50:55 GMT+0530 : {}:Ready for  Closed RO Pull
Mon Apr 15 2024 12:50:55 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 12:50:55 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 12:50:55 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 12:50:55 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 12:50:55 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 12:50:55 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 12:50:57 GMT+0530 : {"StoreID":"01b3ae38-0904-8914-005f-0d0308ce9f3a"}:executing pullOpenRO
Mon Apr 15 2024 12:50:58 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 14:44:47 GMT+0530 : {}:RO Pull started
Mon Apr 15 2024 14:44:47 GMT+0530 : {}:perform extractBundle
Mon Apr 15 2024 14:44:48 GMT+0530 : {}:bundle type is initial
Mon Apr 15 2024 14:44:48 GMT+0530 : {}:Perform performAPICall
Mon Apr 15 2024 14:44:48 GMT+0530 : {}:Ready for  Closed RO Pull
Mon Apr 15 2024 14:44:48 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 14:44:48 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 14:44:48 GMT+0530 : {}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 14:44:48 GMT+0530 : {}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 14:44:48 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 14:44:48 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 14:44:50 GMT+0530 : {"StoreID":"01b3aeaa-0904-8a74-005f-0d0308d04c22"}:executing pullOpenRO
Mon Apr 15 2024 14:44:51 GMT+0530 : {}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 14:47:48 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 15 2024 14:47:48 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 15 2024 14:47:49 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 15 2024 14:47:49 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 14:47:49 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Mon Apr 15 2024 14:47:49 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 14:47:49 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 14:47:49 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 14:47:49 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 14:47:49 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 14:47:49 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 14:47:52 GMT+0530 : {"StoreID":"01b3aead-0904-86a8-005f-0d0308d05222"}:executing pullOpenRO
Mon Apr 15 2024 14:47:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 14:56:08 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 15 2024 14:56:08 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 15 2024 14:56:10 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 15 2024 14:56:10 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 14:56:10 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Mon Apr 15 2024 14:56:10 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 14:56:10 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 14:56:10 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 14:56:10 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 14:56:10 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 14:56:10 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 14:56:12 GMT+0530 : {"StoreID":"01b3aeb6-0904-8a74-005f-0d0308d04e5e"}:executing pullOpenRO
Mon Apr 15 2024 14:56:12 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 14:56:12 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 14:56:12 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Mon Apr 15 2024 14:56:12 GMT+0530 : {}:executing performOpenROPull
Mon Apr 15 2024 14:56:12 GMT+0530 : {}:executing pullROdata
Mon Apr 15 2024 14:56:12 GMT+0530 : {}:executing pullOpenRO
Mon Apr 15 2024 14:59:15 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 15 2024 14:59:15 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 15 2024 14:59:17 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 15 2024 14:59:17 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 14:59:17 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Mon Apr 15 2024 14:59:17 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 14:59:17 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 14:59:17 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 14:59:17 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 14:59:17 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 14:59:17 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 14:59:20 GMT+0530 : {"StoreID":"01b3aeb9-0904-86a8-005f-0d0308d05502"}:executing pullOpenRO
Mon Apr 15 2024 14:59:21 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 14:59:21 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 14:59:21 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Mon Apr 15 2024 14:59:21 GMT+0530 : {}:executing performOpenROPull
Mon Apr 15 2024 14:59:21 GMT+0530 : {}:executing pullROdata
Mon Apr 15 2024 14:59:21 GMT+0530 : {}:executing pullOpenRO
Mon Apr 15 2024 15:01:02 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 15 2024 15:01:02 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 15 2024 15:01:03 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 15 2024 15:01:03 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 15:01:03 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Mon Apr 15 2024 15:01:03 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 15:01:03 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 15:01:03 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 15:01:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 15:01:03 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 15:01:03 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 15:01:05 GMT+0530 : {"StoreID":"01b3aebb-0904-8a74-005f-0d0308d060ca"}:executing pullOpenRO
Mon Apr 15 2024 15:01:06 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 15:01:06 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 15:01:06 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Mon Apr 15 2024 15:01:06 GMT+0530 : {}:executing performOpenROPull
Mon Apr 15 2024 15:01:06 GMT+0530 : {}:executing pullROdata
Mon Apr 15 2024 15:01:06 GMT+0530 : {}:executing pullOpenRO
Mon Apr 15 2024 17:40:59 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 15 2024 17:40:59 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 15 2024 17:41:00 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 15 2024 17:41:00 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 17:41:00 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Mon Apr 15 2024 17:41:00 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 17:41:00 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 17:41:00 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 17:41:00 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 17:41:00 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 17:41:00 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 17:41:02 GMT+0530 : {"StoreID":"01b3af5b-0904-8bf9-005f-0d0308d2a7fa"}:executing pullOpenRO
Mon Apr 15 2024 17:41:03 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 17:41:03 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 17:41:03 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Mon Apr 15 2024 17:41:03 GMT+0530 : {}:executing performOpenROPull
Mon Apr 15 2024 17:41:03 GMT+0530 : {}:executing pullROdata
Mon Apr 15 2024 17:41:03 GMT+0530 : {}:executing pullOpenRO
Mon Apr 15 2024 17:42:02 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Mon Apr 15 2024 17:42:02 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Mon Apr 15 2024 17:42:04 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Mon Apr 15 2024 17:42:04 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 17:42:04 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Mon Apr 15 2024 17:42:04 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Mon Apr 15 2024 17:42:04 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Mon Apr 15 2024 17:42:04 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Mon Apr 15 2024 17:42:04 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 17:42:04 GMT+0530 : {}:executing performROPullWithDateRange
Mon Apr 15 2024 17:42:04 GMT+0530 : {}:executing pullCloseROdata
Mon Apr 15 2024 17:42:06 GMT+0530 : {"StoreID":"01b3af5c-0904-8bf9-005f-0d0308d2a852"}:executing pullOpenRO
Mon Apr 15 2024 17:42:07 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Mon Apr 15 2024 17:42:07 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Mon Apr 15 2024 17:42:07 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Mon Apr 15 2024 17:42:07 GMT+0530 : {}:executing performOpenROPull
Mon Apr 15 2024 17:42:07 GMT+0530 : {}:executing pullROdata
Mon Apr 15 2024 17:42:07 GMT+0530 : {}:executing pullOpenRO
