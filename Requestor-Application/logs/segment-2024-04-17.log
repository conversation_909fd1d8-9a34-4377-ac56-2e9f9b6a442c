Wed Apr 17 2024 09:53:05 GMT+0530 : {"Dealer ID":"12345"}:<PERSON><PERSON><PERSON> started
Wed Apr 17 2024 09:53:05 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 09:53:10 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 09:53:10 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 09:53:10 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Wed Apr 17 2024 09:53:10 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Wed Apr 17 2024 09:53:10 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Wed Apr 17 2024 09:53:10 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Wed Apr 17 2024 09:53:10 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Wed Apr 17 2024 09:53:10 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 09:53:10 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 09:54:02 GMT+0530 : {"StoreID":"01b3b8c7-0904-8c41-005f-0d03091b9056"}:executing pullOpenRO
Wed Apr 17 2024 09:54:04 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Wed Apr 17 2024 09:54:04 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 09:54:04 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Wed Apr 17 2024 09:54:04 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Wed Apr 17 2024 09:54:04 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Wed Apr 17 2024 09:54:04 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Wed Apr 17 2024 09:54:04 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Wed Apr 17 2024 09:54:04 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 09:54:04 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 09:54:45 GMT+0530 : {"StoreID":"01b3b8c8-0904-8ca6-005f-0d03091b8706"}:executing pullOpenRO
Wed Apr 17 2024 09:54:47 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Wed Apr 17 2024 10:03:41 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 10:03:41 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 10:03:42 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 10:03:42 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 10:03:42 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Wed Apr 17 2024 10:03:42 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Wed Apr 17 2024 10:03:42 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Wed Apr 17 2024 10:03:42 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Wed Apr 17 2024 10:03:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Wed Apr 17 2024 10:03:42 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 10:03:42 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 10:04:25 GMT+0530 : {"StoreID":"01b3b8d1-0904-8c41-005f-0d03091b927e"}:executing pullOpenRO
Wed Apr 17 2024 10:04:27 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Wed Apr 17 2024 10:06:19 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 10:06:19 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 10:06:21 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 10:06:21 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 10:06:21 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Wed Apr 17 2024 10:06:21 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Wed Apr 17 2024 10:06:21 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Wed Apr 17 2024 10:06:21 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Wed Apr 17 2024 10:06:21 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Wed Apr 17 2024 10:06:21 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 10:06:21 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 10:07:03 GMT+0530 : {"StoreID":"01b3b8d4-0904-8c41-005f-0d03091b92f6"}:executing pullOpenRO
Wed Apr 17 2024 10:07:05 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Wed Apr 17 2024 10:19:53 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 10:19:53 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 10:19:54 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 10:19:54 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 10:19:54 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 10:19:54 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 10:19:54 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 10:22:29 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 10:22:29 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 10:22:31 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 10:22:31 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 10:22:31 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 10:22:31 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 10:22:31 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 10:35:46 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 10:35:46 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 10:35:49 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 10:35:49 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 10:35:49 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 10:35:49 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 10:35:49 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 10:47:35 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 10:47:35 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 10:47:36 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 10:47:36 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 10:47:36 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 10:47:36 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 10:47:36 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 10:49:02 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 10:49:02 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 10:49:04 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 10:49:04 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 10:49:04 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 10:49:04 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 10:49:04 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 11:51:24 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 11:51:24 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 11:51:26 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 11:51:26 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 11:51:26 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Wed Apr 17 2024 11:51:26 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Wed Apr 17 2024 11:51:26 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Wed Apr 17 2024 11:51:26 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Wed Apr 17 2024 11:51:26 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Wed Apr 17 2024 11:51:26 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 11:51:26 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 11:51:57 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 11:51:57 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 11:51:59 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 11:51:59 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 11:51:59 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 11:51:59 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 11:51:59 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 11:59:51 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 11:59:51 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 11:59:53 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 11:59:53 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 11:59:53 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 11:59:53 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 11:59:53 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:01:09 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:01:09 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:01:10 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:01:10 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:01:10 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:01:10 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:01:10 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:04:51 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:04:51 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:04:53 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:04:53 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:04:53 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:04:53 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:04:53 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:06:00 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:06:00 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:06:01 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:06:01 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:06:01 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:06:01 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:06:01 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:06:20 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:06:20 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:06:21 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:06:21 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:06:21 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:06:21 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:06:21 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:08:09 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:08:09 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:08:10 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:08:10 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:08:10 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:08:10 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:08:10 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:11:54 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:11:54 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:11:55 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:11:55 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:11:55 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:11:55 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:11:55 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:12:41 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:12:41 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:12:43 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:12:43 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:12:43 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:12:43 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:12:43 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:14:49 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:14:49 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:14:50 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:14:50 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:14:50 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:14:50 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:14:50 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:15:33 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:15:33 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:15:34 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:15:34 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:15:34 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:15:34 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:15:34 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:22:12 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:22:12 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:22:14 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:22:14 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:22:14 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:22:14 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:22:14 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:24:55 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:24:55 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:24:57 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:24:57 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:24:57 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:24:57 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:24:57 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:28:25 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:28:25 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:28:27 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:28:27 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:28:27 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 12:28:27 GMT+0530 : undefined:executing pullCloseROdata
Wed Apr 17 2024 12:28:27 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 12:49:55 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:49:55 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:49:57 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:49:57 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:49:57 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Wed Apr 17 2024 12:49:57 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2022","01/01/2022"],["12/01/2021","12/31/2021"],["11/01/2021","11/30/2021"],["10/01/2021","10/31/2021"],["09/01/2021","09/30/2021"],["08/01/2021","08/31/2021"],["07/01/2021","07/31/2021"],["06/01/2021","06/30/2021"],["05/01/2021","05/31/2021"],["04/01/2021","04/30/2021"],["03/01/2021","03/31/2021"],["02/01/2021","02/28/2021"],["01/01/2021","01/31/2021"],["12/01/2020","12/31/2020"],["11/01/2020","11/30/2020"],["10/01/2020","10/31/2020"],["09/01/2020","09/30/2020"],["08/01/2020","08/31/2020"],["07/01/2020","07/31/2020"],["06/01/2020","06/30/2020"],["05/01/2020","05/31/2020"],["04/01/2020","04/30/2020"],["03/01/2020","03/31/2020"],["02/01/2020","02/29/2020"],["01/01/2020","01/31/2020"]] 
Wed Apr 17 2024 12:49:57 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Wed Apr 17 2024 12:49:57 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2022,01/01/2022,12/01/2021,12/31/2021,11/01/2021,11/30/2021,10/01/2021,10/31/2021,09/01/2021,09/30/2021,08/01/2021,08/31/2021,07/01/2021,07/31/2021,06/01/2021,06/30/2021,05/01/2021,05/31/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021,12/01/2020,12/31/2020,11/01/2020,11/30/2020,10/01/2020,10/31/2020,09/01/2020,09/30/2020,08/01/2020,08/31/2020,07/01/2020,07/31/2020,06/01/2020,06/30/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:49:57 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-12-31T18:30:00.000Z  End date:2021-12-31T18:30:00.000Z
Wed Apr 17 2024 12:49:57 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:49:57 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:50:40 GMT+0530 : {"StoreID":"01b3b978-0904-8ca6-005f-0d03091c5696"}:executing pullOpenRO
Wed Apr 17 2024 12:50:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-12-31T18:30:00.000Z  End date:2021-12-31T18:30:00.000Z
Wed Apr 17 2024 12:50:42 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2022,01/01/2022,12/01/2021,12/31/2021,11/01/2021,11/30/2021,10/01/2021,10/31/2021,09/01/2021,09/30/2021,08/01/2021,08/31/2021,07/01/2021,07/31/2021,06/01/2021,06/30/2021,05/01/2021,05/31/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021,12/01/2020,12/31/2020,11/01/2020,11/30/2020,10/01/2020,10/31/2020,09/01/2020,09/30/2020,08/01/2020,08/31/2020,07/01/2020,07/31/2020,06/01/2020,06/30/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:50:42 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-11-30T18:30:00.000Z  End date:2021-12-30T18:30:00.000Z
Wed Apr 17 2024 12:50:42 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:50:42 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:51:24 GMT+0530 : {"StoreID":"01b3b978-0904-8ca6-005f-0d03091c56da"}:executing pullOpenRO
Wed Apr 17 2024 12:51:25 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-11-30T18:30:00.000Z  End date:2021-12-30T18:30:00.000Z
Wed Apr 17 2024 12:51:25 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2022,01/01/2022,12/01/2021,12/31/2021,11/01/2021,11/30/2021,10/01/2021,10/31/2021,09/01/2021,09/30/2021,08/01/2021,08/31/2021,07/01/2021,07/31/2021,06/01/2021,06/30/2021,05/01/2021,05/31/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021,12/01/2020,12/31/2020,11/01/2020,11/30/2020,10/01/2020,10/31/2020,09/01/2020,09/30/2020,08/01/2020,08/31/2020,07/01/2020,07/31/2020,06/01/2020,06/30/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:51:25 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-10-31T18:30:00.000Z  End date:2021-11-29T18:30:00.000Z
Wed Apr 17 2024 12:51:25 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:51:25 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:52:07 GMT+0530 : {"StoreID":"01b3b979-0904-8c41-005f-0d03091c496a"}:executing pullOpenRO
Wed Apr 17 2024 12:52:09 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2022,01/01/2022,12/01/2021,12/31/2021,11/01/2021,11/30/2021,10/01/2021,10/31/2021,09/01/2021,09/30/2021,08/01/2021,08/31/2021,07/01/2021,07/31/2021,06/01/2021,06/30/2021,05/01/2021,05/31/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021,12/01/2020,12/31/2020,11/01/2020,11/30/2020,10/01/2020,10/31/2020,09/01/2020,09/30/2020,08/01/2020,08/31/2020,07/01/2020,07/31/2020,06/01/2020,06/30/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:52:09 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-10-31T18:30:00.000Z  End date:2021-11-29T18:30:00.000Z
Wed Apr 17 2024 12:52:09 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-09-30T18:30:00.000Z  End date:2021-10-30T18:30:00.000Z
Wed Apr 17 2024 12:52:09 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:52:09 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:52:52 GMT+0530 : {"StoreID":"01b3b97a-0904-8ca6-005f-0d03091c573a"}:executing pullOpenRO
Wed Apr 17 2024 12:52:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-09-30T18:30:00.000Z  End date:2021-10-30T18:30:00.000Z
Wed Apr 17 2024 12:52:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2022,01/01/2022,12/01/2021,12/31/2021,11/01/2021,11/30/2021,10/01/2021,10/31/2021,09/01/2021,09/30/2021,08/01/2021,08/31/2021,07/01/2021,07/31/2021,06/01/2021,06/30/2021,05/01/2021,05/31/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021,12/01/2020,12/31/2020,11/01/2020,11/30/2020,10/01/2020,10/31/2020,09/01/2020,09/30/2020,08/01/2020,08/31/2020,07/01/2020,07/31/2020,06/01/2020,06/30/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:52:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-08-31T18:30:00.000Z  End date:2021-09-29T18:30:00.000Z
Wed Apr 17 2024 12:52:54 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:52:54 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:53:35 GMT+0530 : {"StoreID":"01b3b97a-0904-8c41-005f-0d03091c49ce"}:executing pullOpenRO
Wed Apr 17 2024 12:53:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-08-31T18:30:00.000Z  End date:2021-09-29T18:30:00.000Z
Wed Apr 17 2024 12:53:36 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2022,01/01/2022,12/01/2021,12/31/2021,11/01/2021,11/30/2021,10/01/2021,10/31/2021,09/01/2021,09/30/2021,08/01/2021,08/31/2021,07/01/2021,07/31/2021,06/01/2021,06/30/2021,05/01/2021,05/31/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021,12/01/2020,12/31/2020,11/01/2020,11/30/2020,10/01/2020,10/31/2020,09/01/2020,09/30/2020,08/01/2020,08/31/2020,07/01/2020,07/31/2020,06/01/2020,06/30/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:53:36 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-07-31T18:30:00.000Z  End date:2021-08-30T18:30:00.000Z
Wed Apr 17 2024 12:53:36 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:53:36 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:54:18 GMT+0530 : {"StoreID":"01b3b97b-0904-8c41-005f-0d03091c4a0a"}:executing pullOpenRO
Wed Apr 17 2024 12:54:20 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-07-31T18:30:00.000Z  End date:2021-08-30T18:30:00.000Z
Wed Apr 17 2024 12:54:20 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2022,01/01/2022,12/01/2021,12/31/2021,11/01/2021,11/30/2021,10/01/2021,10/31/2021,09/01/2021,09/30/2021,08/01/2021,08/31/2021,07/01/2021,07/31/2021,06/01/2021,06/30/2021,05/01/2021,05/31/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021,12/01/2020,12/31/2020,11/01/2020,11/30/2020,10/01/2020,10/31/2020,09/01/2020,09/30/2020,08/01/2020,08/31/2020,07/01/2020,07/31/2020,06/01/2020,06/30/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:54:20 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-06-30T18:30:00.000Z  End date:2021-07-30T18:30:00.000Z
Wed Apr 17 2024 12:54:20 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:54:20 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:55:02 GMT+0530 : {"StoreID":"01b3b97c-0904-8ca6-005f-0d03091c57ce"}:executing pullOpenRO
Wed Apr 17 2024 12:55:05 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-06-30T18:30:00.000Z  End date:2021-07-30T18:30:00.000Z
Wed Apr 17 2024 12:55:05 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2022,01/01/2022,12/01/2021,12/31/2021,11/01/2021,11/30/2021,10/01/2021,10/31/2021,09/01/2021,09/30/2021,08/01/2021,08/31/2021,07/01/2021,07/31/2021,06/01/2021,06/30/2021,05/01/2021,05/31/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021,12/01/2020,12/31/2020,11/01/2020,11/30/2020,10/01/2020,10/31/2020,09/01/2020,09/30/2020,08/01/2020,08/31/2020,07/01/2020,07/31/2020,06/01/2020,06/30/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:55:05 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-05-31T18:30:00.000Z  End date:2021-06-29T18:30:00.000Z
Wed Apr 17 2024 12:55:05 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:55:05 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:55:46 GMT+0530 : {"StoreID":"01b3b97d-0904-8c41-005f-0d03091c4a5e"}:executing pullOpenRO
Wed Apr 17 2024 12:55:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2021-05-31T18:30:00.000Z  End date:2021-06-29T18:30:00.000Z
Wed Apr 17 2024 12:55:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2022,01/01/2022,12/01/2021,12/31/2021,11/01/2021,11/30/2021,10/01/2021,10/31/2021,09/01/2021,09/30/2021,08/01/2021,08/31/2021,07/01/2021,07/31/2021,06/01/2021,06/30/2021,05/01/2021,05/31/2021,04/01/2021,04/30/2021,03/01/2021,03/31/2021,02/01/2021,02/28/2021,01/01/2021,01/31/2021,12/01/2020,12/31/2020,11/01/2020,11/30/2020,10/01/2020,10/31/2020,09/01/2020,09/30/2020,08/01/2020,08/31/2020,07/01/2020,07/31/2020,06/01/2020,06/30/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:55:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2021-04-30T18:30:00.000Z  End date:2021-05-30T18:30:00.000Z
Wed Apr 17 2024 12:55:48 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:55:48 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:56:26 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 12:56:26 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 12:56:28 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 12:56:28 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 12:56:28 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Closed RO Pull
Wed Apr 17 2024 12:56:28 GMT+0530 : {}:Date batch array of closed RO pull is [["06/01/2020","06/06/2020"],["05/01/2020","05/31/2020"],["04/01/2020","04/30/2020"],["03/01/2020","03/31/2020"],["02/01/2020","02/29/2020"],["01/01/2020","01/31/2020"]] 
Wed Apr 17 2024 12:56:28 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Wed Apr 17 2024 12:56:28 GMT+0530 : {"DealerID":"12345"}:Date batch array is:06/01/2020,06/06/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:56:28 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-05-31T18:30:00.000Z  End date:2020-06-05T18:30:00.000Z
Wed Apr 17 2024 12:56:28 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:56:28 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:57:10 GMT+0530 : {"StoreID":"01b3b97e-0904-8ca6-005f-0d03091c5886"}:executing pullOpenRO
Wed Apr 17 2024 12:57:11 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-05-31T18:30:00.000Z  End date:2020-06-05T18:30:00.000Z
Wed Apr 17 2024 12:57:11 GMT+0530 : {"DealerID":"12345"}:Date batch array is:06/01/2020,06/06/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:57:11 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-04-30T18:30:00.000Z  End date:2020-05-30T18:30:00.000Z
Wed Apr 17 2024 12:57:11 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:57:11 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:57:53 GMT+0530 : {"StoreID":"01b3b97f-0904-8ca6-005f-0d03091c598a"}:executing pullOpenRO
Wed Apr 17 2024 12:57:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-04-30T18:30:00.000Z  End date:2020-05-30T18:30:00.000Z
Wed Apr 17 2024 12:57:54 GMT+0530 : {"DealerID":"12345"}:Date batch array is:06/01/2020,06/06/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:57:54 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-03-31T18:30:00.000Z  End date:2020-04-29T18:30:00.000Z
Wed Apr 17 2024 12:57:54 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:57:54 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:58:36 GMT+0530 : {"StoreID":"01b3b97f-0904-8ca6-005f-0d03091c5a36"}:executing pullOpenRO
Wed Apr 17 2024 12:58:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-03-31T18:30:00.000Z  End date:2020-04-29T18:30:00.000Z
Wed Apr 17 2024 12:58:38 GMT+0530 : {"DealerID":"12345"}:Date batch array is:06/01/2020,06/06/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:58:38 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-02-29T18:30:00.000Z  End date:2020-03-30T18:30:00.000Z
Wed Apr 17 2024 12:58:38 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:58:38 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 12:59:23 GMT+0530 : {"StoreID":"01b3b980-0904-8ca6-005f-0d03091c5a62"}:executing pullOpenRO
Wed Apr 17 2024 12:59:24 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-02-29T18:30:00.000Z  End date:2020-03-30T18:30:00.000Z
Wed Apr 17 2024 12:59:24 GMT+0530 : {"DealerID":"12345"}:Date batch array is:06/01/2020,06/06/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 12:59:24 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-01-31T18:30:00.000Z  End date:2020-02-28T18:30:00.000Z
Wed Apr 17 2024 12:59:24 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 12:59:24 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 13:00:07 GMT+0530 : {"StoreID":"01b3b981-0904-8ca6-005f-0d03091c5ab6"}:executing pullOpenRO
Wed Apr 17 2024 13:00:09 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-01-31T18:30:00.000Z  End date:2020-02-28T18:30:00.000Z
Wed Apr 17 2024 13:00:09 GMT+0530 : {"DealerID":"12345"}:Date batch array is:06/01/2020,06/06/2020,05/01/2020,05/31/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Wed Apr 17 2024 13:00:09 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2020-01-30T18:30:00.000Z
Wed Apr 17 2024 13:00:09 GMT+0530 : {}:executing performROPullWithDateRange
Wed Apr 17 2024 13:00:09 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 13:00:50 GMT+0530 : {"StoreID":"01b3b982-0904-8ca6-005f-0d03091c5af6"}:executing pullOpenRO
Wed Apr 17 2024 13:00:53 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2020-01-30T18:30:00.000Z
Wed Apr 17 2024 13:00:53 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 13:00:53 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Wed Apr 17 2024 13:00:53 GMT+0530 : {}:executing pullCloseROdata
Wed Apr 17 2024 13:00:53 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 13:07:04 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 13:07:04 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 13:07:06 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 13:07:06 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 13:07:06 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Wed Apr 17 2024 13:07:06 GMT+0530 : undefined:executing make model
Wed Apr 17 2024 13:07:06 GMT+0530 : {}:executing pullOpenRO
Wed Apr 17 2024 14:17:38 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Wed Apr 17 2024 14:17:38 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Wed Apr 17 2024 14:17:40 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Wed Apr 17 2024 14:17:40 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Wed Apr 17 2024 14:17:40 GMT+0530 : {"Dealer ID":"12345"}:Ready for  makeModel Pull
Wed Apr 17 2024 14:17:40 GMT+0530 : undefined:executing make model
Wed Apr 17 2024 14:17:40 GMT+0530 : {}:executing pullOpenRO
