Thu Apr 18 2024 16:05:13 GMT+0530 : {"Dealer ID":"12345"}:<PERSON><PERSON><PERSON> started
Thu Apr 18 2024 16:05:13 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:05:22 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:05:22 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:05:22 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Customer Pull
Thu Apr 18 2024 16:05:22 GMT+0530 : undefined:executing pullCloseROdata
Thu Apr 18 2024 16:05:22 GMT+0530 : {}:executing pullOpenRO
Thu Apr 18 2024 16:31:53 GMT+0530 : {"Dealer ID":"12345"}:R<PERSON> <PERSON><PERSON> started
Thu Apr 18 2024 16:31:53 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:31:55 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:31:55 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:31:55 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 16:31:55 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Thu Apr 18 2024 16:31:55 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Thu Apr 18 2024 16:31:55 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Thu Apr 18 2024 16:31:55 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:31:55 GMT+0530 : {}:executing performROPullWithDateRange
Thu Apr 18 2024 16:31:55 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:32:16 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Thu Apr 18 2024 16:32:16 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:32:17 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:32:17 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:32:17 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 16:32:17 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Thu Apr 18 2024 16:32:17 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Thu Apr 18 2024 16:32:17 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Thu Apr 18 2024 16:32:17 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:32:17 GMT+0530 : {}:executing performROPullWithDateRange
Thu Apr 18 2024 16:32:17 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:33:00 GMT+0530 : {"StoreID":"01b3bff6-0904-8ca6-005f-0d0309200da6"}:executing pullOpenRO
Thu Apr 18 2024 16:33:07 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Thu Apr 18 2024 16:33:07 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:33:08 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:33:08 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:33:08 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 16:33:08 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Thu Apr 18 2024 16:33:08 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Thu Apr 18 2024 16:33:08 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Thu Apr 18 2024 16:33:08 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:33:08 GMT+0530 : {}:executing performROPullWithDateRange
Thu Apr 18 2024 16:33:08 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:33:22 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Thu Apr 18 2024 16:33:22 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:33:23 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:33:23 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:33:23 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 16:33:23 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Thu Apr 18 2024 16:33:23 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Thu Apr 18 2024 16:33:23 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Thu Apr 18 2024 16:33:23 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:33:23 GMT+0530 : {}:executing performROPullWithDateRange
Thu Apr 18 2024 16:33:23 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:34:06 GMT+0530 : {"StoreID":"01b3bff7-0904-8de4-005f-0d03092030a2"}:executing pullOpenRO
Thu Apr 18 2024 16:34:09 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:37:47 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Thu Apr 18 2024 16:37:47 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:37:48 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:37:48 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:37:48 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 16:37:48 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Thu Apr 18 2024 16:37:48 GMT+0530 : {}:Ready for performROPullWithDateRangeBatch 
Thu Apr 18 2024 16:37:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Thu Apr 18 2024 16:37:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:37:48 GMT+0530 : {}:executing performROPullWithDateRange
Thu Apr 18 2024 16:37:48 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:38:28 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Thu Apr 18 2024 16:38:28 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:38:29 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:38:29 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:38:29 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 16:38:29 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Thu Apr 18 2024 16:38:29 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Thu Apr 18 2024 16:38:29 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Thu Apr 18 2024 16:38:29 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:38:29 GMT+0530 : {}:executing performGlPullWithDateRange
Thu Apr 18 2024 16:38:29 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:38:46 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Thu Apr 18 2024 16:38:46 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:38:48 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:38:48 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:38:48 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 16:38:48 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Thu Apr 18 2024 16:38:48 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Thu Apr 18 2024 16:38:48 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Thu Apr 18 2024 16:38:48 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:38:48 GMT+0530 : {}:executing performGlPullWithDateRange
Thu Apr 18 2024 16:38:48 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:39:30 GMT+0530 : {"StoreID":"01b3bffc-0904-8de4-005f-0d03092031fa"}:executing pullOpenRO
Thu Apr 18 2024 16:39:43 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:41:34 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Thu Apr 18 2024 16:41:34 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:41:40 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:41:40 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:41:40 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 16:41:40 GMT+0530 : {}:Date batch array of closed RO pull is [["05/01/2020","05/05/2020"],["04/01/2020","04/30/2020"],["03/01/2020","03/31/2020"],["02/01/2020","02/29/2020"],["01/01/2020","01/31/2020"]] 
Thu Apr 18 2024 16:41:40 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Thu Apr 18 2024 16:41:40 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2020,05/05/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Thu Apr 18 2024 16:41:40 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-04-30T18:30:00.000Z  End date:2020-05-04T18:30:00.000Z
Thu Apr 18 2024 16:41:40 GMT+0530 : {}:executing performGlPullWithDateRange
Thu Apr 18 2024 16:41:40 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:42:22 GMT+0530 : {"StoreID":"01b3bfff-0904-9031-005f-0d030920238a"}:executing pullOpenRO
Thu Apr 18 2024 16:42:34 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2020-04-30T18:30:00.000Z  End date:2020-05-04T18:30:00.000Z
Thu Apr 18 2024 16:42:34 GMT+0530 : {"DealerID":"12345"}:Date batch array is:05/01/2020,05/05/2020,04/01/2020,04/30/2020,03/01/2020,03/31/2020,02/01/2020,02/29/2020,01/01/2020,01/31/2020
Thu Apr 18 2024 16:42:34 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2020-03-31T18:30:00.000Z  End date:2020-04-29T18:30:00.000Z
Thu Apr 18 2024 16:42:34 GMT+0530 : {}:executing performGlPullWithDateRange
Thu Apr 18 2024 16:42:34 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:44:47 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Thu Apr 18 2024 16:44:47 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 16:44:49 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 16:44:49 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 16:44:49 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 16:44:49 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Thu Apr 18 2024 16:44:49 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Thu Apr 18 2024 16:44:49 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Thu Apr 18 2024 16:44:49 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 16:44:49 GMT+0530 : {}:executing performGlPullWithDateRange
Thu Apr 18 2024 16:44:49 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 16:44:51 GMT+0530 : {}:executing pullOpenRO
Thu Apr 18 2024 16:44:52 GMT+0530 : {"DealerID":"12345"}:Closed RO pull finished start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 17:51:06 GMT+0530 : {"Dealer ID":"12345"}:RO Pull started
Thu Apr 18 2024 17:51:06 GMT+0530 : {"Dealer ID":"12345"}:perform extractBundle
Thu Apr 18 2024 17:51:08 GMT+0530 : {"Dealer ID":"12345"}:Perform performAPICall
Thu Apr 18 2024 17:51:08 GMT+0530 : {"Dealer ID":"12345"}:bundle type is initial
Thu Apr 18 2024 17:51:08 GMT+0530 : {"Dealer ID":"12345"}:Ready for  Open RO Pull
Thu Apr 18 2024 17:51:08 GMT+0530 : {}:Date batch array of closed RO pull is [["01/01/2020","01/01/2020"]] 
Thu Apr 18 2024 17:51:08 GMT+0530 : {}:Ready for performGlPullWithDateRangeBatch 
Thu Apr 18 2024 17:51:08 GMT+0530 : {"DealerID":"12345"}:Date batch array is:01/01/2020,01/01/2020
Thu Apr 18 2024 17:51:08 GMT+0530 : {"DealerID":"12345"}:Closed RO pull Starting start date:2019-12-31T18:30:00.000Z  End date:2019-12-31T18:30:00.000Z
Thu Apr 18 2024 17:51:08 GMT+0530 : {}:executing performGlPullWithDateRange
Thu Apr 18 2024 17:51:08 GMT+0530 : {}:executing pullCloseROdata
Thu Apr 18 2024 17:51:11 GMT+0530 : {}:executing pullOpenRO
