#!/usr/bin/env node

'use strict';
const pkg = require('./package.json');
const program = require('commander');
const constants = require('./src/constants');
const moment = require("moment-timezone");
const agendaDB = require("./src/model/agendaDB")
const configManager = require('./src/configManager');

const fs = require('fs');

program
    .command('configure')
    .alias('c')
    .option('-b, --baseURL <value>', 'Specify Base URL')
    .option('-u, --vendorID <value>', 'Specify Vendor ID')
    .option('-p, --vendorPassword <value>', 'Specify Vendor Password')
    .description('Configure Requestor Application')
    .action((options) => {
        configManager.doConfig(options);
    });

program
    .command('pull')
    .alias('p')
    .option('--dealerID <value>', 'Specify Dealer ID')
    // .option('--storeUsername <value>', 'Specify Store User Name')
    // .option('--storePassword <value>', 'Specify Store Password')
    .option('-s, --startDate <value>', 'Specify Start Date')
    .option('-e, --endDate <value>', 'Specify End Date')
    .option('--deadLetter <value>', 'Specify Dead-Letter Directory')
    .option('-o, --outputDir <path>', 'Specify the path to save the output')
    .option('-r, --overrideFolderName <output folder name>', 'Override default folder naming convention and save to --outputDir')
    .option('-z, --zipPath <path>', 'Specify the path for output zip file')
    .option('-x, --overrideZipName <output zip name>', 'Override default zip naming convention and save to --zipPath')
    .option('-Z, --noZapAfterDist', 'leave uncompressed directory behind')
    .option('-z, --zapAfterDist', '(default) Zap uncompressed data after performing compression')
    .option('-p, --open', 'Perform  Open Repair Order pull')
    .option('-cu,--customer', 'Perform  Customer data pull')
    .option('-c, --closed [options]', 'Perform  Closed Repair Order pull', /^(all|monthly|weekly|current)$/i)
    .option('-b, --bundle <name>', 'Extract all jobs required for a specific named bundle')
    .option('--mageGroupCode <code>', 'MAGE Group Code')
    .option('--mageStoreCode <code>', 'MAGE Store Code')
    .option('--stateCode <code>', 'State in which the store resides')
    .option('--extractionID <code>', 'Extraction id of job')
    .option('--glAccountCompanyID <code>', 'Extraction id of job')
    .option('--departmentId <code>', 'Extraction department id of job')
    .option('--thirdPartyUsername <code>', 'Extraction third party username of job')

    .description('Start Requestor Application')
    .action(async (options) => {
        console.log("test1@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
        await agendaDB.createStructure(options.extractionID,options.dealerID,options.glAccountCompanyID);
        existFlagsOrDie(options);
 
        var callHadErrors = await configManager.doAPICall(options);
       
        
        if (callHadErrors) {
            try {
                process.exit(constants.STATUS_CODE.DEADLETTER_PATH);

            } catch (error) {
                console.log(error.message);
            }
        } else {
            try {
                process.exit(constants.STATUS_CODE.SUCCESS);
            } catch (error) {
                console.log(error.message);
            }
        }
    
    });

    function existFlagsOrDie(options) {
        // Set default values to --closed and --all parameters
        (options.closed && options.closed.length == undefined) ? options.closed = null : null;
        (options.all && options.all.length == undefined) ? options.all = null : null;
    
        if (options.zipPath && options.outputDir) {
            console.error("Please only specify one of Zip or Output Directory");
            process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        }
        if (options.zipPath && options.overrideFolderName) {
            console.error("Zip Path and Override Folder Not Compatible");
            process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        }
        if (options.outputDir && options.overrideZipName) {
            console.error("Zip Path and Override Folder Not Compatible");
            process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        }
        if (options.overrideFolderName && options.overrideZipName) {
            console.error("Please only specify one of zip or output name override");
            process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        }
        if (!options.deadLetter) {
            console.error("Dead Letter Directory Required");
            process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        } else {
            if (!fs.existsSync(options.deadLetter)) {
                console.error("Dead Letter Directory Must Already Exist: " + options.deadLetter);
                process.exit(constants.STATUS_CODE.GENERAL_DEATH);
            }
        }
    
        options.zapAfterDist = options.zapAfterDist || !options.noZapAfterDist || false;
    
        /**
         * Either --all or any one of (--model|--employee|--customer|--open|--closed|--rowip|--bundle)
         * parameter should pass to CLI.
         */
        // if (!options.open
        //     && !options.closed && !options.all && !options.rowip ) {
        //     console.error(constants.AT_LEAST_ONE_OPT_ERROR);
        //     process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        // }
    
        // if (!options.partyID) {
        //     console.error(constants.PARTY_ID_ERR);
        //     process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        // }

        // if (!options.dealerID) {
        //     console.error(constants.DEALER_ID_ERR);
        //     process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        // }
    
        // if (!options.storeUsername) {
        //     console.error(constants.STOREUSERNAME_NO_ERR);
        //     process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        // }

        // if (!options.storePassword) {
        //     console.error(constants.STOREPASSWORD_NO_ERR);
        //     process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        // }
    
        if (!options.startDate) {
            console.error(constants.START_DATE_PARAM_ERR);
            process.exit(constants.STATUS_CODE.GENERAL_DEATH);
        } else {
            if (!options.startDate.match(constants.CLI_DATE_FORMAT_REGEXP)) {
                console.error(constants.DATE_FORMAT_ERROR, constants.CLI_DATE_FORMAT);
                process.exit(constants.STATUS_CODE.GENERAL_DEATH);
            }
        }
    
        if (!options.endDate) {
            console.error(constants.END_DATE_PARAM_ERR);
            process.exit(constants.STATUS_CODE.GENERAL_DEATH);
    
        } else {
            if (!options.endDate.match(constants.CLI_DATE_FORMAT_REGEXP)) {
                console.error(constants.DATE_FORMAT_ERROR, constants.CLI_DATE_FORMAT);
                process.exit(constants.STATUS_CODE.GENERAL_DEATH);
            }
        }
    
        if (options.noReset) {
            options.reset = false;
        } else {
            options.reset = true;
        }
    
        /**
         * User can override the output folder name; otherwise default to locationID-timestamp
         */
        if (options.overrideZipName) {
            options.overrideName = options.overrideZipName;
            options.workingName = options.overrideZipName;
        } else if (options.overrideFolderName) {
            options.overrideName = options.overrideFolderName;
            options.workingName = options.overrideFolderName;
        } else {
            /**
             * Naming convention:-
             * Format: <mage_group_code>-<mage_store_code>-<state>-<JOB_TYPE>-<third_party_username>-yyyymmddhhmmss
             * Spaces replaced with underscores;
             * separate components with hyphens;
             * Timestamp should be yyyymmddhhmmss (sub-second precision unnecessary);
             * Job Type should be Title Case;
             * Remove all symbols from MAGE IDs (letters and numbers only)
             */
            let mgc = options.mageGroupCode ? getConventionString(options.mageGroupCode) : null;
            let msc = options.mageStoreCode ? getConventionString(options.mageStoreCode) : null;
            let sta = options.stateCode ? getConventionString(options.stateCode) : null;
            let jobType = options.bundle ? options.bundle.toUpperCase() : null;
            let ts = options.omitTimestamp ? null : moment().format("YYYYMMDDhhmmss");
            let namePrefix = [mgc, msc, sta, jobType, options.dealerID, ts]
                .filter(v => v !== null)
                .map(v => `${v}-`)
                .toString();
            options.workingName = namePrefix.slice(0, namePrefix.length - 1).replace(/[,]/g, "");
        }
    }

program.parse(process.argv);

if (program.args.length === 0) program.help();

function list(val, pattern) {
    return val.split(',').filter(value => value.match(pattern));
}

function getConventionString(inSting) {
    return inSting.replace(/[-]/g, "_").replace(/[^a-zA-Z0-9_]/g, '');
}
