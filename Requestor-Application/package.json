{"name": "<PERSON><PERSON><PERSON>-requestor", "version": "1.0.0", "description": "Data Requestor for the Fortellis API", "main": "<PERSON><PERSON><PERSON>-requestor", "preferGlobal": true, "bin": "<PERSON><PERSON><PERSON>-requestor", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON> (Netspective)", "license": "ISC", "dependencies": {"axios": "^0.21.1", "cli-logger": "^0.5.40", "cli-spinner": "^0.2.10", "commander": "^2.17.1", "dotenv": "^6.0.0", "moment-timezone": "^0.5.21", "mongodb": "^3.6.0", "node-zip": "^1.1.1", "path": "^0.12.7", "request": "^2.88.0", "request-promise": "^4.2.2", "simple-node-logger": "^0.93.37", "soap": "^0.27.1", "xml2json": "^0.12.0", "zip-folder": "^1.0.0", "dayjs": "^1.11.13"}}