#!/usr/bin/env node

'use strict';

var zipFolder = require('zip-folder');

/**
 * Function to zip a folder and save it to a particular location
 * 
 * @param {string} dataPath Path to folder to zip
 * @param {string} outputPath Output zip file name with full path
 */
function generateZip(dataPath, outputPath) {
    return new Promise((resolve, reject) => {
        zipFolder(dataPath, outputPath, function (err) {
            if (err) {
                reject(err);
            } else {
                resolve();
            }
        });
    });
}

module.exports = {
    generateZip: generateZip
}

