const axios = require("axios");
const qs = require("qs");
const constants = require("./constants");

exports.generateAuthToken = () => {
  console.log("generating token...................................");
  return new Promise((resolve, reject) => {
    try {
      const authString = Buffer.from(`qq8Ru3IqJW9aURLlRjJP2M5UlnLcTcND:Uzx4yaalR5O5K6GmurhnQX92DLWEyQdC`).toString('base64');
      console.log('auth string',authString);
      const data = qs.stringify({
        grant_type: 'client_credentials',
        scope: 'anonymous'
      });

      const config = {
        method: 'post',
        url: 'https://identity.fortellis.io/oauth2/aus1p1ixy7YL8cMq02p7/v1/token?grant_type=client_credentials&scope=anonymous',
        headers: {
          Authorization: `Basic ${authString}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: data
      };

      axios(config)
        .then(function (response) {
          // console.log("response>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",response);
          console.log("hiiiiiiii");
          resolve({ status: true, data: response});
        })
        .catch(function (error) {
          console.log("error>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",error);
          resolve({ status: false, data: error.response.data });
        });

    } catch (err) {
      console.log("response>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
      reject({ status: false, data: err.message });
    }
  });
};
