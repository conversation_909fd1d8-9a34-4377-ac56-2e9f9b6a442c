const { MongoClient } = require("mongodb");

async function createStructure(extractionId, dealerID, companyId) {
    console.log("createStructure@@@@@@@@@@@@@@@@@@@@@@", extractionId);
    console.log("dealerID@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@", dealerID);
    console.log("companyId@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@", companyId);

    // const uri = "mongodb://localhost:27017";
    // const client = new MongoClient(uri);

    // try {
    //     await client.connect();
    //     const db = client.db("ApiStatus");

    //     // Insert the document
    //     const result = await db.collection("cdk").insertOne({
    //         id: extractionId,
    //         dealerid: dealerID,
    //         cId: companyId,
    //         type: []
    //     });

    //     console.log(`Document inserted with _id: ${result.insertedId}`);
    //     return result; // Return the result directly, no need for manual resolve
    // } catch (error) {
    //     console.error("Error inserting document:", error);
    //     throw error; // Instead of rejecting, throw the error for caller to handle
    // } finally {
    //     await client.close();
    // }
}

module.exports = {
    createStructure
};




// async function updateRO() {
//   const uri = "mongodb://localhost:27017"; // Replace with your MongoDB URI
//   const client = new MongoClient(uri);

//   try {
//     await client.connect();
//     const db = client.db("yourDatabaseName"); // Replace with your database name
//     const collection = db.collection("yourCollectionName"); // Replace with your collection name

//     // Array of RO objects to insert
//     const roDetails = [
//       { Ro1: "RO1 Detail", date: "2025-01-21", status: "Pending" },
//       { Ro2: "RO2 Detail", date: "2025-01-22", status: "Pending" },
//       { Ro3: "RO3 Detail", date: "2025-01-23", status: "Pending" },
//       { Ro4: "RO4 Detail", date: "2025-01-24", status: "Pending" },
//       { Ro5: "RO5 Detail", date: "2025-01-25", status: "Pending" },
//       { Ro6: "RO6 Detail", date: "2025-01-26", status: "Pending" }
//     ];

//     // Update the document by pushing each RO object into the `type.0.RO` array
//     const result = await collection.updateOne(
//       { id: 1 }, // Match the document by `id`
//       {
//         $push: {
//           "type.0.RO": { $each: roDetails }
//         }
//       }
//     );

//     console.log(`Matched ${result.matchedCount} document(s), modified ${result.modifiedCount} document(s)`);
//   } catch (error) {
//     console.error("Error updating document:", error);
//   } finally {
//     await client.close();
//   }
// }