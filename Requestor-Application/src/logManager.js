#!/usr/bin/env node

'use strict';

const constants = require('./constants');

const logController = require('simple-node-logger');
const path = require('path');
const LOGLEVEL = {
    INFO: 'info',
    ERROR: 'error',
    WARN: 'warn'
}

const APP_LOG_DIR = constants.LOG_OUTPUT_PATH;
const LOG_FILE_EXTENSION = '-log-<DATE>.log';
const APP_LOG_PATTERN = 'Application' + LOG_FILE_EXTENSION;
const LOG_DATE_FORMAT = 'YYYY-MM-DD';
const APP_ERR_EVENT_NAME = 'error';

/**
 * Logger for full applicaiton
 */
function getApplicationLogger() {
    const app_opts = {
        errorEventName: APP_ERR_EVENT_NAME,
        logDirectory: APP_LOG_DIR,
        fileNamePattern: APP_LOG_PATTERN,
        dateFormat: LOG_DATE_FORMAT
    };
    var logger = logController.createRollingFileLogger(app_opts);
    return logger;
}

/**
 * Logger for a particular Dealer API session
 *
 * @param {string} logFileName Log file name
 * @param {string} filePath Path to the which the session log file to save
 */
function getSessionLogger(fileNamePattern, logDirectory) {
    const session_opts = {
        errorEventName: APP_ERR_EVENT_NAME,
        logDirectory: logDirectory,
        fileNamePattern: fileNamePattern + LOG_FILE_EXTENSION,
        dateFormat: LOG_DATE_FORMAT
    };
    var logger = logController.createSimpleFileLogger(path.join(logDirectory, fileNamePattern + '.log'));
    return logger;
}


module.exports = {
    LOGLEVEL: LOGLEVEL,
    getSessionLogger: getSessionLogger,
    getApplicationLogger: getApplicationLogger()
}
