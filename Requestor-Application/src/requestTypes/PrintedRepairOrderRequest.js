#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class PrintedRepairOrderRequest extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_PROD_DWNLD;
        this.msg_download_failed = constants.messages.PROD_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.PROD_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.PRINTED_RO;
        this.ctx_download = constants.contexts.PROD_DWNLD;
        this.tag_header = constants.tags.OPENED_HEADER_TAG;
        this.extractionCallMethod = apiManager.performPrintedROPull;
        this.getROSCount = apiManager.getPrintedROsCount;
        this.leading_context_length = 115;
    }
}

module.exports = PrintedRepairOrderRequest;
