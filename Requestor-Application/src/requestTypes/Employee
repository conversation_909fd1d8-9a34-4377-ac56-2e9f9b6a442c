#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class Employee extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_EMP_DWNLD;
        this.msg_download_failed = constants.messages.EMP_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.EMP_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.EMPLOYEE;
        this.ctx_download = constants.contexts.EMPLOYEE_DWNLD;
        this.tag_header = constants.tags.c;
        this.extractionCallMethod = apiManager.performEmployeePull;
        this.leading_context_length = 115;
    }
}

module.exports = Employee;
