#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class MakeModel extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_MAKE_DWNLD;
        this.msg_download_failed = constants.messages.MAKE_DWNLD_FAILED_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.MAKE_DWNLD_CMPLT_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.MAKE;
        this.ctx_download = constants.contexts.MAKE_MODEL_DWNLD_DWNLD;
        this.tag_header = constants.tags.CUSTOMER_HEADER_TAG;
        this.extractionCallMethod = apiManager.performMakeModelPull;
        this.leading_context_length = 115;
    }
}

module.exports = MakeModel;
