#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class Customer extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_COA_BULK_DWNLD  ;
        this.msg_download_failed = constants.messages.COA_BULK_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.COA_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.GL_COA;
        this.ctx_download = constants.contexts.COA;
        this.tag_header = constants.tags.CUSTOMER_HEADER_TAG;
        this.extractionCallMethod = apiManager.performCoaPull;
        this.leading_context_length = 115;
    }
}

module.exports = Customer;
