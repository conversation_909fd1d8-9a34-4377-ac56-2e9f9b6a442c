#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class RoBulk extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_RO_BULK_DWNLD;
        this.msg_download_failed = constants.messages.RO_BULK_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.RO_BULK_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.ROBULK;
        this.ctx_download = constants.contexts.RO_BULK_DWNLD;
        this.tag_header = constants.tags.CUSTOMER_HEADER_TAG;
        this.extractionCallMethod = apiManager.performROBULKPull;
        this.leading_context_length = 115;
    }
}

module.exports = RoBulk;
