#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class Labor extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_LABOR_DWNLD;
        this.msg_download_failed = constants.messages.LABOR_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.LABOR_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.LABOR;
        this.ctx_download = constants.contexts.LABOR_DWNLD;
        this.tag_header = constants.tags.CUSTOMER_HEADER_TAG;
        this.extractionCallMethod = apiManager.performLaborPull;
        this.leading_context_length = 115;
    }
}

module.exports = Labor;
