#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class GLAcounting extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_GL_ACCOUNTING_BULK_DWNLD;
        this.msg_download_failed = constants.messages.GL_ACCOUNTING_BULK_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.GL_ACOUNTING_BULK_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.GL_ACCOUNTING;
        this.ctx_download = constants.contexts.GL_ACCOUNTING_BULK_DWNLD;
        this.tag_header = constants.tags.CUSTOMER_HEADER_TAG;
        this.extractionCallMethod = apiManager.performGlAccountingPull;
        this.leading_context_length = 115;
    }
}

module.exports = GLAcounting;