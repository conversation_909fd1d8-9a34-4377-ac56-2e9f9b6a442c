#!/usr/bin/env node

const fs = require('fs');
const path = require("path");
const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');
const util = require('../util');

const LOG_LEVEL = {
    INFO: 'info',
    ERROR: 'error',
    WARN: 'warn'
}

var segmentInfo;

var closedROsCount =0;
var pageArray = [];
var loopIncrementer = 0;

class RepairOrderRequest extends GenericRequest {
    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_HROD_DWNLD;
        this.msg_download_failed = constants.messages.HROD_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.HROD_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.HISTORY_RO;
        this.ctx_download = constants.contexts.HROD_DWNLD;
        this.success = null;
        this.leading_context_length = 1000;
    }



    async execute(options) {
        console.log("Execute>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        segmentInfo = JSON.stringify({'Franchise':options.franchise,'storeID':options.storeID});
        var prom = new Promise(async (resolve) => {
            var inpObj = {};
            if (options.startDate && options.endDate) {

                console.log("start date ************************************",options.startDate);
                console.log("end date ************************************",options.endDate);
                /** Pull and loop */
                // var isBatchProcessing = (options.closed && options.closed.length !== undefined) ? true : false;
                var isBatchProcessing = true;
                var dateArray = [];
                var dateConversionStatus = true;
                try {
                    // fetch date and convert into below type
                    //dateArray = util.splitDateRange('05/08/2017', '05/08/2019', 'monthly');
                    var extractionMode = options.closed ? options.closed : constants.CLI_CLOSED_FLAG.MODE_MONTHLY;
                    dateArray = util.splitDateRange(options.startDate, options.endDate, extractionMode);
                    util.saveSegment(`Date batch array of closed RO pull is ${JSON.stringify(dateArray)} `,segmentInfo);
                } catch (err) {
                    dateConversionStatus = false;
                    this.writeLog("error", this.ctx_download, this.msg_download_started + '; Error message: ' + err);
                }
                if (dateConversionStatus) {
                    /**
                    * RO bulk/batch download
                    */
                
                    util.saveSegment(`Ready for performROPullWithDateRangeBatch `,segmentInfo);
                    await performROPullWithDateRangeBatch(this, dateArray, 0, isBatchProcessing, false, options, this);
                    resolve(this.noneFailed);
                } else {
                    console.error("Date Conversion Expected to Succeed");
                    resolve(false);
                }
            } else {
                inpObj = { locationID: options.locationID, status: options.status };
                var res = await apiManager.performROPull(inpObj, this);
                this.writeLog("info", this.ctx_download, this.msg_download_started);
                this.storeCapturedDataInTempFile(res, options.locationID, options.status);
                resolve(true);
            }
        });
        try {
            var all_good = await prom;
            return all_good;
        } catch (err) {
            console.error("Unexpected Failure");
            process.exit(1);
        }
    }

    storeCapturedDataInTempFile(res, locationID, type, version) {
        try {
            let fileName = this.filename_base;
            if(version){
                fileName = fileName+'.'+version;
            }
            this.writeLog(LOG_LEVEL.INFO, this.ctx_download, constants.messages.REQ_HEADER + res.requestHeader); // Request header log
            this.writeLog(LOG_LEVEL.INFO, this.ctx_download, constants.messages.RES_HEADER + responseHeaderToString(res.responseHeader)); // Response header log
            this.writeLog(LOG_LEVEL.INFO, this.ctx_download, this.msg_download_completed)
            fs.writeFileSync(this.output_foldername_temp + "/" + fileName + '-' + '.json', res.result);
            fs.writeFileSync(this.output_foldername_temp + "/" + fileName + '-' + '.req', buildRequestSummary(res.requestHeader));
            fs.writeFileSync(this.output_foldername_temp + "/" + fileName + '-' + '.res', buildResponseSummary(res, this));
            // fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '-' + type + '.time', buildTimingJSONString(res, this, locationID, type));
            this.success = true;
        } catch (chain_err) {
            this.writeLog(LOG_LEVEL.WARN, this.ctx_download, 'Data Writeout Failed' + ' (' + chain_err.message + ')');
            this.success = false;
        }
    }

    handleError(err, dealerID, type) {
        console.log("handleError %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%",err);
        try {
            // this.writeLog(LOG_LEVEL.WARN, this.ctx_download, this.msg_download_failed + ' (' + err.result + ')');
            if (err.result) {
                let res = err;
                // this.writeLog(LOG_LEVEL.ERROR, this.ctx_download, this.msg_download_failed + '; ' + err.result);
                fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.req', buildRequestSummary(res.requestHeader));
                fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.err', buildResponseSummary(res, this, false));
                // fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '-' + type + '.time', buildTimingJSONString(res, this, dealerID, type));
                //while the request resulted in an error the processing is expecting it and has a non-failure mode
                //to handle it - as far as the caller is concerned while it wasn't successful it didn't fail either
                this.success = false;
            } else {
                this.writeLog(LOG_LEVEL.WARN, this.ctx_download, this.msg_download_failed + ' (' + err.result + ')');
                fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.err', err.result);
                this.success = false;
            }
        } catch (chain_err) {
            this.writeLog(LOG_LEVEL.WARN, this.ctx_download, 'Error Logging Failed' + ' (' + chain_err.result + ')');
            this.success = false;
        }
    }

    /**
     * Common function to handle the application and session log
     * @param logLevel
     * @param context
     * @param message
     */
    writeLog(logLevel, context, message) {

        context = context.padEnd(constants.contexts.PADDING) + ': ';
        if (logLevel == LOG_LEVEL.INFO) {
            console.log(logLevel + ': ', message);
            this.app_logger.info(context, message);
            this.session_logger.info(context, message);
        } else if (logLevel == LOG_LEVEL.WARN) {
            console.log(logLevel + ': ', message);
            this.app_logger.warn(context, message);
            this.session_logger.warn(context, message);
        } else if (logLevel == LOG_LEVEL.ERROR) {
            console.error(logLevel + ': ', message);
            this.app_logger.error(context + message);
            this.session_logger.error(context + message);
        }
    }

}

async function performROPullWithDateRangeBatch(obj, startAndEndDates, i, performingBatch, oneFailed, options, errFileInfo) {
     console.log("TEST3333333333333333333333333333333333333333333333333333333333333333333333333333");
    let segmentInfo = JSON.stringify({'DealerID':options.dealerID,'EnterpriseCode':options.enterpriseCode}); 
    if (performingBatch) {
        obj.writeLog(
            "info", obj.ctx_download,
            `History RO Batch ${i + 1} of Dealer ID: ${options.dealerID} from ${startAndEndDates[i][0]} to ${startAndEndDates[i][1]}`
        );
        obj.filename_base = obj.filename_base.split('_')[0] + `_${i}`;
        obj.msg_download_completed = `History Repair Order batch ${i + 1} download completed`;
    } else {
        obj.writeLog("info", obj.ctx_download, obj.msg_download_started);
    }
    util.saveSegment('Date batch array is:' + startAndEndDates,segmentInfo);

    try {
        var startDate = new Date(startAndEndDates[i][0]).toISOString();
        var endDate = new Date(startAndEndDates[i][1]).toISOString();
        var currentDate =   new Date().toISOString();
        var inpObj = {
            token: options.token,
            partyID: options.partyID,
            dealerID: options.dealerID,
            status: options.status,
            startDate: startDate,
            endDate: endDate,
            currentDate: currentDate,
            extractionID: options.extractionID,
            glAccountCompanyID: options.glAccountCompanyID,
            thirdPartyUsername: options.thirdPartyUsername,
            departmentId: options.departmentId
        }            
        util.saveSegment('Closed RO pull Starting start date:' + startDate + '  End date:' + endDate,segmentInfo);
        var res = await apiManager.performROPullWithDateRange(inpObj, errFileInfo);
        util.saveSegment('Closed RO pull finished start date:'+startDate+'  End date:'+endDate,segmentInfo); 
        obj.storeCapturedDataInTempFile(res, options.locationID, options.status);
        if (!obj.success) {
            oneFailed = true;
        }
        res = null;
    } catch (err) {
        obj.handleError(err, options.locationID);
        obj.success = false;
        oneFailed = true;
    }
    try {
        if (performingBatch && i + 1 < startAndEndDates.length) {
            await performROPullWithDateRangeBatch(obj, startAndEndDates, i + 1, performingBatch, oneFailed, options, errFileInfo);
        }
    } catch (err) {
        oneFailed = true;
        console.error(`Unexpected Failure During Recursion: ${i}: ${err.message}`);
    }

    if (oneFailed) {
        obj.noneFailed = false;
    } else {
        obj.noneFailed = true;
    }
    return oneFailed;
}


// async function performROPullWithDateRangeBatch(obj, startAndEndDates, i, performingBatch, oneFailed, options, errFileInfo) {

//     closedROsCount =0;
//     pageArray = [];
//     loopIncrementer = 0;

//     let segmentInfo = JSON.stringify({'Franchise':options.franchise,'StoreID':options.storeID}); 
//     if (performingBatch) {
//         obj.writeLog(
//             "info", obj.ctx_download,
//             `History RO Batch ${i + 1} of Franchise: ${options.franchise} from ${startAndEndDates[i][0]} to ${startAndEndDates[i][1]}`
//         );
//         obj.filename_base = obj.filename_base.split('_')[0] + `_${i}`;
//         obj.msg_download_completed = `History Repair Order batch ${i + 1} download completed`;
//     } else {
//         obj.writeLog("info", obj.ctx_download, obj.msg_download_started);
//     }
//     util.saveSegment('Date batch array is:' + startAndEndDates,segmentInfo);

//     try {
//         var startDate = new Date(startAndEndDates[i][0]).toISOString();
//         var endDate = new Date(startAndEndDates[i][1]).toISOString();
//         var inpObj = { token: options.token, locationID: options.locationID, franchise: options.franchise, storeID: options.storeID, storeUsername: options.storeUsername, storePassword:options.storePassword,status: options.status, startDate: startDate, endDate: endDate }
//         util.saveSegment('Closed RO pull Starting start date:' + startDate + '  End date:' + endDate,segmentInfo);
//         // var closedROsCount =await apiManager.getCountROPullWithDateRange(inpObj);
       
//         var closedROsCount = 500;
//         util.saveSegment(`closed RO count in start date ${startDate} and end date ${endDate} is: ${closedROsCount} `,segmentInfo);
//         let start = 0;
//         let end = 0;
//         let limit = constants.API_PAGE_LIMIT;
//         closedROsCount = closedROsCount == 0 ? 1:closedROsCount;
//         let totalLimit = Math.ceil(closedROsCount/limit);
//         for(let i=1;i<=totalLimit;i++){
//             let offset = (i-1)*limit;  
//             if(i==1){
//                 start = offset;
//             }
//             else{
//                 start = offset+1;
//             }
//             end = offset +limit;
//             pageArray.push({start:start,end:end}); 
//         }
//         util.saveSegment(`Pagination array of closed RO pull in start date ${startDate} and end date ${endDate} is: ${JSON.stringify(pageArray)} `,segmentInfo);
//         // Adam allows 1000 ROs per one request.
//         await performROPullWithLimit(inpObj, errFileInfo, options, obj);
//     } catch (err) {
//         obj.handleError(err, options.locationID);
//         obj.success = false;
//         oneFailed = true;
//     }
//     try {
//         if (performingBatch && i + 1 < startAndEndDates.length) {
//             await performROPullWithDateRangeBatch(obj, startAndEndDates, i + 1, performingBatch, oneFailed, options, errFileInfo);
//         }
//     } catch (err) {
//         oneFailed = true;
//         console.error(`Unexpected Failure During Recursion: ${i}: ${err.message}`);
//     }

//     if (oneFailed) {
//         obj.noneFailed = false;
//     } else {
//         obj.noneFailed = true;
//     }
//     return oneFailed;
// }

function performROPullWithLimit(inpObj, errFileInfo, options, obj, resolveObjComplete){
    util.saveSegment(`Pagination  of closed RO pull in start date ${inpObj.startDate} and end date ${inpObj.endDate} is started`,segmentInfo);
    return new Promise(_resolve => {
      if(resolveObjComplete){
        util.saveSegment(`Pagination  of closed RO pull in start date ${inpObj.startDate} and end date ${inpObj.endDate} is finished`,segmentInfo);
            resolveObjComplete('completed');
      }
      else{
        paginateClosedROBatch(inpObj, errFileInfo, options, obj, _resolve);
      }
    });    
}


async function paginateClosedROBatch(inpObj, errFileInfo, options, obj,  _resolve){
        inpObj.start = pageArray[loopIncrementer].start;
        inpObj.end = pageArray[loopIncrementer].end;
        util.saveSegment(`Pagination  of closed RO pull in start date ${inpObj.startDate} and end date ${inpObj.endDate} start index:${options.start} , end index:${options.end}, loopIncrementer:${loopIncrementer}`,segmentInfo);
        var res = await apiManager.performROPullWithDateRange(inpObj, errFileInfo, pageArray[loopIncrementer].start, pageArray[loopIncrementer].end);
        obj.storeCapturedDataInTempFile(res, options.locationID, options.status, loopIncrementer);
        if (!obj.success) {
            oneFailed = true;
        }
        res = null; 
        loopIncrementer++;
        if(loopIncrementer>=pageArray.length){
            performROPullWithLimit(inpObj, errFileInfo, options, obj, _resolve);   
        }
        else{
            paginateClosedROBatch(inpObj, errFileInfo, options, obj,  _resolve);
        }
}

module.exports = RepairOrderRequest;

function buildTimingJSONString(res, requestModel, dealerID, type) {
    return JSON.stringify({
        "Start": res.timingStart,
        "Total": Math.round(res.timingPhases.total),
        "First": Math.round(res.timingPhases.firstByte),
        "DLoad": Math.round(res.timingPhases.download),
        "Size": res.body.length,
        "Status": res.statusCode,
        "DealerID": dealerID,
        "FileType": type,
        "HostName": res.request.uri.hostname
    });
}

function buildResponseSummary(res, requestModel, trimBody = true) {
    return 'Status: ' + (res.statusCode ? res.statusCode : 'No Status Code') + '\n' +
        'Length: ' + (res.result ? JSON.stringify(res.result).length : '<No Body>') + '\n\n' +
        responseHeaderToString(res.result) + '\n' +
        (trimBody ? '\n[Body (first ' + requestModel.leading_context_length + ' characters)]\n' : "[Body]\n") +
        (res.result ? (trimBody ? JSON.stringify(res.result).substring(0, requestModel.leading_context_length) : res.result) : '') + '\n' +
        '[End of Body]\n';
}

/**
 * Function to convert response object to string of new line separated key/value
 * @param {Object} respObject
 */
function responseHeaderToString(respObject) {
    let returnString = '[Begin Headers]';
    if (typeof respObject === 'string') {
        try {
            respObject = JSON.parse(respObject); // Attempt to parse JSON string
        } catch (e) {
            util.saveSegment('Error in JSON parse: ' + respObject);
        }
    }
    if (typeof respObject !== 'object' || respObject === null) {
        util.saveSegment('Invalid data type: ' + respObject);
        returnString += respObject;
    } else {
        Object.entries(respObject).forEach(([key, value]) => {
            returnString += '\n  ' + key + ": " + value; // You can modify this to JSON.stringify(value) if needed
        });
    }
    returnString += '\n[End of Headers]';
    return returnString;
}

function buildRequestSummary(req) {
    return req + '\n';
}
