#!/usr/bin/env node

const fs = require('fs');
const path = require("path");
const constants = require('../constants');
const util = require('../util');


const LOG_LEVEL = {
    INFO: 'info',
    ERROR: 'error',
    WARN: 'warn'
}

var count =0;
var pageArray = [];
var loopIncrementer = 0;

var segmentInfo;

class GenericRequest {

    constructor(output_foldername) {
        this.output_foldername_temp = output_foldername;
        this.success = null;
        this.app_logger = null;
        this.session_logger = null;
        this.filename_base = constants.outputFileName.GL_DELTA;

    }

    setAppLogger(app_logger) {
        this.app_logger = app_logger;
    }

    setSessionLogger(session_logger) {
        this.session_logger = session_logger;
    }

    // async execute(options) {
    //     count =0;
    //     pageArray = [];
    //     loopIncrementer = 0;
    //     segmentInfo = JSON.stringify({'Franchise':options.franchise,'storeID':options.storeID});
    //     this.writeLog(LOG_LEVEL.INFO, this.ctx_download, this.msg_download_started);
    //     util.saveSegment(`${options.status} RO Pull started`,segmentInfo);
    //     try {
    //         util.saveSegment(`Ready for ${options.status} ROs count `,segmentInfo);
    //         let count =await this.getROSCount(options);
    //         util.saveSegment(`${options.status} ROs have ${count} count `,segmentInfo);
    //         let start = 0;
    //         let end = 0;
    //         let limit = constants.API_PAGE_LIMIT;
    //         count = count == 0 ? 1:count;
    //         let totalLimit = Math.ceil(count/limit);
    //         for(let i=1;i<=totalLimit;i++){
    //             let offset = (i-1)*limit;  
    //             if(i==1){
    //                 start = offset;
    //             }
    //             else{
    //                 start = offset+1;
    //             }
    //             end = offset +limit;
    //             pageArray.push({start:start,end:end}); 
    //         }
    //         // Adam allows 1000 ROs per one request.
    //         await this.performROPullWithLimit(options);
    //     } catch (err) {
    //         this.handleError(err, options.dealerID);
    //     }
    //     return this.success;
    // }
    // 
    async execute(options) {
        console.log("excecuting>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        this.writeLog(LOG_LEVEL.INFO, this.ctx_download, this.msg_download_started);
        try {
            var res = await this.extractionCallMethod(options);
            console.log("Res########################################",res);
            console.log("Res########################################",res.isAccounting);
            console.log("requestHeader ??????????????????????????????????????????????????????//res",res.isAccounting);
            this.storeCapturedDataInTempFile(res, options.dealerID,options.startDate, options.endDate,res.isAccounting,options.glAccountCompanyID);
        } catch (err) {
            console.log("Customer ??????????????????????????????????????????????????????err",err);
            this.handleError(err, options.dealerID);
        }
        return this.success;
    }

    async performROPullWithLimit(options, resolveObjComplete){
        util.saveSegment(`Pagination  of ${options.status} RO pull is started`,segmentInfo);
        return new Promise(_resolve => {
          if(resolveObjComplete){
                util.saveSegment(`Pagination  of ${options.status} RO pull is completed`,segmentInfo);
                resolveObjComplete('completed');
          }
          else{
            this.paginateExtractionCallMethod(options, _resolve);
          }
        });    
    }
    
    async paginateExtractionCallMethod(options, _resolve){
        options.start = pageArray[loopIncrementer].start;
        options.end = pageArray[loopIncrementer].end;
        util.saveSegment(`Pagination  of ${options.status} RO pull start index:${options.start} , end index:${options.end}, loopIncrementer:${loopIncrementer}`,segmentInfo);
        var res = await this.extractionCallMethod(options);
        this.storeCapturedDataInTempFile(res, options.dealerID, loopIncrementer);
        loopIncrementer++;
        if(loopIncrementer >= pageArray.length){
            this.performROPullWithLimit(options, _resolve);   
        }
        else{
            this.paginateExtractionCallMethod(options, _resolve);
        }
    }
    

    storeCapturedDataInTempFile(res, dealerID,startDate1,startdate2,isAccounting,companyID) {
        try {
            let fileName = this.filename_base;
            // if(version){
            //     fileName = fileName+'.'+version
            // }
            console.log("isAccounting@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",isAccounting);
            if (isAccounting) {
                companyID = companyID ? companyID : '';
                console.log("The URL contains gl-coa.@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");

            this.writeLog(LOG_LEVEL.INFO, this.ctx_download, constants.messages.REQ_HEADER + res.requestHeader); // Request header log
            this.writeLog(LOG_LEVEL.INFO, this.ctx_download, constants.messages.RES_HEADER + responseHeaderToString(res.responseHeader)); // Response header log
            this.writeLog(LOG_LEVEL.INFO, this.ctx_download, this.msg_download_completed);
            fs.writeFileSync(this.output_foldername_temp + "/" + fileName +'_'+companyID+ '.json',res.result);
            fs.writeFileSync(this.output_foldername_temp + "/" + fileName +'_'+companyID+ '.req', buildRequestSummary(res.requestHeader));
            fs.writeFileSync(this.output_foldername_temp + "/" + fileName +'_'+companyID+ '.res', buildResponseSummary(res, this));
            // fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.time', buildTimingJSONString(res, this, dealerID));
            this.success = true;
            } else {
                console.log("The URL does not contain gl-coa!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!.");

            this.writeLog(LOG_LEVEL.INFO, this.ctx_download, constants.messages.REQ_HEADER + res.requestHeader); // Request header log
            this.writeLog(LOG_LEVEL.INFO, this.ctx_download, constants.messages.RES_HEADER + responseHeaderToString(res.responseHeader)); // Response header log
            this.writeLog(LOG_LEVEL.INFO, this.ctx_download, this.msg_download_completed);
            fs.writeFileSync(this.output_foldername_temp + "/" + fileName + '.json',res.result);
            fs.writeFileSync(this.output_foldername_temp + "/" + fileName + '.req', buildRequestSummary(res.requestHeader));
            fs.writeFileSync(this.output_foldername_temp + "/" + fileName + '.res', buildResponseSummary(res, this));
            // fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.time', buildTimingJSONString(res, this, dealerID));
            this.success = true;
            }
      
            // this.writeLog(LOG_LEVEL.INFO, this.ctx_download, constants.messages.REQ_HEADER + res.requestHeader); // Request header log
            // this.writeLog(LOG_LEVEL.INFO, this.ctx_download, constants.messages.RES_HEADER + responseHeaderToString(res.responseHeader)); // Response header log
            // this.writeLog(LOG_LEVEL.INFO, this.ctx_download, this.msg_download_completed);
            // fs.writeFileSync(this.output_foldername_temp + "/" + fileName + '.json',res.result);
            // fs.writeFileSync(this.output_foldername_temp + "/" + fileName + '.req', buildRequestSummary(res.requestHeader));
            // fs.writeFileSync(this.output_foldername_temp + "/" + fileName + '.res', buildResponseSummary(res, this));
            // // fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.time', buildTimingJSONString(res, this, dealerID));
            // this.success = true;
        } catch (chain_err) {
            this.writeLog(LOG_LEVEL.WARN, this.ctx_download, 'Data Writeout Failed' + ' (' + chain_err.message + ')');
            this.success = false;
        }
    }

    handleError(err, dealerID) {
        console.log("INSIDE GENERIC REQUEST HANDLE ERROR",err);
        try {
            // this.writeLog(LOG_LEVEL.WARN, this.ctx_download, this.msg_download_failed + ' (' + err.result + ')');
            if (err.Status) {
                let res = err;
                console.log("*******************888RES",res);
                //this.writeLog(LOG_LEVEL.ERROR, this.ctx_download, this.msg_download_failed + '; ' + err.message);
                fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.req', buildRequestSummary(res.requestHeader));
                fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.err', buildResponseSummary(res, this, false));
                // fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.time', buildTimingJSONString(res, this, dealerID));

                //while the request resulted in an error the processing is expecting it and has a non-failure mode
                //to handle it - as far as the caller is concerned while it wasn't successful it didn't fail either
                this.success = false;
            } else {
                console.log("Test5&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&");
                fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.req', buildRequestSummary(err.requestHeader));
                this.writeLog(LOG_LEVEL.WARN, this.ctx_download, this.msg_download_failed + ' (' + err.result + ')');

                fs.writeFileSync(this.output_foldername_temp + "/" + this.filename_base + '.err', err.result);
                this.success = false;
            }
        } catch (chain_err) {
            this.writeLog(LOG_LEVEL.WARN, this.ctx_download, 'Error Logging Failed' + ' (' + chain_err.message + ')');
            this.success = false;
        }
    }

    /**
     * Common function to handle the application and session log
     * @param logLevel
     * @param context
     * @param message
     */
    writeLog(logLevel, context, message) {

        context = context ? context.padEnd(constants.contexts.PADDING) + ': ' : '';
        if (logLevel == LOG_LEVEL.INFO) {
            console.log(logLevel + ': ', message);
            this.app_logger.info(context, message);
            this.session_logger.info(context, message);
        } else if (logLevel == LOG_LEVEL.WARN) {
            console.log(logLevel + ': ', message);
            this.app_logger.warn(context, message);
            this.session_logger.warn(context, message);
        } else if (logLevel == LOG_LEVEL.ERROR) {
            console.error(logLevel + ': ', message);
            this.app_logger.error(context + message);
            this.session_logger.error(context + message);
        }
    }
}

module.exports = GenericRequest;

function buildTimingJSONString(res, requestModel, dealerID) {
    return JSON.stringify({
        "Start": res.timingStart,
        "Total": Math.round(res.timingPhases.total),
        "First": Math.round(res.timingPhases.firstByte),
        "DLoad": Math.round(res.timingPhases.download),
        "Size": JSON.stringify(res.body).length,
        "Status": res.statusCode,
        "DealerID": dealerID,
        "FileType": requestModel.ctx_download,
        "HostName": res.request.uri.hostname
    });
}

function buildResponseSummary(res, requestModel, trimBody = true) {
    return 'Status: ' + (res.statusCode ? res.statusCode : 'No Status Code') + '\n' +
        'Length: ' + (res.result ? JSON.stringify(res.result).length : '<No Body>') + '\n\n' +
        responseHeaderToString(res.responseHeader) + '\n' +
        (trimBody ? '\n[Body (first ' + requestModel.leading_context_length + ' characters)]\n' : "[Body]\n") +
        (res.result ? (trimBody ? JSON.stringify(res.result).substring(0, requestModel.leading_context_length) : res.result) : '') + '\n' +
        '[End of Body]\n';
}

/**
 * Function to convert response object to string of new line separated key/value
 * @param {Object} respObject
 */
function responseHeaderToString(respObject) {
    let returnString = '[Begin Headers]';
    if (typeof respObject === 'string') {
        try {
            respObject = JSON.parse(respObject); // Attempt to parse JSON string
        } catch (e) {
            util.saveSegment('Error in JSON parse: ' + respObject);
        }
    }
    if (typeof respObject !== 'object' || respObject === null) {
        util.saveSegment('Invalid data type: ' + respObject);
    }
    Object.entries(respObject).forEach(([key, value]) => {
        returnString += '\n  ' + key + ": " + value; // You can modify this to JSON.stringify(value) if needed
    });
    returnString += '\n[End of Headers]';    
    return returnString;
}

function buildRequestSummary(req) {
    return req + '\n';
}
