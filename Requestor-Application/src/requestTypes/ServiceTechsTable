#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class ServiceTechsTable extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_STOD_DWNLD;
        this.msg_download_failed = constants.messages.STOD_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.STOD_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.SERVICE_TECH;
        this.ctx_download = constants.contexts.STOD_DWNLD;
        this.tag_header = constants.tags.OPENED_HEADER_TAG;
        this.extractionCallMethod = apiManager.performServiceTechsPull;
        this.leading_context_length = 115;
    }
}

module.exports = ServiceTechsTable;
