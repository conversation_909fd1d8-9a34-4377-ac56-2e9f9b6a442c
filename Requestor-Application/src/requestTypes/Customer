#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class Customer extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_CUST_DWNLD;
        this.msg_download_failed = constants.messages.CUST_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.CUST_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.CUSTOMER;
        this.ctx_download = constants.contexts.CUSTOMER_DWNLD;
        this.tag_header = constants.tags.CUSTOMER_HEADER_TAG;
        this.extractionCallMethod = apiManager.performCustomerPull;
        this.leading_context_length = 115;
    }
}

module.exports = Customer;
