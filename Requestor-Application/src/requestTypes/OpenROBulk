#!/usr/bin/env node

const GenericRequest = require('./GenericRequest');
const constants = require('../constants');
const apiManager = require('../apiManager');

class OpenROBulk extends GenericRequest {

    constructor(output_foldername) {
        super(output_foldername);
        this.msg_download_started = constants.messages.PRFM_OROD_DWNLD;
        this.msg_download_failed = constants.messages.PROD_DWNLD_FAILED;
        this.msg_download_completed = constants.messages.OROD_DWNLD_CMPLT;
        this.filename_base = constants.outputFileName.OPEN_RO;
        this.ctx_download = constants.contexts.OPENRO_BULK_DWNLD;
        this.tag_header = constants.tags.CUSTOMER_HEADER_TAG;
        this.extractionCallMethod = apiManager.performOpenROPull;
        this.leading_context_length = 115;
    }
}

module.exports = OpenROBulk;