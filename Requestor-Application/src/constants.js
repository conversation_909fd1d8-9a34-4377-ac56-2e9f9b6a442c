/**
 * Application constants
 */
const path = require("path");
const fs = require("fs");

/**
 * Configuration file (.env) path
 */
const ENV_PATH = path.join(process.env.HOME + "/.fortellis/.env"); // Hidden folder in user's home path
require("dotenv").config({ path: ENV_PATH });
if (!fs.existsSync(ENV_PATH)) {
    console.error(`Configuration file ${ENV_PATH} not accessible or exist; Please run  ../install script`);
    process.exit(1);
}

const TEMP_PATH = path.join(process.env.FORTELLIS_WORK_DIR + "/requestor-temp");
if (!fs.existsSync(TEMP_PATH)) {
    fs.mkdirSync(TEMP_PATH);
}
if (!fs.existsSync(TEMP_PATH + '/log')) {
    fs.mkdirSync(TEMP_PATH + '/log');
}
if (!fs.existsSync(TEMP_PATH + '/extract')) {
    fs.mkdirSync(TEMP_PATH + '/extract');
}

var constants = {
    ENV_PATH: ENV_PATH,
    TEMP_OUTPUT_PATH: TEMP_PATH + '/extract',
    LOG_OUTPUT_PATH: TEMP_PATH + '/log',
    CLI_DATE_FORMAT: "MM/DD/YYYY",
    API_PAGE_LIMIT:250,
    CLI_DATE_FORMAT_REGEXP: /^(|(0[1-9])|(1[0-2]))\/((0[1-9])|(1\d)|(2\d)|(3[0-1]))\/((\d{4}))$/g,
    START_DATE_PARAM_ERR: "--startDate is required",
    END_DATE_PARAM_ERR: "--endDate is required",
    PARTY_ID_ERR: "--partyID is required",
    DEALER_ID_ERR: "--dealerID is required",
    STOREUSERNAME_NO_ERR: "--storeUsername is required",
    STOREPASSWORD_NO_ERR: "--storePassword is required",
    ACTIVITY_STORE_ID_PARAM_ERR: "--activityStoreID is required",
    DATE_FORMAT_ERROR: "Date format error, required format",
    AT_LEAST_ONE_OPT_ERROR: "Either --all or any one of (--model|--customer|--open|--closed|--bundle) parameter should pass",
    FORTELLIS_ACCESS_KEY:process.env.FORTELLIS_ACCESS_KEY,
    FORTELLIS_SECRET_KEY:process.env.FORTELLIS_SECRET_KEY,
    FORTELLIS_CLIENT_ID:process.env.FORTELLIS_CLIENT_ID,
    FORTELLIS_TOKEN_FETCH_URL:process.env.FORTELLIS_TOKEN_FETCH_URL,
    CUSTOMER_DATA_URL:process.env.CUSTOMER_DATA_URL,
    FORTELLIS_UNIQUE_ID:process.env.FORTELLIS_UNIQUE_ID,
    CLI_CLOSED_FLAG: {
        MODE_ALL: "all",
        MODE_MONTHLY: "monthly",
        MODE_WEEKLY: "weekly"
    },
    INVALID_DATE: "Invalid Date",
    auth: {
        'access_key': process.env.FORTELLIS_ACCESS_KEY,
        'secret_key': process.env.FORTELLIS_SECRET_KEY
    },
    headers: {
        'User-Agent': 'Armatus Dealer Uplift (node request)',
        'Accept': '*/*'
    },
    version:{
        'GetRepairOrder' : process.env.GET_REPAIR_ORDER_VERSION
    },
    url:{
        'tokenEndPoint' : process.env.TOKEN_FETCH_URL,
        'GetRepairOrder' : process.env.REPAIR_ORDER_URL
    },
    contexts: {
        PADDING: 25, // Context message padding value for logging
        SC_ADDED: "STATIC_CONFIG_ADDED",
        SC_UPDATE: "STATIC_CONFIG_UPDATE",
        HROD_DWNLD: "HISTORYRO_DOWNLOAD",
        CUSTOMER_DWNLD: "CUSTOMER_DOWNLOAD",
        MAKE_MODEL_DWNLD:"MAKEMODEL_DOWNLOAD",
        GEN_ZIP: "GENERATE_ZIP",
        MKDIR_OUT_FLDR: "CREATE_OUTPUT_FOLDER",
        OROD_DWNLD: "OPEN_RO_DOWNLOAD",
        PROD_DWNLD: "PRINTED_RO_DOWNLOAD",
        RO_BULK_DWNLD: "ROBULK_DOWNLOAD",
        EMPLOYEE_DWNLD: "EMPLOYEE_DOWNLOAD",
        GL_ACCOUNTING_BULK_DWNLD: "GL_ACCOUNTING_DOWNLOAD",
        COA_BULK_DWNLD: "COA_DOWNLOAD",
        LABOR_DWNLD: "LABOR_DOWNLOAD",
        OPENRO_BULK_DWNLD: "OPENROBULK_DOWNLOAD",
        
    },
    messages: {
        REQ_HEADER: "Request Header\n",
        RES_HEADER: "Response Header\n",
        SC_ADDED: "New Static configuration saved",
        SC_UPDATE: "Static configuration update",
        GEN_ZIP_START: "Generating the output zip file",
        GEN_ZIP_END: "Output zip file successfully generated",
        GEN_ZIP_ERROR: "Generating the out zip file failed",
        MK_DIR_OUT: "Creating the output folder",
        MK_DIR_ERROR: "Creating the output folder failed",
        PRFM_OROD_DWNLD: "Performing Open Repair Order bulk download",
        OROD_DWNLD_CMPLT: "Open Repair Order bulk download completed",
        OROD_DWNLD_FAILED: "Open Repair Order bulk download failed",
        PRFM_PROD_DWNLD: "Performing Print Repair Order bulk download",
        PROD_DWNLD_CMPLT: "Print Repair Order bulk download completed",
        PROD_DWNLD_FAILED: "Print Repair Order bulk download failed",
        PRFM_STOD_DWNLD: "Performing Service tech table download",
        STOD_DWNLD_CMPLT: "Service tech table download completed",
        STOD_DWNLD_FAILED: "Service tech table download failed",
        PRFM_CUST_DWNLD: "Performing customer download",
        PRFM_EMP_DWNLD: "Performing employee download",
        PRFM_LABOR_DWNLD: "Performing labor download",
        CUST_DWNLD_FAILED: "Customer download failed",
        EMP_DWNLD_FAILED: "Employee download failed",
        LABOR_DWNLD_FAILED: "Labor download failed",
        CUST_DWNLD_CMPLT: "Customer data download completed",
        EMP_DWNLD_CMPLT: "Employee data download completed",
        LABOR_DWNLD_CMPLT: "Labor data download completed",
        PRFM_MAKE_DWNLD: "Performing make download",
        MAKE_DWNLD_FAILED: "Make download failed",
        MAKE_DWNLD_CMPLT: "Make data download completed",
        PRFM_RO_BULK_DWNLD: "Performing ro bulk download",
        RO_BULK_DWNLD_FAILED: "Ro bulk download failed",
        RO_BULK_DWNLD_CMPLT: "Ro bulk data download completed",
        PRFM_RO_BULK_DWNLD: "Performing ro bulk download",
        RO_BULK_DWNLD_FAILED: "Ro bulk download failed",
        RO_BULK_DWNLD_CMPLT: "Ro bulk data download completed",
        
        PRFM_GL_ACCOUNTING_BULK_DWNLD: "Performing gl accounting download",
        GL_ACCOUNTING_BULK_DWNLD_FAILED: "Gl Accounting bulk download failed",
        GL_ACOUNTING_BULK_DWNLD_CMPLT: "Gl Accounting bulk data download completed",
        PRFM_GL_ACCOUNTING_BULK_DWNLD: "Performing gl accounting bulk download",
        GL_ACCOUNTING_BULK_DWNLD_FAILED: "gl accounting bulk download failed",
        GL_ACCOUNTING_BULK_DWNLD_CMPLT: "Gl Accounting bulk data download completed",

        COA_BULK_DWNLD_FAILED: "Ro bulk download failed",
        COA_DWNLD_CMPLT: "Ro bulk data download completed",
        PRFM_COA_BULK_DWNLD: "Performing ro bulk download",
        COA_BULK_DWNLD_FAILED: "Ro bulk download failed",
        COA_BULK_DWNLD_CMPLT: "Ro bulk data download completed",

        GL_DELTA_DWNLD_FAILED: "GL bulk download failed",
        GL_DELTA_DWNLD_CMPLT: "GL bulk data download completed",
        PRFM_GL_DELTA_BULK_DWNLD: "Performing gl delta download",
        GL_DELTA_BULK_DWNLD_FAILED: "Gl delta download failed",
        GL_DELTA_DWNLD_CMPLT: "Gl bulk data download complete"
        },
    tags: {
        CUSTOMER_HEADER_TAG: "<Customer>",
        OPENED_HEADER_TAG: "<service-repair-order-opeed>",
    },
    url:{
        CUSTOMER: {
            LONG_OPERATIONS: process.env.API_BASE_URL+"/customer/"+process.env.API_VERSION_V2+"/long-operations",
            DELTA: process.env.API_BASE_URL+"/customer/"+process.env.API_VERSION+"/delta?startDate=",
            BULK: process.env.API_BASE_URL+"/customer/"+process.env.API_VERSION_V2+"/bulk",

          },
          SERVICEREPAIRORDERSETUP: {
            HISTORY: process.env.API_BASE_URL+"/servicerepairordersetup/"+process.env.API_VERSION_V2+"/history?startDate=",
            LONG_OPERATIONS: process.env.API_BASE_URL+"/servicerepairordersetup/"+process.env.API_VERSION_V2+"/long-operations",
            
          },
          MAKEMODEL: {
            DELTA: process.env.API_BASE_URL+"/makemodel/"+process.env.API_VERSION+"/delta?startDate=",
            LONG_OPERATIONS: process.env.API_BASE_URL+"/makemodel/"+process.env.API_VERSION+"/long-operations",
            BULK: process.env.API_BASE_URL+"/makemodel/"+process.env.API_VERSION_V2+"/bulk",          
          },
          SERVICEREPAIRORDER: {
            OPEN: process.env.API_BASE_URL+"/servicerepairorder/"+process.env.API_VERSION+"/open",
            CLOSED: process.env.API_BASE_URL+"/servicerepairorder/"+process.env.API_VERSION+"/closed",
            LONG_OPERATIONS: process.env.API_BASE_URL+"/servicerepairorder/"+process.env.API_VERSION+"/long-operations"
          },
          GL_BALANCE: {
            JOURNAL_ENTRY_GL_DETAIL: process.env.API_BASE_URL+"/gl-balance/"+process.env.API_VERSION_V2+"/journal-entry-gl-detail/accounting-date-range",
            GL_COA_BULK: process.env.API_BASE_URL+"/gl-balance/"+process.env.API_VERSION_V2+"/gl-coa/bulk"
          },
          SERVICE_LABOR_TYPE: {
            BULK: process.env.API_BASE_URL+"/service-labor-type/"+process.env.API_VERSION_V2+"/bulk",
            LONG_OPERATIONS: process.env.API_BASE_URL+"/service-labor-type/"+process.env.API_VERSION_V2+"/long-operations"
          },
          EMPLOYEE: {
            BULK: process.env.API_BASE_URL+"/employee/"+process.env.API_VERSION_V2+"/bulk",
            LONG_OPERATIONS: process.env.API_BASE_URL+"/employee/"+process.env.API_VERSION_V2+"/long-operations"
          }
          
    },
    apiTypes: {
        SERVICE_TECH_PULL:"serviceTech",
        OPEN_RO_PULL: "openRO",
        CLOSED_RO_PULL:"allClosedRO",
        PRINTED_RO_PULL: "printedRO",
        OPEN_RO_COUNT: "openROCount",
        CLOSED_RO_COUNT:"allClosedROCount",
        PRINTED_RO_COUNT: "printedROCount"
    },
    wsdlOptions: {
        "overrideRootElement": {
            "namespace": "tem",
            "xmlnsAttributes": [{
                "name": "xmlns:tem",
                "value": "http://tempuri.org/"
            }
            ]
        }
    },
    wsdlOptionsForChartAccountDetail: {
        "overrideRootElement": {
            "namespace": "tran",
            "xmlnsAttributes": [{
                "name": "xmlns:tran",
                "value": "opentrack.dealertrack.com/transitional"
            }
            ]
        }
    },
    outputFileName: {
        HISTORY_RO: "ROs",
        OPEN_RO: "OpenRO",
        PRINTED_RO:"PrintedRO",
        SERVICE_TECH:"ServiceTech",
        DEALER_ADDRESS:"DealerAddress",
        CUSTOMER:"customer",
        MAKE:"make",
        GL_DELTA:"AccountingGLDetails",
        GL_COA:"AccountingCOA",
        ROBULK:'ROs_Bulk',
        EMPLOYEE:"employee",
        GL_ACCOUNTING:'accounting',
        LABOR:'LaborType'
        
    },
    STATUS_CODE: {
        SUCCESS: 0,
        GENERAL_DEATH: 1,
        DEADLETTER_PATH: 2
    }
}

module.exports = constants;
