#!/usr/bin/env node

'use strict';

const fs = require('fs');
const zipManager = require('./zipManager');
const apiManager = require('./apiManager');
const logger = require('./logManager');
const constants = require('./constants');
const util = require('./util');
const path = require('path');
const dayjs = require('dayjs');

const moment = require("moment-timezone");
const LOGLEVEL = logger.LOGLEVEL;
const appLogger = logger.getApplicationLogger;
const generateAuthTokenController = require("./generateAuthToken");
var segmentInfo;
// Import request types

const GetRepairOrderRequest = require("./requestTypes/RepairOrderRequest");
const CustomerRequest = require('./requestTypes/Customer');
const MakeModelRequest = require('./requestTypes/MakeModel');
const CoaRequest = require('./requestTypes/CoaRequest');
const { generateEnvelope } = require('./envelope');
const OpenRepairOrderRequest = require("./requestTypes/OpenRepairOrderRequest");
const GetGLRequest = require("./requestTypes/GlRequest");
const EmployeeRequest = require("./requestTypes/Employee");
const ROBULKREQUEST = require('./requestTypes/RoBulk')
const OPENROBULKREQUEST = require('./requestTypes/OpenROBulk')
const GLACCOUNTINGREQUEST = require('./requestTypes/GLAccountingRequest')
const LaborRequest = require("./requestTypes/Labor");
const agendaDB = require("./model/agendaDB");
const generateAuthToken = async () =>{
   let authToken = await generateAuthTokenController.generateAuthToken();
   if(authToken.status){
      return authToken.data.data.access_token;
   }
}


/**
 * Function to write application and session logs (optional)
 *
 * @param {LOGLEVEL} logLevel - info or error
 * @param {string} message - Log message
 * @param {string} context - Context in which the log belongs to
 * @param {boolean} needSessionLog - To print a separate session log or not
 */
function writeLog(logLevel, message, context, needSessionLog) {
    if (logLevel == LOGLEVEL.INFO) {
        appLogger.info(context.padEnd(constants.contexts.PADDING) + ': ', message);
        console.log(logLevel + ': ', message);
    } else if (logLevel == LOGLEVEL.ERROR) {
        appLogger.error(context.padEnd(constants.contexts.PADDING) + ': ' + message);
        console.error(logLevel + ': ', message);
        //process.kill(process.pid, 'SIGINT'); // don't like it but being true to the merged in code
    }
}

/**
 * Remove directory recursively
 * @param {string} dir_path
 * @see https://stackoverflow.com/a/42505874/3027390
 */
function rimraf(dir_path) {
    if (fs.existsSync(dir_path)) {
        fs.readdirSync(dir_path).forEach(function (entry) {
            var entry_path = path.join(dir_path, entry);
            if (fs.lstatSync(entry_path).isDirectory()) {
                rimraf(entry_path);
            } else {
                fs.unlinkSync(entry_path);
            }
        });
        fs.rmdirSync(dir_path);
    }
}

/**
 * Function save the static configurations values to the .env file
 *
 * @param {object} options - { baseURL: <value>, vendorID: <value>, vendorPassword: <value>}
 */
function doConfig(options) {
    if (options.baseURL && options.vendorID && options.vendorPassword) {
        writeLog(LOGLEVEL.INFO, constants.messages.SC_UPDATE, constants.contexts.SC_UPDATE);

        var result = require('dotenv').config({ path: constants.ENV_PATH });
        if (result.error) {
            throw result.error;
        }
        var env = '';
        env += 'URL_PIP_BASE=' + options.baseURL + '\n';
        env += 'VENDOR_ID=' + options.vendorID + '\n';
        env += 'VENDOR_PASSWORD=' + options.vendorPassword + '\n';
        if (fs.existsSync(constants.ENV_PATH)) {
            var data = fs.readFileSync(constants.ENV_PATH);
            if (data.length > 0) {
                // The env file we just read in contains the desired keys if they are presently in our environment
                if (process.env.DEALERTRACK_URL_PIP_BASE) {
                    var newEnvValues = data.toString();
                    newEnvValues = newEnvValues.replace(`DEALERTRACK_URL_PIP_BASE=${process.env.DEALERTRACK_URL_PIP_BASE}`, `DEALERTRACK_URL_PIP_BASE=${options.baseURL}`);
                    newEnvValues = newEnvValues.replace(`DEALERTRACK_VENDOR_ID=${process.env.DEALERTRACK_VENDOR_ID}`, `DEALERTRACK_VENDOR_ID=${options.vendorID}`)
                    newEnvValues = newEnvValues.replace(`DEALERTRACK_VENDOR_PASSWORD=${process.env.DEALERTRACK_VENDOR_PASSWORD}`, `DEALERTRACK_VENDOR_PASSWORD=${options.vendorPassword}`)
                    fs.writeFileSync(constants.ENV_PATH, newEnvValues);
                } else {
                    fs.appendFileSync(constants.ENV_PATH, env);
                }
            }
        } else {
            /**
             * save the static configuration to .env file
             */
            fs.writeFileSync(constants.ENV_PATH, env);
        }
        writeLog(LOGLEVEL.INFO, constants.messages.SC_ADDED, constants.contexts.SC_ADDED);
        process.exit(0);
    } else {
        process.exit(1);
    }
}


async function existTempPathOrDie(performReset, tempStoreFolder) {

    var tempDir = path.resolve(constants.TEMP_OUTPUT_PATH);
    var tempStoreFolder = path.join(tempDir, tempStoreFolder);
    try {
        if (fs.existsSync(tempStoreFolder) && performReset) {
            rimraf(tempStoreFolder);
        }
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir)
        }

    } catch (error) {
        writeLog(LOGLEVEL.ERROR, constants.contexts.MKDIR_OUT_FLDR, constants.messages.MK_DIR_ERROR + '; Error message: ' + error.message);
        process.exit(constants.STATUS_CODE.GENERAL_DEATH);
    }
    return tempDir;
}

/**
 * Start the API calls and create the zip file
 * @param {object} options -
 */
async function doAPICall(options) {
     console.log("test1@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
     await agendaDB.createStructure(options.extractionID,options.dealerID,options.glAccountCompanyID);

    segmentInfo = JSON.stringify({'Dealer ID':options.dealerID});  
    util.saveSegment('RO Pull started',segmentInfo);
    var tempDir = await existTempPathOrDie(options.reset, options.workingName);
    // Create a folder inside tempDir to save the extractions.
    // User can override the folder name too.
    var tempOutputPath = path.join(tempDir, options.workingName);

    if (fs.existsSync(tempOutputPath) && options.reset) {
        rimraf(tempOutputPath);
    }
    if (!fs.existsSync(tempOutputPath)) {
        fs.mkdirSync(tempOutputPath);
    }

    options.tempOutputPath = tempOutputPath;
    var sessionLogger = logger.getSessionLogger('Session-' + options.storeID, tempOutputPath);
    var extractionHasErrors = false;
    if (options.bundle) {
        extractionHasErrors = await extractBundle(options, sessionLogger);
    } else {
        extractionHasErrors = await extractOne(options, sessionLogger);
    }
    await distributeAndClean(options, tempOutputPath, extractionHasErrors);
    return extractionHasErrors;

}


async function distributeAndClean(options, workDir, extractionHasErrors) {
    var okToZap = false;
    if (extractionHasErrors) {
        okToZap = await distributeToDeadLetter(options, workDir)
        cleanPerOptions(options, okToZap, workDir);
    } else if (options.outputDir || options.zipPath) {
        okToZap = await distributePerOptions(options, workDir);
        cleanPerOptions(options, okToZap, workDir);
    } else {
        console.log("Leaving Results in Working Directory Since No Output Directories Are Specified")
    }
    return okToZap;
}

async function distributePerOptions(options, workDir) {
    if (options.zipPath) {
        return await zipToFromWithName(options.zipPath, workDir, options.workingName);
    } else if (options.outputDir) {
        return await zipToFromWithName(options.outputDir, workDir, options.workingName);
    } else {
        console.error("Please ensure either zipPath or outputDir is set");
        process.exit(constants.STATUS_CODE.GENERAL_DEATH); //should never happen
    }
}

async function distributeToDeadLetter(options, workDir) {
    return await zipToFromWithName(options.deadLetter, workDir, options.workingName);
}

async function zipToFromWithName(targetDirectoryForZipFile, workDir, targetFileName) {
    var zipCompletedOK = false;
    var _zipPath = path.join(targetDirectoryForZipFile, targetFileName);
    _zipPath = _zipPath.endsWith(".zip") ? _zipPath : _zipPath + ".zip";
    await zipManager.generateZip(workDir, _zipPath)
        .then(() => {
            writeLog(LOGLEVEL.INFO, constants.messages.GEN_ZIP_END + ' @path: ' + _zipPath,
                constants.contexts.GEN_ZIP, true);
            zipCompletedOK = true;
        })
        .catch((err) => {
            writeLog(LOGLEVEL.ERROR, constants.messages.GEN_ZIP_ERROR + '; Error message: ' + err.message,
                constants.contexts.GEN_ZIP, true);
        });
    return zipCompletedOK;
}

function cleanPerOptions(options, okToZap, workDir) {
    if (okToZap === true && options.zapAfterDist) {
        console.log("Zapping Per Request: " + workDir);
        if (fs.existsSync(workDir)) {
            rimraf(workDir)
        } else {
            console.log("Working Directory No Longer Exists..." + workDir);
        }
    } else {
        console.log("Leaving Working Files: okToZap: " + okToZap);
    }
}

async function extractOne(options) {
    util.saveSegment('perform extractOne',segmentInfo);
    var executionResolvedOK = false;
    executionResolvedOK = await performAPICall({}, options, sessionLogger);
    return !executionResolvedOK;
}

async function extractBundle(options, sessionLogger) {
    util.saveSegment('perform extractBundle',segmentInfo);
    var sendToDeadLetter = false;
    let token = await generateAuthToken();
    options.token = token;
    if (options.bundle && options.bundle === 'initial') {
        util.saveSegment('bundle type is initial', segmentInfo);
        let currentDate = dayjs();  
        let date31DaysAgo = currentDate.subtract(31, 'day');

        let PULL_RO_BULK_ONLY = false;
        let PULL_RO_DELTA = false;
        let PULL_RO_BULK = false;
        let PULL_RO_DELTA_STARTDATE = null;
        let PULL_RO_DELTA_ENDATE = null;
        let PULL_RO_BULK_STARTDATE = null;
        let PULL_RO_BULK_ENDATE = null;
        let startDate = dayjs(options.startDate, 'MM/DD/YYYY');
        let endDate = dayjs(options.endDate,'MM/DD/YYYY');
        
        if (startDate.isAfter(date31DaysAgo) && endDate.isBefore(currentDate)) {
            console.log(`Both start date and end date are within the last 31 days.`);
            console.log(`Start Date: ${startDate.format('MM/DD/YYYY')}`);
            console.log(`End Date: ${endDate.format('MM/DD/YYYY')}`);
            PULL_RO_BULK_STARTDATE = startDate.format('MM/DD/YYYY');
        
            PULL_RO_BULK_ENDATE = endDate.format('MM/DD/YYYY');
            PULL_RO_BULK_ONLY = true;
        } else if (startDate.isBefore(date31DaysAgo) && endDate.isBefore(date31DaysAgo)) {
            console.log(`Both start date and end date are outside the last 31 days.`);
            console.log(`Delta Start: ${startDate.format('MM/DD/YYYY')}`);
            console.log(`Delta End: ${endDate.format('MM/DD/YYYY')}`);
            PULL_RO_DELTA = true;
            PULL_RO_DELTA_STARTDATE = startDate.format('MM/DD/YYYY');
            PULL_RO_DELTA_ENDATE = endDate.format('MM/DD/YYYY');
        } else {
            let insideRangeStart = startDate.isAfter(date31DaysAgo) ? startDate : date31DaysAgo;
            let insideRangeEnd = endDate.isBefore(currentDate) ? endDate : currentDate;
            let outsideRangeStart = startDate.isBefore(date31DaysAgo) ? startDate : null;
            let outsideRangeEnd = endDate.isAfter(currentDate) ? endDate : null;
        
            if (insideRangeStart.isAfter(date31DaysAgo) || insideRangeEnd.isBefore(currentDate)) {
                console.log(`Inside the last 31 days range:`);
                let nextDay = insideRangeStart.add(0, 'day');
                console.log(`Start: ${nextDay.format('MM/DD/YYYY')} End: ${insideRangeEnd.format('MM/DD/YYYY')}`);
                PULL_RO_BULK_STARTDATE = nextDay.format('MM/DD/YYYY');
                PULL_RO_BULK_ENDATE = insideRangeEnd.format('MM/DD/YYYY');
                PULL_RO_BULK = true;
            }
        
            if (outsideRangeStart) {
                console.log(`Outside the 31 days range (before):`);
                console.log(`Start: ${outsideRangeStart.format('MM/DD/YYYY')} End: ${date31DaysAgo.format('MM/DD/YYYY')}`);
                PULL_RO_DELTA = true;
                PULL_RO_DELTA_STARTDATE = outsideRangeStart.format('MM/DD/YYYY');
                PULL_RO_DELTA_ENDATE = date31DaysAgo.format('MM/DD/YYYY');
            }
        
            if (outsideRangeEnd) {
                console.log(`Outside the 31 days range (after):`);
                console.log(`Start: ${currentDate.format('MM/DD/YYYY')} End: ${outsideRangeEnd.format('MM/DD/YYYY')}`);
                PULL_RO_BULK = true;
            }
        }
        
        if (PULL_RO_DELTA) {
            console.log("Delta start and end date:", PULL_RO_DELTA_STARTDATE, PULL_RO_DELTA_ENDATE);
            let STARTDATE_TEMP = options.startDate;
            let ENDDATE_TEMP = options.endDate;
            options.startDate = PULL_RO_DELTA_STARTDATE;
            options.endDate = PULL_RO_DELTA_ENDATE

             sendToDeadLetter = await performAPICall({ closed: true }, options, sessionLogger)? sendToDeadLetter : true;
             options.closed = false;

             options.startDate = STARTDATE_TEMP;
             options.endDate = ENDDATE_TEMP;
        
        }
        
        if (PULL_RO_BULK) {
            console.log("Bulk start and end date:", PULL_RO_BULK_STARTDATE, PULL_RO_BULK_ENDATE);
             
            let STARTDATE_TEMP = options.startDate;
            let ENDDATE_TEMP = options.endDate;

            options.startDate = PULL_RO_BULK_STARTDATE;
            options.endDate=PULL_RO_BULK_ENDATE;

            sendToDeadLetter = await performAPICall({ roBulk: true }, options, sessionLogger)? sendToDeadLetter : true;
            options.roBulk = false;


            options.startDate = STARTDATE_TEMP;
            options.endDate = ENDDATE_TEMP;
        }
        if(PULL_RO_BULK_ONLY){

            let STARTDATE_TEMP = options.startDate;
            let ENDDATE_TEMP = options.endDate;

            console.log("Bulk start and end date:", PULL_RO_BULK_STARTDATE, PULL_RO_BULK_ENDATE);
            options.startDate = PULL_RO_BULK_STARTDATE;
            options.endDate=PULL_RO_BULK_ENDATE;
            
            sendToDeadLetter = await performAPICall({ roBulk: true }, options, sessionLogger)? sendToDeadLetter : true;
            options.roBulk = false;


            options.startDate = STARTDATE_TEMP;
            options.endDate = ENDDATE_TEMP;
        } 
       
        console.log("OPTIONS.glAccountCompanyID@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",options.glAccountCompanyID);

    //    sendToDeadLetter = await performAPICall({ glDelta: true }, options, sessionLogger);
         sendToDeadLetter = await performAPICall({ customer: true }, options, sessionLogger) ? sendToDeadLetter : true;
         options.customer = false;
        // sendToDeadLetter = await performAPICall({ customerDelta: true }, options, sessionLogger);
        // sendToDeadLetter = await performAPICall({ makeModel: true }, options, sessionLogger);   
         sendToDeadLetter = await performAPICall({ employee: true }, options, sessionLogger);
         options.employee = false;
        sendToDeadLetter = await performAPICall({ labor: true }, options, sessionLogger);
        options.labor = false;
        // sendToDeadLetter = await performAPICall({ historyRo: true }, options, sessionLogger);
        sendToDeadLetter = await performAPICall({ open: true }, options, sessionLogger);
        options.open = false;
        if (options.glAccountCompanyID && options.glAccountCompanyID != undefined) {
            var myArray = options.glAccountCompanyID.split("-");
            for (let i = 0; i < myArray.length; i++) {
                //  sendToDeadLetter = await performAPICall({ glaccounting: true }, options, sessionLogger);
                // options.glAccountCompanyID = myArray[i];
                sendToDeadLetter = await performAPICall({ glDelta: true }, options, sessionLogger);
                options.glDelta = false;
                sendToDeadLetter = await performAPICall({ coaData: true }, options, sessionLogger);
                options.coaData = false;
            }
        }      
    } else if (options.bundle && options.bundle === 'refresh') {
        util.saveSegment('bundle type is refresh', segmentInfo);
        sendToDeadLetter = await performAPICall({ closed: true }, options, sessionLogger);
        // sendToDeadLetter = await performAPICall({ open: true }, options, sessionLogger);
        var currentDate = moment().tz(process.env.DEALERTRACK_EXTRACT_ZONE);
        options.endDate = currentDate.format("MM/DD/YYYY");
        options.startDate = moment(currentDate).subtract(31, "days").format("MM/DD/YYYY");
        sendToDeadLetter = await performAPICall({ closed: "current" }, options, sessionLogger);

    }
    return !sendToDeadLetter;
}

function setRepairOrderStatus(options) {
    var status = 'Open';
    if (options.closed) {
        status = 'Closed';
    }
    return status;
}

/**
 * Function to pick exact template for performing API call
 * @param {object} options -
 */
async function performAPICall(additionalOptions, options, sessionLogger) {

    util.saveSegment('Perform performAPICall', segmentInfo);
    const commonOptionsObject = {
        storeUsername: options.storeUsername,
        storePassword:options.storePassword,
        StoreID: options.storeID,
        Franchise: options.franchise,
        startDate: options.startDate,
        endDate: options.endDate,
        tempOutputPath: options.tempOutputPath,
        dealerID: options.dealerID,
        // partyID:  options.partyID,
        token: options.token
    }

    options = Object.assign({}, options, additionalOptions);
    // options.customer =true
    let status = {};
    // if (options.open || options.closed) {
    //     status = { status: setRepairOrderStatus(options) };
    // }
    options = Object.assign({}, options, commonOptionsObject, status);
    options = Object.assign({}, options, additionalOptions);
    options = Object.assign({}, options, commonOptionsObject);

    var tempOutputPath = options.tempOutputPath;
    var requestCommand = null;    
/*     if (options.open) {      
        util.saveSegment('Ready for  Open RO Pull', segmentInfo);
        requestCommand = new OpenRepairOrderRequest(tempOutputPath);
    } else */ if (options.closed) {
        console.log("Ready for Closed RO Pull");
        util.saveSegment('Ready for  Closed RO Pull', segmentInfo);
        requestCommand = new GetRepairOrderRequest(tempOutputPath);
    }
    else if (options.glDelta) {
        console.log("options********************************",options);
        console.log("Ready for  GL Pull");
        util.saveSegment('Ready for  GL Pull', segmentInfo);
        requestCommand = new GetGLRequest(tempOutputPath);
    }
    else if (options.historyRo) {
        console.log("options********************************",options);
        console.log("Ready for history RO Pull");
        util.saveSegment('Ready for  history RO Pull', segmentInfo);
        requestCommand = new GetRepairOrderRequest(tempOutputPath);
    }
    else if (options.customer) {
        console.log("Ready for Customer  Pull");
        util.saveSegment('Ready for  Customer Pull', segmentInfo);
        requestCommand = new CustomerRequest(tempOutputPath);
    }
    else if (options.makeModel) {
        console.log("Ready for makeModel Pull");
        util.saveSegment('Ready for  makeModel Pull', segmentInfo);
        requestCommand = new MakeModelRequest(tempOutputPath);
    }
    else if (options.customerDelta) {
        console.log("Ready for customer delta Pull");
        util.saveSegment('Ready for  customer deltra  Pull', segmentInfo);
        requestCommand = new MakeModelRequest(tempOutputPath);
    }
    else if (options.coaData) {
        console.log("Ready for COA data Pull");
        util.saveSegment('Ready for  coa data Pull', segmentInfo);
        requestCommand = new CoaRequest(tempOutputPath);
    }
    else if (options.employee) {
        console.log("Ready for employee data Pull");
        util.saveSegment('Ready for  coa data Pull', segmentInfo);
        requestCommand = new EmployeeRequest(tempOutputPath);
    } else if (options.labor) {
        console.log("Ready for labor data Pull");
        util.saveSegment('Ready for  labor data Pull', segmentInfo);
        requestCommand = new LaborRequest(tempOutputPath);
    } 

    if (options.roBulk) {
        console.log("Ready for Ro Bulk data Pull");
        util.saveSegment('Ready for  Ro bulk data Pull', segmentInfo);
        requestCommand = new ROBULKREQUEST(tempOutputPath);
    } else if (options.open) {
        console.log("Ready for OPEN Ro Bulk data Pull");
        util.saveSegment('Ready for  Ro bulk data Pull', segmentInfo);
        requestCommand = new OPENROBULKREQUEST(tempOutputPath);
    }
    else if (options.glaccounting) {
        console.log("Ready for GL ACCOUNTING Bulk data Pull");
        util.saveSegment('Ready for  Gl Accounting bulk data Pull', segmentInfo);
        requestCommand = new GLACCOUNTINGREQUEST(tempOutputPath);
    }
    if (requestCommand) {
        requestCommand.setAppLogger(appLogger);
        requestCommand.setSessionLogger(sessionLogger);
        var executionResolvedOK = await requestCommand.execute(options);
        return executionResolvedOK;
    } else {
        writeLog(LOGLEVEL.ERROR, "No Request Command Selected", "PULL", true)
        return false;
    }
}

module.exports = {
    doConfig: doConfig,
    doAPICall: doAPICall
};
