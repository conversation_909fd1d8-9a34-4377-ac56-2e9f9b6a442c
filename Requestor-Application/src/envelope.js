const constants = require('../src/constants');
function generateEnvelope(options, type) {
    var args = {
        "tem:message": {
            "Message": {
                "MessageType": "DataAccess",
                "Header": {
                    "RequestingCompany": {
                        "Name": process.env.ADAM_VENDOR_ID,
                        "Password": process.env.ADAM_VENDOR_PASSWORD
                    }
                },
                "Body": {
                    "Action": "",
                    "User": {
                        "AdamId": options.storeID,
                        "Franchise": options.franchise,
                        "AdamUser": options.storeUsername,
                        "AdamPass": options.storePassword
                    },
                    "APIVersion": "3.82",
                    "SearchParms":""
                }
            }
        }
    }
    if(type==constants.apiTypes.OPEN_RO_PULL){
      args["tem:message"].Message.Body.SearchParms = `<![CDATA[(status="O") AND (DateIn >= {^${options.startDate}} AND DateIn <= {^${options.endDate}}) AND BETWEEN(RECNO, ${options.start}, ${options.end})]]>`;
      args["tem:message"].Message.Body.Action = 'GetRO';
    }
    else if(type==constants.apiTypes.CLOSED_RO_PULL){
      args["tem:message"].Message.Body.SearchParms = `<![CDATA[(status="C") AND (DateOut >= {^${options.startDate}} AND DateOut <= {^${options.endDate}}) AND BETWEEN(RECNO, ${options.start}, ${options.end})]]>`;
      args["tem:message"].Message.Body.Action = 'GetRO';
    }
    else if(type==constants.apiTypes.PRINTED_RO_PULL){
      args["tem:message"].Message.Body.SearchParms = `<![CDATA[(status="P") AND (DateIn >= {^${options.startDate}} AND DateIn <= {^${options.endDate}}) AND BETWEEN(RECNO, ${options.start}, ${options.end})]]>`;
      args["tem:message"].Message.Body.Action = 'GetRO';
    }
    else if(type==constants.apiTypes.OPEN_RO_COUNT){
      args["tem:message"].Message.Body.SearchParms = `<![CDATA[(status="O") AND (DateIn >= {^${options.startDate}} AND DateIn <= {^${options.endDate}})]]>`;
      args["tem:message"].Message.Body.Action = 'GetRORecCount';
    }
    else if(type==constants.apiTypes.CLOSED_RO_COUNT){
      args["tem:message"].Message.Body.SearchParms = `<![CDATA[(status="C") AND (DateOut >= {^${options.startDate}} AND DateOut <= {^${options.endDate}})]]>`;
      args["tem:message"].Message.Body.Action = 'GetRORecCount';
    }
    else if(type==constants.apiTypes.PRINTED_RO_COUNT){
      args["tem:message"].Message.Body.SearchParms = `<![CDATA[(status="P") AND (DateIn >= {^${options.startDate}} AND DateIn <= {^${options.endDate}})]]>`;
      args["tem:message"].Message.Body.Action = 'GetRORecCount';
    }
    
  return args;
}

module.exports = {
  generateEnvelope: generateEnvelope
};
