#!/usr/bin/env node
'use strict';
const constants = require('./constants');
const soap = require('soap');
var Spinner = require('cli-spinner').Spinner;
var spinner = new Spinner('processing.. %s');
spinner.setSpinnerString('|/-\\');
const util = require('./util');
// const serviceTech = require('../src/pullRO/serviceTechsPull');
const openRO= require('./pullRO/openROPull')
const getRepairOrder = require('./pullRO/getRepairOrder'); 
const getGlData = require('./pullRO/getGlData'); 
const customer = require('./pullRO/customer');
const employee = require('./pullRO/getEmployee');
const make = require('./pullRO/makeModel');
const getCoa = require('./pullRO/getCoa');
const robulk = require('./pullRO/getRoBulk');
const labor = require('./pullRO/getLabor');
const GLAcounting = require('./pullRO/GLAccounting');

var segmentInfo;


async function pullROdata(options, errFileInfo) {
    return new Promise(async(resolve,reject)=>{
        util.saveSegment('executing pullCloseROdata',segmentInfo);
        try{
            let res = await getRepairOrder.pullRO(options, '', errFileInfo);
            if(res.status){
               resolve(res);
            }else{
                reject(res);
            }
            
        }catch(err){
            reject(err);
        }
    })
  
    // return await getRepairOrder.pullRO(options, '', errFileInfo);  
}

async function pullGldata(options, errFileInfo) {
    util.saveSegment('executing pullCloseROdata',segmentInfo);
    return await getGlData.pullGl(options, '', errFileInfo);  
}

async function pullCustomerDeltadata(options, errFileInfo) {
    util.saveSegment('executing pullCloseROdata',segmentInfo);
    return await getRepairOrder.pullRO(options, '', errFileInfo);  
}


async function pullHistoryROdata(options, errFileInfo) {
    util.saveSegment('executing pullCloseROdata',segmentInfo);
    return await getRepairOrder.pullRO(options, '', errFileInfo);  
}


async function pullOpenROdata(options, errFileInfo) {
    util.saveSegment('executing pullROdata',segmentInfo);
    return await openRO.pullOpenRO(options);
}

async function pullOpenGLdata(options, errFileInfo) {
    util.saveSegment('executing pullGLdata',segmentInfo);
    getGlData.pullGl(options);
}

async function performROPullWithDateRange(options, errFileInfo) {
    segmentInfo = JSON.stringify({'StoreID':options.storeID,'Franchise':options.franchise}); 
    util.saveSegment('executing performROPullWithDateRange',segmentInfo);
    return pullROdata(options, errFileInfo);
}

function performGlPullWithDateRange(options, errFileInfo) {
    segmentInfo = JSON.stringify({'StoreID':options.storeID,'Franchise':options.franchise}); 
    util.saveSegment('executing performGlPullWithDateRange',segmentInfo);
    return pullGldata(options, errFileInfo);
}

function performCustomerPullWithDateRange(options, errFileInfo) {
    segmentInfo = JSON.stringify({'StoreID':options.storeID,'Franchise':options.franchise}); 
    util.saveSegment('executing performROPullWithDateRange',segmentInfo);
    return pullCustomerDelta(options, errFileInfo);
}

function performHistoryROPullWithDateRange(options, errFileInfo) {
    segmentInfo = JSON.stringify({'StoreID':options.storeID,'Franchise':options.franchise}); 
    util.saveSegment('executing performROPullWithDateRange',segmentInfo);
    return pullROdata(options, errFileInfo);
}


async function performROPull(options, errFileInfo) {
    segmentInfo = JSON.stringify({'CompanyNumber':options.companyNumber,'dealerId':options.dealerId}); 
    util.saveSegment('executing performOpenROPull',segmentInfo);
    if (options.startDate) {
        options.startDate = new Date(options.startDate).toISOString();
    }
    if (options.endDate) {
        options.endDate = new Date(options.endDate).toISOString();
    }
    return pullOpenROdata(options, errFileInfo);
}

async function performGLPull(options, errFileInfo) {

    segmentInfo = JSON.stringify({'CompanyNumber':options.companyNumber,'dealerId':options.dealerId}); 
    util.saveSegment('executing performOpenROPull',segmentInfo);
    if (options.startDate) {
        options.startDate = new Date(options.startDate).toISOString();
    }
    if (options.endDate) {
        options.endDate = new Date(options.endDate).toISOString();
    }
    return pullOpenGLdata(options, errFileInfo);
}

async function performCustomerPull(options, errFileInfo) {
    util.saveSegment('executing pullCloseROdata',segmentInfo);
    try {
        const res = await customer.pullCustomer(options, '', errFileInfo);
        return res;
    } catch (err) {
        throw new Error("Error during open RO pull: " + err.message); // Rethrow error with additional context
    }   
}
async function performOpenROPull(options, errFileInfo) {
    util.saveSegment('executing open Ro pull');
    return new Promise(async(resolve,reject)=>{
        try {
            const res = await openRO.pullOpenRO(options, '', errFileInfo);
            if(res.status){
                resolve(res);
            }else{
                reject(res);
            }
        } catch (err) {
            console.error("Error during open RO pull:", err);
            throw new Error("Error during open RO pull: " + err.message); 
        }
    })
  
}
async function performMakeModelPull(options, errFileInfo) {
    util.saveSegment('executing make model',segmentInfo);
    return await make.pullMakeModel(options, '', errFileInfo);  
}

async function performCoaPull(options, errFileInfo) {
    util.saveSegment('executing COA pull',segmentInfo);
    return await getCoa.pullCoa(options, '', errFileInfo);  
}

async function performEmployeePull(options, errFileInfo) {
    util.saveSegment('executing EMPLOYEE pull',segmentInfo);
    try{
        let res = await employee.pullEmployee(options, '', errFileInfo);
        return res;
    }catch(err){
    }
   
}

async function performLaborPull(options, errFileInfo) {
    util.saveSegment('executing labor pull',segmentInfo);
    return new Promise(async(resolve,reject)=>{
        try{
            let res = await labor.pullLabor(options, '', errFileInfo);
            if(res.status){
                resolve(res);
            }else{
                reject(res);
            }
        }catch(err){
            throw new Error("Error during open RO pull: " + err.message); 
        }
        
    })
}


// async function performROBULKPull(options, errFileInfo) {
//     util.saveSegment('executing EMPLOYEE pull',segmentInfo);
//     // return await robulk.pullROBULK(options, '', errFileInfo);  
//     try{
//         let res = await robulk.pullROBULK(options, '', errFileInfo);
//         console.log("RO BULK res$$$$$$$$$$$$$$$$$$$$",res);
//         return res;
//     }catch(err){
//         console.log("RO BULK pull has error@@@@@@@@@@@@@@@",err);
//     }
// }
async function performROBULKPull(options, errFileInfo) {
    util.saveSegment('executing EMPLOYEE pull',segmentInfo);
    return new Promise(async(resolve,reject)=>{
        try{
            let res = await robulk.pullROBULK(options, '', errFileInfo);
            if(res.status){
                resolve(res);
            }else{
                reject(res);
            }
            
        }catch(err){
           reject(err);
        }
    })
    // return await robulk.pullROBULK(options, '', errFileInfo);  
  
}
async function performGlAccountingPull(options, errFileInfo) {
    util.saveSegment('executing open Ro pull',segmentInfo);
    return await GLAcounting.getGLAccounting(options, '', errFileInfo);  
}



module.exports = {
    performROPull: performROPull,
    performROPullWithDateRange:performROPullWithDateRange,
    performCustomerPull:performCustomerPull,
    performMakeModelPull:performMakeModelPull,
    performCoaPull:performCoaPull,
    performEmployeePull:performEmployeePull,
    performGlPullWithDateRange:performGlPullWithDateRange,
    performOpenROPull:performOpenROPull,
    performROBULKPull:performROBULKPull,
    performLaborPull:performLaborPull,
    performGlAccountingPull:performGlAccountingPull,
    performGLPull:performGLPull

    
}
