"use strict";

const fs = require('fs');
const moment = require("moment-timezone");
const constants = require("./constants");
const axios = require("axios");
function doDateSplitting(_startDate, _endDate, mode) {
    var _tempStartDate;
    var arr = [];
    var inArr = [];
    _tempStartDate = moment(_endDate).startOf(mode);
    while (isBetween(_startDate, _endDate, _tempStartDate)) {
        inArr.push(convertTODate(_tempStartDate), convertTODate(_endDate));
        arr.push(inArr);
        _endDate = moment(_endDate).subtract(1, mode)
        _tempStartDate = moment(_endDate).startOf(mode)
        _endDate = moment(_endDate).endOf(mode)
        inArr = [];
    }
    arr.push([convertTODate(_startDate), convertTODate(_endDate)]);
    return (arr);
}

function isBetween(startDate, endDate, _tempStartDate) {
    if (moment(startDate).isBefore(_tempStartDate) &&
        moment(endDate).isSameOrAfter(_tempStartDate)) {
        return true;
    }
}

function convertTODate(date) {
    return moment(date).format(constants.CLI_DATE_FORMAT);
}

function convertToMoment(date) {
    console.log(">>>>>>>>>>>>>>>>DATE>>>>>>>>",date);
    return moment(date, constants.CLI_DATE_FORMAT, true).isValid() ?
        moment(new Date(date), constants.CLI_DATE_FORMAT) : constants.INVALID_DATE;
}

// ########################################################################################################################################################
function convertDate(date){
    let modDate = format(date);
    console.log('FORMATED DATE^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ ',modDate);
    let modifiedDate = toTimestamp(modDate);
    return modifiedDate;
  }



  function toTimestamp(strDate){
    console.log('%%%%%%%%%%%%%%%%%%%%%%INSIDE TO TIME STAMP');  
    var datum = Date.parse(strDate);
    // console.log('Time stamp',datum/1000)
    return datum/1000;
}



function format(inputDate) {
   console.log('@@@@@@@@@@@@@@@@@@@@INSIDE FORMAT   ');
    let date = new Date(inputDate);
       if (!isNaN(date.getTime())) {
        // Months use 0 index.
        return date.getMonth() + 1 + '/' + date.getDate() + '/' + date.getFullYear();
      }
    }





// ###############################################################################################################################################













/**
 * Function to convert a date range to set of Months or Weeks
 * @param {Date} startDate
 * @param {Date} endDate
 * @param {constants.CLI_CLOSED_FLAG} MODE
 */
function splitDateRange(startDate, endDate, MODE) {
    var _startDate = convertToMoment(startDate),
        _endDate = convertToMoment(endDate),
        mode = "month"; // Default constants.CLI_CLOSED_FLAG.MODE_MONTHLY
    if (_startDate === constants.INVALID_DATE || _endDate === constants.INVALID_DATE) {
        throw (constants.INVALID_DATE);
    } else if (_endDate.isBefore(_startDate)) {
        throw (`Start date: ${startDate} must be a date before end date: ${endDate}`);
    } else if (MODE === constants.CLI_CLOSED_FLAG.MODE_ALL) {
        return [[convertTODate(_startDate), convertTODate(_endDate)]];
    } else if (MODE === constants.CLI_CLOSED_FLAG.MODE_WEEKLY) {
        mode = "week";
    }
    return (doDateSplitting(_startDate, _endDate, mode));
}

/**
 * Function to Save segment details
 * @param {*} segmentData
 */
async function saveSegment(segmentData, segmentInfo) {
    var currentPath = process.cwd() + '/logs/segment-' + moment().format('YYYY-MM-DD') + '.log';
    var infoStream = fs.createWriteStream(currentPath, { flags: 'a+' });
    var message = moment().toString() + ' : '+ segmentInfo + ':' + segmentData + '\n';
    var respMessage = 'Success';
    try {
        try {
            infoStream.write(message);
        } catch (err) {
            console.log(err);
        }
    } catch (error) {
        respMessage = 'Failure';
        infoStream.write(error);
    }
}

async function  createErrorFile(errFileObj){
    fs.writeFileSync(errFileObj.directory + "/" + errFileObj.errorFileName + '.err', errFileObj.errorMessaage);
}
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function checkApiStatus(options, statusUrl) {
  return new Promise(async (resolve) => {
    // Define config outside the try block to ensure it is accessible in the catch block
    console.log("status url>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",statusUrl);
    const token = "Bearer " + options.token;
    const config = {
      method: "get",
      maxBodyLength: Infinity,
      url: statusUrl,
      headers: {
        "Subscription-Id": constants.FORTELLIS_UNIQUE_ID,
        "Request-Id": constants.FORTELLIS_UNIQUE_ID,
        Authorization: token,
      },
    };

    try {
      const response = await axios(config); // Using async/await to handle the API call
      console.log("response$$$$$$$$$$$$$$$$$$$$$$$$",response);
      console.log("response$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",response.data);
      resolve({
        result: response.data,
        status: true,
        requestHeader: JSON.stringify(config), // Include requestHeader in all cases
        responseHeader: "", // Capture response headers
      });
    } catch (error) {
      console.log("Error:", JSON.stringify(error)); // Logging the error if something goes wrong
      resolve({
        result: error, // Fallback to a default error message
        status: false,
        requestHeader: JSON.stringify(config)
      });
    }
  });
}

module.exports = {
    splitDateRange: splitDateRange,
    saveSegment: saveSegment,
    createErrorFile:createErrorFile,
    convertDate:convertDate,
    sleep:sleep,
    checkApiStatus:checkApiStatus
}
