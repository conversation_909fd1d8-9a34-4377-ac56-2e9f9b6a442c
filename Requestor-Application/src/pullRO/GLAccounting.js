"use strict";

const Spinner = require("cli-spinner").Spinner;
var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
var segmentInfo;
const axios = require("axios");
const constants = require("../constants");
const getOperationId = require('./generateOperationId');
const { option } = require("commander");
/**
 *  This is the Primary function responsible for pulling all closed RO's
 *
 */
// async function getGLAccounting(options) {
//   segmentInfo = JSON.stringify({
//     StoreID: options.storeID,
//     Franchise: options.franchise,
//   });
//   util.saveSegment("executing getGLAccounting", segmentInfo); 
//   // return (allClosedROResponse = await getGLAccountingData(options));
 
// }
/**
 * Fetch all open RO'S from DealerTrack server
 */
function getGLAccounting(options) {
  console.log("options.startDate@@@@@@@@@@@@@@@@@@",options.startDate);
  console.log("options.enddate@@@@@@@@@@@@@@@@@@",options.endDate);
  console.log("options FL ACCOUNTING  @@@@@@@@@@@@@@@@@",options);
  options.startDate = util.convertDate(options.startDate);
  options.endDate = util.convertDate(options.endDate);
  return new Promise((resolve,reject) => {
      // if(options.operationId.status ==false){
      //   console.log('testing',`Fetching Opertaion Id Failed ${options.operationId.data}`);
      //   reject({result:`ERROR IN Fetching Opertaion Id: ${options.operationId.data}`,staus:false})
      // }
      let token = "Bearer " + options.token;  
      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${constants.url.GL_BALANCE.JOURNAL_ENTRY_GL_DETAIL}?companyId=${options.glAccountCompanyID}&startDate=${options.startDate}&endDate=${options.endDate}`,
        headers: { 
          'Department-Id':options.departmentId, 
          'Request-Id': options.thirdPartyUsername, 
          'Subscription-Id': options.thirdPartyUsername, 
          'Authorization': token
        }
      };
      console.log("GL ACCOUNTING CONFIG!!!!!!!!!!!!!!!!!!!!!!!!!!!!",config);
      try {
       axios(config)
        .then(function (response) {
              resolve({
              result:JSON.stringify(response.data),
              status: true,
              requestHeader: JSON.stringify(config),
              responseHeader: "",
              isAccounting:true
            });
        //   })
        })
        .catch(function (error) {
          console.log("gl accounting error@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",error);
        reject({ result: error.response.statusText,requestHeader: JSON.stringify(config), status: false });
        });
    } catch (err) {
      console.log("gl accounting err@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",err);
      console.log(JSON.stringify(err));
      reject({ result: [],requestHeader: JSON.stringify(config), status: false });
    }
  });
}

module.exports = {
    getGLAccounting: getGLAccounting,
};
