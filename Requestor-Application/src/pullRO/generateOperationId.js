const axios = require("axios");
const qs = require("qs");
const dayjs = require('dayjs');
const constants = require("../constants");

exports.generateoperationId = (token,startDate,endDate,dealerID,thirdPartyUsername) => {
  console.log("start date ",startDate);
  startDate = dayjs(startDate, 'MM/DD/YYYY').format('YYYY-MM-DD[T]HH:mm:ss');
  endDate = dayjs(endDate, 'MM/DD/YYYY').format('YYYY-MM-DD[T]HH:mm:ss');
  return new Promise((resolve, reject) => {
    console.log("start date for Ro pUll",startDate);
    console.log("end  date for Ro pUll",endDate);
    const config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `${constants.url.SERVICEREPAIRORDERSETUP.HISTORY}${startDate}&endDate=${endDate}`,
      headers: { 
        'Request-Id': thirdPartyUsername, 
        'Subscription-Id': thirdPartyUsername, 
        'Department-Id': dealerID, 
        'Authorization': `Bearer ${token}`
      }
    };
    console.log("config generateoperationId",config);  
    try {
       axios(config)
        .then(function (response) {     
          setTimeout(()=>{
            console.log("generateoperationId Resolve",response);
            resolve({status: true, data: response,requestHeader: JSON.stringify(config)});
          },40000)          
        })
        .catch(function (error) {
          console.log("generateoperationId API Error",JSON.stringify(error));
          resolve({ status: false, data: error ,requestHeader: JSON.stringify(config)});
        });

    } catch (err) {
      console.log("generateoperationId Error",JSON.stringify(err));
      resolve({ status: false, data: err.message ,requestHeader: JSON.stringify(config)});
    }
  });
};
exports.generateoperationIdForCustomer = (token,dealerID,thirdPartyUsername) => {
  console.log("********GENERATE OPERATION ID FOR CUSTOMER START*******")
    return new Promise((resolve, reject) => {      
        const config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: constants.url.CUSTOMER.BULK,
          headers: { 
            'Request-Id': thirdPartyUsername, 
            'Department-Id': dealerID, 
            'Subscription-Id': thirdPartyUsername, 
            'Authorization': `Bearer ${token}`
          }
        };
        try { 
          axios(config)
          .then(function (response) {
           // setTimeout(() => {
              resolve({status: true, data: response.data, requestHeader: JSON.stringify(config)});
            //}, 40000);            
          })
          .catch(function (error) {
            resolve({ status: false, data: error,requestHeader: JSON.stringify(config) });
          });
  
      } catch (err) {
        resolve({ status: false, data: err.message ,requestHeader: JSON.stringify(config) });
      }
    });
  };
exports.generateoperationIdForMakeModel = (token,dealerID,thirdPartyUsername) => {
  console.log("********GENERATE OPERATION ID FOR MAKEMODEL START*******")
  return new Promise((resolve, reject) => {      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: constants.url.MAKEMODEL.BULK,
        headers: {
          'Request-Id': thirdPartyUsername,
          'Accept': 'application/json',
          'Subscription-Id': thirdPartyUsername,
          'Department-Id': dealerID,
          'Authorization': `Bearer ${token}`
        }
      };
      console.log("config OperationIdForMakeModel", config);
      try{
        axios(config)
        .then(function (response) {
          resolve({ status: true, data: response.data, requestHeader: JSON.stringify(config) });
        })
        .catch(function (error) {
          resolve({ status: false, data: error, requestHeader: JSON.stringify(config) });
        });

    } catch (err) {
      resolve({ status: false, data: err.message, requestHeader: JSON.stringify(config) });
    }
  });
};

/*   exports.generateoperationIdForCustomerDelta = (token) => {
    return new Promise((resolve, reject) => {
      try {
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://api.fortellis.io/cdk-test/drive/customer/v1/delta?startDate=2024-01-08T10:15:30&endDate=2024-01-20T10:15:30',
        headers: { 
          'Request-Id': '********-2c5f-438d-a77d-88f1e94c6398', 
          'Accept': 'application/json', 
          'Subscription-Id': '********-2c5f-438d-a77d-88f1e94c6386', 
          'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjlhNTU2ZGYwLWI3NjUtMTFlOC1hMmU4LTI5ZjRlYmZhMjg4MiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IY_z46-tL_hT736SCP1BF4Iwy3mWOS-SboCgVynfIzyswFVt-4QvL-dBuostfD2zLfwVFyVpMeDDq9MHUQYgiyc03iPshJqYHRd-JnMEzH0fhi32WJ3WhGVrMuWNKN9KfG46sv_OWkGiAaORh8PL-7VMRTmTuvXBGCnkSRP-JdNOjzVk4e_e5z6uUDqutMhb8BZHhc7t9lb_sVsguoGqGtvukJzzlpf7JTcW9aESlk1lfmxpUgw4tXChzZzu6dxQL77YrHqkHouly63LV1yuQ0h-ClmJiVniUv9GGIjD5RA73TRklgxFxPaQqHZv5kcz1LO4allJpT92UOeg3_qHlg'
        }
      }  
      console.log("config OperationIdForCustomerDelta",config);
        axios(config)
          .then(function (response) {
            setTimeout(()=>{
              resolve({status: true, data: response.data.operationId});  
            },40000)
            
          })
          .catch(function (error) {  
            console.log("generateoperationIdForCustomerDelta error",JSON.stringify(error));
            resolve({ status: false, data: error.response.data });
          });
  
      } catch (err) {
        console.log("generateoperationIdForCustomerDelta error",JSON.stringify(err));
        resolve({ status: false, data: err.message });
      }
    });
  }; */
  
/*   exports.generateoperationIdForGl = (token,startDate,endDate) => {
    return new Promise((resolve, reject) => {
      try {  
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.fortellis.io/cdk-test/drive/gl-balance/v2/gl-account-ledger/delta?companyId=5&startDate=${startDate}&endDate=${endDate}`,
        headers: { 
          'Request-Id': '555555555544546464', 
          'Department-Id': '01', 
          'Accept': 'application/json', 
          'Subscription-Id': '********-2c5f-438d-a77d-88f1e94c6386', 
          'Authorization': `Bearer ${token}`
        }
      };  
      console.log("config OperationIdForGL",config);
        axios(config)
          .then(function (response) {
            console.log("generateoperationIdForGl resolve");
            setTimeout(()=>{
              resolve({status: true, data: response.data.operationId});  
            },40000)
            
          })
          .catch(function (error) {
            console.log("generateoperationIdForGl API Error",JSON.stringify(error));
            resolve({ status: false, data: error,requestHeader:JSON.stringify(config)});
          });
  
      } catch (err) {
        console.log("generateoperationIdForGl error",JSON.stringify(err));
        resolve({ status: false, data: err });
      }
    });
  }; */  
   
/*   exports.generateoperationIdForCoa = (token) => {
    console.log("generating Operation Id for coa...................................");
    return new Promise((resolve, reject) => {
      try {
      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${constants.url.SERVICEREPAIRORDER.OPEN}?grant_type=client_credentials&scope=anonymous`,
        url: 'https://identity.fortellis.io/oauth2/aus1p1ixy7YL8cMq02p7/v1/token?grant_type=client_credentials&scope=anonymous',
        headers: { 
          'authorization': 'Basic cXE4UnUzSXFKVzlhVVJMbFJqSlAyTTVVbG5MY1RjTkQ6c0p3UFRTdHBrVTZCNFlhOXdSSkRKRGVsZXFkYmN3VzU=', 
          'content-type': 'application/x-www-form-urlencoded', 
          'accept': 'application/json', 
          'cache-control': 'no-cache'
        }
      };  
        axios(config)
          .then(function (response) {
            setTimeout(()=>{
              console.log("generateoperationIdForCoa Operation id resolved");
              resolve({status: true, data: response.data.operationId});
  
            },40000)
            
          })
          .catch(function (error) {
            resolve({ status: false, data: error.response.data });
          });  
      } catch (err) {
        resolve({ status: false, data: err.message });
      }
    });
  }; */
  exports.generateoperationIdForOpenRO = (token,startDate,endDate,dealerID,thirdPartyUsername) => {
    console.log("********GENERATE OPERATION ID FOR OpenRO START*******")
    startDate = dayjs(startDate, 'MM/DD/YYYY').format('YYYY-MM-DD[T]HH:mm:ss');
    endDate = dayjs(endDate, 'MM/DD/YYYY').format('YYYY-MM-DD[T]HH:mm:ss');
    return new Promise((resolve, reject) => {      
        const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${constants.url.SERVICEREPAIRORDER.OPEN}?startDate=${startDate}&endDate=${endDate}`,
        headers: { 
          'Request-Id': thirdPartyUsername, 
          'Department-Id': dealerID, 
          'Subscription-Id': thirdPartyUsername, 
          'Authorization': `Bearer ${token} `
        }
      };
      console.log("config OperationIdForOpenRO",config);
       try {
        axios(config)
          .then(function (response) {
       
          //  setTimeout(()=>{
              resolve({status: true, data: response.data,requestHeader: JSON.stringify(config)});           
          //  },40000)            
          })
          .catch(function (error) {
            resolve({ status: false, data: error,requestHeader:JSON.stringify(config)});
          });  
      } catch (err) {
        resolve({ status: false, data: err.message ,requestHeader: JSON.stringify(config) });
      }
    });
  };
  exports.generateoperationIdForRobulk = (token,startDate,endDate,dealerID,thirdPartyUsername)=>{
    startDate = dayjs(startDate, 'MM/DD/YYYY').format('YYYY-MM-DD[T]HH:mm:ss');
    endDate = dayjs(endDate, 'MM/DD/YYYY').format('YYYY-MM-DD[T]HH:mm:ss');
    return new Promise((resolve, reject) => {       
        const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${constants.url.SERVICEREPAIRORDER.CLOSED}?startDate=${startDate}&endDate=${endDate}`,
        headers: { 
          'Request-Id': thirdPartyUsername, 
          'Department-Id': dealerID,
          'Subscription-Id': thirdPartyUsername, 
          'Authorization': `Bearer ${token} `
        }
      };
      console.log("config OperationIdForRoBulk",config);
       try{
         axios(config)
          .then(function (response) {       
           // setTimeout(()=>{
              resolve({status: true, data: response.data,requestHeader: JSON.stringify(config)});  
           // },40000) 
           console.log(response)           
          })
          .catch(function (error) {
            resolve({ status: false, data: error,requestHeader:JSON.stringify(config)});
          });  
      } catch (err) {
        resolve({ status: false, data: err.message ,requestHeader:JSON.stringify(config)});
      }
    });
  }
  exports.generateoperationIdForEmployee = (token,dealerID,thirdPartyUsername)=>{
    console.log("********GENERATE OPERATION ID FOR EMPLOYEE START*******")
    return new Promise((resolve, reject) => {     
      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: constants.url.EMPLOYEE.BULK,
        headers: { 
          'Request-Id': thirdPartyUsername, 
          'Accept': 'application/json', 
          'Subscription-Id': thirdPartyUsername,  
          'Department-Id': dealerID, 
          'Authorization': `Bearer ${token}`
        }
      };       
      console.log("config OperationIdForEmployee",config);
       try{
        axios(config)
          .then(function (response) {       
            // setTimeout(()=>{
              resolve({status: true, data: response.data,requestHeader: JSON.stringify(config)});
            // },40000)            
          })
          .catch(function (error) {
            resolve({ status: false, data: error ,requestHeader:JSON.stringify(config)});
          });  
      } catch (err) {
        resolve({ status: false, data: err.message ,requestHeader:JSON.stringify(config)});
      }
    });
  }
  exports.generateoperationIdForLabor = (token,startDate,endDate,dealerID,thirdPartyUsername)=>{  
    console.log("********GENERATE OPERATION ID FOR LABOR START*******")
    startDate = dayjs(startDate, 'MM/DD/YYYY').format('YYYY-MM-DD[T]HH:mm:ss');
    endDate = dayjs(endDate, 'MM/DD/YYYY').format('YYYY-MM-DD[T]HH:mm:ss');
    return new Promise((resolve, reject) => {
      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${constants.url.SERVICE_LABOR_TYPE.BULK}?startDate=${startDate}&endDate=${endDate}`,
        headers: { 
          'Request-Id': thirdPartyUsername, 
          'Department-Id': dealerID, 
          'Subscription-Id': thirdPartyUsername, 
          'Authorization': `Bearer ${token}`
        }
      };      
      console.log("config OperationIdForLabor",config);
      try {
        axios(config)
          .then(function (response) {   
            console.log("generateoperationIdForLabor 1")    
           // setTimeout(()=>{
              resolve({status: true, data: response,requestHeader: JSON.stringify(config)});
           // },40000)            
          })
          .catch(function (error) {
            console.log("generateoperationIdForLabor 2")
            resolve({ status: false, data: error ,requestHeader:JSON.stringify(config)});
          });  
      } catch (err) {
        console.log("generateoperationIdForLabor 3")
        resolve({ status: false, data: err.message ,requestHeader:JSON.stringify(config)});
      }
    });
  }



  
