"use strict";

const Spinner = require("cli-spinner").Spinner;
var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
const axios = require("axios");
const constants = require("../constants");

/**
 *  This is the Primary function responsible for pulling all closed RO's
 *
 */
// async function pullCoa(options) {

//   segmentInfo = JSON.stringify({
//     StoreID: options.storeID,
//     Franchise: options.franchise,
//   });
//   util.saveSegment("executing pullOpenRO", segmentInfo);
//   return (allClosedROResponse = await pullCoaData(options));
// }
/**
 * Fetch all closed RO'S from DealerTrack server
 */
function pullCoa(options) {  
  options.startDate = util.convertDate(options.startDate);
  options.endDate = util.convertDate(options.endDate);
  return new Promise((resolve, reject) => {
    let token = "Bearer " + options.token;
    const config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `${constants.url.GL_BALANCE.GL_COA_BULK}?companyId=${options.glAccountCompanyID}`,
      headers: {
        'Request-Id': constants.FORTELLIS_UNIQUE_ID,
        'Department-Id': options.departmentId,
        'Subscription-Id': constants.FORTELLIS_UNIQUE_ID,
        'Authorization': token
      }
    };
    console.log("config pullCoaData", config);
    try {
      // if(options.operationId.status ==false){
      //   reject({result:`ERROR IN Fetching Opertaion Id: ${options.operationId.data}`,staus:false})
      // }
      let token = "Bearer " + options.token;   
    

      let config = {
         method: 'get',
         maxBodyLength: Infinity,
         url: `${constants.url.GL_BALANCE.GL_COA_BULK}?companyId=${options.glAccountCompanyID}`,
         headers: { 
          'Request-Id': options.thirdPartyUsername, 
          'Department-Id': options.departmentId,
          'Subscription-Id': options.thirdPartyUsername, 
          'Authorization': token
       }
     };
      console.log("config pullCoaData",config);
      axios(config)
        .then(function (response) {
          resolve({
            result: JSON.stringify(response.data),
            status: true,
            requestHeader: JSON.stringify(config),
            responseHeader: "",
            isAccounting: true
          });
        })
        .catch(function (error) {
          console.log("COA ERROR@@@@@@@@@@@@@@@@", error);
          reject({ result: error.response.statusText, status: false, requestHeader: JSON.stringify(config) });
        });
    } catch (err) {
      console.log("COA err@@@@@@@@@@@@@@@@", err);
      reject({ result: [], status: false, requestHeader: JSON.stringify(config) });
    }
  });
}

module.exports = {
    pullCoa: pullCoa,
};
