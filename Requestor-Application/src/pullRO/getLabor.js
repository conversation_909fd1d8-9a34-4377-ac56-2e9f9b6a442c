"use strict";

const Spinner = require("cli-spinner").Spinner;
const parser = require("xml2json");
const fs = require("fs");
const moment = require("moment-timezone");
var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
var allClosedROResponse;
var segmentInfo;
const axios = require("axios");
var qs = require('qs');
const constants = require("../constants");
const getOperationId = require('./generateOperationId');
const { option } = require("commander");

/**
 *  This is the Primary function responsible for pulling all closed RO's
 *
 */
async function pullLabor(options) {
  segmentInfo = JSON.stringify({
    StoreID: options.storeID,
    Franchise: options.franchise,
  });
  util.saveSegment("executing pullOpenRO", segmentInfo);
  const operationIdData = await getOperationId.generateoperationIdForLabor(options.token, options.startDate, options.endDate, options.dealerID,options.thirdPartyUsername);
  console.log('GENERATEO PERATION ID FOR LABOR:', operationIdData)
  if(operationIdData.status){
  let operationId = operationIdData.data;
  const operationStatus = operationId.data?.status;
  const statusUrl = operationId.data._links?.status?.href;

  util.saveSegment(`Fetch operation id status: ${operationStatus}`);
  console.log(`Fetch operation id status: ${operationStatus}`);

  if (operationStatus !== 'received' || !statusUrl) {
    console.log("OPERATION ID NOT FOUND!");
    return {
      status: false,
      result: `ERROR IN Fetching Operation Id: ${
        operationIdData.response?.statusText || "Unknown Error"
      }`,
      requestHeader: operationId.requestHeader,
    }; 
  }
  if(operationId.data.status == 'received'){
    options.operationId = operationId.data;
  }
  util.saveSegment(`Fetched status URL: ${statusUrl}`);
  console.log(`Fetched status URL: ${statusUrl}`);
  const maxAttempts = 3;
  let attempt = 1;
  let response;
  while (attempt <= maxAttempts) {
    try {
      console.log(`Waiting 30 seconds before attempt ${attempt} to check API status: ${statusUrl}`);
      util.saveSegment(`Waiting 30 seconds before attempt ${attempt} to check API status: ${statusUrl}`);

      await util.sleep(30000);
      response = await util.checkApiStatus(options, statusUrl);
      const responseStatus = response.result.status;

      console.log(`${attempt} response: ${responseStatus}`);
      util.saveSegment(`${attempt} response: ${responseStatus}`);
      if (responseStatus == 'complete') {
        console.log('Status is complete, stopping further attempts.');
        util.saveSegment('Status is complete, stopping further attempts.');

        const resultUrl = response.result._links.result.href;
        util.saveSegment(`Fetched result URL: ${resultUrl}`);

        const allClosedROResponse = await pullLaborData(options, resultUrl);
        return allClosedROResponse;
      }
    } catch (error) {
      return {
        status: false,
        result: `Error calling result API: ${error.result}`,
        requestHeader: error.requestHeader,
      }
      console.error('Error calling API:', error);
    }
    attempt++;
    if (attempt < maxAttempts) {
      console.log("Waiting for the next attempt...");
      util.saveSegment("Waiting for the next attempt...");
    } else {
      util.saveSegment("Maximum attempts to get the status from the API reached.");
      return {
        status: false,
        result: `Maximum attempts to get the status from the API reached: ${response}`,
        requestHeader: operationId.requestHeader,
      }; 
    }
  }
}else{

  console.log(`respone: ${JSON.stringify(operationIdData)}`);
  return {
    status: false,
    result: `ERROR IN Fetching Operation Id: ${
      operationIdData.data?.message || "Unknown Error"
    }`,
    requestHeader: operationIdData.requestHeader,
  };

}
}
/**
 * Fetch all closed RO'S from DealerTrack server
 */
function pullLaborData(options, resultUrl) {
  console.log('Start date:', options.startDate);
  console.log('End date:', options.endDate);
  console.log('Current date', options.currentDate);
  console.log('Token :', options.token);
  options.startDate = util.convertDate(options.startDate);
  options.endDate = util.convertDate(options.endDate);
  return new Promise((resolve, reject) => {   
      if (options.operationId.status == false) {
        console.log('testing', `Fetching Opertaion Id Failed ${options.operationId.data}`);
        reject({ result: `ERROR IN Fetching Opertaion Id: ${options.operationId.data}`, staus: false })
      }
      let token = "Bearer " + options.token;
      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: resultUrl,
        headers: {
          'Request-Id': options.thirdPartyUsername,
          'Subscription-Id': options.thirdPartyUsername,
          'Authorization': token
        }
      };
      console.log("config pullLaborData", config);
      try {
        axios(config)
        .then(function (response) {
          util.saveSegment("Labor response:", response);
          resolve({
            result: JSON.stringify(response.data),
            status: true,
            requestHeader: JSON.stringify(config),
            responseHeader: "",
          });
        })
        .catch(function (error) {
          console.log("labor error ", error);
          reject({ result:error.message,requestHeader: JSON.stringify(config), status: false });
        });
    } catch (err) {
      console.log("Labor error2", err);
      reject({ result: err,requestHeader: JSON.stringify(config), status: false });
    }
  });
}

module.exports = {
    pullLabor: pullLabor,
};
