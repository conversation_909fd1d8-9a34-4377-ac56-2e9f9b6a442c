"use strict";

const Spinner = require("cli-spinner").Spinner;
const parser = require("xml2json");
const fs = require("fs");
const moment = require("moment-timezone");
var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
var allClosedROResponse;
var segmentInfo;
async function pullCustomerDelta(options) {
  let operationId = await generateoperationIdForRO(options.token);
  options.operationId = operationId;
  segmentInfo = JSON.stringify({
    StoreID: options.operationId,
    Franchise: options.franchise,
  });
  util.saveSegment("executing pullOpenRO", segmentInfo);
  return (allClosedROResponse = await pullROData(options));
}
module.exports = {
  pullRO: pullRO,
  pullCustomerDelta:pullCustomerDelta
};
