"use strict";

const Spinner = require("cli-spinner").Spinner;
const parser = require("xml2json");
const fs = require("fs");
const moment = require("moment-timezone");
var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
const axios = require("axios");
var qs = require('qs');
const constants = require("../constants");
const operationIdGenerator = require('./generateOperationId');
const { rejects } = require("assert");
const { config } = require("process");
function pullGl(options) {
  console.log("config pullGlData",options);
      options.startDate = new Date(options.startDate);
      options.endDate = new Date(options.endDate);
       options.startDate =  options.startDate.toISOString().split('T')[0];
       options.endDate = options.endDate.toISOString().split('T')[0];
        return new Promise((resolve,reject) => {
          let token = "Bearer " + options.token;
          const config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `${constants.url.GL_BALANCE.JOURNAL_ENTRY_GL_DETAIL}?companyId=${options.glAccountCompanyID}&startDate=${options.startDate}&endDate=${options.endDate}`,
            headers: { 
              'Department-Id': options.departmentId, 
              'Request-Id': options.thirdPartyUsername, 
              'Subscription-Id': options.thirdPartyUsername, 
              'Authorization': token
            }
          };
          console.log("config pullGlData",config);
          try {
            axios(config)
          .then(function (response) {
            console.log("gl data order response",response);
                 resolve({
                result:JSON.stringify(response.data) ,
                status: true,
                requestHeader: JSON.stringify(config),
                responseHeader: "",
              });
          })
          .catch(function (error) {
            console.log("test2????????????????????????????????????????????????",error);
            reject({ result: error.response.statusText,requestHeader: JSON.stringify(config), status: false });
           // resolve({ result: '', status: false,responseHeader: error.response ? error.response.data.message : "Unknown Error",requestHeader:JSON.stringify(config),isAccounting:true });
          });    
        } catch (err) {
          console.log("****************************************test3",err);
          console.log(JSON.stringify(err));
          reject({ result: [],requestHeader: JSON.stringify(config), status: false });
          //resolve({ result: [], status: false,responseHeader:JSON.stringify(err),requestHeader:JSON.stringify(config),isAccounting:true });
        }
    });
}

module.exports = {
  pullGl: pullGl,
};
