"use strict";

const Spinner = require("cli-spinner").Spinner;
const parser = require("xml2json");
const fs = require("fs");
const moment = require("moment-timezone");

var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
var allClosedROResponse;
var segmentInfo;
const axios = require("axios");
var qs = require('qs');
const constants = require("../constants");
const operationIdGenerator = require('./generateOperationId');
/**
 *  This is the Primary function responsible for pulling all closed RO's
 *
 */

const generateoperationIdForRO = async (options) =>{
  let operationId = await operationIdGenerator.generateoperationId(options.token,options.startDate,options.endDate,options.dealerID,options.thirdPartyUsername);
  console.log("operationId>>>>>>>>>>>>>>>>>>>>>>>>>>",operationId);
  //   if(operationId.status){
  //     return operationId.data;
  //  }
   return operationId;  
}

async function pullRO(options) {
  
  let operationId = await generateoperationIdForRO(options);
  console.log("operation Id$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",operationId);
  if(operationId.status){
    util.saveSegment(`Fetch operation  id status${operationId.data.data.status}`);
    console.log(`Fetch operation  id status${operationId.data.data.status}`);
    util.saveSegment(`Fetched status url ${operationId.data.data._links.status.href}`);
    console.log(`Fetch operation  id status${operationId.data.data.status}`);
    console.log(`Fetched status url ${operationId.data.data._links.status.href}`);
     let statusUrl;
      if(operationId.data.data.status == 'received'){
         statusUrl = operationId.data.data._links.status.href;
    
      }else{
         console.log("OPERATION ID NOT FOUND!!!!");
         return {
          status: false,
          result: `ERROR IN Fetching Operation Id: ${operationId.data.data.status}`,
          requestHeader: operationId.requestHeader,
        };    
      }
  let attemptCount = 0;
  const maxAttempts = 3;
  let response ;
  
  while (attemptCount < maxAttempts) {
    attemptCount++;
     try {
      console.log(`Waiting 30 seconds before the ${attemptCount} attempt to check the API status of the request${statusUrl}`);
      util.saveSegment(`Waiting 30 seconds before the ${attemptCount} attempt to check the API status of the request${statusUrl}`);
      await util.sleep(30000); //Waiting 30 seconds for status api check
       response = await util.checkApiStatus(options,statusUrl); //Api used to check the status of the result api.30 seconds delay recommended by  fortellis
      console.log(`${attemptCount} response:${response}`);
      // util.saveSegment(`${attemptCount} response:${JSON.stringify(response)}`);
      if (response.result.status == 'complete') {
        console.log('Status is complete, stopping further attempts.');
        util.saveSegment('Status is complete, stopping further attempts.')
        let resultUrl = response.result._links.result.href;
        util.saveSegment(`fetched result url${resultUrl}`);
        const allClosedROResponse = await pullROData(options,resultUrl); //Api used to retrive the actual data
        return allClosedROResponse;
      }
   
    } catch (error) {
      console.error('Error calling API:', error);
    }
  
    if (attemptCount < maxAttempts) {
      console.log("Waiting for the next attempt...");
      util.saveSegment(`Waiting for the next attempt...`);
      
    }else{
      util.saveSegment(`Maximum attempts to get the status from the API reached!...`);
      console.log("response stratus ro #################################################",response);
      return {
        status: false,
        result: `Maximum attempts to get the status from the API reached: ${response.result.status }`,
        requestHeader: operationId.requestHeader,
      }; 
    }
  }
}else{
   if(operationId.data.response){
    console.log("operations id response>>>>>>>>>>>>>>>>>>>>>>>>",operationId.data.response);
   }else{
    console.log("No respone got for operation id!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
   }
   let errObj ={};
   errObj.status = false;
   errObj.result =`Error in Fetching operation Id ${operationId.data.response.statusText}`;
   errObj.requestHeader = JSON.stringify(operationId.data.config);
   
   return errObj;
}
 

  // return (allClosedROResponse = await pullROData(options));
}
/**
 * Fetch all closed RO'S from DealerTrack server
 */

function pullROData(options,resultUrl) {  
   options.startDate = util.convertDate(options.startDate);
   options.endDate = util.convertDate(options.endDate);
  return new Promise((resolve) => {
      let token = "Bearer " + options.token;       
      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: resultUrl,
        headers: { 
          'Request-Id':  options.thirdPartyUsername, 
          'Subscription-Id':  options.thirdPartyUsername, 
          'Authorization': token
        }
      };
      console.log("config pullROData",config);
      try{
      axios(config)
      .then(function (response) {
             resolve({
            result:JSON.stringify(response.data) ,
            status: true,
            requestHeader: JSON.stringify(config),
            responseHeader: "",
          });
      //   })
      })
      .catch(function (error) {
        resolve({ result: error, requestHeader: JSON.stringify(config), status: false });
      });
    } catch (err) {
      console.log(JSON.stringify(err));
      resolve({ result: [], requestHeader: JSON.stringify(config), status: false });
    }
  });
}
module.exports = {
  pullRO: pullRO,
};
