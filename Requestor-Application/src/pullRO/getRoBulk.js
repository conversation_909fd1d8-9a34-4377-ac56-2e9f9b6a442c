"use strict";

const Spinner = require("cli-spinner").Spinner;
var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
var allClosedROResponse;
var segmentInfo;
const axios = require("axios");
var qs = require('qs');
const constants = require("../constants");
var result = require("dotenv").config({ path: constants.ENV_PATH });
const getOperationId = require('./generateOperationId');
const { option } = require("commander");
/**
 *  This is the Primary function responsible for pulling all closed RO's
 *
 */
async function pullROBULK(options) {
  segmentInfo = JSON.stringify({
    StoreID: options.storeID,
    Franchise: options.franchise,
  });
  util.saveSegment("executing pullOpenRO", segmentInfo);
  console.log("options.startDate@@@@@@@@@@@@@@@@@@@@@@@@@",options.startDate);
  console.log("options.endDate@@@@@@@@@@@@@@@@@@@@@@@@@",options.endDate);
  let operationId = await getOperationId.generateoperationIdForRobulk(options.token,options.startDate,options.endDate,options.dealerID,options.thirdPartyUsername);
  console.log(operationId);
  console.log(`pullROBULK........................${JSON.stringify(operationId)}`);
  console.log(`pullROBULK........................`); 
  const operationStatus = operationId.data?.status;
  const statusUrl = operationId.data._links?.status?.href;
  if (operationStatus != 'received') {
    console.log("pullROBULK  OPERATION ID NOT FOUND!");
    return {
      status: false,
      result: operationId.data.message,
      requestHeader: operationId.requestHeader,
    }; 
  }
  if (operationId.data.status == 'received') {
    options.operationId = operationId.data;
    statusUrl = operationId.data.data._links.status.href;

  }
let attemptCount = 0;
const maxAttempts = 3;

while (attemptCount < maxAttempts) {
  attemptCount++;
   try {
    console.log(`Waiting 30 seconds before the ${attemptCount} attempt to check the API status of the request${statusUrl}`);
    util.saveSegment(`Waiting 30 seconds before the ${attemptCount} attempt to check the API status of the request${statusUrl}`);
    await util.sleep(30000); //Waiting 30 seconds for status api check
    const response = await util.checkApiStatus(options,statusUrl); //Api used to check the status of the result api.30 seconds delay recommended by  fortellis
    console.log(`${attemptCount} checkApiStatus response:${JSON.stringify(response)}`);
    util.saveSegment(`${attemptCount} checkApiStatus response:${JSON.stringify(response)}`);
    if (response.result.status === 'complete') {
      console.log('Status is complete, stopping further attempts.');
      util.saveSegment('Status is complete, stopping further attempts.')
      let resultUrl = response.result._links.result.href;
      util.saveSegment(`fetched result url${resultUrl}`);
      const allClosedROResponse = await pullROBULKData(options,resultUrl); //Api used to retrive the actual data
      return allClosedROResponse;
    }

  } catch (error) {
    console.error('Error calling API:', error);
  }

  if (attemptCount < maxAttempts) {
    console.log("Waiting for the next attempt...");
    util.saveSegment(`Waiting for the next attempt...`);
    
  }else{
    util.saveSegment(`Maximum attempts to get the status from the API reached!...`);
    return {
      status: false,
      result: `Maximum attempts to get the status from the API reached: ${response.result.status }`,
      requestHeader: operationId.requestHeader,
    };     
  }
}
  // console.log("Operation id for ro bulk",operationId);
  // options.operationId = operationId;
  // return (allClosedROResponse = await pullROBULKData(options));
}
/**
 * Fetch all closed RO'S from DealerTrack server
 */
function pullROBULKData(options,resultUrl) {
  console.log('Start date:', options.startDate);
  console.log('End date:', options.endDate);
  console.log('Current date', options.currentDate);
  console.log('Token :',  options.token);
  options.startDate = util.convertDate(options.startDate);
  options.endDate = util.convertDate(options.endDate);
  console.log("Operation ID$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",options.operationId);
  return new Promise((resolve, reject) => {
    if (options.operationId.status == false) {
      console.log('testing', `Fetching Opertaion Id Failed ${options.operationId.data}`);
      reject({ result: `ERROR IN Fetching Opertaion Id: ${options.operationId.data}`, staus: false })
    }
    let token = "Bearer " + options.token;
    const config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: resultUrl,
      headers: {
        'Request-Id': constants.FORTELLIS_UNIQUE_ID,
        'Subscription-Id': constants.FORTELLIS_UNIQUE_ID,
        'Authorization': token
      }
    };
    console.log("config pullROBULKData", config);
    try {
      if(options.operationId.status ==false){
        console.log('testing',`Fetching Opertaion Id Failed ${options.operationId.data}`);
        reject({result:`ERROR IN Fetching Opertaion Id: ${options.operationId.data}`,staus:false})
      }     
      let token = "Bearer " + options.token;  
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: resultUrl,
        headers: { 
          'Request-Id': options.thirdPartyUsername, 
          'Subscription-Id': options.thirdPartyUsername, 
          'Authorization': token
        }
      };
      console.log("config pullROBULKData",config);
      axios(config)
        .then(function (response) {
          resolve({
            result: JSON.stringify(response.data),
            status: true,
            requestHeader: JSON.stringify(config),
            responseHeader: "",
          });
        })
        .catch(function (error) {
          resolve({ result: error.response.statusText, requestHeader: JSON.stringify(config), status: false });
        });
    } catch (err) {
      console.log(JSON.stringify(err));
      resolve({ result: [], requestHeader: JSON.stringify(config), status: false });
    }
  });
}
module.exports = {
    pullROBULK: pullROBULK,
};
