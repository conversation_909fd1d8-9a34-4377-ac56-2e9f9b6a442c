"use strict";

const Spinner = require("cli-spinner").Spinner;
const parser = require("xml2json");
const fs = require("fs");
const moment = require("moment-timezone");

var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");

var errObject = [],
  objRO = [];
var allClosedROResponse;
var tempRequestHeader, tempResponseHeader;
var segmentInfo;

const axios = require("axios");
var qs = require('qs');
var data = qs.stringify({
   
});
const constants = require("../constants");
var result = require("dotenv").config({ path: constants.ENV_PATH });

const logger = require("../logManager");
const LOGLEVEL = logger.LOGLEVEL;
const appLogger = logger.getApplicationLogger;
const operationIdGenerator = require('./generateOperationId');

/**
 *  This is the Primary function responsible for pulling all closed RO's
 *
 */

const generateoperationIdForRO = async (options) =>{
  console.log("options::::::::::::::::::::::::::::::::;",options.startDate)
  let operationId = await operationIdGenerator.generateoperationId(options.token,options.startDate,options.endDate,options.dealerID,options.thirdPartyUsername);
  if(operationId.status){
     return operationId.data;
  }
}

async function pullRO(options) {
  console.log("options::::::::::::::::::::::::::::::::;",options.startDate)
  // console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>PULL RO DATA optionsdf",options);

  let operationId = await generateoperationIdForRO(options);
  options.operationId = operationId;
  segmentInfo = JSON.stringify({
    StoreID: options.operationId,
    Franchise: options.franchise,
  });
  util.saveSegment("executing pullOpenRO", segmentInfo);
  return (allClosedROResponse = await pullROData(options));
}
/**
 * Fetch all closed RO'S from DealerTrack server
 */





function pullROData(options) {
   


   options.startDate = util.convertDate(options.startDate);
   options.endDate = util.convertDate(options.endDate);

  return new Promise((resolve,reject) => {
      let token = "Bearer " + options.token;
      // let config = {
      //   method: 'get',
      //   maxBodyLength: Infinity,
      //   url: `https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v1/long-operations/${options.operationId}/result`,
      //   headers: { 
      //     'Request-Id': '7712415574554', 
      //     'Accept': 'application/json', 
      //     'Subscription-Id': '86232467-2c5f-438d-a77d-88f1e94c6386', 
      //     'Authorization': token
      //   }
      // };    
      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/${options.operationId}}/result`,
        headers: { 
          'Request-Id': options.thirdPartyUsername, 
          'Subscription-Id': options.thirdPartyUsername, 
          'Authorization': token
        }
      };
      console.log("config pullROData",config);
      if(options.operationId == undefined){
        reject({
          result: [] ,
          status: true,
          requestHeader: JSON.stringify(config),
          responseHeader: "Operation id is not availbale",
        });
      }
      try {
        axios(config)
      .then(function (response) {
          resolve({
            result:JSON.stringify(response.data) ,
            status: true,
            requestHeader: JSON.stringify(config),
            responseHeader: "",
          });
      //   })
      })
      .catch(function (error) {
        resolve({ result: error, status: false,requestHeader: JSON.stringify(config) });
      });
    } catch (err) {
      // process.exit(0);
      resolve({ result: [], status: false ,requestHeader: JSON.stringify(config)});
    }
  });
}

module.exports = {
  pullRO: pullRO,
};
