"use strict";

const Spinner = require("cli-spinner").Spinner;
var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
var allClosedROResponse;
var segmentInfo;
const axios = require("axios");
const constants = require("../constants");
const getOperationId = require('./generateOperationId');
const { option } = require("commander");

/**
 *  This is the Primary function responsible for pulling all closed RO's
 *
 */
async function pullOpenRO(options) {
  const segmentInfo = JSON.stringify({
    StoreID: options.storeID,
    Franchise: options.franchise,
  });
  util.saveSegment("executing pullOpenRO", segmentInfo);

  // Fetch the operation ID for open RO
  const operationId = await getOperationId.generateoperationIdForOpenRO(
    options.token,
    options.startDate,
    options.endDate,
    options.dealerID,
    options.thirdPartyUsername
  );
  console.log("PULL OPEN RO OPERATION ID@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",operationId);

  const operationStatus = operationId.data.status;
  const statusUrl = operationId.data._links?.status?.href;

  util.saveSegment(`Fetch operation id status: ${operationStatus}`);
  console.log(`Fetch operation id status: ${operationStatus}`);

  if (operationStatus != 'received') {
    console.log("OPERATION ID NOT FOUND!");
    return {
      status: false,
      result: `ERROR IN Fetching Operation Id: ${operationId.data.status}`,
      requestHeader: operationId.requestHeader,
    }; 
  }

  util.saveSegment(`Fetched status URL: ${statusUrl}`);
  console.log(`Fetched status URL: ${statusUrl}`);

  const maxAttempts = 3;
  let attempt = 1;
  let response;
  while (attempt <= maxAttempts) {
    try {
      console.log(`Waiting 30 seconds before attempt ${attempt} to check API status: ${statusUrl}`);
      util.saveSegment(`Waiting 30 seconds before attempt ${attempt} to check API status: ${statusUrl}`);
      
      await util.sleep(30000); // Wait for 30 seconds before retry
       response = await util.checkApiStatus(options, statusUrl);
      const responseStatus = response.result.status;
      
      console.log(`${attempt} response: ${responseStatus}`);
      util.saveSegment(`${attempt} response: ${responseStatus}`);
      
      if (responseStatus == 'complete') {
        console.log('Status is complete, stopping further attempts.');
        util.saveSegment('Status is complete, stopping further attempts.');
        
        const resultUrl = response.result._links.result.href;
        util.saveSegment(`Fetched result URL: ${resultUrl}`);
        
        const allClosedROResponse = await pullOpenROData(options, resultUrl);
        return allClosedROResponse;
      }
    } catch (error) {
      console.error('Error calling API:', error);
      util.saveSegment(`Error calling API: ${error.message}`);
    }
    attempt++;    
    if (attempt <= maxAttempts) {
      console.log("Waiting for the next attempt...");
      util.saveSegment("Waiting for the next attempt...");
    } else {
      util.saveSegment("Maximum attempts to get the status from the API reached.");
      return {
        status: false,
        result: `Maximum attempts to get the status from the API reached: ${response.result.status }`,
        requestHeader: operationId.requestHeader,
      }; 
    }
  }
  	console.log("All attempts completed without a 'complete' status.");
	util.saveSegment("All attempts completed without a 'complete' status.");  	
}
function pullOpenROData(options,resultUrl) {
  options.startDate = util.convertDate(options.startDate);
  options.endDate = util.convertDate(options.endDate);
  return new Promise((resolve,reject) => {
      let token = "Bearer " + options.token;       
      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: resultUrl,
        headers: {
          'Subscription-Id': options.thirdPartyUsername, 
          'Request-Id': options.thirdPartyUsername, 
          'Authorization': token
        }
      };
      console.log("config pullOpenROData",config);
    try {
      axios(config)
      .then(function (response) {
             resolve({
            result:JSON.stringify(response.data),
            status: true,
            requestHeader: JSON.stringify(config),
            responseHeader: "",
          });
      })
      .catch(function (error) {
       resolve({ result: error.response.statusText, requestHeader: JSON.stringify(config), status: false });
      });
    } catch (err) {
      console.log(JSON.stringify(err));
      reject({ result: [], requestHeader: JSON.stringify(config), status: false });
    }
  });
}
module.exports = {
  pullOpenRO: pullOpenRO,
};
