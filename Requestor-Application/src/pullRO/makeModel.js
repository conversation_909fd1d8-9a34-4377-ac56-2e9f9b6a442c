"use strict";

const Spinner = require("cli-spinner").Spinner;
const parser = require("xml2json");
const fs = require("fs");
const moment = require("moment-timezone");

var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
var allClosedROResponse;
var segmentInfo;
const axios = require("axios");
var qs = require('qs');
const constants = require("../constants");
const getOperationId = require('./generateOperationId');
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 *  This is the Primary function responsible for pulling all closed RO's
 *
 */
async function pullMakeModel(options) {
  let operationDetails = await getOperationId.generateoperationIdForMakeModel(options.token,options.dealerID,options.thirdPartyUsername);
  const operationStatus = operationDetails.data.status;
  const statusUrl = operationDetails.data._links?.status?.href;
  util.saveSegment(`Fetched status URL: ${statusUrl}`);
  console.log(`Fetched status URL: ${statusUrl}`);
  util.saveSegment(`Fetch operation id status: ${operationStatus}`);
  console.log(`Fetch operation id status: ${operationStatus}`);
  if (operationStatus !== 'received' || !statusUrl) {
    console.log("OPERATION ID NOT FOUND!");
    return {
      status: false,
      result: `ERROR IN Fetching Operation Id: ${operationDetails.data.status}`,
      requestHeader: operationDetails.requestHeader,
    }; 
  }
  const maxAttempts = 3;
  let attempt = 1;
  while (attempt <= maxAttempts) {
    try {
      console.log(`Waiting 30 seconds before attempt ${attempt} to check API status: ${statusUrl}`);
      util.saveSegment(`Waiting 30 seconds before attempt ${attempt} to check API status: ${statusUrl}`);
      await sleep(30000); // Wait for 30 seconds before retry
      const response = await checkApiStatus(options, statusUrl);
      const responseStatus = response.result.status;
      console.log(`${attempt} response: ${responseStatus}`);
      util.saveSegment(`${attempt} response: ${responseStatus}`);
      if (responseStatus === 'complete') {
        console.log('Status is complete, stopping further attempts.');
        util.saveSegment('Status is complete, stopping further attempts.');
        const resultUrl = response.result._links.result.href;
        util.saveSegment(`Fetched result URL: ${resultUrl}`);
        const allClosedROResponse = await pullMakeModelData(options, resultUrl);
        return allClosedROResponse;
      }else {
        return response
      }
    } catch (error) {
      console.error('Error calling API:', error);
      util.saveSegment(`Error calling API: ${error.message}`);
    }
    attempt++;
    if (attempt <= maxAttempts) {
      console.log("Waiting for the next attempt...");
      util.saveSegment("Waiting for the next attempt...");
    } else {
      util.saveSegment("Maximum attempts to get the status from the API reached.");
    }
  }
  console.log("All attempts completed without a 'complete' status.");
  util.saveSegment("All attempts completed without a 'complete' status.");
  return {
    status: false,
    result: `Maximum attempts to get the status from the API reached: ${response.result.status }`,
    requestHeader: operationId.requestHeader,
  }; 
}
/**
 * Fetch all closed RO'S from DealerTrack server
 */
function pullMakeModelData(options,resultUrl) {
  options.startDate = util.convertDate(options.startDate);
  options.endDate = util.convertDate(options.endDate);
  return new Promise((resolve,reject) => {
      let token = "Bearer " + options.token;
      let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: resultUrl,
        headers: {
          'Subscription-Id': options.thirdPartyUsername, 
          'Accept': 'application/json', 
          'Request-Id': options.thirdPartyUsername, 
          'Authorization': token
        }
      };
      console.log("config pullMakeModelData",config);
    try {
      axios(config)
      .then(function (response) {
             resolve({
            result:JSON.stringify(response.data),
            status: true,
            requestHeader: JSON.stringify(config),
            responseHeader: "",
          });
      })
      .catch(function (error) {
       resolve({ result: error.response.statusText, requestHeader: JSON.stringify(config), status: false });
      });
    } catch (err) {
      console.log(JSON.stringify(err));
      reject({ result: [],requestHeader: JSON.stringify(config),  status: false });
    }
  });
}
function checkApiStatus(options, statusUrl) {
  return new Promise(async (resolve) => {
    const token = "Bearer " + options.token;
    const config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: statusUrl,
      headers: { 
        'Subscription-Id': constants.FORTELLIS_UNIQUE_ID, 
        'Request-Id': constants.FORTELLIS_UNIQUE_ID, 
        'Authorization': token
      }
    };

    try {
      const token = "Bearer " + options.token;
      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: statusUrl,
        headers: { 
          'Subscription-Id': options.thirdPartyUsername, 
          'Request-Id': options.thirdPartyUsername, 
          'Authorization': token
        }
      };
      const response = await axios(config); // Using async/await to handle the API call
      resolve({
        result: response.data,
        status: true,
        requestHeader: JSON.stringify(config), // Always include the request header
        responseHeader: JSON.stringify(response.headers), // Include response headers if needed
      });
    } catch (error) {
      console.error("Error in API call:", error); // Logging the error for debugging
      resolve({
        result: error.response?.statusText || 'Unknown error',
        status: false,
        requestHeader: JSON.stringify(config), // Include the request header even in error case
        responseHeader: JSON.stringify(error.response?.headers || ""), // Include response headers if available
      });
    }
  });
}
module.exports = {
    pullMakeModel: pullMakeModel,
};
