"use strict";

const Spinner = require("cli-spinner").Spinner;
const parser = require("xml2json");
var spinner = new Spinner("processing.. %s");
spinner.setSpinnerString("|/-\\");
var util = require("../util");
var allClosedROResponse;
var segmentInfo;
const axios = require("axios");
const constants = require("../constants");
const getOperationId = require('./generateOperationId');
/**
 *  This is the Primary function responsible for pulling all closed RO's
 *
 */

async function pullEmployee(options) {
  segmentInfo = JSON.stringify({
    StoreID: options.storeID,
    Franchise: options.franchise,
  });
  util.saveSegment("Executing employee pull", segmentInfo);
  let operationId = await getOperationId.generateoperationIdForEmployee(options.token,options.dealerID,options.thirdPartyUsername);//generating operation id for fetching employee data
  util.saveSegment(`Fetch operation  id status${operationId.data.status}`);
  console.log(`Fetch operation  id status${operationId.data.status}`);
  util.saveSegment(`Fetched status url ${operationId.data._links.status.href}`);
  console.log(`Fetch operation  id status${operationId.data.status}`);
  console.log(`Fetched status url ${operationId.data._links.status.href}`);
   let statusUrl;
    if(operationId.data.status == 'received'){
       statusUrl = operationId.data._links.status.href;
  
    }else{
       console.log("OPERATION ID NOT FOUND!!!!");
       return {
        status: false,
        result: `ERROR IN Fetching Operation Id: ${operationId.data.status}`,
        requestHeader: operationId.requestHeader,
      };   
    }
let attemptCount = 0;
const maxAttempts = 3;

while (attemptCount < maxAttempts) {
  attemptCount++;
   try {
    console.log(`Waiting 30 seconds before the ${attemptCount} attempt to check the API status of the request${statusUrl}`);
    util.saveSegment(`Waiting 30 seconds before the ${attemptCount} attempt to check the API status of the request${statusUrl}`);
    await util.sleep(30000); //Waiting 30 seconds for status api check
    const response = await util.checkApiStatus(options,statusUrl); //Api used to check the status of the result api.30 seconds delay recommended by  fortellis
    console.log(`${attemptCount} response:${response}`);
    util.saveSegment(`${attemptCount} response:${response}`);
    if (response.result.status === 'complete') {
      console.log('Status is complete, stopping further attempts.');
      util.saveSegment('Status is complete, stopping further attempts.')
      let resultUrl = response.result._links.result.href;
      util.saveSegment(`fetched result url${resultUrl}`);
      const allClosedROResponse = await pullEmployeeData(options,resultUrl); //Api used to retrive the actual data
      return allClosedROResponse;
    }else {
      return response
    }

  } catch (error) {
    console.error('Error calling API:', error);
  }

  if (attemptCount < maxAttempts) {
    console.log("Waiting for the next attempt...");
    util.saveSegment(`Waiting for the next attempt...`);
    
  }else{
    util.saveSegment(`Maximum attempts to get the status from the API reached!...`);
    return {
      status: false,
      result: `Maximum attempts to get the status from the API reached: ${response.result.status }`,
      requestHeader: operationId.requestHeader,
    }; 
    
  }
}
  
}


function pullEmployeeData(options,resultUrl) {
  options.startDate = util.convertDate(options.startDate);
  options.endDate = util.convertDate(options.endDate);
  return new Promise((resolve,reject) => {
      let token = "Bearer " + options.token;       
      const config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: resultUrl,
        headers: { 
          'Subscription-Id': options.thirdPartyUsername, 
          'Request-Id': options.thirdPartyUsername, 
          'Authorization': token
        }
      };  
      console.log("config pullEmployeeData",config);
    try {
      axios(config)
        .then(function (response) {
          resolve({
            result: JSON.stringify(response.data),
            status: true,
            requestHeader: JSON.stringify(config),
            responseHeader: "",
          });
        })
        .catch(function (error) {
          resolve({ result: error.response.statusText, status: false,requestHeader: JSON.stringify(config) });
        });
    } catch (err) {
      console.log(JSON.stringify(err));
      reject({ result: [], status: false,requestHeader: JSON.stringify(config) });
    }
  });
}
module.exports = {
    pullEmployee: pullEmployee,
};
