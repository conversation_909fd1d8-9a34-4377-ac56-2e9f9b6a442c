#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Time::Format qw(%time %strftime %manip);
use Math::Round;

no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];
my $show_accounting_in_proxy =  $ARGV[1];
my $is_porsche_store =  $ARGV[2];
# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;

my @header_section;
my @comment_section;
my @job_section;
my @ro_fee_section;
my @ro_disc_section;
my @ro_tech_punch_section;
my @ro_account_section;
my @ro_total_section;

my ($tot_cust_lbr_cost, $tot_cust_lbr_sale, $tot_cust_prt_cost, $tot_cust_prt_sale, $tot_cust_misc_cost, $tot_cust_misc_sale, $tot_cust_gog_cost, $tot_cust_gog_sale,
    $tot_cust_sublet_cost, $tot_cust_sublet_sale, $tot_cust_ded_cost, $tot_cust_ded_sale )= (0)x12;

my ($tot_intern_lbr_cost, $tot_intern_lbr_sale, $tot_intern_prt_cost, $tot_intern_prt_sale, $tot_intern_misc_cost, $tot_intern_misc_sale, $tot_intern_gog_cost,
    $tot_intern_gog_sale, $tot_intern_sublet_cost, $tot_intern_sublet_sale, $tot_intern_ded_cost, $tot_intern_ded_sale )= (0)x12;

my ($tot_warr_lbr_cost, $tot_warr_lbr_sale, $tot_warr_prt_cost, $tot_warr_prt_sale, $tot_warr_misc_cost, $tot_warr_misc_sale, $tot_warr_gog_cost, $tot_warr_gog_sale,
    $tot_warr_sublet_cost, $tot_warr_sublet_sale, $tot_warr_ded_cost, $tot_warr_ded_sale )= (0)x12;

my ($job_cust_lbr_cost, $job_cust_lbr_sale, $job_cust_prt_cost, $job_cust_prt_sale, $job_cust_misc_cost, $job_cust_misc_sale, $job_cust_gog_cost, $job_cust_gog_sale,
    $job_cust_sublet_cost, $job_cust_sublet_sale, $job_cust_ded_cost, $job_cust_ded_sale )= (0)x12;

my ($job_intern_lbr_cost, $job_intern_lbr_sale, $job_intern_prt_cost, $job_intern_prt_sale, $job_intern_misc_cost, $job_intern_misc_sale, $job_intern_gog_cost,
    $job_intern_gog_sale, $job_intern_sublet_cost, $job_intern_sublet_sale, $job_intern_ded_cost, $job_intern_ded_sale )= (0)x12;

my ($job_warr_lbr_cost, $job_warr_lbr_sale, $job_warr_prt_cost, $job_warr_prt_sale, $job_warr_misc_cost, $job_warr_misc_sale, $job_warr_gog_cost, $job_warr_gog_sale,
    $job_warr_sublet_cost, $job_warr_sublet_sale, $job_warr_ded_cost, $job_warr_ded_sale )= (0)x12;

my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;
my $cust_discount = 0;
my $job_billing_code;
my $job_total_discount;
my ($parts_entries, $misc_entries, $gog_entries, $sublet_entries, $ded_entries, $tech_punch_entries, $account_entries) = (0)x6;

my $page_max_height = 27; # Maximum no of lines in page body
my $curr_page_height = 0;
my $invoice_note_lines = 22; # No of lines needed for invoice note
my $pages;
my $curr_page_num;

my $ro_open_void_qry = $conn->prepare(
    "WITH inv_master AS (SELECT
                        substring(ro_number FROM '^[0-9]+')    AS number_part,
                        substring(ro_number FROM '[A-Za-z]+') AS code_part
                    FROM repair_order
       )
      ,series_gap (max_ser_ro, min_ser_ro) AS (
         SELECT * FROM (SELECT
                            number_part::integer,
                            lag(number_part::integer) OVER ranges,
                            number_part::integer - lag(number_part::integer) OVER ranges AS diff,
                            code_part
                    FROM inv_master
                    WINDOW ranges AS (order by number_part::integer))t
         WHERE diff <= 100)
     SELECT ro_number
     FROM repair_order
     WHERE completion_date IS NULL
     UNION ALL
     SELECT generate_series(min_ser_ro+1, max_ser_ro-1)::text
     FROM series_gap");

$ro_open_void_qry->execute();
while (my @open_ro_list = $ro_open_void_qry->fetchrow_array) {
    ($ro_number) = @open_ro_list;
    my $file_open = $ro_number . ".txt";
    unless(open FILE, '>'.$file_open) {
        die "\nUnable to create $file_open\n";
    }
    print_open_ro();
    close FILE;
}

my $ro_qry = $conn->prepare("SELECT ro_number
                               FROM repair_order WHERE completion_date IS NOT NULL
                              ORDER BY ro_number ");

$ro_qry->execute();
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number) = @ro_list;
    my $file = $ro_number . ".txt";
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;
    load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
    my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
    $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
    $ro_doc_insert->finish;
}

# Subroutine to prepare HEADER section of the invoice
sub make_header {
    my $ro_header_qry = $conn->prepare("SELECT
                                to_char(ro.open_time, 'HH24:MI') || ' ' || to_char(ro.creation_date, 'ddMONyy') AS creation_date,
                                to_char(ro.completion_date, 'ddMONyy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                ro.advisor,
                                roc.customer_number,
                                REPLACE(roc.customer_name,',',' '),
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip,
                                CASE
                                   WHEN length(roc.home_phone) > 9 THEN SUBSTRING(roc.home_phone, 1, 3) || '-' || SUBSTRING(roc.home_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.home_phone,7,4)
                                    ELSE ''
                                END AS home_phone,
                                CASE
                                   WHEN length(roc.customer_phone) > 9 THEN SUBSTRING(roc.customer_phone, 1, 3) || '-' || SUBSTRING(roc.customer_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.customer_phone,7,4)
                                    ELSE ''
                                END AS customer_phone,
                                CASE
                                   WHEN length(roc.main_phone) > 9 THEN SUBSTRING(roc.main_phone, 1, 3) || '-' || SUBSTRING(roc.main_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.main_phone,7,4)
                                    ELSE ''
                                END AS main_phone,
                                CASE
                                   WHEN length(roc.cell_phone) > 9 THEN SUBSTRING(roc.cell_phone, 1, 3) || '-' || SUBSTRING(roc.cell_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.cell_phone,7,4)
                                    ELSE ''
                                END AS cell_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                rov.vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in,
                                SUBSTRING(rov.vehicle_color, 1, 9) AS vehicle_color,
                                rov.mileage_out,
                                roc.license_number,
                                ro.tag_no,
                                to_char(ro.delivery_date, 'ddMONyy') AS delivery_date,
                                to_char(ro.promised_time, 'HH24:MI') || ' ' || to_char(ro.promised_date, 'ddMONyy') AS promised,
                                to_char(ro.booked_time, 'HH24:MI') || ' ' || to_char(ro.booked_date, 'ddMONyy') AS ready,
                                ro.payment_code,
                                ro.comments
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
    my ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor, $customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $home_phone, $customer_phone, $main_phone, $cell_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $vehicle_color, $mileage_out, $license_number, $tag_no, $delivery_date, $promised, $ready, $payment_code, $comments ) = @header_row;
    my($customer_first_name, $customer_last_name) = split(" ", $customer_name);
    my $customer_complete_address = $customer_city.$customer_state.$customer_zip;
    if($is_porsche_store eq 'true'){
        $vehicle_vin=substr($vehicle_vin, 0, 12);
        $customer_first_name = '';
        $customer_last_name = '';
        $customer_complete_address = '';
        $customer_number = '';
        $customer_address = '';
        $home_phone = '';
        $customer_phone = '';
        $main_phone = '';
        $cell_phone = '';

    }
    push (@header_section, sprintf(border(">")."%11s %-12s %15s ".'~font{fCourier-Bold12}'."%-10s".'~font{default}'." %25s  ".border("|")."\n",
           "CUSTOMER #:", $customer_number, "", $ro_number, ""));
    push (@header_section, sprintf(border("#")."%78s  ".border("|")."\n", ""));
    push (@header_section, sprintf(border("#")."%39s%-41s".border("|")."\n","", "ACCOUNTING"));
    push (@header_section, sprintf(border(">")."%-78s  ".border("|")."\n", $customer_last_name." ".$customer_first_name));

    push (@header_section, sprintf(border(">")."%-50s%-28s  ".border("|")."\n",
           $customer_address, ""));
    if ($customer_complete_address) {
        push (@header_section, sprintf(border(">")."%-41s%-8s%-29s  ".border("|")."\n",
            $customer_city.", ".$customer_state." ".$customer_zip, "PAGE <--pg_num-->", ""));
    } 
    else {
        push (@header_section, sprintf(border(">")."%-41s%-8s%-29s  ".border("|")."\n",
            "", "PAGE <--pg_num-->", ""));
    }
    push (@header_section, sprintf(border(">")."%5s%-12s%6s%-14s%-41s  ".border("|")."\n",
           "HOME:", $home_phone, "CONT:", $customer_phone, ""));
    push (@header_section, sprintf(border(">")."%-5s%-12s%6s%-30s%-10s%-25s  ".border("|")."\n",
           "BUS:", $main_phone, "CELL:", $cell_phone, "SERVICE ADVISOR:", $advisor));
    push (@header_section, sprintf(border("#")."%78s  ".border("|")."\n", ""));

    # Trim Make/Model to 20 chars.
    # eg: "CHEVROLET SILVERADO 3500" -> will trim to "CHEVROLET SILVERADO", i.e, 20 chars.
    push (@header_section, sprintf(border(">")."%-9s %-4s%-20s%-18s%-8s%-14s%-6s".border("|")."\n",
           $vehicle_color, substr($vehicle_year, -2), substr($vehicle_make." ".$vehicle_model, 0, 20), $vehicle_vin,
           $license_number, $mileage_in."/".$mileage_out, substr($tag_no, 0, 6) ));

    push (@header_section, sprintf(border("#")."%78s  ".border("|")."\n", "")); # Separator Line
    push (@header_section, sprintf(border("#")."%78s  ".border("|")."\n", "")); # Separator Line

    push (@header_section, sprintf(border(">")."%-10s%16s%-15s%19s%-8s%-10s  ".border("|")."\n",
           $delivery_date, "", $promised, "", $payment_code,  $completion_date));

    push (@header_section, sprintf(border("#")."%78s  ".border("|")."\n", ""));

    push (@header_section, sprintf(border(">")."%-13s %-13s %50s  ".border("|")."\n",
          $creation_date, $ready, ""));

 push (@header_section, sprintf(border(">")."%-5s%-7s%-5s%-5s%-6s%-6s%-3s%-8s%-6s%-8s%-8s%-6s%-6s".border("|")."\n",
           "LINE", "OPCODE", "TECH", "TYPE", "A/HRS",  "S/HRS","", "COST", "SALE", "COMP", "", "NET", "TOTAL"));

    # Comment Section
    $Text::Wrap::columns = 31;
    my $wrapped_comment = fill('', '', expand($comments));
    my @comment_list = split "\n", $wrapped_comment;
    foreach my $i (0 .. $#comment_list) {
        push (@comment_section, sprintf(border(">")."%-2s%-31s%45s".border("|")."\n", " ", $comment_list[$i], ""));
    }

}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {
    my $job_qry = $conn->prepare("SELECT DISTINCT
                                    rl.ro_line,
                                    rl.complaint,
                                    rl.add_on_flag,
                                    rj.cause,
                                    rj.correction
                                FROM repair_line rl
                                    LEFT JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ?
                                ORDER BY rl.ro_line");

    $job_qry->execute($ro_number);
    my ( $ro_line, $complaint, $add_on_flag, $cause, $correction );
    while (my @job_row = $job_qry->fetchrow_array) {
        ( $ro_line, $complaint, $add_on_flag, $cause, $correction ) = @job_row;
        my @job_header_section;

        # Job Complaint
        $Text::Wrap::columns = 67;
        my $wrapped_complaint = fill('', '', expand($complaint));
        my @complaint_list = split "\n", $wrapped_complaint;

        push (@job_header_section, sprintf(border(">")."%-71s%7s".border("|")."\n",$ro_line.($add_on_flag?"** ":" ").$complaint_list[0], ""));
        foreach my $i (1 .. $#complaint_list) {
            push (@job_header_section, sprintf(border(">")."%-2s%-67s%9s".border("|")."\n", " ", $complaint_list[$i], ""));
        }
        push (@job_section, [@job_header_section]);

        # Job Cause
        if ($cause){
            my @job_cause_section;
            $Text::Wrap::columns = 61;
            my $wrapped_cause = fill('', '', expand($cause));
            my @cause_list = split "\n", $wrapped_cause;
            push (@job_cause_section, sprintf(border(">")."%-7s%-61s%10s".border("|")."\n", "CAUSE: ", $cause_list[0], ""));
            foreach my $i (1 .. $#cause_list) {
                push (@job_cause_section, sprintf(border(">")."%7s%-61s%10s".border("|")."\n", "", $cause_list[$i], ""));
            }
            push (@job_section, [@job_cause_section]);
        }

        ($job_cust_lbr_cost, $job_cust_lbr_sale, $job_cust_prt_cost, $job_cust_prt_sale, $job_cust_misc_cost, $job_cust_misc_sale, $job_cust_gog_cost, $job_cust_gog_sale,
         $job_cust_sublet_cost, $job_cust_sublet_sale, $job_cust_ded_cost, $job_cust_ded_sale )= (0)x12;

        ($job_intern_lbr_cost, $job_intern_lbr_sale, $job_intern_prt_cost, $job_intern_prt_sale, $job_intern_misc_cost, $job_intern_misc_sale, $job_intern_gog_cost,
         $job_intern_gog_sale, $job_intern_sublet_cost, $job_intern_sublet_sale, $job_intern_ded_cost, $job_intern_ded_sale )= (0)x12;

        ($job_warr_lbr_cost, $job_warr_lbr_sale, $job_warr_prt_cost, $job_warr_prt_sale, $job_warr_misc_cost, $job_warr_misc_sale, $job_warr_gog_cost, $job_warr_gog_sale,
         $job_warr_sublet_cost, $job_warr_sublet_sale, $job_warr_ded_cost, $job_warr_ded_sale )= (0)x12;

        my $lbr_qry = $conn->prepare("SELECT DISTINCT ON (rj.ro_job_line)
                                        rj.ro_job_line,
                                        left(rj.billing_code, 1) AS billing_code,
                                        rj.billing_code          AS billing_labor_type,
                                        rj.op_code,
                                        rj.op_description,
                                        rj.sold_hours,
                                        rj.actual_hours,
                                        rj.labor_cost,
                                        rj.sale_amount,
                                        COUNT(*) OVER (Partition BY rj.ro_number, rj.ro_line,
                                                 rj.ro_job_line) AS lbr_count
                                FROM repair_job rj
                                WHERE rj.ro_number = ? AND rj.ro_line = ?
                                ORDER BY rj.ro_job_line");

        $lbr_qry->execute($ro_number, $ro_line);
        my ( $ro_job_line, $billing_labor_type, $op_code, $op_description , $sold_hours,
             $actual_hours, $labor_cost, $sale_amount, $lbr_count );
        while (my @lbr_row = $lbr_qry->fetchrow_array) {
            ( $ro_job_line, $job_billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours,
             $actual_hours, $labor_cost, $sale_amount, $lbr_count ) = @lbr_row;

            my $lbr_billing;
            my @job_labor_section;

            # Job Code, description
            if ($op_code || $op_description){
                $Text::Wrap::columns = 63;
                $op_description = $op_code." ".$op_description;
                my $wrapped_op_description = fill('', '', expand($op_description));
                my @op_description_list = split "\n", $wrapped_op_description;
                push (@job_labor_section, sprintf(border(">")."%5s%-63s%10s".border("|")."\n", "", $op_description_list[0], ""));
                foreach my $i (1 .. $#op_description_list) {
                    push (@job_labor_section, sprintf(border(">")."%5s%-63s%10s".border("|")."\n", "", $op_description_list[$i], ""));
                }
            }

            if ($lbr_count == 10000)
            {
                if (lc($job_billing_code) eq 'i'){
                    $job_intern_lbr_sale = $job_intern_lbr_sale + $sale_amount;
                    $job_intern_lbr_cost = $job_intern_lbr_cost + $labor_cost;
                } elsif (lc($job_billing_code) eq 'c'){
                    $job_cust_lbr_sale = $job_cust_lbr_sale + $sale_amount;
                    $job_cust_lbr_cost = $job_cust_lbr_cost + $labor_cost;
                } elsif (lc($job_billing_code) eq 'w'){
                    $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                    $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                }

                my @tech_section = make_tech_section($ro_line, $ro_job_line);
                push (@job_labor_section, @tech_section);

                push (@job_labor_section, sprintf(border(">")."%2s%-5s  %5.2f %5.2f %9d %9d%6s %8.2f %8.2f ".border("|")."\n",
                                            "", $billing_labor_type, $actual_hours, $sold_hours, $labor_cost*100,
                                            $sale_amount*100, "", $sale_amount, $sale_amount));
            }else{
                my $lbr_detail_qry = $conn->prepare("SELECT
                                                        left(rj.billing_code, 1) AS billing_code,
                                                        rj.billing_code          AS billing_labor_type,
                                                        coalesce(nullif(rjt.booked_hours, 0)::numeric, nullif(rj.sold_hours, 0)::numeric, 0),
                                                        coalesce(nullif(rjt.actual_hours, 0)::numeric, nullif(rj.actual_hours, 0)::numeric, 0),
                                                        coalesce(nullif(rjt.tech_lbr_cost, 0)::numeric, nullif(rj.labor_cost, 0)::numeric, 0),
                                                        coalesce(nullif(rjt.tech_lbr_sale, 0)::numeric, nullif(rj.sale_amount, 0)::numeric, 0),
                                                        rjt.tech_id
                                                    FROM repair_job rj
                                                        LEFT JOIN repair_job_technician_detail rjt
                                                            ON rjt.ro_number = rj.ro_number
                                                               AND rjt.ro_line = rj.ro_line
                                                               AND rjt.ro_job_line = rj.ro_job_line
                                                               AND rjt.tech_billing_type = rj.billing_code
                                                    WHERE rj.ro_number = ? AND rj.ro_line = ? AND rj.ro_job_line = ?
                                                    ORDER BY rj.ro_job_line");
                $lbr_detail_qry->execute($ro_number, $ro_line, $ro_job_line);
                while ( my @lbr_detail_row = $lbr_detail_qry->fetchrow_array){
                    my $job_tech_id;
                    ( $job_billing_code, $billing_labor_type, $sold_hours,
                    $actual_hours, $labor_cost, $sale_amount, $job_tech_id ) = @lbr_detail_row;
                    push (@job_labor_section, sprintf(border(">")."%16s%5s%5.2f%6.2f%9d%8d%13s%8.2f%8.2f".border("|")."\n",
                                            $job_tech_id, $billing_labor_type, $actual_hours, $sold_hours, $labor_cost*100,
                                            $sale_amount*100, "", $sale_amount, $sale_amount));
                    if (lc($job_billing_code) eq 'i'){
                        $job_intern_lbr_sale = $job_intern_lbr_sale + $sale_amount;
                        $job_intern_lbr_cost = $job_intern_lbr_cost + $labor_cost;
                    } elsif (lc($job_billing_code) eq 'c'){
                        $job_cust_lbr_sale = $job_cust_lbr_sale + $sale_amount;
                        $job_cust_lbr_cost = $job_cust_lbr_cost + $labor_cost;
                    } elsif (lc($job_billing_code) eq 'w'){
                        $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                        $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                    }
                }
            }
            push (@job_section, [@job_labor_section]);
            my @misc_section = make_misc_section_labor($ro_line);
            push (@job_section, [@misc_section]);
            my @parts_section = make_parts_section($ro_line, $ro_job_line);
            push (@job_section, [@parts_section]);
        }

        my @misc_section = make_misc_section($ro_line);
        push (@job_section, [@misc_section]);

        my @gog_section = make_gog_section($ro_line);
        push (@job_section, [@gog_section]);

        my @sublet_section = make_sublet_section($ro_line, $billing_labor_type);
        push (@job_section, [@sublet_section]);

        my @disc_section = make_discount_section($ro_number,$ro_line);
        push (@job_section, [@disc_section]);

        my @job_total_section = make_job_total_section($ro_line);
        push (@job_section, [@job_total_section]);

        my @ded_section = make_deductible_section($ro_line);
        push (@job_section, [@ded_section]);

        my $techs_qry = "SELECT
                            string_agg(DISTINCT tech_id, ', ')
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ?";

        my ($job_techs) = $conn->selectrow_array($techs_qry, undef, ($ro_number, $ro_line));

        # Job Correction
        if ($correction){
            my @job_correction_section;
            $correction = "VERSION 1 (EMP# ".$job_techs."): ".$correction;
            $Text::Wrap::columns = 67;
            my $wrapped_correction = fill('', '', expand($correction));
            my @correction_list = split "\n", $wrapped_correction;
            push (@job_correction_section, sprintf(border(">")." %-77s".border("|")."\n", $correction_list[0]));
            foreach my $i (1 .. $#correction_list) {
                push (@job_correction_section, sprintf(border(">")." %-77s".border("|")."\n", $correction_list[$i]));
            }
            push (@job_section, [@job_correction_section]);
        }

    }
}

# Subroutine to prepare the TECH section of a JOB
sub make_tech_section{
    my ($ro_line, $ro_job_line) = @_;
    my @tech_section_array;
    my $tech_details_qry = $conn->prepare("SELECT
                                                ro_job_tech_line,
                                                tech_id,
                                                work_date,
                                                work_start_time,
                                                work_end_time,
                                                work_note,
                                                actual_hours,
                                                booked_hours
                                            FROM repair_job_technician_detail td
                                            WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
    $tech_details_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours);
    if($tech_details_qry->rows > 0){
        while (my @tech_row = $tech_details_qry->fetchrow_array) {
            ($ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours) = @tech_row;
            my $tech_detail = $tech_id; #need to add technician name and LIC#
            push (@tech_section_array, sprintf(border(">")."%11s".border("|")."",
                                                $tech_detail));
        }
    }
    return @tech_section_array;
}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line) = @_;
    my @parts_section_array;

    my $parts_qry = $conn->prepare("SELECT DISTINCT ON (ro_part_line)
                                        ro_part_line,
                                        part_number,
                                        part_description,
                                        left(nullif(prt_billing_type, 'NA'), 1)   AS prt_billing_code,
                                        nullif(prt_billing_type, 'NA')            AS prt_billing_type,
                                        quantity_sold,
                                        unit_cost,
                                        unit_sale,
                                        unit_core_cost,
                                        unit_core_sale,
                                        unit_part_list,
                                        COUNT(*) OVER (Partition BY rp.ro_number, rp.ro_line,
                                                 rp.ro_job_line, rp.ro_part_line) AS prt_count
                                    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ? AND quantity_sold != 0
                                    ORDER BY ro_part_line");
    $parts_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
         $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale, $unit_part_list, $prt_count);

    $parts_entries = $parts_qry->rows;
    if($parts_entries > 0){
        while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
              $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale, $unit_part_list,$prt_count) = @parts_row;

            $prt_billing_code = ($prt_billing_code?$prt_billing_code:$job_billing_code);
            $part_description = $part_number." ".$part_description;
            $Text::Wrap::columns = 24;
            my $wrapped_part_description = fill('', '', expand($part_description));
            my @part_description_list = split "\n", $wrapped_part_description;
            if($prt_count == 1){
            my $extended_sale = $unit_sale*$quantity_sold*100;
            my $extended_sale_round = round ( $extended_sale );
            my $extended_cost = $unit_cost*$quantity_sold*100;
            my $extended_cost_round = round ( $extended_cost );
                push (@parts_section_array, sprintf(border(">")."%3s%2s %-24s %10.0f %7.0f    %d  %7s %6.2f %7.2f ".border("|")."\n",
                                                   "", $quantity_sold, $part_description_list[0],
                                                  $extended_cost_round, $extended_sale_round, 0, "", $unit_sale, $unit_sale*$quantity_sold));
                foreach my $i (1 .. $#part_description_list) {
                    push (@parts_section_array, sprintf(border(">")."%6s%-24s%-48s".border("|")."\n", "", $part_description_list[$i], ""));
                }
                if ($unit_core_cost != 0 && $unit_core_sale != 0){
                    push (@parts_section_array, sprintf(border(">")."%3s%-27s %7.2f %7.2f    %d  %7.2f %7.2f %8.2f ".border("|")."\n",
                                                    "", "CORE CHARGE", $unit_core_cost*$quantity_sold, $unit_core_sale*$quantity_sold, 0,
                                                    $unit_core_sale, $unit_core_sale, $unit_core_sale*$quantity_sold));
                }
                if (lc($prt_billing_code) eq 'i'){
                    $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                    $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                } elsif (lc($prt_billing_code) eq 'c'){
                    $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                    $job_cust_prt_cost = $job_cust_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                } elsif (lc($prt_billing_code) eq 'w'){
                    $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                    $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                }
            }else{
                push (@parts_section_array, sprintf(border(">")."%3s%2s %-24s%48s".border("|")."\n",
                                                    "", $quantity_sold, $part_description_list[0], ""));
                foreach my $i (1 .. $#part_description_list) {
                    push (@parts_section_array, sprintf(border(">")."%6s%-24s%-48s".border("|")."\n", "", $part_description_list[$i], ""));
                }
                my $prt_detail_qry = $conn->prepare("SELECT
                                                        left(prt_billing_type, 1) AS prt_billing_code,
                                                        prt_billing_type,
                                                        quantity_sold,
                                                        unit_cost,
                                                        unit_sale,
                                                        unit_core_cost,
                                                        unit_core_sale
                                                    FROM repair_part rp
                                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ? AND quantity_sold != 0
                                                          AND ro_part_line = ?
                                                    ORDER BY ro_part_line");
                $prt_detail_qry->execute($ro_number, $ro_line, $ro_job_line, $ro_part_line);
                while ( my @prt_detail_row = $prt_detail_qry->fetchrow_array){
                    ( $prt_billing_code, $prt_billing_type, $quantity_sold, $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale ) = @prt_detail_row;

                    push (@parts_section_array, sprintf(border(">")."%18s%6s %-5s %7.2f %7.2f    %d  %7.2f %7.2f %8.2f ".border("|")."\n",
                                                    "", $prt_billing_type, "", $unit_cost*$quantity_sold, $unit_sale*$quantity_sold, 0,
                                                    "", $unit_sale, $unit_sale*$quantity_sold));
                    if ($unit_core_cost != 0 || $unit_core_sale != 0){
                        push (@parts_section_array, sprintf(border(">")."%3s%-27s %7.2f %7.2f    %d  %7.2f %7.2f %8.2f ".border("|")."\n",
                                                        "", "CORE CHARGE", $unit_core_cost*$quantity_sold, $unit_core_sale*$quantity_sold, 0,
                                                        $unit_core_sale, $unit_core_sale, $unit_core_sale*$quantity_sold));
                    }
                    if (lc($prt_billing_code) eq 'i'){
                        $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                        $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                    } elsif (lc($prt_billing_code) eq 'c'){
                        $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                        $job_cust_prt_cost = $job_cust_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                    } elsif (lc($prt_billing_code) eq 'w'){
                        $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                        $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                    }
                }
            }
        }
    }
    return @parts_section_array;
}

# Subroutine to prepare the MISC section of a JOB
sub make_misc_section_labor{
    my ($ro_line) = @_;
    my @misc_section_array;
    my $misc_qry = $conn->prepare("SELECT
                                       misc_quantity::numeric,        
                                       other_code,
                                       other_description,
                                       left(other_billing_type, 1) AS other_billing_type,
                                       other_cost,
                                       other_sale
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line =? AND item_type = 'MISC' AND misc_quantity IS NULL");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $misc_qty, $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale);
    $misc_entries = $misc_qry->rows;
    my $misc_cost_fee = 0;
    if($misc_entries > 0){
        while (my @misc_row = $misc_qry->fetchrow_array) {
            ( $misc_qty, $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale) = @misc_row;
            $Text::Wrap::columns = 28;
            $misc_description = $misc_code." ".$misc_description;
            my $wrapped_misc_description = fill('', '', expand($misc_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;

           
                if ($misc_qty > 0){
                push (@misc_section_array, sprintf(border(">")."    %-31s %7d %7d %12s %7.2f %7.2f ".border("|")."\n",
                                                             $misc_description_list[0], $misc_cost*100, $misc_sale*100, "", $misc_sale, $misc_sale));
                } else {
                push (@misc_section_array, sprintf(border(">")."  %-31s %7d %7d %12s %7.2f %7.2f ".border("|")."\n",
                                                            $misc_description_list[0], $misc_cost*100, $misc_sale*100, "", $misc_sale, $misc_sale));
                }
            foreach my $i (1 .. $#misc_description_list) {
                push (@misc_section_array, sprintf(border(">")." %-28s%-49s".border("|")."\n", $misc_description_list[$i], ""));
            }

            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $job_intern_misc_sale = $job_intern_misc_sale + $misc_sale;
                $job_intern_misc_cost = $job_intern_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 'c'){
                $job_cust_misc_sale = $job_cust_misc_sale + $misc_sale;
                $job_cust_misc_cost = $job_cust_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $job_warr_misc_sale = $job_warr_misc_sale + $misc_sale;
                $job_warr_misc_cost = $job_warr_misc_cost + $misc_cost;
            }
        }
    }
    return @misc_section_array;
}
sub make_misc_section{
    my ($ro_line) = @_;
    my @misc_section_array;
    my $misc_qry = $conn->prepare("SELECT
                                       misc_quantity::numeric,        
                                       other_code,
                                       other_description,
                                       left(other_billing_type, 1) AS other_billing_type,
                                       other_cost,
                                       other_sale
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line =? AND item_type = 'MISC'  AND misc_quantity IS NOT NULL");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $misc_qty, $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale);
    $misc_entries = $misc_qry->rows;
    my $misc_cost_fee = 0;
    if($misc_entries > 0){
        while (my @misc_row = $misc_qry->fetchrow_array) {
            ( $misc_qty, $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale) = @misc_row;
            if ($misc_qty > 0){
              $misc_cost_fee = $misc_sale/$misc_qty; 
            }
            $Text::Wrap::columns = 28;
            $misc_description = $misc_code." ".$misc_description;
            my $wrapped_misc_description = fill('', '', expand($misc_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;

           
                if ($misc_qty > 0){
                push (@misc_section_array, sprintf(border(">")."    %-2d%-27s %7d %7d %12s %7.2f %7.2f ".border("|")."\n",
                                                            $misc_qty, $misc_description_list[0], $misc_cost*100, $misc_sale*100, "", $misc_cost_fee, $misc_sale));
                } else {
                push (@misc_section_array, sprintf(border(">")."  %-31s %7d %7d %12s %7.2f %7.2f ".border("|")."\n",
                                                            $misc_description_list[0], $misc_cost*100, $misc_sale*100, "", $misc_sale, $misc_sale));
                }
            foreach my $i (1 .. $#misc_description_list) {
                push (@misc_section_array, sprintf(border(">")." %-28s%-49s".border("|")."\n", $misc_description_list[$i], ""));
            }

            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $job_intern_misc_sale = $job_intern_misc_sale + $misc_sale;
                $job_intern_misc_cost = $job_intern_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 'c'){
                $job_cust_misc_sale = $job_cust_misc_sale + $misc_sale;
                $job_cust_misc_cost = $job_cust_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $job_warr_misc_sale = $job_warr_misc_sale + $misc_sale;
                $job_warr_misc_cost = $job_warr_misc_cost + $misc_cost;
            }
        }
    }
    return @misc_section_array;
}

# Subroutine to prepare the GOG section of a JOB
sub make_gog_section{
    my ($ro_line) = @_;
    my @gog_section_array;
    my $gog_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    left(other_billing_type, 1) AS other_billing_type,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'GOG'");
    $gog_qry->execute($ro_number, $ro_line);
    my ( $gog_code, $gog_description, $other_billing_type, $gog_cost, $gog_sale);

    $gog_entries = $gog_qry->rows;
    if($gog_qry->rows > 0){

        while (my @gog_row = $gog_qry->fetchrow_array) {
            ( $gog_code, $gog_description, $other_billing_type, $gog_cost, $gog_sale) = @gog_row;
            $Text::Wrap::columns = 28;
            $gog_description = $gog_code." ".$gog_description;
            my $wrapped_gog_description = fill('', '', expand($gog_description));
            my @gog_description_list = split "\n", $wrapped_gog_description;
            push (@gog_section_array, sprintf(border(">")."  %-31s %7d %7d %12s %7.2f %7.2f ".border("|")."\n",
                                               $gog_description_list[0], $gog_cost*100, $gog_sale*100, "", $gog_cost, $gog_sale));

            foreach my $i (1 .. $#gog_description_list) {
                push (@gog_section_array, sprintf(border(">")." %-28s%-49s".border("|")."\n", "", $gog_description_list[$i], ""));
            }
            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $job_intern_gog_sale = $job_intern_gog_sale + $gog_sale;
                $job_intern_gog_cost = $job_intern_gog_cost + $gog_cost;
            } elsif (lc($other_billing_type) eq 'c'){
                $job_cust_gog_sale = $job_cust_gog_sale + $gog_sale;
                $job_cust_gog_cost = $job_cust_gog_cost + $gog_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $job_warr_gog_sale = $job_warr_gog_sale + $gog_sale;
                $job_warr_gog_cost = $job_warr_gog_cost + $gog_cost;
            }
        }
    }
    return @gog_section_array;
}

# Subroutine to prepare the SUBLET section of a JOB
sub make_sublet_section{
    my ($ro_line, $billing_code) = @_;
    my @sublet_section_array;
    my $sublet_qry = $conn->prepare("SELECT
                                        other_code,
                                        other_description,
                                        left(other_billing_type, 1) AS other_billing_type,
                                        other_cost,
                                        other_sale
                                    FROM repair_other
                                    WHERE ro_number = ? AND ro_line =? AND item_type = 'SUBLET'");
    $sublet_qry->execute($ro_number, $ro_line);
    my ( $sublet_code, $sublet_description, $other_billing_type, $sublet_cost, $sublet_sale);

    $sublet_entries = $sublet_qry->rows;
    if($sublet_entries > 0){

        while (my @sublet_row = $sublet_qry->fetchrow_array) {
            ( $sublet_code, $sublet_description, $other_billing_type, $sublet_cost, $sublet_sale) = @sublet_row;
            $Text::Wrap::columns = 63;
            my $wrapped_sublet_description = fill('', '', expand($sublet_description));
            my @sublet_description_list = split "\n", $wrapped_sublet_description;
            push (@sublet_section_array, sprintf(border(">")."SUBL %-63s%10s".border("|")."\n", $sublet_description_list[0], ""));
            foreach my $i (1 .. $#sublet_description_list) {
                push (@sublet_section_array, sprintf(border(">")."%5s%-73s".border("|")."\n", "", $sublet_description_list[$i]));
            } 
            push (@sublet_section_array, sprintf(border(">")."  %15s%5s%11s %7d %7d %12s %7.2f %7.2f ".border("|")."\n",
                                                    "", $billing_code, "", $sublet_cost*100, $sublet_sale*100, "", $sublet_cost, $sublet_sale));
            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $job_intern_sublet_sale = $job_intern_sublet_sale + $sublet_sale;
                $job_intern_sublet_cost = $job_intern_sublet_cost + $sublet_cost;
            } elsif (lc($other_billing_type) eq 'c'){
                $job_cust_sublet_sale = $job_cust_sublet_sale + $sublet_sale;
                $job_cust_sublet_cost = $job_cust_sublet_cost + $sublet_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $job_warr_sublet_sale = $job_warr_sublet_sale + $sublet_sale;
                $job_warr_sublet_cost = $job_warr_sublet_cost + $sublet_cost;
            }
        }
    }
    return @sublet_section_array;
}

# Subroutine to prepare the DEDUCTIBLE section of a JOB
sub make_deductible_section{
    my ($ro_line) = @_;
    my @ded_section_array;
    my $ded_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'DEDUCTIBLE'");
    $ded_qry->execute($ro_number, $ro_line);
    my ( $ded_code, $ded_description, $ded_cost, $ded_sale);

    $ded_entries = $ded_qry->rows;
    if($ded_entries > 0){

        while (my @ded_row = $ded_qry->fetchrow_array) {
            ( $ded_code, $ded_description, $ded_cost, $ded_sale) = @ded_row;
            push (@ded_section_array, sprintf(border(">")."%-69s%8.2f ".border("|")."\n",
                                                "CUSTOMER PAY DEDUCTIBLE FOR LINE ".$ro_line, $ded_sale));
            $job_cust_ded_sale = $job_cust_ded_sale + $ded_sale;
            $job_cust_ded_cost = $job_cust_ded_cost + $ded_cost;
        }
    }
    $tot_cust_ded_sale = $tot_cust_ded_sale+$job_cust_ded_sale;
    return @ded_section_array;
}

sub make_discount_section {
    my ($ro_number,$ro_line) = @_;
    my @disc_section_array;
    $job_total_discount = 0;
    my $disc_qry = $conn->prepare("SELECT
                                      disc_code,
                                      disc_description,
                                      disc_applied,
                                      labor_discount,
                                      parts_discount,
                                      total_discount
                                  FROM repair_discount
                                  WHERE ro_number = ?
                                  AND ro_line =?
                               ");
    $disc_qry->execute($ro_number,$ro_line);
    my ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount );

    while (my @disc_row = $disc_qry->fetchrow_array) {
        ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount ) = @disc_row;
        $Text::Wrap::columns = 68;
        $disc_description = $disc_code." ".$disc_description;
        my $wrapped_disc_description = fill('', '', expand($disc_description));
        my @disc_description_list = split "\n", $wrapped_disc_description;

        foreach my $i (0 .. $#disc_description_list) {
            push (@disc_section_array, sprintf(border(">")."%-68s%10s".border("|")."\n",
                                    $disc_description_list[$i], ""));
        }

        if ($labor_discount != 0){
            push (@disc_section_array, sprintf(border(">")."%6s%-10s %14s%10s %7s%13s%8.2f%8.2f ".border("|")."\n",
                                                "", "LABOR", $disc_applied, 0, $labor_discount*100, "", $labor_discount, $labor_discount));
        }
        if ($parts_discount != 0){
            push (@disc_section_array, sprintf(border(">")."%6s%-10s %14s%10s %7s%13s%8.2f%8.2f ".border("|")."\n",
                                                "", "PARTS", $disc_applied, 0, $parts_discount*100, "", $parts_discount, $parts_discount));
        }
        $cust_discount = $cust_discount + $total_discount;
        $job_total_discount = $job_total_discount + $total_discount
    }
    return @disc_section_array;
}

# Subroutine to prepare the TOTAL section of a JOB
sub make_job_total_section{
    my ($ro_line) = @_;
    my @job_total_section_array;

    push (@job_total_section_array, sprintf(border(">")."PARTS:%9.2f   LABOR:%9.2f   OTHER:%9.2f   TOTAL LINE %s:%11.2f ".border("|")."\n",
                                            $job_cust_prt_sale+$job_warr_prt_sale+$job_intern_prt_sale,
                                            $job_cust_lbr_sale+$job_warr_lbr_sale+$job_intern_lbr_sale,
                                            $job_cust_misc_sale+$job_warr_misc_sale+$job_intern_misc_sale
                                                +$job_cust_gog_sale+$job_warr_gog_sale+$job_intern_gog_sale
                                                +$job_cust_sublet_sale+$job_warr_sublet_sale+$job_intern_sublet_sale+$job_total_discount,
                                            $ro_line,
                                            $job_cust_prt_sale+$job_warr_prt_sale+$job_intern_prt_sale
                                                +$job_cust_lbr_sale+$job_warr_lbr_sale+$job_intern_lbr_sale
                                                +$job_cust_misc_sale+$job_warr_misc_sale+$job_intern_misc_sale
                                                +$job_cust_gog_sale+$job_warr_gog_sale+$job_intern_gog_sale
                                                +$job_cust_sublet_sale+$job_warr_sublet_sale+$job_intern_sublet_sale+$job_total_discount));

    $tot_cust_lbr_cost = $tot_cust_lbr_cost+$job_cust_lbr_cost;
    $tot_cust_lbr_sale = $tot_cust_lbr_sale+$job_cust_lbr_sale;
    $tot_cust_prt_cost = $tot_cust_prt_cost+$job_cust_prt_cost;
    $tot_cust_prt_sale = $tot_cust_prt_sale+$job_cust_prt_sale;
    $tot_cust_misc_cost = $tot_cust_misc_cost+$job_cust_misc_cost;
    $tot_cust_misc_sale = $tot_cust_misc_sale+$job_cust_misc_sale;
    $tot_cust_gog_cost = $tot_cust_gog_cost+$job_cust_gog_cost;
    $tot_cust_gog_sale = $tot_cust_gog_sale+$job_cust_gog_sale;
    $tot_cust_sublet_cost = $tot_cust_sublet_cost+$job_cust_sublet_cost;
    $tot_cust_sublet_sale = $tot_cust_sublet_sale+$job_cust_sublet_sale;

    $tot_warr_lbr_cost = $tot_warr_lbr_cost+$job_warr_lbr_cost;
    $tot_warr_lbr_sale = $tot_warr_lbr_sale+$job_warr_lbr_sale;
    $tot_warr_prt_cost = $tot_warr_prt_cost+$job_warr_prt_cost;
    $tot_warr_prt_sale = $tot_warr_prt_sale+$job_warr_prt_sale;
    $tot_warr_misc_cost = $tot_warr_misc_cost+$job_warr_misc_cost;
    $tot_warr_misc_sale = $tot_warr_misc_sale+$job_warr_misc_sale;
    $tot_warr_gog_cost = $tot_warr_gog_cost+$job_warr_gog_cost;
    $tot_warr_gog_sale = $tot_warr_gog_sale+$job_warr_gog_sale;
    $tot_warr_sublet_cost = $tot_warr_sublet_cost+$job_warr_sublet_cost;
    $tot_warr_sublet_sale = $tot_warr_sublet_sale+$job_warr_sublet_sale;

    $tot_intern_lbr_cost = $tot_intern_lbr_cost+$job_intern_lbr_cost;
    $tot_intern_lbr_sale = $tot_intern_lbr_sale+$job_intern_lbr_sale;
    $tot_intern_prt_cost = $tot_intern_prt_cost+$job_intern_prt_cost;
    $tot_intern_prt_sale = $tot_intern_prt_sale+$job_intern_prt_sale;
    $tot_intern_misc_cost = $tot_intern_misc_cost+$job_intern_misc_cost;
    $tot_intern_misc_sale = $tot_intern_misc_sale+$job_intern_misc_sale;
    $tot_intern_gog_cost = $tot_intern_gog_cost+$job_intern_gog_cost;
    $tot_intern_gog_sale = $tot_intern_gog_sale+$job_intern_gog_sale;
    $tot_intern_sublet_cost = $tot_intern_sublet_cost+$job_intern_sublet_cost;
    $tot_intern_sublet_sale = $tot_intern_sublet_sale+$job_intern_sublet_sale;

    return @job_total_section_array;
}

# Subroutine to get the RO TAX amount
sub get_ro_tax_amount {
    my $tax_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ? AND item_type = 'TAX'");
    $tax_qry->execute($ro_number);
    my ( $tax_type, $tax_amount);

    while (my @tax_row = $tax_qry->fetchrow_array) {
        ( $tax_type, $tax_amount) = @tax_row;
        if (lc($tax_type) eq "c"){
            $cust_ro_tax = $tax_amount;
        }elsif (lc($tax_type) eq "w"){
            $warr_ro_tax = $tax_amount;
        }elsif (lc($tax_type) eq "i"){
            $intern_ro_tax = $tax_amount;
        }
    }
}

#Subroutine to prepare the FOOTER section of the invoice
sub get_footer {
    my @footer_section;
    my ($is_end, $page_number) = @_;
    get_ro_tax_amount();
    



    if($is_end) {
        push (@footer_section, sprintf(border(">")."%48s%-17s%5s%8s".border("|")."\n", "","DESCRIPTION","","TOTALS"));
        push (@footer_section, sprintf(border(">")."%44s%-17s%5s%11.2f ".border("|")."\n",
              "","LABOR AMOUNT","", $tot_cust_lbr_sale));
        push (@footer_section, sprintf(border(">")."%44s%-17s%5s%11.2f ".border("|")."\n",
              "","PARTS AMOUNT","", $tot_cust_prt_sale));
        push (@footer_section, sprintf(border(">")."%44s%-17s%5s%11.2f ".border("|")."\n",
              "","GAS, OIL, LUBE","", $tot_cust_gog_sale));
        push (@footer_section, sprintf(border(">")."%44s%-17s%5s%11.2f ".border("|")."\n",
              "","SUBLET AMOUNT","", $tot_cust_sublet_sale));
        push (@footer_section, sprintf(border(">")."%44s%-17s%5s%11.2f ".border("|")."\n",
              "","MISC. CHARGES","", $tot_cust_misc_sale + $tot_cust_ded_sale));
        push (@footer_section, sprintf(border(">")."%44s%-17s%5s%11.2f ".border("|")."\n",
              "","TOTAL CHARGES","", $tot_cust_lbr_sale + $tot_cust_prt_sale + $tot_cust_gog_sale + $tot_cust_sublet_sale +
                  $tot_cust_misc_sale + $tot_cust_ded_sale));
        my $less_insu = get_insurance_amount();
        my $tot_less_insu = $cust_discount - $less_insu;
        push (@footer_section, sprintf(border("#")."%44s%-17s%5s%11.2f ".border("|")."\n",
              "","LESS INSURANCE","", -1*$tot_less_insu));
        push (@footer_section, sprintf(border(">")."%44s%-17s%5s%11.2f ".border("|")."\n",
              "","SALES TAX","", $cust_ro_tax));
        push (@footer_section, sprintf(border(">")."%78s".border("|")."\n", ""));
        push (@footer_section, sprintf(border(">")."%44s%-17s%5s%11.2f ".border("|")."\n",
              "","","", $tot_cust_lbr_sale + $tot_cust_prt_sale + $tot_cust_gog_sale + $tot_cust_sublet_sale +
                  $tot_cust_misc_sale + $tot_cust_ded_sale + $cust_ro_tax + $tot_less_insu));

    }
    push (@footer_section, sprintf(border("#")."%78s".border("|")."\f", ""));
    return @footer_section;
}

# Subroutine to make the Total(RO) FEE section
sub make_ro_fee_section {
    my $fee_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_description,
                                      other_cost,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ?
                                  AND item_type = 'FEE'
                                  AND ro_line IS NULL");
    $fee_qry->execute($ro_number);
    my ($fee_type, $fee_description, $fee_cost, $fee_sale);

    while (my @fee_row = $fee_qry->fetchrow_array) {
        ($fee_type, $fee_description, $fee_cost, $fee_sale) = @fee_row;
        push (@ro_fee_section, sprintf(border(">")."%-30s %7s %7s  %20s %8.2f ".border("|")."\n",
              $fee_description, $fee_cost*100, $fee_sale*100, "",  $fee_sale));
        if (lc($fee_type) eq "c"){
            $tot_cust_misc_sale = $tot_cust_misc_sale + $fee_sale;
            $tot_cust_misc_cost = $tot_cust_misc_cost + $fee_cost;
        }elsif (lc($fee_type) eq "w"){
            $tot_warr_misc_sale = $tot_warr_misc_sale + $fee_sale;
            $tot_warr_misc_cost = $tot_warr_misc_cost + $fee_cost;
        }elsif (lc($fee_type) eq "i"){
            $tot_intern_misc_sale = $tot_intern_misc_sale + $fee_sale;
            $tot_intern_misc_cost = $tot_intern_misc_cost + $fee_cost;
        }
    }
    return @ro_fee_section;
}

# Subroutine to make the Total(RO) Discount section
sub make_ro_disc_section {
    my $disc_qry = $conn->prepare("SELECT
                                      disc_code,
                                      disc_description,
                                      disc_applied,
                                      labor_discount,
                                      parts_discount,
                                      total_discount
                                  FROM repair_discount
                                  WHERE ro_number = ?
                                  AND ro_line IS NULL
                               ");
    $disc_qry->execute($ro_number);
    my ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount );

    while (my @disc_row = $disc_qry->fetchrow_array) {
        ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount ) = @disc_row;
        $Text::Wrap::columns = 68;
        $disc_description = $disc_code." ".$disc_description;
        my $wrapped_disc_description = fill('', '', expand($disc_description));
        my @disc_description_list = split "\n", $wrapped_disc_description;

        foreach my $i (0 .. $#disc_description_list) {
            push (@ro_disc_section, sprintf(border(">")."%-68s%10s".border("|")."\n",
                                    $disc_description_list[$i], ""));
        }

        if ($labor_discount != 0){
            push (@ro_disc_section, sprintf(border(">")."%6s%-10s %14s%7s %7s%15s%7.2f %8.2f ".border("|")."\n",
                                                "", "LABOR", $disc_applied, 0, $labor_discount*100, "", $labor_discount, $labor_discount));
        }
        if ($parts_discount != 0){
            push (@ro_disc_section, sprintf(border(">")."%6s%-10s %14s%7s %7s%15s%7.2f %8.2f ".border("|")."\n",
                                                "", "PARTS", $disc_applied, 0, $parts_discount*100, "", $parts_discount, $parts_discount));
        }
        $cust_discount = $cust_discount + $total_discount;
    }
    return @ro_disc_section;
}

# Subroutine to make the Tech-Punch section
sub make_ro_tech_punch_section {
    my $tech_punch_qry = $conn->prepare("SELECT
                                      ro_line,
                                      tech_id,
                                      to_char(work_date, 'mm-dd-yy') AS work_date,
                                      to_char(work_start_time, 'HH24:MI'),
                                      to_char(work_end_time, 'HH24:MI'),
                                      work_type,
                                      work_duration
                                  FROM repair_tech_punch
                                  WHERE ro_number = ?
                                  ORDER BY ro_punch_id");
    $tech_punch_qry->execute($ro_number);
    my ( $ro_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_type, $work_duration );

    $tech_punch_entries = $tech_punch_qry->rows;
    if($tech_punch_entries > 0){
        push (@ro_tech_punch_section, sprintf(border("#")."%78s".border("|")."\n", ""));
        push (@ro_tech_punch_section, sprintf(border("#")."%4sDATE   START  FINISH  DURATION  TYPE    TECH  LINE(S)  CHG%16s".border("|")."\n", "", ""));
        while ( my @tech_punch_row = $tech_punch_qry->fetchrow_array) {
            ( $ro_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_type, $work_duration ) = @tech_punch_row;
            push (@ro_tech_punch_section, sprintf(border(">")."%-8s   %5s  %6s  %8.2f  %4s  %6s  %7s %20s".border("|")."\n",
                                            $work_date, $work_start_time, $work_end_time, $work_duration, $work_type, $tech_id, $ro_line, ""));
        }
    }
    else{
        push (@ro_tech_punch_section, sprintf(border("#")." %78s ".border("|")."\n", "")); # Separator Line
    }
    return @ro_tech_punch_section;
}

# Subroutine to make the Account section
sub make_ro_account_section { 
    my $account_qry = $conn->prepare("select  row_number() over ( ) as sl_no,* from (SELECT
                                      ro_account_no,
                                      TRUNC(sale::numeric) AS sale,
                                       case when account_type = 'A' and (cost is null or cost = '0')
                                           then '*******'
                                         else  TRUNC(coalesce(cost,'0')::numeric)::text end AS cost,
                                      account_control_no,
                                      company_id
                                  FROM repair_account_gldetails WHERE ro_number = ? 
                                  ORDER BY
    CASE 
      WHEN  account_type like 'S%' THEN 1
       WHEN  account_type like '%,S' THEN 2
      ELSE 3
   END,ro_account_no)t");
                              
    $account_qry->execute($ro_number);
    my ( $sl_no, $ro_account_no, $sale, $cost, $account_control_no, $company_id );

    if($account_qry->rows > 0){
         push (@ro_account_section, sprintf(border("#")." %78s ".border("|")."\n", "")); # Separator Line
        if($account_qry->rows == 1){
           push (@ro_account_section, sprintf(border(">")."%-12s %-7s %-7s %-9s %45s".border("|")."\n",
                                                "TRGT/ACCOUNT", "SALE", "COST", "CONTROL", ""));
        } else{
             push (@ro_account_section, sprintf(border(">")."%-12s %-7s %-7s %-9s%1s%-12s %-7s %-7s %-9s".border("|")."\n",
                                                 "TRGT/ACCOUNT", "SALE", "COST", "CONTROL", "", "TRGT/ACCOUNT", "SALE", "COST", "CONTROL"));
        }
       
        while (my @account_row = $account_qry->fetchrow_array) {
            ( $sl_no, $ro_account_no, $sale, $cost, $account_control_no, $company_id ) = @account_row;
             if ($sl_no % 2 == 0){
                 push (@ro_account_section, sprintf(border(">")."%-12s %-7s %-7s %-9s".border("|")."\n",
                                                    $company_id.'/'.$ro_account_no, $sale, $cost, $account_control_no ));
                $ro_account_section[@ro_account_section - 2] = $ro_account_section[@ro_account_section - 2] . $ro_account_section[@ro_account_section - 1 ];
				delete($ro_account_section[@ro_account_section - 1 ]);
             } else{
                 push (@ro_account_section, sprintf(border(">")."%-12s %-7s %-7s %-9s"."",
                                                    $company_id.'/'.$ro_account_no, $sale, $cost, $account_control_no ));   
             }    

            # push (@ro_account_section, sprintf(border(">")."%4s %-14s %-10s %-10s %-10s %1s %-14s %-10s %-10s %-10s".border("|")."\n",
                                                    # "", "TRGT/ACCOUNT", "SALE", "COST", "CONTROL", "", "TRGT/ACCOUNT", "SALE", "COST", "CONTROL"));
        }
        if($account_qry->rows % 2 == 1){
  				#$array_size=@tech_section_array;
                push (@ro_account_section, sprintf(border(">")."%35s".border("|")."\n",""));
                $ro_account_section[@ro_account_section - 2] = $ro_account_section[@ro_account_section - 2] . $ro_account_section[@ro_account_section - 1 ];
                delete($ro_account_section[@ro_account_section - 1 ]);
        }

    }
    
    ### gl accounting

        # my $gl_account_qry = $conn->prepare("select timestampSequence, postingSequence, postingDate, journalId, accNo, accDesc, accType, incBalGrp, debit, credit, roNumber, postingTime from repair_account_gldetails_tmp
        #                              WHERE roNumber = ? ");

        my $gl_account_qry = $conn->prepare('select  DENSE_RANK()  OVER (PARTITION  BY "RONo" ORDER BY "RONo", "PostingTime" ) as "PostingTimeSerial",
        "PostingSequence", to_char("AccountingDate"::date,\'MM/dd\'), "JournalID",
        "AccountNo","AccountDescription","AccType","IncBalGrpDesc",
       "Debit","Credit","RONo", "PostingTime" 
       from ro_status_view WHERE "RONo" = ? 
       order by "PostingTimeSerial", "PostingSequence"::numeric ASC');

    $gl_account_qry->execute($ro_number);
    my ( $timestampSequence, $postingSequence, $postingDate, $journalId, $accNo, $accDesc, $accType, $incBalGrp, $debit, $credit, $roNumber, $postingTime);

    if($gl_account_qry->rows > 0){
         push (@ro_account_section, sprintf(border("#")." %78s ".border("|")."\n", "")); # Separator Line
         push (@ro_account_section, sprintf(border("#")." %78s ".border("|")."\n", "")); # Separator Line
         push (@ro_account_section, sprintf(border("#")."%-78s".border("|")."\n", "Supplemental Internal Accounting Detail - RO# ".$ro_number)); # Separator Line
         push (@ro_account_section, sprintf(border("#")." %78s ".border("|")."\n", "")); # Separator Line
        push (@ro_account_section, sprintf(border(">")."%-11s  %-4s  %-8s  %-4s  %9s  %9s  %-19s".border("|")."\n",
                                                 "Date-TS-PS", "Jrnl", "Account", "Type", "Cost/DR", "Sale/CR", "Account Description"));
        my $timestampSequenceCheck = 0;
        while (my @gl_account_row = $gl_account_qry->fetchrow_array) {
            ( $timestampSequence, $postingSequence, $postingDate, $journalId, $accNo, $accDesc, $accType, $incBalGrp, $debit, $credit, $roNumber, $postingTime ) = @gl_account_row;
                if ($timestampSequenceCheck ne $timestampSequence) {
                push (@ro_account_section, sprintf(border("#")." %78s ".border("|")."\n", "")); # Separator Line
                printf(FILE border("#")."%78s".border("|")."\n", "");
                $timestampSequenceCheck = $timestampSequence;
                }
                 push (@ro_account_section, sprintf(border(">")."%-11s  %-4s  %-8s  %-4s  %9s  %9s  %-19s".border("|")."\n",
 $postingDate.'-'.$timestampSequence.'-'.$postingSequence, $journalId, $accNo, $accType, $debit, $credit, $accDesc));

    }
    }
    return @ro_account_section;
}

# Subroutine to print accounting totals
sub make_accounting_totals {
 
    my ($is_accounting_end, $page_number, $cost_total, $sale_total, $comp_total) = @_;
 
    $sale_total = ($tot_cust_lbr_sale + $tot_cust_prt_sale + $tot_cust_gog_sale + $tot_cust_sublet_sale +
                  $tot_cust_misc_sale + $tot_cust_ded_sale + $tot_intern_lbr_sale + $tot_intern_prt_sale + $tot_intern_gog_sale + $tot_intern_sublet_sale +
                  $tot_intern_misc_sale + $tot_intern_ded_sale + $tot_warr_lbr_sale + $tot_warr_prt_sale + $tot_warr_gog_sale + $tot_warr_sublet_sale +
                  $tot_warr_misc_sale + $tot_warr_ded_sale + $cust_discount)*100;
     $cost_total = ($tot_cust_lbr_cost + $tot_cust_prt_cost + $tot_cust_gog_cost + $tot_cust_sublet_cost +
                  $tot_cust_misc_cost + $tot_cust_ded_cost + $tot_intern_lbr_cost + $tot_intern_prt_cost + $tot_intern_gog_cost + $tot_intern_sublet_cost +
                  $tot_intern_misc_cost + $tot_intern_ded_cost + $tot_warr_lbr_cost + $tot_warr_prt_cost + $tot_warr_gog_cost + $tot_warr_sublet_cost +
                  $tot_warr_misc_cost + $tot_warr_ded_cost)*100;
     $comp_total = 0;
    my $rounded_cost_total = round( $cost_total );
    my $rounded_sale_total = round( $sale_total );
    #if($is_accounting_end){
        push (@ro_total_section, sprintf(border("#")." %-25s%-5s%-48s".border("|")."\n", "COST, SALE, & COMP TOTALS", "", $rounded_cost_total." ".$rounded_sale_total." ".$comp_total));	
    #}
    return @ro_total_section;
}


# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")."%78s".border("|")."\n", "");
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
 sub print_header_section {
    my ($page_num) = @_;
    my %hash = (pg_num => $page_num );
    foreach (@header_section)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
 }

# Subroutine for pagination
sub paginate_and_print_segment {
    my (@section_array) = @_;
    # Print section that fit in the current page
    if(scalar(@section_array) <= ($page_max_height - $curr_page_height)){
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{
        for my $sub_section(@section_array){
            if($curr_page_height < $page_max_height)
            {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            } else{
                print FILE get_footer(0, $curr_page_num);
                $curr_page_num = $curr_page_num + 1;
                print_header_section ($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
            }
        }
    }
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
    ($tot_cust_lbr_cost, $tot_cust_lbr_sale, $tot_cust_prt_cost, $tot_cust_prt_sale, $tot_cust_misc_cost, $tot_cust_misc_sale, $tot_cust_gog_cost, $tot_cust_gog_sale,
    $tot_cust_sublet_cost, $tot_cust_sublet_sale, $tot_cust_ded_cost, $tot_cust_ded_sale )= (0)x12;

    ($tot_intern_lbr_cost, $tot_intern_lbr_sale, $tot_intern_prt_cost, $tot_intern_prt_sale, $tot_intern_misc_cost, $tot_intern_misc_sale, $tot_intern_gog_cost,
    $tot_intern_gog_sale, $tot_intern_sublet_cost, $tot_intern_sublet_sale, $tot_intern_ded_cost, $tot_intern_ded_sale )= (0)x12;

    ($tot_warr_lbr_cost, $tot_warr_lbr_sale, $tot_warr_prt_cost, $tot_warr_prt_sale, $tot_warr_misc_cost, $tot_warr_misc_sale, $tot_warr_gog_cost, $tot_warr_gog_sale,
    $tot_warr_sublet_cost, $tot_warr_sublet_sale, $tot_warr_ded_cost, $tot_warr_ded_sale )= (0)x12;

    ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;
    $cust_discount = 0;

    $curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @comment_section;
    undef @job_section;
    undef @ro_fee_section;
    undef @ro_disc_section;
    undef @ro_tech_punch_section;
    undef @ro_tech_punch_section;
    undef @ro_account_section;
    undef @ro_total_section;
    ########## Prepare invoice ##########
    make_header();
    make_job_section();
    make_ro_fee_section();
    make_ro_disc_section();
    make_ro_tech_punch_section();
     if($show_accounting_in_proxy eq 'true'){
        make_ro_account_section();
        my $accounting_page_height = $page_max_height-1;
       # print("accounting_page_height: ".$accounting_page_height." page_max_height: ".$page_max_height." curr_page_height: ".$curr_page_height)."\n";
    #     if($curr_page_height == $accounting_page_height){
    #         print FILE print_accounting_totals(1, $curr_page_num, 0, 0, 0);
    #     } 
    #     else {
    #         print_blank_line($accounting_page_height - $curr_page_height);
    #         print FILE print_accounting_totals(1, $curr_page_num, 0, 0, 0);
    # }
 }
    make_accounting_totals();

    ########## Print invoice ##########
    print_header_section ($curr_page_num);

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }

    # Pagination of RO fee section
    paginate_and_print_segment(@ro_fee_section);

    # Pagination of RO Discount section
    paginate_and_print_segment(@ro_disc_section);

    # Pagination of RO Comments
    paginate_and_print_segment(@comment_section);

    # Pagination of RO Technician Punch details
    paginate_and_print_segment(@ro_tech_punch_section);

     # Pagination of Accounting details
    if($show_accounting_in_proxy eq 'true'){
       paginate_and_print_segment(@ro_account_section);
    }
    # Pagination of Accounting details
    


    # End of invoice footer
    if($curr_page_height == $page_max_height){
         print_blank_line($page_max_height - 1);
        paginate_and_print_segment(@ro_total_section);
        print FILE get_footer(1, $curr_page_num);
    } else {
        print_blank_line($page_max_height - $curr_page_height -1);
        paginate_and_print_segment(@ro_total_section);
        print FILE get_footer(1, $curr_page_num);
    }
}

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}


# Print Open/Void RO
sub print_open_ro {
    printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO# ".$ro_number." * See Open and Void RO Report *");
}

# LESS INSURANCE
sub get_insurance_amount {
    my $insurance = $conn->prepare("SELECT  
                                                CASE
                                                    WHEN COUNT(is_insurance)  FILTER (WHERE is_insurance::boolean) > 0
                                                    THEN 'YES'
                                                ELSE 'NO' END                                              AS is_insurance,
                                            SUM(pay_amount::numeric)  FILTER (WHERE is_insurance::boolean) AS insurance_amount
                                        FROM  du_dms_cdk3pa_proxy.repair_order_payment 
                                        WHERE ro_number = ?
                                        GROUP BY ro_number");
    $insurance->execute($ro_number);
   
    my ( $is_insurance, $insurance_amount);
    my $ins_entries = $insurance->rows;
    if($ins_entries > 0){
        while (my @res_row = $insurance->fetchrow_array) {
            ( $is_insurance, $insurance_amount) = @res_row;
        }
    }
    return $insurance_amount ? $insurance_amount : 0;
   

}
