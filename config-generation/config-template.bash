# Bash Source Test Config File

export DMS_BASE='<DMS_BASE>'
source "$DU_ETL_HOME/DU-DMS/DMS-<ETL_DMS>/<ETL_DMS>.env"

DMS_BASE_DIR='<DMS_BASE_DIR>'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_<ETL_DMS>}"
PROMPT_FOR_FILE='true'

#ETL  DMS: <ETL_DMS>
#Base DMS: <DMS_BASE> (optional)

DMS="<DMS_IMPORT>"
MFG="<MFG>"
STATE='<STATE>'

SERVICE_NAME=${GGS_PROD_PG_SERVICE}
AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
ETL_DIST="${DU_ETL_DIST_DIR}"

SOURCE_COMPANY_ID="<COMPANY_ID>"
COMPANY_ID="<COMPANY_ID>"
PROJECT_ID="<PROJECT_ID>"
PROJECT_TYPE="<PROJECT_TYPE>"
PROJECT_NAME="<PROJECT_NAME>"
SECONDARY_PROJECT_ID="<SECONDARY_PROJECT_ID>"
SECONDARY_PROJECT_TYPE="<SECONDARY_PROJECT_TYPE>"
SECONDARY_PROJECT_NAME="<SECONDARY_PROJECT_NAME>"
SUPPLEMENT_TAG="<SUPPLEMENT_TAG>"
SECONDARY_SUPPLEMENT_TAG="<SECONDARY_SUPPLEMENT_TAG>"
IS_SHIPPED="<IS_SHIPPED>"
SECONDARY_IS_SHIPPED="<SECONDARY_IS_SHIPPED>"
IMPORTED_BY='<IMPORTED_BY>'