

FILE='config-store.txt'
USER='<EMAIL>'
# ENV='env'


# COMPANY_ID='62412933'
# COMPANY_IDS='63098570, 63099198, *********, 62412933' 


function psql_solve360_script {
    DU_ETL_SOLVE360_SERVICE=solve360
    psql "service=${DU_ETL_SOLVE360_SERVICE} options='-c search_path=etl_solve360_jobs'" \
        --pset pager='off' \
        --set=ON_ERROR_STOP=1 \
        "$@"
}

function psql_portal_script {
    DU_ETL_PORTAL_SERVICE=du_portal
    psql "service=${DU_ETL_PORTAL_SERVICE} options='-c search_path=app__data_master'" \
        --pset pager='off' \
        --set=ON_ERROR_STOP=1 \
        "$@"
}

function get_jobs() {

SHOW_TEST_DATA=true
# Set the Internal Field Separator (IFS) to comma (,)
IFS=','
# Read the input string into an array
read -r -a values <<< "$COMPANY_IDS"
# Loop through the array and do whatever you need with each value
for company_id in "${values[@]}"; do

# Trim leading spaces
trimmed_company_id="${company_id#"${company_id%%[![:space:]]*}"}"
# Trim trailing spaces
trimmed_company_id="${trimmed_company_id%"${trimmed_company_id##*[![:space:]]}"}"

if [[ $SHOW_TEST_DATA = true ]]; then
    psql_portal_script -At --set=company="${trimmed_company_id}" <<SQL
       SELECT
          company_id,
          mage_manufacturer,
          state,
          company_name,
          regexp_replace(mage_group_code, '[^a-zA-Z0-9_]', '', 'g') AS mage_group_code,
          regexp_replace(mage_group_name, '[^a-zA-Z0-9_]', '', 'g') AS mage_group_name,
          regexp_replace(mage_store_code, '[^a-zA-Z0-9_]', '', 'g') AS mage_store_code,
          regexp_replace(mage_store_name, '[^a-zA-Z0-9_]', '', 'g') AS mage_store_name,
          third_party_username,
          project_id,
          project_name,
          project_type,
          assignedto_cn,
          dms_code,
          etl_dms,
          errors,
          secondary_project_id,
          secondary_project_type,
          secondary_project_name,
          supplement_tag,
          secondary_supplement_tag,
          is_primary_shipped ,  
          is_secondary_shipped
     FROM app__data_master.get_company_active_project_details_list_for_scheduler(ARRAY[:'company']::BIGINT[]);
SQL
else
    psql_portal_script -At --set=company="${trimmed_company_id}" <<SQL
       SELECT
          company_id,
          mage_manufacturer,
          state,
          company_name,
          regexp_replace(mage_group_code, '[^a-zA-Z0-9_]', '', 'g') AS mage_group_code,
          regexp_replace(mage_group_name, '[^a-zA-Z0-9_]', '', 'g') AS mage_group_name,
          regexp_replace(mage_store_code, '[^a-zA-Z0-9_]', '', 'g') AS mage_store_code,
          regexp_replace(mage_store_name, '[^a-zA-Z0-9_]', '', 'g') AS mage_store_name,
          third_party_username,
          project_id,
          project_name,
          project_type,
          assignedto_cn,
          dms_code,
          etl_dms,
          errors,
          secondary_project_id,
          secondary_project_type,
          secondary_project_name,
          supplement_tag,
          secondary_supplement_tag,
          is_primary_shipped ,  
          is_secondary_shipped
     FROM app__data_master.get_company_active_project_details_list_for_scheduler(ARRAY[:'company']::BIGINT[]);
SQL



fi

done
}


function create_config_file() {

    DMS_BASE_ETL_DIR='/etl/home/<USER>/DU-ETL'
    CONFIG_TEMPLATE="$DU_ETL_HOME"/config-generation/config-template.bash

    #local FILENAME="[${MAGE_GROUP_CODE}]${MAGE_STORE_CODE}+${YYYYMM}_${MANUFACTURER_ID}"

    local FILENAME=config_${1}.bash
    local ENV
    local DMS_BASE
    local DMS_BASE_DIR
    local DMS_BASE_DIR_VAR
    local IMPORTED_BY="$PERFORMED_BY"
    local DMS_IMPORT
    YYYYMM=$(date +%Y%m)

    ETL_DMS_ACT=$ETL_DMS
    DMS_IMPORT=$ETL_DMS
    DMS_ACT=$DMS
    if [[ "$ETL_DMS" = 'UCS' || "$ETL_DMS" = 'Reynolds' ]]; then
        ETL_DMS_ACT=ReynoldsRCI
        DMS_ACT=ReynoldsRCI
    fi
    if [[ "$ETL_DMS" = 'Reynolds' ]]; then
        DMS_IMPORT=ReynoldsRCI
    fi

    ENV="$ETL_DMS"
    DMS_BASE_DIR_VAR="DU_ETL_BASEDIR_${ETL_DMS}"

    sed -e  "s/<ENV>/${ETL_DMS_ACT}/g"                                 \
        -e  "s/<DATE_PART>/${YYYYMM}/g"                                \
        -e  "s/<DMS_BASE>/${DMS_ACT}/g"                                \
        -e  "s/<ETL_DMS>/${ETL_DMS_ACT}/g"                             \
        -e  "s/<DMS_IMPORT>/${ETL_DMS}/g"                          \
        -e  "s#<DMS_BASE_DIR>#${DMS_BASE_ETL_DIR}#g"                   \
        -e  "s/<STORE_NAME>/${MAGE_STORE_NAME}/g"                      \
        -e  "s/<MFG>/${MANUFACTURER_ID}/g"                             \
        -e  "s/<STATE>/${STATE}/g"                                     \
        -e  "s/<GROUP_CODE>/${MAGE_GROUP_CODE}/g"                      \
        -e  "s/<GROUP_NAME>/${MAGE_GROUP_NAME}/g"                      \
        -e  "s/<STORE_PART>/${MAGE_STORE_CODE}/g"                      \
        -e  "s/<SOURCE_COMPANY_ID>/${COMPANY_ID}/g"                    \
        -e  "s/<COMPANY_ID>/${COMPANY_ID}/g"                           \
        -e  "s/<PROJECT_ID>/${PROJECT_ID}/g"                           \
        -e  "s/<PROJECT_TYPE>/${PROJECT_TYPE}/g"                       \
        -e  "s/<PROJECT_NAME>/${PROJECT_NAME}/g"                       \
        -e  "s/<SECONDARY_PROJECT_ID>/${SECONDARY_PROJECT_ID}/g"       \
        -e  "s/<SECONDARY_PROJECT_TYPE>/${SECONDARY_PROJECT_TYPE}/g"   \
        -e  "s/<SECONDARY_PROJECT_NAME>/${SECONDARY_PROJECT_NAME}/g"   \
        -e  "s/<IMPORTED_BY>/${IMPORTED_BY}/g"                         \
        -e  "s/<SUPPLEMENT_TAG>/${SUPPLEMENT_TAG}/g"                   \
        -e  "s/<SECONDARY_SUPPLEMENT_TAG>/${SECONDARY_SUPPLEMENT_TAG}/g" \
        -e  "s/<IS_SHIPPED>/${IS_SHIPPED}/g"                   \
        -e  "s/<SECONDARY_IS_SHIPPED>/${SECONDARY_IS_SHIPPED}/g" \
        "${CONFIG_TEMPLATE}"                                           \
        > "${FILENAME}"
        echo "File: ${FILENAME}" >> "${FILE}"

}

function generate_config_file() {
    (
        progress "Generating Config File"
        echo "####################                     $WORK_DIR_PROCESSING_RESULTS_DIR"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"

        get_jobs > "job.txt"

IFS='|'
while read COMPANY_ID MANUFACTURER_ID STATE COMPANY_NAME MAGE_GROUP_CODE MAGE_GROUP_NAME MAGE_STORE_CODE MAGE_STORE_NAME THIRD_PARTY_USERNAME PROJECT_ID PROJECT_NAME PROJECT_TYPE ASSIGNEDTO_CN DMS ETL_DMS ERRORS SECONDARY_PROJECT_ID SECONDARY_PROJECT_TYPE SECONDARY_PROJECT_NAME SUPPLEMENT_TAG SECONDARY_SUPPLEMENT_TAG IS_SHIPPED SECONDARY_IS_SHIPPED
do
    if echo "$STATE" | grep -q '[(].*[)]'; then
        STATE=$(echo "$STATE" | sed -n 's/.*(\(.*\)).*/\1/p')
        echo "STATE: $STATE"
    else
        echo "STATE: $STATE"
    fi
  echo "--------------------------------------------------------------------------------" >> "${FILE}"
  echo "      Company Id: $COMPANY_ID"            >> "${FILE}"
  echo " Manufacturer Id: $MANUFACTURER_ID"       >> "${FILE}"
  echo "           State: $STATE"                 >> "${FILE}"
  echo "    Company Name: $COMPANY_NAME"          >> "${FILE}"
  echo "    SIS Username: $THIRD_PARTY_USERNAME"  >> "${FILE}"
  echo "      Project Id: $PROJECT_ID"            >> "${FILE}"
  echo "    Project Name: $PROJECT_NAME"          >> "${FILE}"
  echo "    Project Type: $PROJECT_TYPE"          >> "${FILE}"
  echo "     Assigned To: $ASSIGNEDTO_CN"         >> "${FILE}"
  echo "             DMS: $DMS"                   >> "${FILE}"
  echo "         ETL_DMS: $ETL_DMS"               >> "${FILE}"
  echo "        SUPP TAG: $SUPPLEMENT_TAG"        >> "${FILE}"
  echo "    SEC SUPP TAG: $SECONDARY_SUPPLEMENT_TAG" >> "${FILE}"
  echo "         SHIPPED: $IS_SHIPPED"            >> "${FILE}"
  echo "     SEC SHIPPED: $SECONDARY_IS_SHIPPED"  >> "${FILE}"

  create_config_file "$COMPANY_ID"

done < "job.txt"
        
    )
}

function generate_mock_config_file() {
    (
        progress "Generating Config File"
        echo "####################                     $WORK_DIR_PROCESSING_RESULTS_DIR"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        COMPANY_ID='00000000000'
        MANUFACTURER_ID="NA"
        STATE="NA"
        COMPANY_NAME="NA"
        THIRD_PARTY_USERNAME="NA"
        PROJECT_ID="00000000000"
        PROJECT_NAME="NA"
        PROJECT_TYPE="NA"
        ASSIGNEDTO_CN="V"
        DMS="NA"
        ETL_DMS="NA"
        SECONDARY_PROJECT_ID="NA"
        SECONDARY_PROJECT_TYPE="NA"
        SECONDARY_PROJECT_NAME="NA"
        SUPPLEMENT_TAG=false
        SECONDARY_SUPPLEMENT_TAG=false
        IS_SHIPPED=false
        SECONDARY_IS_SHIPPED=false

  echo "$COMPANY_ID|$MANUFACTURER_ID|$STATE|$COMPANY_NAME|$MAGE_GROUP_CODE|$MAGE_GROUP_NAME|$MAGE_STORE_CODE|$MAGE_STORE_NAME|$THIRD_PARTY_USERNAME|$PROJECT_ID|$PROJECT_NAME|$PROJECT_TYPE|$ASSIGNEDTO_CN|$DMS|$ETL_DMS|$SECONDARY_PROJECT_ID|$SECONDARY_PROJECT_TYPE|$SECONDARY_PROJECT_NAME|$SUPPLEMENT_TAG|$SECONDARY_SUPPLEMENT_TAG|$IS_SHIPPED|$SECONDARY_IS_SHIPPED" > "job.txt"
  echo "--------------------------------------------------------------------------------" >> "${FILE}"
  echo " Source Company Id: $COMPANY_ID"            >> "${FILE}"
  echo "        Company Id: $COMPANY_ID"            >> "${FILE}"
  echo "   Manufacturer Id: $MANUFACTURER_ID"       >> "${FILE}"
  echo "             State: $STATE"                 >> "${FILE}"
  echo "      Company Name: $COMPANY_NAME"          >> "${FILE}"
  echo "      SIS Username: $THIRD_PARTY_USERNAME"  >> "${FILE}"
  echo "        Project Id: $PROJECT_ID"            >> "${FILE}"
  echo "      Project Name: $PROJECT_NAME"          >> "${FILE}"
  echo "      Project Type: $PROJECT_TYPE"          >> "${FILE}"
  echo "       Assigned To: $ASSIGNEDTO_CN"         >> "${FILE}"
  echo "               DMS: $DMS"                   >> "${FILE}"
  echo "           ETL_DMS: $ETL_DMS"               >> "${FILE}"

  create_config_file "$COMPANY_ID"
    )
}

#create_config_file
