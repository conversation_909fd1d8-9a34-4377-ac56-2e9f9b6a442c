#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Time::Format qw(%time %strftime %manip);
use Term::ANSIColor;
no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];

# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;

my @header_section;
my @comment_section;
my @job_section;
my @ro_fee_section;
my @ro_misc_section;
my @grand_total_section;
my @ro_account_detail_section;
my @ro_make_misc_service_section;
my @ro_disc_section;
my @ro_tech_punch_section;

my ($creation_date, $creation_time, $completion_date, $department, $branch, $sub_branch, $advisor, $customer_number, $customer_name, $customer_address, $customer_city,
    $customer_state, $customer_zip, $customer_phone, $work_phone, $other_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
    $mileage_in, $vehicle_color, $mileage_out, $license_number, $tag_no, $delivery_date, $promised, $ready, $payment_code, $comments, $estimate_amount,
    $ar_number, $auth_number, $po_number, $terms_and_conditions, $copy_number, $customer_signature, $stock_number, $tag_number,$type,$status);

my ($tot_cust_lbr_cost, $tot_cust_lbr_sale, $tot_cust_prt_cost, $tot_cust_prt_sale, $tot_cust_misc_cost, $tot_cust_misc_sale, $tot_cust_gog_cost, $tot_cust_gog_sale,
    $tot_cust_sublet_cost, $tot_cust_sublet_sale, $tot_cust_ded_cost, $tot_cust_ded_sale )= (0)x12;

my ($tot_intern_lbr_cost, $tot_intern_lbr_sale, $tot_intern_prt_cost, $tot_intern_prt_sale, $tot_intern_misc_cost, $tot_intern_misc_sale, $tot_intern_gog_cost,
    $tot_intern_gog_sale, $tot_intern_sublet_cost, $tot_intern_sublet_sale, $tot_intern_ded_cost, $tot_intern_ded_sale )= (0)x12;

my ($tot_warr_lbr_cost, $tot_warr_lbr_sale, $tot_warr_prt_cost, $tot_warr_prt_sale, $tot_warr_misc_cost, $tot_warr_misc_sale, $tot_warr_gog_cost, $tot_warr_gog_sale,
    $tot_warr_sublet_cost, $tot_warr_sublet_sale, $tot_warr_ded_cost, $tot_warr_ded_sale )= (0)x12;

my ($tot_sc_lbr_cost, $tot_sc_lbr_sale, $tot_sc_prt_cost, $tot_sc_prt_sale, $tot_sc_misc_cost, $tot_sc_misc_sale, $tot_sc_gog_cost, $tot_sc_gog_sale,
    $tot_sc_sublet_cost, $tot_sc_sublet_sale, $tot_sc_ded_cost, $tot_sc_ded_sale )= (0)x12;

my ($sale_amount, $labor_cost, $parts_cost, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

my ($job_cust_lbr_cost, $job_cust_lbr_sale, $job_cust_prt_cost, $job_cust_prt_sale, $job_cust_misc_cost, $job_cust_misc_sale, $job_cust_gog_cost, $job_cust_gog_sale,
    $job_cust_sublet_cost, $job_cust_sublet_sale, $job_cust_ded_cost, $job_cust_ded_sale )= (0)x12;

my ($job_intern_lbr_cost, $job_intern_lbr_sale, $job_intern_prt_cost, $job_intern_prt_sale, $job_intern_misc_cost, $job_intern_misc_sale, $job_intern_gog_cost,
    $job_intern_gog_sale, $job_intern_sublet_cost, $job_intern_sublet_sale, $job_intern_ded_cost, $job_intern_ded_sale )= (0)x12;

my ($job_warr_lbr_cost, $job_warr_lbr_sale, $job_warr_prt_cost, $job_warr_prt_sale, $job_warr_misc_cost, $job_warr_misc_sale, $job_warr_gog_cost, $job_warr_gog_sale,
    $job_warr_sublet_cost, $job_warr_sublet_sale, $job_warr_ded_cost, $job_warr_ded_sale )= (0)x12;

my ($job_sc_lbr_cost, $job_sc_lbr_sale, $job_sc_prt_cost, $job_sc_prt_sale, $job_sc_misc_cost, $job_sc_misc_sale, $job_sc_gog_cost, $job_sc_gog_sale,
    $job_sc_sublet_cost, $job_sc_sublet_sale, $job_sc_ded_cost, $job_sc_ded_sale )= (0)x12;

my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax, $sc_ro_tax) = (0)x4;
my $cust_discount = 0;
my $job_billing_code;
my ($parts_entries, $misc_entries, $misc_service_entries, $gog_entries, $sublet_entries, $ded_entries, $tech_punch_entries) = (0)x7;

my $page_max_height = 39; # Maximum no of lines in page body
my $curr_page_height = 0;
my $invoice_note_lines = 22; # No of lines needed for invoice note
my $pages;
my $curr_page_num;
my $internal_tot_freight_amount='0.00';
	my $service_tot_freight_amount='0.00';
	my $warranty_tot_freight_amount='0.00';
	my $customer_tot_freight_amount='0.00';
       
        my $internal_tot_hazmat_amount='0.00';
	my $service_tot_hazmat_amount='0.00';
	my $warranty_tot_hazmat_amount='0.00';
	my $customer_tot_hazmat_amount='0.00';

        my $internal_tot_slstax_amount='0.00';
	my $service_tot_slstax_amount='0.00';
	my $warranty_tot_slstax_amount='0.00';
	my $customer_tot_slstax_amount='0.00';

         my $internal_tot_charge_amount='0.00';
	my $service_tot_charge_amount='0.00';
	my $warranty_tot_charge_amount='0.00';
	my $customer_tot_charge_amount='0.00';

	my $internal_tot_misc_amount='0.00';
	my $service_tot_misc_amount='0.00';
	my $warranty_tot_misc_amount='0.00';
	my $customer_tot_misc_amount='0.00';

my $tot_job_misc_cost=0;
my $ro_qry = $conn->prepare("SELECT ro_number
                               FROM repair_order
                              ORDER BY ro_number");

$ro_qry->execute();
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number) = @ro_list;
    my $file = $ro_number . ".txt";
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;
    load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
    my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
    $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
    $ro_doc_insert->finish;
}

# Subroutine to prepare HEADER section of the invoice
sub make_header {
    my $ro_header_qry = $conn->prepare("SELECT
                                to_char(ro.creation_date :: DATE, 'Mon dd yy') AS creation_date,
                                to_char(open_time, 'HH12:MI AM') AS creation_time,
                                to_char(ro.completion_date :: DATE, 'Mon dd yy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                ro.advisor,
                                roc.customer_number,
                                roc.customer_name,
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip,
                                roc.customer_phone,
				roc.work_phone,
				roc.other_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                rov.vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in,
                                SUBSTRING(rov.vehicle_color, 1, 9) AS vehicle_color,
                                rov.mileage_out,
                                roc.license_number,
                                ro.tag_no,
                                to_char(ro.delivery_date :: DATE, 'Mon dd yy') AS delivery_date,
                                to_char(ro.promised_time, 'HH24:MI') || ' ' || to_char(ro.promised_date, 'ddMONyy') AS promised,
                                to_char(ro.booked_time, 'HH24:MI') || ' ' || to_char(ro.booked_date, 'ddMONyy') AS ready,
                                ro.payment_code,
                                regexp_replace(ro.comments, '\r|\n', '', 'g') as comments,
                                ro.estimate_amount::numeric,
                                ro.ar_number,
                                ro.auth_number,
                                ro.po_number,
                                ro.terms_and_conditions,
                                COALESCE(ro.copy_number,'0') as copy_number,
                                ro.customer_signature,
                                ro.stock_number,
                                ro.tag_number,
                                ro.type,
				ro.status
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
    ($creation_date, $creation_time, $completion_date, $department, $branch, $sub_branch, $advisor, $customer_number, $customer_name, $customer_address, $customer_city,
     $customer_state, $customer_zip, $customer_phone, $work_phone, $other_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
     $mileage_in, $vehicle_color, $mileage_out, $license_number, $tag_no, $delivery_date, $promised, $ready, $payment_code, $comments, $estimate_amount,
     $ar_number, $auth_number, $po_number, $terms_and_conditions, $copy_number, $customer_signature, $stock_number, $tag_number,$type,$status ) = @header_row;
    push (@header_section, sprintf(border("#")."%105s".border("|")."\n", ""));
    push (@header_section, sprintf(border("#")."%101s %-2s ".border("|")."\n", "", "<--pg_num-->"));
    push (@header_section, sprintf(border("#")."%105s".border("|")."\n", ""));
    my $stock_internal;

if($type eq "InternalRepairOrder"){
 $stock_internal = '';
push (@header_section, sprintf(border(">").'~font{DejaVuSansMono-Bold9.5}'."%-44s".'~font{default}'."%-2s%-33s%-5s%-20s".border("|")."\n", $stock_number." Internal","", " A/R Number: ".$ar_number,"","Invoice Number"));
push (@header_section, sprintf(border(">")."%-15s".'~font{DejaVuSansMono-Bold9.5}'."%-30s".'~font{default}'."%-3s%-33s".border("|")."\n",
           "","Sold TO","","Customer Number: "));      
push (@header_section, sprintf(border(">")."%-17s%-30s%-1s%-35s".'~font{DejaVuSansMono-Bold11.5}'."%-5s%-10s".'~font{default}'.border("|")."\n",
           "",$customer_name,"", " PO Number: ".$auth_number,"",align_center($ro_number, 6)));
 push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%-27s%-26s%-1s%-38s%-1s%-20s".'~font{default}'.border("|")."\n",
            "Phone (H): ",$customer_address,""," Auth Number: ".$auth_number,""," Printed: " ));
  push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%-27s%-26s%-2s%-37s%-1s%-20s".border("|")."\n",
          "Phone (W): ",$customer_city.", ".$customer_state." ".$customer_zip,"","Service Writer: ".$advisor,""," Copy # ".$copy_number));
 
}
else{
 $stock_internal = $stock_number;
push (@header_section, sprintf(border(">").'~font{DejaVuSansMono-Bold9.5}'."%-44s".'~font{default}'."%-3s%-32s%-5s%-15s".'~font{default}'.border("|")."\n",$customer_name ,"","A/R Number: ".$ar_number,"","Invoice Number"));
push (@header_section, sprintf(border(">")."%-47s%-2s%-33s%-24s".'~font{default}'.border("|")."\n",
           $customer_address,"", "Customer Number: ".$customer_number,""));
push (@header_section, sprintf(border(">")."%-47s%-2s%-33s%-5s".'~font{DejaVuSansMono-Bold11.5}'."%-10s".border("|")."\n",
           $customer_city.", ".$customer_state." ".$customer_zip,"", "PO Number: ".$auth_number,"", align_center($ro_number, 6)));
 push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%-53s%-2s%-37s%-2s%-24s".'~font{default}'.border("|")."\n",
           "Phone (H): ".$other_phone,"", "Auth Number: ".$auth_number,"", "Printed: " ));
  push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%-53s%-2s%-37s%-2s%-24s".border("|")."\n",
           "Phone (W): ".$work_phone,"", "Service Writer: ".$advisor,"","Copy # ".$copy_number));

}
    

    
   # push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%-67s%-37s".'~font{DejaVuSansMono-Bold9.5}'."%-22s".'~font{HelveticaMono1}'.border("|")."\n",
    #       $customer_city.", ".$customer_state." ".$customer_zip, "PO Number: ".$po_number, align_center($ro_number, 15)));
   
    #push (@header_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s".'~font{default}'."\n", ""));   
   
  
 #push (@header_section, sprintf(border(">").'~font{HelveticaMono0.1}'."%119s".border("|")."\n", ""));   
 push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%-53s%-2s%-8s%-22s%-1s%-23s".border("|")."\n", "Cell Phone: ".$customer_phone,"", "Estimate Amount:",currency_format($estimate_amount),"","Date Opened: ".$creation_date));

    push (@header_section, sprintf(border(">")."%-53s%-2s%-37s%-2s%-23s".border("|")."\n", "Email Addr: ".$customer_email,"", "Terms & Conditions:".$terms_and_conditions,"","Date Notified:".$completion_date));
    
   #push (@header_section, sprintf(border(">")."%1s".border("|")."\n", "")); 

    push (@header_section, sprintf(border(">")."%-55s%-37s%-2s%-24s".border("|")."\n",
          "","Type of Sale: Retail","","Date Delivered:".$delivery_date));

    push (@header_section, sprintf(border(">")."%-53s".border("|")."\n",
          "Year/Make/Model:".$vehicle_year." ".$vehicle_make." ".$vehicle_model));


    push (@header_section, sprintf(border(">")."%-39s%-66s".border("|")."\n",
          "VIN: ".$vehicle_vin, ""));

    push (@header_section, sprintf(border(">")."%-18s%-11s%-32s".'~font{DejaVuSansMono-Bold9.5}'."%-35s".'~font{DejaVuSansMono8}'.border("|")."\n",
          "License Number: ".$license_number, "", "Color: ".$vehicle_color, "Customer"));

    push (@header_section, sprintf(border(">")."%-29s%-32s".'~font{DejaVuSansMono-Bold9.5}'."%-30s".'~font{DejaVuSansMono8}'.border("|")."\n",
          "Stock Number: ".$stock_internal, "Mileage In: ".$mileage_in, "Signature"));
    push (@header_section, sprintf(border(">")."%-29s%-10s%-66s".border("|")."\n",
          "Tag Number: ".$tag_number, "Mileage Out: ".$mileage_out, ""));
    push (@header_section, sprintf(border(">")."%119s".border("|")."\n", ""));
    push (@header_section, sprintf(border(">")."%119s".border("|")."\n", ""));
    push (@header_section, sprintf(border(">")."%119s".'~font{default}'.border("|")."\n", ""));

}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {

    my $job_qry = $conn->prepare("SELECT
                                    rl.ro_line,
                                    rl.complaint,
                                    rl.recommendation,
                                    rj.ro_job_line,
                                    left(rj.billing_code, 1) AS billing_code,
                                    rj.billing_code          AS billing_labor_type,
                                    rj.op_code,
                                    rj.op_description,
                                    rj.sold_hours,
                                    rj.labor_cost,
                                    rj.sale_amount,
                                    rj.cause,
                                    rj.correction,
                                    case when rj.job_added!='' then to_char(rj.job_added::date,'mm/dd/yy ')||to_char(rj.job_added::time,'HH12:MI AM') end as job_added
                                FROM repair_line rl
                                    JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ?
                                ORDER BY rl.ro_line :: numeric");
    $job_qry->execute($ro_number);
    my ( $ro_line, $complaint, $recommendation, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours, $cause, $correction, $job_added );
    while (my @job_row = $job_qry->fetchrow_array) {
        ( $ro_line, $complaint, $recommendation, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours, $labor_cost, $sale_amount, $cause, $correction, $job_added ) = @job_row;
        my @job_header_section;
        my @parts_header_section;

        # Job Complaint
        $Text::Wrap::columns = 65;
        my $wrapped_complaint = fill('', '', expand($billing_code."/ ".$complaint));
        my @complaint_list = split "\n", $wrapped_complaint;
        
       if ($status eq 'Cancelled'){
           push (@job_header_section, sprintf(border(">").'~font{DejaVuSansMono-BoldOblique9.5}~bggray{0.01}'."%-3s %-29s".'~bggray{1.0}~font{DejaVuSansMono-Bold9.5}'.
                                   "%-9s SvcWtr:%-14s   Job Added: %13s  ".'~font{default}'.border("|")."\n",
              $ro_line.".", "Customer statement of problem", "",
              "",
              $job_added));

       }
       else{
	    push (@job_header_section, sprintf(border(">").'~font{DejaVuSansMono-BoldOblique9.5}~bggray{0.01}'."%-3s %-29s".'~bggray{1.0}~font{DejaVuSansMono-Bold9.5}'.
                                   "%-9s SvcWtr:%-14s   Job Added: %13s  ".'~font{default}'.border("|")."\n",
              $ro_line.".", "Customer statement of problem", "",
              length($advisor)>14?substr($advisor, 0, 11)."...":$advisor,
              $job_added));
       } 
        foreach my $i (0 .. $#complaint_list) {
            push (@job_header_section, sprintf(border(">")."  %-65s%39s".border("|")."\n", $complaint_list[$i], " "));
        }
        push (@job_section, [@job_header_section]);

	# Job Cause
	my @job_cause_section;
        if($cause ne ''){
        	
	        my $wrapped_cause = fill('', '', expand($cause));
	        my @cause_list = split "\n", $wrapped_cause;
	        push (@job_cause_section, sprintf(border(">").'~font{DejaVuSansMono-Oblique9.5}'."     ".'~bggray{0.01}'."1 -- Cause/Action to Take".'~bggray{1.0}~font{default}'." %73s".border("|")."\n", ""));
	        foreach my $i (0 .. $#cause_list) {
	            push (@job_cause_section, sprintf(border(">")."   %-65s%37s".border(" |")."\n", $cause_list[$i], " "));
	        }
	        
	}
	else{

		push (@job_cause_section, sprintf(border("#")."%105s".border("|")."\n", ""));
	}
	push (@job_section, [@job_cause_section]);

        # Job Correction
        my @job_correction_section;
        my $wrapped_correction = fill('', '', expand($correction));
        my @correction_list = split "\n", $wrapped_correction;
        push (@job_correction_section, sprintf(border(">").'~font{DejaVuSansMono-Oblique9.5}'."     ".'~bggray{0.01}'."1 -- Correction/Action Taken".'~bggray{1.0}~font{default}'." %70s".border("|")."\n", ""));
        push (@job_correction_section, sprintf(border(">")."%-3s%-65s%-28s".'~font{DejaVuSansMono-Bold9.5}'."%8.2f".'~font{default}'.border("|")."\n","",$correction_list[0],"",$sale_amount));
        foreach my $i (1 .. $#correction_list) {
            push (@job_correction_section, sprintf(border(">")."%-3s%-65s%-37s".border(" |")."\n","",$correction_list[$i],""));
        }

        push (@job_section, [@job_correction_section]);

        my @tech_section = make_tech_section($ro_line, $ro_job_line);
        push (@job_section, [@tech_section]);

        ($job_cust_lbr_cost, $job_cust_lbr_sale, $job_cust_prt_cost, $job_cust_prt_sale, $job_cust_misc_cost, $job_cust_misc_sale, $job_cust_gog_cost, $job_cust_gog_sale,
         $job_cust_sublet_cost, $job_cust_sublet_sale, $job_cust_ded_cost, $job_cust_ded_sale )= (0)x12;

        ($job_intern_lbr_cost, $job_intern_lbr_sale, $job_intern_prt_cost, $job_intern_prt_sale, $job_intern_misc_cost, $job_intern_misc_sale, $job_intern_gog_cost,
         $job_intern_gog_sale, $job_intern_sublet_cost, $job_intern_sublet_sale, $job_intern_ded_cost, $job_intern_ded_sale )= (0)x12;

        ($job_warr_lbr_cost, $job_warr_lbr_sale, $job_warr_prt_cost, $job_warr_prt_sale, $job_warr_misc_cost, $job_warr_misc_sale, $job_warr_gog_cost, $job_warr_gog_sale,
         $job_warr_sublet_cost, $job_warr_sublet_sale, $job_warr_ded_cost, $job_warr_ded_sale )= (0)x12;

        ($job_sc_lbr_cost, $job_sc_lbr_sale, $job_sc_prt_cost, $job_sc_prt_sale, $job_sc_misc_cost, $job_sc_misc_sale, $job_sc_gog_cost, $job_sc_gog_sale,
         $job_sc_sublet_cost, $job_sc_sublet_sale, $job_sc_ded_cost, $job_sc_ded_sale )= (0)x12;

        if (lc($billing_code) eq 'i'){
            $job_intern_lbr_sale = $job_intern_lbr_sale + $sale_amount;
            $job_intern_lbr_cost = $job_intern_lbr_cost + $labor_cost;
        } elsif (lc($billing_code) eq 'c'){
            $job_cust_lbr_sale = $job_cust_lbr_sale + $sale_amount;
            $job_cust_lbr_cost = $job_cust_lbr_cost + $labor_cost;
        } elsif (lc($billing_code) eq 'w'){
            $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
            $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
        } elsif (lc($billing_code) eq 's'){
            $job_sc_lbr_sale = $job_sc_lbr_sale + $sale_amount;
            $job_sc_lbr_cost = $job_sc_lbr_cost + $labor_cost;
        }

        my @parts_section = make_parts_section($ro_line, $ro_job_line);

        push (@job_section,[@parts_section]);

        my @misc_section = make_misc_section($ro_line);
        push (@job_section, [@misc_section]);

        my @gog_section = make_gog_section($ro_line);
        push (@job_section, [@gog_section]);

        my @sublet_section = make_sublet_section($ro_line, $billing_labor_type);
        push (@job_section, [@sublet_section]);

        my @job_total_section = make_job_total_section($ro_line);
        push (@job_section, [@job_total_section]);

        my @lbr_item_section = make_lbr_itme_section($ro_line, $ro_job_line);
        push (@job_section, [@lbr_item_section]);

        # Job Recommendation
        if ($recommendation) {
            my @job_recommendation_section;
            my $wrapped_recommendation = fill('', '', expand($recommendation));
            my @recommendation_list = split "\n", $wrapped_recommendation;
            push (@job_recommendation_section, sprintf(border(">").'~font{DejaVuSansMono-BoldOblique9.5}'."     Recommendations".'~font{default}'." %84s".border("|")."\n", ""));
            push (@job_recommendation_section, sprintf(border(">")."   %-65s%37s".border("|")."\n", $recommendation_list[0], ""));
            foreach my $i (1 .. $#recommendation_list) {
                push (@job_recommendation_section, sprintf(border(">")."   %-65s%37s".border(" |")."\n", $recommendation_list[$i], " "));
            }
            push (@job_section, [@job_recommendation_section]);
        }

        my @ded_section = make_deductible_section($ro_line);
        push (@job_section, [@ded_section]);

        my $techs_qry = "SELECT
                            string_agg(DISTINCT tech_id, ', ')
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ?";

        my ($job_techs) = $conn->selectrow_array($techs_qry, undef, ($ro_number, $ro_line));

        }
}

# Subroutine to prepare the TECH section of a JOB
sub make_tech_section{
    my ($ro_line, $ro_job_line) = @_;
    my @tech_section_array;
    my $tech_details_qry = $conn->prepare("SELECT
                                                ro_job_tech_line,
                                                tech_id,
                                                tech_name,
						case when repair_in!='' and repair_out!='' then  to_char(repair_in::date, 'dd MON yy')|| ' ' ||to_char(repair_in::time, 'HH24:MI:SS') end AS work_start_time,
						case when repair_in!='' and repair_out!='' then to_char(repair_out::date, 'dd MON yy')|| ' ' ||to_char(repair_out::time, 'HH24:MI:SS') end AS work_end_time, 
                                                actual_hours,
                                                booked_hours,
                                                tech_lbr_cost::float,
                                                tech_lbr_sale,
                                                td.repair_in,
                                                td.repair_out        
                                            FROM repair_job_technician_detail td
                                              LEFT JOIN repair_job_labor_item USING(ro_number, ro_line, ro_job_line)
                                            WHERE td.ro_number = ? AND td.ro_line = ? AND td.ro_job_line = ?");
    $tech_details_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_job_tech_line, $tech_id, $tech_name, $work_start_time, $work_end_time, $actual_hours, $booked_hours, $tech_lbr_cost, $tech_lbr_sale, $repair_in, $repair_out);
    
    if($tech_details_qry->rows > 0){
        while (my @tech_row = $tech_details_qry->fetchrow_array) {
            ($ro_job_tech_line, $tech_id, $tech_name, $work_start_time, $work_end_time, $actual_hours, $booked_hours, $tech_lbr_cost, $tech_lbr_sale, $repair_in, $repair_out) = @tech_row;
            	push (@tech_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold7.5}'." %1s %18s Warranty ID %13s Tech %20s lop Act Hr %40s  ".'~font{default}'.border("|")."\n",
                                    "","","","",""));
                if(  $actual_hours != 0 ){

			push (@tech_section_array, sprintf(border(">")." Repair   In: %-17s    Out: %-16s%10.2f%20s".border("|")."\n",
                                                    $work_start_time, $work_end_time, $actual_hours, ""));
		}

            	push (@tech_section_array, sprintf(border(">")."%28s %-7s  %-20s  %-3s  %4.2f %7.2f %4.2f  %6.2f %7.2f %6s".border("|")."\n",
                                                    "", $tech_id, $tech_name, "1", $actual_hours, $tech_lbr_cost,
                                                     $booked_hours, $tech_lbr_sale, $tech_lbr_sale*$booked_hours, ""));
        }
    }
    return @tech_section_array;
}

# Subroutine to prepare the Labor item section of a JOB
sub make_lbr_itme_section{
    my ($ro_line, $ro_job_line) = @_;
    my @lbr_item_section_array;
    my $lbr_item_qry = $conn->prepare("SELECT
                                            op_code,
                                            lbr_hrs,
                                            other_lbr_hrs,
                                            setup_hrs,
                                            diagnosis_hrs,
                                            paint_hrs,
                                            admin_hrs,
                                            additional_hrs,
                                            cc,
                                            fc_odbii,
                                            author,
                                            auth_code,
                                            cr
                                        FROM repair_job_labor_item
                                        WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
    $lbr_item_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $op_code, $lbr_hrs, $other_lbr_hrs, $setup_hrs, $diagnosis_hrs, $paint_hrs , $admin_hrs, $additional_hrs, $cc, $fc_odbii, $author, $auth_code, $cr);

    if($lbr_item_qry->rows > 0){
        push (@lbr_item_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold7.5}'." %1s Iop %1s GM Op Code %6s CC %2s FC/ODBII %2s Author   Auth Code    FLH    OLH   CR    Setup   Diag   Paint   Admin  Additional ".'~font{default}'.border("|")."\n",
                                    "","","","",""));
        while (my @lbr_item_row = $lbr_item_qry->fetchrow_array) {
            ( $op_code, $lbr_hrs, $other_lbr_hrs, $setup_hrs, $diagnosis_hrs, $paint_hrs , $admin_hrs, $additional_hrs, $cc, $fc_odbii, $author, $auth_code, $cr) = @lbr_item_row;
            push (@lbr_item_section_array, sprintf(border(">")."   %1s %-16s %-4s %-9s  %-8s %-7s %5.2f %5.2f %-3s %5.2f %5.2f  %5.2f %5.2f  %5.2f   ".border("|")."\n",
                                                "1", $op_code, $cc, $fc_odbii, $author, $auth_code, $lbr_hrs, $other_lbr_hrs, $cr, $setup_hrs, $diagnosis_hrs, $paint_hrs , $admin_hrs, $additional_hrs));
        }
        push (@lbr_item_section_array, sprintf(border("#")."%105s".border(" |")."\n", "  "));
    }
    return @lbr_item_section_array;
}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line) = @_;
    my @parts_section_array;

    my $parts_qry = $conn->prepare("SELECT DISTINCT ON (ro_part_line)
                                        ro_part_line,
                                        part_number,
                                        part_description,
                                        left(nullif(prt_billing_type, 'NA'), 1)   AS prt_billing_code,
                                        nullif(prt_billing_type, 'NA')            AS prt_billing_type,
                                        quantity_sold,
                                        unit_cost,
                                        unit_sale,
                                        unit_core_cost,
                                        unit_core_sale
                                    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?
                                    ORDER BY ro_part_line");
    $parts_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
         $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale);

    $parts_entries = $parts_qry->rows;

    if($parts_entries > 0){
        push (@parts_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold7.5}'." %1s Iop %1s Part Number %3s Failed %4s Description %76s  ".'~font{default}'.border("|")."\n",
                                            "","","","",""));
        my $index = 1;                                   
        while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
              $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale) = @parts_row;

            $prt_billing_code = ($prt_billing_code?$prt_billing_code:$job_billing_code);
            $part_number = $part_number;
            $part_description =  $part_description;
            $Text::Wrap::columns = 24;
            my $wrapped_part_description = fill('', '', expand($part_description));
            my @part_description_list = split "\n", $wrapped_part_description;
               
                if($quantity_sold == 0){
                    $index = 0;
                } 
                
                push (@parts_section_array, sprintf(border(">")." %-2s%-2s   %-15s        %-27s  %10s %7.2f%4s  %6.2f %7.2f %6s".border("|")."\n",
                "", $index, $part_number,$part_description_list[0],"",
                    $unit_cost,$quantity_sold,$unit_sale, $unit_sale*$quantity_sold, ""));
                $index ++;

                foreach my $i (1 .. $#part_description_list) {
                    push (@parts_section_array, sprintf(border(">")."%-32s%-32s%-35s".border("|")."\n", "", $part_description_list[$i], ""));
                }
                if ($unit_core_cost != 0 || $unit_core_sale != 0){
                    push (@parts_section_array, sprintf(border(">")."%3s%-27s %7.2f %7.2f    %d  %7.2f %7.2f %8.2f           ".border("|")."\n",
                                                    "", "CORE CHARGE", $unit_core_cost*$quantity_sold, $unit_core_sale*$quantity_sold, 0,
                                                    $unit_core_sale, $unit_core_sale, $unit_core_sale*$quantity_sold));
                }
                if (lc($prt_billing_code) eq 'i'){
                    $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                    $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                } elsif (lc($prt_billing_code) eq 'c'){
                    $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                    $job_cust_prt_cost = $job_cust_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                } elsif (lc($prt_billing_code) eq 'w'){
                    $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                    $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                } elsif (lc($prt_billing_code) eq 's'){
                    $job_sc_prt_sale = $job_sc_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                    $job_sc_prt_cost = $job_sc_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                }
        }
      }
    push (@parts_section_array, sprintf(border(">")."%48s ".'~bggray{0.01}'."Sub Total Parts".'~bggray{1.0}'." %4s  ".'~bggray{0.01}'."%7.2f".'~bggray{1.0}'." %18s".'~bggray{0.01}'."%8.2f".'~bggray{1.0}'.border("|")."\n",
                                  "","", $job_intern_prt_cost+$job_cust_prt_cost+$job_warr_prt_cost+$job_sc_prt_cost,"",$job_intern_prt_sale+$job_cust_prt_sale+$job_warr_prt_sale+$job_sc_prt_sale ));
   
    return @parts_section_array;
}

# Subroutine to prepare the MISC section of a JOB
sub make_misc_section{
    my ($ro_line) = @_;
    my @misc_section_array;
    my $misc_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       left(other_billing_type, 1) AS other_billing_type,
                                       other_cost,
                                       other_sale
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line =? AND item_type = 'MISC' AND other_cost::numeric > -1");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale);
    $misc_entries = $misc_qry->rows;
     # push (@misc_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold8.5}'." %1s Miscellaneous Charges and Deductions %72s".'~font{default}'.border("|")."\n",
     #                                    "",""));
    # push (@misc_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold7.5}'." %1s Payment Notes %72s".'~font{default}'.border("|")."\n",
      #                                   "",""));
    # push (@misc_section_array, sprintf(border("#").'~font{DejaVuSansMono8}'." %1s %3s %52s".'~font{default}'.border("|")."\n",
       #                                  "",$comments,""));
    if($misc_entries > 0){
           push (@misc_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold8.5}'." %1s Miscellaneous Charges and Deductions %72s".'~font{default}'.border("|")."\n",
                                         "",""));
           while (my @misc_row = $misc_qry->fetchrow_array) {
            ( $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale) = @misc_row;
            $Text::Wrap::columns = 28;
            $misc_description =$misc_description;
            my $wrapped_misc_description = fill('', '', expand($misc_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;
            
            push (@misc_section_array, sprintf(border(">")."   %-28s %38s %7.2f %19s %6.2f".border("|")."\n",
                                               $misc_description_list[0],"","0.00","",$misc_cost));

            foreach my $i (1 .. $#misc_description_list) {
                push (@misc_section_array, sprintf(border(">")." %-28s%-49s".border("|")."\n", $misc_description_list[$i], ""));
            }

            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $job_intern_misc_sale = $job_intern_misc_sale + $misc_sale;
                $job_intern_misc_cost = $job_intern_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 'c'){
                $job_cust_misc_sale = $job_cust_misc_sale + $misc_sale;
                $job_cust_misc_cost = $job_cust_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $job_warr_misc_sale = $job_warr_misc_sale + $misc_sale;
                $job_warr_misc_cost = $job_warr_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 's'){
                $job_sc_misc_sale = $job_sc_misc_sale + $misc_sale;
                $job_sc_misc_cost = $job_sc_misc_cost + $misc_cost;
            }
        }
    }
    return @misc_section_array;
}


# Subroutine to prepare the MISC section of a JOB
sub make_misc_service_section{
    my $misc_service_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       left(other_billing_type, 1) AS other_billing_type,
                                       other_cost,
                                       other_sale
                                   FROM repair_other
                                   WHERE ro_number = ?  AND item_type = 'MISC' AND other_cost::numeric < 0 LIMIT 1");
    $misc_service_qry->execute($ro_number);
    my ( $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale);
    $misc_service_entries = $misc_service_qry->rows;
     # push (@misc_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold8.5}'." %1s Miscellaneous Charges and Deductions %72s".''.border("|")."\n",
     #                                    "",""));
    # push (@misc_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold7.5}'." %1s Payment Notes %72s".''.border("|")."\n",
      #                                   "",""));
    # push (@misc_section_array, sprintf(border("#").''." %1s %3s %52s".''.border("|")."\n",
       #                                  "",$comments,""));
    if($misc_service_entries > 0){
           push (@ro_make_misc_service_section, sprintf(border("#").'~font{DejaVuSansMono-Bold8.5}'." %1s Miscellaneous Charges and Deductions %72s".''.border("|")."\n",
                                         "",""));
           while (my @misc_row = $misc_service_qry->fetchrow_array) {
            ( $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale) = @misc_row;
            $Text::Wrap::columns = 28;
            $misc_description =$misc_description;
            my $wrapped_misc_description = fill('', '', expand($misc_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;
            push (@ro_make_misc_service_section, sprintf(border(">")."   %-28s %43s %7.2f %20s %6.2f".border("|")."\n",
                                               $misc_description_list[0],"","0.00","",$misc_cost));

            foreach my $i (1 .. $#misc_description_list) {
                push (@ro_make_misc_service_section, sprintf(border(">")." %-28s%-49s".border("|")."\n", $misc_description_list[$i], ""));
            }

        }
    }
    return @ro_make_misc_service_section;
}

# Subroutine to prepare the GOG section of a JOB
sub make_gog_section{
    my ($ro_line) = @_;
    my @gog_section_array;
    my $gog_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    left(other_billing_type, 1) AS other_billing_type,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'GOG'");
    $gog_qry->execute($ro_number, $ro_line);
    my ( $gog_code, $gog_description, $other_billing_type, $gog_cost, $gog_sale);

    $gog_entries = $gog_qry->rows;
    if($gog_qry->rows > 0){

        while (my @gog_row = $gog_qry->fetchrow_array) {
            ( $gog_code, $gog_description, $other_billing_type, $gog_cost, $gog_sale) = @gog_row;
            $Text::Wrap::columns = 28;
            $gog_description = $gog_code." ".$gog_description;
            my $wrapped_gog_description = fill('', '', expand($gog_description));
            my @gog_description_list = split "\n", $wrapped_gog_description;
            push (@gog_section_array, sprintf(border(">")."  %-28s %7.2f %7.2f %13s %7.2f %8.2f ".border("|")."\n",
                                               $gog_description_list[0], $gog_cost, $gog_sale, "", $gog_cost, $gog_sale));

            foreach my $i (1 .. $#gog_description_list) {
                push (@gog_section_array, sprintf(border(">")." %-28s%-49s".border("|")."\n", "", $gog_description_list[$i], ""));
            }
            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $job_intern_gog_sale = $job_intern_gog_sale + $gog_sale;
                $job_intern_gog_cost = $job_intern_gog_cost + $gog_cost;
            } elsif (lc($other_billing_type) eq 'c'){
                $job_cust_gog_sale = $job_cust_gog_sale + $gog_sale;
                $job_cust_gog_cost = $job_cust_gog_cost + $gog_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $job_warr_gog_sale = $job_warr_gog_sale + $gog_sale;
                $job_warr_gog_cost = $job_warr_gog_cost + $gog_cost;
            } elsif (lc($other_billing_type) eq 's'){
                $job_sc_gog_sale = $job_sc_gog_sale + $gog_sale;
                $job_sc_gog_cost = $job_sc_gog_cost + $gog_cost;
            }
        }
    }
    return @gog_section_array;
}

# Subroutine to prepare the SUBLET section of a JOB
sub make_sublet_section{
    
    my ($ro_line, $billing_code) = @_;
    my @sublet_section_array;

    my ( $sublet_total_cost, $sublet_total_sale );
    my $sublet_sum_qry = $conn->prepare("SELECT
                                        coalesce(nullif(SUM(other_cost), '0.00'), 0.00) ::numeric AS sublet_total_cost,
                                        coalesce(nullif(SUM(other_sale), '0.00'), 0.00) ::numeric AS sublet_total_sale
                                    FROM du_dms_dealerbuilt_proxy.repair_other
                                    WHERE ro_number = ? AND
                                    ro_line = ? AND item_type = 'SUBLET'");
    $sublet_sum_qry->execute($ro_number, $ro_line);

    while (my @sublet_total_row = $sublet_sum_qry->fetchrow_array) {
            ( $sublet_total_cost, $sublet_total_sale ) = @sublet_total_row;
    }

    my $sublet_qry = $conn->prepare("SELECT
                                        other_code,
                                        other_description,
                                        left(other_billing_type, 1) AS other_billing_type,
                                        other_cost,
                                        other_sale
                                    FROM repair_other
                                    WHERE ro_number = ? AND ro_line =? AND item_type = 'SUBLET'");
    $sublet_qry->execute($ro_number, $ro_line);
    my ( $sublet_code, $sublet_description, $other_billing_type, $sublet_cost, $sublet_sale);

    $sublet_entries = $sublet_qry->rows;

    if($sublet_total_cost > 0 || $sublet_total_sale > 0){
         if($sublet_entries > 0){

        push (@sublet_section_array, sprintf(border(">").'~font{DejaVuSansMono-BoldOblique9}'."%-4s %-96s".'~font{default}'.border("|")."\n",
                                             "", "Sublet"));

        while (my @sublet_row = $sublet_qry->fetchrow_array) {
            ( $sublet_code, $sublet_description, $other_billing_type, $sublet_cost, $sublet_sale) = @sublet_row;
            push (@sublet_section_array, sprintf(border(">")." %-3s %-65s %7.2f %18s %7.2f  ".border("|")."\n",
                                                    "", $sublet_code. " ". $sublet_description, $sublet_cost, "", $sublet_sale));

            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $job_intern_sublet_sale = $job_intern_sublet_sale + $sublet_sale;
                $job_intern_sublet_cost = $job_intern_sublet_cost + $sublet_cost;
            } elsif (lc($other_billing_type) eq 'c'){
                $job_cust_sublet_sale = $job_cust_sublet_sale + $sublet_sale;
                $job_cust_sublet_cost = $job_cust_sublet_cost + $sublet_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $job_warr_sublet_sale = $job_warr_sublet_sale + $sublet_sale;
                $job_warr_sublet_cost = $job_warr_sublet_cost + $sublet_cost;
            } elsif (lc($other_billing_type) eq 's'){
                $job_sc_sublet_sale = $job_sc_sublet_sale + $sublet_sale;
                $job_sc_sublet_cost = $job_sc_sublet_cost + $sublet_cost;
            }
        }
       }
    }
    
    return @sublet_section_array;
}

# Subroutine to prepare the DEDUCTIBLE section of a JOB
sub make_deductible_section{
    my ($ro_line) = @_;
    my @ded_section_array;
    my $ded_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'DEDUCTIBLE'");
    $ded_qry->execute($ro_number, $ro_line);
    my ( $ded_code, $ded_description, $ded_cost, $ded_sale);

    $ded_entries = $ded_qry->rows;
    if($ded_entries > 0){

        while (my @ded_row = $ded_qry->fetchrow_array) {
            ( $ded_code, $ded_description, $ded_cost, $ded_sale) = @ded_row;
            push (@ded_section_array, sprintf(border(">")."%-69s%8.2f ".border("|")."\n",
                                                "CUSTOMER PAY DEDUCTIBLE FOR LINE ".$ro_line, $ded_sale));
            $job_cust_ded_sale = $job_cust_ded_sale + $ded_sale;
            $job_cust_ded_cost = $job_cust_ded_cost + $ded_cost;
        }
    }
    $tot_cust_ded_sale = $tot_cust_ded_sale+$job_cust_ded_sale;
    return @ded_section_array;
}

# Subroutine to prepare the TOTAL section of a JOB
sub make_job_total_section{

    my ($ro_line) = @_;
    my @job_total_section_array;

     push (@job_total_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold8.5}'."%-50s".'~bggray{0.01}'."SubTotal Job #".'~bggray{1.0}'."%-5s%-34s".'~bggray{0.01}'."%8.2f".'~bggray{1.0}~font{default}'.border("|")."\n",
                                       "",$ro_line,"",$job_intern_prt_sale+$job_cust_prt_sale+$job_warr_prt_sale+$job_sc_prt_sale+
                                                      $job_intern_sublet_sale+$job_cust_sublet_sale+$job_warr_sublet_sale+$job_sc_sublet_sale+$sale_amount+
                                                      $job_intern_misc_cost+$job_cust_misc_cost+ $job_warr_misc_cost+$job_sc_misc_cost));
    push (@job_total_section_array, sprintf(border("#")."%105s".border(" |")."\n", "  "));

    $tot_cust_lbr_cost = $tot_cust_lbr_cost+$job_cust_lbr_cost;
    $tot_cust_lbr_sale = $tot_cust_lbr_sale+$job_cust_lbr_sale;
    $tot_cust_prt_cost = $tot_cust_prt_cost+$job_cust_prt_cost;
    $tot_cust_prt_sale = $tot_cust_prt_sale+$job_cust_prt_sale;
    $tot_cust_misc_cost = $tot_cust_misc_cost+$job_cust_misc_cost;
    $tot_cust_misc_sale = $tot_cust_misc_sale+$job_cust_misc_sale;
    $tot_cust_gog_cost = $tot_cust_gog_cost+$job_cust_gog_cost;
    $tot_cust_gog_sale = $tot_cust_gog_sale+$job_cust_gog_sale;
    $tot_cust_sublet_cost = $tot_cust_sublet_cost+$job_cust_sublet_cost;
    $tot_cust_sublet_sale = $tot_cust_sublet_sale+$job_cust_sublet_sale;
    $tot_cust_ded_sale = $tot_cust_ded_sale+$job_cust_ded_sale;

    $tot_warr_lbr_cost = $tot_warr_lbr_cost+$job_warr_lbr_cost;
    $tot_warr_lbr_sale = $tot_warr_lbr_sale+$job_warr_lbr_sale;
    $tot_warr_prt_cost = $tot_warr_prt_cost+$job_warr_prt_cost;
    $tot_warr_prt_sale = $tot_warr_prt_sale+$job_warr_prt_sale;
    $tot_warr_misc_cost = $tot_warr_misc_cost+$job_warr_misc_cost;
    $tot_warr_misc_sale = $tot_warr_misc_sale+$job_warr_misc_sale;
    $tot_warr_gog_cost = $tot_warr_gog_cost+$job_warr_gog_cost;
    $tot_warr_gog_sale = $tot_warr_gog_sale+$job_warr_gog_sale;
    $tot_warr_sublet_cost = $tot_warr_sublet_cost+$job_warr_sublet_cost;
    $tot_warr_sublet_sale = $tot_warr_sublet_sale+$job_warr_sublet_sale;

    $tot_sc_lbr_cost = $tot_sc_lbr_cost+$job_sc_lbr_cost;
    $tot_sc_lbr_sale = $tot_sc_lbr_sale+$job_sc_lbr_sale;
    $tot_sc_prt_cost = $tot_sc_prt_cost+$job_sc_prt_cost;
    $tot_sc_prt_sale = $tot_sc_prt_sale+$job_sc_prt_sale;
    $tot_sc_misc_cost = $tot_sc_misc_cost+$job_sc_misc_cost;
    $tot_sc_misc_sale = $tot_sc_misc_sale+$job_sc_misc_sale;
    $tot_sc_gog_cost = $tot_sc_gog_cost+$job_sc_gog_cost;
    $tot_sc_gog_sale = $tot_sc_gog_sale+$job_sc_gog_sale;
    $tot_sc_sublet_cost = $tot_sc_sublet_cost+$job_sc_sublet_cost;
    $tot_sc_sublet_sale = $tot_sc_sublet_sale+$job_sc_sublet_sale;

    $tot_intern_lbr_cost = $tot_intern_lbr_cost+$job_intern_lbr_cost;
    $tot_intern_lbr_sale = $tot_intern_lbr_sale+$job_intern_lbr_sale;
    $tot_intern_prt_cost = $tot_intern_prt_cost+$job_intern_prt_cost;
    $tot_intern_prt_sale = $tot_intern_prt_sale+$job_intern_prt_sale;
    $tot_intern_misc_cost = $tot_intern_misc_cost+$job_intern_misc_cost;
    $tot_intern_misc_sale = $tot_intern_misc_sale+$job_intern_misc_sale;
    $tot_intern_gog_cost = $tot_intern_gog_cost+$job_intern_gog_cost;
    $tot_intern_gog_sale = $tot_intern_gog_sale+$job_intern_gog_sale;
    $tot_intern_sublet_cost = $tot_intern_sublet_cost+$job_intern_sublet_cost;
    $tot_intern_sublet_sale = $tot_intern_sublet_sale+$job_intern_sublet_sale;

    return @job_total_section_array;
}

# Subroutine to get the RO TAX amount
sub get_ro_tax_amount {
    my $tax_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ? AND item_type = 'TAX'");
    $tax_qry->execute($ro_number);
    my ( $tax_type, $tax_amount);

    while (my @tax_row = $tax_qry->fetchrow_array) {
        ( $tax_type, $tax_amount) = @tax_row;
        if (lc($tax_type) eq "c"){
            $cust_ro_tax = $tax_amount;
        }elsif (lc($tax_type) eq "w"){
            $warr_ro_tax = $tax_amount;
        }elsif (lc($tax_type) eq "s"){
            $sc_ro_tax = $tax_amount;
        }elsif (lc($tax_type) eq "i"){
            $intern_ro_tax = $tax_amount;
        }
    }
}

# Subroutine to get the RO Account detail
sub get_ro_account_detail {
   
}

# Subroutine to prepare ro MISC section of a JOB

sub make_ro_misc_section {
    my $ro_misc_qry = $conn->prepare(   "SELECT
                                       other_code,
                                       other_description,
                                       left(other_billing_type, 1) AS other_billing_type,
                                       other_cost,
                                       other_sale
                                   FROM repair_other
                                  WHERE ro_number = ?
                                  AND ro_line IS NULL AND item_type = 'MISC'");
   $ro_misc_qry->execute($ro_number);
    my ( $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale);
    
    
    if($ro_misc_qry->rows > 0){
    	while (my @ro_misc_row = $ro_misc_qry->fetchrow_array) {
        	( $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale) = @ro_misc_row;
		push (@ro_misc_section, sprintf(border(">").'~font{DejaVuSansMono-Bold9.5}'."%2s Miscellaneous Charges and Deductions For All Jobs %47s".'~font{default}'.border("|")."\n",
                                     "", ""));
	      push (@ro_misc_section, sprintf(border(">")."%2s %-30s %43.2f    %22.2f    ".border("|")."\n",
                                     "",$misc_description, $misc_cost, $misc_sale));
	
        	if (lc($other_billing_type) eq 'i'){
                	$job_intern_misc_sale = $job_intern_misc_sale + $misc_sale;
	                $job_intern_misc_cost = $job_intern_misc_cost + $misc_cost;
        	} elsif (lc($other_billing_type) eq 'c'){
                	$job_cust_misc_sale = $job_cust_misc_sale + $misc_sale;
	                $job_cust_misc_cost = $job_cust_misc_cost + $misc_cost;
        	} elsif (lc($other_billing_type) eq 'w'){
                	$job_warr_misc_sale = $job_warr_misc_sale + $misc_sale;
	                $job_warr_misc_cost = $job_warr_misc_cost + $misc_cost;
        	} elsif (lc($other_billing_type) eq 's'){
                	$job_sc_misc_sale = $job_sc_misc_sale + $misc_sale;
	                $job_sc_misc_cost = $job_sc_misc_cost + $misc_cost;
	        }
    	}
    }
    return @ro_misc_section;
}

# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_grand_total_section {
    get_ro_tax_amount();

     
     my ( $internal_balance, $customer_balance, $service_balance, $warranty_balance, $internal_charge ); 

     my ( $cust_ro_rental, $warr_ro_rental, $sc_ro_rental, $intern_ro_rental);

     my $rental_qry = $conn->prepare("SELECT
                                      other_billing_type,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ? AND item_type = 'RENTAL'");
    $rental_qry->execute($ro_number);
   
    my ( $rental_type, $rental_sale);

    while (my @rental_row = $rental_qry->fetchrow_array) {
        ( $rental_type, $rental_sale ) = @rental_row;
        if (lc($rental_type) eq "c"){
            $cust_ro_rental = $rental_sale;
        }elsif (lc($rental_type) eq "w"){
            $warr_ro_rental = $rental_sale;
        }elsif (lc($rental_type) eq "s"){
            $sc_ro_rental = $rental_sale;
        }elsif (lc($rental_type) eq "i"){
            $intern_ro_rental = $rental_sale;
        }
    }


    my ( $warranty_balance_due, $warranty_orginal_amount, $internal_balance_due, $internal_orginal_amount, $service_balance_due, $service_orginal_amount, $customer_orginal_amount );
    my $deductible_qry = $conn->prepare("SELECT
                                      warranty_balance_due,
                                      warranty_orginal_amount,
                                      internal_balance_due,
                                      internal_orginal_amount,
                                      service_balance_due,
                                      service_orginal_amount,
                                      customer_orginal_amount
                                  FROM repair_rodeductable_detail
                                  WHERE ro_number = ?");
    $deductible_qry->execute($ro_number);

      while (my @deductible_row = $deductible_qry->fetchrow_array) {
        (  $warranty_balance_due, $warranty_orginal_amount, $internal_balance_due, $internal_orginal_amount, $service_balance_due, $service_orginal_amount, $customer_orginal_amount ) = @deductible_row;
    }


    	
    my $ro_total_qry = $conn->prepare(   "SELECT * 
                                          FROM du_dms_dealerbuilt_proxy.repair_rototal_detail rd
                                          WHERE ro_number = ?");
    $ro_total_qry->execute($ro_number);
    my ( $ro,$tot_freight_amount,$tot_hazmat_amount,$tot_labor_amount,$tot_parts_amount,$tot_misc_amount,$tot_sublet_amount,$tot_tax_amount,$tot_slstax_amount,$tot_charge_amount,$tot_ro_pay_type);
    if($ro_total_qry->rows > 0){
    	while (my @ro_total_row = $ro_total_qry->fetchrow_array) {
        	($ro,$tot_freight_amount,$tot_hazmat_amount,$tot_labor_amount,$tot_parts_amount,$tot_misc_amount,$tot_sublet_amount,$tot_tax_amount,$tot_slstax_amount,$tot_charge_amount,$tot_ro_pay_type) = @ro_total_row;
                    if($tot_ro_pay_type eq "Customer"){
                        $customer_tot_freight_amount=$tot_freight_amount;
                        $customer_tot_hazmat_amount=$tot_hazmat_amount;
                        $customer_tot_slstax_amount=$tot_slstax_amount;
                        $customer_tot_charge_amount=$tot_charge_amount;
                        $customer_tot_misc_amount=$tot_misc_amount;
                    }
                      if($tot_ro_pay_type eq "Warranty"){
                        $warranty_tot_freight_amount=$tot_freight_amount;
                        $warranty_tot_hazmat_amount=$tot_hazmat_amount;
                        $warranty_tot_slstax_amount=$tot_slstax_amount;
                        $warranty_tot_charge_amount=$tot_charge_amount;
                        $warranty_tot_misc_amount=$tot_misc_amount;
                    }
                    if($tot_ro_pay_type eq "Internal"){
                        $internal_tot_freight_amount=$tot_freight_amount;
                        $internal_tot_hazmat_amount=$tot_hazmat_amount;
                        $internal_tot_slstax_amount=$tot_slstax_amount;
                        $internal_tot_charge_amount=$tot_charge_amount;
                        $internal_tot_misc_amount=$tot_misc_amount;
                    }
                        if($tot_ro_pay_type eq "ServiceContract"){
                        $service_tot_freight_amount=$tot_freight_amount;
                        $service_tot_hazmat_amount=$tot_hazmat_amount;
                        $service_tot_slstax_amount=$tot_slstax_amount;
                        $service_tot_charge_amount=$tot_charge_amount;
                        $service_tot_misc_amount=$tot_misc_amount;
                    }

        }
    }


    my ($customer_deductible, $service_deductible, $warranty_deductible, $internal_deductible);
    
    $internal_balance = $tot_intern_lbr_sale+$tot_intern_prt_sale+$tot_intern_sublet_sale+$internal_tot_slstax_amount+$tot_intern_misc_cost+$intern_ro_rental; 
    
    $customer_balance = $tot_cust_lbr_sale+$tot_cust_prt_sale+$tot_cust_sublet_sale+$tot_cust_misc_cost+$cust_ro_rental+$customer_tot_freight_amount 
    +$customer_tot_slstax_amount+$tot_cust_ded_sale+$customer_tot_slstax_amount;
    
    $service_balance = $tot_sc_lbr_sale+$tot_sc_prt_sale+$tot_sc_sublet_sale+$service_tot_slstax_amount+$tot_sc_ded_sale+$tot_sc_misc_cost+$sc_ro_rental; 
    
    $warranty_balance = $tot_warr_lbr_sale+$tot_warr_prt_sale+$tot_warr_sublet_sale+$warranty_tot_slstax_amount+$tot_warr_misc_cost+$warr_ro_rental; 

    $customer_deductible = $customer_orginal_amount - $customer_balance;
    if($customer_deductible < 0){
        $customer_deductible = 0.00;
    }

    $service_balance = sprintf("%.2f", $service_balance);
    $service_orginal_amount = sprintf("%.2f", $service_orginal_amount); 

    # print "\n";
    # print "-ro_number:".$ro_number."tot_intern_misc_sale".$tot_intern_misc_sale."-".$tot_intern_misc_cost."tot_intern_sublet_sale".$tot_intern_sublet_sale;
    # print "\n";

    # print "\n";
    # print "-ro_number:".$ro_number."service_deductible".$service_deductible."service_balance".$service_balance;
    # print "\n";

    if( $service_balance == 0 || $service_balance_due == 0){
       $service_deductible = sprintf("%.2f", $service_balance_due);
    } elsif( $service_balance == $service_orginal_amount ){
       $service_balance = $service_balance - abs($service_balance_due);
       $service_deductible = 0.00;
    } else { 
       $service_deductible = sprintf("%.2f", $service_balance-($service_orginal_amount-abs($service_balance_due)));
       $service_balance =  sprintf("%.2f", $service_balance-($service_deductible));
    }

    # print "\n";
    # print "+ro_number:".$ro_number."service_deductible".$service_deductible."service_balance".$service_balance;
    # print "\n";


    # print "\n";
    # print "-ro_number:".$ro_number."warranty_orginal_amount:".$warranty_orginal_amount."warranty_deductible:".$warranty_deductible."warranty_balance:".$warranty_balance;
    # print "\n";

  
    $warranty_balance = sprintf("%.2f", $warranty_balance);
    $warranty_orginal_amount = sprintf("%.2f", $warranty_orginal_amount); 
    $warranty_balance_due =  sprintf("%.2f", $warranty_balance_due);

    # print "\n";
    # print "-ro_number:".$ro_number."warranty_orginal_amount:".$warranty_orginal_amount."warranty_balance_due:".$warranty_balance_due."warranty_deductible:".$warranty_deductible."warranty_balance:".$warranty_balance;
    # print "\n";
    
    # print "\n";
    # print "ro_number:".$ro_number."warranty_balance:".$warranty_balance;
    # print "\n";

    if( $warranty_balance_due > 0){
       $warranty_balance = sprintf("%.2f", $warranty_orginal_amount-abs($warranty_balance_due));
       $warranty_deductible = sprintf("%.2f", $warranty_balance_due);
    } elsif( $warranty_balance_due < 0 ){
       $warranty_balance = sprintf("%.2f", $warranty_orginal_amount+abs($warranty_balance_due));
       $warranty_deductible = sprintf("%.2f", 0.00);
    } else { 
       $warranty_deductible = sprintf("%.2f", 0.00);
    }

    # print "\n";
    # print "ro_number:".$ro_number."warranty_balance:".$warranty_balance;
    # print "\n";

    # print "\n";
    # print "+ro_number:".$ro_number."warranty_orginal_amount:".$warranty_orginal_amount."warranty_balance_due:".$warranty_balance_due."warranty_deductible:".$warranty_deductible."warranty_balance:".$warranty_balance;
    # print "\n";

    if( $internal_balance == 0 || $internal_balance_due == 0){
       $internal_balance = sprintf("%.2f", $internal_balance);
    } else{
      $internal_balance =   sprintf("%.2f", $internal_balance-$internal_balance_due);
    }
  
    $internal_charge =  $internal_balance;

    # print "\n";
    # print "ro_number:".$ro_number."warranty_deductible".$service_deductible."warranty_balance".$service_balance;
    # print "\n";

    push (@grand_total_section, sprintf(border("#")."%105s".border(" |")."\n", "  ")); # Separator Line
    push (@grand_total_section, sprintf(border("#")."%105s".border(" |")."\n", "  ")); # Separator Line
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %11s%-19s  %7s%-23s  %11s%-19s  %9s%-21s".'~font{default}'.border("|")."\n",
           "", "Internal",
           "", "Servive Contract",
           "","Warranty",
           "","Customer Pay"));
    push (@grand_total_section, sprintf(border("#").'~font{DejaVuSansMono-Bold7.5}'."  %-5s".'~font{DejaVuSansMono7.5}'."  %-13s  %6s  ".
                                                    '~font{DejaVuSansMono-Bold7.5}'."  %-5s".'~font{DejaVuSansMono7.5}'."  %-13s  %6s  ".
                                                    '~font{DejaVuSansMono-Bold7.5}'."  %-5s".'~font{DejaVuSansMono7.5}'."  %-13s  %6s  ".
                                                    '~font{DejaVuSansMono-Bold7.5}'."  %-5s".'~font{DejaVuSansMono7.5}'."  %-13s  %6s".border("|")."\n",
                                        "COST","Description", "Retail",
                                        "COST","Description", "Retail",
                                        "COST", "Description", "Retail",
                                        "COST", "Description", "Retail "));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'."%7.2f  %-10s   %7.2f ".
                                                    "   %7.2f %-10s   %7.2f ".
                                                    "   %7.2f %-10s   %7.2f ".
                                                    "  %7.2f  %-10s   %7.2f  ".'~font{default}'.border("|")."\n",
                                       $tot_intern_lbr_cost, "Labor", $tot_intern_lbr_sale,
                                       $tot_sc_lbr_cost, "Labor", $tot_sc_lbr_sale,
                                       $tot_warr_lbr_cost, "Labor", $tot_warr_lbr_sale,
                                       $tot_cust_lbr_cost, "Labor", $tot_cust_lbr_sale));

    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'."%7.2f  %-10s   %7.2f".
                                                    "    %7.2f %-10s   %7.2f".
                                                    "    %7.2f %-10s   %7.2f".
                                                    "   %7.2f  %-10s   %7.2f  ".'~font{default}'.border("|")."\n",
                                       $tot_intern_prt_cost,"Parts",$tot_intern_prt_sale,
                                       $tot_sc_prt_cost,"Parts",$tot_sc_prt_sale,
                                       $tot_warr_prt_cost,"Parts",$tot_warr_prt_sale,
                                       $tot_cust_prt_cost,"Parts",$tot_cust_prt_sale));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'."%7.2f  %-10s   %7.2f".
                                                    "    %7.2f %-10s   %7.2f".
                                                    "    %7.2f %-10s   %7.2f".
                                                    "   %7.2f  %-10s   %7.2f  ".'~font{default}'.border("|")."\n",
                                       $tot_intern_sublet_cost,"Sublet",$tot_intern_sublet_sale,
                                       $tot_sc_sublet_cost,"Sublet",$tot_sc_sublet_sale,
                                       $tot_warr_sublet_cost,"Sublet",$tot_warr_sublet_sale,
                                       $tot_cust_sublet_cost,"Sublet",$tot_cust_sublet_sale));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f  ".'~font{default}'.border("|")."\n",
                                      "","Freight",$internal_tot_freight_amount,
                                      "","Freight",$service_tot_freight_amount,
                                      "","Freight",$warranty_tot_freight_amount,
                                      "","Freight",$customer_tot_freight_amount));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f  ".'~font{default}'.border("|")."\n",
                                    "","Car Rental",$intern_ro_rental,
                                    "","Car Rental",$sc_ro_rental,
                                    "","Car Rental",$warr_ro_rental,
                                    "","Car Rental",$cust_ro_rental));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f  ".'~font{default}'.border("|")."\n",
                                    "","Spec Tax",0.00,
                                    "","Spec Tax",0.00,
                                    "","Spec Tax",0.00,
                                    "","Spec Tax",0.00));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f  ".'~font{default}'.border("|")."\n",
                                   "","Haz Mat",$internal_tot_hazmat_amount,
                                   "","Haz Mat",$service_tot_hazmat_amount,
                                   "","Haz Mat",$warranty_tot_hazmat_amount,
                                   "","Haz Mat",$customer_tot_hazmat_amount));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f  ".'~font{default}'.border("|")."\n",
                                  "","Tax",$internal_tot_slstax_amount,
                                  "","Tax",$service_tot_slstax_amount,
                                  "","SlsTax",$warranty_tot_slstax_amount,
                                  "","SlsTax",$customer_tot_slstax_amount));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f  ".'~font{default}'.border("|")."\n",
                                  "","Co-pay",$internal_balance_due,
                                  "","Deductible",$service_deductible,
                                  "","Deductible",$warranty_deductible,
                                  "","Deductible",$customer_deductible));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7s  ".'~font{default}'.border("|")."\n",
                                  "","Charge",$internal_charge,
                                  "","Charge",0.00,
                                  "","Charge",0.00,
                                  "","",""));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f  ".'~font{default}'.border("|")."\n",
                                   "","Misc",$tot_intern_misc_cost,
                                   "","Misc",$tot_sc_misc_cost,
                                   "","Misc",$tot_warr_misc_cost,
                                   "","Misc",$tot_cust_misc_cost));
    push (@grand_total_section, sprintf(border(">").'~font{DejaVuSansMono7.5}'." %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f ".
                                                    "   %5s   %-11s  %7.2f  ".'~font{default}'.border("|")."\n",
                                  "","Balance",$internal_balance,
                                  "","Balance",$service_balance,
                                  "","Balance",$warranty_balance,
                                  "","Amt Due",$customer_orginal_amount));

       return @grand_total_section;
   }


# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_account_detail_seection {
     #push (@ro_account_detail_section, sprintf(border("#")."%105s".border(" |")."\n", "  ")); # Separator Line
     #push (@ro_account_detail_section, sprintf(border("#")."%105s".border(" |")."\n", "  ")); # Separator Line
     my $account_deatil_qry = $conn->prepare("SELECT
                                        ro_account_no,
                                        account_description,
                                        coalesce(account_controlnumber,ro_number) as account_controlnumber,
                                        account_referencenumber,
                                        account_transactionamount,
                                        proposed_amount,
                                        cost_amount,
                                        proposed_adjustment
                                  FROM repair_account
                                  WHERE ro_number = ? ");
    $account_deatil_qry->execute($ro_number);
    my ( $ro_account_no, $account_description, $account_controlnumber, $account_referencenumber, $account_transactionamount, $proposed_amount, $cost_amount, $proposed_adjustment);
   
   if($account_deatil_qry->rows > 0){
        push (@ro_account_detail_section, sprintf(border("#").'~font{DejaVuSansMono-Bold7.5}'."%2s %-33s %89s"
                                                        .'~font{default}'.border("|")."\n",
                                                        "",
                                                        "Payment notes",
                                                        ""
                                                    ));
   }
   if($comments ne ''){
    push (@ro_account_detail_section, sprintf(border("#").'~font{DejaVuSansMono8}'."%-2s %-10s %-20s"
                                                    .'~font{default}'.border("|")."\n",
                                                    "",
                                                    "$comments",
                                                    ""
                                                   ));
   }

    if($account_deatil_qry->rows > 0){
	
                push (@ro_account_detail_section, sprintf(border("#").'~font{DejaVuSansMono7.5}'." %-6s %-33s %9s %7s %11s %11s  %-10s  %-25s %3s"
                                                    .'~font{default}'.border("|")."\n",
                                                    "",
                                                    "",
                                                    "Proposed",
                                                    "Cost",
                                                    "Accounting",
                                                    "Proposed", 
                                                    "",
                                                    "",
                                                    ""));
         	push (@ro_account_detail_section, sprintf(border("#").'~font{DejaVuSansMono7.5}'." %-6s %-33s %9s %7s %11s %11s  %-10s  %-25s %3s"
                                                    .'~font{default}'.border("|")."\n",
                                                    "Acct",
                                                    "Acct description",
                                                    "Amount",
                                                    "Amount",
                                                    "processed",
                                                    "Adjustment", 
                                                    "Control",
                                                    "Control Description",
                                                    ""));
    	while(my @account_deatil_row = $account_deatil_qry->fetchrow_array) {
        	( $ro_account_no, $account_description, $account_controlnumber, $account_referencenumber, $account_transactionamount, $proposed_amount, $cost_amount, $proposed_adjustment) = @account_deatil_row;
         	if( $cost_amount eq ''){
               		$cost_amount = "0.00"

         	}
                
         	push (@ro_account_detail_section, sprintf(border("#").'~font{DejaVuSansMono7.5}'." %-6s %-33s %9s %7s %11s %11s  %-10s  %-25s %3s"
                                                .'~font{default}'.border("|")."\n",
                                                $ro_account_no,
                                                $account_description,
                                                $proposed_amount,
                                                $cost_amount,
                                                $proposed_amount,
                                                "0.00", 
                                                $account_controlnumber,
                                                $account_referencenumber,
                                                ""));
    	}
    }
    return @ro_account_detail_section;
   }

sub calculate_page_count {
    my $page_count;
    my $tot_page_lines = 0;
    # Add page count calculation of job section
    foreach my $job_sub_sections (@job_section){
        $tot_page_lines = $tot_page_lines + scalar(@$job_sub_sections);
    }

    # Add page count calculation of FEE section
    $tot_page_lines = $tot_page_lines + scalar(@ro_fee_section);

    # Add page count calculation of Discount section
    $tot_page_lines = $tot_page_lines + scalar(@ro_disc_section);

     # Add page count calculation of Discount section
    $tot_page_lines = $tot_page_lines + scalar(@ro_misc_section);

    # Add page count calculation of grand total section
    $tot_page_lines = $tot_page_lines + scalar(@grand_total_section);

    $page_count = ceil($tot_page_lines / $page_max_height);

    return $page_count;
}
#Subroutine to prepare the FOOTER section of the invoice
sub get_footer {
    my @footer_section;
    my ($is_end, $page_number) = @_;

    push (@footer_section, sprintf(border("#")."%106s".border("|")."\f", ""));
    return @footer_section;
}

# Subroutine to make the Total(RO) FEE section
sub make_ro_fee_section {
    my $fee_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_description,
                                      other_cost,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ?
                                  AND item_type = 'FEE'
                                  AND ro_line IS NULL");
    $fee_qry->execute($ro_number);
    my ( $fee_type, $fee_description, $fee_cost, $fee_sale);

    while (my @fee_row = $fee_qry->fetchrow_array) {
        ( $fee_type, $fee_description, $fee_cost, $fee_sale) = @fee_row;
        if (lc($fee_type) eq "c"){
            $tot_cust_misc_sale = $tot_cust_misc_sale + $fee_sale;
            $tot_cust_misc_cost = $tot_cust_misc_cost + $fee_cost;
        }elsif (lc($fee_type) eq "w"){
            $tot_warr_misc_sale = $tot_warr_misc_sale + $fee_sale;
            $tot_warr_misc_cost = $tot_warr_misc_cost + $fee_cost;
        }elsif (lc($fee_type) eq "i"){
            $tot_intern_misc_sale = $tot_intern_misc_sale + $fee_sale;
            $tot_intern_misc_cost = $tot_intern_misc_cost + $fee_cost;
        }
    }
    return @ro_fee_section;
}

# Subroutine to prepare ro Disc section of a JOB
sub make_ro_disc_section {
    my $disc_qry = $conn->prepare("SELECT
                                      disc_code,
                                      disc_description,
                                      disc_applied,
                                      labor_discount,
                                      parts_discount,
                                      total_discount
                                  FROM repair_discount
                                  WHERE ro_number = ?
                                  AND ro_line IS NULL");
    $disc_qry->execute($ro_number);
    my ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount );

    while (my @disc_row = $disc_qry->fetchrow_array) {
        ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount ) = @disc_row;
        $Text::Wrap::columns = 68;
        $disc_description = $disc_code." ".$disc_description;
        my $wrapped_disc_description = fill('', '', expand($disc_description));
        my @disc_description_list = split "\n", $wrapped_disc_description;

        foreach my $i (0 .. $#disc_description_list) {
            push (@ro_disc_section, sprintf(border(">")."%-68s%10s".border("|")."\n",
                                    $disc_description_list[$i], ""));
        }

        if ($labor_discount != 0){
            push (@ro_disc_section, sprintf(border(">")."%6s%-10s %14s%7.2f %7.2f%15s%7.2f %8.2f ".border("|")."\n",
                                                "", "LABOR", $disc_applied, 0, $labor_discount, "", $labor_discount, $labor_discount));
        }
        if ($parts_discount != 0){
            push (@ro_disc_section, sprintf(border(">")."%6s%-10s %14s%7.2f %7.2f%15s%7.2f %8.2f ".border("|")."\n",
                                                "", "PARTS", $disc_applied, 0, $parts_discount, "", $parts_discount, $parts_discount));
        }
        $cust_discount = $cust_discount + $total_discount;
    }
    return @ro_disc_section;
}

# Subroutine to make the Tech-Punch section
sub make_ro_tech_punch_section {
    my $tech_punch_qry = $conn->prepare("SELECT
                                      ro_line,
                                      tech_id,
                                      to_char(work_date, 'mm-dd-yy') AS work_date,
                                      to_char(work_start_time, 'HH24:MI'),
                                      to_char(work_end_time, 'HH24:MI'),
                                      work_type,
                                      work_duration
                                  FROM repair_tech_punch
                                  WHERE ro_number = ?
                                  ORDER BY work_start_time");
    $tech_punch_qry->execute($ro_number);
    my ( $ro_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_type, $work_duration );

    $tech_punch_entries = $tech_punch_qry->rows;

    return @ro_tech_punch_section;
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")."%105s".border("|")."\n", "");
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
 sub print_header_section {
    my ($page_num) = @_;
    my %hash = (pg_num => $page_num );
    foreach (@header_section)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
 }

# Subroutine for pagination
sub paginate_and_print_segment {
    my (@section_array) = @_;
    # Print section that fit in the current page
    if(scalar(@section_array) <= ($page_max_height - $curr_page_height)){
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{
        for my $sub_section(@section_array){
            if($curr_page_height < $page_max_height)
            {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            } else{
                print FILE get_footer(0, $curr_page_num);
                $curr_page_num = $curr_page_num + 1;
                print_header_section ($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
            }
        }
    }
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
    ($tot_cust_lbr_cost, $tot_cust_lbr_sale, $tot_cust_prt_cost, $tot_cust_prt_sale, $tot_cust_misc_cost, $tot_cust_misc_sale, $tot_cust_gog_cost, $tot_cust_gog_sale,
    $tot_cust_sublet_cost, $tot_cust_sublet_sale, $tot_cust_ded_cost, $tot_cust_ded_sale )= (0)x12;

    ($tot_intern_lbr_cost, $tot_intern_lbr_sale, $tot_intern_prt_cost, $tot_intern_prt_sale, $tot_intern_misc_cost, $tot_intern_misc_sale, $tot_intern_gog_cost,
    $tot_intern_gog_sale, $tot_intern_sublet_cost, $tot_intern_sublet_sale, $tot_intern_ded_cost, $tot_intern_ded_sale )= (0)x12;

    ($tot_warr_lbr_cost, $tot_warr_lbr_sale, $tot_warr_prt_cost, $tot_warr_prt_sale, $tot_warr_misc_cost, $tot_warr_misc_sale, $tot_warr_gog_cost, $tot_warr_gog_sale,
    $tot_warr_sublet_cost, $tot_warr_sublet_sale, $tot_warr_ded_cost, $tot_warr_ded_sale )= (0)x12;

    ($tot_sc_lbr_cost, $tot_sc_lbr_sale, $tot_sc_prt_cost, $tot_sc_prt_sale, $tot_sc_misc_cost, $tot_sc_misc_sale, $tot_sc_gog_cost, $tot_sc_gog_sale,
    $tot_sc_sublet_cost, $tot_sc_sublet_sale, $tot_sc_ded_cost, $tot_sc_ded_sale )= (0)x12;

    ($cust_ro_tax, $warr_ro_tax, $sc_ro_tax, $intern_ro_tax) = (0)x4;
    $cust_discount = 0;

    $curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @comment_section;
    undef @job_section;
    undef @ro_fee_section;
    undef @ro_disc_section;
    undef @ro_misc_section;
    undef @grand_total_section;
    undef @ro_account_detail_section;
    undef @ro_make_misc_service_section;
    undef @ro_tech_punch_section;
    ########## Prepare invoice ##########
    make_header();
    make_job_section();
    make_ro_fee_section();
    make_ro_disc_section();
    make_ro_misc_section();

    make_misc_service_section();
    make_account_detail_seection();
    make_grand_total_section();
    make_ro_tech_punch_section();

    ########## Print invoice ##########
    print_header_section ($curr_page_num);

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }

    # Pagination of RO fee section
    paginate_and_print_segment(@ro_fee_section);

    # Pagination of RO Discount section
    paginate_and_print_segment(@ro_disc_section);

    # Pagination of RO Misc section
    paginate_and_print_segment(@ro_misc_section);

    # Pagination of RO Comments
    paginate_and_print_segment(@comment_section);

    # Pagination of RO Account detail section
    paginate_and_print_segment(@ro_make_misc_service_section);

    # Pagination of RO Account detail section
    paginate_and_print_segment(@ro_account_detail_section);
     
    # Pagination of Grand total section
    paginate_and_print_segment(@grand_total_section);

    # Pagination of RO Technician Punch details
    paginate_and_print_segment(@ro_tech_punch_section);

    # End of invoice footer
    if($curr_page_height == $page_max_height){
        print FILE get_footer(1, $curr_page_num);
    } else {
        print_blank_line($page_max_height - $curr_page_height);
        print FILE get_footer(1, $curr_page_num);
    }
}

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}

sub align_center {
    # Return $str centered in a field of $col $padchars.
    # $padchar defaults to ' ' if not specified.
    # $str is truncated to len $column if too long.

    my ($str, $col, $padchar) = @_;
    $padchar = ' ' unless $padchar;
    my $strlen = length($str);
    $str = substr($str, 0, $col) if ($strlen > $col);
    $strlen = length($str);
    my $fore = int(($col - $strlen) / 2);
    my $aft = $col - ($strlen + $fore);
    $padchar x $fore . $str . $padchar x $aft;
}
sub currency_format{
    use Number::Format qw(:subs);
    my $precision=2;
    my $symbol='$';
    my ($number) = @_;
    my $formatted =format_price($number, $precision, $symbol);
    return  $formatted;
}
