#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

REPO_ROOT="$(cd $(dirname $0) && pwd)"

SCHEMA_NAME="${1:?Schema Name Required}"
TARGET_DIR="${2:?Target Directory Required}"
CONVERT_TO_PDF="${3:-true}"
# Ver generic_proxy:- Generic proxy invoice,
# Ver invoice_mimic:- Mimic invoice merged with Auto/mate template
VERSION="${4:-"generic_proxy"}"
SINGLE_STORE_FLAG="${6:-false}"
CUSTOM_BRANCH_NAME=$7

echo $TARGET_DIR

if [[ "$VERSION" = "generic_proxy" ]]; then
    "$REPO_ROOT"/generate-fixed-width-proxy-invoice.bash \
            "$SCHEMA_NAME" \
            "$TARGET_DIR" \
            "$CONVERT_TO_PDF"
elif [[ "$VERSION" = "invoice_mimic" ]]; then
    TMP_DIR="/tmp/du-etl-reynolds-proxyinvoice-work"
    mkdir -p "$TMP_DIR"
    clear_dir "$TMP_DIR"
    BOOK_DIR="$TMP_DIR"/bookmarking

    [[ -d "$TARGET_DIR" ]] || die "$2 must exist and should be empty"
    
    rm -rf /tmp/input
    mkdir /tmp/input

    clear_dir "$TARGET_DIR"
    mkdir "$TARGET_DIR"/text || die "Could not prepare text directory"
    mkdir "$TARGET_DIR"/text/inv-bg || die "Could not prepare text directory"
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        mkdir "$TARGET_DIR"/pdf  || die "Could not prepare pdf directory"
    fi

    (
        cd "$TARGET_DIR"/text || die "Failed to set target directory as active"

        if [[ "$VERSION" = "invoice_mimic" ]]; then
            "$REPO_ROOT"/create-proxy-invoice-report-reynolds-mimic.pl "${SCHEMA_NAME}" "${SINGLE_STORE_FLAG}" "${CUSTOM_BRANCH_NAME}" 
        fi

        #Create fixed width proxy invoice PDF report by iterating the proxy invoice txt reports
        loop_count=0
        if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
            progress "Beginning PDF Conversions - Count: $(find . -maxdepth 1 -type f | wc -l)"
            echo "$(date +%H:%M:%S) Starting"

            echo "" > "$TMP_DIR"/"blank.txt";
            enscript -q \
                    --no-header \
                    -r \
                    -L 1 "$TMP_DIR"/"blank.txt" \
                    -o - \
                    | ps2pdf - "$TMP_DIR"/"blank.pdf"

            for pi in ./*.txt; do
                ((loop_count++))
                if [[ $(($loop_count%200)) = 0 ]]; then
                    echo "$(date +'%H:%M:%S') Converted $loop_count"
                fi
                clear_dir "$BOOK_DIR"
                [ -f "$pi" ] || break
                filename=$(basename "$pi")
                filename="${filename%.*}"
                if [[ "$VERSION" = "invoice_mimic" ]]; then
                    enscript -q \
                            -f DejaVuSansMono@9 \
                            -e~ \
                            --no-header \
                            -s 4.3 \
                            --margins=10:2:14:10 \
                            -L 73 "${pi}" \
                            -o - \
                        | sed '/^\/bgs /,/^}/{
                                /x y blskip/s//x   y height .2 mul add   blskip/
                                }' | ps2pdf - "$BOOK_DIR"/"${filename}.pdf"
                fi

                enscript -q \
                        -f HelveticaMono@14 \
                        --no-header \
                        --margins=20:2:12:15 \
                        -L 73 "./inv-bg/${filename}-inv-bg.txt" \
                        -r \
                        -o - \
                    | ps2pdf - "$BOOK_DIR"/"${filename}-inv-bg.pdf"


                pdftk "$BOOK_DIR"/"${filename}-inv-bg.pdf" "$TMP_DIR"/"blank.pdf" cat output "$BOOK_DIR"/"${filename}-append-blank.pdf"

                pdftk "$BOOK_DIR"/"${filename}-append-blank.pdf" cat 1-endsouth output "$BOOK_DIR"/"${filename}-inv-bg-out.pdf"
  
                qpdf --decrypt "$BOOK_DIR"/"${filename}.pdf" /tmp/input/"${filename}.pdf" 

                pdftk /tmp/input/"${filename}.pdf" multibackground "$REPO_ROOT"/invoice-template/reynolds_template.pdf output "$BOOK_DIR"/"${filename}-merged.pdf"

                pdftk "$BOOK_DIR"/"${filename}-merged.pdf" multibackground "$BOOK_DIR"/"${filename}-inv-bg-out.pdf" output "$BOOK_DIR"/"${filename}-merged-inv-bg.pdf"

                pdftk "$BOOK_DIR"/"${filename}.pdf" dump_data output "${BOOK_DIR}"/temp-pdf-metadata.txt

                printf 'BookmarkBegin\nBookmarkTitle: %s\nBookmarkLevel: 1\nBookmarkPageNumber: 1' "${filename}" \
                    > "${BOOK_DIR}"/temp-pdf-new-bookmark.txt


                cat "${BOOK_DIR}"/temp-pdf-metadata.txt \
                    "${BOOK_DIR}"/temp-pdf-new-bookmark.txt \
                    > "${BOOK_DIR}"/temp-pdf-new-metadata.txt

                pdftk "$BOOK_DIR"/"${filename}-merged-inv-bg.pdf" \
                    update_info \
                    "${BOOK_DIR}"/temp-pdf-new-metadata.txt \
                    output - \
                    > ../pdf/"${filename}.pdf" 2>&1
            done
        else
            progress "Skipping PDF Conversion as Requested"
        fi

    ) || die "Failed during Proxy Invoice generation"

    rm -rf "$TARGET_DIR"/text/inv-bg
 
    cd "$TARGET_DIR"/text
    
    grep -rli '~font{HelveticaMono2}' * | xargs -i@ sed -i 's/~font{HelveticaMono2}//g' @
    grep -rli '~font{DejaVuSansMono9}' * | xargs -i@ sed -i 's/~font{DejaVuSansMono9}//g' @
    grep -rli '~font{DejaVuSansMono9}' * | xargs -i@ sed -i 's/~font{DejaVuSansMono9}//g' @
    grep -rli '~font{HelveticaMono1}' * | xargs -i@ sed -i 's/~font{HelveticaMono1}//g' @
    grep -rli '~font{HelveticaMono3}' * | xargs -i@ sed -i 's/~font{HelveticaMono3}//g' @
    grep -rli '~font{default}' * | xargs -i@ sed -i 's/~font{default}//g' @
    grep -rli '~font{DejaVuSansMono-Bold9}' * | xargs -i@ sed -i 's/~font{DejaVuSansMono-Bold9}//g' @
    grep -rli '~color{.60 0 0}' * | xargs -i@ sed -i 's/~color{.60 0 0}//g' @
    grep -rli '~color{0 0 0}' * | xargs -i@ sed -i 's/~color{0 0 0}//g' @
    grep -rli '~font{DejaVuSansMono9}' * | xargs -i@ sed -i 's/~font{DejaVuSansMono9}//g' @

    # Create one or more PDF bundles from the individual PDF files
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        (
            cd "$TARGET_DIR"/pdf
            mkdir ../bundle
            BUNDLE_PREFIXES=$(find . -mindepth 1 -maxdepth 1 -type f -exec basename {} \; | perl -ale 'print $1 if /(\d+)\d{2}.pdf/;' | sort | uniq)
            for prefix in $BUNDLE_PREFIXES; do
                FIRST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $1}')
                LAST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $NF}')
                FIRST=${FIRST_TMP::-4}
                LAST=${LAST_TMP::-4}
                pdftk ./"${prefix}"*.pdf cat output ../bundle/bundle-"${FIRST}-${LAST}".pdf
            done
        )
    fi
    say "Done Creating Proxy Invoices"
else
    die "Invalid Proxy Version"
fi
