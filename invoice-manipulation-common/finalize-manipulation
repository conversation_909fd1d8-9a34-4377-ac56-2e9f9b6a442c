#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

BASE_WORK_DIR="${1:?Base Working Directory Required}"

[[ -d "$BASE_WORK_DIR" ]] || die "Base Working Directory Required to Exist"
(
    cd "$BASE_WORK_DIR"
    if [[ -e ./invoice-lists/recently-closed-not-qualified-retail.txt ]]; then
        cp ./invoice-lists/recently-closed-not-qualified-retail.txt \
           ./invoice-list-files/recently-closed-retail-ros.txt
    fi
    if [[ -e ./invoice-lists/not_reported_ros.txt ]]; then
        cp ./invoice-lists/not_reported_ros.txt \
           ./invoice-list-files/not-reported-ros.txt
    fi
    if [[ -e ./invoice-lists/missing-not-closed.txt ]]; then
        cp ./invoice-lists/missing-not-closed.txt \
           ./invoice-list-files/missing-ros_open-only.txt
    fi
    if [[ -e ./invoice-lists/missing-not-not-closed.txt ]]; then
        cp ./invoice-lists/missing-not-not-closed.txt \
           ./invoice-list-files/missing-ros_closed-only.txt
    fi

    echo 'PATH=%PATH%;"C:\Program Files (x86)\VeryPDF PDF Split-Merge v3.0"' \
         > ./invoice-list-files/meta/bulk-bundling-script-copy-paste.txt

    cat ./invoice-list-files/meta/*batch-script.txt \
        >> ./invoice-list-files/meta/bulk-bundling-script-copy-paste.txt
)
