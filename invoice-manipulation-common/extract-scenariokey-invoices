#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

SCNKEY="${1:?ScenarioKey Required}"
OUTPUT_DIR="${2:?Output Directory Required}"

OPT_FIRST_RO="${3:-}"
OPT_LAST_RO="${4:-}"

say "Extracting Scenariokey Invoices for $1"

if [[ "$SCNKEY" = 'TEST' ]]; then
    warn "No-Op: Test Mode"
    exit 0
fi

if [[ ! -d "$OUTPUT_DIR" ]]; then
    die "Provided output directory must exist"
fi

function psql_prod() {
    psql "service=${GGS_PROD_PG_SERVICE}" --set=ON_ERROR_STOP=1 "$@" --set=scnkey="${SCNKEY}"
}

IS_VALID_KEY=$(psql_prod -At <<SQL
    SELECT EXISTS (SELECT 1 FROM magescenario WHERE scenariokey = :'scnkey');
SQL
)

if [[ "$IS_VALID_KEY" != 't' ]]; then
    die "Provided ScenarioKey must exist"
fi

function export_invoice_lists() {
psql_prod --quiet <<SQL
    BEGIN;
    UPDATE magescenario
       SET temp_rangestartingro = rangestartingro,
           temp_rangeendingro   = rangeendingro,
           rangestartingro      = COALESCE(NULLIF('$OPT_FIRST_RO', ''), rangestartingro),
           rangeendingro        = COALESCE(NULLIF('$OPT_LAST_RO',  ''), rangeendingro)
     WHERE scenariokey = :'scnkey';

    CREATE TEMP TABLE scnkey_invseq AS
        SELECT *
          FROM mageviewinvoicesequenceranged
         WHERE scenariokey = :'scnkey';

    UPDATE magescenario
       SET rangestartingro  = temp_rangestartingro,
           rangeendingro = temp_rangeendingro
     WHERE scenariokey = :'scnkey';

    CREATE TEMP TABLE qual_ros AS
        SELECT DISTINCT invoicenumber
          FROM magepartdetail
          JOIN magepartdetailextended                              USING (partdetailid, scenariokey)
          JOIN scnkey_invseq                                       USING (scenariokey, invoicenumber)
         WHERE scenariokey = :'scnkey'
               AND NOT finalexcluded;

    CREATE TEMP TABLE non_qual_ros AS
        SELECT invoicenumber
          FROM mageinvoicesequencemaster mism
          JOIN scnkey_invseq                                       USING (scenariokey, invoicenumber)
         WHERE scenariokey = :'scnkey'
               AND NOT EXISTS (SELECT 1
                                 FROM qual_ros
                                WHERE qual_ros.invoicenumber = mism.invoicenumber);

    CREATE TEMP TABLE all_retail_ros AS
        SELECT invoicenumber
          FROM mageinvoicesequencemaster mism
          JOIN scnkey_invseq invseq                                USING (scenariokey, invoicenumber)
         WHERE scenariokey = :'scnkey'
               AND EXISTS (    SELECT 1
                                 FROM magepartdetail mpd
                                WHERE mpd.scenariokey = mism.scenariokey
                                      AND mism.invoicenumber = mpd.invoicenumber
                                      AND (mpd.iscustomerpay OR mpd.isotherpaytype));

    CREATE TEMP TABLE all_ros_in_range AS
        SELECT invnum
          FROM generate_series(
                   (SELECT invoicenumber::integer
                      FROM scnkey_invseq
                     WHERE invoicenumber ~ '^\d+$'
                  ORDER BY invoicesequence ASC
                     LIMIT 1),
                   (SELECT invoicenumber::integer
                      FROM scnkey_invseq
                     WHERE invoicenumber ~ '^\d+$'
                  ORDER BY invoicesequence DESC
                     LIMIT 1)
                     ) gs (invnum);

    \copy (SELECT invoicenumber FROM qual_ros     ORDER BY invoicenumber) TO qualified-invoices.txt
    \copy (SELECT invoicenumber FROM non_qual_ros ORDER BY invoicenumber) TO not-qualified-invoices.txt
    \copy (SELECT invoicenumber FROM all_retail_ros ORDER BY invoicenumber) TO all-retail-invoices.txt
    \copy (SELECT invoicenumber FROM non_qual_ros nqr WHERE EXISTS (SELECT 1 FROM all_retail_ros arr WHERE nqr.invoicenumber = arr.invoicenumber) ORDER BY invoicenumber) TO retail-not-qualified-invoices.txt
    \copy (SELECT invnum FROM all_ros_in_range ORDER BY invnum) TO all-in-range-invoices.txt

    ROLLBACK;
SQL
}

(
    cd "$OUTPUT_DIR"
    export_invoice_lists
)
