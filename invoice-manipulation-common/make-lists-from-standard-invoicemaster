#!/usr/bin/env bash
set -o nounset

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

function usage_die() {
    scream "$@"
    usage
    exit
}

function usage() {
pre_usage "Constructs researched_ros, not_reported_ros, not_closed_ros from ADP source documents"
    cat <<TXT
    --invoice-master <file>
    --open-ro <file>
    --research <file>
TXT
post_usage
}

[ $# -eq 0 ] && { usage; exit 1; }

OUTPUT_DIR="$PWD"
META_DIR="$OUTPUT_DIR"/invoicemaster-meta/
mkdir -p "$META_DIR"

while [ $# -gt 0 ]; do
    case "$1" in
        --invoice-master)
            [ -f "$2" -o -p "$2" ] || usage_die "Invoice Master Source Required"
            INVOICE_MASTER="${2:?File Name Required}"
            shift
            ;;
        --open-ro)
            [ -f "$2" -o -p "$2" ] || usage_die "Open RO Source Required"
            OPEN_RO_LIST="${2:?File Name Required}"
            shift
            ;;
        --research)
            [ -f "$2" -o -p "$2" ] || usage_die "Research Source Required"
            RESEARCH_LIST="${2:?File Name Required}"
            shift
            ;;
        --invoice-master-source)
            cp "$2" "$META_DIR"/invoice-master-source.txt
            shift
            ;;
        * )
            usage
            die "Unrecognized option $1; exiting";
            ;;
    esac
    shift
done

cp "$OPEN_RO_LIST" "$META_DIR"/open-ro-list.txt
cp "$RESEARCH_LIST" "$META_DIR"/research-list.txt

function psql_out() {
    psql "service=local_du_etl options='-c search_path=public'" --quiet "$@"
}

psql_out --quiet <<SQL
    CREATE TEMP TABLE invoicemaster (
        source_store text,
        ro_number text,
        invoice_date_close date,
        invoice_date_open date,
        vehicle_make text,
        PRIMARY KEY (ro_number)
    );

    CREATE TEMP TABLE openros (
        ro_number text PRIMARY KEY
    );

    CREATE TEMP TABLE researchros (
        ro_number text PRIMARY KEY
    );

    \copy invoicemaster FROM '$INVOICE_MASTER'
    \copy openros FROM '$META_DIR/open-ro-list.txt' WITH (FORMAT csv, HEADER false)
    \copy researchros FROM '$META_DIR/research-list.txt' WITH (FORMAT csv, HEADER false)

    SELECT count(*) FROM invoicemaster;
    SELECT count(*) FROM openros;
    SELECT count(*) FROM researchros;

    CREATE TEMP TABLE not_reported_ros AS
        WITH first_ro (first_invnum) AS (
             SELECT ro_number::integer
               FROM invoicemaster
              WHERE ro_number ~ '^\d+$'
           ORDER BY 1 ASC
              LIMIT 1
        ), last_ro (last_invnum) AS (
            SELECT ro_number::integer
               FROM invoicemaster
              WHERE ro_number ~ '^\d+$'
           ORDER BY 1 DESC
              LIMIT 1
        )
        SELECT invnum AS invoicenumber
          FROM (first_ro CROSS JOIN last_ro) AS extents
    CROSS JOIN LATERAL generate_series(first_invnum, last_invnum) gs (invnum)
         WHERE NOT EXISTS (SELECT 1
                             FROM invoicemaster im
                            WHERE im.ro_number = gs.invnum::text);

    \copy (SELECT * FROM not_reported_ros WHERE NOT EXISTS (SELECT 1 FROM openros WHERE openros.ro_number = not_reported_ros.invoicenumber::text) ORDER BY 1) TO ./invoicemaster-meta/not_reported_ros.txt
    \copy (SELECT * FROM not_reported_ros WHERE     EXISTS (SELECT 1 FROM openros WHERE openros.ro_number = not_reported_ros.invoicenumber::text) ORDER BY 1) TO ./invoicemaster-meta/not_closed_ros.txt
    \copy (SELECT ro_number FROM invoicemaster ORDER BY ro_number) TO ./invoicemaster-meta/expected_canonical_ros.txt
SQL

wc -l ./invoicemaster-meta/*

exit 0
