#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

LOCAL_KEY_PATH=/vagrant/etl-cdk/inv-cdk/
REMOTE_HOST='md-client'

function do_ssh() {
    ssh "$REMOTE_HOST" "$@"
}

if [[ -f "$LOCAL_KEY_PATH"/enumerated-invoices.zip ]]; then
    die "Local zip file present - remove if you want to attempt a new fetch."
fi

if [[ -d "$LOCAL_KEY_PATH"/enumerated-invoice-files ]]; then
    die "Files already present - remove if you want to attempt a new fetch."
fi

if [[ -d "$LOCAL_KEY_PATH"/enumeration-bundle-meta ]]; then
    die "Bundle Meta already present - remove if you want to attempt a new fetch."
fi

if [[ -d "$LOCAL_KEY_PATH"/enumeration-meta ]]; then
    die "Enumeration Meta already present - remove if you want to attempt a new fetch."
fi

if do_ssh 'test -f $HOME/DU-ETL-INVM/enumerated-invoice-files.zip'; then
    die "Remote zip already present - remove if you want to attempt a new fetch."

fi

echo "Zipping up remote files"
do_ssh 'rm -rf $HOME/DU-ETL-INVM/enumerated-invoices.zip'
do_ssh 'cd $HOME/DU-ETL-INVM && zip -qr enumerated-invoices.zip ./enumerated-invoice-files/ ./enumeration-bundle-meta/ ./enumeration-meta/'
rsync -P md-client:/home/<USER>/DU-ETL-INVM/enumerated-invoices.zip \
          "$LOCAL_KEY_PATH"/enumerated-invoices.zip

echo "Unzipping to $LOCAL_KEY_PATH"
unzip -q "$LOCAL_KEY_PATH"/enumerated-invoices.zip -d "$LOCAL_KEY_PATH"

unix2dos -q "$LOCAL_KEY_PATH"/enumeration-bundle-meta/*
unix2dos -q "$LOCAL_KEY_PATH"/enumeration-meta/*

exit
