#!/usr/bin/env bash

set -o nounset

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

RUN_MODE="--execute"

[ $# -eq 0 ] && { usage; exit 1; }

BATCH_SIZE=500

while [ $# -gt 0 ]; do
    case "$1" in
        --base-dir)
            BASE_WORK_DIR="${2:?Directory Name Required}"
            [[ -d "$BASE_WORK_DIR" ]] || die "Base Directory Must Exist at $BASE_WORK_DIR"
            shift
            ;;
        --label)
            SEGREGATION_LABEL="${2:?Segregation Label Required}"
            shift
            ;;
        --list)
            SEGREGATION_LIST_FILE="${2:?Relative List File Name Required}"
            shift
            ;;
        --batch-size)
            BATCH_SIZE="${2:?Size specification required}"
            shift
            ;;
        * )
            usage
            die "Unrecognized option $1; exiting";
            ;;
    esac
    shift
done

INVOICE_DIR="$BASE_WORK_DIR"/Invoices/
OUTPUT_DIR="$BASE_WORK_DIR"/invoice-list-files/"$SEGREGATION_LABEL"/

# Mustn't clear - output is cumulative
[[ -d "$INVOICE_DIR" ]]           || die "Invoice Directory Must Exist in Work Dir"

[[ -n "$SEGREGATION_LABEL" ]]     || die "Label must be non-empty"

# We need to conditionally rotate the RO based upon
# whether it is UCS or not.  Presently only UCS has a recap
# file and since we are already dealing with that explicitly
# it is easiest to simply leverage that knowledge into a
# simple toggle here.
if [[ -d "./Invoice-Recaps" ]]; then
    PERFORM_ROTATE='true'
    CREATE_BOOKMARK='true'
else
    PERFORM_ROTATE='false'
    CREATE_BOOKMARK='false'
    RUN_MODE='--copy-only'
fi

WORK_TEMP="/tmp/du-etl-invoice-manipulation-segregate-ros/"
WORK_INV_STAGE="${WORK_TEMP}/invoice-staging/"
clear_dir "$WORK_TEMP"
mkdir "$WORK_INV_STAGE"

function copy_files_from_list() {

    function do_copy() {
        EXT_TARGET_DIR="${1}"
        INVOICE_FILE="${2}"
        INVOICE_NUM=$(basename "${2}" .pdf)
        if [[ $RUN_MODE = '--execute' ]]; then
            # cp "./Invoices/${2}" "${1}"/"${2}"
            # Non-Descructive Crop To 100in x 8.5in (landscape)
            # This matches the recap RO orientation whose goal is to
            # accurately reflect the S4 report which is also landscape.
            clear_dir "${WORK_INV_STAGE}"

            cp "./Invoices/${INVOICE_FILE}" "${WORK_INV_STAGE}"/"${INVOICE_FILE}"
            if [[ "$CREATE_BOOKMARK" = 'true' ]]; then
                pdftk "${WORK_INV_STAGE}"/"${INVOICE_FILE}" dump_data output \
                      "${WORK_INV_STAGE}"/temp-pdf-metadata.txt

                printf 'BookmarkBegin\nBookmarkTitle: %s\nBookmarkLevel: 1\nBookmarkPageNumber: 1' "${INVOICE_NUM}" \
                     > "${WORK_INV_STAGE}"/temp-pdf-new-bookmark.txt

                cat "${WORK_INV_STAGE}"/temp-pdf-metadata.txt \
                    "${WORK_INV_STAGE}"/temp-pdf-new-bookmark.txt \
                    > "${WORK_INV_STAGE}"/temp-pdf-metadata-with-bookmark.txt

                pdftk "${WORK_INV_STAGE}"/"${INVOICE_FILE}" \
                      update_info "${WORK_INV_STAGE}"/temp-pdf-metadata-with-bookmark.txt \
                      output "${WORK_INV_STAGE}"/"${INVOICE_FILE}".pdfbm
            else
                mv "${WORK_INV_STAGE}"/"${INVOICE_FILE}" \
                   "${WORK_INV_STAGE}"/"${INVOICE_FILE}".pdfbm
            fi

            if [[ "$PERFORM_ROTATE" = 'true' ]]; then
                podofobox "${WORK_INV_STAGE}"/"${INVOICE_FILE}".pdfbm \
                          "${EXT_TARGET_DIR}"/"${INVOICE_FILE}" \
                          media 0 0 79200 61200 \
                    2> ./podofo.log \
                    || die "PoDoFo Box Cropping Failed on ${INVOICE_FILE}"
            else
                cp "${WORK_INV_STAGE}"/"${INVOICE_FILE}".pdfbm "${EXT_TARGET_DIR}"/"${INVOICE_FILE}"
            fi
        elif [[ $RUN_MODE = '--copy-only' ]]; then
            cp "./Invoices/${INVOICE_FILE}" "${EXT_TARGET_DIR}"/"${INVOICE_FILE}"
        else
            touch "${EXT_TARGET_DIR}"/"${INVOICE_FILE}" || die "Can't touch this..."
        fi
    }
    function copy_recap() {
        if [[ "$PERFORM_ROTATE" = 'false' ]]; then
            return 0
        fi
        # Copy, and convert to PDF, the recap for the named RO#
        # and ensure that is appears before the RO content that
        # it describes.
        local EXT_TARGET_DIR="${1}"
        local INVOICE_NUM="${2}"
        local INVOICE_FILE="${INVOICE_NUM}_A-recap.pdf"
        [[ -e "./Invoice-Recaps/final-text/${INVOICE_NUM}-recap.txt" ]] || {
            echo "Missing Recap for ${INVOICE_NUM}"
            return 0
        }

        if [[ $RUN_MODE = '--execute' ]]; then
            clear_dir "${WORK_INV_STAGE}"

            enscript --silent -fCourier-Bold11 --no-header --landscape \
                     "./Invoice-Recaps/final-text/${INVOICE_NUM}-recap.txt" --output=- \
            | ps2pdf - \
            > "${WORK_INV_STAGE}"/"${INVOICE_FILE}"

            pdftk "${WORK_INV_STAGE}"/"${INVOICE_FILE}" dump_data output \
                  "${WORK_INV_STAGE}"/temp-pdf-metadata.txt

            printf 'BookmarkBegin\nBookmarkTitle: %s\nBookmarkLevel: 1\nBookmarkPageNumber: 1' "${INVOICE_NUM}" \
                 > "${WORK_INV_STAGE}"/temp-pdf-new-bookmark.txt

            cat "${WORK_INV_STAGE}"/temp-pdf-metadata.txt \
                "${WORK_INV_STAGE}"/temp-pdf-new-bookmark.txt \
                > "${WORK_INV_STAGE}"/temp-pdf-metadata-with-bookmark.txt

            pdftk "${WORK_INV_STAGE}"/"${INVOICE_FILE}" \
                  update_info "${WORK_INV_STAGE}"/temp-pdf-metadata-with-bookmark.txt \
                  output "${WORK_INV_STAGE}"/"${INVOICE_FILE}".pdfbm

            cp "${WORK_INV_STAGE}"/"${INVOICE_FILE}".pdfbm \
                "${EXT_TARGET_DIR}"/"${INVOICE_FILE}"

        else
            cp "./Invoice-Recaps/final-text/${INVOICE_NUM}-recap.txt" \
               "${EXT_TARGET_DIR}"/"${INVOICE_FILE}"
        fi
    }
    sourcefile="${1}"
    if [[ ! -s "$sourcefile" ]]; then
        echo "Skipping Empty File $sourcefile"
        return 0
    fi
    if [[ $RUN_MODE = '--execute' ]]; then
        work_dir="${WORK_TEMP}/${2}-invoices"
    elif [[ $RUN_MODE = '--copy-only' ]]; then
        work_dir="./invoice-list-files/${2}-invoices"
    fi
    work_dir_and_prefix="${work_dir}/${2}"
    work_batch_dir='/tmp' # dynamic, safe init value
    target_dir="./invoice-list-files/${2}-invoices"
    target_dir_and_prefix="${target_dir}/${2}"
    target_batch_dir='/tmp' # dynamic, safe init value
    meta_dir='./invoice-list-files/meta/'
    batch_log="./${meta_dir}"/"${2}"-batch-log.txt
    batch_script="./${meta_dir}"/"${2}"-batch-script.txt
    batchnumber=0
    batchsize="$BATCH_SIZE"
    invcnt=1000000   # make sure larger than batch size to force a first pass switch
    lastfileprefix='n/a' # dynamic, safe init value that won't match first RO#
    firstbatchronum='n/a'
    lastbatchronum='n/a'
    mkdir -p "$target_dir"
    mkdir -p "$meta_dir"
    if [[ -f "${batch_log}" ]]; then
        rm "${batch_log}"
    fi
    if [[ -f "${batch_script}" ]]; then
        rm "${batch_script}"
    fi
    touch "${batch_log}"
    touch "${batch_script}"

    for filename in $(cat "${sourcefile}"); do
        filenameprefix=$(perl -alne 'print $1 if /^(\d+[^._]*)/' <<<$filename)
        if [[ "$lastfileprefix" = "$filenameprefix" ]]; then
            do_copy "${work_batch_dir}" "$filename"
        else
            if ((invcnt >= batchsize)); then
                if [[ "$batchnumber" != 0 ]]; then
                    lastbatchronum="${lastfileprefix}"
                    echo "${lastbatchronum}" >> "${batch_log}"
                    cat <<BAT >>"${batch_script}"
cmd /V /C "pdfpg.exe ./invoice-list-files/$2-invoices/$2-${batchnumber}/*.pdf ./invoice-list-files/${2}-${firstbatchronum}-${lastbatchronum}.pdf"
BAT
                fi
                invcnt=1
                ((batchnumber+= 1))
                firstbatchronum="${filenameprefix}"
                target_batch_dir="${target_dir_and_prefix}-${batchnumber}"
                work_batch_dir="${work_dir_and_prefix}-${batchnumber}"
                clear_dir "${work_batch_dir}"
                echo "Segregating batch # $batchnumber $2 ROs"
                echo -n "$2 Batch-${batchnumber} ${firstbatchronum} " >> "${batch_log}"
                copy_recap "${work_batch_dir}" "$filenameprefix"
                do_copy "${work_batch_dir}" "$filename"
            else
                ((invcnt+= 1))
                copy_recap "${work_batch_dir}" "$filenameprefix"
                do_copy "${work_batch_dir}" "$filename"
            fi
            lastfileprefix="$filenameprefix"
        fi
    done
    lastbatchronum="${lastfileprefix}"
    echo "${lastbatchronum}" >> "${batch_log}"
    cat <<BAT >>"${batch_script}"
cmd /V /C "pdfpg.exe ./invoice-list-files/$2-invoices/$2-${batchnumber}/*.pdf ./invoice-list-files/${2}-${firstbatchronum}-${lastbatchronum}.pdf"
BAT
    cat "$batch_log"
    if [[ $RUN_MODE = '--execute' ]]; then
        cp -r "${WORK_TEMP}"/*-invoices ./invoice-list-files/
    fi
}

(
    cd "$BASE_WORK_DIR"

    [[ -f ./invoice-lists/"$SEGREGATION_LIST_FILE" ]] \
        || die "Segregation List Must Exist"

    if [[ ! -s ./invoice-lists/"$SEGREGATION_LIST_FILE" ]]; then
        echo "Skipping Empty File ./invoice-lists/$SEGREGATION_LIST_FILE"
        exit 0
    fi

    echo "File Count: $(wc -l ./invoice-lists/$SEGREGATION_LIST_FILE)"

    copy_files_from_list \
        ./invoice-lists/"$SEGREGATION_LIST_FILE" \
        "$SEGREGATION_LABEL"

)
