#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

RO_LIST_FILE="${1:?File Name Required}"

[[ -f "$RO_LIST_FILE" ]] || [[ -p "$RO_LIST_FILE" ]] || die "File $RO_LIST_FILE must exist!"

function do_psql() {
    psql "service=${DU_ETL_PG_SERVICE}" --set=ON_ERROR_STOP=1 --quiet
}

do_psql <<SQL
    CREATE TEMP TABLE rolist (
        invoicenumber text PRIMARY KEY
    );

     \copy rolist FROM '$RO_LIST_FILE'

    CREATE TEMP TABLE list_ranges AS
    SELECT min(invnum)                        AS "Starting Invoice",
           max(invnum)                        AS "Ending Invoice",
           max(invnum) - min(invnum) + 1      AS "Invoice Count",
           sum(max(invnum) - min(invnum) + 1)
               OVER (ORDER BY min(invnum))    AS "Running Count"
           -- sequential invoices also have sequential row numbers
           -- so all invoices having the same gap with their row number
           -- belong to the same sequential range.
      FROM (SELECT invoicenumber::integer AS invnum,
                   invoicenumber::integer - row_number() OVER (ORDER BY invoicenumber) AS rn
              FROM (SELECT replace(invoicenumber, '.pdf', '') AS invoicenumber FROM rolist) cleanrolist
             WHERE invoicenumber ~ '^\d+'
           ) src
  GROUP BY rn
  ORDER BY 1;

      \C 'Missing Invoice Number Groups'
  SELECT *
    FROM list_ranges;

      \C 'Missing Invoice Numbers Having a Suffix'

  SELECT replace(invoicenumber, '.pdf', '') AS invoicenumber
    FROM rolist
   WHERE replace(invoicenumber, '.pdf', '') ~ '\D'
ORDER BY invoicenumber;

SQL
