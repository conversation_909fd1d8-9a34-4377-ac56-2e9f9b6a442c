#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

BASE_WORK_DIR="${1:?Working Directory Required}"
INVOICES_DIR="$BASE_WORK_DIR"/Invoices
LISTS_DIR="$BASE_WORK_DIR"/invoice-lists


[[ -d "$BASE_WORK_DIR" ]] || die "Working directory must exist"
[[ -d "$INVOICES_DIR"  ]] || die "Working directory must contain Invoices subdirectory"

[[ -d "$BASE_WORK_DIR"/split-meta ]]         || die "split-meta must exist"
[[ -d "$BASE_WORK_DIR"/invoicemaster-meta ]] || die "invoicemaster-meta must exist"
[[ -d "$BASE_WORK_DIR"/scenariokey-meta ]]   || die "scenariokey-meta must exist"

export INVNUM_SUFFIX=''
if [[ "${2:-NA}" = 'ADP' ]]; then
    echo "ADP Rules - Matching Entire RO#"
    INVNUM_SUFFIX='.'
else
    echo "General Rules - RO#s Are Prefix Matched"
fi
function toggle_invnum_type() {
    perl -alne 'printf("%s%s\n", $_, $ENV{INVNUM_SUFFIX});'
}

function stage_file_lists() {
    ls -1 ./Invoices > ./invoice-lists/invoice-file-names.txt

    cat ./invoice-lists/qualified-invoices.txt \
        ./invoice-lists/not-qualified-invoices.txt \
        | sort \
        > ./invoice-lists/all-selected-invoice-numbers.txt

    # Take the list of qualified RO#s and locate files
    # whose name starts with the same sequence (i.e., all types)
    echo "Qual Files"
    grep -F -f <(toggle_invnum_type < ./invoice-lists/qualified-invoices.txt) \
               ./invoice-lists/invoice-file-names.txt \
               > ./invoice-lists/qualified-file-names.txt

    # Locate all versions of files who invoice number prefix
    # matches the not qualified listing generated from MAGE
    echo "Not Qual Files"
    grep -F -f <(toggle_invnum_type < ./invoice-lists/not-qualified-invoices.txt) \
               ./invoice-lists/invoice-file-names.txt \
               > ./invoice-lists/not-qualified-file-names.txt

    # Extract just the unique RO#s from the listing of
    # all service invoices.
    echo "Invoice Numbers Present"
    perl -alne 'print $1 if /^(\d+[^._]?)/' \
                     < ./invoice-lists/invoice-file-names.txt \
                     | uniq \
                     | sort \
                     > ./invoice-lists/invoice-numbers-present.txt

    # Perform recently closed RO process on the selected range subset only
    # so we first need to compute that...
    echo "Selection Open ROs"
    comm -12 \
         ./invoice-lists/all-selected-invoice-numbers.txt \
         ./invoice-lists/not_closed_ros.txt \
         > ./invoice-lists/selection-not-closed-ros.txt

    # Using the unique listing of service invoice RO#s
    # identify any that also appear on our list of Open
    # ROs so that we know which of the present service invoices
    # are recently closed and therefore potentially have qualified
    # detail that is not in the MAGE database.
    echo "Recently Closed Numbers"
    comm -12 \
         ./invoice-lists/invoice-numbers-present.txt \
         ./invoice-lists/selection-not-closed-ros.txt \
         > ./invoice-lists/recently-closed-ros.txt

    # ... identify those recently closed retail ROs explicitly for
    # handling as production deems fit.
    echo "Recently Closed Files"
    grep -F -f <(toggle_invnum_type < ./invoice-lists/recently-closed-ros.txt) \
         ./invoice-lists/invoice-file-names.txt \
         > ./invoice-lists/recently-closed-files.txt

    # Lots of Not Reported RO# that we want to limit to the sub-range covered
    # by our scenario.
    echo "In Range AND Not Reported Numbers"
    comm -12 \
         ./invoice-lists/all-in-range-invoices.txt \
         ./invoice-lists/not_reported_ros.txt \
         > ./invoice-lists/not-reported-ros-in-range.txt

    echo "Closed But Not Reported Files - Basically the same as 'recently closed'"
    grep -F -f <(toggle_invnum_type < ./invoice-lists/not-reported-ros-in-range.txt) \
         ./invoice-lists/invoice-file-names.txt \
         > ./invoice-lists/closed-but-not-reported-files.txt

    echo "Missing Files In Range"
    grep -F -f <(toggle_invnum_type < ./invoice-lists/all-in-range-invoices.txt) \
               ./invoice-lists/missing_invoices.txt \
               > ./invoice-lists/missing-inrange-invoices.txt

    echo "Missing Files - In Range Open"
    grep -F -f <(toggle_invnum_type < ./invoice-lists/not_closed_ros.txt) \
               ./invoice-lists/missing-inrange-invoices.txt \
               > ./invoice-lists/missing-not-closed.txt

    echo "Missing Files - In Range Not Reported"
    grep -F -f <(toggle_invnum_type < ./invoice-lists/not_reported_ros.txt) \
               ./invoice-lists/missing-inrange-invoices.txt \
               > ./invoice-lists/missing-not-reported.txt

    echo "Missing Files - In Range Reported"
    comm -13 \
         <(cat ./invoice-lists/missing-not-closed.txt ./invoice-lists/missing-not-reported.txt | sort) \
         ./invoice-lists/missing-inrange-invoices.txt \
         > ./invoice-lists/missing-reported-invoices.txt

    echo "Missing Files - In Range Selected"
    grep -F -f <(toggle_invnum_type < ./invoice-lists/all-selected-invoice-numbers.txt) \
               ./invoice-lists/missing-inrange-invoices.txt \
               > ./invoice-lists/missing-selected-invoices.txt

    return 0
}

(
    cd "$BASE_WORK_DIR"
    clear_dir "$LISTS_DIR"

    cp ./split-meta/*         "$LISTS_DIR"
    cp ./invoicemaster-meta/* "$LISTS_DIR"
    cp ./scenariokey-meta/*   "$LISTS_DIR"

    stage_file_lists || die "File listing staging failed"

    say "Filtered Invoice Lists Created"
)
