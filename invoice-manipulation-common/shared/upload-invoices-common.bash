#!/usr/bin/env bash

# Basically this is a common remote-execution wrapper around
# the existing unzip-split-load routines.  File selection
# facilitites are added for usability since the user probably
# doesn't know the Linux CLI very well.

# TODO: Implement --copy-from, --copy-to mode
# At present we are dealing with the common case
# of a single zip file per store and no sharing.
# The copy from/to mode is a convenience mode.

source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

DMS="${1:? Argument 1 required - suffix of DMS repos (Reynolds, AutoSoft,...)}"
shift

dms=${DMS,,}

[[ -d "${DU_ETL_HOME}/DU-DMS/DMS-${DMS}" ]] || die "DMS-${DMS} does not exist"

SCN_KEY="${1:?Argument 2 Required - Scenario Key or load or test [load]}"
shift

DMS_REPO_BASE="$DU_ETL_HOME"/DU-DMS/DMS-${DMS}
TMP_DIR=$HOME/tmp/du-etl-upload-${dms}-invoices
TMP_AUDIT_DIR=$HOME/tmp/du-etl-audit-upload-${dms}-invoices
LOAD_RESULT_FILE="$TMP_DIR"/invoice-load-result.txt
AUDITLOAD_RESULT_FILE="$TMP_AUDIT_DIR"/invoice-audit-load-result.txt

INVOICES_BASE="${INVOICES_BASE:-/etl}"
INVOICES_IN=$INVOICES_BASE/invoices-in/${dms}
INVOICES_ARCHIVE=$INVOICES_BASE/invoices-archive/${dms}
INVOICES_ERR=$INVOICES_BASE/invoices-err/${dms}

DMS_INVOICES_DIR="${DU_ETL_WORK}"/etl-${dms}/inv-${dms}
TEST_OUTPUT_DIR=/tmp/du-etl-test-upload-${dms}-invoices
MAGE_LOAD="false"
AUDIT_LOAD="true"
LOADIT="true"
LOADITAUDIT="true"

function select_input_zip_file() {
    local prompt="Please select a source file:"
    local dir_option="Select Archived File"
    local current_dir="${DMS_INVOICES_DIR}"
    local files
    local opt
    local IFS=$'\n'

    set +o nounset

    while :
    do
        PS3="$prompt "
        files=( $(find "${current_dir}" -maxdepth 1 -mindepth 1 ! -name "archive" -printf '%f\n' | sort) )

        select opt in  "${files[@]}" \
                       "${dir_option}" \
                       "Quit"
        do
            if [[ "${opt}" = "Quit" ]]; then
                quit
            elif [[ "${opt}" = "Select New File" ]]; then
                current_dir="${DMS_INVOICES_DIR}"
                dir_option="Select Archived File"
            elif [[ "${opt}" = "Select Archived File" ]]; then
                current_dir="${DMS_INVOICES_DIR}/archive"
                dir_option="Select New File"
            elif [[ $REPLY =~ ^[0-9]+$ ]] && (( $REPLY > 0 && $REPLY <= ${#files[@]} )); then
                say "Current selection # ${REPLY}: ${opt}"
                prompt_retry "Do you wish to change your selection?"
                if [[ "$RETRY_ANSWER" = false ]]; then
                    INPUT_ZIP_FILE="${current_dir}/${opt}"
                    break 2
                fi
            else
                yell "Invalid option [ ${REPLY} ]"
            fi
          break
        done
    done
    set -o nounset
}

function build_it() {
    clear_dir "$TMP_DIR"

    # Clear the build output directory only if we are testing and only
    # prior to building - the resultant output will need to be usable
    # in "test load" mode
    if [[ "$SCN_KEY" = 'test' ]]; then
        clear_dir "$OUTPUT_DIRECTORY"
    else
        dir_exists_or_die "$OUTPUT_DIRECTORY"
    fi

    echo "$STORE_ID" > "$TMP_DIR"/store-id

    [[ -f "$INPUT_ZIP_FILE" ]] || die "Selected file does not seem to exist... ($INPUT_ZIP_FILE)"

    OUTPUT_ZIP_FILENAME="${STORE_ID}-${DMS}ROs-"$(date +%Y%m%d_%H%M%S)".zip"

    "$DMS_REPO_BASE"/invoice-manipulation/unzip-split-load \
          --src-file "$INPUT_ZIP_FILE" \
          --store-id "$STORE_ID" \
          --no-ggs-db "$TMP_DIR"

    zip -jq "$TMP_DIR"/"$OUTPUT_ZIP_FILENAME" "$TMP_DIR"/*

    echo "**********************************************************************"
    echo "    Store Id:  $(< $TMP_DIR/store-id)"
    echo "  Input File:  $INPUT_ZIP_FILE"
    echo " Output File:  $OUTPUT_ZIP_FILENAME"
    echo "Record Count:  $(wc -l $TMP_DIR/*.tsv)"
    unzip -l "$TMP_DIR"/"$OUTPUT_ZIP_FILENAME"
    echo "**********************************************************************"
    echo "NOTE: ROs Found in Files, Not Checked Against MAGE"
 
    cp "$TMP_DIR"/"$OUTPUT_ZIP_FILENAME" \
         "$OUTPUT_DIRECTORY"/"$STORE_ID".tmp || die "Failed to copy data to output directory"
    mv "$OUTPUT_DIRECTORY"/"$STORE_ID".tmp \
         "$OUTPUT_DIRECTORY"/"$OUTPUT_ZIP_FILENAME" || die "Failed to rename tmp file to zip"
  
    ls -lh "$OUTPUT_DIRECTORY"
}

function load_it() {
    local store_exists

    clear_dir "$TMP_DIR"

    function psql_ggs() {
        if [[ ! "$SCN_KEY" = 'test' ]]; then
            psql "service=${GGS_PROD_PG_SERVICE:-Production GGS Needed}" --set=ON_ERROR_STOP=1 "$@"
        else
            psql "service=${GGS_LOCAL_PG_SERVICE:-Local GGS Needed for Testing}" --set=ON_ERROR_STOP=1 "$@"
        fi
    }

    FILE_TO_LOAD="${1:?File Specification Required}"

    progress "Loading file $(basename $FILE_TO_LOAD)"

    echo "Loading $FILE_TO_LOAD" >> "$LOAD_RESULT_FILE"
    echo "" >> "$LOAD_RESULT_FILE"

    unzip -q "$FILE_TO_LOAD" -d "$TMP_DIR"

    STORE_ID="$(< $TMP_DIR/store-id)"
    store_exists=$(
        psql_ggs --set=s_id="$STORE_ID" \
            -At <<SQL
            select count(*) FROM store WHERE s_id = :'s_id';
SQL
)

    if [[ ! "$store_exists" = '1' ]]; then
        yell "Store $STORE_ID does not exist"
        echo "Store $STORE_ID does not exist" >> "$LOAD_RESULT_FILE"
        return 1
    fi

    if [[ "$SCN_KEY" = 'test' ]]; then
        ls -lh "$TMP_DIR"
    fi

    if [[ ! -s "$TMP_DIR"/store-id ]]; then
       yell "non-empty store-id file expected in zip file but it is not present"
       return 1
    fi   

    # Avoid timing issues with conversion of DMS-Reynolds unzip-split-load to shared DU-ETL version
    if [[ -f "$TMP_DIR"/du-etl-invoices-to-upload.tsv ]]; then
        mv "$TMP_DIR"/du-etl-invoices-to-upload.tsv \
           "$TMP_DIR"/${dms}-invoices-to-upload.tsv
    fi

    if  [[ ! -s "$TMP_DIR"/${dms}-invoices-to-upload.tsv ]]; then
      yell "non-empty reynolds-invoices-to-upload.tsv expected in zip file but it is not present"
      return 1
    fi 

    {
        echo "Store Identification: $(cat $TMP_DIR/store-id)"
        echo ""
        echo "Loading Outcome:"
    } >> "$LOAD_RESULT_FILE"

    if psql_ggs -q <<SQL >> "$LOAD_RESULT_FILE" 2>&1; then
        CREATE TEMP TABLE invsrc (LIKE fixedopsserviceinvoicebase);
        \set QUIET off
        \copy invsrc FROM '$TMP_DIR/${dms}-invoices-to-upload.tsv'

        SELECT count(*) AS "Source File RO# Count" FROM invsrc;

        INSERT INTO fixedopsserviceinvoicebase
             SELECT *
               FROM invsrc
              WHERE NOT EXISTS (SELECT 1
                                  FROM fixedopsserviceinvoicebase fsb
                                 WHERE (fsb.s_id, fsb.invoicenumber) = (invsrc.s_id, invsrc.invoicenumber));
SQL
        echo "INSERT: indicates how many not already present" >> "$LOAD_RESULT_FILE"
    fi

}

function load_it_to_audit() {
    local store_exists
    AUDIT_LOCAL_PG_SERVICE=audit_etl_local
    clear_dir "$TMP_AUDIT_DIR"
    # echo "AUDIT_PROD_PG_SERVICE       $AUDIT_PROD_PG_SERVICE"
    echo "AUDIT_LOCAL_PG_SERVICE  $AUDIT_LOCAL_PG_SERVICE"
    echo "SCN_KEY      $SCN_KEY"
    function psql_audit() {
        if [[ ! "$SCN_KEY" = 'test' ]]; then
            
            psql "service=${AUDIT_PROD_PG_SERVICE:-Production AUDIT Needed}" --set=ON_ERROR_STOP=1 "$@"
        else
            psql "service=${AUDIT_LOCAL_PG_SERVICE:-Local AUDIT Needed for Testing}" --set=ON_ERROR_STOP=1 "$@"
        fi
    }

    FILE_TO_LOAD="${1:?File Specification Required}"

    progress "Loading file $(basename $FILE_TO_LOAD)"

    echo "Loading $FILE_TO_LOAD" >> "$AUDITLOAD_RESULT_FILE"
    echo "" >> "$AUDITLOAD_RESULT_FILE"

    unzip -q "$FILE_TO_LOAD" -d "$TMP_AUDIT_DIR"

    STORE_ID="$(< $TMP_AUDIT_DIR/store-id)"
    
    if [[ "$SCN_KEY" = 'test' ]]; then
        ls -lh "$TMP_AUDIT_DIR"
    fi

    if [[ ! -s "$TMP_AUDIT_DIR"/store-id ]]; then
       yell "non-empty store-id file expected in zip file but it is not present"
       return 1
    fi   

    # Avoid timing issues with conversion of DMS-Reynolds unzip-split-load to shared DU-ETL version
    if [[ -f "$TMP_AUDIT_DIR"/du-etl-invoices-to-upload.tsv ]]; then
        mv "$TMP_AUDIT_DIR"/du-etl-invoices-to-upload.tsv \
           "$TMP_AUDIT_DIR"/${dms}-invoices-to-upload.tsv
    fi

    if  [[ ! -s "$TMP_AUDIT_DIR"/${dms}-invoices-to-upload.tsv ]]; then
      yell "non-empty reynolds-invoices-to-upload.tsv expected in zip file but it is not present"
      return 1
    fi 

    {
        echo "Store Identification: $(cat $TMP_AUDIT_DIR/store-id)"
        echo ""
        echo "Loading Outcome:"
    } >> "$AUDITLOAD_RESULT_FILE"

    echo "AUDITLOAD_RESULT_FILE     $AUDITLOAD_RESULT_FILE"

    if psql_audit -q <<SQL >> "$AUDITLOAD_RESULT_FILE" 2>&1; then

    CREATE TEMP TABLE invsrc (
	invoiceid text NOT NULL,
	s_id text NOT NULL,
	invoicenumber text NOT NULL,
    invoicesource text NOT NULL
	);

        \set QUIET off
        \copy invsrc FROM '$TMP_AUDIT_DIR/${dms}-invoices-to-upload.tsv'

        INSERT INTO invoice_master.actual_ro_document
             SELECT
                current_date, 
                s_id,
                invoicenumber,
                invoicesource 
               FROM invsrc i
              WHERE NOT EXISTS (SELECT 1
                                  FROM invoice_master.actual_ro_document ai
                                 WHERE (ai.dealership_id, ai.ro_number) = (i.s_id, i.invoiceid));
SQL
    if [ $? -eq 0 ]; then
        echo "Audit insertion of invoices completed"
    else
        die "Audit insertion of invoices failed."
    fi
        echo "INSERT: indicates how many not already present" >> "$AUDITLOAD_RESULT_FILE"
    fi

}

function email() {
    local SUBJECT="$1"
    if [[ ! "$SEND_TO_ETL_INVOICES_NOTIFY" = '' ]]; then
        if type send-email >/dev/null 2>&1; then
            say "Sending Summary Emails"

            send-email --to "$SEND_TO_ETL_INVOICES_NOTIFY" \
                       --subject "$SUBJECT" \
                       --body-file "$LOAD_RESULT_FILE" \
                       --send
        fi
    fi
}

function release_lock() {
    rm "${OUTPUT_DIRECTORY}/.lock"
 }

function perform_load() {
    if [[ -e "${OUTPUT_DIRECTORY}/.lock" ]]; then
        return 0
    fi

    trap release_lock EXIT
    touch "${OUTPUT_DIRECTORY}/.lock"
    while true
    do
        # Exit if no zip files
        if [ "$(find $OUTPUT_DIRECTORY -name '*.zip' | wc -l)" = '0' ]; then
            break
        fi

        # Get newest file; this is a simplistic way to ensure that if
        # an older file is provided that does not completely import and doesn't
        # clear out of the directory that subsequent correct files will still be
        # processed.  Better error handling can make this a moot distinction
        # but processing times are so fast that FIFO or LIFO doesn't matter
        # by itself.
        LOAD_FILE=$(stat --printf="%y %n\n" $OUTPUT_DIRECTORY/*.zip  \
         | sort --reverse | head --lines=1 | awk '{print $4}')

        echo "$LOAD_RESULT_FILE"
        echo "$AUDIT_LOAD"
        say "Beginning Load - this can take a while"
        {
            echo "Beginning Load"
            echo ""
            echo "Current Output Directory Contents:"
            ls -lh "$OUTPUT_DIRECTORY"
            echo ""

        } #> "$LOAD_RESULT_FILE"

        if [[ $MAGE_LOAD = 'true' ]]; then
            if load_it "$LOAD_FILE"; then
                say "Mage Load Completed"
                LOADIT="true"
            else
                say "Mage Load Not Completed"
                LOADIT="false"
            fi
        fi
        if [[ $AUDIT_LOAD = 'true' ]]; then
            if load_it_to_audit "$LOAD_FILE"; then
                say "Audit Load Completed"
                LOADITAUDIT="true"
            else
                say "Audit Load Not Completed"
                LOADITAUDIT="false"
            fi
        fi
        if [[ $LOADIT == "true" && $LOADITAUDIT == "true" ]]; then
            say "Load Completed - Archiving File"
            mv "$LOAD_FILE" "$INVOICES_ARCHIVE"
            # email "${DMS} Invoices Load To Audit is Success - $STORE_ID"
        else
            yell "Load To Audit Failed"
            mv "$LOAD_FILE" "$INVOICES_ERR"
            # email "${DMS} Invoices Load To Audit Failed - $STORE_ID"
        fi
        cat "$LOAD_RESULT_FILE"
    done
}


if [[ $SCN_KEY = 'test' ]]; then
    OUTPUT_DIRECTORY="$TEST_OUTPUT_DIR"
    STORE_ID='[PIMS]SYSTEM'

    if [[ $# = '1' ]]; then
        if [[ "$1" = 'load' ]]; then
            perform_load
        else
            die "Second argument not recognized, expected load, found $1"
        fi
    else
        INPUT_ZIP_FILE="$DMS_REPO_BASE"/invoice-manipulation/src/test/data/etl-upload-${dms}-invoices-1.zip
        build_it
    fi
else
    OUTPUT_DIRECTORY="$INVOICES_IN"
    STORE_ID="${SCN_KEY%+*}" # grab everything before the + sign
    if [[ $SCN_KEY = 'load' ]]; then
        perform_load
    else
        select_input_zip_file
        build_it
        mv "${INPUT_ZIP_FILE}" "${DMS_INVOICES_DIR}/archive"
    fi
fi

exit
