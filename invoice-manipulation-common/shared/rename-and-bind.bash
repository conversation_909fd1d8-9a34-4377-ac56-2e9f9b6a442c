#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

shopt -s nocasematch
set +o errexit

DMS="${1:? Argument 1 required - suffix of DMS repos (Reynolds, AutoSoft,...)}"
shift
dms=${DMS,,}

TMP_DIR=$HOME/tmp/du-etl-inv-${dms}
DMS_INVOICES_DIR="${DU_ETL_WORK}"/etl-${dms}/inv-${dms}
INVOICES_OUT_DIR="${INVOICES_OUT_DIR:=/etl/invoices-out}"

CURRENT_DIR="$(pwd)"

clear_dir "${TMP_DIR}"
mkdir -p "$TMP_DIR/combined"
mkdir -p "$TMP_DIR/meta"

function cleanup() {
   cd "$CURRENT_DIR"
}
trap cleanup EXIT

function select_source_archive() {
    local prompt="Please select a source file:"
    local dir_option="Select Archived File"
    local current_dir="${DMS_INVOICES_DIR}"
    local files
    local opt
    local IFS=$'\n'

    set +o nounset

    while :
    do
        PS3="$prompt "
        files=( $(find "${current_dir}" -maxdepth 1 -mindepth 1 ! -type d -printf '%f\n' | sort) )

        select opt in  "${files[@]}" \
                       "${dir_option}" \
                       "Quit"
        do
            if [[ "${opt}" = "Quit" ]]; then
                quit
            elif [[ "${opt}" = "Select New File" ]]; then
                current_dir="${DMS_INVOICES_DIR}"
                dir_option="Select Archived File"
            elif [[ "${opt}" = "Select Archived File" ]]; then
                current_dir="${DMS_INVOICES_DIR}/archive"
                dir_option="Select New File"
            elif [[ $REPLY =~ ^[0-9]+$ ]] && (( $REPLY > 0 && $REPLY <= ${#files[@]} )); then
                say "Current selection # ${REPLY}: ${opt}"
                prompt_retry "Do you wish to change your selection?"
                if [[ "$RETRY_ANSWER" = false ]]; then
                    INPUT_ZIP_FILE="${current_dir}/${opt}"
                    break 2
                fi
            else
                yell "Invalid option [ ${REPLY} ]"
            fi
          break
        done
    done
    set -o nounset
}

function unzip_source_archive() {
    mkdir -p "$DMS_INVOICES_DIR"/archive

    cd "$TMP_DIR"
    mkdir -p  invoices
    cd invoices
    progress "Extracting ROs from $INPUT_ZIP_FILE"
    unzip -q "$INPUT_ZIP_FILE"
    SOURCE_DIR="$(find . -iname '*.pdf' -print -quit)"
    if [[ -z "$SOURCE_DIR" ]]; then
        yell "No PDF files found from $INPUT_ZIP_FILE"
        exit 1
    fi
    SOURCE_DIR="$(realpath $(dirname "$SOURCE_DIR"))"
    archive="$(basename "$INPUT_ZIP_FILE")"

    mv -f "$INPUT_ZIP_FILE" "$DMS_INVOICES_DIR/archive/"
}

function unzip_tekion_source_archive() {
    mkdir -p "$DMS_INVOICES_DIR"/archive
    cd "$TMP_DIR"
    mkdir -p  invoices
    cd invoices
    progress "Extracting ROs from $INPUT_ZIP_FILE"
    unzip -q "$INPUT_ZIP_FILE"
    SOURCE_DIR="$(pwd)"
    archive="$(basename "$INPUT_ZIP_FILE")"

    mv -f "$INPUT_ZIP_FILE" "$DMS_INVOICES_DIR/archive/"
}

function Move_final_pdf_to_separate_folders_according_to_file_name_dominionvue()
{
cd "$TMP_DIR/splitpdf/"
rm -rf ../splittedoutput
mkdir ../splittedoutput

inputFile="./*.pdf"
    progress "Moving in progress"
	for file in $inputFile
	do
		filename=$(basename "$file")
    	filename="${filename%Page*.*}"
    	if [ -d ../splittedoutput/"${filename}" ];then
    		# echo "Moving ${filename} to ../splittedoutput"
            currentfile="${filename}"
    	else 
    		mkdir ../splittedoutput/"${filename}"
    	fi

    	cp $file ../splittedoutput/"${filename}"
    done
    progress "Moving completed"

}
function combine_pdf_dominionvue()
{
cd "$TMP_DIR/splittedoutput/"
rm -rf ../invoices
mkdir ../invoices

ip=./*
progress "Combining induvidual Ros in progress"
for file in $ip;do
	# progress "Combining induvidual Ros: $file"
	filename=$(basename "$file")

	input="${file}"/*.pdf

	pdftk $input cat output ../invoices/"${filename}.pdf"

done
progress "Combining induvidual Ros completed"
}

function rename_pdfs() {
    progress "Renaming files in $SOURCE_DIR"
    cd "$SOURCE_DIR"
    rm -f *OpenROList*
    if [[ $dms = "ucs" ]]; then
        rename 's/^.*?_00(\w)0*(\d+).*$/$2_$1.pdf/' *.PDF
        ls
    elif [[ $dms = "quorum" ]]; then
        rename 's/\w{0,}_(\d+)_\d+.*$/$1.pdf/' *.pdf
        ls
    elif [[ $dms = "tekion" ]]; then
        cd ../
        mkdir -p inv-tmp
        rm -rf ./inv-tmp/*
        mv ./invoices/* ./inv-tmp/
        rm -rf ./inv-tmp/__MACOSX
        rm -rf ./invoices/*
        cd inv-tmp
        find . -type f -name '*.zip' -exec unzip -- '{}' -x '*.zip' \; 
        find . -type f -name '*.pdf' -print0 | xargs -0 mv -t ../invoices/
        cd ../invoices/
        OTHERNAMEPDFS=$(find . -regex "\./[0-9A-Za-z]+_+\.pdf" | wc -l)
        
        if [[ $OTHERNAMEPDFS -gt "0" ]]; then
            echo "OTHERNAMEPDFS: $OTHERNAMEPDFS"
            ronumber="RO#"'[[:space:]]\{0,\}'":"'[[:space:]]\{0,\}''[0-9]\{0,\}'
            OTHERNAMEPDFSFILE=$(find . -regex "\./[0-9A-Za-z]+_+\.pdf")
            echo "$OTHERNAMEPDFSFILE" > "$TMP_DIR/meta/otherfilenameformat.txt"
            rm -rf "$TMP_DIR/text/" 
            mkdir "$TMP_DIR/text/"
            while IFS= read -r file
            do
            	filenamepdf=$(basename "$file")
                filenamepdf="${filenamepdf%.pdf}"
                pdftotext -layout -enc "UTF-8" $file "$TMP_DIR/text/${filenamepdf}.txt"
                extracted_ro=$(grep -o -m 1 $ronumber $TMP_DIR/text/${filenamepdf}.txt  | sed 's/RO# : //g')
                mv $file "${extracted_ro}.pdf"
            done < "$TMP_DIR/meta/otherfilenameformat.txt"

        fi
     #rename 's/\w{0,}_(\d+)_\w{0,}_\w{0,}_\w{0,}_\w{0,}.*$/$1.pdf/' *.pdf 
     rename -e 's/\w{0,}_(\d+)_\w{0,}_\w{0,}_\w{0,}_\w{0,}.*$/$1.pdf/ or s/(\d+)_\w{0,}.*$/$1.pdf/' *.pdf

    elif [[ $dms = "dominion" || $dms = "dominionvue" ]]; then
            cd ../
            mkdir -p inv-tmp
            rm -rf ./inv-tmp/*
            mv ./invoices/* ./inv-tmp/
            rm -rf ./inv-tmp/__MACOSX
            rm -rf ./invoices/*
            cd inv-tmp
            find . -type f -name '*.zip' -exec unzip -- '{}' -x '*.zip' \; 
            find . -type f -name '*.pdf' -print0 | xargs -0 mv -t ../invoices/
            cd ../invoices/
                ronumber="RO"'[[:space:]]\{0,\}'"#"'[[:space:]]\{0,\}''[0-9]\{0,\}'
                pagenumber="Page"'[[:space:]]\{0,\}''[0-9]\{0,\}''[[:space:]]\{0,\}'"of"'[[:space:]]\{0,\}''[0-9]\{0,\}'

                rm -rf "$TMP_DIR/text/" 
                mkdir "$TMP_DIR/text/"
                rm -rf "$TMP_DIR/splitpdf/" 
                mkdir "$TMP_DIR/splitpdf/"
                progress "Splitting to find exact ro in progress."
                for pdffile in ./*.pdf; do
                    filenamesplitpdf=$(basename "$pdffile")
                    filenamesplitpdf="${filenamesplitpdf%.pdf}"
                    pdftk $pdffile burst output "$TMP_DIR/splitpdf/${filenamesplitpdf}_%06d.pdf" compress
                done
                progress "Splitting to find exact ro completed."
                cd "$TMP_DIR/splitpdf/"
                progress "Converting pdf to text to find exact ro in progress."
                for file in ./*.pdf; do
                    filenamepdf=$(basename "$file")
                    filenamepdf="${filenamepdf%.pdf}"
                    pdftotext -layout -enc "UTF-8" $file "$TMP_DIR/text/${filenamepdf}.txt"
                done
                progress "Converting pdf to text to find exact ro completed"

                cd "$TMP_DIR/text/"
                progress "Finding exact ro and renaming in progress."
                for textfile in ./*.txt; do
                    filenametext=$(basename "$textfile")
                    filenametext="${filenametext%.txt}"
                    extracted_ro=$(grep -o -m 1 $ronumber "$TMP_DIR/text/${filenametext}.txt")
                    extracted_ro=$(echo "$extracted_ro" | sed -r 's/^.{5}//')
                    extracted_pagenumber=$(grep -o -m 1 $pagenumber $TMP_DIR/text/${filenametext}.txt)
                    extracted_pagenumber=$(echo "$extracted_pagenumber"  | sed 's/ //g')
                    mv "$TMP_DIR/splitpdf/${filenametext}.pdf" "$TMP_DIR/splitpdf/${extracted_ro}${extracted_pagenumber}.pdf"
                    # progress "Renaming: ${filenametext}.pdf to ${extracted_ro}${extracted_pagenumber}.pdf"
                done
                progress "Finding exact ro and renaming completed"
                Move_final_pdf_to_separate_folders_according_to_file_name_dominionvue
                combine_pdf_dominionvue
                cd ../invoices/            
    else
        rename 's/[^\d]*(\d+).pdf/$1.pdf/' *.pdf
    fi
        find . -not -regex "\./[0-9]+_?[A-Z]?\.pdf" -not -name "." -delete
        PDFS=$(find . -regex "\./[0-9]+_?[A-Z]?\.pdf" | wc -l)

    if [[ $PDFS = "0" ]]; then
        yell "No PDF files found after renaming. Expected files with ro#.pdf. Source $INPUT_ZIP_FILE"
        exit 1
    fi
}

function create_lists() {
    local num_found num_missing
    progress "Creating list of files"
    cd "$SOURCE_DIR"
    for prefix in $(seq \
        $(ls *.pdf | sort -n | head -n 1 | perl -aln -e '/^(\d+).*$/ && print $1') \
        $(ls *.pdf | sort -n | tail -n 1 | perl -aln -e '/^(\d+).*$/ && print $1')); do
        if [[ $dms = "ucs" ]]; then
            echo ${prefix}_?.pdf
        else
            echo ${prefix}.pdf
        fi
    done > "$TMP_DIR/meta/sequenceoutput.txt"
    if [[ $dms = 'ucs' ]]; then
        cat "$TMP_DIR/meta/sequenceoutput.txt" | grep -P '_\?' > "$TMP_DIR/meta/missinginvoices.txt"
        cat "$TMP_DIR/meta/sequenceoutput.txt" | grep -Pv '_\?' > "$TMP_DIR/meta/foundinvoices.txt"
    else
        ls *.pdf > "$TMP_DIR/meta/foundinvoices.txt"
        grep -v -f "$TMP_DIR/meta/foundinvoices.txt" "$TMP_DIR/meta/sequenceoutput.txt" > "$TMP_DIR/meta/missinginvoices.txt";
    fi
    num_found=$(wc -l < "$TMP_DIR/meta/foundinvoices.txt")
    num_missing=$(wc -l < "$TMP_DIR/meta/missinginvoices.txt")
    progress  "Found ROs: $num_found Missing ROs: $num_missing"
}

function combine() {
    progress "Combining files. Please wait..."
    cd "$SOURCE_DIR"

    # Handle initial ?_?.pdf and ??_?.pdf group
    count=$(find . -type f -name "?_?.pdf" -o -name "??_?.pdf" | wc -l)
    if [[ $count -gt 0 ]]; then
        progress "  0"
        files=$(find . -type f \( -name "?_?.pdf" -o -name "??_?.pdf" \))
        if ! pdftk $files cat output "$TMP_DIR/combined/0xx.pdf"; then
            echo "Error: Failed to combine initial files (?_?.pdf / ??_?.pdf)"
            exit 1
        fi
    fi

    # Grouping by prefix
    for prefix in $(ls *.pdf 2>/dev/null | sort -n | perl -aln -e '/^(\d+)\d\d.*$/ && print $1' | uniq); do
        progress "  $prefix"

        files=$(ls ${prefix}*.pdf 2>/dev/null | grep -E '\.pdf$')
        if [[ -z "$files" ]]; then
            echo "Error: No files found for prefix $prefix"
            exit 1
        fi

        if ! pdftk $files cat output "$TMP_DIR/combined/${prefix}xx.pdf"; then
            echo "Error: pdftk failed for prefix $prefix"
            exit 1
        fi

        # Extra check for "00" prefix (optional: can be removed)
        if [[ ${prefix:0:2} == "00" ]]; then
            if ! pdftk $files cat output "$TMP_DIR/combined/${prefix}xx.pdf"; then
                echo "Error: pdftk failed again for 00-prefix $prefix"
                exit 1
            fi
        fi
    done

    progress "Creating archive of combined files"
    cd "$TMP_DIR/combined"
    FILENAME="$(basename "$INPUT_ZIP_FILE")"
    FILENAME="${FILENAME%%.*}-combined.zip"
    cp $TMP_DIR/meta/*.txt . 2>/dev/null
    zip -q "$FILENAME" *
    mv "$FILENAME" "$INVOICES_OUT_DIR"
    progress "$FILENAME will be transferred to dropbox-ro-exports"
}

function main() {
    select_source_archive
    progress "Process $DMS invoices $INPUT_ZIP_FILE"
    if [[ $dms = "tekion" || $dms = "dominion" || $dms = "dominionvue" ]]; then
        unzip_tekion_source_archive
    else
        unzip_source_archive
    fi
    rename_pdfs
    create_lists 
    combine
    progress "Done"
}

main
