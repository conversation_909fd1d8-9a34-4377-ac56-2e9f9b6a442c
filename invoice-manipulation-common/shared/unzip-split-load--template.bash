# Source in DMS-* Implementation after providing overrides for:

# dms-specific working directory
: "${WORK_DIR:?Must Implement WORK_DIR}"

UNZIP_TARGET="$WORK_DIR"/plainttext-source
SPLIT_WORK="$WORK_DIR"/split-work
SPLIT_TARGET="$WORK_DIR"/split-final
PSQLSCRIPT="$WORK_DIR"/invoice-loading-script.psql
BULKTSVFILE="$WORK_DIR"/fixedopsinvoicebase.tsv
DBSERVICE_ETL="${DU_ETL_PG_SERVICE:?Must define DU_ETL_PG_SERVICE}"

SRC_FILE=
DBSERVICE_GGS=
TARGET_S_ID=
SOURCE_S_ID=
CALLER_OUTPUT_DIR=

# --no-suffix
# --no-dulicates
# --one-page-per-file

function process_command_line_args() {
    while [ $# -gt 0 ]; do
        case "$1" in
            --src-file   ) SRC_FILE="${2}";                            shift 2 ;;
            --ggs-db     ) DBSERVICE_GGS="${2}";                       shift 2 ;;
            --no-ggs-db  ) DBSERVICE_GGS="file"
                           CALLER_OUTPUT_DIR="${2}";                   shift 2 ;;

            --store-id   ) TARGET_S_ID="${2}";                         shift 2 ;;
            --copy-from  ) SOURCE_S_ID="${2}";                         shift 2 ;;
            *            ) yell "Unrecognized option $1; exiting";      exit 1 ;;
        esac
    done
}

function initialize_environment() {
    [[ -f "$SRC_FILE"       ]] || die "Source file must exist: $SRC_FILE"
    [[ -n "$TARGET_S_ID"    ]] || die "Must specify --store-id"
    [[ -n "$DBSERVICE_GGS"  ]] || die "Must specify --ggs-db"

    clear_dir "$WORK_DIR"
    mkdir -p "$UNZIP_TARGET"
    mkdir -p "$SPLIT_WORK"
    mkdir -p "$SPLIT_TARGET"
}

function psql_ggs() {
    psql "service=$DBSERVICE_GGS" --set=ON_ERROR_STOP=1 "$@"
}

function psql_etl() {
    psql "service=$DBSERVICE_ETL" --set=ON_ERROR_STOP=1 "$@"
}

function assert_store_is_present() {
    if [[ "$DBSERVICE_GGS" = 'file' ]]; then
        # when dealing with a file output we will assume
        # that the specified store is present in production.
        STORE_IS_PRESENT='1'
    else
        STORE_IS_PRESENT=$(
            psql_ggs --set=in_s_id="$TARGET_S_ID" \
                 -At <<SQL
            select count(*) FROM store WHERE s_id = :'in_s_id';
SQL
        )
    fi

    if [[ ! "$STORE_IS_PRESENT" = '1' ]]; then
        die "Could not locate $TARGET_S_ID in GGS ($STORE_IS_PRESENT)"
    fi
}

function assert_etl_is_present() {
    psql_etl -Atc "select 'etl';" > /dev/null || die "no etl"
}

function stage_input_file() {
    FILE_INQUIRY="$(file -b "$SRC_FILE")"
    if [[ "$FILE_INQUIRY" =~ ASCII ]]; then
        say "Source File is: Text"
        cp "$SRC_FILE" "$UNZIP_TARGET"/
    elif [[ "$FILE_INQUIRY" =~ Zip ]]; then
        say "Source File is: Zip"
        unzip -q -j "$SRC_FILE" -d "$UNZIP_TARGET"/
    else
        die "Unrecognized Source File Type: $FILE_INQUIRY"
    fi
}

function split_bundle() {
    local source_file=$(readlink -e "${1:?source required}")
    local splitting_dir="${2:?destination required}"
    local FLDS
    local RONUM
    local ROTYP
    [[ -f "$source_file" ]] || die "File not found: $source_file"
    [[ -d "$splitting_dir" ]] || die "Directory not found: $splitting_dir"
    (
        cd "$SPLIT_WORK"
        perform_split "$source_file"

    ) || die "Split of $source_file Failed"
    (
        cd "$SPLIT_WORK"
        move_split_work_dir_items_to_destination "$splitting_dir"
    ) || die "Processing of split files failed"
}

function dms_hook_clean_input_document() {
    return
}

function iterate_over_source_text_files_and_split_them() {
    (
        cd "$UNZIP_TARGET"

        for pdf in *.{pdf,PDF}; do
            if [[ ! -e "$pdf" ]]; then
                continue
            fi
            filename="${pdf%.*}"
            pdftotext -layout "${pdf}" "${filename}.txt"
            rm "${pdf}"
        done

        for invoicebundle in ./*; do
            dos2unix -q "$invoicebundle"
            progress "Processing $invoicebundle"
            dms_hook_clean_input_document "$invoicebundle"
            split_bundle "$invoicebundle" "$SPLIT_TARGET"
        done
    ) || die "Split Loop Failed"
}

function load_each_split_file_into_etl_database_then_export_tsv() {
(
    function remove_existing_etl_load_script() {
        cd "$SPLIT_TARGET"
        if [[ -f "$PSQLSCRIPT" ]]; then
            rm "$PSQLSCRIPT"
        fi
    }

    function append_script_preamble_to_etl_load_script() {
        cat <<SQL >> "$PSQLSCRIPT"
            BEGIN;

            \set target_s_id '$TARGET_S_ID'

            CREATE TEMP TABLE invoicedoc (
                invoicenumber text NOT NULL PRIMARY KEY,
                invoicecontent text NOT NULL
            );
            INSERT INTO invoicedoc
            VALUES ('N/A', 'N/A')
SQL
    }

    function append_each_split_file_to_etl_load_script() {
        for filetoload in ./*; do
            ronumber="$(basename $filetoload)"
            # only process files since subdirs can be present
            if [[ -f "$filetoload" ]]; then
                echo ""
                echo "\set invoice"' `cat '"'""$filetoload""'"'`'
                echo ", ('$ronumber',:'invoice')"
            fi >> "$PSQLSCRIPT"
        done

        echo ""  >> "$PSQLSCRIPT"
        echo ";" >> "$PSQLSCRIPT"
        echo ""  >> "$PSQLSCRIPT"
    }

    function append_etl_copy_to_tsv_command_to_etl_load_script() {
        cat <<SQL >> "$PSQLSCRIPT"
            \o '${BULKTSVFILE}'

            COPY (
                SELECT s_id || '>' || invoicenumber AS invoiceid,
                       s_id,
                       invoicenumber,
                       null as customerid, null as vin, null as vehicle_year, null as vehicle_make, null as vehicle_model, null as opendate, null as closedate, 0 as deductible, 0 as cp_laborsale, 0 as cp_partssale, now() as recordcreationdate,
                       true as calculatederror, false as calculatedwarning, 'Source Only' as calculatedmessage, false as manuallyverified, true as manuallyallow, 'Default Allow' as manualdate,
                       invoicecontent AS invoicesource,
                       null as advisorid, null as advisorname, false as isvoid, 'N/A' AS invoicesummary, false as donotdelete, false as isopen, 0 as laborsaletotal, 0 as partssaletotal, '' as laborops, '' as laborophash,
                       null as sourceinvoicetype, 0 as linecount, 0 as laborcount, 0 as partcount, 0 as adjustmentcount, null as advisor_full
                FROM (
                    SELECT invoicecontent, invoicenumber, :'target_s_id'::text AS s_id
                    FROM invoicedoc
                    WHERE invoicenumber <> 'N/A'
                ) AS sourcecleanup
            ) TO stdout;

            \o
SQL
    }

    function append_script_postamble_to_etl_load_script() {
        echo "ROLLBACK;" >> "$PSQLSCRIPT"
    }

    function execute_etl_load_script() {
        say "Loading ROs into local ETL database"
        psql_etl -qf "$PSQLSCRIPT" || die "Failed to load invoices into ETL"
    }

    remove_existing_etl_load_script
    append_script_preamble_to_etl_load_script
    append_each_split_file_to_etl_load_script
    append_etl_copy_to_tsv_command_to_etl_load_script
    append_script_postamble_to_etl_load_script
    execute_etl_load_script

) || die "Failed to load invoices into GGS"
}

function load_etl_script_tsv_output_into_ggs() {
    [[ -f "${BULKTSVFILE:?File Name Required}" ]] || die "Expecting an existing ETL TSV file here: $BULKTSVFILE"
    say "Loading ROs into GGS database"
    psql_ggs -q <<SQL
    CREATE TEMP TABLE invsrc (LIKE fixedopsserviceinvoicebase);
    \set QUIET off
    \copy invsrc FROM '${BULKTSVFILE}'

    INSERT INTO fixedopsserviceinvoicebase
         SELECT *
           FROM invsrc
          WHERE NOT EXISTS (SELECT 1
                              FROM fixedopsserviceinvoicebase fsb
                             WHERE (fsb.s_id, fsb.invoicenumber) = (invsrc.s_id, invsrc.invoicenumber));

SQL
    echo "(COPY: total attempted; INSERT: not already present)"
}

function send_etl_script_tsv_output_back_to_caller() {
    progress "Passing ROs back to calling script via $CALLER_OUTPUT_DIR"
    cp "${BULKTSVFILE:?File Name Required}" "$CALLER_OUTPUT_DIR"/du-etl-invoices-to-upload.tsv
    if  [[ ! -s "$CALLER_OUTPUT_DIR"/du-etl-invoices-to-upload.tsv ]]; then
      die "Load Failed - du-etl-invoices-to-upload.tsv file is empty"
    fi  
}

function perform_store_to_store_copy() {
    SOURCE_IS_PRESENT=$(
    psql_ggs --set=in_s_id="$SOURCE_S_ID" \
         -At <<SQL
    select count(*) FROM store WHERE s_id = :'in_s_id';
SQL
)
    if [[ ! "$SOURCE_IS_PRESENT" = '1' ]]; then
        die "Could not locate $SOURCE_S_ID in GGS ($SOURCE_IS_PRESENT)"
    fi

    say "Copying from $SOURCE_S_ID to $TARGET_S_ID"

    psql_ggs --set=target_s_id="$TARGET_S_ID" --set=source_s_id="$SOURCE_S_ID" <<SQL
        INSERT INTO fixedopsserviceinvoicebase
        SELECT :'target_s_id' || '>' || invoicenumber AS invoiceid,
               :'target_s_id' AS s_id,
               invoicenumber,
               customerid, vin, vehicle_year, vehicle_make, Vehicle_model, opendate, closedate, deductible,
               cp_laborsale, cp_partssale, now() as recordcreationdate,
               calculatederror, calculatedwarning, calculatedmessage, manuallyverified, manuallyallow, manualnote,
               invoicesource,
               advisorid, advisorname, isvoid, invoicesummary, donotdelete, isopen,
               laborsaletotal, partsaletotal, laborops, laborophash,
               sourceinvoicetype, linecount, laborcount, partcount, adjustmentcount, advisor_full
          FROM fixedopsserviceinvoicebase invsrc
         WHERE s_id = :'source_s_id'
           AND NOT EXISTS (SELECT 1
                             FROM fixedopsserviceinvoicebase fsb
                            WHERE s_id = :'target_s_id'
                              AND (:'target_s_id', fsb.invoicenumber) = (invsrc.s_id, invsrc.invoicenumber));
SQL
}

function main() {
    process_command_line_args "$@"

    # Short-circuit for the simple case where the data already exists in production
    # and we simply want to create a copy with a new store id.
    if [[ -n "$SOURCE_S_ID" ]]; then
        perform_store_to_store_copy
        return
    fi

    initialize_environment
    assert_store_is_present
    assert_etl_is_present
    stage_input_file

    FILE="${INV_DMS_HOME}"/src/unzip-split-load--template-override.bash
    if [[ -f "$FILE" ]]; then
      source "$FILE"
      iterate_over_source_text_files_and_split_them_override	
    else
      iterate_over_source_text_files_and_split_them
    fi

    load_each_split_file_into_etl_database_then_export_tsv
    if [[ "$DBSERVICE_GGS" = 'file' ]]; then
        send_etl_script_tsv_output_back_to_caller
    else
        load_etl_script_tsv_output_into_ggs
    fi
    say "Load Completed"
}
