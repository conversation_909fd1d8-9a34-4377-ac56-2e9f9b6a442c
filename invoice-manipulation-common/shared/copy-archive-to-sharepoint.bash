

source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
shopt -s nocasematch
sudo chown -R etl:etluser /etl/invoices-out
sudo chown -R etl:etluser /etl/invoices-to-sharepoint-archive

INVOICES_OUT_DIR="${INVOICES_OUT_DIR:-/etl/invoices-out}"
SHAREPOINT_COPY_ARCHIVE_DIR="${SHAREPOINT_COPY_ARCHIVE_DIR:-/etl/invoices-to-sharepoint-archive}"
#DST="sharepoint:etl/dropbox-ro-exports"
DST="sso1-onedrive:etl/dropbox-ro-exports"
#while true; do
   # Exit if no zip files in queue
#   if [ "$(find $INVOICES_OUT_DIR -name '*.zip' | wc -l)" = '0' ]; then
#      exit
#   fi

   # Get oldest file in queue
#   ZIP_FILE=$(stat --printf="%y %n\n" $INVOICES_OUT_DIR/*.zip  \
#      | sort | head --lines=1 | awk '{print $4}')

find $INVOICES_OUT_DIR -type f -iname "*.zip" | while read i
do
   ZIP_FILE="$i"
   echo "Transferring $ZIP_FILE"
   rclone copy "$ZIP_FILE" "$DST"
   mkdir -p "$SHAREPOINT_COPY_ARCHIVE_DIR"
   mv "$ZIP_FILE" "$SHAREPOINT_COPY_ARCHIVE_DIR"
done
