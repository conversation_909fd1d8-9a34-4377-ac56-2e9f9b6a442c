

source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
shopt -s nocasematch

INVOICES_OUT_DIR="${INVOICES_OUT_DIR:-/etl/invoices-out}"
FTP="<EMAIL>:/ftpdata/dropbox-ro-exports"

while true; do
   # Exit if no zip files in queue
   if [ "$(find $INVOICES_OUT_DIR -name '*.zip' | wc -l)" = '0' ]; then
      exit
   fi

   # Get oldest file in queue
   ZIP_FILE=$(stat --printf="%y %n\n" $INVOICES_OUT_DIR/*.zip  \
      | sort | head --lines=1 | awk '{print $4}')
   
   echo "Transferring $ZIP_FILE"
   scp "$ZIP_FILE" "$FTP"   
   rm -f "$ZIP_FILE"
done

