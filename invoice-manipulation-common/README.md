# Guidelines

*Enumeration of Invoices*

`./invoice-manipulation-common/enumerate-invoice-directory <SRC_DIR>`

Use the above to generate a missing invoice number listing in the `SRC_DIR/meta/missing.txt` file as well as separating multples revisions for a given invoice number into `SRC_DIR/enumerate/final` and `SRC_DIR/enumerate/other` directories.  The `final` directory will contain just the "most recent" version for a given invoice number while the `other` directory will contain all the other documents.

Apply the **Combine /w Bookmarks** process to these directories and manually distribute the resultant PDF bundles.

*Combine w/ Bookmarks:*

1. Merge the resultant PDF files together, manually, into batches of 100 that all share the same invoice prefix, using your *PDF Merging Software*.
    1. Due to the file renaming this merged document will be in invoice number order.
    1. The count-per-file is arbitrary and depends on how big the combined file gets.  Try to keep it between 5-10MB in size.  Do no more than 1,000 invoices regardless.
    1. Identify the range present in the combined PDF in some manner.  Either explicit start and end invoices or, if using blocks of 100, `###XX.pdf` where the Xs are literal to indicate all values.  Experience shows that file size limitations of the source software come into play.  Combined with the manual nature of the process leads to using batch sizes of 500 under ideal circumstances.  Another naming scheme in this instance is `##XXX-MofN`.

The following may require manual segregation of invoices into separate directories such that each directory will become a single PDF.

```
cd {output directory from prior command}
PATH=%PATH%;"C:\Program Files (x86)\VeryPDF PDF Split-Merge v3.0"

# Manual directory specification
cmd /V /C "set "SEQ={sequence}" && pdfpg.exe ./!SEQ!/*.pdf ./!SEQ!xx.pdf"

# Suggest maximum resultant PDF size < ~30MB
cmd /V /C "pdfpg.exe ./final/*.pdf ./STORE-start-end-final.pdf"
cmd /V /C "pdfpg.exe ./other/*.pdf ./STORE-start-end-other.pdf"

# All Directories
REM for all subdirectories of the current directory
REM combine PDFs to a single PDF
FOR /D %%D IN (*) DO pdfpg.exe %%D\*.pdf .\%%Dxx.pdf

Note - only one % if used in command line mode
```

Replace **{sequence}** with the invoice prefix for the batch of 100.

Combines the individual PDFs within the user-specified subdirectory into a single PDF in the main directory while retaining booksmarks.  The default naming convention is to specify the prefix and place 2 x's to indicate all invoices sharing that prefix.

# Bulk Invoice Document Isolation

Identifies anonmalous invoices within a larger document and removes them.
AutoSoft document
```
(
(?>
(?:
(?-x)\s+Furey Chrysler Dodge Jeep(?x).*\r?\n
(?:^.*\r?\n)+?
)
(?:^\s+\d{4,5}\s+Job.+\r?\n)
)
(?<=^\s+\d{4}\s+Job.+\r?\n)
)

Replace

\r\n
```

Similar but for AutoSoft voided ROs

```
(
(?>
(?:
(?-x)\s+Furey Chrysler Dodge Jeep(?x).*\r?\n
(?:^.*\r?\n){3}
(?:
94661|
94729
)
(?=^.*Voided\sR/O)
(?:^.*\r?\n)+?
)
(?:^\s+\d{4,5}\s+Job.+\r?\n)
)
)
```
