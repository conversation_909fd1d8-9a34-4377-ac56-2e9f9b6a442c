#!/usr/bin/env bash

set -o nounset

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

[ $# -eq 0 ] && { usage; exit 1; }

while [ $# -gt 0 ]; do
    case "$1" in
        --invoice-dir)
            INVOICE_DIR="${2:?Directory Name Required}"
            shift
            ;;
        --output-dir)
            OUTPUT_DIR="${2:?Directory Name Required}"
            shift
            ;;
        * )
            usage
            die "Unrecognized option $1; exiting";
            ;;
    esac
    shift
done

function psql_work() {
    psql "service=${DU_ETL_PG_SERVICE}" --quiet --set=ON_ERROR_STOP=1 "$@"
}

[[ -d "$INVOICE_DIR" ]] || die "--invoice-dir must exist"
[[ -n "$OUTPUT_DIR"   ]] || die "--output-dir must be named"

clear_dir "$OUTPUT_DIR" || die "Failed to create output directory"

TMP_DIR=/tmp/du-etl-invm-create-enumeration-meta/
ACTUALS_FILE="$TMP_DIR"/actuals.txt
MISSING_DETAIL_FILE="$OUTPUT_DIR"/missing-ros.txt
MISSING_SUMMARY_FILE="$OUTPUT_DIR"/missing-summary.txt

MOST_RECENT_FILE="$OUTPUT_DIR"/most-recent-version.txt
ALTERNATES_FILE="$OUTPUT_DIR"/alternate-version.txt
CANONICAL_RO_FILE="$OUTPUT_DIR"/present-canonical-ros.txt

ALL_PDFS_FILE="$OUTPUT_DIR"/every-pdf.txt
MISSING_PDFS_FILE="$OUTPUT_DIR"/missing-pdf.txt

clear_dir "$TMP_DIR" || die "Could not create temporary working area"

find "$INVOICE_DIR" -maxdepth 1 -type f -print0 \
    | xargs -0 -I {} basename {} \; \
    | sort \
    > "$ACTUALS_FILE"

FIRST_FILE=$(head -n 1 $ACTUALS_FILE)
LAST_FILE=$(tail -n 1 $ACTUALS_FILE)

if [[ $FIRST_FILE =~ ^([[:digit:]]+) ]]; then
    FIRST_INV="${BASH_REMATCH[1]}"
else
    die "Unrecognized First Invoice Number: $FIRST_FILE"
fi
if [[ $LAST_FILE =~ ^([[:digit:]]+) ]]; then
    LAST_INV="${BASH_REMATCH[1]}"
else
    die "Unrecognized Last Invoice Number: $LAST_FILE"
fi

psql_work --quiet <<SQL
BEGIN;
    CREATE TEMP TABLE filenames (
        filename text PRIMARY KEY
    );

\copy filenames FROM '$ACTUALS_FILE'

-- Invoice Numeric Base, Invoice Number Suffix, Invoice Type (i/a), Version Spec
CREATE TEMP TABLE filename_parts AS
    SELECT *,
           ((regexp_matches(filename, '^(\d+)'))[1])::integer                        AS invnum,
           regexp_split_to_array(replace(filename, '.pdf', ''), '[-_]')              AS split_parts,
           array(SELECT unnest(m) FROM regexp_matches(filename, '[-_]', 'g') rm (m)) AS split_chars
      FROM filenames;

CREATE TEMP TABLE invoice_series AS
    SELECT invnum
      FROM generate_series('$FIRST_INV'::integer, '$LAST_INV'::integer) invseq (invnum);

CREATE TEMP TABLE missing_invnums AS
    SELECT invnum
      FROM invoice_series
     WHERE NOT EXISTS (SELECT 1
                         FROM filename_parts
                        WHERE filename_parts.invnum = invoice_series.invnum);

CREATE TEMP TABLE canonical_ros AS
     SELECT
            CASE WHEN array_length(split_parts, 1) = 1
                 THEN split_parts[1] || '-0'
                 WHEN array_length(split_parts, 1) = 2
                 THEN CASE WHEN split_parts[2] ~ '\d'
                           THEN split_parts[1] || '-' || split_parts[2]
                           ELSE split_parts[1] || split_parts[2] || '-0'
                       END
                 ELSE split_parts[1] || split_parts[2] || '-' || split_parts[3]
             END AS canonical_ro_number,
           *
      FROM filename_parts
      ORDER BY 1;

CREATE TEMP TABLE most_recent_canonical_ros AS
    SELECT DISTINCT ON (ro_num) ro_num, ro_version, filename

      FROM (SELECT split_part(canonical_ro_number, '-', 1) AS ro_num,
                   split_part(canonical_ro_number, '-', 2) AS ro_version,
                   *
              FROM canonical_ros
          ) canon_parts
   ORDER BY ro_num ASC, ro_version::integer ASC;

CREATE TEMP TABLE not_most_recent_files AS
    SELECT *
      FROM filenames fn
     WHERE NOT EXISTS (SELECT 1
                         FROM most_recent_canonical_ros mr
                        WHERE mr.filename = fn.filename);

\copy (SELECT invnum   FROM missing_invnums           ORDER BY invnum)   TO '$MISSING_DETAIL_FILE'
\copy (SELECT invnum || '.pdf' FROM missing_invnums   ORDER BY invnum)   TO '$MISSING_PDFS_FILE'
\copy (SELECT filename FROM most_recent_canonical_ros ORDER BY filename) TO '$MOST_RECENT_FILE'
\copy (SELECT filename FROM not_most_recent_files     ORDER BY filename) TO '$ALTERNATES_FILE'
\copy (SELECT ro_num   FROM most_recent_canonical_ros ORDER BY ro_num)   TO '$CANONICAL_RO_FILE'

ROLLBACK;
SQL

cat "$MOST_RECENT_FILE" "$MISSING_PDFS_FILE" | sort > "$ALL_PDFS_FILE"

cp "$ACTUALS_FILE" "$OUTPUT_DIR"

"$DU_ETL_HOME"/invoice-manipulation-common/summarize-ro-list \
    "$MISSING_DETAIL_FILE" \
    > "$MISSING_SUMMARY_FILE"

exit
