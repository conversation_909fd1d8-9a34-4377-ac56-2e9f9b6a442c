#!/usr/bin/env bash

set -o nounset

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

echo "Creating Enumeration Bundles"

[ $# -eq 0 ] && { usage; exit 1; }

START_RO=1
END_RO=999999999

while [ $# -gt 0 ]; do
    case "$1" in
        --source-dir)
            SOURCE_DIR="${2:?Directory Name Required}"
            shift
            ;;
        --start-at)
            START_RO="${2}"
            shift
            ;;
        --end-at)
            END_RO="${2}"
            shift
            ;;
        * )
            die "Unrecognized option $1; exiting";
            ;;
    esac
    shift
done

[[ -d "$SOURCE_DIR" ]] || die "Source directory must exist"

INVOICEMASTER_META_DIR="$SOURCE_DIR"/invoicemaster-meta/
ENUMERATION_META_DIR="$SOURCE_DIR"/enumeration-meta/
PLACEHOLDER_DIR="$SOURCE_DIR"/enumeration-placeholders/
BUNDLE_META="$SOURCE_DIR"/enumeration-bundle-meta/
INVOICE_OUT_DIR="$SOURCE_DIR"/enumerated-invoice-files/
TMP_WORK_DIR=/tmp/du-etl-invm-create-enum-bundles/
WORK_INV_STAGE="${TMP_WORK_DIR}"/invoice-staging/

clear_dir "$TMP_WORK_DIR"
clear_dir "$INVOICE_OUT_DIR"
clear_dir "$PLACEHOLDER_DIR"
clear_dir "$BUNDLE_META"

[[ -d "$INVOICEMASTER_META_DIR" ]] || die "Source directory must contain invoicemaster-meta"
[[ -d "$ENUMERATION_META_DIR" ]]   || die "Source directory must contain enumeration-meta"

function psql_work() {
    psql "service=local_du_etl" --set=ON_ERROR_STOP=1 "$@"
}
(
    cd "$SOURCE_DIR"
    psql_work <<SQL
        DROP SCHEMA IF EXISTS invoice_manipulation CASCADE;
        CREATE SCHEMA invoice_manipulation;
        SET search_path TO invoice_manipulation;

        CREATE UNLOGGED TABLE present_ros       ( invoicenumber text PRIMARY KEY );
        \copy present_ros FROM $ENUMERATION_META_DIR/present-canonical-ros.txt

        CREATE UNLOGGED TABLE missing_ros       ( invoicenumber text PRIMARY KEY );
        \copy missing_ros FROM $ENUMERATION_META_DIR/missing-ros.txt

        CREATE UNLOGGED TABLE open_ros          ( invoicenumber text PRIMARY KEY );
        \copy open_ros FROM $INVOICEMASTER_META_DIR/open-ro-list.txt

        CREATE UNLOGGED TABLE researched_ros    ( invoicenumber text PRIMARY KEY );
        \copy researched_ros FROM $INVOICEMASTER_META_DIR/research-list.txt

        CREATE UNLOGGED TABLE not_on_master_ros ( invoicenumber text PRIMARY KEY );
        \copy not_on_master_ros FROM $INVOICEMASTER_META_DIR/not_reported_ros.txt
        \copy not_on_master_ros FROM $INVOICEMASTER_META_DIR/not_closed_ros.txt

        CREATE UNLOGGED TABLE expected_ros      ( invoicenumber text PRIMARY KEY );
        \copy expected_ros FROM $INVOICEMASTER_META_DIR/expected_canonical_ros.txt

        CREATE UNLOGGED TABLE invoicemaster_final AS
            WITH master_list (invoicenumber, is_present) AS (
                SELECT *, true
                  FROM present_ros
             UNION ALL
                SELECT *, false
                  FROM missing_ros
            ), cross_link AS (
                SELECT *,
                      (EXISTS (SELECT 1
                                 FROM expected_ros er
                                WHERE er.invoicenumber = ml.invoicenumber))    AS is_expected,
                      (EXISTS (SELECT 1
                                 FROM open_ros opr
                                WHERE opr.invoicenumber = ml.invoicenumber))   AS on_open,
                      (EXISTS (SELECT 1
                                 FROM researched_ros rr
                                WHERE rr.invoicenumber = ml.invoicenumber))    AS researched
                 FROM master_list ml
            ), interpret AS (
                SELECT ((regexp_matches(invoicenumber, '^(\d+)'))[1])::int     AS invnum,
                       invoicenumber,
                       CASE WHEN is_present AND is_expected
                            THEN 'Present'
                            WHEN is_present AND NOT is_expected
                            THEN '3-Recently Closed'
                            WHEN NOT is_present AND is_expected
                            THEN '2-Missing'
                            WHEN NOT is_present AND NOT is_expected
                            THEN '4-Not Closed'
                            ELSE '1-Undefined'
                        END                                                    AS disposition,
                       CASE WHEN researched
                            THEN 'Supplement'
                            ELSE '' END                                        AS research,
                       CASE WHEN on_open
                            THEN 'Open'
                            ELSE ''
                        END                                                    AS open
                FROM cross_link
            )
            SELECT *
              FROM interpret;

\o '$ENUMERATION_META_DIR/invoicemaster-exceptions.txt'
    SELECT $START_RO AS starting_override,
           $END_RO   AS ending_override;

    SELECT disposition, count(*)
      FROM invoicemaster_final
     WHERE invnum BETWEEN $START_RO AND $END_RO
     GROUP BY disposition
     ORDER BY disposition;

    SELECT invoicenumber, substring(disposition, 3, 20), research, open
      FROM invoicemaster_final
     WHERE disposition NOT IN ('Present')
           AND invnum BETWEEN $START_RO AND $END_RO
  ORDER BY 2, 1;
\o

    CREATE UNLOGGED TABLE requires_research AS
        SELECT invoicenumber
          FROM invoicemaster_final
         WHERE (
                (disposition = '2-Missing')
                OR
                (disposition = '4-Not Closed' AND research = '' AND open = '')
               );

    \copy (SELECT * FROM requires_research ORDER BY invoicenumber) TO '$ENUMERATION_META_DIR/requires-research.txt'

SQL

)

    "$DU_ETL_HOME"/invoice-manipulation-common/summarize-ro-list \
        "$ENUMERATION_META_DIR"/requires-research.txt \
        | dos2unix \
        > "$ENUMERATION_META_DIR"/requires-research-summary.txt

    dos2unix "$ENUMERATION_META_DIR"/invoicemaster-exceptions.txt

CREATE_BOOKMARK='false'

head -n 20 "$ENUMERATION_META_DIR"/invoicemaster-exceptions.txt

for inv_num in $(
    psql_work -Atc "SELECT invoicenumber FROM invoice_manipulation.invoicemaster_final WHERE disposition IN ('2-Missing','4-Not Closed')"
); do
    {
        echo "RO# $inv_num Not Present - Please See Supplements for Details"
    } > "$PLACEHOLDER_DIR"/"$inv_num".txt
done

function copy_files_from_list() {

    function do_copy() {
        EXT_TARGET_DIR="${1}"
        INVOICE_FILE="${2}"
        INVOICE_NUM=$(basename "${2}" .pdf)
        clear_dir "${WORK_INV_STAGE}"

        if [[ ! -f ./Invoices/"$INVOICE_FILE" ]]; then
            CREATE_BOOKMARK='true'
            if [[ -e "${PLACEHOLDER_DIR}"/"${INVOICE_NUM}".txt ]]; then
                enscript -q -fCourier-Bold9 --no-header "${PLACEHOLDER_DIR}"/"${INVOICE_NUM}".txt --output=- \
                    | ps2pdf - \
                    > "${WORK_INV_STAGE}"/"${INVOICE_FILE}"
            else
                die "Both Placeholder and Source Documents for $INVOICE_NUM are missing!"
            fi
        else
            CREATE_BOOKMARK='false'
            cp "./Invoices/${INVOICE_FILE}" "${WORK_INV_STAGE}"/"${INVOICE_FILE}"
        fi

        if [[ "$CREATE_BOOKMARK" = 'true' ]]; then
            pdftk "${WORK_INV_STAGE}"/"${INVOICE_FILE}" dump_data output \
                  "${WORK_INV_STAGE}"/temp-pdf-metadata.txt

            printf 'BookmarkBegin\nBookmarkTitle: %s\nBookmarkLevel: 1\nBookmarkPageNumber: 1' "${INVOICE_NUM}" \
                 > "${WORK_INV_STAGE}"/temp-pdf-new-bookmark.txt

            cat "${WORK_INV_STAGE}"/temp-pdf-metadata.txt \
                "${WORK_INV_STAGE}"/temp-pdf-new-bookmark.txt \
                > "${WORK_INV_STAGE}"/temp-pdf-metadata-with-bookmark.txt

            pdftk "${WORK_INV_STAGE}"/"${INVOICE_FILE}" \
                  update_info "${WORK_INV_STAGE}"/temp-pdf-metadata-with-bookmark.txt \
                  output "${WORK_INV_STAGE}"/"${INVOICE_FILE}".pdfbm

            cp "${WORK_INV_STAGE}"/"${INVOICE_FILE}".pdfbm "${EXT_TARGET_DIR}"/"${INVOICE_FILE}"
        else
            mv "${WORK_INV_STAGE}"/"${INVOICE_FILE}" \
               "${EXT_TARGET_DIR}"/"${INVOICE_FILE}"
        fi
    }

    sourcefile="${1}"

    work_dir="${TMP_WORK_DIR}/${2}-invoices"
    work_dir_and_prefix="${work_dir}/${2}"
    work_batch_dir='/tmp' # dynamic, safe init value
    target_dir="$INVOICE_OUT_DIR"
    target_dir_and_prefix="${target_dir}/${2}"
    target_batch_dir='/tmp' # dynamic, safe init value
    meta_dir="$BUNDLE_META"
    batch_log="${meta_dir}"/"${2}"-batch-log.txt
    batch_script="${meta_dir}"/"${2}"-batch-script.txt
    batchnumber=0
    batchsize=100
    invcnt=1000   # make sure larger than batch size to force a first pass switch
    lastfileprefix='n/a' # dynamic, safe init value that won't match first RO#
    firstbatchronum='n/a'
    lastbatchronum='n/a'
    ro_century_old=0
    ro_century_cur=1

    if [[ -f "${batch_log}" ]]; then
        rm "${batch_log}"
    fi
    if [[ -f "${batch_script}" ]]; then
        rm "${batch_script}"
    fi
    touch "${batch_log}"
    touch "${batch_script}"

    for filename in $(cat "${sourcefile}"); do
        filenameprefix=$(perl -alne 'print $1 if /^(\d+[^._]*)/' <<<$filename)
        ro_century_cur=$(perl -alne 'print $1 if /^(\d+)\d{2}\D/' <<<$filename)
        if [[ "$lastfileprefix" = "$filenameprefix" ]]; then
            do_copy "${work_batch_dir}" "$filename"
        else
            if [[ "$ro_century_old" != "$ro_century_cur" ]]; then
                ro_century_old="${ro_century_cur}"
                if [[ "$batchnumber" != 0 ]]; then
                    lastbatchronum="${lastfileprefix}"
                    echo "${lastbatchronum}" >> "${batch_log}"
                    cat <<BAT >>"${batch_script}"
cmd /V /C "pdfpg.exe ./enumerated-invoice-files/$2-invoices/$2-${batchnumber}/*.pdf ./enumerated-invoice-files/${2}-${firstbatchronum}-${lastbatchronum}.pdf"
BAT
                fi
                invcnt=1
                ((batchnumber+= 1))
                firstbatchronum="${filenameprefix}"
                target_batch_dir="${target_dir_and_prefix}-${batchnumber}"
                work_batch_dir="${work_dir_and_prefix}-${batchnumber}"
                clear_dir "${work_batch_dir}"
                echo "Segregating batch # $batchnumber $2 ROs (Century $ro_century_cur)"
                echo -n "$2 Batch-${batchnumber} ${firstbatchronum} " >> "${batch_log}"
                do_copy "${work_batch_dir}" "$filename"
            else
                ((invcnt+= 1))
                do_copy "${work_batch_dir}" "$filename"
            fi
            lastfileprefix="$filenameprefix"
        fi
    done
    lastbatchronum="${lastfileprefix}"
    echo "${lastbatchronum}" >> "${batch_log}"
    cat <<BAT >>"${batch_script}"
cmd /V /C "pdfpg.exe ./enumerated-invoice-files/$2-invoices/$2-${batchnumber}/*.pdf ./enumerated-invoice-files/${2}-${firstbatchronum}-${lastbatchronum}.pdf"
BAT
    cat "$batch_log"
    cp -r "${TMP_WORK_DIR}"/*-invoices ./enumerated-invoice-files/
}

(
    cd "$SOURCE_DIR"
    copy_files_from_list \
        "$ENUMERATION_META_DIR"/every-pdf.txt \
        'every-pdf'

    head ./enumeration-bundle-meta/*

)

# VOID....Enumerate Invoices > Missing RO#s; Present RO#s;
# VOID....Invoice Master > Not Reported; Not Closed; Open ROs; Researched ROs; Expected RO#s;

# VOID....Expected EXCEPT Present [limit to non-numeric] => missing non-numeric (Missing-NN) invoices
# VOID....(Missing ROs INTERSECT Expected ROs) UNION Missing-NN => Missing-Expected-ROs
# VOID....Missing AND Not Closed => Missing-Open
# VOID....Missing AND Not Report => Missing-NotReported
#

# --summarize
# Create the following table by pulling in all of the various "*-meta" directory contents (excepting scenariokey)
# RO# | Group ID | PDF File (Present, Missing, Unexpected) | PDF Versions (0=missing,1,etc) | Invoice Master (Present,Missing,Open,Void) | Open RO (Yes/No) | Researched (Yes/No) | Placeholder Text
# NOTE: Omit qualified/not-qualified/cannot-qualify disposition
# The Group ID is the "prefix" in the <prefix>xx? form.  Bundles generated with this script will use 100 ROs regardless of how large they become.

# --build
# Every row in the summary table will either exist as a PDF document in Invoices or can have a make-shift document built for it.
# This means generating placeholder pages for [PDF File=Missing] documents
# --build implies sumarize as the decision to build is simply a option while constructing the summary
# build includes the processing of the "alternate-version.txt" file too

# Need to specify a "Placeholders" directory, similar in nature to the S4-Recap directory for UCS
# The text files in that directory will be converted to PDF as-is should the segregate-ros script fail
# to locate a PDF file in the Invoices directory.
# Failure to locate the placeholder document is either a failure or ignored depending on the caller
# so ened to add a flag dictating this behavior to segregate-ros; defaulting to ignore since the qual/non-qual bundles don't care

# Run invoicemaster-meta generator after uploading source text files to md-client
# Run create-enumeration-meta on Invoices
# The outputs of those two scripts should be all that is required to execute this script

# ********* Write, then run the --summarize routine and validate it
# ********* Modify segregate-ros to deal with missing documents by creating a placeholder PDF with the proper RO# and place it in the bundle directory; also parameterize bundle size
# Execute using the --build flag which will construct a master PDF file listing which is then sent to segregate-ros

# VOID....process enumeration-meta/sequence_result.txt
# VOID....Break into groups of 100 RO# bundles (shared prefixes) - each getting their own directory
# VOID....if a matching PDF is found place it into the bundle directory
# VOID....otherwise (invoicemaster_meta)
# VOID....if "not closed"
# VOID....elif "not reported"
# VOID....else <reported>
# VOID....fi
# VOID....Append status to the appropirate list
# VOID....# Create a dummy PDF file containing the RO# and a note to see supplement for RO disposition.

# Upon completion of each batch:
# Construct a DOS cmd command to execute to bundle the directory

# Much of this code already exists in "segregate-ros"
# Except:
# The group size should be parameterized
# The insertion of placeholder documents for missing RO#s
# Note the the bundling of the UCS Recap pages must remain - especially for parts
#     as the S4 source document contains the parts cost

# Probably this should simply call segregate-ros and specify sequence_result.txt
# Having segregate-ros know about and conditionally use invoicemaster_meta is OK.

# Within the segregate-ros meta directory also generate these two files on every pass
# <prefix>-missing-not-closed.txt
# <prefix>-missing-not-reported.txt
# Should probably place them within <prefix>-data as a sibling of meta
# which should renamed post-execution.
# Capture wc -l output to an overview file
# rename:  invoice-list-files => segregation-results
# the contents of <prefix>-data will ultimately be the various PDF files (once merged)
#     and the two <prefix>-missing-*.txt files

# The invoice master listing needs to generate a "non-numeric-ro-numbers.txt" file
# so that non-numeric missing ROs can be detected (numeric documents are handled due to range building)
# This script is the logical place to integrate those two sources of knowledge.

# Presently no placeholder documents will be generated for non-numeric RO#s.  Go with this unless and until
# a request (or the data suggests a need) for different behavior materializes.

# The reason this script is separate from create-filtered-invoice-lists is that this script
# does not consider any scenariokey-meta and thus only needs to be run once per store instead of
# once per scenario.  For this reason the target location of the files should not be "invoice-list-files"
# but rather something like "enumerated-invoice-files" though having the same internal structure.  segregate_ros
# will need a refactoring pass (AND NEW API probably) to make this work.
