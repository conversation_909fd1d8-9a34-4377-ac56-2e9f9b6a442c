#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

CONFIG_DIR="${DU_ETL_CONFIG_DIR_KenGarff:?Please export DU_ETL_CONFIG_DIR_KenGarff}"
function ensure_eti_dir_exists() {
    if [[ ! -d "${DU_ETL_ETI_DIR_KenGarff:?DU_ETL_ETI_DIR_KenGarff should be defined}" ]]; then
        die "Cannot import if $DU_ETL_ETI_DIR_KenGarff does not even exist"
    fi
}
clear

if [[ "$1" = 'test' ]]; then
    export ETI="$DU_ETL_ETI_DIR_Reynolds"="$DU_ETL_BASEDIR_KenGarff"/parse-derivatives
    mkdir -p "$DU_ETL_ETI_DIR_Reynolds"
    TW=~/tmp/etl-kengarff-testparse-workdir
    ensure_eti_dir_exists
    ETI="$DU_ETL_ETI_DIR_KenGarff"
    clear_dir "$TW"
    function du_transform() {
        "${DU_ETL_HOME}"/DU-Transform/du-parse.bash \
            --set-source "${ETI}" \
            --set-work "${TW}" \
            "$@"
        return $?
    }
    du_transform --dms "KenGarff"           --done   || die "Setting DMS failed"
    du_transform --select-file              --done   || die "Interactive File Selection Failed"
    du_transform --parse                    --done   || die "Parsing Failed"
    exit 0
fi

ensure_eti_dir_exists

if [[ "$1" = '--distribute-only' ]]; then
    custom_config_file=$(cat "$DU_ETL_BASEDIR_KenGarff"/last-config-file-name.txt)
    [[ -f "$custom_config_file" ]] \
        || die "Last config file $custom_config_file does not exist..."
else
    say "Please select a configuration file to import with."
    custom_config_file=$(select_file "$DU_ETL_CONFIG_DIR_KenGarff")
    [[ "$?" = 0 ]] || die "File selection failed"
fi

clear

"$DU_ETL_HOME"/DU-Solve360/present-config-to-user "$custom_config_file"
prompt_continuation "Proceed with importing the above data?"

if [[ -f "$custom_config_file" ]]; then
    ./main/run-template.bash --use-config "$custom_config_file" \
                             "$@" \
                             || die "Main Template Run Failed!"
fi
if [[ "$1" = '--distribute-only' ]]; then
    echo "Removing $custom_config_file"
    rm "$custom_config_file"
else
    echo "Saving config file name for subsequent --distribute-only call"
    echo "$custom_config_file" > "$DU_ETL_BASEDIR_KenGarff"/last-config-file-name.txt
fi
