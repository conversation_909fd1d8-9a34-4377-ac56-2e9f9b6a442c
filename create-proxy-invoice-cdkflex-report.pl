#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Time::Format qw(%time %strftime %manip);
use POSIX;

no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];
my $show_accounting_in_proxy = $ARGV[1];
my $is_porsche_store =  $ARGV[2];

# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;

my @header_section;
my @comment_section;
my @job_section;
my @ro_fee_section;
my @ro_disc_section;
my @grand_total_section;
my @ro_account_section;
my @end_of_invoice_section;
my @invoice_note_section;
my @paytype_filter;

my ($tot_cust_lbr_cost, $tot_cust_lbr_sale, $tot_cust_prt_cost, $tot_cust_prt_sale, $tot_cust_misc_cost, $tot_cust_misc_sale, $tot_cust_gog_cost, $tot_cust_gog_sale,
    $tot_cust_sublet_cost, $tot_cust_sublet_sale, $tot_cust_ded_cost, $tot_cust_ded_sale, $tot_job_cust_discount )= (0)x13;

my ($tot_intern_lbr_cost, $tot_intern_lbr_sale, $tot_intern_prt_cost, $tot_intern_prt_sale, $tot_intern_misc_cost, $tot_intern_misc_sale, $tot_intern_gog_cost,
    $tot_intern_gog_sale, $tot_intern_sublet_cost, $tot_intern_sublet_sale, $tot_intern_ded_cost, $tot_intern_ded_sale )= (0)x12;

my ($tot_warr_lbr_cost, $tot_warr_lbr_sale, $tot_warr_prt_cost, $tot_warr_prt_sale, $tot_warr_misc_cost, $tot_warr_misc_sale, $tot_warr_gog_cost, $tot_warr_gog_sale,
    $tot_warr_sublet_cost, $tot_warr_sublet_sale, $tot_warr_ded_cost, $tot_warr_ded_sale )= (0)x12;

my ($job_cust_lbr_cost, $job_cust_lbr_sale, $job_cust_prt_cost, $job_cust_prt_sale, $job_cust_misc_cost, $job_cust_misc_sale, $job_cust_gog_cost, $job_cust_gog_sale,
    $job_cust_sublet_cost, $job_cust_sublet_sale, $job_cust_ded_cost, $job_cust_ded_sale, $job_cust_discount )= (0)x13;

my ($job_intern_lbr_cost, $job_intern_lbr_sale, $job_intern_prt_cost, $job_intern_prt_sale, $job_intern_misc_cost, $job_intern_misc_sale, $job_intern_gog_cost,
    $job_intern_gog_sale, $job_intern_sublet_cost, $job_intern_sublet_sale, $job_intern_ded_cost, $job_intern_ded_sale )= (0)x12;

my ($job_warr_lbr_cost, $job_warr_lbr_sale, $job_warr_prt_cost, $job_warr_prt_sale, $job_warr_misc_cost, $job_warr_misc_sale, $job_warr_gog_cost, $job_warr_gog_sale,
    $job_warr_sublet_cost, $job_warr_sublet_sale, $job_warr_ded_cost, $job_warr_ded_sale )= (0)x12;

my ($job_billing_code, $billing_labor_type);
my $has_any_service_contract_job;

my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;
my ($cust_ro_fee, $warr_ro_fee, $intern_ro_fee) = (0)x3;
my $insurance_amount = 0;
my $cust_discount = 0;

my ($parts_entries, $misc_entries, $gog_entries, $sublet_entries, $ded_entries, $fee_entries, $disc_entries, $job_disc_entries) = (0)x7;
my $lbr_type;
my $page_max_height = 57; # Maximum no of lines in page body -- 73 (Maximum lines in PDF) - 14 (Header) - 2 (Footer)
my $curr_page_height = 0;
my $invoice_note_lines = 22; # No of lines needed for invoice note
my $pages;
my $curr_page_num;

my @service_contract_types = ();

my @employee_types = ();

# List  of pay-types which should be Warranty with label 'PRE-PAID MAINTENANCE'
my @pre_paid_maint_types = ();

# List  of pay-types which should be Warranty with label 'EXTENDED WARRANTY'
my @extended_warr_types = ();

# List of pay types which should be Warranty if any insurance exist for the RO
my @service_contract_with_insurance = ();
my $have_insurance_pay = 0;
my %dynamicPayTypes;
my %paytypeLabels;
my %resultObj;

my $ro_open_void_qry = $conn->prepare(
    "WITH inv_master AS (SELECT
                        substring(ro_number FROM '^[0-9]+')    AS number_part,
                        substring(ro_number FROM '[A-Za-z]+') AS code_part
                    FROM repair_order
       )
      ,series_gap (max_ser_ro, min_ser_ro) AS (
         SELECT * FROM (SELECT
                            number_part::integer,
                            lag(number_part::integer) OVER ranges,
                            number_part::integer - lag(number_part::integer) OVER ranges AS diff,
                            code_part
                    FROM inv_master
                    WINDOW ranges AS (order by number_part::integer))t
         WHERE diff <= 100)
     SELECT ro_number
     FROM repair_order
     WHERE completion_date IS NULL
     UNION ALL
     SELECT generate_series(min_ser_ro+1, max_ser_ro-1)::text
     FROM series_gap");

$ro_open_void_qry->execute();
while (my @open_ro_list = $ro_open_void_qry->fetchrow_array) {
    ($ro_number) = @open_ro_list;
    my $file_open = $ro_number . ".txt";
    unless(open FILE, '>'.$file_open) {
        die "\nUnable to create $file_open\n";
    }
    print_open_ro();
    close FILE;
}

my $ro_qry = $conn->prepare("SELECT ro_number
                             FROM repair_order
                             WHERE completion_date IS NOT NULL
                             ORDER BY ro_number");

$ro_qry->execute();
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number) = @ro_list;
    my $file = $ro_number . ".txt";
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;
    load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
    my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
    $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
    $ro_doc_insert->finish;
}

# Subroutine to prepare HEADER section of the invoice
sub make_header {
    my $ro_header_qry = $conn->prepare("SELECT
                                to_char(ro.creation_date, 'mm/dd/yyyy')   AS creation_date,
                                to_char(ro.completion_date, 'mm/dd/yyyy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                ro.advisor,
                                roc.customer_number,
                                roc.customer_name,
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip,
                                roc.customer_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                rov.vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in,
                                ro.comments
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
    my ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor, $customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $customer_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $comments ) = @header_row;
    #push (@header_section, sprintf("\n")); # TOP margin
    if($is_porsche_store eq 'true'){
        $vehicle_vin=substr($vehicle_vin, 0, 12);
        $customer_name = '';
        $customer_email = '';
        $customer_city = '';
        $customer_zip = '';
        $customer_state = '';
        $customer_number = '';
        $customer_address = '';
        $customer_phone = '';

    }
    push (@header_section, sprintf(border(">")." %-45s%-20s%-5s%-5s%10s%15s ".border("|")."\n",
           "Customer #: ".$customer_number,
           $advisor,
           " ",
           " ",
           $creation_date,
           $sub_branch. (defined ($sub_branch || $department) ? "+" : "") .$department.$ro_number));

    push (@header_section, sprintf(border(">")." %-100s ".border("|")."\n", $customer_name));

    push (@header_section, sprintf(border(">")." %-50s%-50s ".border("|")."\n",
           $customer_email, " "));
    push (@header_section, sprintf(border(">")." %-50s%-50s ".border("|")."\n",
           $customer_address, " "));
    push (@header_section, sprintf(border(">")." %-50s%-50s ".border("|")."\n",
           $customer_city.", ".$customer_state.", ".$customer_zip, " "));

    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));

    push (@header_section, sprintf(border(">")." %-30s%-45s%10s%-15s ".border("|")."\n", $customer_phone,
           $vehicle_year."/".$vehicle_make."/".$vehicle_model,
           $completion_date, "")); # YR/MAKE/MODEL/TRIM, DATE

    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line

    push (@header_section, sprintf(border(">")." %-30s%-30s%10s%-30s ".border("|")."\n",
           " ",
           $vehicle_vin, $mileage_in, "")); # VIN

    push (@header_section, sprintf(border(">")." %-35s%-15s%-15s%-12s%-23s ".border("|")."\n",
           " ",
           " ",
           " ",
           " ",
           $creation_date)); # Date

    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line

    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));

    # Comment Section
    $Text::Wrap::columns = 84;
    my $wrapped_comment = fill('', '', expand($comments));
    my @comment_list = split "\n", $wrapped_comment;
    if ($comments){
        push (@comment_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@comment_section, sprintf(border("#")." %4sCOMMENTS %s ".border("|")."\n", "", "-"x87));
        foreach my $i (0 .. $#comment_list) {
            push (@comment_section, sprintf(border(">")." %-6s%-84s%10s ".border("|")."\n", " ", $comment_list[$i], ""));
        }
    }
}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {
    my $job_qry = $conn->prepare("SELECT DISTINCT
                                    rl.ro_line,
                                    rl.complaint,
                                    rj.cause,
                                    rj.correction
                                FROM repair_line rl
                                    LEFT JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ?
                                ORDER BY rl.ro_line");

    $job_qry->execute($ro_number);
    my ( $ro_line, $complaint, $cause, $correction );
    while (my @job_row = $job_qry->fetchrow_array) {
        ( $ro_line, $complaint, $cause, $correction ) = @job_row;

        my @job_header_section;
        push (@job_header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@job_header_section, sprintf(border(">")." %4sJOB#%3s CHARGES%s ".border("|")."\n", "", $ro_line,        "-"x81));
        push (@job_section, [@job_header_section]);

        ($job_cust_lbr_cost, $job_cust_lbr_sale, $job_cust_prt_cost, $job_cust_prt_sale, $job_cust_misc_cost, $job_cust_misc_sale, $job_cust_gog_cost, $job_cust_gog_sale,
         $job_cust_sublet_cost, $job_cust_sublet_sale, $job_cust_ded_cost, $job_cust_ded_sale, $job_cust_discount )= (0)x13;

        ($job_intern_lbr_cost, $job_intern_lbr_sale, $job_intern_prt_cost, $job_intern_prt_sale, $job_intern_misc_cost, $job_intern_misc_sale, $job_intern_gog_cost,
         $job_intern_gog_sale, $job_intern_sublet_cost, $job_intern_sublet_sale, $job_intern_ded_cost, $job_intern_ded_sale )= (0)x12;

        ($job_warr_lbr_cost, $job_warr_lbr_sale, $job_warr_prt_cost, $job_warr_prt_sale, $job_warr_misc_cost, $job_warr_misc_sale, $job_warr_gog_cost, $job_warr_gog_sale,
         $job_warr_sublet_cost, $job_warr_sublet_sale, $job_warr_ded_cost, $job_warr_ded_sale )= (0)x12;

        my $lbr_qry = $conn->prepare("SELECT DISTINCT ON (rj.ro_job_line)
                                        rj.ro_job_line,
                                        left(rj.billing_code, 1) AS billing_code,
                                        rj.billing_code          AS billing_labor_type,
                                        rj.op_code,
                                        rj.op_description,
                                        rj.sold_hours,
                                        rj.actual_hours,
                                        rj.labor_cost,
                                        rj.sale_amount,
                                        COUNT(*) OVER (Partition BY rj.ro_number, rj.ro_line,
                                                 rj.ro_job_line) AS lbr_count
                                FROM repair_job rj
                                WHERE rj.ro_number = ? AND rj.ro_line = ?
                                ORDER BY rj.ro_job_line");
        $lbr_qry->execute($ro_number, $ro_line);
        my ( $ro_job_line, $op_code, $op_description, $sold_hours,
             $actual_hours, $labor_cost, $sale_amount, $lbr_count );
        while (my @lbr_row = $lbr_qry->fetchrow_array) {
            ( $ro_job_line, $job_billing_code, $billing_labor_type, $op_code, $op_description, $sold_hours,
              $actual_hours, $labor_cost, $sale_amount, $lbr_count ) = @lbr_row;

            my @job_labor_section;
            my $lbr_billing;

            push (@job_labor_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
            if ($lbr_count == 1)
            {
               if (grep $_ eq lc($billing_labor_type), @service_contract_types){
                    $lbr_type = "WARRANTY";
                    $has_any_service_contract_job = 1;
                    $lbr_billing = "SERVICE CONTRACT". (defined($billing_labor_type) ? " (" .$billing_labor_type. ")".("-"x(24-length($billing_labor_type))) : "-"x27);
                    $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                    $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                } elsif (grep $_ eq lc($billing_labor_type), @employee_types){
                    $lbr_type = "CUSTOMER";
                    $lbr_billing = "EMPLOYEE". (defined($billing_labor_type) ? " (" .$billing_labor_type. ")".("-"x(32-length($billing_labor_type))) : "-"x35);
                    $job_cust_lbr_sale = $job_cust_lbr_sale + $sale_amount;
                    $job_cust_lbr_cost = $job_cust_lbr_cost + $labor_cost;
                } elsif ((grep $_ eq lc($billing_labor_type), @service_contract_with_insurance) &&
                         $have_insurance_pay) {
                    $lbr_type = "WARRANTY";
                    $has_any_service_contract_job = 1;
                    $lbr_billing = "SERVICE CONTRACT". (defined($billing_labor_type) ? " (" .$billing_labor_type. ")".("-"x(24-length($billing_labor_type))) : "-"x27);
                    $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                    $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                } elsif (grep $_ eq lc($billing_labor_type), @pre_paid_maint_types){
                    $lbr_type = "WARRANTY";
                    $has_any_service_contract_job = 1;
                    $lbr_billing = "PRE-PAID MAINTENANCE". (defined($billing_labor_type) ? " (" .$billing_labor_type. ")".("-"x(20-length($billing_labor_type))) : "-"x23);
                    $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                    $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                } elsif (grep $_ eq lc($billing_labor_type), @extended_warr_types){
                    $lbr_type = "WARRANTY";
                    $has_any_service_contract_job = 1;
                    $lbr_billing = "EXTENDED WARRANTY". (defined($billing_labor_type) ? " (" .$billing_labor_type. ")".("-"x(23-length($billing_labor_type))) : "-"x26);
                    $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                    $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                } elsif (lc($job_billing_code) eq 'i' || lc($job_billing_code) eq 'c' || lc($job_billing_code) eq 'w') {
                    %dynamicPayTypes = swap_paytype_dynamically($job_billing_code,$billing_labor_type);
                    $has_any_service_contract_job = $dynamicPayTypes{'serviceContract'};
                    if (lc($dynamicPayTypes{'billingType'}) eq 'i'){
                        $lbr_type = "INTERNAL";
                        $lbr_billing = uc($dynamicPayTypes{'label'}). (defined($billing_labor_type) ? " (" .$dynamicPayTypes{'paytype'}. ")".("-"x((40-$dynamicPayTypes{'labelLength'})-length($billing_labor_type))) : "-"x(43-$dynamicPayTypes{'labelLength'}));
                        $job_intern_lbr_sale = $job_intern_lbr_sale + $sale_amount;
                        $job_intern_lbr_cost = $job_intern_lbr_cost + $labor_cost;
                    } 
                    elsif (lc($dynamicPayTypes{'billingType'}) eq 'c'){
                        $lbr_type = "CUSTOMER";
                        $lbr_billing = uc($dynamicPayTypes{'label'}). (defined($billing_labor_type) ? " (" .$dynamicPayTypes{'paytype'}. ")".("-"x((40-$dynamicPayTypes{'labelLength'})-length($billing_labor_type))) : "-"x(43-$dynamicPayTypes{'labelLength'}));
                        $job_cust_lbr_sale = $job_cust_lbr_sale + $sale_amount;
                        $job_cust_lbr_cost = $job_cust_lbr_cost + $labor_cost;
                    } elsif (lc($dynamicPayTypes{'billingType'}) eq 'w'){
                        $lbr_type = "WARRANTY";
                        $lbr_billing = uc($dynamicPayTypes{'label'}). (defined($billing_labor_type) ? " (" .$dynamicPayTypes{'paytype'}. ")".("-"x((40-$dynamicPayTypes{'labelLength'})-length($billing_labor_type))) : "-"x(43-$dynamicPayTypes{'labelLength'}));
                        $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                        $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                    }
                }
                push (@job_labor_section, sprintf(border(">")." %4sLABOR----%-43s%-44s ".border("|")."\n",  "", $lbr_billing, "-"x39));

                # Job, OpCode , Detail
                $Text::Wrap::columns = 45;
                my $wrapped_op_description = fill('', '', expand($op_description));
                my @op_description_list = split "\n", $wrapped_op_description;

                push (@job_labor_section, sprintf(border(">")."     J# %-3s %-18s %-45s %6.2f  %7.2f  %7.2f ".border("|")."\n",$ro_line, $op_code,
                                                    $op_description_list[0], $sold_hours, $labor_cost, $sale_amount));
                foreach my $i (1 .. $#op_description_list) {
                    push (@job_labor_section, sprintf(border(">")." %30s%-45s%25s ".border("|")."\n", " ", $op_description_list[$i], " "));
                }
            }else{
                push (@job_labor_section, sprintf(border(">")." %4sLABOR----%-87s ".border("|")."\n",  "", "-"x50));
                # Job, OpCode , Detail
                $Text::Wrap::columns = 65;
                my $wrapped_op_description = fill('', '', expand($op_description));
                my @op_description_list = split "\n", $wrapped_op_description;

                push (@job_labor_section, sprintf(border(">")."     J# %-3s %-18s %-65s %4s ".border("|")."\n",$ro_line, $op_code,
                                                    $op_description_list[0], ""));
                foreach my $i (1 .. $#op_description_list) {
                    push (@job_labor_section, sprintf(border(">")." %30s%-65s%5s ".border("|")."\n", " ", $op_description_list[$i], ""));
                }
                my $lbr_detail_qry = $conn->prepare("SELECT
                                                        left(rj.billing_code, 1) AS billing_code,
                                                        rj.billing_code          AS billing_labor_type,
                                                        rj.sold_hours,
                                                        rj.actual_hours,
                                                        rj.labor_cost,
                                                        rj.sale_amount
                                                    FROM repair_job rj
                                                    WHERE rj.ro_number = ? AND rj.ro_line = ? AND rj.ro_job_line = ?
                                                    ORDER BY rj.ro_job_line");
                $lbr_detail_qry->execute($ro_number, $ro_line, $ro_job_line);
                while ( my @lbr_detail_row = $lbr_detail_qry->fetchrow_array){
                    ( $job_billing_code, $billing_labor_type, $sold_hours,
                    $actual_hours, $labor_cost, $sale_amount ) = @lbr_detail_row;
                    push (@job_labor_section, sprintf(border(">")." %30s%-45s %6.2f  %7.2f  %7.2f ".border("|")."\n",
                                                    "", $billing_labor_type, $sold_hours, $labor_cost, $sale_amount));
                    if ((grep $_ eq lc($billing_labor_type), @service_contract_types) ||
                             (grep $_ eq lc($billing_labor_type), @pre_paid_maint_types) ||
                             (grep $_ eq lc($billing_labor_type), @extended_warr_types) ||
                             ((grep $_ eq lc($billing_labor_type), @service_contract_with_insurance) && $have_insurance_pay)){
                        $has_any_service_contract_job = 1;
                        $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                        $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                    } elsif (lc($job_billing_code) eq 'i' || lc($job_billing_code) eq 'c' || lc($job_billing_code) eq 'w') {
                        %dynamicPayTypes = swap_paytype_dynamically($job_billing_code,$billing_labor_type);
                        $has_any_service_contract_job = $dynamicPayTypes{'serviceContract'};
                        if (lc($dynamicPayTypes{'billingType'}) eq 'i'){
                            $job_intern_lbr_sale = $job_intern_lbr_sale + $sale_amount;
                            $job_intern_lbr_cost = $job_intern_lbr_cost + $labor_cost;
                        } 
                        elsif (lc($dynamicPayTypes{'billingType'}) eq 'c'){
                            $job_cust_lbr_sale = $job_cust_lbr_sale + $sale_amount;
                            $job_cust_lbr_cost = $job_cust_lbr_cost + $labor_cost;
                        } elsif (lc($dynamicPayTypes{'billingType'}) eq 'w'){
                            $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                            $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                        }
                    }
                }
            }

            my $techs_qry = "SELECT
                                string_agg(DISTINCT tech_id, ', '),
                                sum(actual_hours),
                                sum(booked_hours)
                            FROM repair_job_technician_detail td
                            WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?";

            my ($job_techs, $total_act_time, $total_booked_time) = $conn->selectrow_array($techs_qry, undef, ($ro_number, $ro_line, $ro_job_line));

            push (@job_labor_section, sprintf(border(">")."     TECH(S): %-87s ".border("|")."\n", $job_techs));

            push (@job_section, [@job_labor_section]);

            my @tech_section = make_tech_section($ro_line, $ro_job_line, $total_act_time, $total_booked_time);
            push (@job_section, [@tech_section]);

            my @parts_section = make_parts_section($ro_line, $ro_job_line);
            push (@job_section, [@parts_section]);
        }

        # Job Complaint
        my @job_complaint_section;
        $Text::Wrap::columns = 80;
        my $wrapped_complaint = fill('', '', expand($complaint));
        my @complaint_list = split "\n", $wrapped_complaint;
        push (@job_complaint_section, sprintf(border(">")." %4s%-13s%-83s ".border("|")."\n", "", "COMPLAINT", $complaint_list[0]));
        foreach my $i (1 .. $#complaint_list) {
            push (@job_complaint_section, sprintf(border(">")." %17s%-83s ".border("|")."\n", "", $complaint_list[$i]));
        }

        push (@job_section, [@job_complaint_section]);

        # Job Cause
        my @job_cause_section;
        my $wrapped_cause = fill('', '', expand($cause));
        my @cause_list = split "\n", $wrapped_cause;
        push (@job_cause_section, sprintf(border(">")." %4s%-13s%-83s ".border("|")."\n", "", "CAUSE", $cause_list[0]));
        foreach my $i (1 .. $#cause_list) {
            push (@job_cause_section, sprintf(border(">")." %17s%-83s ".border("|")."\n", "", $cause_list[$i]));
        }

        push (@job_section, [@job_cause_section]);

        # Job Correction
        my @job_correction_section;
        my $wrapped_correction = fill('', '', expand($correction));
        my @correction_list = split "\n", $wrapped_correction;
        push (@job_correction_section, sprintf(border(">")." %4s%-13s%-83s ".border("|")."\n", "", "CORRECTION", $correction_list[0]));
        foreach my $i (1 .. $#correction_list) {
            push (@job_correction_section, sprintf(border(">")." %17s%-83s ".border("|")."\n", "", $correction_list[$i]));
        }

        push (@job_section, [@job_correction_section]);

        my @misc_section = make_misc_section($ro_line);
        push (@job_section, [@misc_section]);

        my @gog_section = make_gog_section($ro_line);
        push (@job_section, [@gog_section]);

        my @sublet_section = make_sublet_section($ro_line);
        push (@job_section, [@sublet_section]);

        my @ded_section = make_deductible_section($ro_line);
        push (@job_section, [@ded_section]);

        my @disocunt_section = make_ro_job_disc_section($ro_line);
        push (@job_section, [@disocunt_section]);

        my @job_total_section = make_job_total_section($ro_line);
        push (@job_section, [@job_total_section]);

    }
}

# Subroutine to prepare the TECH section of a JOB
sub make_tech_section{
    my ($ro_line, $ro_job_line, $total_act_time, $total_booked_time) = @_;
    my @tech_section_array;
    my $tech_details_qry = $conn->prepare("SELECT
                                                ro_job_tech_line,
                                                tech_id,
                                                work_date,
                                                work_start_time,
                                                work_end_time,
                                                work_note,
                                                actual_hours,
                                                booked_hours
                                            FROM repair_job_technician_detail td
                                            WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
    $tech_details_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours);
    if($tech_details_qry->rows > 0){
        push (@tech_section_array, sprintf(border("#")." %-100s ".border("|")."\n",
                                            "               TECH#      DATE      START   FINISH      ACT     TIME    DESCRIPTION"));

        while (my @tech_row = $tech_details_qry->fetchrow_array) {
            ($ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours) = @tech_row;

            push (@tech_section_array, sprintf(border(">")."           %10s   %-10s   %5s   %6s   %6.2f   %6.2f   %29s ".border("|")."\n",
                                                $tech_id, $work_date, $work_start_time, $work_end_time, $actual_hours, $booked_hours, $work_note));
        }
        push (@tech_section_array, sprintf(border(">")." %35s%-18s%6.2f   %6.2f %31s ".border("|")."\n", "","TOTAL TECH TIME",
                                            $total_act_time, $total_booked_time, ""));
        push (@tech_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
    }
    return @tech_section_array;
}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line) = @_;
    my @parts_section_array;

    push (@parts_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
    my $parts_qry = $conn->prepare("SELECT DISTINCT ON (ro_part_line)
                                        ro_part_line,
                                        part_number,
                                        part_description,
                                        left(nullif(prt_billing_type, 'NA'), 1)   AS prt_billing_code,
                                        nullif(prt_billing_type, 'NA'),
                                        quantity_sold,
                                        unit_cost,
                                        unit_sale,
                                        unit_core_cost,
                                        unit_core_sale,
                                        COUNT(*) OVER (Partition BY rp.ro_number, rp.ro_line,
                                                 rp.ro_job_line, rp.ro_part_line) AS prt_count
                                    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ? AND quantity_sold != 0
                                    ORDER BY ro_part_line");
    $parts_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold, $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale, $prt_count);

    $parts_entries = $parts_qry->rows;
    if($parts_entries > 0){
        push (@parts_section_array, sprintf(border("#")." %4s PARTS--QTY--FP--NUMBER%s--DESCRIPTION%s--%6s---%6s---%6s--%-7s ".border("|")."\n",
                                            "", "-"x12, "-"x13, "U/COST", "E/COST", "U/SALE", "-"x7));
        while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold, $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale, $prt_count) = @parts_row;

            $prt_billing_code = ($prt_billing_code?$prt_billing_code:$job_billing_code);
            $prt_billing_type = ($prt_billing_type?$prt_billing_type:$billing_labor_type);
            $Text::Wrap::columns = 24;
            my $wrapped_part_description = fill('', '', expand($part_description));
            my @part_description_list = split "\n", $wrapped_part_description;
            if($prt_count == 1){
                push (@parts_section_array, sprintf(border(">")." %-10s  %3s  %2s  %-18s  %-24s %7.2f  %7.2f  %7.2f  %7.2f ".border("|")."\n",
                                                    "", $quantity_sold, "", $part_number, $part_description_list[0], $unit_cost,
                                                    $unit_cost*$quantity_sold, $unit_sale, $unit_sale*$quantity_sold ));
                foreach my $i (1 .. $#part_description_list) {
                    push (@parts_section_array, sprintf(border(">")." %41s%-24s%-35s ".border("|")."\n", "", $part_description_list[$i], ""));
                }
                if ($unit_core_cost != 0 && $unit_core_sale != 0){
                    push (@parts_section_array, sprintf(border(">")." %-39s  %-24s %7.2f  %7.2f  %7.2f  %7.2f ".border("|")."\n",
                                                    "", "CORE CHARGE", $unit_core_cost, $unit_core_cost*$quantity_sold, $unit_core_sale,
                                                    $unit_core_sale*$quantity_sold ));
                }
                if ((grep $_ eq lc($prt_billing_type), @service_contract_types) ||
                         (grep $_ eq lc($prt_billing_type), @pre_paid_maint_types) ||
                         (grep $_ eq lc($prt_billing_type), @extended_warr_types) ||
                         ((grep $_ eq lc($prt_billing_type), @service_contract_with_insurance) && $have_insurance_pay)){
                    $has_any_service_contract_job = 1;
                    $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                    $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                } elsif (lc($prt_billing_code) eq 'i' || lc($prt_billing_code) eq 'c' || lc($prt_billing_code) eq 'w') {
                    %dynamicPayTypes = swap_paytype_dynamically($prt_billing_code,$prt_billing_type);
                    $has_any_service_contract_job = $dynamicPayTypes{'serviceContract'};
                    if (lc($dynamicPayTypes{'billingType'}) eq 'i'){
                        $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                        $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                    } 
                    elsif (lc($dynamicPayTypes{'billingType'}) eq 'c'){
                            $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                            $job_cust_prt_cost = $job_cust_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                    } elsif (lc($dynamicPayTypes{'billingType'}) eq 'w'){
                        $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                        $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                    }
                }
            }else{
                push (@parts_section_array, sprintf(border(">")." %-10s  %3s  %2s  %-18s  %-24s %34s ".border("|")."\n",
                                                    "", $quantity_sold, "", $part_number, $part_description_list[0], "" ));
                foreach my $i (1 .. $#part_description_list) {
                    push (@parts_section_array, sprintf(border(">")." %41s%-24s%-35s ".border("|")."\n", "", $part_description_list[$i], ""));
                }
                my $prt_detail_qry = $conn->prepare("SELECT
                                                        left(prt_billing_type, 1) AS prt_billing_code,
                                                        prt_billing_type,
                                                        quantity_sold,
                                                        unit_cost,
                                                        unit_sale,
                                                        unit_core_cost,
                                                        unit_core_sale
                                                    FROM repair_part rp
                                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ? AND quantity_sold != 0
                                                          AND ro_part_line = ?
                                                    ORDER BY ro_part_line");
                $prt_detail_qry->execute($ro_number, $ro_line, $ro_job_line, $ro_part_line);
                while ( my @prt_detail_row = $prt_detail_qry->fetchrow_array){
                    ( $prt_billing_code, $prt_billing_type, $quantity_sold, $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale) = @prt_detail_row;
                    push (@parts_section_array, sprintf(border(">")." %-39s  %-24s %7.2f  %7.2f  %7.2f  %7.2f ".border("|")."\n",
                                                    "", $prt_billing_type, $unit_cost, $unit_cost*$quantity_sold,
                                                    $unit_sale, $unit_sale*$quantity_sold ));
                    if ($unit_core_cost != 0 || $unit_core_sale != 0){
                        push (@parts_section_array, sprintf(border(">")." %-39s  %-24s %7.2f  %7.2f  %7.2f  %7.2f ".border("|")."\n",
                                                        "", "CORE CHARGE", $unit_core_cost, $unit_core_cost*$quantity_sold, $unit_core_sale,
                                                        $unit_core_sale*$quantity_sold ));
                    }
                    if ((grep $_ eq lc($prt_billing_type), @service_contract_types) ||
                             (grep $_ eq lc($prt_billing_type), @pre_paid_maint_types) ||
                             (grep $_ eq lc($prt_billing_type), @extended_warr_types) ||
                             ((grep $_ eq lc($prt_billing_type), @service_contract_with_insurance) && $have_insurance_pay)) {
                        $has_any_service_contract_job = 1;
                        $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                        $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                    } elsif (lc($prt_billing_code) eq 'i' || lc($prt_billing_code) eq 'c' || lc($prt_billing_code) eq 'w') {
                        %dynamicPayTypes = swap_paytype_dynamically($prt_billing_code,$prt_billing_type);
                        $has_any_service_contract_job = $dynamicPayTypes{'serviceContract'};
                        if (lc($dynamicPayTypes{'billingType'}) eq 'i'){
                            $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                            $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                        } elsif (lc($dynamicPayTypes{'billingType'}) eq 'c'){
                            $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                            $job_cust_prt_cost = $job_cust_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                        } elsif (lc($dynamicPayTypes{'billingType'}) eq 'w'){
                            $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale*$quantity_sold) + ($unit_core_sale*$quantity_sold);
                            $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost*$quantity_sold) + ($unit_core_cost*$quantity_sold);
                        }
                    }
                }
            }
        }
    }
    return @parts_section_array;
}

# Subroutine to prepare the MISC section of a JOB
sub make_misc_section{
    my ($ro_line) = @_;
    my @misc_section_array;
    my $misc_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       left(other_billing_type, 1) AS other_billing_code,
                                       other_billing_type,
                                       other_cost,
                                       other_sale
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line =? AND item_type = 'MISC'");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $misc_code, $misc_description, $other_billing_code, $other_billing_type, $misc_cost, $misc_sale);
    $misc_entries = $misc_qry->rows;
    if($misc_entries > 0){
        push (@misc_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@misc_section_array, sprintf(border("#")." %4s MISC%sCODE%sDESCRIPTION%sCOST%sSALE%s ".border("|")."\n",
                                            "", "-"x3, "-"x9, "-"x44, "-"x7, "-"x5));

        while (my @misc_row = $misc_qry->fetchrow_array) {
            ( $misc_code, $misc_description, $other_billing_code, $other_billing_type, $misc_cost, $misc_sale) = @misc_row;
            $Text::Wrap::columns = 48;
            my $wrapped_misc_description = fill('', '', expand($misc_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;
            push (@misc_section_array, sprintf(border(">")." %-12s% -10s   %-48s   %8.2f   %8.2f%5s ".border("|")."\n",
                                                "", $misc_code, $misc_description_list[0], $misc_cost, $misc_sale,"" ));
            foreach my $i (1 .. $#misc_description_list) {
                push (@misc_section_array, sprintf(border(">")." %25s%-48s%-27s ".border("|")."\n", "", $misc_description_list[$i], ""));
            }
            $other_billing_code = ($other_billing_code?$other_billing_code:$job_billing_code);
            $other_billing_type = ($other_billing_type?$other_billing_type:$billing_labor_type);
            if ((grep $_ eq lc($other_billing_type), @service_contract_types) ||
                     (grep $_ eq lc($other_billing_type), @pre_paid_maint_types) ||
                     (grep $_ eq lc($other_billing_type), @extended_warr_types) ||
                     ((grep $_ eq lc($other_billing_type), @service_contract_with_insurance) && $have_insurance_pay)) {
                $has_any_service_contract_job = 1;
                $job_warr_misc_sale = $job_warr_misc_sale + $misc_sale;
                $job_warr_misc_cost = $job_warr_misc_cost + $misc_cost;
            } elsif (lc($other_billing_code) eq 'i' || lc($other_billing_code) eq 'c' || lc($other_billing_code) eq 'w') {
                %dynamicPayTypes = swap_paytype_dynamically($other_billing_code,$other_billing_type);
                $has_any_service_contract_job = $dynamicPayTypes{'serviceContract'};
                if (lc($dynamicPayTypes{'billingType'}) eq 'i'){
                    $job_intern_misc_sale = $job_intern_misc_sale + $misc_sale;
                    $job_intern_misc_cost = $job_intern_misc_cost + $misc_cost;
                } elsif (lc($dynamicPayTypes{'billingType'}) eq 'c'){
                    $job_cust_misc_sale = $job_cust_misc_sale + $misc_sale;
                    $job_cust_misc_cost = $job_cust_misc_cost + $misc_cost;
                } elsif (lc($dynamicPayTypes{'billingType'}) eq 'w'){
                    $job_warr_misc_sale = $job_warr_misc_sale + $misc_sale;
                    $job_warr_misc_cost = $job_warr_misc_cost + $misc_cost;
                }
            }
        }
    }
    return @misc_section_array;
}

# Subroutine to prepare the GOG section of a JOB
sub make_gog_section{
    my ($ro_line) = @_;
    my @gog_section_array;
    my $gog_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    left(other_billing_type, 1) AS other_billing_code,
                                    other_billing_type,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'GOG'");
    $gog_qry->execute($ro_number, $ro_line);
    my ( $gog_code, $gog_description, $other_billing_code, $other_billing_type, $gog_cost, $gog_sale);
    $gog_entries = $gog_qry->rows;
    if($gog_qry->rows > 0){
        push (@gog_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@gog_section_array, sprintf(border("#")." %4s GOG%sCODE%sDESCRIPTION%sCOST%sSALE%s ".border("|")."\n",
                                            "", "-"x4, "-"x9, "-"x44, "-"x7, "-"x5));

        while (my @gog_row = $gog_qry->fetchrow_array) {
            ( $gog_code, $gog_description, $other_billing_code, $other_billing_type, $gog_cost, $gog_sale) = @gog_row;
            $Text::Wrap::columns = 48;
            my $wrapped_gog_description = fill('', '', expand($gog_description));
            my @gog_description_list = split "\n", $wrapped_gog_description;
            push (@gog_section_array, sprintf(border(">")." %-12s% -10s   %-48s   %8.2f   %8.2f%5s ".border("|")."\n","",
                                                $gog_code, $gog_description_list[0], $gog_cost, $gog_sale,"" ));
            foreach my $i (1 .. $#gog_description_list) {
                push (@gog_section_array, sprintf(border(">")." %25s%-48s%-27s ".border("|")."\n", "", $gog_description_list[$i], ""));
            }
            $other_billing_code = ($other_billing_code?$other_billing_code:$job_billing_code);
            $other_billing_type = ($other_billing_type?$other_billing_type:$billing_labor_type);
            if ((grep $_ eq lc($other_billing_type), @service_contract_types) ||
                     (grep $_ eq lc($other_billing_type), @pre_paid_maint_types) ||
                     (grep $_ eq lc($other_billing_type), @extended_warr_types) ||
                     ((grep $_ eq lc($other_billing_type), @service_contract_with_insurance) && $have_insurance_pay)) {
                $has_any_service_contract_job = 1;
                $job_warr_gog_sale = $job_warr_gog_sale + $gog_sale;
                $job_warr_gog_cost = $job_warr_gog_cost + $gog_cost;
            } elsif (lc($other_billing_code) eq 'i' || lc($other_billing_code) eq 'c' || lc($other_billing_code) eq 'w') {
                %dynamicPayTypes = swap_paytype_dynamically($other_billing_code,$other_billing_type);
                $has_any_service_contract_job = $dynamicPayTypes{'serviceContract'};
                if (lc($dynamicPayTypes{'billingType'}) eq 'i'){
                    $job_intern_gog_sale = $job_intern_gog_sale + $gog_sale;
                    $job_intern_gog_cost = $job_intern_gog_cost + $gog_cost;
                } elsif (lc($dynamicPayTypes{'billingType'}) eq 'c'){
                    $job_cust_gog_sale = $job_cust_gog_sale + $gog_sale;
                    $job_cust_gog_cost = $job_cust_gog_cost + $gog_cost;
                } elsif (lc($dynamicPayTypes{'billingType'}) eq 'w'){
                    $job_warr_gog_sale = $job_warr_gog_sale + $gog_sale;
                    $job_warr_gog_cost = $job_warr_gog_cost + $gog_cost;
                }
            }
        }
    }
    return @gog_section_array;
}

# Subroutine to prepare the SUBLET section of a JOB
sub make_sublet_section{
    my ($ro_line) = @_;
    my @sublet_section_array;
    my $sublet_qry = $conn->prepare("SELECT
                                        other_code,
                                        other_description,
                                        left(other_billing_type, 1) AS other_billing_code,
                                        other_billing_type,
                                        other_cost,
                                        other_sale
                                    FROM repair_other
                                    WHERE ro_number = ? AND ro_line =? AND item_type = 'SUBLET'");
    $sublet_qry->execute($ro_number, $ro_line);
    my ( $sublet_code, $sublet_description, $other_billing_code, $other_billing_type, $sublet_cost, $sublet_sale);
    $sublet_entries = $sublet_qry->rows;
    if($sublet_entries > 0){
        push (@sublet_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@sublet_section_array, sprintf(border("#")." %4s SUBLET%sCODE%sDESCRIPTION%sCOST%sSALE%s ".border("|")."\n",
                                            "", "-"x3, "-"x9, "-"x42, "-"x7, "-"x5));

        while (my @sublet_row = $sublet_qry->fetchrow_array) {
            ( $sublet_code, $sublet_description, $other_billing_code, $other_billing_type, $sublet_cost, $sublet_sale) = @sublet_row;
            $Text::Wrap::columns = 46;
            my $wrapped_sublet_description = fill('', '', expand($sublet_description));
            my @sublet_description_list = split "\n", $wrapped_sublet_description;
            push (@sublet_section_array, sprintf(border(">")." %-14s% -10s   %-46s   %8.2f   %8.2f%5s ".border("|")."\n",
                                                "", $sublet_code, $sublet_description_list[0], $sublet_cost, $sublet_sale, "" ));
            foreach my $i (1 .. $#sublet_description_list) {
                push (@sublet_section_array, sprintf(border(">")." %27s%-46s%-27s ".border("|")."\n", "", $sublet_description_list[$i], ""));
            }
            $other_billing_code = ($other_billing_code?$other_billing_code:$job_billing_code);
            $other_billing_type = ($other_billing_type?$other_billing_type:$billing_labor_type);
            if ((grep $_ eq lc($other_billing_type), @service_contract_types) ||
                     (grep $_ eq lc($other_billing_type), @pre_paid_maint_types) ||
                     (grep $_ eq lc($other_billing_type), @extended_warr_types) ||
                     ((grep $_ eq lc($other_billing_type), @service_contract_with_insurance) && $have_insurance_pay)) {
                $has_any_service_contract_job = 1;
                $job_warr_sublet_sale = $job_warr_sublet_sale + $sublet_sale;
                $job_warr_sublet_cost = $job_warr_sublet_cost + $sublet_cost;
            } elsif (lc($other_billing_code) eq 'i' || lc($other_billing_code) eq 'c' || lc($other_billing_code) eq 'w') {
                %dynamicPayTypes = swap_paytype_dynamically($other_billing_code,$other_billing_type);
                $has_any_service_contract_job = $dynamicPayTypes{'serviceContract'};
                if (lc($dynamicPayTypes{'billingType'}) eq 'i'){
                    $job_intern_sublet_sale = $job_intern_sublet_sale + $sublet_sale;
                    $job_intern_sublet_cost = $job_intern_sublet_cost + $sublet_cost;
                } elsif (lc($dynamicPayTypes{'billingType'}) eq 'c'){
                    $job_cust_sublet_sale = $job_cust_sublet_sale + $sublet_sale;
                    $job_cust_sublet_cost = $job_cust_sublet_cost + $sublet_cost;
                } elsif (lc($dynamicPayTypes{'billingType'}) eq 'w'){
                    $job_warr_sublet_sale = $job_warr_sublet_sale + $sublet_sale;
                    $job_warr_sublet_cost = $job_warr_sublet_cost + $sublet_cost;
                }
            }
        }
    }
    return @sublet_section_array;
}

# Subroutine to prepare the DEDUCTIBLE section of a JOB
sub make_deductible_section{
    my ($ro_line) = @_;
    my @ded_section_array;
    my $ded_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'DEDUCTIBLE'");
    $ded_qry->execute($ro_number, $ro_line);
    my ( $ded_code, $ded_description, $ded_cost, $ded_sale);
    $ded_entries = $ded_qry->rows;
    if($ded_entries > 0){
        push (@ded_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@ded_section_array, sprintf(border("#")." %4s DEDUCTIBLE%sCODE%sDESCRIPTION%sSALE%s ".border("|")."\n",
                                            "", "-"x3, "-"x12, "-"x42, "-"x9));

        while (my @ded_row = $ded_qry->fetchrow_array) {
            ( $ded_code, $ded_description, $ded_cost, $ded_sale) = @ded_row;
            $Text::Wrap::columns = 47;
            my $wrapped_ded_description = fill('', '', expand($ded_description));
            my @ded_description_list = split "\n", $wrapped_ded_description;
            push (@ded_section_array, sprintf(border(">")." %-18s%-13s   %-47s   %7.2f%9s ".border("|")."\n",
                                                "", $ded_code, $ded_description_list[0], $ded_sale, "" ));
            foreach my $i (1 .. $#ded_description_list) {
                push (@ded_section_array, sprintf(border(">")." %34s%-47s%-19s ".border("|")."\n", "", $ded_description_list[$i], ""));
            }
            $job_cust_ded_sale = $job_cust_ded_sale + $ded_sale;
            $job_cust_ded_cost = $job_cust_ded_cost + $ded_cost;
        }
    }
    return @ded_section_array;
}

# Subroutine to make the Job Discount section
sub make_ro_job_disc_section {
    my ($ro_line) = @_;
    my @discount_section_array;
    my $disc_qry = $conn->prepare("SELECT
                                      disc_code,
                                      disc_description,
                                      disc_applied,
                                      labor_discount,
                                      parts_discount,
                                      total_discount
                                  FROM repair_discount
                                  WHERE ro_number = ?
                                  AND ro_line = ?");
    $disc_qry->execute($ro_number, $ro_line);
    $job_disc_entries = $disc_qry->rows;
    if($job_disc_entries > 0){
        my ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount );
            push (@discount_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
            push (@discount_section_array, sprintf(border("#")." %4sDISCOUNT%sDESCRIPTION%sTYPE%sCOST%sSALE%s ".border("|")."\n",
                                                "", "-"x6, "-"x42, "-"x7, "-"x7, "-"x3));
        while (my @disc_row = $disc_qry->fetchrow_array) {
            ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount ) = @disc_row;
            $Text::Wrap::columns = 52;
            my $wrapped_disc_description = fill('', '', expand($disc_description));
            my @disc_description_list = split "\n", $wrapped_disc_description;

            push (@discount_section_array, sprintf(border(">")." %4s%-13s %-52s %29s ".border("|")."\n",
                                        "", $disc_code, $disc_description_list[0], ""));
            foreach my $i (1 .. $#disc_description_list) {
                push (@discount_section_array, sprintf(border(">")." %-17s %-52s %29s ".border("|")."\n",
                                        "", $disc_description_list[$i], ""));
            }

            if ($labor_discount != 0){
                push (@discount_section_array, sprintf(border(">")." %17s %-52s %4s   %8.2f   %8.2f%3s ".border("|")."\n",
                                                    "", "LABOR", $disc_applied, 0, $labor_discount, ""));
            }
            if ($parts_discount != 0){
                push (@discount_section_array, sprintf(border(">")." %17s %-52s %4s   %8.2f   %8.2f%3s ".border("|")."\n",
                                                    "", "PARTS", $disc_applied, 0, $parts_discount, ""));
            }
            $job_cust_discount = $job_cust_discount + $total_discount;
        }
    }
    return @discount_section_array;
}

# Subroutine to prepare the TOTAL section of a JOB
sub make_job_total_section{
    my ($ro_line) = @_;
    my @job_total_section_array;
    push (@job_total_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));

    push (@job_total_section_array, sprintf(border(">")." %4sJOB#%3s TOTALS %s%s%s%s%s%s%s ".border("|")."\n", "", $ro_line,
       "-"x10, "CUSTOMER",
       "-"x19, "WARRANTY",
       "-"x19, "INTERNAL",
       "-"x9));

    push (@job_total_section_array, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                        "", "COST", "SALE", "COST", "SALE", "COST", "SALE"));

    push (@job_total_section_array, sprintf(border(">")." %9sLABOR      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $job_cust_lbr_cost, $job_cust_lbr_sale, $job_warr_lbr_cost, $job_warr_lbr_sale,
                                            $job_intern_lbr_cost, $job_intern_lbr_sale));
    if($job_cust_prt_cost != 0 || $job_cust_prt_sale != 0 || $job_intern_prt_cost != 0 ||
       $job_intern_prt_sale != 0 || $job_warr_prt_cost != 0 || $job_warr_prt_sale != 0){
        push (@job_total_section_array, sprintf(border(">")." %9sPARTS      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", $job_cust_prt_cost, $job_cust_prt_sale, $job_warr_prt_cost, $job_warr_prt_sale,
                                                $job_intern_prt_cost, $job_intern_prt_sale));
    }
    if($misc_entries > 0){
        push (@job_total_section_array, sprintf(border(">")." %9sMISC       %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", $job_cust_misc_cost, $job_cust_misc_sale, $job_warr_misc_cost, $job_warr_misc_sale,
                                                $job_intern_misc_cost, $job_intern_misc_sale));
    }
    if($gog_entries > 0){
        push (@job_total_section_array, sprintf(border(">")." %9sGOG        %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", $job_cust_gog_cost, $job_cust_gog_sale, $job_warr_gog_cost, $job_warr_gog_sale,
                                                $job_intern_gog_cost, $job_intern_gog_sale));
    }
    if($sublet_entries > 0){
        push (@job_total_section_array, sprintf(border(">")." %9sSUBLET     %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", $job_cust_sublet_cost, $job_cust_sublet_sale, $job_warr_sublet_cost, $job_warr_sublet_sale,
                                                $job_intern_sublet_cost, $job_intern_sublet_sale));
    }
    if($ded_entries > 0){
        push (@job_total_section_array, sprintf(border(">")." %9sDEDUCTIBLE %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", 0, $job_cust_ded_sale, 0, 0, 0, 0));
    }
    if($job_disc_entries > 0){
        push (@job_total_section_array, sprintf(border(">")." %9sDISCOUNT   %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", 0, -1*$job_cust_discount, 0, 0, 0, 0));
    }

    push (@job_total_section_array, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                            "", "-"x11, "-"x11, "-"x11, "-"x11, "-"x11, "-"x11));
    push (@job_total_section_array, sprintf(border(">")." %9sTOTAL      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "",
                                            $job_cust_lbr_cost+$job_cust_prt_cost+$job_cust_misc_cost+$job_cust_gog_cost+$job_cust_sublet_cost,
                                            $job_cust_lbr_sale+$job_cust_prt_sale+$job_cust_misc_sale+$job_cust_gog_sale+$job_cust_sublet_sale+$job_cust_ded_sale+$job_cust_discount,
                                            $job_warr_lbr_cost+$job_warr_prt_cost+$job_warr_misc_cost+$job_warr_gog_cost+$job_warr_sublet_cost,
                                            $job_warr_lbr_sale+$job_warr_prt_sale+$job_warr_misc_sale+$job_warr_gog_sale+$job_warr_sublet_sale,
                                            $job_intern_lbr_cost+$job_intern_prt_cost+$job_intern_misc_cost+$job_intern_gog_cost+$job_intern_sublet_cost,
                                            $job_intern_lbr_sale+$job_intern_prt_sale+$job_intern_misc_sale+$job_intern_gog_sale+$job_intern_sublet_sale));

    $tot_cust_lbr_cost = $tot_cust_lbr_cost+$job_cust_lbr_cost;
    $tot_cust_lbr_sale = $tot_cust_lbr_sale+$job_cust_lbr_sale;
    $tot_cust_prt_cost = $tot_cust_prt_cost+$job_cust_prt_cost;
    $tot_cust_prt_sale = $tot_cust_prt_sale+$job_cust_prt_sale;
    $tot_cust_misc_cost = $tot_cust_misc_cost+$job_cust_misc_cost;
    $tot_cust_misc_sale = $tot_cust_misc_sale+$job_cust_misc_sale;
    $tot_cust_gog_cost = $tot_cust_gog_cost+$job_cust_gog_cost;
    $tot_cust_gog_sale = $tot_cust_gog_sale+$job_cust_gog_sale;
    $tot_cust_sublet_cost = $tot_cust_sublet_cost+$job_cust_sublet_cost;
    $tot_cust_sublet_sale = $tot_cust_sublet_sale+$job_cust_sublet_sale;
    $tot_cust_ded_sale = $tot_cust_ded_sale+$job_cust_ded_sale;
    $tot_job_cust_discount = $tot_job_cust_discount+$job_cust_discount;

    $tot_warr_lbr_cost = $tot_warr_lbr_cost+$job_warr_lbr_cost;
    $tot_warr_lbr_sale = $tot_warr_lbr_sale+$job_warr_lbr_sale;
    $tot_warr_prt_cost = $tot_warr_prt_cost+$job_warr_prt_cost;
    $tot_warr_prt_sale = $tot_warr_prt_sale+$job_warr_prt_sale;
    $tot_warr_misc_cost = $tot_warr_misc_cost+$job_warr_misc_cost;
    $tot_warr_misc_sale = $tot_warr_misc_sale+$job_warr_misc_sale;
    $tot_warr_gog_cost = $tot_warr_gog_cost+$job_warr_gog_cost;
    $tot_warr_gog_sale = $tot_warr_gog_sale+$job_warr_gog_sale;
    $tot_warr_sublet_cost = $tot_warr_sublet_cost+$job_warr_sublet_cost;
    $tot_warr_sublet_sale = $tot_warr_sublet_sale+$job_warr_sublet_sale;

    $tot_intern_lbr_cost = $tot_intern_lbr_cost+$job_intern_lbr_cost;
    $tot_intern_lbr_sale = $tot_intern_lbr_sale+$job_intern_lbr_sale;
    $tot_intern_prt_cost = $tot_intern_prt_cost+$job_intern_prt_cost;
    $tot_intern_prt_sale = $tot_intern_prt_sale+$job_intern_prt_sale;
    $tot_intern_misc_cost = $tot_intern_misc_cost+$job_intern_misc_cost;
    $tot_intern_misc_sale = $tot_intern_misc_sale+$job_intern_misc_sale;
    $tot_intern_gog_cost = $tot_intern_gog_cost+$job_intern_gog_cost;
    $tot_intern_gog_sale = $tot_intern_gog_sale+$job_intern_gog_sale;
    $tot_intern_sublet_cost = $tot_intern_sublet_cost+$job_intern_sublet_cost;
    $tot_intern_sublet_sale = $tot_intern_sublet_sale+$job_intern_sublet_sale;

    return @job_total_section_array;
}

#Subroutine to prepare the FOOTER section of the invoice
sub get_footer {
    my @footer_section;
    my ($is_end, $page_number) = @_;
    push (@footer_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
    if($is_end) {
        # no newline after the form-feed since a last newline is coming from somewhere and
        # causing the PDF document to paginate a blank page at the end.
        push (@footer_section, sprintf(border("#")." %-15s %25s%40s%19s ".border("|")."\n\f",
               "Page $page_number of $pages",
               "ACCOUNTING COPY",
               "[  END  OF  INVOICE  ]",
               ""));
    } else {
        push (@footer_section, sprintf(border("#")." %-15s %25s%40s%19s ".border("|")."\n\f\n",
               "Page $page_number of $pages",
               "ACCOUNTING COPY",
               "[  CONTINUED ON NEXT PAGE  ]",
               ""));
    }
    return @footer_section;
}

# Subroutine to make the Total(RO) FEE section
sub make_ro_fee_section {
    my $fee_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_description,
                                      other_cost,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ?
                                  AND item_type = 'FEE'
                                  AND ro_line IS NULL");
    $fee_qry->execute($ro_number);
    $fee_entries = $fee_qry->rows;
    if($fee_entries > 0){
        my ( $fee_type, $fee_description, $fee_cost, $fee_sale);
            push (@ro_fee_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
            push (@ro_fee_section, sprintf(border("#")." %4sMISC%sCODE%sDESCRIPTION%sCOST%sSALE%s ".border("|")."\n",
                                                "", "-"x3, "-"x2, "-"x51, "-"x7, "-"x6));

        while (my @fee_row = $fee_qry->fetchrow_array) {
            ( $fee_type, $fee_description, $fee_cost, $fee_sale) = @fee_row;
            push (@ro_fee_section, sprintf(border(">")." %-11s% -4s  %-55s   %8.2f   %8.2f%6s ".border("|")."\n",
                                                "", $fee_type, $fee_description, $fee_cost, $fee_sale,"" ));
            if (lc($fee_type) eq "c"){
                $tot_cust_misc_sale = $tot_cust_misc_sale + $fee_sale;
                $tot_cust_misc_cost = $tot_cust_misc_cost + $fee_cost;
            }elsif (lc($fee_type) eq "w"){
                $tot_warr_misc_sale = $tot_warr_misc_sale + $fee_sale;
                $tot_warr_misc_cost = $tot_warr_misc_cost + $fee_cost;
            }elsif (lc($fee_type) eq "i"){
                $tot_intern_misc_sale = $tot_intern_misc_sale + $fee_sale;
                $tot_intern_misc_cost = $tot_intern_misc_cost + $fee_cost;
            }
        }
    }
    return @ro_fee_section;
}

# Subroutine to make the Total(RO) Discount section
sub make_ro_disc_section {
    my $disc_qry = $conn->prepare("SELECT
                                      disc_code,
                                      disc_description,
                                      disc_applied,
                                      labor_discount,
                                      parts_discount,
                                      total_discount
                                  FROM repair_discount
                                  WHERE ro_number = ?
                                  AND ro_line IS NULL");
    $disc_qry->execute($ro_number);
    $disc_entries = $disc_qry->rows;
    if($disc_entries > 0){
        my ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount );
            push (@ro_disc_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
            push (@ro_disc_section, sprintf(border("#")." %4sDISCOUNT%sDESCRIPTION%sTYPE%sCOST%sSALE%s ".border("|")."\n",
                                                "", "-"x6, "-"x42, "-"x7, "-"x7, "-"x3));
        while (my @disc_row = $disc_qry->fetchrow_array) {
            ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount ) = @disc_row;
            $Text::Wrap::columns = 52;
            my $wrapped_disc_description = fill('', '', expand($disc_description));
            my @disc_description_list = split "\n", $wrapped_disc_description;

            push (@ro_disc_section, sprintf(border(">")." %4s%-13s %-52s %29s ".border("|")."\n",
                                        "", $disc_code, $disc_description_list[0], ""));
            foreach my $i (1 .. $#disc_description_list) {
                push (@ro_disc_section, sprintf(border(">")." %-17s %-52s %29s ".border("|")."\n",
                                        "", $disc_description_list[$i], ""));
            }

            if ($labor_discount != 0){
                push (@ro_disc_section, sprintf(border(">")." %17s %-52s %4s   %8.2f   %8.2f%3s ".border("|")."\n",
                                                    "", "LABOR", $disc_applied, 0, $labor_discount, ""));
            }
            if ($parts_discount != 0){
                push (@ro_disc_section, sprintf(border(">")." %17s %-52s %4s   %8.2f   %8.2f%3s ".border("|")."\n",
                                                    "", "PARTS", $disc_applied, 0, $parts_discount, ""));
            }
            $cust_discount = $cust_discount + $total_discount;
        }
    }
    return @ro_disc_section;
}

# Subroutine to get the RO TAX amount
sub get_ro_tax_amount {
    my $tax_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ? AND item_type = 'TAX'");
    $tax_qry->execute($ro_number);
    my ( $tax_type, $tax_amount);

    while (my @tax_row = $tax_qry->fetchrow_array) {
        ( $tax_type, $tax_amount) = @tax_row;
        if ($tax_type eq "C"){
            if (($tot_cust_lbr_sale+$tot_cust_prt_sale+$tot_cust_misc_sale+
                $tot_cust_gog_sale+$tot_cust_sublet_sale+$tot_cust_ded_sale+
                $tot_job_cust_discount+$cust_discount) == 0 && $has_any_service_contract_job){
                    $warr_ro_tax = $warr_ro_tax + $tax_amount;
            } else{
                $cust_ro_tax = $tax_amount;
            }
        }elsif ($tax_type eq "W"){
            $warr_ro_tax = $warr_ro_tax + $tax_amount;
        }elsif ($tax_type eq "I"){
            $intern_ro_tax = $tax_amount;
        }
    }
}


# Subroutine to get the RO Insurance amount
sub get_ro_insurance_amount {
    $insurance_amount = 0;
    my $insurance_qry = $conn->prepare("SELECT
                                        SUM(pay_amount::numeric)  FILTER (WHERE is_insurance::boolean) AS total_insurance_amount
                                        FROM du_dms_cdkflex_proxy.repair_order_payment
                                        WHERE ro_number = ?");
    $insurance_qry->execute($ro_number);
    my ( $total_insurance_amount );

    while (my @insurance_row = $insurance_qry->fetchrow_array) {
        ( $total_insurance_amount) = @insurance_row;
        $insurance_amount = $total_insurance_amount;
    }
}


# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_grand_total_section {
    get_ro_tax_amount();
    get_ro_insurance_amount();

    push (@grand_total_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@grand_total_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@grand_total_section, sprintf(border("#")." %4sGRAND TOTALS%s ".border("|")."\n", "", "-"x84));
    push (@grand_total_section, sprintf(border(">")." %19s%s%s%s%s%s%s%s ".border("|")."\n", "",
           "-"x10, "CUSTOMER",
           "-"x19, "WARRANTY",
           "-"x19, "INTERNAL",
           "-"x9));

    push (@grand_total_section, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                        "", "COST", "SALE", "COST", "SALE", "COST", "SALE"));

    if ($tot_intern_lbr_cost!=0 || $tot_intern_lbr_sale!=0 || $tot_cust_lbr_cost!=0 || $tot_cust_lbr_sale!=0 || $tot_warr_lbr_cost!=0 || $tot_warr_lbr_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sLABOR         %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n", "",
                                            $tot_cust_lbr_cost, $tot_cust_lbr_sale, $tot_warr_lbr_cost, $tot_warr_lbr_sale, $tot_intern_lbr_cost, $tot_intern_lbr_sale));
    }
    if ($tot_intern_prt_cost!=0 || $tot_intern_prt_sale!=0 || $tot_cust_prt_cost!=0 || $tot_cust_prt_sale!=0 || $tot_warr_prt_cost!=0 || $tot_warr_prt_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sPARTS         %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $tot_cust_prt_cost, $tot_cust_prt_sale, $tot_warr_prt_cost, $tot_warr_prt_sale, $tot_intern_prt_cost, $tot_intern_prt_sale));
    }
    if ($tot_intern_misc_cost!=0 || $tot_intern_misc_sale!=0 || $tot_cust_misc_cost!=0 || $tot_cust_misc_sale!=0 || $tot_warr_misc_cost!=0 || $tot_warr_misc_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sMISC          %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $tot_cust_misc_cost, $tot_cust_misc_sale, $tot_warr_misc_cost, $tot_warr_misc_sale, $tot_intern_misc_cost,
                                            $tot_intern_misc_sale));
    }
    if ($tot_intern_gog_cost!=0 || $tot_intern_gog_sale!=0 || $tot_cust_gog_cost!=0 || $tot_cust_gog_sale!=0 || $tot_warr_gog_cost!=0 || $tot_warr_gog_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sGOG           %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $tot_cust_gog_cost, $tot_cust_gog_sale, $tot_warr_gog_cost, $tot_warr_gog_sale, $tot_intern_gog_cost, $tot_intern_gog_sale));
    }
    if ($tot_intern_sublet_cost!=0 || $tot_intern_sublet_sale!=0 || $tot_cust_sublet_cost!=0 || $tot_cust_sublet_sale!=0 || $tot_warr_sublet_cost!=0 || $tot_warr_sublet_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sSUBLET        %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $tot_cust_sublet_cost, $tot_cust_sublet_sale, $tot_warr_sublet_cost, $tot_warr_sublet_sale, $tot_intern_sublet_cost,
                                            $tot_intern_sublet_sale));
    }
    if ($tot_intern_ded_cost!=0 || $tot_intern_ded_sale!=0 || $tot_cust_ded_cost!=0 || $tot_cust_ded_sale!=0 || $tot_warr_ded_cost!=0 || $tot_warr_ded_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sDEDUCTIBLE    %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $tot_cust_ded_cost, $tot_cust_ded_sale, $tot_warr_ded_cost, $tot_warr_ded_sale, $tot_intern_ded_cost, $tot_intern_ded_sale));
    }

    if($show_accounting_in_proxy eq 'true'){
        if ($insurance_amount > 0){
            push (@grand_total_section, sprintf(border(">")." %6sLESS ADJUSTMENT%10.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", 0, $insurance_amount, 0, 0, 0, 0));
        }
    }

    if ($cust_ro_tax != 0 || $warr_ro_tax != 0 || $intern_ro_tax != 0){
        push (@grand_total_section, sprintf(border(">")." %6sTAX           %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", 0, $cust_ro_tax, 0, $warr_ro_tax, 0, $intern_ro_tax));
    }
    if ($cust_discount+$tot_job_cust_discount != 0 ){
        push (@grand_total_section, sprintf(border(">")." %6sDISCOUNT      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", 0, -1*($cust_discount+$tot_job_cust_discount), 0, 0, 0, 0));
    }

    my $total_amount = 0;
    if($show_accounting_in_proxy eq 'true'){
        $total_amount = ($tot_cust_lbr_sale+$tot_cust_prt_sale+$tot_cust_misc_sale+$tot_cust_gog_sale+$tot_cust_sublet_sale+$tot_cust_ded_sale+$cust_ro_tax+$cust_discount+$tot_job_cust_discount)-$insurance_amount;

    } else{
        $total_amount = $tot_cust_lbr_sale+$tot_cust_prt_sale+$tot_cust_misc_sale+$tot_cust_gog_sale+$tot_cust_sublet_sale+$tot_cust_ded_sale+$cust_ro_tax+$cust_discount+$tot_job_cust_discount;
    }
    
    push (@grand_total_section, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                        "", "-"x11, "-"x11, "-"x11, "-"x11, "-"x11, "-"x11));
    push (@grand_total_section, sprintf(border(">")." %6sTOTAL         %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n", "",
                                        $tot_cust_lbr_cost+$tot_cust_prt_cost+$tot_cust_misc_cost+$tot_cust_gog_cost+$tot_cust_sublet_cost+$tot_cust_ded_cost,
                                        $total_amount,
                                        $tot_warr_lbr_cost+$tot_warr_prt_cost+$tot_warr_misc_cost+$tot_warr_gog_cost+$tot_warr_sublet_cost+$tot_warr_ded_cost,
                                        $tot_warr_lbr_sale+$tot_warr_prt_sale+$tot_warr_misc_sale+$tot_warr_gog_sale+$tot_warr_sublet_sale+$tot_warr_ded_sale+$warr_ro_tax,
                                        $tot_intern_lbr_cost+$tot_intern_prt_cost+$tot_intern_misc_cost+$tot_intern_gog_cost+$tot_intern_sublet_cost+$tot_intern_ded_cost,
                                        $tot_intern_lbr_sale+$tot_intern_prt_sale+$tot_intern_misc_sale+$tot_intern_gog_sale+$tot_intern_sublet_sale+$tot_intern_ded_sale+$intern_ro_tax));
}

# Subroutine to make the Account section
sub make_ro_account_section { 
    my $account_qry = $conn->prepare("select  row_number() over ( ) as sl_no,* from (SELECT
                                      ro_account_no,
                                      TRUNC(sale::numeric) AS sale,
                                       case when account_type = 'A' and (cost is null or cost = '0')
                                           then '*******'
                                         else  TRUNC(coalesce(cost,'0')::numeric)::text end AS cost,
                                      account_control_no,
                                      company_id
                                  FROM repair_account_gldetails WHERE ro_number = ? 
                                  ORDER BY
    CASE 
      WHEN  account_type like 'S%' THEN 1
       WHEN  account_type like '%,S' THEN 2
      ELSE 3
   END,ro_account_no)t");
                              
    $account_qry->execute($ro_number);
    my ( $sl_no, $ro_account_no, $sale, $cost, $account_control_no, $company_id );

    if($account_qry->rows > 0){
        push (@ro_account_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
        if($account_qry->rows == 1){
           push (@ro_account_section, sprintf(border(">")."%4s %-14s %-10s %-10s %-10s %45s".border("|")."\n",
                                                "", "TRGT/ACCOUNT", "SALE", "COST", "CONTROL", ""));
        } else{
             push (@ro_account_section, sprintf(border(">")."%4s %-14s %-10s %-10s %-10s %1s %-14s %-10s %-10s %-10s".border("|")."\n",
                                                "", "TRGT/ACCOUNT", "SALE", "COST", "CONTROL", "", "TRGT/ACCOUNT", "SALE", "COST", "CONTROL"));
        }
       
        while (my @account_row = $account_qry->fetchrow_array) {
            ( $sl_no, $ro_account_no, $sale, $cost, $account_control_no, $company_id ) = @account_row;
             if ($sl_no % 2 == 0){
                 push (@ro_account_section, sprintf(border(">")."%1s %-14s %-10s %-10s %-10s".border("|")."\n",
                                                    "", $company_id.'/'.$ro_account_no, $sale, $cost, $account_control_no ));
                $ro_account_section[@ro_account_section - 2] = $ro_account_section[@ro_account_section - 2] . $ro_account_section[@ro_account_section - 1 ];
				delete($ro_account_section[@ro_account_section - 1 ]);
             } else{
                 push (@ro_account_section, sprintf(border(">")."%4s %-14s %-10s %-10s %-10s"."",
                                                    "", $company_id.'/'.$ro_account_no, $sale, $cost, $account_control_no ));   
             }    

            # push (@ro_account_section, sprintf(border(">")."%4s %-14s %-10s %-10s %-10s %1s %-14s %-10s %-10s %-10s".border("|")."\n",
                                                    # "", "TRGT/ACCOUNT", "SALE", "COST", "CONTROL", "", "TRGT/ACCOUNT", "SALE", "COST", "CONTROL"));
        }
        if($account_qry->rows % 2 == 1){
  				#$array_size=@tech_section_array;
                push (@ro_account_section, sprintf(border(">")."%47s".border("|")."\n",""));
                $ro_account_section[@ro_account_section - 2] = $ro_account_section[@ro_account_section - 2] . $ro_account_section[@ro_account_section - 1 ];
                delete($ro_account_section[@ro_account_section - 1 ]);
        }
	
    }
    return @ro_account_section;
}

# Subroutine to calculate the total no of pages in the invoice
# Update page count calculation section, if any modification is made in the body of the report
sub calculate_page_count {
    my $page_count;
    #my $page_height = 0;
    my $tot_page_lines = 0;
    # Add page count calculation of job section
    foreach my $job_sub_sections (@job_section){
        $tot_page_lines = $tot_page_lines + scalar(@$job_sub_sections);
    }

    # Add page count calculation of FEE section
    $tot_page_lines = $tot_page_lines + scalar(@ro_fee_section);

    # Add page count calculation of Discount section
    $tot_page_lines = $tot_page_lines + scalar(@ro_disc_section);

    # Add page count calculation of grand total section
    $tot_page_lines = $tot_page_lines + scalar(@ro_account_section);
    $tot_page_lines = $tot_page_lines + scalar(@grand_total_section);
    $tot_page_lines = $tot_page_lines + scalar(@comment_section);
    $tot_page_lines = $tot_page_lines + scalar(@end_of_invoice_section);

    $page_count = ceil($tot_page_lines / $page_max_height);

    return $page_count;
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")." %100s ".border("|")."\n", "");
    }
}

# Subroutine for pagination
sub paginate_and_print_segment {
    my (@section_array) = @_;
    # Print section that fit in the current page
    if(scalar(@section_array) <= ($page_max_height - $curr_page_height)){
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{
        for my $sub_section(@section_array){
            if($curr_page_height < $page_max_height)
            {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            } else{
                print FILE get_footer(0, $curr_page_num);
                print FILE @header_section;
                print FILE $sub_section;
                $curr_page_num = $curr_page_num + 1;
                $curr_page_height = 1;
            }
        }
    }
}

# Subroutine to prepare end of invoice section
sub make_end_of_invoice_section {
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %4s%-31s%s%32s ".border("|")."\n",
           "",
           "*"x26,
           "A L L  D E T A I L  I N V O I C E",
           "*"x27));
}

# Subroutine to prepare invoice note section
sub make_invoice_note_section {
    for (my $i = 0; $i < $invoice_note_lines; $i++) {
        push (@invoice_note_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
    }
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
    ($tot_cust_lbr_cost, $tot_cust_lbr_sale, $tot_cust_prt_cost, $tot_cust_prt_sale, $tot_cust_misc_cost, $tot_cust_misc_sale, $tot_cust_gog_cost, $tot_cust_gog_sale,
    $tot_cust_sublet_cost, $tot_cust_sublet_sale, $tot_cust_ded_cost, $tot_cust_ded_sale, $tot_job_cust_discount )= (0)x13;

    ($tot_intern_lbr_cost, $tot_intern_lbr_sale, $tot_intern_prt_cost, $tot_intern_prt_sale, $tot_intern_misc_cost, $tot_intern_misc_sale, $tot_intern_gog_cost,
    $tot_intern_gog_sale, $tot_intern_sublet_cost, $tot_intern_sublet_sale, $tot_intern_ded_cost, $tot_intern_ded_sale )= (0)x12;

    ($tot_warr_lbr_cost, $tot_warr_lbr_sale, $tot_warr_prt_cost, $tot_warr_prt_sale, $tot_warr_misc_cost, $tot_warr_misc_sale, $tot_warr_gog_cost, $tot_warr_gog_sale,
    $tot_warr_sublet_cost, $tot_warr_sublet_sale, $tot_warr_ded_cost, $tot_warr_ded_sale )= (0)x12;

    ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;
    ($cust_ro_fee, $warr_ro_fee, $intern_ro_fee) = (0)x3;
    $insurance_amount = 0;
    $cust_discount = 0;

    $curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @comment_section;
    undef @job_section;
    undef @grand_total_section;
    undef @ro_account_section;
    undef @end_of_invoice_section;
    undef @invoice_note_section;
    undef @ro_fee_section;
    undef @ro_disc_section;
    $have_insurance_pay = 0;
    ########## Prepare invoice ##########
    make_header();
    $have_insurance_pay = get_insurance_pay_status();
    make_job_section();
    make_ro_fee_section();
    make_ro_disc_section();
    make_grand_total_section();

    if($show_accounting_in_proxy eq 'true'){
        make_ro_account_section();
    }
   
    make_end_of_invoice_section();
    # make_invoice_note_section();
    $pages = calculate_page_count();

    ########## Print invoice ##########
    print FILE @header_section; # Header for the first page

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }

    # Pagination of RO fee section
    paginate_and_print_segment(@ro_fee_section);

    # Pagination of RO Discount section
    paginate_and_print_segment(@ro_disc_section);

    # Pagination of RO Comments
    paginate_and_print_segment(@comment_section);

    if($show_accounting_in_proxy eq 'true'){
       # Pagination of RO Technician Punch details
       paginate_and_print_segment(@ro_account_section);
    }

    # Pagination of Grand total section
    paginate_and_print_segment(@grand_total_section);

    # Pagination of invoice end section
    paginate_and_print_segment(@end_of_invoice_section);

    # End Of Invoice Note
    # paginate_and_print_segment(@invoice_note_section);

    # End of invoice footer
    if($curr_page_height == $page_max_height){
        print FILE get_footer(1, $curr_page_num);
    } else {
        print_blank_line($page_max_height - $curr_page_height);
        print FILE get_footer(1, $curr_page_num);
    }
}

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}

# Print Open/Void RO
sub print_open_ro {
    printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO# ".$ro_number." * See Open and Void RO Report *");
}

# Get insurance payment status of RO
sub get_insurance_pay_status {
    return $conn->selectrow_array("SELECT
                                       bool_or(is_insurance)
                                   FROM repair_order_payment
                                   WHERE ro_number = ?", undef, $ro_number);
}

# Get Paytype details and swapping
sub swap_paytype_dynamically($$) {
    my ($billing_type,$billing_code) = @_;
    $billing_type=lc($billing_type);
    %paytypeLabels = (
        'i' => "INTERNAL",
        'w' => "WARRANTY",
        'c' => "CUSTOMER"
    );
    %resultObj = (
       billingType     => lc($billing_type),
       paytype         => $billing_code,
       label           => $paytypeLabels{$billing_type},
       fromtype        => $paytypeLabels{$billing_type},
       labelLength     => length($paytypeLabels{$billing_type}),
       serviceContract => ''  
    );

    my $paytype_details_qry = $conn->prepare("SELECT
                                                label,
                                                pay_type,
                                                target_type,
                                                from_type,
                                                insurance
                                            FROM pay_type_filter");
    $paytype_details_qry->execute();
    my ($label, $pay_type, $target_type, $from_type, $insurance);
    my $service_contract = '';
    if(lc($target_type) eq 'warranty') {
        $service_contract = 1;
    }
    if($paytype_details_qry->rows > 0){
        while (my @paytype_row = $paytype_details_qry->fetchrow_array) {
            ($label, $pay_type, $target_type, $insurance) = @paytype_row;
            if (length($label)<0){
                if(uc(substr $pay_type, 0, 1) eq 'I'){
                  $label = 'INTERNAL';
                } elsif(uc(substr $pay_type, 0, 1) eq 'W'){
                  $label = 'WARRANTY'; 
                } elsif(uc(substr $pay_type, 0, 1) eq 'C'){
                  $label = 'CUSTOMER';
                }
            }
            if($pay_type eq $billing_code) {       
                %resultObj = (
                    billingType      => substr($target_type, 0, 1),
                    paytype          => $pay_type,
                    label            => $label,
                    fromtype         => $from_type,
                    labelLength      => length($label),
                    serviceContract  => $service_contract 
                );
            }
        }
    }
    return %resultObj;
}
