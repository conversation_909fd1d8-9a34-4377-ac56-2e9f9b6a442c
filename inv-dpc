#!/usr/bin/env bash
shopt -s failglob

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

batch_script_name='batch-bundling-script-copy-paste.txt'
meta_dir_name='meta'
build_dir_name='build'
batch_file_relative="${build_dir_name}/${batch_script_name}"

REPO_ROOT="$(cd $(dirname $0) && pwd)"
DMS_ROOT="$REPO_ROOT"/DU-DMS/DMS-DPC
CMD_DIR="$DMS_ROOT"/invoice-manipulation
COM_CMD_DIR="$REPO_ROOT"/invoice-manipulation-common

[[ -d "$CMD_DIR" ]] || die "Could not locate commands at $CMD_DIR"
export PATH="$PATH":"$CMD_DIR":"$COM_CMD_DIR"

INV_WORK="$DU_ETL_WORK"/etl-dpc/inv-dpc
[[ -d "$INV_WORK" ]] || die "Cannot work if work directory is missing: $INV_WORK"

function convert_to_pdf() {
    import-invoice-bundle.bash \
        --zip-file "$1" \
        --out-dir "$2"
}

function write_out_cmd_script() {
    cat <<BAT >"$1"
PATH=%PATH%;"C:\Program Files (x86)\VeryPDF PDF Split-Merge v3.0"
# Run this while located at <DU_ETL_WORK>/etl-dpc/inv-dpc
cmd /V /C "pdfpg.exe ./$2/Invoices/*.pdf ./$2/$2-master-scenario-bundle.pdf"
BAT
}

say "Processing Directory $INV_WORK"

(
    cd "$INV_WORK"
    for zipfile in ./*.zip
    do
        zipbasefilename="$(basename $zipfile .zip)"

        progress "Found $zipbasefilename"

        zip_out_dir="$INV_WORK"/"${zipbasefilename:?Zip File Name Not Found}"/Invoices
        if [[ -d "$zip_out_dir" ]]; then
            yell "Skipping Existing Directory $zip_out_dir"
            continue
        else
            mkdir -p "$zip_out_dir" || die "Failed to create $zip_out_dir"
        fi

        progress "Beginning PDF Conversion"
        convert_to_pdf "$zipfile" "$zip_out_dir"

        progress "Enumerating"
        create-enumeration-meta \
            --invoice-dir "$zip_out_dir" \
            --output-dir "$zip_out_dir"/"$meta_dir_name"

        batch_file_actual="${zip_out_dir}"/"${batch_file_relative}"
        mkdir "${zip_out_dir}/${build_dir_name}"
        write_out_cmd_script \
            "$batch_file_actual" \
            "$zipbasefilename"

        say "Invoice Zip File Processed - Please run the script @"
        say "${batch_file_actual}"

        mv "$zipfile" "./${zipbasefilename}"/
    done
) || die "Failed during processing of $INV_WORK directory"

exit
