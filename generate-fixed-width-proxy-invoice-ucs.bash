#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

REPO_ROOT="$(cd $(dirname $0) && pwd)"

SCHEMA_NAME="${1:?Schema Name Required}"
TARGET_DIR="${2:?Target Directory Required}"
CONVERT_TO_PDF="${3:-true}"
# Ver generic_proxy:- Generic proxy invoice,
# Ver invoice_mimic:- Mimic invoice merged with Auto/mate template
VERSION="${4:-"generic_proxy"}"

echo $TARGET_DIR

if [[ "$VERSION" = "generic_proxy" ]]; then
    "$REPO_ROOT"/generate-fixed-width-proxy-invoice.bash \
            "$SCHEMA_NAME" \
            "$TARGET_DIR" \
            "$CONVERT_TO_PDF"
elif [[ "$VERSION" = "invoice_mimic" ]]; then
    TMP_DIR="/tmp/du-etl-reynolds-proxyinvoice-work"
    mkdir -p "$TMP_DIR"
    clear_dir "$TMP_DIR"
    BOOK_DIR="$TMP_DIR"/bookmarking

    [[ -d "$TARGET_DIR" ]] || die "$2 must exist and should be empty"
    
    rm -rf /tmp/input
    mkdir /tmp/input

    clear_dir "$TARGET_DIR"
    mkdir "$TARGET_DIR"/text || die "Could not prepare text directory"
    mkdir "$TARGET_DIR"/text/inv-bg || die "Could not prepare text directory"
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        mkdir "$TARGET_DIR"/pdf  || die "Could not prepare pdf directory"
    fi

    (
        cd "$TARGET_DIR"/text || die "Failed to set target directory as active"

        if [[ "$VERSION" = "invoice_mimic" ]]; then
            "$REPO_ROOT"/create-proxy-invoice-report-ucs-mimic.pl "${SCHEMA_NAME}"
        fi
    )
       
    rm -rf "$TARGET_DIR"/text/inv-bg

    echo "$TARGET_DIR"/pdf

    pdf_create generate -o "$TARGET_DIR"/pdf
 
    cd "$TARGET_DIR"/text
    
    grep -rli '~font{HelveticaMono2}' * | xargs -i@ sed -i 's/~font{HelveticaMono2}//g' @
    grep -rli '~font{DejaVuSansMono9}' * | xargs -i@ sed -i 's/~font{DejaVuSansMono9}//g' @
    grep -rli '~font{DejaVuSansMono9}' * | xargs -i@ sed -i 's/~font{DejaVuSansMono9}//g' @
    grep -rli '~font{HelveticaMono1}' * | xargs -i@ sed -i 's/~font{HelveticaMono1}//g' @
    grep -rli '~font{HelveticaMono3}' * | xargs -i@ sed -i 's/~font{HelveticaMono3}//g' @
    grep -rli '~font{default}' * | xargs -i@ sed -i 's/~font{default}//g' @
    grep -rli '~font{DejaVuSansMono-Bold9}' * | xargs -i@ sed -i 's/~font{DejaVuSansMono-Bold9}//g' @
    grep -rli '~color{.60 0 0}' * | xargs -i@ sed -i 's/~color{.60 0 0}//g' @
    grep -rli '~color{0 0 0}' * | xargs -i@ sed -i 's/~color{0 0 0}//g' @
    grep -rli '~font{DejaVuSansMono9}' * | xargs -i@ sed -i 's/~font{DejaVuSansMono9}//g' @

    # Create one or more PDF bundles from the individual PDF files
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        (
            cd "$TARGET_DIR"/pdf
            mkdir ../bundle
            BUNDLE_PREFIXES=$(find . -mindepth 1 -maxdepth 1 -type f -exec basename {} \; | perl -ale 'print $1 if /(\d+)\d{2}.pdf/;' | sort | uniq)
            for prefix in $BUNDLE_PREFIXES; do
                echo "prefix:$prefix"
                FIRST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $1}')
                LAST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $NF}')
                FIRST=${FIRST_TMP::-4}
                LAST=${LAST_TMP::-4}
                pdftk ./*"${prefix}"*.pdf cat output ../bundle/bundle-"${FIRST}-${LAST}".pdf
            done
        )
    fi
    say "Done Creating Proxy Invoices"
else
    die "Invalid Proxy Version"
fi
