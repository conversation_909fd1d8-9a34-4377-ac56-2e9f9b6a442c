source "/home/<USER>/.bashrc.d/mailgun.bash"

function psql_audit_import() {
    psql "service=$DU_ETL_SCHEDULER_SERVICE options='-c search_path=core'" --set=ON_ERROR_STOP=1 --set=mfg="$BRAND" "$@"
    return $?
}

function psql_audit_import_status() {
    psql "service=$DU_ETL_SCHEDULER_SERVICE options='-c search_path=process_data'" --set=ON_ERROR_STOP=1 "$@"
    return $?
}

function alter_schema(){
        INPUT_TYPE=$1
        OLD_SCHEMA=${SRC_SCHEMA}_model
        echo "INPUT_TYPE ::: $INPUT_TYPE"
        echo "OLD_SCHEMA1 ::: $OLD_SCHEMA"
        IFS=',' read -ra ALL_COMPANY_IDS <<< "$COMPANY_IDS"
        for COMPANY_ID in "${ALL_COMPANY_IDS[@]}"; do
            echo "COMPANY_ID: $COMPANY_ID"
            FIX_UUID=$(echo "$UUID" | tr -d '-')
            FIX_UUID_LOWER=$(echo "$FIX_UUID" | tr '[:upper:]' '[:lower:]')        
            NEW_SCHEMA=du_dms_${FIX_UUID_LOWER}_${COMPANY_ID}
            echo "NEW_SCHEMA ::: $NEW_SCHEMA"
            rename_schema_for_halt_resolution "$COMPANY_ID" "$UUID" "$OLD_SCHEMA" "$NEW_SCHEMA" "$INPUT_TYPE" || die "Dump file creation for new schema failed"
            OLD_SCHEMA=${NEW_SCHEMA}
            echo "OLD_SCHEMA2 ::: $OLD_SCHEMA"
        done
}

function dummy_report_excluded_results() {
    (

        progress "Creating Dummy exclusion reports"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        echo "make_format,make_name" >> rename_rules.csv
        echo "manufacturer,make_list" >> manufacturer_details.csv
        echo "manufacturer,make_list" >> all_manufacturer_details.csv
        echo "type,paytype,project_type,company_id,is_parts_allowed" >> paytype_details.csv
        echo "dept,is_allowed" >> department_details.csv
    )
}

function email_halt_summary() {
    local SUBJECT="$1"
    local BODY="$2"
    # SEND_TO_ETL_IMPORT_NOTIFY='<EMAIL>,<EMAIL>'
    if [[ ! "${SEND_TO_ETL_IMPORT_NOTIFY:-}" = '' ]]; then
        if type send-email >/dev/null 2>&1; then
            say "Sending Pre-import Halt Summary Emails"
            send-email --to "${SEND_TO_ETL_IMPORT_NOTIFY}" \
                       --subject "${SUBJECT}" \
                       --body "${BODY}" \
                       --send
        else
            debug "send-email command not available even though SEND_TO_ETL_IMPORT_NOTIFY is set"
        fi
    fi
}

function report_excluded_results() {
    (

        progress "Creating exclusion reports"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        
        psql_audit_import --quiet <<SQL
        set client_min_messages = warning;

        \o 'rename_rules.csv'        
        COPY (SELECT make_format, make_name , true
          FROM make_rule) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );
        
        -- \o 'manufacturer_details.csv'
        -- COPY (SELECT  manufacturer, make_list, true
        --   FROM manufacturer WHERE manufacturer=:'mfg') TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

        \o 'all_manufacturer_details.csv'
        COPY (SELECT  manufacturer, make_list, true
          FROM manufacturer) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

        \o 'department_details.csv'
        COPY (SELECT dept, is_allowed, true FROM department) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );
SQL

    IFS=',' read -ra ALL_COMPANY_IDS <<< "$COMPANY_IDS"
    for COMPANY_ID in "${ALL_COMPANY_IDS[@]}"; do
    psql_audit_import --quiet --set=COMPANY_ID="$COMPANY_ID" \
    <<SQL
        \o 'paytype_details_$COMPANY_ID.csv'
            COPY (SELECT UPPER(LEFT(category::text, 1)) AS type, paytype,  'parts' AS project_type, company_id, is_parts_allowed, true
                FROM store_paytype WHERE company_id=:'COMPANY_ID'
                UNION
                SELECT UPPER(LEFT(category::text, 1)) AS type, paytype,  'labor' As project_type, company_id, is_labor_allowed, true
                FROM store_paytype WHERE company_id=:'COMPANY_ID') TO STDOUT WITH (FORMAT CSV, HEADER TRUE );
SQL
    done
    )
}

function load_data_from_scheduler_database() {
    progress "Load data in Scheduler import database"
    cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
    rename_rules="$WORK_DIR_PROCESSING_RESULTS_DIR/rename_rules.csv"
    manufacturer_details="$WORK_DIR_PROCESSING_RESULTS_DIR/manufacturer_details.csv"
    all_manufacturer_details="$WORK_DIR_PROCESSING_RESULTS_DIR/all_manufacturer_details.csv"
    department_details="$WORK_DIR_PROCESSING_RESULTS_DIR/department_details.csv"
    psql_local <<SQL
    CREATE TABLE etl_manufacturer_detail (
            manufacturer      text NOT NULL,
            valid_makes_array       text[],
            is_default    boolean NOT NULL DEFAULT true
    );
SQL

    psql_local <<SQL
    CREATE TABLE etl_all_manufacturer_detail (
            manufacturer      text NOT NULL,
            valid_makes_array       text[],
            is_default    boolean NOT NULL DEFAULT true
    );
    \copy etl_all_manufacturer_detail from '$all_manufacturer_details' with (format csv, header true)
SQL


    psql_local <<SQL
    CREATE TABLE etl_makerenames_rule_detail (
            original_name       text NOT NULL,
            renamed_name  text NOT NULL,
            is_default    boolean NOT NULL DEFAULT true
    );
    \copy etl_makerenames_rule_detail from '$rename_rules' with (format csv, header true)
        CREATE TABLE etl_makerenames_detail (
            original_name       text NOT NULL,
            renamed_name  text NOT NULL,
            is_default    boolean NOT NULL DEFAULT true
    );
    \copy etl_makerenames_detail from '$rename_rules' with (format csv, header true)
SQL

    psql_local <<SQL
        CREATE TABLE etl_paytype_detail (
                type          text NOT NULL,
                paytype       text NOT NULL,
                project_type  text NOT NULL,
                store         text NOT NULL,
                is_allowed    boolean,
                is_default    boolean NOT NULL DEFAULT true
        );
SQL
            psql_local <<SQL
            TRUNCATE TABLE etl_paytype_detail;
SQL
            psql_local <<SQL
            TRUNCATE TABLE etl_manufacturer_detail;
SQL
        IFS=',' read -ra ALL_COMPANY_IDS <<< "$COMPANY_IDS"
        for COMPANY_ID in "${ALL_COMPANY_IDS[@]}"; do
            paytype_details="$WORK_DIR_PROCESSING_RESULTS_DIR/paytype_details_${COMPANY_ID}.csv"
            echo "Looking for COMPANY_ID: $COMPANY_ID in file: $WORK_DIR_PROCESSING_RESULTS_DIR/job.txt"
            COMPANY_DETAILS=$(grep $COMPANY_ID "$WORK_DIR_PROCESSING_RESULTS_DIR/job.txt")
            IFS='|' read -ra COMPANY_BRAND <<< "$COMPANY_DETAILS"
            echo "Debug: COMPANY_DETAILS='$COMPANY_DETAILS'"
            echo "COMPANY_BRAND[0] = '${COMPANY_BRAND[0]}'"
            echo "COMPANY_BRAND[1] = '${COMPANY_BRAND[1]}'"
            echo "COMPANY_BRAND[1] = '${COMPANY_BRAND[1]}'"

            if [ ${#COMPANY_BRAND[@]} -gt 1 ] && [ -n "${COMPANY_BRAND[1]}" ]; then
                echo "COMPANY_BRAND: ${COMPANY_BRAND[1]}"
            else
                mv "$INPUT_BUNDLE_ZIP" "$DEAD_LETTER_DIR"/
                die "COMPANY_BRAND is empty or does not exist"
            fi
            psql_local <<SQL
            \copy etl_paytype_detail from '$paytype_details' with (format csv, header true)
SQL
           
            if [ "${COMPANY_BRAND[1]}" = "GM" ] && [[ ! "MD OH NA MT UT" =~ (^|[[:space:]])${COMPANY_BRAND[2]}($|[[:space:]]) ]]; then

            psql_local --set=mfg="${COMPANY_BRAND[1]}" \
            <<SQL
            INSERT INTO etl_manufacturer_detail
            (manufacturer, valid_makes_array, is_default)
            SELECT *
            FROM etl_all_manufacturer_detail;
SQL
            else
            psql_local --set=mfg="${COMPANY_BRAND[1]}" \
            <<SQL
            INSERT INTO etl_manufacturer_detail
            (manufacturer, valid_makes_array, is_default)
            SELECT *
            FROM etl_all_manufacturer_detail
            WHERE manufacturer=:'mfg';
SQL
            fi
            

        done
    psql_local <<SQL
    CREATE TABLE etl_department_detail (
            department_name       text NOT NULL,
            is_allowed            boolean,
            is_default            boolean NOT NULL DEFAULT true
    );
    \copy etl_department_detail from '$department_details' with (format csv, header true)
SQL
    psql_local <<SQL
    CREATE TABLE etl_sequence_detail (
        start_ro text NOT NULL,
        end_ro text NOT NULL
    );
SQL
}


function save_import_halt_status() {
    scheduler_id=$1
    company_id=$2
    is_make_halt=$3
    is_dept_halt=$4
    is_paytype_halt=$5
    inv_seq_halt=$6


    progress "Inserting Halt status to database"
    psql_audit_import_status --quiet \
               --set=scheduler_id="$scheduler_id" \
               --set=company_id="$company_id" \
               --set=is_make_halt="$is_make_halt" \
               --set=is_dept_halt="$is_dept_halt" \
               --set=is_paytype_halt="$is_paytype_halt" \
               --set=inv_seq_halt="$inv_seq_halt" \
               >/dev/null \
<<'SQL'
INSERT INTO scheduler_pre_import_status(scheduler_id,company_id,is_make_halt,is_dept_halt,is_paytype_halt,inv_seq_halt) VALUES(:'scheduler_id',:'company_id',:'is_make_halt',:'is_dept_halt',:'is_paytype_halt',:'inv_seq_halt');
SQL

}


function create_halt_schema_dump(){
    COMPANY_ID=$1
    SCHEDULER_ID=$2 
    OLD_SCHEMA=$3
    NEW_SCHEMA=$4
    INPUT_TYPE=$5

    pg_dump -Fc "service=$DU_ETL_PG_SERVICE options='-c search_path=${NEW_SCHEMA}'" \
    -t '*' \
    --exclude-schema=pg_catalog \
    > "${WORK_DIR_PROCESSING_RESULTS_DIR}/process-${INPUT_TYPE}-csv-results-${SCHEDULER_ID}-${COMPANY_ID}.pgdump"

# Rename schema in pgdump file
# sed -i "s/${NEW_SCHEMA}/${OLD_SCHEMA}/g" "${WORK_DIR_PROCESSING_RESULTS_DIR}/process-xml-csv-results-${SCHEDULER_ID}-${COMPANY_ID}.pgdump"

    echo "Dump file created for new Schema: '${NEW_SCHEMA}'"
}

function rename_schema_for_halt_resolution() {
# SQL script for schema duplication
COMPANY_ID=$1
SCHEDULER_ID=$2  
OLD_SCHEMA=$3
echo "OLD_SCHEMA ::: $OLD_SCHEMA"
NEW_SCHEMA=$4
echo "NEW_SCHEMA ::: $NEW_SCHEMA"
INPUT_TYPE=$5
paytype_details="$WORK_DIR_PROCESSING_RESULTS_DIR/paytype_details_${COMPANY_ID}.csv"
progress "Processing schema duplication"
psql_local --quiet \
           --set=OLD_SCHEMA="$OLD_SCHEMA" \
           --set=NEW_SCHEMA="$NEW_SCHEMA" \
           >/dev/null \
<<SQL
DROP SCHEMA IF EXISTS $NEW_SCHEMA CASCADE;
CREATE SCHEMA $NEW_SCHEMA;

DO \$\$
DECLARE
    table_name text; -- Renamed to avoid ambiguity
BEGIN
    FOR table_name IN
        SELECT tablename
        FROM pg_tables
        WHERE schemaname = '$OLD_SCHEMA'
    LOOP
        EXECUTE 'CREATE TABLE $NEW_SCHEMA.' || table_name || ' AS TABLE $OLD_SCHEMA.' || table_name;       
    END LOOP;
END \$\$;
SQL
create_halt_schema_dump $COMPANY_ID $SCHEDULER_ID $OLD_SCHEMA $NEW_SCHEMA $INPUT_TYPE
}


