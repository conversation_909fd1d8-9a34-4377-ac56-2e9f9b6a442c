#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

REPO_ROOT="$(cd $(dirname $0) && pwd)"

SCHEMA_NAME="${1:?Schema Name Required}"
TARGET_DIR="${2:?Target Directory Required}"
CONVERT_TO_PDF="${3:-true}"
# Ver generic_proxy:- Generic proxy invoice,
# Ver invoice_mimic:- Mimic invoice merged with Auto/mate template
VERSION="${4:-"generic_proxy"}"
PORSCHE_STORE="${6:-false}"
DEALER_ADDRESS="${7}"

if [[ "$VERSION" = "generic_proxy" ]]; then
    "$REPO_ROOT"/generate-fixed-width-proxy-invoice.bash \
            "$SCHEMA_NAME" \
            "$TARGET_DIR" \
            "$CONVERT_TO_PDF"
elif [[ "$VERSION" = "invoice_mimic" ]]; then
    TMP_DIR="/tmp/du-automate-etl-proxyinvoice-work"
    mkdir -p "$TMP_DIR"
    clear_dir "$TMP_DIR"
    BOOK_DIR="$TMP_DIR"/bookmarking

    [[ -d "$TARGET_DIR" ]] || die "$2 must exist and should be empty"
    
    clear_dir "$TARGET_DIR"
    mkdir "$TARGET_DIR"/text || die "Could not prepare text directory"

    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
         mkdir "$TARGET_DIR"/pdf  || die "Could not prepare pdf directory"
         mkdir -p "$TARGET_DIR"/pdf-tmp/actual  || die "Could not prepare actual pdf directory"
         mkdir -p "$TARGET_DIR"/pdf-tmp/vertical || die "Could not prepare vertical pdf directory"
         mkdir -p "$TARGET_DIR"/pdf-tmp/open || die "Could not prepare vertical pdf directory"
        (
            automate_pdf g -o "$TARGET_DIR"/pdf-tmp -p "$PORSCHE_STORE"
        ) || die "Failed during Proxy Invoice generation"
        
        cd "$TARGET_DIR"/pdf-tmp/actual
        PDF_COUNT=$(ls -1 | wc -l)

        if [[ "$PDF_COUNT" -gt 0 ]]; then
            

            (
                cd "$TARGET_DIR"/pdf-tmp
                rm -rf ./output
                mkdir -p ./output
                verticalro="./vertical"
                verticalout="./verticalout"
                splitropage_1="./split_first"
                splitropage_other="./split_other"

                rm -rf ./verticalout
                mkdir -p ./verticalout
                loop_count=0
                for pi in ./actual/*.pdf; do
                    rm -rf "${splitropage_1}"
                    mkdir -p "${splitropage_1}"
                    rm -rf "${splitropage_other}"
                    mkdir -p "${splitropage_other}"
                    filename=$(basename "$pi")
                    filename="${filename%.*}"
                    ((loop_count++))
                    if [[ $(($loop_count%100)) = 0 ]]; then
                        echo "$(date +'%H:%M:%S') Converted $loop_count"
                    fi
                    pdftk ./actual/"${filename}.pdf" multistamp "$DU_ETL_HOME"/DU-ProxyInvoice/invoice-template/automate-side-border.pdf output ./output/"${filename}.pdf"
                    pdftk "${verticalro}/${filename}.pdf" cat 1east output "${verticalout}/${filename}.pdf"
                    pdftk ./output/"${filename}.pdf" cat 1 output "${splitropage_1}"/"${filename}.pdf"
                    pdftk ./output/"${filename}.pdf" burst output "${splitropage_other}/${filename}_%02d.pdf"
                    rm "${splitropage_other}/${filename}_01.pdf"
                    pdftk "${splitropage_1}"/"${filename}.pdf" stamp  "${verticalout}/${filename}.pdf" output "${splitropage_other}/${filename}_01.pdf"
                    pdftk "${splitropage_other}"/*.pdf cat output ../pdf/"${filename}.pdf"
                done
            ) || die "Failed to combine pdfs"

            

           
            cd "$TARGET_DIR"/pdf
            (
                for pi in *.pdf; do 
                    filename=$(basename "$pi")
                    filename="${filename%.*}"
                    pdftotext -layout -enc "UTF-8" "$TARGET_DIR"/pdf/${filename}.pdf "$TARGET_DIR"/text/${filename}.txt
                done
            ) || die "Failed during conversion of pdf to text generation"

            cp "$TARGET_DIR"/pdf-tmp/open/*.pdf "$TARGET_DIR"/pdf 
            rm -rf "$TARGET_DIR"/pdf-tmp
        fi

    else
        progress "Skipping PDF Conversion as Requested"
    fi


    # Create one or more PDF bundles from the individual PDF files
    if [[ "$PDF_COUNT" -gt 0 ]]; then
        if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
            (
                cd "$TARGET_DIR"/pdf
                mkdir ../bundle
                BUNDLE_PREFIXES=$(find . -mindepth 1 -maxdepth 1 -type f -exec basename {} \; | perl -ale 'print $1 if /(\d+)\d{2}.pdf/;' | sort | uniq)
                for prefix in $BUNDLE_PREFIXES; do
                    FIRST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $1}')
                    LAST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $NF}')
                    FIRST=${FIRST_TMP::-4}
                    LAST=${LAST_TMP::-4}
                    pdftk ./"${prefix}"*.pdf cat output ../bundle/bundle-"${FIRST}-${LAST}".pdf
                done
            )
        fi
    else
       say "No proxy invoice found"
    fi
     say "Done Creating Proxy Invoices"
else
    die "Invalid Proxy Version"
fi
