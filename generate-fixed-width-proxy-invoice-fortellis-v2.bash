#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

REPO_ROOT="$(cd $(dirname $0) && pwd)"

SCHEMA_NAME="${1:?Schema Name Required}"
TARGET_DIR="${2:?Target Directory Required}"
CONVERT_TO_PDF="${3:-true}"
# CONVERT_TO_PDF="false"
# Ver generic_proxy:- Generic proxy invoice,
# Ver merge_with_template:- Proxy invoice merged with fortellis template,
# Ver invoice_mimic:- Mimic invoice merged with fortellis template
# VERSION="${4:-"generic_proxy"}"

VERSION="invoice_mimic"

BACKGROUND_TEMPLATE_PATH="${5:-none}"
SHOW_ACCOUNTING_IN_PROXY="${6:-false}"
PORSCHE_STORE="${7:-false}"
DUAL_PROXY="${8:-false}"

APPLY_BACKGROUND='true'
if [[ -f "$BACKGROUND_TEMPLATE_PATH" ]]; then
    APPLY_BACKGROUND='true'
fi
  
if [[ "$VERSION" = "generic_proxy" ]]; then
    "$REPO_ROOT"/generate-fixed-width-proxy-invoice.bash \
            "$SCHEMA_NAME" \
            "$TARGET_DIR" \
            "$CONVERT_TO_PDF" \
            "${SHOW_ACCOUNTING_IN_PROXY}" \
            "${PORSCHE_STORE}"
elif [[ "$VERSION" = "merge_with_template" ]] || [[ "$VERSION" = "invoice_mimic" ]]; then
    TMP_DIR="/tmp/du-etl-fortellis-proxyinvoice-work"
    mkdir -p "$TMP_DIR"
    BOOK_DIR="$TMP_DIR"/bookmarking

    mkdir "$TARGET_DIR"/text-supplement || die "Could not prepare text V2 directory"
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        mkdir "$TARGET_DIR"/pdf-supplement  || die "Could not prepare pdf V2 directory"
    fi

    (
        cd "$TARGET_DIR"/text-supplement || die "Failed to set target directory as active"

        if [[ "$VERSION" = "invoice_mimic" ]]; then
            "$REPO_ROOT"/create-proxy-invoice-report-fortellis-mmic.pl "${SCHEMA_NAME}" "${SHOW_ACCOUNTING_IN_PROXY}" "${PORSCHE_STORE}" "${DUAL_PROXY}"
        fi

        #Create fixed width proxy invoice PDF report by iterating the proxy invoice txt reports
        loop_count=0
        if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
            progress "Beginning PDF Conversions - Count: $(find . -type f | wc -l)"
            echo "$(date +%H:%M:%S) Starting"
            for pi in ./*.txt; do
                sed -i -Ee 's/\~([A-Z])/\1/g' "${pi}"
                sed -i -Ee 's/\~([[:space:]])/\1/g' "${pi}"
                sed -i -Ee 's/\~([0-9])/\1/g' "${pi}"
                ((loop_count++))
                if [[ $(($loop_count%200)) = 0 ]]; then
                    echo "$(date +'%H:%M:%S') Converted $loop_count"
                fi
                clear_dir "$BOOK_DIR"
                [ -f "$pi" ] || break
                filename=$(basename "$pi")
                filename="${filename%.*}"
                if [[ "$VERSION" = "invoice_mimic" ]]; then
                    enscript -q \
                            -fCourierStd12 \
                            --no-header \
                            --margins=8:2:10:10 \
                            -L 73 "${pi}" \
                            -e~ \
                            -o - \
                        | ps2pdf - "$BOOK_DIR"/"${filename}.pdf"
                fi

                # if [[ "$APPLY_BACKGROUND" = 'true' ]]; then
                #     pdftk "$BOOK_DIR"/"${filename}.pdf" multibackground "$REPO_ROOT"/invoice-template/fortellis_template_blank.pdf output "$BOOK_DIR"/"${filename}-merged.pdf"
                # else
                    # mv "$BOOK_DIR"/"${filename}.pdf" "$BOOK_DIR"/"${filename}-merged.pdf"
                # fi

                pdftk "$BOOK_DIR"/"${filename}.pdf" dump_data output "${BOOK_DIR}"/temp-pdf-metadata.txt

                printf 'BookmarkBegin\nBookmarkTitle: %s\nBookmarkLevel: 1\nBookmarkPageNumber: 1' "${filename}" \
                    > "${BOOK_DIR}"/temp-pdf-new-bookmark.txt

                cat "${BOOK_DIR}"/temp-pdf-metadata.txt \
                    "${BOOK_DIR}"/temp-pdf-new-bookmark.txt \
                    > "${BOOK_DIR}"/temp-pdf-new-metadata.txt

                pdftk "${BOOK_DIR}"/"${filename}.pdf" \
                    update_info \
                    "${BOOK_DIR}"/temp-pdf-new-metadata.txt \
                    output - \
                    > ../pdf-supplement/"${filename}.pdf" 2>&1

            done
            echo "$(date +%H:%M:%S) Ending"
        else
            progress "Skipping PDF Conversion as Requested"
        fi

    ) || die "Failed during Proxy Invoice generation"

    # Create one or more PDF bundles from the individual PDF files
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        (
            cd "$TARGET_DIR"/pdf-supplement
            mkdir ../bundle-supplement
            BUNDLE_PREFIXES=$(find . -mindepth 1 -maxdepth 1 -type f -exec basename {} \; | perl -ale 'print $1 if /(\w{0,}\d+)\d{2}\w{0,}.pdf/;' | sort | uniq)
            for prefix in $BUNDLE_PREFIXES; do
                FIRST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $1}')
                LAST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $NF}')
                FIRST=${FIRST_TMP::-4}
                LAST=${LAST_TMP::-4}
                pdftk ./"${prefix}"*.pdf cat output ../bundle-supplement/bundle-"${FIRST}-${LAST}".pdf
            done
        )
    fi
    say "Done Creating Proxy Version 2 Invoices"
else
    die "Invalid Proxy Version"
fi
