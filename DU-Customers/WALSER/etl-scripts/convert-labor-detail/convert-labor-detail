#!/usr/bin/env bash

set -e

TMP_DIR='/tmp/du-etl-transform-walser/'
mkdir -p "$TMP_DIR"
rm -rf "$TMP_DIR"/*
mkdir -p "$TMP_DIR"/output

WORK_FILE="$TMP_DIR"/labor-detail-work-file.txt

cp "$1" "$WORK_FILE"
dos2unix "$WORK_FILE"

# !!!!!!!!!!!!!!!!! Assumes that STORE is 5 characters...which is not good but solves the immediate problem for BGB-S

PERL_PROG=$(cat <<'PROG'
    BEGIN {
        use Text::Trim;

        print "VEH-ID.. STORE..... TYPE LABOR\$.. STORE..... SA. STORE..... TECH. LBR-COST\$.. STORE..... OP-CODE SOLD-HOURS STORE..... RO....";
        print "";
    }
    my ($vehid,$type,$labor,$store1,$sa,$tech,$lbrcost,$store2,$opcode,$soldhrs,$store3,$ronum) = unpack "A8A5A9A6A9A6A13A6A13A11A6A12";
    printf("%-9.9s%-5.5s%10.10s%9.9s %-5.5s%9.9s %-5.5s%11.11s%12.12s %-5.5s%13.13s%11.11s %-5.5s%12.12s\n",
           trim($vehid),
           trim($store1),
           trim($type),
           trim($labor),
           trim($store1),
           trim($sa),
           trim($store1),
           trim($tech),
           trim($lbrcost),
           trim($store1),
           trim($opcode),
           trim($soldhrs),
           trim($store1),
           trim($ronum),
           ) if (/(?!^\s*$)/ && ! /(?=VEH-ID)/ && ! /^\?/);
PROG
)

perl -ln -e "$PERL_PROG" "$WORK_FILE"
