#!/usr/bin/env bash

wc -l ./source/an-labor-opportunity-analysis-20160808-client-supplied-data.csv

psql "service=solve360" <<SQL
BEGIN;
CREATE SCHEMA IF NOT EXISTS du_customer_an;

SET search_path TO solve360_rules, solve360_api, middletier_solve360_data;
DROP VIEW IF EXISTS du_customer_an.ebis_solve360_map;
DROP TABLE IF EXISTS du_customer_an.labor_viability_20160808 CASCADE;
CREATE TABLE du_customer_an.labor_viability_20160808 AS
    SELECT *
      FROM (
            SELECT project_id, project_name,
                   (labor_viability(s360_project_labor)).*,
                   (COALESCE(labor_whr_stated_prioryear, labor_whr_stated_ytd) * 12)::integer AS labor_whr_stated,
                   uplift_warranty_rate_type(s360_project_labor),
                   CASE WHEN s360_project_labor.labor_whr_calculation_basis = 'Calculated Data'  THEN 'Computed'
                        WHEN s360_project_labor.labor_whr_calculation_basis = 'Cust - Prev. Yr.' THEN 'Cust. PY'
                        WHEN s360_project_labor.labor_whr_calculation_basis = 'Cust - YTD'       THEN 'Cust. YTD'
                        ELSE CASE WHEN s360_project_labor.labor_whr_stated_prioryear IS NOT NULL THEN 'Selected PY'
                                  WHEN s360_project_labor.labor_whr_stated_mean IS NOT NULL      THEN 'Selected Mean'
                                  WHEN s360_project_labor.labor_whr_stated_ytd IS NOT NULL       THEN 'Selected YTD'
                                  WHEN s360_project_labor.labor_whr_calculated IS NOT NULL       THEN 'Selected Calc'
                                  ELSE 'None'
                              END
                    END AS monthly_warranty_hours_source
              FROM middletier_solve360_data.s360_project_labor
              JOIN middletier_solve360_data.s360_project_header USING (project_id)
             WHERE project_name ~ 'AutoNation\s'
           ) compute
     WHERE hours > 0;  -- ignore incomplete labor records so that the secondary project numbers can be used if avaiable.

DROP TABLE IF EXISTS du_customer_an.labor_op_analysis_20160808;
CREATE TABLE du_customer_an.labor_op_analysis_20160808 (
    region                              text,
    store                               text NOT NULL,
    extract                             text NOT NULL,
    dealer_name                         text NOT NULL,
    lri_program                         text,
    initial_increase_on                 date,
    initial_rate                        numeric(10,2),
    elr_mgr_wlri_start_on               date,
    last_increase_date                  date,
    last_increase_rate_final            numeric(10,2),
    last_increase_rate_change           numeric(10,2),
    last_increase_amount                money,
    next_increase_on                    date,
    most_recent_analysis_on             date,
    most_recent_analysis_rate           numeric(10,2),
    analysis_rate_increase_from_last    numeric(10,2),
    analysis_amount_increase_from_last  money,
    cumulative_impact                   money,
    market_survey_required              text,
    auto_increase_expires_on            date,
    PRIMARY KEY (store, extract, lri_program)
);

\copy du_customer_an.labor_op_analysis_20160808 FROM './source/an-labor-opportunity-analysis-201603-client-supplied-data.csv' WITH (FORMAT csv, HEADER true, DELIMITER E'\t')
--\copy du_customer_an.labor_op_analysis_20160808 FROM './source/an-labor-opportunity-analysis-20160808-client-supplied-data.csv' WITH (FORMAT csv, HEADER true, DELIMITER E'\t')


ALTER TABLE du_customer_an.labor_op_analysis_20160808
    ADD COLUMN delay_calc text;

ALTER TABLE du_customer_an.labor_op_analysis_20160808
    ADD COLUMN id serial NOT NULL;

UPDATE du_customer_an.labor_op_analysis_20160808
   SET delay_calc =    CASE WHEN next_increase_on >= most_recent_analysis_on
                            THEN 'Future'
                            WHEN analysis_rate_increase_from_last < 0
                            THEN 'Not Viable'
                            WHEN most_recent_analysis_on - next_increase_on > (365 * 4)
                            THEN 'Long Time'
                            WHEN most_recent_analysis_on - next_increase_on BETWEEN (365 * 3.5) AND (365 * 4.0)
                            THEN '8 Halves'
                            WHEN most_recent_analysis_on - next_increase_on BETWEEN (365 * 3.0) AND (365 * 3.5)
                            THEN '7 Halves'
                            WHEN most_recent_analysis_on - next_increase_on BETWEEN (365 * 2.5) AND (365 * 3.0)
                            THEN '6 Halves'
                            WHEN most_recent_analysis_on - next_increase_on BETWEEN (365 * 2.0) AND (365 * 2.5)
                            THEN '5 Halves'
                            WHEN most_recent_analysis_on - next_increase_on BETWEEN (365 * 1.5) AND (365 * 2.0)
                            THEN '4 Halves'
                            WHEN most_recent_analysis_on - next_increase_on BETWEEN (365 * 1.0) AND (365 * 1.5)
                            THEN '3 Halves'
                            WHEN most_recent_analysis_on - next_increase_on BETWEEN (365 * 0.5) AND (365 * 1.0)
                            THEN '2 Halves'
                            WHEN most_recent_analysis_on - next_increase_on BETWEEN (365 * 0.0) AND (365 * 0.5)
                            THEN '1 Half'
                            ELSE '???'
                        END;

SELECT count(*) FROM du_customer_an.labor_op_analysis_20160808;

\x
SELECT * FROM du_customer_an.labor_op_analysis_20160808 LIMIT 1;

CREATE VIEW du_customer_an.ebis_solve360_map AS
    SELECT id,
           primary_project_id,
           secondary_project_id,
           CASE WHEN id IS NULL
                THEN 'Unmatched Solve360'
                WHEN most_recent_analysis_on IS NULL
                THEN 'Incomplete EBIS'
                WHEN an_store_number IS NULL
                THEN 'Unmatched EBIS'
                WHEN (SELECT count(*)
                        FROM du_customer_an.labor_viability_20160808 AS lv
                       WHERE lv.project_id = primary_project_id
                             OR lv.project_id = secondary_project_id) = 0
                THEN 'Incomplete Solve360'
                ELSE 'Matched'
            END AS matching_disposition
      FROM (
            SELECT '2' || s360_an_store_number AS an_store_number,
                   max(primary_project_id)     AS primary_project_id,
                   max(secondary_project_id)   AS secondary_project_id
              FROM (
                    SELECT project_id,
                           project_name,
                          (SELECT regexp_matches(project_name, '^.*\[.*-?(\d{3})\].*'))[1] AS s360_an_store_number,
                           project_type,
                           CASE WHEN project_type = 'Labor UL'
                                THEN project_id
                                ELSE NULL
                            END                                        AS primary_project_id,
                           CASE WHEN project_type = 'Labor UL'
                                THEN NULL
                                ELSE project_id
                            END                                        AS secondary_project_id
                      FROM middletier_solve360_data.s360_project_header
                      JOIN middletier_solve360_data.s360_project_base USING (project_id)
                     WHERE project_name ~ 'AutoNation\s'
                           AND EXISTS (SELECT 1
                                         FROM du_customer_an.labor_viability_20160808
                                        WHERE labor_viability_20160808.project_id = s360_project_header.project_id)
                   ) prj
          GROUP BY an_store_number
           ) solve360
 FULL JOIN (
            SELECT id, store, extract, lri_program, dealer_name, most_recent_analysis_on
              FROM du_customer_an.labor_op_analysis_20160808
           ) ebis
           ON (store = an_store_number);
;

COMMIT;

SQL
