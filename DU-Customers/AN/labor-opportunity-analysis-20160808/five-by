#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
WORK_DIR=/tmp/du-etl-customer-an-laboranalysis/five-by/  # manual sync need in the SQL due to using 'SQL' heredoc start
EXTERNAL_DIR=/vagrant/etl-script-work/

clear_dir "$WORK_DIR"

psql "service=solve360" <<'SQL'
DROP TABLE IF EXISTS du_customer_an.matched_projects;
DROP TABLE IF EXISTS du_customer_an.fiveby;

CREATE TABLE du_customer_an.matched_projects AS
    SELECT s360_project_header.project_name,
           s360_project_base.manufacturerid,
           COALESCE(array_to_string(lpp.followups, '; '), 'Secondary')                 AS followup_reasons,
           COALESCE('Secondary - ' || ppp.secondary_status,
                    replace(array_to_string((lpp.project_cti_retraction).subtype_labels,
                                            '; '),
                            'Status: Inactive - ' ,
                            ''))                                                       AS retraction_reasons,
           not(solve360_rules.is_empty(lpp.project_cti_retraction))                    AS is_retracted,
           lpp.start_on AS labor_started_on,
           lpp.shipped_on AS labor_shipped_on,
           lpp.approved_on AS labor_approved_on,
           ppp.start_on AS secondary_started_on,
           ppp.secondary_deadline,
           matched.*,
           (lbr_viability).hours * 12                                                   AS solve_warr_hours,
           (lbr_viability).existing_rate::numeric(10,2)                                 AS solve_warr_rate,
           (lbr_viability).monthly_warranty_hours_source,
           (lbr_viability).uplift_warranty_rate_type,
           last_increase_rate_final::numeric(10,2)                                      AS ebis_warr_rate,
           (lbr_viability).uplift_rate::numeric(10,2)                                   AS solve_uplift_rate,
           most_recent_analysis_rate::numeric(10,2)                                     AS ebis_uplift_rate,
           round(((lbr_viability).uplift_rate
                      - (lbr_viability).existing_rate)
                    * ((lbr_viability).hours * 12),
                 0)                                                                     AS solve_profit,
           (json_object->>'custom17626657')::numeric(10,2)                              AS solve_best_rate,
           COALESCE(whr_stated,
                    (lbr_viability).hours * 12,
                    analysis_hours)                                                     AS input_warr_hours

      FROM (
            SELECT ebis.*,
                   (COALESCE(pph.labor_viability_20160808,
                             sph.labor_viability_20160808))                             AS lbr_viability,
                   COALESCE(pph.labor_whr_stated,
                            sph.labor_whr_stated)                                       AS whr_stated,
                   CASE WHEN analysis_rate_increase_from_last = 0
                        THEN 0
                        WHEN analysis_rate_increase_from_last IS NULL
                        THEN 0
                        ELSE (analysis_amount_increase_from_last
                                / analysis_rate_increase_from_last)::numeric
                    END::integer                                                        AS analysis_hours,
                   CASE WHEN last_increase_rate_change = 0
                        THEN 0
                        WHEN last_increase_rate_change IS NULL
                        THEN 0
                        ELSE (last_increase_amount
                                / last_increase_rate_change)::numeric
                    END::integer                                                        AS last_increase_hours,
                   CASE WHEN NOT (pph.labor_viability_20160808 IS NULL)
                        THEN 'Labor'
                        WHEN NOT (sph.labor_viability_20160808 IS NULL)
                        THEN 'Parts'
                        ELSE NULL
                    END                                                                 AS viability_calculation_source,
                   pph.project_name                                                     AS labor_project,
                   sph.project_name                                                     AS parts_project
              FROM (SELECT *
                      FROM du_customer_an.ebis_solve360_map
                     WHERE matching_disposition = 'Matched'
                   ) AS main
              JOIN du_customer_an.labor_op_analysis_20160808 AS ebis USING (id)
         LEFT JOIN (SELECT project_id, s360_project_header.project_name, labor_viability_20160808, labor_whr_stated
                      FROM middletier_solve360_data.s360_project_header
                      JOIN du_customer_an.labor_viability_20160808 USING (project_id)
                   ) pph ON (primary_project_id = pph.project_id)
         LEFT JOIN (SELECT project_id, s360_project_header.project_name, labor_viability_20160808, labor_whr_stated
                      FROM middletier_solve360_data.s360_project_header
                      JOIN du_customer_an.labor_viability_20160808 USING (project_id)
                   ) sph ON (secondary_project_id = sph.project_id)
           ) matched
      JOIN middletier_solve360_data.s360_project_raw    ON ((lbr_viability).project_id = s360_project_raw.id)
      JOIN middletier_solve360_data.s360_project_header ON ((lbr_viability).project_id = s360_project_header.project_id)
      JOIN middletier_solve360_data.s360_project_base   ON ((lbr_viability).project_id = s360_project_base.project_id)
 LEFT JOIN (
            SELECT *
              FROM core.primary_project
              JOIN solve360_rules.s360_project_tag_cti USING (project_id)
              WHERE project_type = 'Labor UL'
           ) lpp ON ((lbr_viability).project_id = lpp.project_id)
 LEFT JOIN (
            SELECT *
              FROM core.primary_project
              JOIN solve360_rules.s360_project_tag_cti USING (project_id)
             WHERE project_type = 'Parts UL' ANd calculate_secondary
          ) ppp ON ((lbr_viability).project_id = ppp.project_id)
;

CREATE TABLE du_customer_an.fiveby AS
    SELECT id,
           dealer_name,
           store,
           extract,
           lri_program,
           CASE WHEN COALESCE(labor_started_on,
                              secondary_started_on) - last_increase_date <= 0
                THEN NULL
                ELSE COALESCE(labor_started_on,
                              secondary_started_on) - last_increase_date
           END / 30                                                            AS mth_since_last_inc,
           /* End of EBIS Header */
           CASE WHEN manufacturerid IN ('Toyota','Audi','Lexus','Mercedes')
                THEN 'Special Manufacturer'
                WHEN labor_approved_on IS NOT NULL
                THEN 'Approved'
                WHEN is_retracted AND NOT (retraction_reasons ~ 'Not Viable')
                THEN 'Not Approved'
                WHEN labor_shipped_on IS NOT NULL
                THEN 'Shipped'
                ELSE CASE WHEN current_date - last_increase_date < 360
                          THEN 'Increased w/i 12m'
                          ELSE CASE WHEN solve_profit > (500 * 12)
                                    THEN 'Viable'
                                    ELSE 'Not Viable'
                                END
                      END
            END                                                                AS five_dispo,
           CASE WHEN NULLIF(retraction_reasons, '') IS NOT NULL
                THEN retraction_reasons
                WHEN NULLIF(followup_reasons, '') IS NOT NULL
                THEN followup_reasons
                WHEN labor_approved_on IS NOT NULL
                THEN 'Approved: ' || labor_approved_on
                ELSE '*Unresolved*'
            END                                                                AS disposition,
           CASE WHEN labor_approved_on IS NOT NULL
                THEN solve_profit
                ELSE NULL
            END                                                                AS approved_uplift,
           CASE WHEN labor_approved_on IS NOT NULL
                THEN NULL
                WHEN solve_uplift_rate < solve_warr_rate
                THEN NULL
                ELSE solve_profit
             END                                                               AS unrealized_profit,

           labor_started_on,
           labor_shipped_on,

           secondary_started_on,
           secondary_deadline,

           -- Identify "Not Viable" from Secondary Projects

           solve_warr_hours,
           solve_warr_rate,
           solve_uplift_rate,

           solve_uplift_rate - solve_warr_rate                                 AS solve_uplift_increase,

           uplift_warranty_rate_type,
           monthly_warranty_hours_source,

           solve_best_rate,
           solve_uplift_rate - ebis_uplift_rate                                AS ebis_diff,
           ebis_uplift_rate,
           project_name,

           (lbr_viability).project_id
      FROM du_customer_an.matched_projects
      JOIN (
            SELECT project_id, project_notes, deadline_notes, punch_list_notes, json_object->>'custom9018194' AS impact_notes
              FROM middletier_solve360_data.s360_project_note
              JOIN middletier_solve360_data.s360_project_raw ON (project_id = id)
           ) notes ON ((lbr_viability).project_id  = project_id)
      WHERE region ~ 'Eastern'

    UNION ALL

    SELECT id, dealer_name, store, extract, lri_program,
           (most_recent_analysis_on::date - last_increase_date) / 30,
           CASE WHEN lri_program ~* 'Toyota|Audi|Lexus|Mercedes'
                THEN 'Special Manufacturer'
                ELSE CASE WHEN current_date - last_increase_date < 360
                          THEN 'Increased w/i 12m'
                          ELSE 'Unknown Potential'
                      END
            END AS five_dispo,
           'Never Worked',
           NULL,
           NULL, NULL,
           NULL, NULL, NULL, NULL,
           NULL, NULL, NULL, NULL,
           NULL, NULL, NULL, NULL,
           NULL, NULL
      FROM du_customer_an.labor_op_analysis_20160808 ebis
     WHERE NOT EXISTS (SELECT 1 FROM du_customer_an.matched_projects mp WHERE ebis.id = mp.id)
           AND region ~ 'Eastern'

    ORDER BY lri_program, labor_started_on, secondary_started_on;

\copy du_customer_an.fiveby TO /tmp/du-etl-customer-an-laboranalysis/fiveby.csv WITH (FORMAT csv, HEADER true)

SQL

unix2dos "$WORK_DIR"/fiveby.csv

cp "$WORK_DIR"/fiveby.csv \
   "$EXTERNAL_DIR"/fiveby.csv
