#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

set -e

TMP_DIR='/tmp/du-etl-transform-bhautomotive/'
mkdir -p "$TMP_DIR"
rm -rf "$TMP_DIR"/*
mkdir -p "$TMP_DIR"/output

WORK_DIR='/mnt/du-etl-work/etl-cdk/bha-work/work'
OUTPUT_DIR='/mnt/du-etl-work/etl-cdk/cdk-zip/'

if [[ ! -f "$WORK_DIR"/AllPrefix-COA-FromMaster.txt ]]; then
    die "Please place a copy of AllPrefix-COA-FromMaster.txt into $WORK_DIR"
fi

PERL_PDA_PROG=$(cat <<'PROG'
    BEGIN {
        use Date::Manip qw(ParseDate UnixDate);
        use Text::Trim;

        print "    Refer# Sale Type  T-Date    Cust#              Part# Desc                Qty    Cost     Sale   So  Sale Acct";
        print "---------- ---------- --------- ------- ---------------- ------------------ ---- ------- -------- ---- ----------";
    }
    my ($inv,$a,$st,$b,$td,$c,$cust,$pn,$d,$pd,$qty,$cost,$sale,$src,$acct) = unpack "A10A1A9A2A7A1A8A17A1A19A8A8A9A5A20";
    printf("%10.10s %-10.10s %9.9s %-7.7s %16.16s %-18.18s %4.4s%8.8s%9.9s%5.5s%11.11s\n",
           trim($inv),
           trim($st),
           UnixDate(ParseDate($td),"%m/%d/%y"),
           trim($cust),
           trim($pn),
           trim($pd),
           trim($qty),
           trim($cost),
           trim($sale),
           trim($src),
           trim($acct)
           ) if /^.*RO\s{8}/ ;
PROG
)

DETAIL_FILE=$(find "$WORK_DIR" -name '*PTS*')
LOOKUP_FILE=$(find "$WORK_DIR" -name '*SVC*')

(
    cd "$TMP_DIR"
    csplit "$LOOKUP_FILE" -n 1 -f 'bhautomotive-lookup-' \
        '/ITEMS LISTED/'

    tr '\014' '\n' < bhautomotive-lookup-0 > "$TMP_DIR"/output/bhautomotive-billingcodes.txt
    tr '\014' '\n' < bhautomotive-lookup-1 > "$TMP_DIR"/output/bhautomotive-opcode_master.txt

    csplit "$DETAIL_FILE" -n 1 -f 'bhautomotive-detail-' \
        '/RO\.\#\./'                      \
        '/VEH-ID\.\./'                    \
        '/INVOICE\.\.\./'

    tr '\014' '\n' < bhautomotive-detail-0 > "$TMP_DIR"/output/bhautomotive-invoicemaster.txt
    tr '\014' '\n' < bhautomotive-detail-1 > "$TMP_DIR"/output/bhautomotive-openros.txt
    tr '\014' '\n' < bhautomotive-detail-2 \
        | grep -v 'PAGE    1' \
        > "$TMP_DIR"/output/bhautomotive-labordetail.txt
    tr '\f' '\n' < bhautomotive-detail-3 > "$TMP_DIR"/output/bhautomotive-partdetail.txt

    perl -ln -e "$PERL_PDA_PROG" "$TMP_DIR"/output/bhautomotive-partdetail.txt \
                                 > "$TMP_DIR"/output/bhautomotive-partdetail-reformed.txt

    tail -n +60 < "$TMP_DIR"/output/bhautomotive-partdetail-reformed.txt | head -n 10
    head "$TMP_DIR"/output/bhautomotive-labordetail.txt

    rm "$TMP_DIR"/output/bhautomotive-partdetail.txt
) || {
    echo "Processing Failed"
    exit 1
}

zip -j "$OUTPUT_DIR"/BHA-"$(basename $DETAIL_FILE)".zip "$TMP_DIR"/output/* "$WORK_DIR"/AllPrefix-COA-FromMaster.txt

rm "$DETAIL_FILE"
rm "$LOOKUP_FILE"

exit

# find . -type f -print \
#      | xargs -I {} sh -c 'cat $1 | tr "\r" "*" | grep -P -B 4 -A 3 PAGE | grep -P -B 4 -A 3 "\d38[067]\d\d\*" | unix2dos > ../PDA-Converted/"${1%.*}-retailmissing.txt' -- {}


