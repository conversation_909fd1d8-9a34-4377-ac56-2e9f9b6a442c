#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

DIR_DROPBOX=/mnt/du-etl-work/etl-cdk/bha-work/download
#DIR_ETI="${DU_ETL_ETI_DIR_CDK:?ETI Directory Specification Required}"
DIR_BUNDLE_ARCHIVE="$DIR_DROPBOX"/bundled
DIR_BUNDLE_DROPOFF=/mnt/du-etl-work/etl-cdk/bha-work

[[ -d "$DIR_DROPBOX" ]] || die "Dropbox Must Exist @ $DIR_DROPBOX"
#[[ -d "$DIR_ETI" ]]     || die "ETI Directory Must Exist @ $DIR_ETI"

mkdir -p "$DIR_BUNDLE_ARCHIVE"
mkdir -p "$DIR_BUNDLE_DROPOFF"

function perform_build() {

FIRST_DROPBOX_FILE="$(
    find $DIR_DROPBOX -maxdepth 1 -regextype 'posix-extended' -regex '.*(SVC|PTS).*' -print | sort --reverse | head -n 1
)"

echo "$FIRST_DROPBOX_FILE"

if [[ ! -f "$FIRST_DROPBOX_FILE" ]]; then
    say "No Files to Process"
    exit 0
fi

PERL_ZIP_NAME_PARSE_PROG="$(cat <<'EOF'
    if (m~/(\w+)(SVC|PTS)\d*\.\w{3}~) {
        $name = $1;
        $name =~ s/ /_/g;
        print $name . '.zip';
    } else {
        die "Not a valid zip file name";
    }
EOF
)"

PERL_MATCH_NAME_PARSE_PROG="$(cat <<'EOF'
    if (m~/(\w+)(SVC|PTS)\d*\.\w{3}~) {
        print $1;
    } else {
        die "Not a valid base file name";
    }
EOF
)"

ZIP_FILE_NAME=$(echo "$FIRST_DROPBOX_FILE" | perl -alne "$PERL_ZIP_NAME_PARSE_PROG")
FILE_MATCHER_PREFIX=$(echo "$FIRST_DROPBOX_FILE" | perl -alne "$PERL_MATCH_NAME_PARSE_PROG")

echo "$ZIP_FILE_NAME"
echo "$FILE_MATCHER_PREFIX"

cp -v "$DIR_DROPBOX"/"$FILE_MATCHER_PREFIX"PTS* "$DIR_DROPBOX"/"$FILE_MATCHER_PREFIX"SVC* "$DIR_BUNDLE_ARCHIVE"

zip -vjm "$DIR_BUNDLE_DROPOFF"/"$ZIP_FILE_NAME" "$DIR_DROPBOX"/"$FILE_MATCHER_PREFIX"PTS* "$DIR_DROPBOX"/"$FILE_MATCHER_PREFIX"SVC*

unzip "$DIR_BUNDLE_DROPOFF"/"$ZIP_FILE_NAME" -d "$DIR_BUNDLE_DROPOFF"/work/

./split-and-reformat-rawdata

mv -v "$DIR_BUNDLE_DROPOFF"/"$ZIP_FILE_NAME" "$DIR_BUNDLE_DROPOFF"/processed/

}

mkdir -p "$DIR_BUNDLE_DROPOFF"/processed/

while true;
do
    NUMBER_OF_FILES=$(wc -l < <(find $DIR_DROPBOX -maxdepth 1 -type f))
    if [[ $NUMBER_OF_FILES = 0 ]]; then
        say "Done! - No Files Left To Process"
        exit 0
    elif (( NUMBER_OF_FILES % 2 == 0 )); then
        perform_build
    else
        die "Directory $DIR_DROPBOX must contain an even number of files - looking for SVC|PTS pairs ($NUMBER_OF_FILES files found)"
        # two wrongs will make a write but going to stick with this simple check for now
    fi
done;

exit
