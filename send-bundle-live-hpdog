#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

ETLDIR=/etl/etl-vagrant/etl-fortellis/fortellis-zip
DISTDIR="$HOME"/armatus-dropbox/Imports
ARCHIVEDIR="$HOME"/tmp/du-etl-dms-fortellis-extractor-work/manual/processed-archive

# Debug targets
if [ "${DU_ETL_DEBUG_MODE:=0}" -gt 0 ]; then
    ETLDIR="$HOME"/tmp/du-etl-dms-fortellis-extractor-work/manual/etl
fi
DISTDIR="$HOME"/tmp/du-etl-dms-fortellis-extractor-work/manual/dist
mkdir -p "$ETLDIR" "$DISTDIR"

mkdir -p "$ARCHIVEDIR" || die "Failed to create archive directory"

[[ -d "$DISTDIR" ]] || die "Distribution Directory Does Not Exist (mounted?)"
[[ -d "$ETLDIR"  ]] || die "ETL Directory Does Not Exist (mounted?)"

./send-bundle-dump-to-etl \
    "${1:?Zip File Must Be Specified}" \
    "$ETLDIR" \
    "$DISTDIR" \
    || die "Failed during Distribution"

progress "Moving Bundle to Archive"

mv --force "$1" "$ARCHIVEDIR"/ || die "Moving to Archive Failed"

if type send-email >/dev/null 2>&1; then
    progress "Sending Email"
    file_name_only="$(basename $1)"
    send-email --subject "Fortellis Ready to Import: $file_name_only" \
               --body "Extraction Completed, Processed, and Ready to Import:\n\n${file_name_only}\n" \
               --to '<EMAIL>' \
               --send
else
    yell "Email sending skipped, send-email command not found"
fi
