#!/usr/bin/env bash

# Reload agent

URL=http://tomcat.aws.production.dealeruplift.net/gogetterservices/login
TMP_OUT="$HOME/tmp/agent-reload.html"

USERNAME="ggsmaint%40PIMS"
PASSWORD=" maintbot2017yodel"

while [ $# -gt 0 ]; do
    case "$1" in
        --username) USERNAME="${2/@/%40}"; shift 2;;
        --password) PASSWORD="${2}";       shift 2;;
        --url)      URL="${2}";            shift 2;;
        *) echo "Unrecognized option ${1}" exit 1;;
    esac
done

LOGON_FORM="useridentifier=$USERNAME\
&userkey=$PASSWORD\
&logonsubmit=Logon\
&clientname=webclient\
&clientkey=webclient\
&clientdomain=PIMS"

# Get logon page
curl --connect-timeout 10 $URL/index > $TMP_OUT 2>&1
status=$?
if [ $status -ne 0 ] ; then 
    echo "Can't access application: $status"
    exit 1
fi

# Logon
curl -L --data $LOGON_FORM $URL/agentlogon > $TMP_OUT 2>&1
html=$(<$TMP_OUT)

# Get agent session id
[[ $html =~ (agentsessionid=[a-z0-9]*)\" ]]
agentsessionid=${BASH_REMATCH[1]}

# Get Administration page
curl -L $URL/storegroupadministration?$agentsessionid > $TMP_OUT 2>&1

# Reload
curl -L --data "$agentsessionid&command=Agent%20Reload" $URL/storegroupadministration > $TMP_OUT 2>&1
 
# Logoff
curl -L --data "$agentsessionid&command=Logoff" $URL/agentlogoff > $TMP_OUT 2>&1

