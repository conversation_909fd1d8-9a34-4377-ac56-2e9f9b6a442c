#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Time::Format qw(%time %strftime %manip);
use POSIX qw/ceil/;

no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];
my $is_porsche_store =  $ARGV[1];
my $dealer_address = $ARGV[2]; 

# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;

my @header_section;
my @job_section;
my @grand_total_section;
my @end_of_invoice_section;
my @invoice_note_section;

my ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale, $customer_paid_amount )= (0)x12;

my ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

my ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

my ($sale_amount, $labor_cost, $parts_cost, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

my $job_hours = 0;
my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;

my ($parts_entries, $misc_entries, $gog_entries, $sublet_entries, $ded_entries) = (0)x5;
my $lbr_type;
my $page_max_height = 50; # Maximum no of lines in page body
my $page_header_height = 9; # No of lines in header section
my $invoice_dealer_header = 6; # No of lines in the dealer header section (1st page)
my $total_header_height;
my $curr_page_height = 0;
my $invoice_note_lines = 22; # No of lines needed for invoice note
my $pages;
my $curr_page_num;
my $invoice_datetime;
my $cust_inv_head;

my $story_number;
my $ro_qry = $conn->prepare("SELECT ro_number
                               FROM repair_order
                              ORDER BY ro_number");

$ro_qry->execute();
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number) = @ro_list;
    my $file = $ro_number . ".txt";
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;

    my $file_inv_head = "inv-bg/".$ro_number . "-inv-bg.txt";
    unless(open FILE, '>'.$file_inv_head) {
        die "\nUnable to create $file_inv_head\n";
    }
    print_ro_inv_bg_head();
    close FILE;

    load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
    my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
    $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
    $ro_doc_insert->finish;
}

# Subroutine to prepare HEADER section of the invoice
sub make_header {
    my $ro_header_qry = $conn->prepare("SELECT
                                to_char(ro.creation_date, 'mm/dd/yy') || ' ' || to_char(ro.open_time, 'HH24:MI') AS creation_date,
                                to_char(ro.completion_date, 'mm/dd/yy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                UPPER(ro.advisor),
                                --ro.terms,
                                REPLACE(ro.terms, '_', ' '),
                                ro.estimate_amount,
                                roc.customer_number,
                                roc.customer_name,
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip,
                                '(' || SUBSTRING(cell_phone, 1, 3) || ')' || SUBSTRING(cell_phone, 4,3) ||
                                    '-' || SUBSTRING(cell_phone,7,4) AS cell_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                rov.vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in,
                                rov.engine_trans,
                                --SUBSTRING(rov.vehicle_color, 1, 9) AS vehicle_color,
                                rov.vehicle_color AS vehicle_color,
                                rov.mileage_out,
                                roc.license_number,
                                ro.tag_no,
                                to_char(ro.delivery_date, 'ddMONyy') AS delivery_date,
                                to_char(ro.promised_time, 'HH24:MI') || ' ' || to_char(ro.promised_date, 'ddMONyy') AS promised,
                                to_char(ro.booked_time, 'HH24:MI') || ' ' || to_char(ro.booked_date, 'ddMONyy') AS ready,
                                ro.payment_code,
                                ro.comments,
                                ro_ext,
                                status,
                                roc.control_number,
                                to_char(in_service_date, 'mm/dd/yy') AS in_service_date,
                                '(' || SUBSTRING(work_phone, 1, 3) || ')' || SUBSTRING(work_phone, 4,3) ||
                                    '-' || SUBSTRING(work_phone,7,4) AS work_phone,
                                '(' || SUBSTRING(home_phone, 1, 3) || ')' || SUBSTRING(home_phone, 4,3) ||
                                    '-' || SUBSTRING(home_phone,7,4) AS home_phone,
                                nullif(delivery_miles, 0) AS delivery_miles,
                                rov.stock_number,
                                to_char(ro.lastModified::timestamp, 'mm/dd/yy HH24:MI')
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
    my ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor, $terms, $estimate_amount, $customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $cell_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $engine_trans, $vehicle_color, $mileage_out, $license_number, $tag_no, $delivery_date, $promised, $ready, $payment_code, $comments, $ro_ext, $status,
        $control_number, $in_service_date, $work_phone, $home_phone, $delivery_miles, $stock_number, $lastModified ) = @header_row;

    if ($lastModified eq '') {
        $invoice_datetime = $creation_date;
    }
    else{
        $invoice_datetime = $lastModified;
    }

    if ($status eq 'COMPLETED') {
        $status = 'COMPLETE';
    }
    $cust_inv_head = $ro_ext;
    $customer_paid_amount = $estimate_amount;
    if($is_porsche_store eq 'true'){
        $vehicle_vin=substr($vehicle_vin, 0, 12);
        $work_phone = '';
        $home_phone = '';
        $customer_name ='';
        $customer_address = '';
        $customer_city = '';
        $customer_zip = '';
        $customer_state = '';
        $customer_email = '';
    }
    my $vehicle_details;
    if ($vehicle_color) {
        $vehicle_details = $vehicle_year." ".$vehicle_make." ".$vehicle_model."(".$vehicle_color.")";
    } else {
        $vehicle_details = $vehicle_year." ".$vehicle_make." ".$vehicle_model;
    }   
    push (@header_section, sprintf(border(">")."%-28s %-66s".border("|")."\n", substr($customer_name, 0, 28), ""));
    push (@header_section, sprintf(border(">")."%-28s ".'~font{DejaVuSansMono-Bold10}'."%-18s".'~font{default}'." %-6s %-6s %-14s %-8s "
                                              .'~font{Helvetica-Bold10}'."%-9s".'~font{default}'.border("|")."\n",
                                   substr($customer_address, 0, 28),
                                   align_center($vehicle_vin, 18),
                                   align_center($mileage_in, 6),
                                   align_center($mileage_out, 6),
                                   align_center($creation_date, 14),
                                   align_center($completion_date, 8),
                                   align_center($ro_number." EOD", 9)));
    push (@header_section, sprintf(border(">")."%-28s %-66s".border("|")."\n",
                                   substr($customer_city.", ".$customer_state." ".$customer_zip, 0, 28), ""));
    push (@header_section, sprintf(border(">")."%-28s %-40s ".'~font{DejaVuSansMono-Bold10}'."%-6s %-18s".'~font{default}'.border("|")."\n",
                                   substr($customer_email, 0, 28),
                                   $vehicle_details,
                                   align_center($tag_no, 6),
                                   align_center($status, 18)));
    push (@header_section, sprintf(border(">")."%-95s".border("|")."\n", ""));
    $story_number = $comments;
    $terms = lc($terms);
    $terms =~ s/(?:^|(?<=\s))(\w)/\U$1/g;
   
    push (@header_section, sprintf(border(">")."%-14s %-14s %-10s %-8s %-9s %-8s %-9s %-14s".border("|")."\n",
                                   align_center($control_number, 14),
                                   align_center($license_number, 14),
                                   "", "",
                                   align_center($in_service_date, 9),
                                   "",
                                   align_center($delivery_miles, 9), 
                                   align_center($terms,14)));
    push (@header_section, sprintf(border(">")."%-95s".border("|")."\n", ""));

    push (@header_section, sprintf(border(">")."%-14s %-14s%-14s %8s %-23s  %-18s".border("|")."\n",
                                  align_center($home_phone, 14),
                                  align_center($work_phone, 14),
                                  align_center($cell_phone, 14), 
                                  align_center($stock_number, 8),
                                  align_center($advisor, 23),
                                  align_center($engine_trans,18)));
    push (@header_section, sprintf(border(">")."%-95s".border("|")."\n", ""));

    # push (@header_section, sprintf(border(">")."%-9s %-12s  %70s".border("|")."\n",
    #                                        "Story", $story_number, ""));
    # push (@job_header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {
    my $job_qry = $conn->prepare("SELECT
                                    rl.ro_line,
                                    rl.complaint,
                                    rj.ro_job_line,
                                    left(rj.billing_code, 1) AS billing_code,
                                    rj.billing_code          AS billing_labor_type,
                                    CASE WHEN rj.op_code = 'NO-OP' THEN NULL ELSE rj.op_code END,
                                    rj.op_description,
                                    rj.sold_hours,
                                    rj.labor_cost,
                                    rj.sale_amount,
                                    rj.cause,
                                    rj.correction,
                                    rl.origin
                                FROM repair_line rl
                                    JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ? ORDER BY rl.ro_line ASC");

    $job_qry->execute($ro_number);

    # my @job_header_section;

    # push (@job_header_section, sprintf(border(">")."%-9s %-12s  %62s".border("|")."\n",
                                        #    "Story", $story_number, ""));
    # push (@job_header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
 

    my ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours, $cause, $correction, $add_on_flag );
    while (my @job_row = $job_qry->fetchrow_array) {
        ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours, $labor_cost, $sale_amount, $cause, $correction, $add_on_flag ) = @job_row;
        if ($billing_code eq 'I' || $billing_code eq 'i'){
            $lbr_type = "Internal";
        } elsif ($billing_code eq 'C' || $billing_code eq 'c'){
            $lbr_type = "Customer";
        } elsif ($billing_code eq 'W' || $billing_code eq 'w'){
            $lbr_type = "Warranty";
        }
        my @job_header_section;
        my $techs_qry = $conn->prepare("SELECT
                            tech_id,
                            actual_hours,
                            booked_hours
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
        $techs_qry->execute($ro_number, $ro_line, $ro_job_line);
        my ($job_tech, $act_time, $booked_time);
        my @job_tech_row = $techs_qry->fetchrow_array;
        ($job_tech, $act_time, $booked_time) = @job_tech_row;
         $job_hours = $sold_hours;

        push (@job_header_section, sprintf(border(">")." %-9s %-12s %-13s %-7s %5.2f    %-17s ".'~font{DejaVuSansMono-Bold10}'."%9s"
                                                      .'~font{default}'."  %11s".border("|")."\n",
                                           $ro_line.' '.$add_on_flag, $op_code, "", $job_tech, $sold_hours, $lbr_type, currency_format($sale_amount), ""));
        while (@job_tech_row = $techs_qry->fetchrow_array) {
            ($job_tech, $act_time, $booked_time) = @job_tech_row;
            push (@job_header_section, sprintf(border(">")." %-36s %-7s %5.2f %-43s".border("|")."\n",
                                           "", $job_tech, $sold_hours, ""));
            #$job_hours = $job_hours + $act_time;
        }

        push (@job_section, [@job_header_section]);

        #my @tech_section = make_tech_section($ro_line, $ro_job_line, $total_act_time, $total_booked_time);
        #push (@job_section, [@tech_section]);

        # Job Complaint
        my @job_complaint_section;
        $Text::Wrap::columns = 62;
        my $wrapped_complaint = fill('', '', expand($complaint));
        my @complaint_list = split "\n", $wrapped_complaint;
        if( length $complaint_list[0] ) {
        
            push (@job_complaint_section, sprintf(border(">")." %-10s %-62s %20s".border("|")."\n", "Concern", $complaint_list[0], ""));
            foreach my $i (1 .. $#complaint_list) {
                push (@job_complaint_section, sprintf(border(">")." %10s %-62s %20s".border("|")."\n", "", $complaint_list[$i], ""));
            }
            push (@job_section, [@job_complaint_section]);
        }

        # Job Cause
        my @job_cause_section;
        my $wrapped_cause = fill('', '', expand($cause));
        my @cause_list = split "\n", $wrapped_cause;
        if( length $cause_list[0] ) {
            push (@job_cause_section, sprintf(border(">")." %-10s %-62s %20s".border("|")."\n", "Cause", $cause_list[0], ""));
            foreach my $i (1 .. $#cause_list) {
                push (@job_cause_section, sprintf(border(">")." %10s %-62s %20s".border("|")."\n", "", $cause_list[$i], ""));
            }
            push (@job_section, [@job_cause_section]);
        }
       

        # Job Correction
        my @job_correction_section;
        my $wrapped_correction = fill('', '', expand($correction));
        my @correction_list = split "\n", $wrapped_correction;
        if( length $correction_list[0] ) {
            push (@job_correction_section, sprintf(border(">")." %-10s %-62s %20s".border("|")."\n", "Correction", $correction_list[0], ""));
            foreach my $i (1 .. $#correction_list) {
                push (@job_correction_section, sprintf(border(">")." %10s %-62s %20s".border("|")."\n", "", $correction_list[$i], ""));
            }
            push (@job_section, [@job_correction_section]);
        }    

        my @parts_section = make_parts_section($ro_line, $ro_job_line);
        push (@job_section, [@parts_section]);

        my @misc_section = make_misc_section($ro_line);
        push (@job_section, [@misc_section]);

        my @gog_section = make_gog_section($ro_line);
        push (@job_section, [@gog_section]);

        my @sublet_section = make_sublet_section($ro_line);
        push (@job_section, [@sublet_section]);

        my @ded_section = make_deductible_section($ro_line);
        push (@job_section, [@ded_section]);

        my @job_total_section = make_job_total_section($ro_line);
        push (@job_section, [@job_total_section]);

    }
}

# Subroutine to prepare the TECH section of a JOB
sub make_tech_section{
    my ($ro_line, $ro_job_line, $total_act_time, $total_booked_time) = @_;
    my @tech_section_array;
    my $tech_details_qry = $conn->prepare("SELECT
                                                ro_job_tech_line,
                                                tech_id,
                                                work_date,
                                                work_start_time,
                                                work_end_time,
                                                work_note,
                                                actual_hours,
                                                booked_hours
                                            FROM repair_job_technician_detail td
                                            WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
    $tech_details_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours);
    if($tech_details_qry->rows > 0){
        push (@tech_section_array, sprintf(border("#")." %-100s ".border("|")."\n",
                                            "               TECH#      DATE      START   FINISH      ACT     TIME    DESCRIPTION"));

        while (my @tech_row = $tech_details_qry->fetchrow_array) {
            ($ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours) = @tech_row;

            push (@tech_section_array, sprintf(border(">")."           %10s   %-10s   %5s   %6s   %6.2f   %6.2f   %29s ".border("|")."\n",
                                                $tech_id, $work_date, $work_start_time, $work_end_time, $actual_hours, $booked_hours, $work_note));
        }
        push (@tech_section_array, sprintf(border(">")." %35s%-18s%6.2f   %6.2f %31s ".border("|")."\n", "","TOTAL TECH TIME",
                                            $total_act_time, $total_booked_time, ""));
        push (@tech_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
    }
    return @tech_section_array;
}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line) = @_;
    my @parts_section_array;

    my $parts_qry = $conn->prepare("SELECT
                                        ro_part_line,
                                        part_number,
                                        part_description,
                                        quantity_sold,
                                        unit_cost,
                                        unit_sale,
                                        unit_core_cost
                                    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ? AND quantity_sold > 0");
    $parts_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_part_line, $part_number, $part_description, $quantity_sold, $unit_cost, $unit_sale, $unit_core_cost);
    $parts_sale = 0;
    $parts_cost = 0;
    $parts_entries = $parts_qry->rows;
    if($parts_entries > 0){
        push (@parts_section_array, sprintf(border("#")."%4s".'~bggray{0.85}'." Part Number %9s  Description %16s Qty. ". '~font{DejaVuSansMono-Bold10}' ."Unit Price  Ext. Price %4s Cost".'~font{default}~bggray{1.0}'." ".border("|")."\n",
                                            "", "", "", ""));
        while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_number, $part_description, $quantity_sold, $unit_cost, $unit_sale, $unit_core_cost) = @parts_row;

            $Text::Wrap::columns = 28;
            my $wrapped_part_description = fill('', '', expand($part_description));
            my @part_description_list = split "\n", $wrapped_part_description;
            push (@parts_section_array, sprintf(border(">")."%4s %-21s  %-28s %3d ".'~font{DejaVuSansMono-Bold10}'." %10s  %10s %9s ".'~font{default}'.border("|")."\n",
                                                "", $part_number, $part_description_list[0], $quantity_sold,
                                                 currency_format($unit_sale), currency_format($unit_sale*$quantity_sold), currency_format($unit_cost*$quantity_sold) ));
            if($unit_core_cost>0){
                push (@parts_section_array, sprintf(border(">")."%4s %-21s  %-28s %3d ".'~font{DejaVuSansMono-Bold10}'." %10s  %10s %9s ".'~font{default}'.border("|")."\n",
                                                "", "", "CLEAN CORE", $quantity_sold,
                                                 currency_format($unit_core_cost), currency_format($unit_core_cost), currency_format($unit_core_cost) ));
                
                my $unit_core_cost_dirty = -$unit_core_cost;
                my $quantity_sold_dirty = - $quantity_sold;
                push (@parts_section_array, sprintf(border(">")."%4s %-21s  %-28s %3d ".'~font{DejaVuSansMono-Bold10}'." %10s  %10s %9s ".'~font{default}'.border("|")."\n",
                                                "", "", "DIRTY CORE", $quantity_sold_dirty,
                                                 currency_format($unit_core_cost_dirty), currency_format($unit_core_cost_dirty), currency_format($unit_core_cost_dirty) ));
           
            }
            foreach my $i (1 .. $#part_description_list) {
                push (@parts_section_array, sprintf(border(">")."%28s%-28s%-39s".border("|")."\n", "", $part_description_list[$i], ""));
            }
            $parts_sale = $parts_sale + ($unit_sale*$quantity_sold);
            $parts_cost = $parts_cost + ($unit_cost*$quantity_sold);
        }
        push (@parts_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold10}' ."%72s %10s %9s  ".'~font{default}'.border("|")."\n",
                                            "Parts Total...", currency_format($parts_sale), currency_format($parts_cost)));
    }
    return @parts_section_array;
}

# Subroutine to prepare the MISC section of a JOB
sub make_misc_section{
    my ($ro_line) = @_;
    my @misc_section_array;
    my $misc_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       other_cost,
                                       other_sale,
                                       quantity
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line =? AND item_type = 'MISC'");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $misc_code, $misc_description, $misc_cost, $misc_sale, $misc_quantity);
    $misc_job_sale = 0;
    $misc_job_cost = 0;
    $misc_entries = $misc_qry->rows;
    if($misc_entries > 0){
        push (@misc_section_array, sprintf(border("#")."%4s".'~bggray{0.85}'." Misc. Code  %9s  Description %16s Qty. ". '~font{DejaVuSansMono-Bold10}' ."Unit Price  Ext. Price %4s Cost".'~font{default}~bggray{1.0}'." ".border("|")."\n",
                                            "", "", "", ""));

        while (my @misc_row = $misc_qry->fetchrow_array) {
            ( $misc_code, $misc_description, $misc_cost, $misc_sale, $misc_quantity) = @misc_row;
            $Text::Wrap::columns = 28;
            my $wrapped_misc_description = fill('', '', expand($misc_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;
            push (@misc_section_array, sprintf(border(">")."%4s %-21s  %-28s %3d ".'~font{DejaVuSansMono-Bold10}'." %10s  %10s %9s ".'~font{default}'.border("|")."\n",
                                                "", $misc_code, $misc_description_list[0], $misc_quantity,
                                                currency_format($misc_sale), currency_format($misc_sale * $misc_quantity), currency_format($misc_cost * $misc_quantity) ));
            foreach my $i (1 .. $#misc_description_list) {
                push (@misc_section_array, sprintf(border(">")."%28s%-28s%-39s".border("|")."\n", "", $misc_description_list[$i], ""));
            }
            $misc_job_sale = $misc_job_sale + ($misc_sale * $misc_quantity);
            $misc_job_cost = $misc_job_cost + ($misc_cost * $misc_quantity);
        }
        push (@misc_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold10}' ."%72s %10s %9s  ".'~font{default}'.border("|")."\n",
                                            "Misc. Total...", currency_format($misc_job_sale), currency_format($misc_job_cost)));
    }
    return @misc_section_array;
}

# Subroutine to prepare the GOG section of a JOB
sub make_gog_section{
    my ($ro_line) = @_;
    my @gog_section_array;
    my $gog_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale,
                                    quantity
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'GOG'");
    $gog_qry->execute($ro_number, $ro_line);
    my ( $gog_code, $gog_description, $gog_cost, $gog_sale, $gog_quanity);
    $gog_job_sale = 0;
    $gog_job_cost = 0;
    $gog_entries = $gog_qry->rows;
    if($gog_qry->rows > 0){
        push (@gog_section_array, sprintf(border("#")."%4s".'~bggray{0.85}'." GOG. Code   %9s  Description %16s Qty. ". '~font{DejaVuSansMono-Bold10}' ."Unit Price  Ext. Price %4s Cost".'~font{default}~bggray{1.0}'." ".border("|")."\n",
                                            "", "", "", ""));
        while (my @gog_row = $gog_qry->fetchrow_array) {
            ( $gog_code, $gog_description, $gog_cost, $gog_sale, $gog_quanity) = @gog_row;
            $Text::Wrap::columns = 28;
            my $wrapped_gog_description = fill('', '', expand($gog_description));
            my @gog_description_list = split "\n", $wrapped_gog_description;
            push (@gog_section_array, sprintf(border(">")."%4s %-21s  %-28s %3d ".'~font{DejaVuSansMono-Bold10}'." %10s  %10s %9s ".'~font{default}'.border("|")."\n",
                                                "", $gog_code, $gog_description_list[0], $gog_quanity,
                                                currency_format($gog_sale), currency_format($gog_sale * $gog_quanity), currency_format($gog_cost * $gog_quanity)));
            foreach my $i (1 .. $#gog_description_list) {
                push (@gog_section_array, sprintf(border(">")."%28s%-28s%-39s".border("|")."\n", "", $gog_description_list[$i], ""));
            }
            $gog_job_sale = $gog_job_sale + ($gog_sale * $gog_quanity);
            $gog_job_cost = $gog_job_cost + ($gog_cost * $gog_quanity);
        }
        push (@gog_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold10}' ."%72s %10s %9s  ".'~font{default}'.border("|")."\n",
                                            "GOG Total...", currency_format($gog_job_sale), currency_format($gog_job_cost)));
    }
    return @gog_section_array;
}

# Subroutine to prepare the SUBLET section of a JOB
sub make_sublet_section{
    my ($ro_line) = @_;
    my @sublet_section_array;
    my $sublet_qry = $conn->prepare("SELECT
                                        other_code,
                                        other_description,
                                        other_cost,
                                        other_sale,
                                        quantity,
                                        vendor_name
                                    FROM repair_other
                                    WHERE ro_number = ? AND ro_line =? AND item_type = 'SUBLET'");
    $sublet_qry->execute($ro_number, $ro_line);
    my ( $sublet_code, $sublet_description, $sublet_cost, $sublet_sale, $sublet_quanity, $vendor_name );
    $sublet_job_sale = 0;
    $sublet_job_cost = 0;
    $sublet_entries = $sublet_qry->rows;
    if($sublet_entries > 0){
        push (@sublet_section_array, sprintf(border("#")."%4s".'~bggray{0.85}'." Sublet Code %9s Vendor Name %9s  PO# / Description %16s ". '~font{DejaVuSansMono-Bold10}' ."%4s Cost".'~font{default}~bggray{1.0}'." ".border("|")."\n",
                                            "", "", "", "", ""));
        while (my @sublet_row = $sublet_qry->fetchrow_array) {
            ( $sublet_code, $sublet_description, $sublet_cost, $sublet_sale, $sublet_quanity, $vendor_name) = @sublet_row;
            $Text::Wrap::columns = 28;
            my $wrapped_sublet_description = fill('', '', expand($sublet_description));
            my @sublet_description_list = split "\n", $wrapped_sublet_description;
            push (@sublet_section_array, sprintf(border(">")."%4s %-21s %-22s %-18s %10s %9s %1s".'~font{default}'.border("|")."\n",
                                                "", $sublet_code, $vendor_name, $sublet_description_list[0], currency_format($sublet_sale), currency_format($sublet_cost), "" ));
            foreach my $i (1 .. $#sublet_description_list) {
                push (@sublet_section_array, sprintf(border(">")."%28s%-28s%-39s".border("|")."\n", "", $sublet_description_list[$i], ""));
            }
            $sublet_job_sale = $sublet_job_sale + $sublet_sale;
            $sublet_job_cost = $sublet_job_cost + $sublet_cost;
        }
        push (@sublet_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold10}' ."%72s %10s %9s  ".'~font{default}'.border("|")."\n",
                                            "Sublet Total...", currency_format($sublet_job_sale), currency_format($sublet_job_cost) ));
    }
    return @sublet_section_array;
}

# Subroutine to prepare the DEDUCTIBLE section of a JOB
sub make_deductible_section{
    my ($ro_line) = @_;
    my @ded_section_array;
    my $ded_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'DEDUCTIBLE'");
    $ded_qry->execute($ro_number, $ro_line);
    my ( $ded_code, $ded_description, $ded_cost, $ded_sale);
    $ded_job_sale = 0;
    $ded_job_cost = 0;
    $ded_entries = $ded_qry->rows;
    if($ded_entries > 0){
        push (@ded_section_array, sprintf(border("#")."%4s".'~bggray{0.85}'." Deductible Code %5s  Description %16s Qty. ". '~font{DejaVuSansMono-Bold10}' ."Unit Price  Ext. Price %4s Cost".'~font{default}~bggray{1.0}'." ".border("|")."\n",
                                            "", "", "", ""));
        while (my @ded_row = $ded_qry->fetchrow_array) {
            ( $ded_code, $ded_description, $ded_cost, $ded_sale) = @ded_row;
            $Text::Wrap::columns = 28;
            my $wrapped_ded_description = fill('', '', expand($ded_description));
            my @ded_description_list = split "\n", $wrapped_ded_description;
            push (@ded_section_array, sprintf(border(">")."%4s %-21s  %-28s %3d ".'~font{DejaVuSansMono-Bold10}'." %10s  %10s %9.2f ".'~font{default}'.border("|")."\n",
                                                "", $ded_code, $ded_description_list[0], 1,
                                                currency_format($ded_sale), currency_format($ded_sale), 0 ));
            foreach my $i (1 .. $#ded_description_list) {
                push (@ded_section_array, sprintf(border(">")."%28s%-28s%-39s".border("|")."\n", "", $ded_description_list[$i], ""));
            }
            $ded_job_sale = $ded_job_sale + $ded_sale;
            $ded_job_cost = $ded_job_cost + $ded_cost;
        }
        push (@ded_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold10}' ."%72s %10s %9.2f  ".'~font{default}'.border("|")."\n",
                                            "Deductible Total...", currency_format($ded_job_sale), 0));
    }
    return @ded_section_array;
}

# Subroutine to prepare the TOTAL section of a JOB
sub make_job_total_section{
    my ($ro_line) = @_;
    my @job_total_section_array;

    push (@job_total_section_array, sprintf(border("#")."%73s %10s %10s".border("|")."\n", "", "="x10, "="x10));
    push (@job_total_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold10}'."%29s Total Hours: %6.2f %8s Line Total... %10s %9s  ".'~font{default}'.border("|")."\n",
                                     "", $job_hours, "",
                                     currency_format($sale_amount+$parts_sale+$misc_job_sale+$gog_job_sale+$sublet_job_sale+$ded_job_sale),
                                     currency_format($parts_cost+$misc_job_cost+$gog_job_cost+$sublet_job_cost)));
    push (@job_total_section_array, sprintf(border("#")."%95s".border("|")."\n", "_"x95));

    if ($lbr_type eq "Internal"){
        $intern_lbr_cost = $intern_lbr_cost+$labor_cost;
        $intern_lbr_sale = $intern_lbr_sale+$sale_amount;
        $intern_prt_cost = $intern_prt_cost+$parts_cost;
        $intern_prt_sale = $intern_prt_sale+$parts_sale;
        $intern_misc_cost = $intern_misc_cost+$misc_job_cost;
        $intern_misc_sale = $intern_misc_sale+$misc_job_sale;
        $intern_gog_cost = $intern_gog_cost+$gog_job_cost;
        $intern_gog_sale = $intern_gog_sale+$gog_job_sale;
        $intern_sublet_cost = $intern_sublet_cost+$sublet_job_cost;
        $intern_sublet_sale = $intern_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    } elsif ($lbr_type eq "Customer"){
        $cust_lbr_cost = $cust_lbr_cost+$labor_cost;
        $cust_lbr_sale = $cust_lbr_sale+$sale_amount;
        $cust_prt_cost = $cust_prt_cost+$parts_cost;
        $cust_prt_sale = $cust_prt_sale+$parts_sale;
        $cust_misc_cost = $cust_misc_cost+$misc_job_cost;
        $cust_misc_sale = $cust_misc_sale+$misc_job_sale;
        $cust_gog_cost = $cust_gog_cost+$gog_job_cost;
        $cust_gog_sale = $cust_gog_sale+$gog_job_sale;
        $cust_sublet_cost = $cust_sublet_cost+$sublet_job_cost;
        $cust_sublet_sale = $cust_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    } elsif ($lbr_type eq "Warranty"){
        $warr_lbr_cost = $warr_lbr_cost+$labor_cost;
        $warr_lbr_sale = $warr_lbr_sale+$sale_amount;
        $warr_prt_cost = $warr_prt_cost+$parts_cost;
        $warr_prt_sale = $warr_prt_sale+$parts_sale;
        $warr_misc_cost = $warr_misc_cost+$misc_job_cost;
        $warr_misc_sale = $warr_misc_sale+$misc_job_sale;
        $warr_gog_cost = $warr_gog_cost+$gog_job_cost;
        $warr_gog_sale = $warr_gog_sale+$gog_job_sale;
        $warr_sublet_cost = $warr_sublet_cost+$sublet_job_cost;
        $warr_sublet_sale = $warr_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    }
    return @job_total_section_array;
}

#Subroutine to prepare the FOOTER section of the invoice
sub get_footer {
    my @footer_section;
    my ($is_end, $page_number) = @_;
    push (@footer_section, sprintf(border("#")."%95s".border("|")."\n", ""));
    push (@footer_section, sprintf(border(">").'~font{CourierStd10}' ." %14s %15s INVOICE %12s *END-OF-DAY COPY %10s Page %d of %d ".'~font{default}'.border("|")."\n\f",
                                   $invoice_datetime, "", "", "", $curr_page_num, $pages));
    return @footer_section;
}

# Subroutine to get the RO TAX amount
sub get_ro_tax_amount {
    my $tax_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ? AND item_type = 'TAX'");
    $tax_qry->execute($ro_number);
    my ( $tax_type, $tax_amount);

    while (my @tax_row = $tax_qry->fetchrow_array) {
        ( $tax_type, $tax_amount) = @tax_row;
        if ($tax_type eq "C"){
            $cust_ro_tax = $tax_amount;
        }elsif ($tax_type eq "W"){
            $warr_ro_tax = $tax_amount;
        }elsif ($tax_type eq "I"){
            $intern_ro_tax = $tax_amount;
        }
    }
}

# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_grand_total_section {
    get_ro_tax_amount();

    # Non Customer Totals
    # push (@grand_total_section, sprintf(border("#")."%95s".border("|")."\n", "_"x95));
    push (@grand_total_section, sprintf(border("#").'~font{DejaVuSansMono-Bold12}'."%29s Non-Customer Totals %29s".'~font{default}'.border("|")."\n",
                                "", ""));
    push (@grand_total_section, sprintf(border("#")." Charge Description    %49s ".'~font{DejaVuSansMono-Bold10}'."Amount %8s Cost ".'~font{default}'.border("|")."\n",
                                "", ""));
    
    if ($warr_lbr_cost!=0 || $warr_lbr_sale!=0){
        push (@grand_total_section, sprintf(border("#")." W - Labor %56s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($warr_lbr_sale), "", currency_format($warr_lbr_cost)));
    }
    if ($warr_prt_cost!=0 || $warr_prt_sale!=0){
        push (@grand_total_section, sprintf(border("#")." W - Parts %56s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($warr_prt_sale), "", currency_format($warr_prt_cost)));
   }
    if ($warr_misc_cost!=0 || $warr_misc_sale!=0){
        push (@grand_total_section, sprintf(border("#")." W - Misc %57s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($warr_misc_sale), "", currency_format($warr_misc_cost)));
    }
    if ($warr_gog_cost!=0 || $warr_gog_sale!=0){
        push (@grand_total_section, sprintf(border("#")." W - GOG %58s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($warr_gog_sale), "", currency_format($warr_gog_cost)));
    }
    if ($warr_sublet_cost!=0 || $warr_sublet_sale!=0){
        push (@grand_total_section, sprintf(border("#")." W - Sublet %55s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($warr_sublet_sale), "", currency_format($warr_sublet_cost)));
    }
    if ($warr_ded_cost!=0 || $warr_ded_sale!=0){
        push (@grand_total_section, sprintf(border("#")." W - Deductible %51s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($warr_ded_sale), "", currency_format($warr_ded_cost)));
    }
    if ($warr_ro_tax > 0){
        push (@grand_total_section, sprintf(border("#")." W - Tax %58s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($warr_ro_tax), "", ""));
    }
    
    if ($intern_lbr_cost!=0 || $intern_lbr_sale!=0){
        push (@grand_total_section, sprintf(border("#")." I - Labor %56s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($intern_lbr_sale), "", currency_format($intern_lbr_cost)));
    }
    if ($intern_prt_cost!=0 || $intern_prt_sale!=0){
        push (@grand_total_section, sprintf(border("#")." I - Parts %56s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($intern_prt_sale), "", currency_format($intern_prt_cost)));
    }
    if ($intern_misc_cost!=0 || $intern_misc_sale!=0){
        push (@grand_total_section, sprintf(border("#")." I - Misc %57s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($intern_misc_sale), "", currency_format($intern_misc_cost)));
    }
    if ($intern_gog_cost!=0 || $intern_gog_sale!=0){
        push (@grand_total_section, sprintf(border("#")." I - GOG %58s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($intern_gog_sale), "", currency_format($intern_gog_cost) ));
    }
    if ($intern_sublet_cost!=0 || $intern_sublet_sale!=0){
        push (@grand_total_section, sprintf(border("#")." I - Sublet %55s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($intern_sublet_sale), "", currency_format($intern_sublet_cost)));
    }
    if ($intern_ded_cost!=0 || $intern_ded_sale!=0){
        push (@grand_total_section, sprintf(border("#")." I - Deductible %51s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($intern_ded_sale), "", currency_format($intern_ded_cost)));
    }
    if ($intern_ro_tax > 0){
        push (@grand_total_section, sprintf(border("#")." I - Tax %58s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($intern_ro_tax), "", ""));
    }

    # Total Warranty
    if(($warr_lbr_sale + $warr_prt_sale + $warr_misc_sale + $warr_gog_sale +
        $warr_sublet_sale + $warr_ded_sale + $warr_ro_tax) > 0){

        push (@grand_total_section, sprintf(border("#")."%95s".border("|")."\n", "_"x95));
        push (@grand_total_section, sprintf(border("#")." TOTAL WARRANTY %51s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($warr_lbr_sale + $warr_prt_sale + $warr_misc_sale + $warr_gog_sale +
                                    $warr_sublet_sale + $warr_ded_sale + $warr_ro_tax), "", ""));
    }

    # Total Internal
    if(($intern_lbr_sale + $intern_prt_sale + $intern_misc_sale + $intern_gog_sale +
        $intern_sublet_sale + $intern_ded_sale + $intern_ro_tax) > 0){

        # push (@grand_total_section, sprintf(border("#")."%95s".border("|")."\n", "_"x95));
        push (@grand_total_section, sprintf(border("#")." TOTAL INTERNAL %51s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($intern_lbr_sale + $intern_prt_sale + $intern_misc_sale + $intern_gog_sale +
                                    $intern_sublet_sale + $intern_ded_sale + $intern_ro_tax), "", ""));
    }

    my $customer_payment_qry = $conn->prepare("SELECT
                                                description,
                                                SUM(amount) AS amount,
                                                SUM(
                                                    COST) AS
                                                cost,
                                                MIN(key::integer) AS keyorder
                                            FROM
                                                du_dms_automate_proxy.repair_customer_payment
                                            WHERE
                                                ro_number = ?
                                                AND nullif (trim(description), '') IS NOT NULL
                                            GROUP BY
                                                1
                                            ORDER BY
                                                keyorder");
    $customer_payment_qry->execute($ro_number);

    my $customer_payment_method_qry = $conn->prepare("SELECT
                                                payment_type,
                                                amount AS payment_amount,
                                                payment_description
                                            FROM repair_customer_payment_methods
                                            WHERE ro_number = ? ORDER BY key ASC");
    $customer_payment_method_qry->execute($ro_number);

    # Customer Totals
    push (@grand_total_section, sprintf(border("#")."%95s".border("|")."\n", "_"x95));
    push (@grand_total_section, sprintf(border("#").'~font{DejaVuSansMono-Bold12}'."%30s  Customer Totals  %30s".'~font{default}'.border("|")."\n",
                                "", ""));
    push (@grand_total_section, sprintf(border("#")." Charge Description    %49s ".'~font{DejaVuSansMono-Bold10}'."Amount %8s Cost ".'~font{default}'.border("|")."\n",
                                "", ""));

    my ( $ro_cust_pay_id, $description, $amount, $cost, $grand_sale, $grand_cost);
    if($customer_payment_qry->rows > 0){
        # my $labor_other_sale_total;
        # my $labor_other_cost_total;
        # my $parts_other_sale_total;
        # my $parts_other_cost_total;
       while (my @customer_payment_row = $customer_payment_qry->fetchrow_array) {
            ( $description, $amount, $cost, $ro_cust_pay_id) = @customer_payment_row;
            $grand_sale += $amount;
            $grand_cost += $cost;
            if($cost == 0){
                push (@grand_total_section, sprintf(border("#")." %-66s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                    $description, currency_format($amount), "", ""));
            } else{
                push (@grand_total_section, sprintf(border("#")." %-66s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                    $description, currency_format($amount), "", currency_format($cost)));
            }
        }
    }                          
    # Total Charge
    push (@grand_total_section, sprintf(border("#")." Total Amount Due %49s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                            "", currency_format($grand_sale), "", ""));
    push (@grand_total_section, sprintf(border("#")."%95s".border("|")."\n", "_"x95));
    my ( $payment_type, $payment_amount, $payment_description);
    if($customer_payment_method_qry->rows > 0){
       while (my @customer_payment_method_row = $customer_payment_method_qry->fetchrow_array) {
            ( $payment_type, $payment_amount, $payment_description) = @customer_payment_method_row;
            push (@grand_total_section, sprintf(border("#")." %-17s %48s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                            $payment_description,"",currency_format($payment_amount), "", ""));
        }
    } 
    if ($payment_description ne 'Personal Check') {
        if($customer_payment_method_qry->rows > 0){
        push (@grand_total_section, sprintf(border("#")." Total Amount Paid %48s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($customer_paid_amount), "", ""));  
        } elsif($customer_paid_amount > 0) {
            push (@grand_total_section, sprintf(border("#")." TOTAL OTHER CHARGE %47s ".'~font{DejaVuSansMono-Bold10}'."%11s %2s %11s ".'~font{default}'.border("|")."\n",
                                "", currency_format($customer_paid_amount), "", ""));  
        }  
    }
                                                   
}

# Subroutine to calculate the total no of pages in the invoice
# Update page count calculation section, if any modification is made in the body of the report
sub calculate_page_count {
    my $page_count = 1;
    my $page_height = 0;
    my $total_page_content_height = 0;

    for my $job(@job_section){
        $total_page_content_height = $total_page_content_height + scalar(@$job);
    }
    $total_page_content_height = $total_page_content_height + scalar(@grand_total_section);

    if($total_page_content_height <= ($page_max_height - $page_header_height - $invoice_dealer_header)){
        $page_count = 1;
    } else{
        $page_count = ceil(($total_page_content_height - ($page_max_height - $page_header_height - $invoice_dealer_header)) /
                            ($page_max_height - $page_header_height)) + 1;
    }
    return $page_count;
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")."%95s".border("|")."\n", "");
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
 sub print_header_section {
    my ($page_num) = @_;
    my %hash = (pg_num => $page_num );
    my @header_section_dealer;
    if($page_num eq 1){
        my @dealer_list = split "~", $dealer_address;
        my $store_name = $dealer_list[0];
        my $store_address_1 = $dealer_list[1];
        my $store_address_2 = $dealer_list[2];
        my $store_address_3 = $dealer_list[3];
        my $store_content = $dealer_list[4];
        push (@header_section_dealer, sprintf(border(">")."%-96s".border("|")."\n", " "x(round((96-length($store_name))/2)).$store_name));
        push (@header_section_dealer, sprintf(border(">")."%-96s".border("|")."\n", " "x(round((96-length($store_address_1))/2)).$store_address_1));
        push (@header_section_dealer, sprintf(border(">")."%-96s".border("|")."\n", " "x(round((96-length($store_address_2))/2)).$store_address_2));
        push (@header_section_dealer, sprintf(border(">")."%-96s".border("|")."\n", " "x(round((96-length($store_address_3))/2)).$store_address_3));
        push (@header_section_dealer, sprintf(border(">")."%-96s".border("|")."\n", " "x(round((96-length($store_content))/2)).$store_content));
        foreach (@header_section_dealer)
        {
            my $header_line = $_;
            $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
            print FILE $header_line;
        }
        print_blank_line(1);
    }
    foreach (@header_section)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
 }

sub print_dealer_header_section {
    print_blank_line($invoice_dealer_header);
 }

# Subroutine for pagination
sub paginate_and_print_segment {
    my (@section_array) = @_;

    if($curr_page_num == 1){
        $total_header_height = $page_header_height + $invoice_dealer_header;
    } else{
        $total_header_height = $page_header_height;
    }

    # Print section that fit in the current page
    if(scalar(@section_array) <= (($page_max_height - $total_header_height) - $curr_page_height)){
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{
        for my $sub_section(@section_array){
            if($curr_page_height < ($page_max_height - $total_header_height))
            {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            } else{
                print FILE get_footer(0, $curr_page_num);
                $curr_page_num = $curr_page_num + 1;
                print_header_section ($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
            }
        }
    }
}

# Subroutine to prepare end of invoice section
sub make_end_of_invoice_section {
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %4s%-31s%s%32s ".border("|")."\n",
           "",
           "*"x26,
           "A L L  D E T A I L  I N V O I C E",
           "*"x27));
}

# Subroutine to prepare invoice note section
sub make_invoice_note_section {
    for (my $i = 0; $i < $invoice_note_lines; $i++) {
        push (@invoice_note_section, sprintf(border("#")."%95s".border("|")."\n", ""));
    }
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
    ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

    ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

    ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

    ($sale_amount, $parts_cost, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

    $job_hours = 0;

    ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;

    #$curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @job_section;
    undef @grand_total_section;
    undef @end_of_invoice_section;
    undef @invoice_note_section;

    ########## Prepare invoice ##########
    make_header();
    make_job_section();
    make_grand_total_section();
    $pages = calculate_page_count();

    ########## Print invoice ##########
    #print_dealer_header_section();
    $curr_page_height = 0;
    print_header_section ($curr_page_num);

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }

    # Pagination of Grand total section
    paginate_and_print_segment(@grand_total_section);

    if($curr_page_num == 1){
        $total_header_height = $page_header_height + $invoice_dealer_header;
    } else{
        $total_header_height = $page_header_height;
    }

    # End of invoice footer
    if($curr_page_height == ($page_max_height - $total_header_height)){
        print FILE get_footer(1, $curr_page_num);
    } else {
        print_blank_line(($page_max_height - $total_header_height) - $curr_page_height);
        print FILE get_footer(1, $curr_page_num);
    }
}

sub print_ro_inv_bg_head {
    printf(FILE $cust_inv_head);
}

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}

sub align_center {
    # Return $str centered in a field of $col $padchars.
    # $padchar defaults to ' ' if not specified.
    # $str is truncated to len $column if too long.

    my ($str, $col, $padchar) = @_;
    $padchar = ' ' unless $padchar;
    my $strlen = length($str);
    $str = substr($str, 0, $col) if ($strlen > $col);
    $strlen = length($str);
    my $fore = int(($col - $strlen) / 2);
    my $aft = $col - ($strlen + $fore);
    $padchar x $fore . $str . $padchar x $aft;
}

sub currency_format{
    use Number::Format qw(:subs);
    my $precision=2;
    my $symbol='$';
    my ($number) = @_;
    my $formatted_number = $number =~ s/\$-/-\$/r;
    # print $formatted_number, $number;
    my $formatted =format_price($formatted_number, $precision, $symbol);
    return  $formatted;
}
