# Exclude all of the contained repos that we
# house in this directory but which maintain
# independent version control flows.
# Git Sub-Modules may make sense eventually
# though the complexity is likely not worth
# it.  Proper semantic versioning will hopefully
# prove sufficient.
/DU-Extract
/DU-Load
/DU-Transform
/GGSMageWorker
/DU-Scheduler
/DU-DMS/DMS-AutoNation
/DU-DMS/DMS-DealerTrack
/DU-DMS/DMS-DealerBuilt
/DU-DMS/DMS-PBS
/DU-DMS/DMS-SIS
/DU-DMS/DMS-AutoMate
/DU-DMS/DMS-UCS
/DU-DMS/DMS-ADV
/DU-DMS/DMS-Reynolds
/DU-DMS/DMS-KenGarff
/DU-DMS/DMS-Quorum
/DU-Solve360
/DU-DMS/DMS-CDK
/DU-DMS/DMS-DPC
/DU-DMS/DMS-AutoSoft
/DU-DMS/DMS-Dominion
/DU-ProxyInvoice
/DU-DMS/DMS-CDKDash
/DU-DMS/DMS-CDK3PA
/DU-DMS/DMS-Advantage
/DU-DMS/DMS-MPK

# Temporary directories that are re-created
# by various processes
/tmp
/tmp-*

# sub-directories created by test-run scripts
# use thie test-run- prefix by convention
/test-run-*

