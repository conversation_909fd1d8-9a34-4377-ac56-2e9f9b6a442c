#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

WORK_DIR=/mnt/du-etl-work/etl-ucs/inv-ucs/
DMS_BASE="$DU_ETL_HOME"/DU-DMS/DMS-UCS/
DMS_INV_MANIP="$DMS_BASE"/invoice-manipulation/
COM_INV_MANIP="$DU_ETL_HOME"/invoice-manipulation-common/
SCN_META_DIR="$WORK_DIR"/scenariokey-meta

mkdir -p "$SCN_META_DIR"
mkdir -p "$WORK_DIR"/invoicemaster-meta

function main() {
    if [[ "$#" = '0' ]]; then
        usage_die "Must pass in at least mode argument"
    fi

    MODE="$1"

    if [[ "$MODE" = 'test' ]]; then
        $DMS_BASE/invoice-manipulation/run-tests.bash "${2:-check}" \
            || die "Test Failed"
        say "inv-ucs testing successful"
        exit 0
    fi
    (
        cd "$WORK_DIR"

        while true; do
            echo "$MODE"
            if [[ "$MODE" = 'rename' ]]; then
                "$DMS_INV_MANIP"/do-rename-and-bind
            fi
            if [[ "$MODE" = 'missing' ]]; then
                "$DMS_INV_MANIP"/resolve-missing-ros
            fi
            if [[ "$MODE" = 'list' ]]; then
                SCNKEY="${2:?ScenarioKey required after list mode}"
                shift
                "$COM_INV_MANIP"/extract-scenariokey-invoices \
                    "$SCNKEY" \
                    "$SCN_META_DIR" \
                    "${FIRST_RO:-}" \
                    "${LAST_RO:-}"
                "$COM_INV_MANIP"/create-filtered-invoice-lists "${PWD}"
            fi
            if [[ "$MODE" = 'bundle' ]]; then
                "$DMS_INV_MANIP"/perform-list-bundling
            fi
            if [[ "$MODE" = 'full' ]]; then
                SCNKEY="${2:?ScenarioKey required after full mode}"
                shift
                "$DMS_INV_MANIP"/do-rename-and-bind
                "$DMS_INV_MANIP"/resolve-missing-ros
                "$COM_INV_MANIP"/extract-scenariokey-invoices \
                    "$SCNKEY" \
                    "$SCN_META_DIR" \
                    "${FIRST_RO:-}" \
                    "${LAST_RO:-}"
                "$DMS_INV_MANIP"/perform-list-bundling "${PWD}"
            fi
            shift
            if [[ "$#" = 0 ]]; then
                break
            else
                MODE="$1"
            fi
        done
    )
}

function usage_die() {
    scream "$@"
    usage
    exit
}

function usage() {
pre_usage ""
    cat <<TXT
    Usage:
        ./inv-ucs mode[, mode]*
        FIRST_RO=# LAST_RO=# ./inv-ucs list|full SCENARIOKEY

    Working Directory:
        "$WORK_DIR"

    Pre-Reqs:
        Invoices (rename will normalize names and then enumerate)
        Invoice-Recaps/source/invoice_recaps.txt
        Populated DU-ETL-Database/UCS-Schema for the relevant Store
           Required for "missing" mode execution

    Post-Actions:
        In Windows cmd execute the contents of:
        invoice-list-files/meta/bulk-bundling-script-copy-paste.txt
        while in $WORK_DIR

    Environment:
        Specifies the first and last RO# to be
        evaluated during "list" mode execution.
        FIRST_RO
        LAST_RO

    Modes:
        - rename             # creates split-meta
        - missing            # creates invoicemaster-meta
        - list SCENARIOKEY   # creates scenariokey-meta, invoice-lists
        - bundle             # creates invoice-list-files and segregates ROs
        - full SCENARIOKEY   # performs all of the above
        - test [*check|copy] # standalone, test functionality

TXT
post_usage
}

main "$@"

exit
