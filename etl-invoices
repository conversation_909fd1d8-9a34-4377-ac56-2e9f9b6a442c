#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash


SCENARIO_KEY=''
LOAD=false
INTERACTIVE=true

SOLVE360_SERVICE="service=solve360"
S360_USER=${SOLVE360_API_EMAIL}
S360_TOKEN=${SOLVE360_API_TOKEN}
SOLVE_API_URL=${SOLVE360_API_URL}
S360_URL=https://secure.solve360.com

PORTAL_API_KEY=${PORTAL_EVENT_API_KEY}
PORTAL_API_URL=${PORTAL_EVENT_API_URL}
ETL_INVOICE_LOG="/etl/log"
DMSES=(
    "AutoMate"
    "AutoMate Bundle Only"
    "AutoSoft"
    "CDKDash"
    "DealerTrack"
    "Dominion"
    "Reynolds"
    "UCS"
    "PBS"
    "Quorum"
    "Tekion"
)

function usage() {
    if [ "$*" != "" ] ; then
        scream "Error: $*"
    fi
    pre_usage "ETL Invoices"
    cat >&2 <<EOF
Usage: etl-invoices [--options]
--dms                       DMS
--key                       scenario key
--load                      perform load
--help                      display this usage message and exits
EOF
    post_usage
    exit 1
}

while [ $# -gt 0 ]; do
    case "$1" in
    --dms)
        DMS="${2:?DMS Required}"
        INTERACTIVE=false
        shift
        ;;
    --key)
        SCENARIO_KEY="${2:?Scenario Key Required}"
        shift 
        ;;
    --source-file)
        SOURCE_FILE="${2:?Source File Archive Required}"
        shift
        ;;
    --load)
        LOAD=true        
        ;;
    --help)
        usage
        ;;
    *)
        die "Unrecognized option $1; exiting"
        exit 1
    esac
    shift
done

function psql_solve360_script {
    psql "$SOLVE360_SERVICE options='-c search_path=middletier_solve360_data'" \
        --pset pager='off' \
        --set=ON_ERROR_STOP=1 \
        --set=dms_projects="du_dms_${dms}_projects" \
        "$@"
}

function select_dms() {
    local prompt="Please select a DMS:"

    while :
    do
        PS3="${BEGIN_PROMPT}$prompt${END_PROMPT} "
        select opt in "${DMSES[@]}" "Show Range Errors" "Quit"
        do
            if [[ "$opt" = "Quit" ]]; then
                quit
            elif [[ "$opt" = "Show Range Errors" ]]; then
                display_range_errors
            elif [[ $REPLY =~ ^[0-9]+$ ]]; then
                DMS="$opt"
                return 0
            else
                yell "Invalid option [ ${REPLY} ]"
          fi
          break
        done
    done
}

function display_range_errors() {
    psql_solve360_script --quiet --set=DMS=$DMS <<SQL
       \pset title 'Projects with Range Errors'
       SELECT
          project_id,
          o.project_name,
          project_type,
          scenariokey,
          CASE project_type
          WHEN 'Parts UL' THEN
              parts_extended_range
          ELSE
              labor_extended_range
          END AS ro_ranges,
          dms_code
        FROM core.project_owner o
        JOIN middletier_solve360_data.s360_project_masby USING (project_id)
        JOIN middletier_solve360_data.s360_project_rorange USING (project_id)
        JOIN middletier_solve360_data.s360_project_base USING (project_id)
        JOIN middletier_solve360_data.s360_project_header USING (project_id)
        JOIN middletier_solve360_data.s360_company_mage USING (company_id)
       WHERE
          ((project_type = 'Parts UL' AND parts_range_imported_on IS NULL) OR 
           (project_type = 'Labor UL' AND labor_range_imported_on IS NULL))
          AND ((project_type = 'Parts UL' AND parts_extended_range IS NOT NULL) OR 
           (project_type = 'Labor UL' AND labor_extended_range IS NOT NULL))
          AND scenariokey IS NOT NULL
          AND nullif(category_list,'') IS NOT NULL
          -- 35636382 Stage: In Progress - Parts UL
          -- 35259149 Stage: In Progress - Labor UL
          AND regexp_split_to_array(category_list, ',')::bigint[] && '{35636382,35259149}'::bigint[]
          -- not test
          AND EXISTS (
            SELECT 1
            FROM (
              SELECT regexp_matches(
                  CASE project_type
                  WHEN 'Parts UL' THEN
                      parts_extended_range
                  ELSE
                      labor_extended_range
                  END,                     
                '^\s*(?:(\d+)\s*-)?\s*(\d+)\s*-\s*(\d+)(?:\s*-\s*(\d+))?$') matches
            )q1
            WHERE coalesce(matches[1]::int, matches[2]::int) > coalesce(matches[4]::int, matches[3]::int)
          )
          ORDER BY 1;
SQL
}

function display_projects() {
    psql_solve360_script --quiet --set=DMS=$DMS <<SQL

       CREATE OR REPLACE VIEW :dms_projects AS
       SELECT row_number() OVER (ORDER BY project_id) AS "Row#",
              project_id,
              o.project_name,
              scenariokey,
              (SELECT pb2.project_id
                 FROM core.project_owner o2 
                 JOIN middletier_solve360_data.s360_project_header ph2 ON 
                    (ph2.project_id = o2.project_id AND (ARRAY['35259149', '35636382']::text[] && 
                    regexp_split_to_array(ph2.category_list, ',\s?')::text[]) = true) 
                 JOIN middletier_solve360_data.s360_project_base pb2 ON
                    (pb2.project_id = ph2.project_id AND pb2.project_type = 
                     CASE WHEN pb.project_type = 'Parts UL' THEN 'Labor UL'
                          WHEN pb.project_type = 'Labor UL' THEN 'Parts UL' END)
                WHERE o2.company_id = o.company_id 
                  AND o2.project_id != o.project_id
                  LIMIT 1) secondary_project_id
             FROM core.project_owner o
            JOIN middletier_solve360_data.s360_project_masby USING (project_id)
            JOIN middletier_solve360_data.s360_project_base pb USING (project_id)
            JOIN middletier_solve360_data.s360_project_header USING (project_id)
            JOIN middletier_solve360_data.s360_company_mage USING (company_id)
            JOIN core.dms dms ON (pb.dms_code = ANY(dms.aliases)) AND :'DMS' = ANY(dms.aliases)
           WHERE
              ((project_type = 'Parts UL' AND parts_range_imported_on IS NULL) OR 
               (project_type = 'Labor UL' AND labor_range_imported_on IS NULL))
              AND scenariokey IS NOT NULL
              AND nullif(category_list,'') IS NOT NULL
              -- 35636382 Stage: In Progress - Parts UL
              -- 35259149 Stage: In Progress - Labor UL
              AND regexp_split_to_array(category_list, ',')::bigint[] && '{35636382,35259149}'::bigint[]
              -- not test
              AND (NOT ARRAY['*********', '*********']::text[] && regexp_split_to_array(category_list, ',\s?')::text[])
              ORDER BY o.project_name;


       \set title :DMS ' Projects'
       \pset title :title 
       SELECT * FROM :dms_projects ORDER BY project_id;
SQL
}

function get_project_id_for_row() {
    local row=$1

    if [[ ! "${row}" =~ ^[0-9]*$ ]]; then
        return 1
    fi
    
    PROJECT_ID=$(psql_solve360_script -At --set=row="${row}" <<SQL
    SELECT project_id
        FROM (
           SELECT row_number() OVER (ORDER BY project_id) AS rn, project_id
             FROM :dms_projects e 
           ORDER BY 1
        ) projects
     WHERE  projects.rn = :'row';
SQL
)
    if [[ -z "${PROJECT_ID:-}" ]]; then
        return 1
    fi

    SECONDARY_PROJECT_ID=$(psql_solve360_script -At --set=row="${row}" <<SQL
    SELECT secondary_project_id
        FROM (
           SELECT row_number() OVER (ORDER BY project_id) AS rn, secondary_project_id
             FROM :dms_projects e 
           ORDER BY 1
        ) projects
     WHERE  projects.rn = :'row';
SQL
)
     return 0
}



function get_scenario_key() {
    SCENARIO_KEY=$(psql_solve360_script -At --set=project_id="$PROJECT_ID" <<SQL
     SELECT scenariokey
       FROM :dms_projects
     WHERE  project_id = :'project_id';
SQL
)
}

function select_project() {
    local prompt="Enter row to select project (enter to continue)"
    local row

    PROJECT_ID=''
    SECONDARY_PROJECT_ID=''
    SCENARIO_KEY=''

    while true; do
        display_projects
        echo -e -n "${txt_prompt}${prompt}: ${txt_reset}"
        read row
        if [[ -z "${row}" ]]; then
            break
        fi
        if [[ ${row} =~ ^([0-9]*) ]]; then
          if ! get_project_id_for_row $row; then
                 yell "Invalid row: $row"
          else
              get_scenario_key
              break
          fi
        else
            yell "Invalid row"
        fi
  done
}


function update_solve360() {
    
    local field="$1"
    local project_id="$2"
    
    echo "Updating " $field - $project_id
    imported_by=''
    # curl -X PUT \
    #     --user "${S360_USER}:${S360_TOKEN}" \
    #     --header "Content-Type: application/json" \
    #     --max-time 30 \
    #     -d "{\"${field}\": \"$(date -I)\"}"  \
    #     "${S360_URL}/projectblogs/${project_id}"  \
    curl -X POST \
    --header "Content-Type: application/json" \
    --max-time 30 \
    -d "{
        \"solve360ApiType\": \"projectblogs\", 
        \"currentFunctionName\": \"Update Project\", 
        \"actionBy\": \"${imported_by}\", 
        \"id\": \"${project_id}\", 
        \"application\": \"ETL\",
        \"options\": {
            \"body\": {
                \"${field}\": \"$(date -I)\"
            },
            \"headers\": {
                \"Content-Type\": \"application/json\",
                \"Authorization\": \"${S360_TOKEN}\",
                \"ACCEPT\": \"application/json\"
            },
            \"method\": \"PUT\",
            \"uri\": \"${S360_URL}/projectblogs/${project_id}\"
        },
        \"authToken\"          : \"\"}"  \
    "${SOLVE_API_URL}" > /dev/null 2>&1
     update_portal_event ${2}
}

function update_portal_event() {
    local project_id=${1}
    local imported_on="$(date -I)"
    local performedby=${USER}
    echo "${imported_on}: Invoice Import" >> ${ETL_INVOICE_LOG}/update-portal-event.log
    echo "${imported_on}: Updating the project ${project_id}" >> ${ETL_INVOICE_LOG}/update-portal-event.log
    echo "performedby :${performedby}" >> ${ETL_INVOICE_LOG}/update-portal-event.log
    if [[ ! "${PORTAL_API_URL:-}" = '' ]]; then
        curl -X PUT \
            --user "auth-key:${PORTAL_API_KEY}" \
            --header "Content-Type: application/json" \
            --max-time 30 \
            -d "{\"inProjectId\"     : \"${project_id}\",  \
                 \"inSource\"        : \"MAGE_Invoice_Import\", \
                 \"inAction\"        : \"complete\", \
                 \"inPerformedOn\"   : \"${imported_on}\", \
		 \"inPerformedBy\"   : \"${performedby}\", \
		 \"inData\"          : \"{}\", \
                 \"inCreatedBy\"     : \"${performedby}\"}" \
                "${PORTAL_API_URL}" >> ${ETL_INVOICE_LOG}/update-portal-event.log 2>&1
    fi  
}

function update_project() {
    local labor_imported_on_field="custom21374166"
    local parts_imported_on_field="custom21374164"
    
    echo "PROJECT: $PROJECT_ID SECONDARY: $SECONDARY_PROJECT_ID"

    if [[ "$SCENARIO_KEY" =~ \+L_ ]]; then
        update_solve360 "$labor_imported_on_field" "$PROJECT_ID"
        if [[ -n "$SECONDARY_PROJECT_ID" ]]; then
          update_solve360 "$parts_imported_on_field" "$SECONDARY_PROJECT_ID"
        fi
    else
        update_solve360 "$parts_imported_on_field" "$PROJECT_ID"
        if [[ -n "$SECONDARY_PROJECT_ID" ]]; then
          update_solve360 "$labor_imported_on_field" "$SECONDARY_PROJECT_ID"
        fi
    fi

}

function process_invoices() {
    if [[ $LOAD = true ]]; then
        SCENARIO_KEY=load
    elif [[ -z $SCENARIO_KEY ]]; then
        select_project
    fi
    if [[ -n $SCENARIO_KEY ]]; then
        if "${DU_ETL_HOME}/invoice-manipulation-common/shared/upload-invoices-common.bash" \
            "$DMS" "$SCENARIO_KEY"; then
                    update_project
        fi

            update_project
    fi
}

function main() {
    local dms_repo_dir

    while true; do
        if [[ $INTERACTIVE = true ]]; then
            select_dms
        fi
        
        dms="${DMS,,}"
        dms_projects=du_dms_${dms}_projects
        dms_repo_dir="${DU_ETL_HOME}/DU-DMS/DMS-${DMS}/invoice-manipulation"
        export INV_DMS_HOME="${dms_repo_dir}";

        case "$DMS" in
            AutoMate)
                "${dms_repo_dir}/inv-automate"
                ;;
            UCS|"AutoMate Bundle Only"|PBS|Quorum|Tekion|Dominion)
                "$DU_ETL_HOME/invoice-manipulation-common/shared/rename-and-bind.bash" $DMS
                ;;
            DealerTrack)
                "${dms_repo_dir}/process-invoices.bash"
                ;;
            AutoSoft|CDKDash|Reynolds)
                process_invoices
                ;;
            *)
                yell "Unrecognized DMS \"$DMS\""
                yell "Valid DMSes: ${DMSES[@]}"
                exit 1
                ;;
        esac
        
        if [[ $INTERACTIVE = false ]]; then
            exit 0
        fi
        SCENARIO_KEY=''
    done
}

main
