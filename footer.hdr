% Fancy header file to print page number in the footer section
% To use this fancy header, place this file into '/usr/share/enscript' directory.
% Need to add --fancy-header=footer, footer value into enscript command 

% -- code follows this line --
%Format: pagenumstr $V$%

%HeaderHeight: 12
%FooterHeight: 12

/do_header {   % print default simple header

  % Footer
  gsave
    d_footer_x d_footer_y HFpt_h 3 div add translate
    HF setfont

    user_footer_p {
      d_footer_x  d_footer_y moveto user_footer_left_str show

      d_footer_w user_footer_center_str stringwidth pop sub 2 div
      0 moveto user_footer_center_str show

      d_footer_x d_footer_w add user_footer_right_str stringwidth pop sub
      d_footer_y moveto user_footer_right_str show
    } if
  grestore

} def
