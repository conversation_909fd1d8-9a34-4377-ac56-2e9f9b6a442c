#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

REPO_ROOT="$(cd $(dirname $0) && pwd)"

SCHEMA_NAME="${1:?Schema Name Required}"
TARGET_DIR="${2:?Target Directory Required}"
CONVERT_TO_PDF="${3:-true}"
# Ver generic_proxy:- Generic proxy invoice,
# Ver invoice_mimic:- Mimic invoice merged with Auto/mate template
VERSION="${4:-"generic_proxy"}"
PORSCHE_STORE='false'

echo $TARGET_DIR

if [[ "$VERSION" = "generic_proxy" ]]; then
    "$REPO_ROOT"/generate-fixed-width-proxy-invoice.bash \
            "$SCHEMA_NAME" \
            "$TARGET_DIR" \
            "$CONVERT_TO_PDF"
elif [[ "$VERSION" = "invoice_mimic" ]]; then
    TMP_DIR="/tmp/du-etl-dominionvue-proxyinvoice-work"
    mkdir -p "$TMP_DIR"
    clear_dir "$TMP_DIR"
    BOOK_DIR="$TMP_DIR"/bookmarking

    [[ -d "$TARGET_DIR" ]] || die "$2 must exist and should be empty"

    clear_dir "$TARGET_DIR"
    mkdir "$TARGET_DIR"/text || die "Could not prepare text directory"
    mkdir "$TARGET_DIR"/text/inv-bg || die "Could not prepare text directory"
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        mkdir "$TARGET_DIR"/pdf  || die "Could not prepare pdf directory"
        (
            dominionvue_pdf generate -o "$TARGET_DIR"/pdf -p "$PORSCHE_STORE"
        ) || die "Failed during Proxy Invoice generation"
        cd "$TARGET_DIR"/pdf
        ls
        PDF_COUNT=$(ls -1 | wc -l)
        echo "PDF_COUNT: $PDF_COUNT"
        if [[ "$PDF_COUNT" -gt 0 ]]; then
            (
                for pi in *.pdf; do 
                    filename=$(basename "$pi")
                    filename="${filename%.*}"
                    pdftotext -layout -enc "UTF-8" "$TARGET_DIR"/pdf/${filename}.pdf "$TARGET_DIR"/text/${filename}.txt
                done
            ) || die "Failed during conversion of pdf to text generation"

        fi
        
    fi


    if [[ "$PDF_COUNT" -gt 0 ]]; then
        # Create one or more PDF bundles from the individual PDF files
        if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
            (
                cd "$TARGET_DIR"/pdf
                mkdir ../bundle
                BUNDLE_PREFIXES=$(find . -mindepth 1 -maxdepth 1 -type f -exec basename {} \; | perl -ale 'print $1 if /(\d+)\d{2}.pdf/;' | sort | uniq)
                for prefix in $BUNDLE_PREFIXES; do
                    FIRST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $1}')
                    LAST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $NF}')
                    FIRST=${FIRST_TMP::-4}
                    LAST=${LAST_TMP::-4}
                    pdftk ./"${prefix}"*.pdf cat output ../bundle/bundle-"${FIRST}-${LAST}".pdf
                done
            )
        fi
        say "Done Creating Proxy Invoices"
    else
       say "No proxy invoice found"
    fi
    say "Done Creating Proxy Invoices"
else
    die "Invalid Proxy Version"
fi
