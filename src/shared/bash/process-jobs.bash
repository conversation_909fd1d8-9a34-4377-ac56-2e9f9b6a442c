#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

POWEROFF=false
JOBS_DIR="${JOBS_DIR:-}"
RETAIN_JOB_FILES_DAYS="${RETAIN_JOB_FILES_DAYS:-+7}"
SOURCE='Unknown'
POWEROFF_DELAY=600 # 10 minutes
CHECKPOINT_DIR="/tmp/.$(basename "$0" .bash)"
CHECKPOINT_FILE="${CHECKPOINT_DIR}"/$$

function usage() {
    if [ "$*" != "" ] ; then
        scream "Error: $*"
    fi
    pre_usage "Processes jobs"
    cat >&2 <<EOF
Usage: ./process-jobs.bash [--options]
--jobs-dir                  job input directory (default $JOBS_DIR)
--source                    source of job (e.g., Invoice AutoMate)
--poweroff                  power off after running jobs (default $POWEROFF)
--retain-files              how long to retain job script files (default $RETAIN_JOB_FILES_DAYS)
--help                      display this usage message and exits
EOF
    post_usage
    exit 1
}


while [ $# -gt 0 ]; do
    case "$1" in
    --jobs-dir)
        JOBS_DIR="${2:?Directory Required}"
        [[ -d "$JOBS_DIR" ]] || die "Jobs directory $JOBS_DIR must exist."
        shift 
        ;;
    --poweroff)
        POWEROFF=true        
        ;;
    --retain-files)
        RETAIN_JOB_FILES_DAYS="${2:?Number of days is required}"
        shift
        ;;
    --source)
        SOURCE="${2:?Source required}"
        shift
        ;;
    --help)
        usage
        ;;
    *)
        die "Unrecognized option $1; exiting"
        exit 1
    esac
    shift
done

echo "------------------------------------------------------------"
echo "process-jobs.bash start @ $(date +%c)"
echo "------------------------------------------------------------"
echo "Jobs dir:     ${JOBS_DIR}"
echo "Source:       ${SOURCE}"
echo "Power off:    ${POWEROFF}"
echo "Retain files: ${RETAIN_JOB_FILES_DAYS}"
echo

function cleanup() {
   rm "$CHECKPOINT_FILE"
}

trap cleanup EXIT

function email() {
    local subject="$1"
    local job_log_file="$2"
    local to="$3"

    function get_email_message() {
        local num_lines=20

        head -n $num_lines "${job_log_file}"
        echo "----- See log file for details -----"
        tail -n $num_lines "${job_log_file}"
    }

    if type send-email >/dev/null 2>&1; then
        log "Sending email to ${to}"

        if send-email --to "${to}" \
                      --subject "${subject}" \
                      --body "$(get_email_message)" \
                      --send > /dev/null; then
            return
        fi
    fi
}

function wait_dir_change() {
   local dir="$1"
   local type="$2"
   local timeout="${3:-0}"
   local poll_delay="${4:-10}"
   local last_checkpoint=$(stat --format %Y "$CHECKPOINT_FILE")

   [[ -n "$type" ]] && type="-type $type"

   while true
   do
      sleep $poll_delay
      if [[ -n "$(find "$dir" $type -newermm "$CHECKPOINT_FILE")" ]]; then
         return 0
      elif ((timeout > 0)); then
         local now=$(date +%s)
         ((now - last_checkpoint > timeout)) && return 1
      fi
   done
}

function get_job_id() {
   local file="$(basename $1)"

   if [[ "$file" =~ ([^.]*) ]]; then
      echo ${BASH_REMATCH[1]}
   else
      echo ""
   fi
}

function log() {
    echo $@
}

function purge() {
    find "${JOBS_DIR}" -type f -mtime "$RETAIN_JOB_FILES_DAYS" -delete
}

function process-jobs() {
    local now pending job_file job_filename job_log_file job_id job_status jobs_waiting

    while true
    do
        touch "$CHECKPOINT_FILE"
        pending=($(ls -rt "${JOBS_DIR}/pending"))
        log "# of pending files: ${#pending[@]}"

        job_file=''
        jobs_waiting=false

        for f in ${pending[@]}; do
            job_id=$(get_job_id "$f")

            if [[ -z "$(find "${JOBS_DIR}/processing" -name "${job_id}*")" ]]; then
                job_file="${f}"
                break
            else
                jobs_waiting=true
                log "Job waiting: ${f}"
            fi
        done


        if [[ -z "${job_file}" ]]; then
            if [[ $jobs_waiting = 'true' ]]; then
                log "Waiting for a job to complete or a new job to arrive"
                wait_dir_change "${JOBS_DIR}"
                log "Resuming"
                continue
            elif tty -s; then
                break
            elif [[ $POWEROFF = "true" ]]; then
                log "Waiting to power off"
                if wait_dir_change "${JOBS_DIR}/pending" f $POWEROFF_DELAY; then
                    log "Resuming"
                    continue
                else
                    break
                fi
            else
                log "Waiting for a new job to arrive"
                wait_dir_change "${JOBS_DIR}/pending" f
                log "Resuming"
                continue
            fi
        fi

        if ! mv "${JOBS_DIR}/pending/${job_file}" "${JOBS_DIR}/processing"; then
            continue
        fi

        job_filename=$(basename "${job_file}")
        job_file="${JOBS_DIR}/processing/${job_filename}"
        job_log_file="${job_file}.log"
        job_email_file="${job_file}.mailto"

        log "Processing $job_file"
       
        echo "Starting Job: ${job_id} @ $(date)" > "${job_log_file}"
        
        if /usr/bin/time --f "Elapsed time: %E" -ao "${job_log_file}" "${job_file}" >> "${job_log_file}" 2>&1; then
            job_status="successfully"
        else
            job_status="with errors"
        fi

        # Remove escape sequences from log file
        sed -i 's/\x1b[^m]*m//g' "${job_log_file}"

        log "Job processed $job_status" | tee -a "${job_log_file}"
        log "Log file is ${JOBS_DIR}/processed/${job_filename}.log" | tee -a "${job_log_file}"

        if [[ -s "${job_email_file}" ]]; then
            email "$SOURCE job $job_id completed $job_status" "${job_log_file}" "$(< ${job_email_file})"
            rm "${job_email_file}"
        fi

        mv "${job_file}" "${job_log_file}" "${JOBS_DIR}/processed"        
        purge
    done
}

mkdir -p "$CHECKPOINT_DIR"
process-jobs

echo "------------------------------------------------------------"
echo "process-jobs.bash end @ $(date +%c)"
echo "------------------------------------------------------------"

if [[ $POWEROFF = "true" ]]; then
    if [[ "$(find "${CHECKPOINT_DIR}" -type f | wc -l)"  = 1 ]]; then
        log "Powering off"
        sudo poweroff
    fi
fi
