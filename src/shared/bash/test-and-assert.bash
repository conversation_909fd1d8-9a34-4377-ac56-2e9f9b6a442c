function assert_same_contents() {
    EXPECTED_FILE="${1:?Required Expected Path}"
    ACTUALS_FILE="${2:?Required Actuals Path}"
    if [[ "${3:-check}" = 'save' ]]; then
        diff --brief --new-file "$EXPECTED_FILE" "$ACTUALS_FILE"
        if [[ "$?" != '0' ]]; then
            echo "Copying Actuals $ACTUALS_FILE"
            cp "$ACTUALS_FILE" "$EXPECTED_FILE"
        fi
    fi
    diff --brief --new-file "$EXPECTED_FILE" "$ACTUALS_FILE"
    if [[ "$?" = 1 ]]; then
        diff --new-file "$EXPECTED_FILE" "$ACTUALS_FILE"
    else
        return 0
    fi
}

function dir_exists_or_die() {
    [[ -d "${1:?Directory Path Required}" ]] || die "Specified Directory Must Exist: $1"
}
