# Source this via the DMS-specific "*.env" files - which are themselves sourced...

export DMS="${REPO_DMS_KEY:?Must define REPO_DMS_KEY before sourcing this script}"
export DMS_HOME=DU-DMS/DMS-"$REPO_DMS_KEY"
export SRC_SCHEMA="du_dms_${REPO_DMS_KEY,,}"
export INVSEQ_LOAD_TYPE='--load-ext'

PARSE_SCRIPT=${DU_ETL_HOME}/DU-DMS/DMS-"$REPO_DMS_KEY"/do-parse
PROCESS_JOBS_SCRIPT=${DU_ETL_HOME}/src/shared/bash/do-process.bash
PROCESS_SCRIPT="${DU_ETL_HOME}"/DU-DMS/DMS-"$REPO_DMS_KEY"/do-process
EXPORT_SCRIPT="${DU_ETL_HOME}"/DU-DMS/DMS-"$REPO_DMS_KEY"/do-export
