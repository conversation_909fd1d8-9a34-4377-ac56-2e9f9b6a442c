# Not sourced via globals-index

function psql_qry() {
    psql "service=${GGS_PROD_PG_SERVICE}" --set=ON_ERROR_STOP=1 "$@"
}

function get_scenariokey_type() {
    local SCN_KEY_LABOR=''
    SCN_KEY_LABOR=$(psql_qry -At --set=scnkey="${1:?ScenarioKey Required}" <<SQL
        SELECT islabor FROM magescenario WHERE scenariokey = :'scnkey';
SQL
)

    if  [[ "$SCN_KEY_LABOR" = 'f' ]]; then
        echo 'parts'
    elif [[ "$SCN_KEY_LABOR" = 't' ]]; then
        echo 'labor'
    else
        echo 'unknown'
        return 1
    fi
    return 0
}
