### Provides functions wrapping echo and, in some cases, exit.

source "$DU_ETL_HOME"/src/shared/bash/ansi-color-constants.bash


QUIT_FILE=~/tmp/du-etl-quiting

[ -e "$QUIT_FILE" ] && rm "$QUIT_FILE"

# Pass back general status and/or progress information
# to the caller, in color.  Use this for primary information
# as it gets passed along on stdout via echo.
# A copy of the message is recorded via a call to info()
function say() {
    [ -e "$QUIT_FILE" ] && exit 99
    echo -n -e $txt_green
    echo -n    "$@"
    echo    -e $txt_reset
    info "$@"
    return 0
}

function progress() {
    [ -e "$QUIT_FILE" ] && exit 99
    echo -n -e $txt_cyan
    echo -n    "$@"
    echo    -e $txt_reset
    trace "$@"
    return 0
}

# While typically one would expect to die after yelling
# at the user (on stdout) if for some reason a simple
# notify-and-continue flow is needed this function can be used.
# A copy of the message is recorded via a call to warn()
function yell() {
    [ -e "$QUIT_FILE" ] && exit 99
    echo -n -e $txt_yellow
    echo -n    "$@"
    echo    -e $txt_reset
    warn "$@"
    return 0
}

# Pass the supplied arguments to echo while wrapping
# the entire output in color.  The invoke exit 1.
# All output is sent to stderr
# A copy of the message is recorded via a call to fatal()
function die() {
    [ -e "$QUIT_FILE" ] && exit 99
    echo -n -e $txt_red
    echo -n    "$@"
    echo    -e $txt_reset
    fatal "$@"
    exit 1
} >&2

# Basically a die() without the dying
# It is expected the caller will self-immolate shortly
function scream() {
    [ -e "$QUIT_FILE" ] && exit 99
    echo -n -e $txt_red
    echo -n    "$@"
    echo    -e $txt_reset
    fatal "$@"
} >&2

# Basically a "die" but without the failure connotation
# The user indicated their desire to stop the script

function quit() {
    echo  -n -e $txt_yellow
    if [[ $# = 0 ]]; then
        echo -n "Quitting per user request"
    else
        echo -n "$@"
    fi
    echo     -e $txt_reset
    echo "" > "$QUIT_FILE"
    exit 99
}

# Shared log output function for trace/debug/info
function do_log() {
    if [[ -z "${DU_ETL_DEBUG_MODE:=0}" ]]; then
        return 0
    fi
    MIN_MODE=$1
    MODE_TEXT=$2
    shift 2
    if ((DU_ETL_DEBUG_MODE < MIN_MODE)); then
        return 0
    fi
    printf "%s: %s: %-80s <%s>\n" $(date +%H:%M:%S) $MODE_TEXT "$@" $(basename $0) \
         >> "${DU_ETL_DEBUG_TARGET:-/dev/null}"
}

# Echo the input if the current ETL_DEBUG_MODE level is >= 2
function trace() {
    do_log 2 "TRACE" "$@"
}

# Echo the input if the current ETL_DEBUG_MODE level is >= 1
function debug() {
    do_log 1 "DEBUG" "$@"
}

# Echo the input; is expected to be called from "say"
function info() {
    do_log 0 "INFO-" "$@"
}

# Like INFO, warning cannot be disabled
# but places a WARN- prefix on the logged line
# This is expected to called from "yell()"
function warn() {
    do_log 0 "WARN-" "$@"
}

# Fatal is like WARN but is expected to be called
# via "die()".
function fatal() {
    do_log 0 "FATAL" "$@"
}
