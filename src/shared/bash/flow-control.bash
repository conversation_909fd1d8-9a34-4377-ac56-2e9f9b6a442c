# Source this to gain access to various functions related to flow-control and input capture

# Provides an prompt (optionally user-specified)
# to the user and dies if the user is disinclined
# to continue.
function prompt_continuation() {
    local prompt="${1:-Do you wish to continue?}"
    shift
    local answer

    while true; do
        echo -e -n "${txt_prompt}$prompt [Y/n/e]: ${txt_reset}"; read -n 1 answer
        echo '' # to get a newline before moving on
        [ -z "$answer" ] && answer="${1:-Y}"

        case "$answer" in
            [yY] )
                break # break out of the loop and pass control back for continuation
                ;;
            [nNeE] )
                die "Quitting - User Request"
                ;;
            * )
                scream "Unrecognized Input"
                : # do nothing and return control to the loop
                ;;
        esac
    done
    return 0
}

# Provide a prompt allowing the user to choose to
# indicate their desired to "Retry" some previously
# performed input or computation.
# A value of "true" or "false" is placed
# into the global RETRY_ANSWER variable
# It is assumed that a retry is generally
# not required so the original default is false/do not retry
function prompt_retry() {
    RETRY_ANSWER=
    local prompt="${1:-Do you wish to make any changes?}"
    shift
    local answer

    while true; do
        echo -e -n "${txt_prompt}$prompt [y/N/e]: ${txt_reset}"; read -n 1 answer
        echo '' # to get a newline before moving on
        [ -z "$answer" ] && answer="${1:-N}"

        case "$answer" in
            [yY] )
                RETRY_ANSWER=true
                break
                ;;
            [nN] )
                RETRY_ANSWER=false
                break
                ;;
            [eE] )
                quit
                break
                ;;
            * )
                scream "Unrecognized Input"
                : # do nothing and return control to the loop
                ;;
        esac
    done
    return 0
}
