
# Clear the named directory of all its contents,
# recurisive, without erroring if it is already empty.
# Create it if necessary.
function clear_dir() {
    mkdir -p "$1"
    find "$1" -mindepth 1 -delete
}

function select_file() {
    local dir_path="${1:?Directory Path Required}"
    (
        local chosen_file=''
        PS3="Select a file by number: "

        cd "$dir_path" || return 1

        select opt in * \
                  "None of the above..."
        do
            if [[ "$opt" = "None of the above..." ]]; then
                return 0
            else
                chosen_file="$dir_path"/"$opt"
            fi
            break
        done
        echo "$chosen_file"
    )
    return
}

function has_files() {
    if [[ ! -d "${1:?Directory required for has_files test}" ]]; then
        echo "Has file tests requires directory argument: $1"
        return 2
    fi
    if find "$1" -mindepth 1 -maxdepth 1 -print -quit | grep -q .; then
        return 0
    else
        return 1
    fi
}
