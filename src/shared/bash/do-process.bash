
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
echo "${1}"
[[ -n "${1}" ]]            || die "First argument must be target directory for source files"
[[ -d "${1}" ]]            || die "First argument isn't a directory ${1}"
[[ -n "${2}" ]]            || die "Second argument must be target schema for source tables"

TARGET_DIR="${1}"
SRC_SCHEMA="${2}"
TSV_DIR=${TARGET_DIR%/*}
#export UNIQUE_ID=$(uuidgen)
echo ${UNIQUE_ID}
echo ${UNIQUE_ID} > ${TARGET_DIR}/unique-id.value


# ${DU_ETL_HOME}/src/shared/excluded-paytype-list $SRC_SCHEMA
# ${DU_ETL_HOME}/src/shared/excluded-department-list $SRC_SCHEMA


function load_invoice_selections() {
    debug "Load Invoice Selections"
    export START_INV_FILE=~/tmp/du-process-invoicesequence-start-invoice.txt
    export END_INV_FILE=~/tmp/du-process-invoicesequence-end-invoice.txt
    export BREAK_AT_FILE=~/tmp/du-process-invoicesequence-break-invoice.txt
    export RESTART_AT_FILE=~/tmp/du-process-invoicesequence-restart-invoice.txt
    if [[ -e "$START_INV_FILE" ]]; then
        START_INV=$(< "${START_INV_FILE}" )
    fi
    if [[ -e "$END_INV_FILE" ]]; then
        END_INV=$(< "${END_INV_FILE}" )
    fi
    if [[ -e "$BREAK_AT_FILE" ]]; then
        BREAK_AT=$(< "${BREAK_AT_FILE}" )
    fi
    if [[ -e "$RESTART_AT_FILE" ]]; then
        RESTART_AT=$(< "${RESTART_AT_FILE}" )
    fi
}

load_invoice_selections
# psql wrapper that outputs raw content to stdout
function psql_out() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}'" \
     --set=UUID="${UNIQUE_ID}"
    return $?
}

function psql_table() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}'" \
        --set=ON_ERROR_STOP=1 "$@"
    return
}
function psql_general() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}'" \
        --set=ON_ERROR_STOP=1 "$@" \
        --set=START_INV="${START_INV}" \
        --set=END_INV="${END_INV}" \
        --set=RESTART_AT="${RESTART_AT}" \
        --set=BREAK_AT="${BREAK_AT}" \
        --set=DMS="${DMS}"
    return
}

function delete_invoice_sequence(){
 echo "Deleting invalid ros"   
 psql_general <<SQL
  CREATE TABLE invalid_ros AS 
 with inv_seq as (
        select generate_series((start_ro :: INTEGER) ,(COALESCE(end_ro, start_ro))::integer) as invoicenumber from sequence_detail 
    ) , sequence_string AS (       
        select string_agg(invoicenumber::text, ',') AS seq_ros from inv_seq 
    )
    SELECT invoicenumber::text from inv_seq ;
 DELETE FROM client_partsdetails WHERE invoicenumber IN (SELECT invoicenumber FROM invalid_ros);
 DELETE FROM client_labordetails WHERE invoicenumber IN (SELECT invoicenumber FROM invalid_ros );
 DELETE FROM client_job_details WHERE invoicenumber IN (SELECT invoicenumber FROM invalid_ros);
 DELETE FROM client_invoicedetails WHERE invoicenumber IN (SELECT invoicenumber FROM invalid_ros);
 DELETE FROM client_discount_details WHERE invoicenumber IN (SELECT invoicenumber FROM invalid_ros);
 DELETE FROM client_gl_accounting WHERE invoicenumber IN (SELECT invoicenumber FROM invalid_ros);
SQL
}

function save_unique_id() {
    psql_out <<SQL
        INSERT INTO client_uuid(unique_id) VALUES(:'UUID');
SQL
}
save_unique_id

function save_all_department() {
    psql_out <<SQL
        INSERT INTO client_department_details(allowed_dept)
        SELECT array_agg(DISTINCT deptname) FROM client_invoicedetails WHERE deptname IS NOT NULL;
        UPDATE client_department_details 
        	SET disallowed_dept = (SELECT ARRAY_AGG(department_name) FROM department_details);
        UPDATE client_department_details 
        SET allowed_dept = (SELECT ARRAY (
        	SELECT unnest(allowed_dept)
      		EXCEPT ALL
      		SELECT unnest(disallowed_dept)
      	) FROM client_department_details);



SQL
}
save_all_department


function state_rules_warranty_count() {
     psql_general <<SQL
  \copy (SELECT (SELECT count(*) FROM exportable_labor_detail WHERE paytype ~ '#' AND paytype ~ '^W')||'/'||(SELECT count(*) FROM exportable_labor_detail WHERE paytype ~ '^W')) TO '${TARGET_DIR}/state-rules-item-count.csv' WITH (FORMAT csv, HEADER false)
SQL
}

function delete_outofrange_ros() {
 psql_general <<SQL
 CREATE TABLE valid_ros AS SELECT * FROM du_etl_t_invseq.generate_ro_sequence(:'START_INV',:'END_INV',:'BREAK_AT',:'RESTART_AT');
 DELETE FROM client_partsdetails WHERE invoicenumber NOT IN (SELECT invoicenumber FROM valid_ros WHERE opendate IS NOT NULL);
 DELETE FROM client_labordetails WHERE invoicenumber NOT IN (SELECT invoicenumber FROM valid_ros WHERE opendate IS NOT NULL);
 DELETE FROM client_job_details WHERE invoicenumber NOT IN (SELECT invoicenumber FROM valid_ros WHERE opendate IS NOT NULL);
 DELETE FROM client_invoicedetails WHERE invoicenumber NOT IN (SELECT invoicenumber FROM valid_ros WHERE opendate IS NOT NULL);
 DELETE FROM client_discount_details WHERE invoicenumber NOT IN (SELECT invoicenumber FROM valid_ros WHERE opendate IS NOT NULL);
 DELETE FROM client_gl_accounting WHERE invoicenumber NOT IN (SELECT invoicenumber FROM valid_ros WHERE opendate IS NOT NULL);
SQL

}

function update_partnumber_detail() {
    
    psql_general <<SQL
    UPDATE client_partsdetails
    SET partnumber = pn_new
    FROM (VALUES
           ('SL-JEEP-JK-SMK-G1-FBA',    'SLJEEPJKSMKG1FBA'),
           ('261I/***********ISATE',    '261I/**********'),
           ('000-989-79-02-11-BIFU',    '000989790211BIFU'),
           ('FO1014106PP*FRTCOVER*',    'FO1014106PP*FRTCOVER'),
           ('FO1015105PP*LWRCOVER*',    'FO1015105PP*LWRCOVER'),
           ('FO1241257PP*FENDER*RT',    'FO1241257PP*FENDERRT'),
           ('CH1249154*INNER*SHEILD',   '********************'),
           ('************UNDERSHIELD*', '***********UNDERSHLD'),
           ('CH1225235PP*RAD*TIEBAR',   '********************'),
           ('CH1242175PP*RH*FENDER',    'CH1242175PP*RHFENDER'),
           ('NI1000216PP*FRT*COVER',    'NI1000216PP*FRTCOVER'),
           ('68406529AABAUMANCHRYSLER', '68406529AABAUMANCHRY'),
           ('R4896464ACBAUMANCHRYSLER', 'R4896464ACBAUMANCHRY'),
           ('12*VOLT*SOLUTIONS_H-KEY',  '12*VOLT*SOLU_HKEY'),
           ('RAD/COND/FAN/MOTOR/LKQ',   'RAD/COND/FAN/MOTOR'),
           ('ROAD*HAZARD*18ANDABOVE',   'ROADHAZARD18ANDABOVE'),
           ('ROAD*HAZARD*17ANDBELOW',   'ROADHAZARD17ANDBELOW'),
           ('34YR8F1A2XLV2784764348',   '348F1A2XLV2784764348'),
           ('MICHELIN/PILOTSPORTC2',    'MICHELINPILOTSPORTC2'),
           ('FAULKNISSANB889UHS26J',    'FAULKNISSANB889UHS26'),
           ('AMLHSWAYLINK/K750282/93821', 'AMLHSWAYLINK/K750282'),
           ('CLOCKSPRINF/DODGEQ2770', 'CLOCKSPRINF/DODGEQ27'),
           ('VALVECOVERGASKET/93821', 'VALVECOVERGASKET/938'),
           ('WATEROUTHOUSING/93821', 'WATEROUTHOUSING/9382'),
           ('A/CCONNECTWITHLEADS/1300', 'A/CCONNECTWITHLEADS/'),
           ('USEDDOORCABLEASSY/1251', 'USEDDOORCABLEASSY/12'),
           ('REARBRAKEHRDKIT/93821', 'REARBRAKEHRDKIT/9382'),
           ('RACK&PINIONASSY/93821', 'RACK&PINIONASSY/9382'),
           ('FUELSENDER/Q40656/KIA', 'FUELSENDER/Q40656/KI'),
           ('WHEELBEARING/FW395/1914', 'WHEELBEARING/FW395/1'),
           ('TSTAT/W/HOSE&SEAL/93821', 'TSTAT/W/HOSE&SEAL/93'),
           ('TAILCAP/SPOILER/10285',  'TAILCAP/SPOILER/1028'),
           ('RHFTSTRUTLOADED/93821',  'RHFTSTRUTLOADED/9382')
      ) vals (pn_old, pn_new)
    WHERE partnumber = pn_old;

SQL

psql_general <<SQL
    UPDATE client_partsdetails 
    SET partnumber = TRIM(partnumber), 
    normalized_part_number = TRIM(normalized_part_number) 
    WHERE partnumber IS NOT NULL;

    UPDATE client_partsdetails 
    SET partnumber = 'NPN', 
    normalized_part_number = 'NPN'
    WHERE NULLIF(TRIM(partnumber), '') IS NULL;
SQL

psql_general <<SQL
    UPDATE client_partsdetails
    SET partnumber = replace(partnumber, '-', '')
    WHERE length(partnumber) > 20;
SQL
psql_general <<SQL
    UPDATE client_partsdetails
    SET partnumber = replace(partnumber, '*', '')
    WHERE length(partnumber) > 20;
SQL
psql_general <<SQL
    UPDATE client_partsdetails
    SET partnumber = replace(partnumber, '/', '')
    WHERE length(partnumber) > 20;
SQL
psql_general <<SQL
    UPDATE client_partsdetails d
    SET partnumber = substr(partnumber, 1, 20)
    WHERE length(partnumber) > 20
    AND NOT EXISTS
        (SELECT partnumber FROM client_partsdetails WHERE partnumber = substr(d.partnumber, 1,20));
SQL
psql_general <<SQL
    UPDATE client_partsdetails
    SET partdescription = partnumber
    WHERE NULLIF(partdescription,'') IS NULL;
SQL
psql_general <<SQL
   UPDATE client_labordetails
    SET opcode = TRIM(opcode)
    WHERE opcode IS NOT NULL;
SQL
psql_general <<SQL
    ALTER TABLE client_labordetails
       ADD COLUMN opcode_old text;
    UPDATE client_labordetails
    SET opcode_old = opcode
    WHERE LENGTH(opcode) > 0;
SQL
psql_general <<SQL
    UPDATE client_labordetails
    SET opcode = COALESCE(opcode,'NO-OP') || CASE WHEN use_alt_desc                       
                                 THEN '-' || to_char(multiple_rank, 'FM0099')   
                                 WHEN has_multiple_desc                         
                                 THEN '-MISC'                                   
                                 ELSE ''                                        
                             END
    WHERE :'DMS' NOT IN ('PBS','Quorum');
SQL
psql_general <<SQL
    UPDATE client_labordetails
    SET opcode = pn_new
    FROM (VALUES
           ('0114MAINT0600723$35-MISC', '0114MAINT0600723$35'),
           ('0114MAINT0600723$35-001',  '0114MAINT0600723$35@'),
           ('PROGRAMMING/CODING-001',   'PROGRAM/CODING-001'),
           ('PROGRAMMING/CODING-MISC',  'PROGRAM/CODING-MISC'),
           ('DETAILFULL(TRUCK)MISC',    'DETAILFULLTRUCKMISC'),
           ('100,100,100,100,100-001',  '100X5-001'),
           ('100,100,100,100,100-MISC', '100X5-MISC'),
           ('55CVZBRAKES/ROTOR-001',    '55CVZBRAKESROTOR001'),
           ('00CVZDIESELREPAIR-MISC',   '00CVZDIESELREPAIRMSC'),
           ('$5.00OFFOILCHANGEMISC',    '$500OFFOILCHANGEMISC')
       ) vals (pn_old, pn_new)
    WHERE opcode = pn_old;
SQL
psql_general <<SQL
    UPDATE client_labordetails
    SET opcode = 'DETAIL-FULL(TRUCK)'
    WHERE opdescription = 'CHARITY/AUCTION PURCHASED BY CUSTOMER-';
SQL
psql_general <<SQL
    UPDATE client_labordetails
    SET opcode = 'MISC-LONG-OP'
    WHERE length(opcode) > 50;
SQL
psql_general <<SQL
    UPDATE client_labordetails
    SET opcode = replace(opcode, '-', '')
    WHERE length(opcode) > 20;
SQL
psql_general <<SQL
    UPDATE client_labordetails
    SET opcode = replace(opcode, '*', '')
    WHERE length(opcode) > 20;
SQL
psql_general <<SQL
    UPDATE client_labordetails
    SET opcode = replace(opcode, '/', '')
    WHERE length(opcode) > 20;
SQL
psql_general <<SQL
    UPDATE client_labordetails d
    SET opcode = substr(opcode, 1, 20)
    WHERE length(opcode) > 20
    AND NOT EXISTS
     (SELECT opcode FROM client_partsdetails WHERE opcode = substr(d.opcode, 1,20));
SQL
}

function export_client_invoicedetails() {
    psql_table <<SQL
      \copy (SELECT                                 \
        invoicenumber,                              \
        open_date,                                  \
        close_date,                                 \
        void_date,                                  \
        vehicle_year,                               \
        COALESCE(renamed_make_name,vehicle_make),   \
        vehicle_model,                              \
        vin,                                        \
        customer_id,                                \
        customer_name,                              \
        advisor_id,                                 \
        store_code,                                 \
        is_current_store,                           \
        branch,                                     \
        deptname,                                   \
        mileage,                                    \
        comment                                     \
       FROM client_invoicedetails LEFT JOIN client_makerenames ON (vehicle_make = original_make_name) \
       WHERE invoicenumber IN((SELECT invoicenumber FROM client_partsdetails) \
       UNION (SELECT invoicenumber FROM client_labordetails)) AND close_date IS NOT NULL) TO '${TARGET_DIR}/invoice-master.csv' WITH (FORMAT csv, HEADER true)
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export invoice master"
fi
}
function update_opcode() {

     psql_table <<SQL      
       WITH opcodes_with_multiple_descriptions AS (   
        SELECT *, row_number() OVER (PARTITION BY group_code 
                                    ORDER BY item_count DESC, group_description) AS group_index 
          FROM (  
                SELECT opcode  AS group_code,
                      opdescription AS group_description,
                      count(*) AS item_count, 
                      count(*) OVER (PARTITION BY opcode) AS op_count
                  FROM client_labordetails 
              GROUP BY 1, 2 
              ORDER BY 1, 2 DESC 
        ) src 
        WHERE op_count > 1
        ) 
        UPDATE client_labordetails 
          SET multiple_rank         = group_index, 
              has_multiple_desc     = true,
              alt_group_desc        = opdescription, 
              use_alt_desc          = item_count > 5 
          FROM opcodes_with_multiple_descriptions
        WHERE group_description = opdescription 
          AND group_code = opcode;
SQL

}

function update_opcode_special() {

     psql_table <<SQL
       WITH opcodes_with_multiple_descriptions AS (   
        SELECT *, row_number() OVER (PARTITION BY group_code 
                                    ORDER BY item_count DESC, group_description) AS group_index 
          FROM (  
                SELECT opcode  AS group_code,
                      opdescription AS group_description,
                      count(*) AS item_count, 
                      count(*) OVER (PARTITION BY opcode) AS op_count
                  FROM client_labordetail
              GROUP BY 1, 2 
              ORDER BY 1, 2 DESC 
        ) src 
        WHERE op_count > 1
        ) 
        UPDATE client_labordetails 
          SET multiple_rank         = group_index, 
              has_multiple_desc     = true,
              alt_group_desc        = opdescription, 
              use_alt_desc          = item_count > 5 
          FROM opcodes_with_multiple_descriptions
        WHERE group_description = opdescription
          AND group_code = opcode;
SQL

}

function export_client_labordetail() {
    psql_table <<SQL
      \copy (SELECT                                                             \
      cl.invoicenumber                              AS invoicenumber,           \
      open_date,                                                                \
      close_date,                                                               \
      'L'                                           AS laborsource,             \
      opcode                    		            AS opcode,                  \
      CASE WHEN use_alt_desc                                                    \
        THEN alt_group_desc                                                     \
        ELSE COALESCE(opdescription,opcode,'NO-OP')                             \
      END                                           AS opcodedescription,       \
      base_paytype                                  AS paytype,                 \
      CASE WHEN unit_sale::numeric >=0                                        	\
        THEN unit_hours                                                     	\
        ELSE '0.00'                             							    \
      END  											AS hours,                   \
      CASE WHEN unit_sale::numeric >=0                                        	\
        THEN round(unit_sale,2)                                                 \
        ELSE '0.00'                             								\
      END  											AS unitsale,                \
      CASE WHEN unit_sale::numeric >=0                                        	\
        THEN round(extended_sale,2)                                            	\
        ELSE '0.00'                             								\
      END  											AS extendedsale,            \
      vehicle_year,                                                             \
      vehicle_make,                                                             \
      vehicle_model,                                                            \
      vin,                                                                      \
      advisor_id 									AS advisorid,               \
      customer_id									AS customernumber,          \
      jobid                                         AS jobid,                   \
      row_number() OVER (PARTITION BY cl.invoicenumber,jobid ORDER BY jobid)    AS lineindex,   \
      NOW()	                                        AS recordcreationdate       \
    FROM client_labordetails cl                                                 \
    JOIN client_invoicedetails cm ON cl.invoicenumber = cm.invoicenumber        \
    WHERE close_date IS NOT NULL) TO '${TARGET_DIR}/labor-details.csv' WITH (FORMAT csv, HEADER true)
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export labor details"
fi
}

function export_client_partsdetail() {
    psql_table <<SQL
      \copy (SELECT                                                                              \
      cp.invoicenumber                              				AS invoicenumber,            \
      open_date,                                                                                 \
      close_date,                                                                                \
      base_partsource                               				AS partsource,               \
      COALESCE(partnumber, 'NPN')	            				AS partnumber,                   \
      COALESCE(partdescription,partnumber, 'NPN')   				AS partdescription,          \
      base_paytype                                  				AS paytype,                  \
      unitcount                                     				AS quantity,                 \
      round(unitcost*unitcount,2)	                    			AS extendedcost,             \
      round(unitsale*unitcount,2)	                    			AS extendedsale,             \
      round(unitcost,2)	                                    			AS unitcost,             \
      round(unitsale,2)	                                    			AS unitsale,             \
      list_price,                                                                                \
      vehicle_year,                                                                              \
      vehicle_make,                                                                              \
      vehicle_model,                                                                             \
      vin,                                                                                       \
      advisor_id 									AS advisorid,                                \
      customer_id									AS customernumber,                           \
      jobid                                         				AS jobid,                    \
      row_number() OVER (PARTITION BY cp.invoicenumber,jobid ORDER BY jobid, part_line)    AS lineindex,    \
      round(corecharge_cost,2)                               			AS corechargecost,       \
      round(corecharge_sale,2)                               			AS corechargesale,       \
      round(corereturn_cost,2)                               			AS corereturncost,       \
      round(corereturn_sale,2)                               			AS corereturnsale,       \
      kit_name                                      				AS kitname,                  \
      NOW()	                                    				AS recordcreationdate            \
    FROM client_partsdetails cp JOIN client_invoicedetails cm ON cp.invoicenumber = cm.invoicenumber        \
    WHERE close_date IS NOT NULL ORDER BY cp.invoicenumber, jobid, part_line) TO '${TARGET_DIR}/part-details.csv' WITH (FORMAT csv, HEADER true)
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export parts details"
fi
}

function export_jobdetails() {
    psql_table <<SQL
        \copy (SELECT * FROM client_job_details     \
               where invoicenumber not in(select invoicenumber from client_invoicedetails    \
                                           where close_date is null)                         \
               ORDER BY invoicenumber ASC) TO '${TARGET_DIR}/job-details.csv' WITH (FORMAT csv, HEADER true)
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export job details"
fi
}

function export_discountdetails() {
   psql_table <<SQL
        \copy (SELECT * FROM client_discount_details     \
               where invoicenumber not in(select invoicenumber from client_invoicedetails    \
                                           where close_date is null)                         \
                ORDER BY invoicenumber ASC) TO '${TARGET_DIR}/discount-details.csv' WITH (FORMAT csv, HEADER true)
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export discount details"
fi
}

function export_accountdetails() {
    psql_table <<SQL
        CREATE TEMP TABLE gl_accounting_details AS
        SELECT * FROM client_gl_accounting
        where invoicenumber not in(select invoicenumber from client_invoicedetails    \
                                           where close_date is null)
        ORDER BY invoicenumber ASC;
        \copy (SELECT * FROM gl_accounting_details) TO '${TARGET_DIR}/gl-accounting-details.csv' WITH (FORMAT csv, HEADER true)
SQL
}
function export_makedetails() {
  psql_table <<SQL
        CREATE TEMP TABLE make_details AS
          SELECT               \
            (SELECT '{' || STRING_AGG(DISTINCT(makes), ',') || '}' FROM (SELECT * FROM (SELECT unnest(actual_makes) makes FROM du_etl_t_makes.assigned_other) q WHERE makes NOT IN (SELECT unnest(related_makes) FROM client_scenarioinfo))t) AS disallowed_makes, \
            (SELECT '{' || STRING_AGG(DISTINCT(makes), ',') || '}' FROM (SELECT unnest(related_makes) makes FROM client_scenarioinfo) ) AS allowed_makes,  \
            (SELECT '{' || STRING_AGG(make_name,',') || '}' FROM du_etl_t_makes.unassigned_makes where make_name not in (SELECT unnest(related_makes) FROM client_scenarioinfo)) AS unknown_makes; \
        \copy (SELECT * FROM make_details) TO '${TARGET_DIR}/make-details.csv' WITH (FORMAT csv, HEADER true)
SQL

  }

function export_generaldetails() {
  psql_general <<SQL
        CREATE TEMP TABLE general_details AS
          SELECT               \
            (SELECT '{' || STRING_AGG(DISTINCT(makes), ',') || '}' FROM (SELECT * FROM (SELECT unnest(actual_makes) makes FROM du_etl_t_makes.assigned_other) q WHERE makes NOT IN (SELECT unnest(related_makes) FROM client_scenarioinfo))t) AS disallowed_makes, \
            (SELECT ARRAY_AGG(elem) AS combined_array FROM ( SELECT unnest(related_makes::text[]) AS elem FROM client_scenarioinfo) AS unnested) AS allowed_makes,  \
            (SELECT '{' || STRING_AGG(make_name,',') || '}' FROM du_etl_t_makes.unassigned_makes where make_name NOT IN (SELECT unnest(related_makes) FROM client_scenarioinfo)) AS unknown_makes, \
            NULL AS start_inv, \
            NULL AS end_inv,  \
            json_agg(json_build_object(
        	'start_inv', '',
        	'end_inv', '',
        	'restart_at', '',
       	 'break_at', ''
    		)) AS import_invoice_seq_ranges,\
            ( SELECT ARRAY_AGG(paytype) FILTER ( WHERE project_type = 'parts' and type = 'C' AND is_allowed = false) FROM paytype_details 
            ) AS parts_excl_retail_pattype, 
            (SELECT ARRAY_AGG(paytype) FILTER ( WHERE project_type = 'parts' and type = 'W' AND is_allowed = false) FROM paytype_details 
            ) AS parts_excl_warranty_pattype, 
            (SELECT ARRAY_AGG(paytype) FILTER ( WHERE project_type = 'labor' and type = 'C' AND is_allowed = false) FROM paytype_details
            ) AS labor_excl_retail_pattype, 
            (SELECT ARRAY_AGG(paytype) FILTER ( WHERE project_type = 'labor' and type = 'W' AND is_allowed = false) FROM paytype_details 
            ) AS labor_excl_warranty_pattype, 
            ''::text AS parts_upper_band, \
            ''::text AS labor_upper_band, 
            ''::text AS dominant_parts_warr_mu, \
            ''::text AS dominant_labor_warr_lr, 
            null AS dominant_labor_warr_lr_json, 
			null AS dominant_parts_warr_mu_json, 
            ''::text AS dominate_wlr_calc, \
            ''::text AS dominate_wmu_calc, \
            (SELECT allowed_dept FROM client_department_details LIMIT 1) AS allowed_department, \
			(SELECT disallowed_dept FROM client_department_details LIMIT 1) AS disallowed_department, \
            CASE WHEN :'DMS' = ANY('{Reynolds}') THEN 
                true
        	ELSE 
        	    false
        	END AS is_non_job_based, \
            (SELECT scheduler_id FROM client_scheduler_id LIMIT 1) AS scheduler_id; \
        \copy (SELECT * FROM general_details) TO '${TARGET_DIR}/general-details.csv' WITH (FORMAT csv, HEADER true)
SQL

}

function export_ro_sequence() {
    psql_table <<SQL
        CREATE TEMP TABLE ro_sequence AS
      WITH inv_master AS (SELECT
                                invoicenumber,
                                regexp_replace(invoicenumber, '[[:alpha:]s]', '', 'g')::integer inv_number,
                                open_date,
                                close_date,
                                void_date
                                FROM client_invoicedetails
                            ), 
                            ro_sequence AS (SELECT generate_series(min_invoicenumber,
                                             max_invoicenumber) AS ro_number
                                        FROM (SELECT
                                                min(inv_number) AS min_invoicenumber,
                                                max(inv_number) AS max_invoicenumber
                                            FROM inv_master
                                            ) t
                            )
                        SELECT														
                            ROW_NUMBER()												
                            OVER (													
                                ORDER BY COALESCE(number_part::integer, ro_number::integer ), code_part nulls first) AS sequence_no,			
                            ro_number,													
                            open_date,													
                            close_date,														
                            void_date														
                        FROM (SELECT													
                                coalesce(ro_number::text , invoicenumber)                                        AS ro_number,	
                                substring(invoicenumber from '^[0-9]+')                                          AS number_part,	
                                substring(invoicenumber from '[A-Za-z]+$')                                       AS code_part,	
                                open_date,												
                                close_date,												
                                void_date													
                            FROM ro_sequence												
          FULL OUTER JOIN inv_master ON ro_number :: text = invoicenumber						
     ) t;
 \copy ro_sequence TO '${TARGET_DIR}/ro-sequence.csv' WITH (FORMAT csv, HEADER true)
     
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export RO Sequence"
fi
}

function export_paytypedetails() {
    psql_table <<SQL
        \copy (SELECT * FROM client_paytype_details) TO '${TARGET_DIR}/paytype-details.csv' WITH (FORMAT csv, HEADER true)
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export job details"
fi
}

function export_ro_sequence_job() {
    psql_general <<SQL
  CREATE TABLE ro_sequence_new AS 
    WITH seq AS ( 
        SELECT
            "invoicenumber" as ro_number,
            open_date, close_date,void_date,
            ((regexp_matches(invoicenumber, '^(\d+)\D*$')) [1])::integer AS ro_num_int
        FROM client_invoicedetails rd 
        ORDER BY 3, 1
    )
    , seq_1 AS (
        SELECT *,
            COALESCE(lead(ro_num_int) OVER seq_range > ro_num_int + 100, true) AS shift_seq,
            COALESCE(lag(ro_num_int) OVER seq_range < ro_num_int - 100, true)  AS new_seq 
        FROM seq
        WINDOW seq_range AS (ORDER BY ro_num_int, ro_number) 
    )
    , start_end_seq AS (
        SELECT *
        FROM seq_1
        WHERE shift_seq OR new_seq
    )
    , wind_seq AS (
        SELECT
            ro_number                                              AS start_ro,
            lead(ro_number) OVER(ORDER BY ro_num_int, ro_number)   AS end_ro,
            ro_num_int                                             AS start_ro_int,
            lead(ro_num_int) OVER(ORDER BY ro_num_int, ro_number)  AS end_ro_int,
            open_date                                              AS start_ro_date,
            lead(open_date) OVER(ORDER BY ro_num_int, ro_number)   AS end_ro_date,
            lead(shift_seq) OVER(ORDER BY ro_num_int, ro_number)   AS end_shift_seq,
            shift_seq,
            new_seq
        FROM start_end_seq
    )
   , audit_range AS(
            SELECT
                ROW_NUMBER() OVER(ORDER BY start_ro_date, end_ro_date, start_ro_int) AS range_seq,
                start_ro_int as start_ro,
                CASE WHEN shift_seq AND new_seq 
                    THEN start_ro_int 
                ELSE end_ro_int END AS end_ro,
                start_ro_date,
                CASE WHEN shift_seq AND new_seq 
                    THEN start_ro_date
                ELSE end_ro_date END AS end_ro_date
            FROM wind_seq
            WHERE new_seq
            ORDER BY start_ro_date, end_ro_date, start_ro_int
      )
      , derivatives3 AS (      
      select 
        generate_series((start_ro :: INTEGER) ,(COALESCE(end_ro, start_ro))::integer) as invoicenumber,
        start_ro_date, end_ro_date
        from audit_range 
      )
     select
       ROW_NUMBER() OVER(ORDER BY  der.start_ro_date, der.end_ro_date ,der.invoicenumber) AS invoicesequence,
       COALESCE(ci.ro_number::text, der.invoicenumber::text) AS invoicenumber,
       ci.open_date, ci.close_date,ci.void_date
       from derivatives3 der
       left JOIN seq ci on  der.invoicenumber=ci.ro_num_int;
  \copy (SELECT invoicesequence, invoicenumber, open_date, close_date, void_date FROM ro_sequence_new) TO '${TARGET_DIR}/ro-sequence.csv' WITH (FORMAT csv, HEADER true)
SQL
     
if [[ ! $? = '0' ]]; then
    die "Could not export RO Sequence"
fi
}

function export_paymentdetails() {
   psql_table <<SQL
        \copy (SELECT * FROM client_payment_details ORDER BY invoicenumber ASC) TO '${TARGET_DIR}/payment-details.csv' WITH (FORMAT csv, HEADER true)
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export payment details"
fi
}
function export_scheduler_id() {
   psql_table <<SQL
        \copy (SELECT scheduler_id FROM client_scheduler_id LIMIT 1) TO '${TARGET_DIR}/scheduler-id.value' WITH (FORMAT text, DELIMITER E'\t', HEADER false)
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export scheduler id"
fi
}

function export_proxy_details() {
   psql_table <<SQL
        \copy (SELECT * FROM client_ro_document ORDER BY invoicenumber ASC) TO '${TARGET_DIR}/proxy-text.tsv' WITH (FORMAT csv, DELIMITER E'\t', HEADER true)
SQL

if [[ ! $? = '0' ]]; then
    die "Could not export proxy"
fi
}

# if [[ "$DMS" = "CDK3PA" ]] || [[ "$DMS" = "ReynoldsRCI" ]] || [[ "$DMS" = "DealerTrack" ]] || 
#    [[ "$DMS" = "DealerBuilt" ]] || [[ "$DMS" = "AutoMate" ]] || [[ "$DMS" = "DominionVue" ]] || 
#    [[ "$DMS" = "AutoSoft" ]]; then
#     update_opcode_special
# else
    update_opcode
#fi

TSV_FILE="$TSV_DIR/uploaded/proxy-text.tsv"
if [[ "$DMS" = "CDK3PA" ]] || [[ "$DMS" = "DealerTrack" ]] || [[ "$DMS" = "AutoMate" ]] || 
[[ "$DMS" = "DealerBuilt" ]] || [[ "$DMS" = "ReynoldsRCI" ]] || [[ "$DMS" = "AutoSoft" ]]; then
    echo "TSV_DIR: $TSV_DIR"
    if [ -f "$TSV_FILE" ]; then  

        if [[ "$DMS" = "CDK3PA" ]]; then
            export_proxy_details
        else
            cp  ${TSV_FILE} ${TARGET_DIR}
        fi
        echo "$TSV_FILE exist " 
    else
        echo "$TSV_FILE does not exist"
        echo "invoicenumber, proxy" > ${TSV_FILE}
        cp ${TSV_FILE} ${TARGET_DIR}
    fi 
else
    echo "$TSV_FILE does not exist"
    echo "invoicenumber, proxy" > ${TSV_FILE}
    cp ${TSV_FILE} ${TARGET_DIR}
fi

function update_discount_description() {
    psql_table <<SQL
    with part_discounts as
    (
    select summary from client_discount_details where part_discount is not null and (labor_discount is null or labor_discount::numeric = 0) group by summary
    )
    ,labor_discounts as 
    (
    select summary from client_discount_details where labor_discount is not null and (part_discount is null or part_discount::numeric = 0) group by summary
    )
    ,both_discounts as 
    (
    select summary from client_discount_details where total_discount is not null and (part_discount is null or part_discount::numeric = 0) and (labor_discount is null or labor_discount::numeric = 0) group by summary
    )
    ,descriptions as 
    (
    select summary from part_discounts
    union all
    select summary from labor_discounts
    union all
    select summary from both_discounts
    )
    ,summary_to_count as (
    select summary, count(*) from descriptions group by summary having count(*) > 1
    )
    update client_discount_details cdd
    set summary = 
    case  
        when (part_discount is not null AND part_discount::numeric != 0) and (labor_discount is null or labor_discount::numeric = 0) then CONCAT(cdd.summary, '-Parts')
        when (labor_discount is not null AND labor_discount::numeric != 0) and (part_discount is null or part_discount::numeric = 0) then CONCAT(cdd.summary, '-Labor')
        else cdd.summary
    end
    from summary_to_count stc
    where cdd.summary = stc.summary; 
SQL
}

export_scheduler_id
state_rules_warranty_count
update_partnumber_detail
delete_invoice_sequence
export_ro_sequence_job
#delete_outofrange_ros
export_client_invoicedetails
#export_ro_sequence
export_jobdetails
export_client_labordetail
export_client_partsdetail
update_discount_description
export_discountdetails
export_accountdetails
export_makedetails
export_generaldetails
export_paytypedetails
export_paymentdetails
