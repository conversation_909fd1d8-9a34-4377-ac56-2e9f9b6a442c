### Provides functions related to capturing user input within command line applications


ESC=$'\x1b'
CYAN="$ESC[1;36m"
BEGIN_PROMPT="$CYAN"
END_PROMPT="$ESC[0m"

# Prompts (in color) for user input then stores the supplied response to the variable name supplied
# Args:
#   1 - Prompting message (double-quote)
#   2 - Target variable name [required]
#   3 - Default value [optional]
function request_input() {
    local prompt="$1"
    local targetvar="$2"
    local default="${3:-}"
 
    targetvar_value=`resolve_target_var "${targetvar}"`
    read -p "${BEGIN_PROMPT}${prompt}: ${END_PROMPT}" -e -i "${default}" $targetvar
    return 0
}

# Resolve the indirect variable reference to by the input value
# Cannot be used in a context where unset variables cause the script
# to abort.
# Args:
#   1 - The name of a variable
function resolve_target_var()
{
    set +o nounset
    eval echo "\${${1}}"
    set -o nounset
    return 0
}

function ask_number() {
    local regex="$2"
    local msg="$3"

    ANSWER=''
    while true; do
      echo -e -n "${txt_prompt}$1: ${txt_reset}" >&2
      read ANSWER

      if [[ -z "${ANSWER}" || "${ANSWER}" = "?" || "${ANSWER}" =~ ${regex} ]]; then
        return
      fi

      echo -e "${txt_warn}Invalid ${msg}${txt_reset}" >&2
    done
}

function ask_decimal() {
    local emsg=${1:-'decimal'}
    ask_number "$1" "^[0-9]+\.[0-9]+$" "$emsg"
}

function ask_integer() {
    local emsg=${1:-'integer'}
    ask_number "$1" "^[0-9]+$" "$emsg"
}

function ask_decimal_or_integer {
    local emsg=${1:-'number'}
    ask_number "$1" "^[0-9.]+$" "$emsg"
}

# Prompt using a regex for validation
# $1 - prompt
# $2 - regex
# $3 - default
# $4 - maxlen (default 1)
# return value in ANSWER variable
function ask() {
    local prompt="$1"
    local regex="${2:-''}"
    local default="${3:-''}"
    local maxlen="${4:-1}"

    ANSWER=''
    while true; do
      echo -e -n "${txt_prompt}${prompt}: ${txt_reset}"
      if [[ -z "$maxlen" ]]; then
        read ANSWER
      else
        read -n "${maxlen}" ANSWER
        if [[ "${#ANSWER}" = $maxlen ]]; then
            echo ""
        fi
      fi
      if [[ -z "$ANSWER" ]]; then
        ANSWER="${default}"
        return
      fi
      if [[ "${ANSWER}" =~ ${regex} ]]; then
        return
      fi
      echo -e "${txt_warn}Invalid option: ${ANSWER}${txt_reset}"
    done
}

# Take the supplied ScenarioKey and make it conform to a group-store-scenario path
function parse_key_to_path() {
    echo "$1" | perl -nle 'print "$1/$2/$3" if /\[([^\]]+)\]([^+]+)\+((?:L_)?\d{6}_.*)/'
}
