### Provides user-friendly names for both direct colors as well as
### semantic definitions like "error", "notice", "debug", etc...

txt_reset='\e[0m'
txt_yellow='\e[1;33m'
txt_red='\e[1;31m'
txt_green='\e[1;32m'
txt_dark_green='\e[0;32m'
txt_dark_yellow='\e[0;33m'
txt_cyan='\e[1;36m'
txt_dark_cyan='\e[0;36m'
txt_magenta='\e[1;35m'
txt_white='\e[1;37m'
txt_blue='\e[1;34m'
txt_data_table="$txt_cyan"
txt_prompt="$txt_cyan"
txt_warn=$txt_red
txt_usage=$txt_green
txt_help_title='\e[4;1;35m' # Magenta bold underlined																																				35m]'
txt_help=$txt_magenta

function pre_usage() {
    echo -e "${txt_cyan}$@"
    echo -en $txt_usage
}

function post_usage() {
    echo -en $txt_reset
}

function pre_help() {
    echo -en "${txt_help}"
}

function post_help() {
   echo -en ${txt_reset}
}

# Display help from stdin
function display_help() {
	less --tilde --raw-control-chars --clear-screen --prompt="(press h for help or q to quit help)"
}
