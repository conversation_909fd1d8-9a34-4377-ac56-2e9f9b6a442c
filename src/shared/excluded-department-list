#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

set -o nounset

CMD_SCHEMA=$1

function psql_table() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${CMD_SCHEMA}'" \
        --set=ON_ERROR_STOP=1 "$@"
    return
}

debug "Processing Department"


# Drop and create all of the necessary tables
# and associated data.
psql_table --quiet <<SQL  

    INSERT INTO excluded_department VALUES ('B');

SQL

if [[ "$?" != '0' ]]; then
    die "Insert Department Failed!"
fi

exit 0
