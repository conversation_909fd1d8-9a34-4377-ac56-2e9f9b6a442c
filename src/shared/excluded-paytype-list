#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

set -o nounset

CMD_SCHEMA=$1

function psql_table() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${CMD_SCHEMA}'" \
        --set=ON_ERROR_STOP=1 "$@"
    return
}

debug "Processing Paytype"


# Drop and create all of the necessary tables
# and associated data.
psql_table --quiet <<SQL  

    INSERT INTO excluded_paytype VALUES 
    ('W','WB', 'parts', 'All'),
    ('C','CSC','parts','All'),
    ('W','WB', 'labor', 'All'),
    ('C','CSC','labor','All');

SQL

if [[ "$?" != '0' ]]; then
    die "Insert Paytype Failed!"
fi

exit 0
