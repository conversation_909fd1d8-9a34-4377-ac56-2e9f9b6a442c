SELECT set_config('client_min_messages', 'WARNING', false);

DROP TABLE IF EXISTS data_dictionary_reference;
CREATE TABLE data_dictionary_reference (
    field_name text not null primary key,
    is_array boolean null, --can only know if has_children is true
    has_children boolean not null
);

DROP TABLE IF EXISTS data_dictionary_current;
CREATE TABLE data_dictionary_current (
    field_name text not null primary key,
    is_array boolean null,
    has_children boolean not null
);

CREATE OR REPLACE FUNCTION build_etl_view(target_table text)
RETURNS text
LANGUAGE plpgsql
STRICT
AS $func$
DECLARE select_query text; select_tlist text := '';
        from_clause text := 'build_records(%L, %L %s) AS br (%s)';
        func_args text := '';
        func_sig text := '';
        final_query text := '';
        created_view_name text; view_builder_main text; view_builder_func text; model_cols record;
        primary_field text;
        table_prefix text;
BEGIN
    table_prefix := CASE WHEN lower(target_table) = 'other' THEN 'proxy_' ELSE 'etl_' END;
    created_view_name := table_prefix || lower(target_table) || '_view';
    select_query := $qry$
        SELECT %s
          FROM %s
    $qry$;
    select_tlist = '"roNumber"';
    func_sig = '"roNumber" text';

    IF target_table = 'Job'
        THEN primary_field = 'lineCode';
    ELSIF target_table = 'Labor'
        THEN primary_field = 'sequenceNo';
    ELSIF target_table = 'Parts'
        THEN primary_field = 'sequenceNo';
    ELSIF target_table = 'Cust_Mail'
        THEN primary_field = 'EmailAddress';
    ELSIF target_table = 'Total'
        THEN primary_field = 'totPayType';
    ELSIF target_table = 'Tech'
        THEN primary_field = 'hrsSequenceNo';
    ELSIF target_table = 'Pay'
        THEN primary_field = 'payPaymentCode';
    ELSIF target_table = 'Tech_Punch'
        THEN primary_field = 'punLineCode';
    END IF;

    IF primary_field IS NOT NULL
    THEN
        select_tlist := select_tlist || format(', %I, %I', primary_field, primary_field || '_idx');
        func_sig     := func_sig     || format(', %I text, %I int', primary_field, primary_field || '_idx');
    END IF;

    FOR model_cols IN EXECUTE format('SELECT * FROM etl_model WHERE target_table = %L AND field_name <> %L
                                      AND field_name <> %L', target_table, 'roNumber', coalesce(primary_field, ''))
    LOOP
        IF model_cols.is_array THEN
            select_tlist := select_tlist || format(', %I, %I', model_cols.field_name, model_cols.field_name || '_idx');
            func_sig     := func_sig     || format(', %I text, %I int', model_cols.field_name, model_cols.field_name || '_idx');
            func_args    := func_args    || format(', %L', model_cols.field_name || '[]');
        ELSE
            select_tlist := select_tlist || format(', %I', model_cols.field_name);
            func_sig     := func_sig     || format(', %I text', model_cols.field_name);
            func_args    := func_args    || format(', %L', model_cols.field_name);
        END IF;

    END LOOP;
    EXECUTE format('DROP VIEW IF EXISTS %I;', created_view_name);
    final_query := format('CREATE VIEW %I AS %s;',
                   created_view_name,
                   format(select_query,
                          select_tlist,
                          format(from_clause,
                                 table_prefix || lower(target_table),
                                 coalesce(primary_field, 'roNumber'),
                                 func_args,
                                 func_sig
                                )
                         )
                  );
    EXECUTE final_query;
    RETURN created_view_name;
END;
$func$;

DROP FUNCTION IF EXISTS build_records(text, variadic text[]) CASCADE;
CREATE OR REPLACE FUNCTION build_records(jsonb_table_name text, primary_field text, variadic fields text[])
RETURNS SETOF record
LANGUAGE plpgsql
STRICT
AS $$
DECLARE tlist text; fld text; from_list text;
BEGIN

    tlist := format('%I', 'roNumber');
    from_list := format(E'\nFROM %I', jsonb_table_name);

    IF primary_field <> 'roNumber'
    THEN
        tlist := tlist || format(', %s.*', lower(primary_field));
        from_list := from_list || format( ', LATERAL (SELECT jsonb_array_elements(%I->%L)->>%L AS %I,
                                                                (jsonb_array_elements(%I->%L)->%L->>%L)::integer AS %I) %s',
                                          primary_field,
                                          'V',
                                          '_text',
                                          primary_field,
                                          primary_field,
                                          'V',
                                          '_attributes',
                                          'Idx',
                                          primary_field || '_id',
                                          lower(primary_field));
        fields := array_remove(fields, primary_field || '[]');
    END IF;

    FOREACH fld IN ARRAY fields
    LOOP
        IF (fld ~ '\[') AND jsonb_table_name <> 'etl_head' THEN
            tlist := tlist || format(', %s.*', substring(fld, 1, length(fld) - 2));

            from_list := from_list || format(' LEFT JOIN LATERAL (
                                                   SELECT jsonb_array_elements(%I->%L)->>%L AS %I,
                                                          (jsonb_array_elements(%I->%L)->%L->>%L)::integer AS %I) %s
                                                   ON %s.%I = %s.%I',
                                               substring(fld, 1, length(fld) - 2),
                                               'V',
                                               '_text',
                                               substring(fld, 1, length(fld) - 2),
                                               substring(fld, 1, length(fld) - 2),
                                               'V',
                                               '_attributes',
                                               'Idx',
                                               substring(fld, 1, length(fld) - 2) || '_id',
                                               substring(lower(fld), 1, length(fld) - 2),
                                               lower(primary_field),
                                               primary_field || '_id',
                                               substring(lower(fld), 1, length(fld) - 2),
                                               substring(fld, 1, length(fld) - 2) || '_id');
        ELSIF (fld ~ '\[') AND jsonb_table_name = 'etl_head' THEN
            RAISE EXCEPTION '% - Array fields are not expecting in Head table', substring(fld, 1, length(fld) - 2);
        ELSE
            tlist := tlist || format(', %I', fld);
        END IF;
    END LOOP;
    RETURN QUERY EXECUTE 'SELECT ' || tlist || from_list;

END;
$$;

CREATE OR REPLACE FUNCTION build_etl_table(target_table text)
RETURNS text
LANGUAGE plpgsql
STRICT
AS $func$
DECLARE created_table_name text; table_builder text; model_cols record; table_prefix text;
BEGIN
    table_prefix := CASE WHEN lower(target_table) = 'other' THEN 'proxy_' ELSE 'etl_' END;
    created_table_name := table_prefix || lower(target_table);
    table_builder := format('CREATE TABLE %I ( "roNumber" text NOT NULL', created_table_name);
    FOR model_cols IN EXECUTE format('SELECT * FROM du_dms_fortellis_model.etl_model WHERE target_table = %L AND field_name <> %L', target_table, 'roNumber')
    LOOP
        table_builder := table_builder || format(', %I %s', model_cols.field_name, case when model_cols.is_array then 'jsonb' else 'text' end);
    END LOOP;

    table_builder := table_builder || ' );';
    EXECUTE format('DROP TABLE IF EXISTS %I CASCADE;', created_table_name);
    EXECUTE table_builder;
    RETURN created_table_name;
END;
$func$;

CREATE OR REPLACE FUNCTION create_jq_transform_from_model(target_table text)
RETURNS text
LANGUAGE plpgsql
STRICT
AS $func$
DECLARE result_json text;
BEGIN
    SELECT format(E'{\n%s\n}', field_set) AS jqtrans
      INTO result_json
      FROM (
           SELECT string_agg(field_name, E',\n') AS field_set
             FROM du_dms_fortellis_model.etl_model
            WHERE etl_model.target_table = create_jq_transform_from_model.target_table
           ) AS consolidate;

    RETURN result_json;
END;
$func$;

CREATE OR REPLACE FUNCTION assert_not_null(val_to_test anyelement, message_if_null text)
RETURNS anyelement
LANGUAGE plpgsql
AS $func$
BEGIN
    IF val_to_test IS NULL
    THEN
        RAISE EXCEPTION '%', message_if_null;
    ELSE
        RETURN val_to_test;
    END IF;
END;
$func$;

CREATE OR REPLACE FUNCTION build_validation_view(target_table text)
RETURNS text
LANGUAGE plpgsql
STRICT
AS $func$
DECLARE select_query text;
        where_clause text := E'false\n';
        from_clause text := '%s_detail';
        final_query text := '';
        created_view_name text; view_builder_main text; view_builder_func text; model_cols record;
        primary_field text;
        table_prefix text;
BEGIN
    table_prefix := CASE WHEN lower(target_table) = 'other' THEN 'proxy_' ELSE 'etl_' END;
    created_view_name := table_prefix || lower(target_table) || '_validation';
    select_query := $qry$
        SELECT "roNumber"
          FROM %s
         WHERE %s
    $qry$;

    IF target_table = 'Job'
        THEN primary_field = 'lineCode';
    ELSIF target_table = 'Labor'
        THEN primary_field = 'sequenceNo';
    ELSIF target_table = 'Parts'
        THEN primary_field = 'sequenceNo';
    ELSIF target_table = 'Cust_Mail'
        THEN primary_field = 'EmailAddress';
    ELSIF target_table = 'Total'
        THEN primary_field = 'totPayType';
    ELSIF target_table = 'Tech'
        THEN primary_field = 'hrsSequenceNo';
    ELSIF target_table = 'Pay'
        THEN primary_field = 'payPaymentCode';
    ELSIF target_table = 'Tech_Punch'
        THEN primary_field = 'punLineCode';
    END IF;

    FOR model_cols IN EXECUTE format('SELECT * FROM etl_model WHERE target_table = %L AND field_name <> %L
                                      AND field_name <> %L', target_table, 'roNumber', coalesce(primary_field, ''))
    LOOP
        IF model_cols.is_array THEN
            where_clause := where_clause || format('OR %I <> %I', model_cols.field_name || '_idx', primary_field || '_idx');
        END IF;

    END LOOP;
    EXECUTE format('DROP VIEW IF EXISTS %I;', created_view_name);
    final_query := format('CREATE VIEW %I AS %s;',
                   created_view_name,
                   format(select_query,
                          format(from_clause, table_prefix || lower(target_table)),
                          where_clause
                         )
                  );
    EXECUTE final_query;
    RETURN created_view_name;
END;
$func$;