BEGIN;

SELECT 'Creating Schema!';

SELECT set_config('client_min_messages', 'WARNING', true);

DROP SCHEMA  IF EXISTS  du_dms_fortellis CASCADE;
CREATE SCHEMA du_dms_fortellis;

SELECT set_config('search_path', 'du_dms_fortellis', true);

\i :DU_ETL_HOME/DU-Transform/client/process/prepare-client-tables.psql
\i :DU_ETL_HOME/DU-Transform/client/process/create-client-functions.psql

\i :DU_ETL_HOME/DU-Transform/client/makes/prepare-client-tables.psql
\i :DU_ETL_HOME/DU-Transform/client/invoicesequence/prepare-client-tables.psql
\i :DU_ETL_HOME/DU-Transform/client/paytypes/prepare-client-tables.psql

\i :DU_ETL_HOME/DU-Transform/client/decisions/paytype-decision-functions.psql

CREATE TABLE source_ro_details (
    "Dealer" text,
    "RO" text NOT NULL,
    "Department" text,
    "Opened Date" text,
    "Closed Date" text,
    "Void Flag" text,
    "RO Status" text,
    "VIN" text,
    "Year" text,
    "Make" text,
    "Model" text,
    "Mileage In" text,
    "Mileage Out" text,
    "Customer Number" text,
    "Advisor Name" text,
    "Advisor Number" text,
    "RO Comments" text,
    "RO Disc Prt" text,
    "RO Disc Lbr" text,
    "RO Misc" text,
    "Job No" text,
    "Op Complaint" text,
    "Op Cause" text,
    "Op Correction" text,
    "Line Code" text,
    "Labor Type" text,
    "Op Sale Type" text,
    "Account Code" text,
    "Op Code" text,
    "Op Description" text,
    "Billable Hours" text,
    "Labor Total" text,
    "Part Line Number" text,
    "Part Number" text,
    "Part Description" text,
    "Part Quantity" text,
    "Part Cost" text,
    "Part Price" text,
    "Op Misc Desc" text,
    "Op Misc Amount" text
);


CREATE TABLE source_ro (
    "RO" text NOT NULL,
    "Dealer" text,
    "Department" text,
    "Opened Date" text,
    "Closed Date" text,
    "Void Flag" text,
    "RO Status" text,
    "VIN" text,
    "Year" text,
    "Make" text,
    "Model" text,
    "Mileage In" text,
    "Mileage Out" text,
    "Customer Number" text,
    "Advisor Name" text,
    "Advisor Number" text,
    "RO Comments" text,
    "RO Disc Prt" text,
    "RO Disc Lbr" text,
    "RO Misc" text,
    "Op Misc Desc" text,
    "Op Misc Amount" text   
);

CREATE TABLE source_ro_labor (
    "RO" text NOT NULL,
    "Job No" text,
    "Op Complaint" text,
    "Op Cause" text,
    "Op Correction" text,
    "Line Code" text,
    "Labor Type" text,
    "Op Sale Type" text,
    "Account Code" text,
    "Op Code" text,
    "Op Description" text,
    "Billable Hours" text,
    "Labor Total" text  
);

CREATE TABLE source_ro_parts (
    "RO" text NOT NULL,
    "Job No" text,
    "Part Line Number" text,
    "Part Number" text,
    "Part Description" text,
    "Part Quantity" text,
    "Part Cost" text,
    "Part Price" text
);

COMMIT;
