DROP FUNCTION IF EXISTS du_dms_fortellis_model.insert_head_data_to_model(roNumber text, ro_head json ) CASCADE;
CREATE OR REPLACE FUNCTION du_dms_fortellis_model.insert_head_data_to_model(ronumber text, ro_head json)
    RETURNS void
LANGUAGE plpgsql
AS $func$
DECLARE
    vehicle_array      json;
    mls_array          json;
    totals_array       json;
    technician_array   json;
    job_array          json;
    labor_array        json;
    parts_array        json;
    fees_array         json;
    labor_fee_array         json;
    paymentarray       json;
    discount_array     json;
    deductable_array   json;
    ro_number          text;

    head_input_type    text;
    vehicle_input_type text;
    mls_input_type     text;
    totals_input_type  text;
    technician_input_type  text;
    job_input_type     text;
    labor_input_type   text;
    parts_input_type   text;
    fees_input_type    text;
    lbr_fees_input_type    text;
    discount_input_type text;
    payment_input_type text;
    deductable_input_type text;
BEGIN

    IF ro_head ISNULL
    THEN
        RETURN;
    END IF;

    ro_number = NULLIF(regexp_replace(ronumber, '\D', '', 'g'), '');
    -- RAISE NOTICE 'ro_head value: %', ro_head;
    head_input_type = json_typeof(ro_head);
    -- RAISE NOTICE 'head_input_type: %', head_input_type;
    IF head_input_type = 'array' THEN
        FOR ro_head IN
        SELECT
        *
        FROM
        json_array_elements(ro_head)
        LOOP 
        INSERT INTO du_dms_fortellis_model.etl_head
        (
            "roNumber", "serviceAdvisor", "hostItemId", "addOnFlag", "apptDate", "apptTime", "apptFlag",
            "blockAutoMsg", "bookedDate", "bookedTime", "bookerNo", "cashier", "closedDate", "comebackFlag",
            "comments", "contactEmailAddress", "contactPhoneNumber", "customerId", "name1", "name2",
            "addressLine1", "addressLine2", "cityStateZip", "number", "address","desc","dedMultiValueCount",
            "disMultiValueCount","emailMultiValueCount","estimatedCompletionDate","estimatedCompletionTime","feeMultiValueCount","hasCustPayFlag",
            "hasIntPayFlag","hasWarrPayFlag","hrsMultiValueCount","lastServiceDate","warMultiValueCount","lbrMultiValueCount","linMultiValueCount",
            "mileage","mileageOut","mileageLastVisit","mlsMultiValueCount","openDate","openTime","origPromisedDate","origPromisedTime","isOrigWaiter",
            "payCPTotal","payMultiValueCount","totalPaymentMade","payBalanceDue","phoneMultiValueCount","postedDate","priorityValue","promisedDate",
            "promisedTime","prtMultiValueCount","punMultiValueCount","purchaseOrderNo","rapApptId","rapMultiValueCount","remarks","rentalFlag","isSoldByDealer",
            "isSpecialCustomer","statusCode","statusDesc","tagNo","totMultiValueCount","visItemMultiValueCount","voidedDate","isCustomerWaiting"
        )
        VALUES (
            ro_number,
            ro_head ->> 'serviceAdvisor',
            ro_head ->> 'hostItemId',
            ro_head ->> 'addOnFlag',
            ro_head ->> 'apptDate',
            ro_head ->> 'apptTime',
            ro_head ->> 'apptFlag',
            ro_head ->> 'blockAutoMsg',
            ro_head ->> 'bookedDate',
            ro_head ->> 'bookedTime',
            ro_head ->> 'bookerNo',
            ro_head ->> 'cashier',
            ro_head ->> 'closedDate',
            ro_head ->> 'comebackFlag',
            ro_head ->> 'comments',
            ro_head ->> 'contactEmailAddress',
            ro_head ->> 'contactPhoneNumber',
            ro_head -> 'customer' ->> 'customerId',
            ro_head -> 'customer' ->> 'name1',
            ro_head -> 'customer' ->> 'name2',
            ro_head -> 'customer' -> 'address' ->> 'addressLine1',
            ro_head -> 'customer' -> 'address' ->> 'addressLine2',
            ro_head -> 'customer' -> 'address' ->> 'cityStateZip',
            ro_head -> 'customer' -> 'phoneNumbers',
            (
                CASE 
                    WHEN json_typeof(ro_head -> 'customer' -> 'emailAddresses') = 'array' THEN (
                        -- SELECT array_to_string(array_agg(json_array_elements(ro_head -> 'customer' -> 'emailAddresses') ->> 'address'), ', ')
                        SELECT json_array_elements(ro_head -> 'customer' -> 'emailAddresses') ->> 'address' 
                        FROM json_array_elements(ro_head -> 'customer' -> 'emailAddresses') as value
                        LIMIT 1
                    )
                    WHEN json_typeof(ro_head -> 'customer' -> 'emailAddresses') = 'object' THEN (
                        ro_head -> 'customer' -> 'emailAddresses' ->> 'address'
                    )
                    ELSE NULL
                END
            ),
            (
                CASE 
                    WHEN json_typeof(ro_head -> 'customer' -> 'emailAddresses') = 'array' THEN (
                        -- SELECT array_to_string(array_agg(json_array_elements(ro_head -> 'customer' -> 'emailAddresses') ->> 'desc'), ', ')
                        SELECT json_array_elements(ro_head -> 'customer' -> 'emailAddresses') ->> 'desc' 
                        FROM json_array_elements(ro_head -> 'customer' -> 'emailAddresses') as value
                        LIMIT 1
                    )
                    WHEN json_typeof(ro_head -> 'customer' -> 'emailAddresses') = 'object' THEN (
                        ro_head -> 'customer' -> 'emailAddresses' ->> 'desc'
                    )
                    ELSE NULL
                END
            ),
            ro_head ->> 'dedMultiValueCount',
            ro_head ->> 'disMultiValueCount',
            ro_head ->> 'emailMultiValueCount',
            ro_head ->> 'estimatedCompletionDate',
            ro_head ->> 'estimatedCompletionTime',
            ro_head ->> 'feeMultiValueCount',
            ro_head ->> 'hasCustPayFlag',
            ro_head ->> 'hasIntPayFlag',
            ro_head ->> 'hasWarrPayFlag',
            ro_head ->> 'hrsMultiValueCount',
            ro_head ->> 'lastServiceDate',
            ro_head ->> 'warMultiValueCount',
            ro_head ->> 'lbrMultiValueCount',
            ro_head ->> 'linMultiValueCount',
            ro_head ->> 'mileage',
            ro_head ->> 'mileageOut',
            ro_head ->> 'mileageLastVisit',
            ro_head ->> 'mlsMultiValueCount',
            ro_head ->> 'openDate',
            ro_head ->> 'openTime',
            ro_head ->> 'origPromisedDate',
            ro_head ->> 'origPromisedTime',
            ro_head ->> 'isOrigWaiter',
            ro_head ->> 'payCPTotal',
            ro_head ->> 'payMultiValueCount',
            ro_head ->> 'totalPaymentMade',
            ro_head ->> 'payBalanceDue',
            ro_head ->> 'phoneMultiValueCount',
            ro_head ->> 'postedDate',
            ro_head ->> 'priorityValue',
            ro_head ->> 'promisedDate',
            ro_head ->> 'promisedTime',
            ro_head ->> 'prtMultiValueCount',
            ro_head ->> 'punMultiValueCount',
            ro_head ->> 'purchaseOrderNo',
            ro_head ->> 'rapApptId',
            ro_head ->> 'rapMultiValueCount',
            ro_head ->> 'remarks',
            ro_head ->> 'rentalFlag',
            ro_head ->> 'isSoldByDealer',
            ro_head ->> 'isSpecialCustomer',
            ro_head ->> 'statusCode',
            ro_head ->> 'statusDesc',
            ro_head ->> 'tagNo',
            ro_head ->> 'totMultiValueCount',
            ro_head ->> 'visItemMultiValueCount',
            ro_head ->> 'voidedDate',
            ro_head ->> 'isCustomerWaiting'
        );

        END LOOP;
    ELSIF head_input_type = 'object' THEN
        INSERT INTO du_dms_fortellis_model.etl_head
        (
            "roNumber", "serviceAdvisor", "hostItemId", "addOnFlag", "apptDate", "apptTime", "apptFlag",
            "blockAutoMsg", "bookedDate", "bookedTime", "bookerNo", "cashier", "closedDate", "comebackFlag",
            "comments", "contactEmailAddress", "contactPhoneNumber", "customerId", "name1", "name2",
            "addressLine1", "addressLine2", "cityStateZip", "number","address","desc","dedMultiValueCount",
            "disMultiValueCount","emailMultiValueCount","estimatedCompletionDate","estimatedCompletionTime","feeMultiValueCount","hasCustPayFlag",
            "hasIntPayFlag","hasWarrPayFlag","hrsMultiValueCount","lastServiceDate","warMultiValueCount","lbrMultiValueCount","linMultiValueCount",
            "mileage","mileageOut","mileageLastVisit","mlsMultiValueCount","openDate","openTime","origPromisedDate","origPromisedTime","isOrigWaiter",
            "payCPTotal","payMultiValueCount","totalPaymentMade","payBalanceDue","phoneMultiValueCount","postedDate","priorityValue","promisedDate",
            "promisedTime","prtMultiValueCount","punMultiValueCount","purchaseOrderNo","rapApptId","rapMultiValueCount","remarks","rentalFlag","isSoldByDealer",
            "isSpecialCustomer","statusCode","statusDesc","tagNo","totMultiValueCount","visItemMultiValueCount","voidedDate","isCustomerWaiting"
        )
        VALUES (
            ro_number,
            ro_head ->> 'serviceAdvisor',
            ro_head ->> 'hostItemId',
            ro_head ->> 'addOnFlag',
            ro_head ->> 'apptDate',
            ro_head ->> 'apptTime',
            ro_head ->> 'apptFlag',
            ro_head ->> 'blockAutoMsg',
            ro_head ->> 'bookedDate',
            ro_head ->> 'bookedTime',
            ro_head ->> 'bookerNo',
            ro_head ->> 'cashier',
            ro_head ->> 'closedDate',
            ro_head ->> 'comebackFlag',
            ro_head ->> 'comments',
            ro_head ->> 'contactEmailAddress',
            ro_head ->> 'contactPhoneNumber',
            ro_head -> 'customer' ->> 'customerId',
            ro_head -> 'customer' ->> 'name1',
            ro_head -> 'customer' ->> 'name2',
            ro_head -> 'customer' -> 'address' ->> 'addressLine1',
            ro_head -> 'customer' -> 'address' ->> 'addressLine2',
            ro_head -> 'customer' -> 'address' ->> 'cityStateZip',
            ro_head -> 'customer' -> 'phoneNumbers',
            (
                CASE 
                    WHEN json_typeof(ro_head -> 'customer' -> 'emailAddresses') = 'array' THEN (
                        -- SELECT array_to_string(array_agg(json_array_elements(ro_head -> 'customer' -> 'emailAddresses') ->> 'address'), ', ')
                        SELECT json_array_elements(ro_head -> 'customer' -> 'emailAddresses') ->> 'address' 
                        FROM json_array_elements(ro_head -> 'customer' -> 'emailAddresses') as value
                        LIMIT 1
                    )
                    WHEN json_typeof(ro_head -> 'customer' -> 'emailAddresses') = 'object' THEN (
                        ro_head -> 'customer' -> 'emailAddresses' ->> 'address'
                    )
                    ELSE NULL
                END
            ),
            (
                CASE 
                    WHEN json_typeof(ro_head -> 'customer' -> 'emailAddresses') = 'array' THEN (
                        -- SELECT array_to_string(array_agg(json_array_elements(ro_head -> 'customer' -> 'emailAddresses') ->> 'desc'), ', ')
                        SELECT json_array_elements(ro_head -> 'customer' -> 'emailAddresses') ->> 'desc' 
                        FROM json_array_elements(ro_head -> 'customer' -> 'emailAddresses') as value
                        LIMIT 1
                    )
                    WHEN json_typeof(ro_head -> 'customer' -> 'emailAddresses') = 'object' THEN (
                        ro_head -> 'customer' -> 'emailAddresses' ->> 'desc'
                    )
                    ELSE NULL
                END
            ),
            ro_head ->> 'dedMultiValueCount',
            ro_head ->> 'disMultiValueCount',
            ro_head ->> 'emailMultiValueCount',
            ro_head ->> 'estimatedCompletionDate',
            ro_head ->> 'estimatedCompletionTime',
            ro_head ->> 'feeMultiValueCount',
            ro_head ->> 'hasCustPayFlag',
            ro_head ->> 'hasIntPayFlag',
            ro_head ->> 'hasWarrPayFlag',
            ro_head ->> 'hrsMultiValueCount',
            ro_head ->> 'lastServiceDate',
            ro_head ->> 'warMultiValueCount',
            ro_head ->> 'lbrMultiValueCount',
            ro_head ->> 'linMultiValueCount',
            ro_head ->> 'mileage',
            ro_head ->> 'mileageOut',
            ro_head ->> 'mileageLastVisit',
            ro_head ->> 'mlsMultiValueCount',
            ro_head ->> 'openDate',
            ro_head ->> 'openTime',
            ro_head ->> 'origPromisedDate',
            ro_head ->> 'origPromisedTime',
            ro_head ->> 'isOrigWaiter',
            ro_head ->> 'payCPTotal',
            ro_head ->> 'payMultiValueCount',
            ro_head ->> 'totalPaymentMade',
            ro_head ->> 'payBalanceDue',
            ro_head ->> 'phoneMultiValueCount',
            ro_head ->> 'postedDate',
            ro_head ->> 'priorityValue',
            ro_head ->> 'promisedDate',
            ro_head ->> 'promisedTime',
            ro_head ->> 'prtMultiValueCount',
            ro_head ->> 'punMultiValueCount',
            ro_head ->> 'purchaseOrderNo',
            ro_head ->> 'rapApptId',
            ro_head ->> 'rapMultiValueCount',
            ro_head ->> 'remarks',
            ro_head ->> 'rentalFlag',
            ro_head ->> 'isSoldByDealer',
            ro_head ->> 'isSpecialCustomer',
            ro_head ->> 'statusCode',
            ro_head ->> 'statusDesc',
            ro_head ->> 'tagNo',
            ro_head ->> 'totMultiValueCount',
            ro_head ->> 'visItemMultiValueCount',
            ro_head ->> 'voidedDate',
            ro_head ->> 'isCustomerWaiting'
        );
    END IF;
    
    vehicle_array = ro_head -> 'vehicle';
    vehicle_input_type = json_typeof(vehicle_array);
    IF vehicle_input_type = 'array' THEN
        FOR vehicle_array IN
        SELECT
        *
        FROM
        json_array_elements(vehicle_array)
        LOOP 
            INSERT INTO du_dms_fortellis_model.etl_vehicle
            ("roNumber","vin","deliveryDate","make","makeDesc","model","modelDesc","year","licenseNumber","vehicleColor","vehId","lotLocation")
            VALUES (ro_number,
                vehicle_array ->> 'vin',
                vehicle_array ->> 'deliveryDate',
                vehicle_array ->> 'make',
                vehicle_array ->> 'makeDesc',
                vehicle_array ->> 'model',
                vehicle_array ->> 'modelDesc',
                vehicle_array ->> 'year',
                vehicle_array ->> 'licenseNumber',
                vehicle_array ->> 'vehicleColor',
                vehicle_array ->> 'vehId',
                vehicle_array ->> 'lotLocation'
            );
        END LOOP;
    ELSIF vehicle_input_type = 'object' THEN
        INSERT INTO du_dms_fortellis_model.etl_vehicle
        ("roNumber","vin","deliveryDate","make","makeDesc","model","modelDesc","year","licenseNumber","vehicleColor","vehId","lotLocation")
        VALUES (ro_number,
            vehicle_array ->> 'vin',
            vehicle_array ->> 'deliveryDate',
            vehicle_array ->> 'make',
            vehicle_array ->> 'makeDesc',
            vehicle_array ->> 'model',
            vehicle_array ->> 'modelDesc',
            vehicle_array ->> 'year',
            vehicle_array ->> 'licenseNumber',
            vehicle_array ->> 'vehicleColor',
            vehicle_array ->> 'vehId',
            vehicle_array ->> 'lotLocation'
    );
    END IF;
    -- RETURN;

    paymentarray = ro_head -> 'payments';
    payment_input_type = json_typeof(paymentarray);
    IF payment_input_type = 'array' THEN
        FOR paymentarray IN
        SELECT
        *
        FROM
        json_array_elements(paymentarray)
        LOOP 
            INSERT INTO du_dms_fortellis_model.etl_pay
            ("roNumber","insuranceFlag","paymentAmount","code")
            VALUES (ro_number,
                paymentarray ->> 'insuranceFlag',
                paymentarray ->> 'paymentAmount',
                paymentarray ->> 'code'
            );
        END LOOP;
    ELSIF payment_input_type = 'object' THEN
        INSERT INTO du_dms_fortellis_model.etl_pay
        ("roNumber","insuranceFlag","paymentAmount","code")
        VALUES (ro_number,
            paymentarray ->> 'insuranceFlag',
            paymentarray ->> 'paymentAmount',
            paymentarray ->> 'code'
    );
    END IF;

    mls_array = ro_head -> 'mls';
    mls_input_type = json_typeof(mls_array);
    IF mls_input_type = 'array' THEN
        FOR mls_array IN
        SELECT
        *
        FROM
        json_array_elements(mls_array)
        LOOP 
            INSERT INTO du_dms_fortellis_model.etl_mls
            ("roNumber","sequenceNo","lineCode","type","opCode","opCodeDesc","laborType","mcdPercentage","sale","gogPrice","miscPrice","subletPrice","cost","gogCost","subletCost","miscCost")
            VALUES (ro_number,
                mls_array ->> 'sequenceNo',
                mls_array ->> 'lineCode',
                mls_array ->> 'type',
                mls_array ->> 'opCode',
                mls_array ->> 'opCodeDesc',
                mls_array ->> 'laborType',
                mls_array ->> 'mcdPercentage',
                mls_array ->> 'sale',
                mls_array ->> 'gogPrice',
                mls_array ->> 'miscPrice',
                mls_array ->> 'subletPrice',
                mls_array ->> 'cost',
                mls_array ->> 'gogCost',
                mls_array ->> 'subletCost',
                mls_array ->> 'miscCost'
            );
        END LOOP;
    ELSIF mls_input_type = 'object' THEN
        INSERT INTO du_dms_fortellis_model.etl_mls
        ("roNumber","sequenceNo","lineCode","type","opCode","opCodeDesc","laborType","mcdPercentage","sale","gogPrice","miscPrice","subletPrice","cost","gogCost","subletCost","miscCost")
        VALUES (ro_number,
                mls_array ->> 'sequenceNo',
                mls_array ->> 'lineCode',
                mls_array ->> 'type',
                mls_array ->> 'opCode',
                mls_array ->> 'opCodeDesc',
                mls_array ->> 'laborType',
                mls_array ->> 'mcdPercentage',
                mls_array ->> 'sale',
                mls_array ->> 'gogPrice',
                mls_array ->> 'miscPrice',
                mls_array ->> 'subletPrice',
                mls_array ->> 'cost',
                mls_array ->> 'gogCost',
                mls_array ->> 'subletCost',
                mls_array ->> 'miscCost'
    );
    END IF;

    deductable_array = ro_head -> 'deductables';
    deductable_input_type = json_typeof(deductable_array);
    IF deductable_input_type = 'array' THEN
        FOR deductable_array IN
        SELECT
        *
        FROM
        json_array_elements(deductable_array)
        LOOP 
            INSERT INTO du_dms_fortellis_model.etl_deductable
            ("roNumber","sequenceNo","lineCodes","laborType","maximumAmount","actualAmount","laborAmount","partsAmount")
            VALUES (ro_number,
                deductable_array ->> 'sequenceNo',
                deductable_array ->> 'lineCodes',
                deductable_array ->> 'laborType',
                deductable_array ->> 'maximumAmount',
                deductable_array ->> 'actualAmount',
                deductable_array ->> 'laborAmount',
                deductable_array ->> 'partsAmount'
            );
        END LOOP;
    ELSIF deductable_input_type = 'object' THEN
        INSERT INTO du_dms_fortellis_model.etl_deductable
        ("roNumber","sequenceNo","lineCodes","laborType","maximumAmount","actualAmount","laborAmount","partsAmount")
        VALUES (ro_number,
                deductable_array ->> 'sequenceNo',
                deductable_array ->> 'lineCodes',
                deductable_array ->> 'laborType',
                deductable_array ->> 'maximumAmount',
                deductable_array ->> 'actualAmount',
                deductable_array ->> 'laborAmount',
                deductable_array ->> 'partsAmount'
    );
    END IF;

    discount_array = ro_head -> 'discounts';
    discount_input_type = json_typeof(discount_array);
    IF discount_input_type = 'array' THEN
        FOR discount_array IN
        SELECT
        *
        FROM
        json_array_elements(discount_array)
        LOOP 
            INSERT INTO du_dms_fortellis_model.etl_discount
            ("roNumber","id","appliedBy","classOrType","debitAccountNo","debitControlNo","debitTargetCo","desc","laborDiscount","level","lineCode","lopSeqNo","managerOverride","originalDiscount","overrideAmount","overrideGPAmount","overrideGPPercent","overridePercent","overrideTarget","partsDiscount","sequenceNo","totalDiscount","userId")
            VALUES (ro_number,
                discount_array ->> 'id',
                discount_array ->> 'appliedBy',
                discount_array ->> 'classOrType',
                discount_array ->> 'debitAccountNo',
                discount_array ->> 'debitControlNo',
                discount_array ->> 'debitTargetCo',
                discount_array ->> 'desc',
                discount_array ->> 'laborDiscount',
                discount_array ->> 'level',
                discount_array ->> 'lineCode',
                discount_array ->> 'lopSeqNo',
                discount_array ->> 'managerOverride',
                discount_array ->> 'originalDiscount',
                discount_array ->> 'overrideAmount',
                discount_array ->> 'overrideGPAmount',
                discount_array ->> 'overrideGPPercent',
                discount_array ->> 'overridePercent',
                discount_array ->> 'overrideTarget',
                discount_array ->> 'partsDiscount',
                discount_array ->> 'sequenceNo',
                discount_array ->> 'totalDiscount',
                discount_array ->> 'userId'
            );
        END LOOP;
    ELSIF discount_input_type = 'object' THEN
        INSERT INTO du_dms_fortellis_model.etl_discount
        ("roNumber","id","appliedBy","classOrType","debitAccountNo","debitControlNo","debitTargetCo","desc","laborDiscount","level","lineCode","lopSeqNo","managerOverride","originalDiscount","overrideAmount","overrideGPAmount","overrideGPPercent","overridePercent","overrideTarget","partsDiscount","sequenceNo","totalDiscount","userId")
        VALUES (ro_number,
                discount_array ->> 'id',
                discount_array ->> 'appliedBy',
                discount_array ->> 'classOrType',
                discount_array ->> 'debitAccountNo',
                discount_array ->> 'debitControlNo',
                discount_array ->> 'debitTargetCo',
                discount_array ->> 'desc',
                discount_array ->> 'laborDiscount',
                discount_array ->> 'level',
                discount_array ->> 'lineCode',
                discount_array ->> 'lopSeqNo',
                discount_array ->> 'managerOverride',
                discount_array ->> 'originalDiscount',
                discount_array ->> 'overrideAmount',
                discount_array ->> 'overrideGPAmount',
                discount_array ->> 'overrideGPPercent',
                discount_array ->> 'overridePercent',
                discount_array ->> 'overrideTarget',
                discount_array ->> 'partsDiscount',
                discount_array ->> 'sequenceNo',
                discount_array ->> 'totalDiscount',
                discount_array ->> 'userId'
    );
    END IF;


    technician_array = ro_head -> 'technicianPunchTimes';
    technician_input_type = json_typeof(technician_array);
    IF technician_input_type = 'array' THEN
        FOR technician_array IN
        SELECT
        *
        FROM
        json_array_elements(technician_array)
        LOOP 
            INSERT INTO du_dms_fortellis_model.etl_tech_punch
            ("roNumber","technicianId","workDate","timeOn","timeOff","duration","lineCode","workType","alteredFlag")
            VALUES (ro_number,
                technician_array ->>  'technicianId',
                technician_array ->>  'workDate',
                technician_array ->>  'timeOn',
                technician_array ->>  'timeOff',
                technician_array ->>  'duration',
                technician_array ->>  'lineCode',
                technician_array ->>  'workType',
                technician_array ->>  'alteredFlag'
            );
        END LOOP;
    ELSIF technician_input_type = 'object' THEN
        INSERT INTO du_dms_fortellis_model.etl_tech_punch
        ("roNumber","technicianId","workDate","timeOn","timeOff","duration","lineCode","workType","alteredFlag")
        VALUES (ro_number,
                technician_array ->>  'technicianId',
                technician_array ->>  'workDate',
                technician_array ->>  'timeOn',
                technician_array ->>  'timeOff',
                technician_array ->>  'duration',
                technician_array ->>  'lineCode',
                technician_array ->>  'workType',
                technician_array ->>  'alteredFlag'
    );
    END IF;


    totals_array = ro_head -> 'totals';
    totals_input_type = json_typeof(totals_array);
    IF totals_input_type = 'array' THEN
        FOR totals_array IN
        SELECT
        *
        FROM
        json_array_elements(totals_array)
        LOOP 
            INSERT INTO du_dms_fortellis_model.etl_total
            ("roNumber","payType","roSale","roCost","laborSale","laborCost","laborCount","laborDiscount","laborSalePostDed","partsSale","partsCost","partsCount","partsDiscount","partsSalePostDed","miscSale","miscCost","miscCount","lubeSale","lubeCost","lubeCount","subletSale","subletCost","subletCount","shopChargeSale","shopChargeCost","roTax","stateTax","localTax","supp2Tax","supp3Tax","supp4Tax","actualHours","soldHours","otherHours","timeCardHours","coreCost","coreSale","discount","flagHours","forcedShopCharge","roSalePostDed")
            VALUES (ro_number,
                totals_array ->>  'payType',
                totals_array ->>  'roSale',
                totals_array ->>  'roCost',
                totals_array ->>  'laborSale',
                totals_array ->>  'laborCost',
                totals_array ->>  'laborCount',
                totals_array ->>  'laborDiscount',
                totals_array ->>  'laborSalePostDed',
                totals_array ->>  'partsSale',
                totals_array ->>  'partsCost',
                totals_array ->>  'partsCount',
                totals_array ->>  'partsDiscount',
                totals_array ->>  'partsSalePostDed',
                totals_array ->>  'miscSale',
                totals_array ->>  'miscCost',
                totals_array ->>  'miscCount',
                totals_array ->>  'lubeSale',
                totals_array ->>  'lubeCost',
                totals_array ->>  'lubeCount',
                totals_array ->>  'subletSale',
                totals_array ->>  'subletCost',
                totals_array ->>  'subletCount',
                totals_array ->>  'shopChargeSale',
                totals_array ->>  'shopChargeCost',
                totals_array ->>  'roTax',
                totals_array ->>  'stateTax',
                totals_array ->>  'localTax',
                totals_array ->>  'supp2Tax',
                totals_array ->>  'supp3Tax',
                totals_array ->>  'supp4Tax',
                totals_array ->>  'actualHours',
                totals_array ->>  'soldHours',
                totals_array ->>  'otherHours',
                totals_array ->>  'timeCardHours',
                totals_array ->>  'coreCost',
                totals_array ->>  'coreSale',
                totals_array ->>  'discount',
                totals_array ->>  'flagHours',
                totals_array ->>  'forcedShopCharge',
                totals_array ->>  'roSalePostDed'
            );
        END LOOP;
    ELSIF totals_input_type = 'object' THEN
        INSERT INTO du_dms_fortellis_model.etl_total
        ("roNumber","payType","roSale","roCost","laborSale","laborCost","laborCount","laborDiscount","laborSalePostDed","partsSale","partsCost","partsCount","partsDiscount","partsSalePostDed","miscSale","miscCost","miscCount","lubeSale","lubeCost","lubeCount","subletSale","subletCost","subletCount","shopChargeSale","shopChargeCost","roTax","stateTax","localTax","supp2Tax","supp3Tax","supp4Tax","actualHours","soldHours","otherHours","timeCardHours","coreCost","coreSale","discount","flagHours","forcedShopCharge","roSalePostDed")
        VALUES (ro_number,
                totals_array ->>  'payType',
                totals_array ->>  'roSale',
                totals_array ->>  'roCost',
                totals_array ->>  'laborSale',
                totals_array ->>  'laborCost',
                totals_array ->>  'laborCount',
                totals_array ->>  'laborDiscount',
                totals_array ->>  'laborSalePostDed',
                totals_array ->>  'partsSale',
                totals_array ->>  'partsCost',
                totals_array ->>  'partsCount',
                totals_array ->>  'partsDiscount',
                totals_array ->>  'partsSalePostDed',
                totals_array ->>  'miscSale',
                totals_array ->>  'miscCost',
                totals_array ->>  'miscCount',
                totals_array ->>  'lubeSale',
                totals_array ->>  'lubeCost',
                totals_array ->>  'lubeCount',
                totals_array ->>  'subletSale',
                totals_array ->>  'subletCost',
                totals_array ->>  'subletCount',
                totals_array ->>  'shopChargeSale',
                totals_array ->>  'shopChargeCost',
                totals_array ->>  'roTax',
                totals_array ->>  'stateTax',
                totals_array ->>  'localTax',
                totals_array ->>  'supp2Tax',
                totals_array ->>  'supp3Tax',
                totals_array ->>  'supp4Tax',
                totals_array ->>  'actualHours',
                totals_array ->>  'soldHours',
                totals_array ->>  'otherHours',
                totals_array ->>  'timeCardHours',
                totals_array ->>  'coreCost',
                totals_array ->>  'coreSale',
                totals_array ->>  'discount',
                totals_array ->>  'flagHours',
                totals_array ->>  'forcedShopCharge',
                totals_array ->>  'roSalePostDed'
    );
    END IF;

    job_array = ro_head -> 'operations';
    job_input_type = json_typeof(job_array);
    IF job_input_type = 'array' THEN
        FOR job_array IN
        SELECT
        *
        FROM
        json_array_elements(job_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_job
    ("roNumber","lineCode","complaintCode","serviceRequest","campaignCode","addOnFlag","statusCode",
    "statusDesc","comebackFlag","dispatchCode","estimatedDuration","cause","storySequenceNo","storyEmployeeNo",
    "storyText","bookerNo","actualWork")
    VALUES (ro_number,
        job_array -> 'line' ->>'lineCode',
        job_array -> 'line' ->>'complaintCode',
        job_array -> 'line' ->>'serviceRequest',
        job_array -> 'line' ->>'campaignCode',
        job_array -> 'line' ->>'addOnFlag',
        job_array -> 'line' ->>'statusCode',
        job_array -> 'line' ->>'statusDesc',
        job_array -> 'line' ->>'comebackFlag',
        job_array -> 'line' ->>'dispatchCode',
        job_array -> 'line' ->>'estimatedDuration',
        job_array -> 'line' ->>'cause',
        job_array -> 'line' ->>'storySequenceNo',
        job_array -> 'line' ->>'storyEmployeeNo',
        job_array -> 'line' ->>'storyText',
        job_array -> 'line' ->>'bookerNo',
        job_array -> 'line' ->>'actualWork'
    );
    labor_array = job_array -> 'line' -> 'laborOperations';
    labor_input_type = json_typeof(labor_array);
    IF labor_input_type = 'array' THEN
        FOR labor_array IN
        SELECT
        *
        FROM
        json_array_elements(labor_array::json)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_labor
    ("roNumber","sequenceNo","lineCode","opCode","opCodeDesc","mcdPercentage","sale","cost","technicianIds",
    "actualHours","soldHours","otherHours","timeCardHours","comebackFlag","comebackRO","comebackSA",
    "comebackTech","type","forcedShopCharge","bookedDate","bookedTime","flagHours")
    VALUES (ro_number,
        labor_array ->> 'sequenceNo',
        labor_array ->> 'lineCode',
        labor_array ->> 'opCode',
        labor_array ->> 'opCodeDesc',
        labor_array ->> 'mcdPercentage',
        labor_array ->> 'sale',
        labor_array ->> 'cost',
        labor_array ->> 'technicianIds',
        labor_array ->> 'actualHours',
        labor_array ->> 'soldHours',
        labor_array ->> 'otherHours',
        labor_array ->> 'timeCardHours',
        labor_array ->> 'comebackFlag',
        labor_array ->> 'comebackRO',
        labor_array ->> 'comebackSA',
        labor_array ->> 'comebackTech',
        labor_array ->> 'type',
        labor_array ->> 'forcedShopCharge',
        labor_array ->> 'bookedDate',
        labor_array ->> 'bookedTime',
        labor_array ->> 'flagHours'
    );

    labor_fee_array = labor_array -> 'fees';    
    fees_input_type = json_typeof(labor_fee_array);
    IF fees_input_type = 'array' THEN
        FOR labor_fee_array IN
        SELECT
        *
        FROM
        json_array_elements(labor_fee_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    labor_fee_array ->> 'id',
    labor_fee_array ->> 'cost',
    labor_fee_array ->> 'laborType',
    labor_fee_array ->> 'lineCode',
    labor_fee_array ->> 'lopOrPartFlag',
    labor_fee_array ->> 'mcdPercentage',
    labor_fee_array ->> 'opCode',
    labor_fee_array ->> 'opCodeDesc',
    labor_fee_array ->> 'sale',
    labor_fee_array ->> 'sequenceNo',
    labor_fee_array ->> 'type',
    labor_fee_array ->> 'lopOrPartSeqNo'
    );
    END LOOP;
    ELSIF lbr_fees_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    labor_fee_array ->> 'id',
    labor_fee_array ->> 'cost',
    labor_fee_array ->> 'laborType',
    labor_fee_array ->> 'lineCode',
    labor_fee_array ->> 'lopOrPartFlag',
    labor_fee_array ->> 'mcdPercentage',
    labor_fee_array ->> 'opCode',
    labor_fee_array ->> 'opCodeDesc',
    labor_fee_array ->> 'sale',
    labor_fee_array ->> 'sequenceNo',
    labor_fee_array ->> 'type',
    labor_fee_array ->> 'lopOrPartSeqNo'
    );
    END IF;
    --END LOOP;

    parts_array = labor_array -> 'parts';

    parts_input_type = json_typeof(parts_array);

    IF parts_input_type = 'array' THEN
        FOR parts_array IN
        SELECT
        *
        FROM
        json_array_elements(parts_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_parts
    ("roNumber","sequenceNo","lineCode","laborSequenceNo","desc","source","bin1","employeeId","outsideSalesmanId","specialStatus","compLineCode","qtyOrdered","qtySold",
    "qtyOnHand","qtyFilled","qtyBackOrdered","laborType","mcdPercentage","extendedSale","extendedCost","sale","cost","list","comp",
    "coreSale","coreCost","number","partClass")
    VALUES (ro_number,
        parts_array ->> 'sequenceNo',
        parts_array ->> 'lineCode',
        parts_array ->> 'laborSequenceNo',
        parts_array ->> 'desc',
        parts_array ->> 'source',
        parts_array ->> 'bin1',
        parts_array ->> 'employeeId',
        parts_array ->> 'outsideSalesmanId',
        parts_array ->> 'specialStatus',
        parts_array ->> 'compLineCode',
        parts_array ->> 'qtyOrdered',
        parts_array ->> 'qtySold',
        parts_array ->> 'qtyOnHand',
        parts_array ->> 'qtyFilled',
        parts_array ->> 'qtyBackOrdered',
        parts_array ->> 'laborType',
        parts_array ->> 'mcdPercentage',
        parts_array ->> 'extendedSale',
        parts_array ->> 'extendedCost',
        parts_array ->> 'sale',
        parts_array ->> 'cost',
        parts_array ->> 'list',
        parts_array ->> 'comp',
        parts_array ->> 'coreSale',
        parts_array ->> 'coreCost',
        parts_array ->> 'number',
        parts_array ->> 'partClass'
    );
    fees_array = parts_array -> 'fees';
    fees_input_type = json_typeof(fees_array);
    IF fees_input_type = 'array' THEN
        FOR fees_array IN
        SELECT
        *
        FROM
        json_array_elements(fees_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END LOOP;
    ELSIF fees_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END IF;
    END LOOP;
    ELSIF parts_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_parts
    ("roNumber","sequenceNo","lineCode","laborSequenceNo","desc","source","bin1","employeeId","outsideSalesmanId","specialStatus","compLineCode","qtyOrdered","qtySold",
    "qtyOnHand","qtyFilled","qtyBackOrdered","laborType","mcdPercentage","extendedSale","extendedCost","sale","cost","list","comp",
    "coreSale","coreCost","number","partClass")
    VALUES (ro_number,
        parts_array ->> 'sequenceNo',
        parts_array ->> 'lineCode',
        parts_array ->> 'laborSequenceNo',
        parts_array ->> 'desc',
        parts_array ->> 'source',
        parts_array ->> 'bin1',
        parts_array ->> 'employeeId',
        parts_array ->> 'outsideSalesmanId',
        parts_array ->> 'specialStatus',
        parts_array ->> 'compLineCode',
        parts_array ->> 'qtyOrdered',
        parts_array ->> 'qtySold',
        parts_array ->> 'qtyOnHand',
        parts_array ->> 'qtyFilled',
        parts_array ->> 'qtyBackOrdered',
        parts_array ->> 'laborType',
        parts_array ->> 'mcdPercentage',
        parts_array ->> 'extendedSale',
        parts_array ->> 'extendedCost',
        parts_array ->> 'sale',
        parts_array ->> 'cost',
        parts_array ->> 'list',
        parts_array ->> 'comp',
        parts_array ->> 'coreSale',
        parts_array ->> 'coreCost',
        parts_array ->> 'number',
        parts_array ->> 'partClass'
    );
    fees_array = parts_array -> 'fees';
    fees_input_type = json_typeof(fees_array);
    IF fees_input_type = 'array' THEN
        FOR fees_array IN
        SELECT
        *
        FROM
        json_array_elements(fees_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END LOOP;
    ELSIF fees_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END IF;
    END IF;
    END LOOP;
    ELSIF labor_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_labor
    ("roNumber","sequenceNo","lineCode","opCode","opCodeDesc","mcdPercentage","sale","cost","technicianIds",
    "actualHours","soldHours","otherHours","timeCardHours","comebackFlag","comebackRO","comebackSA",
    "comebackTech","type","forcedShopCharge","bookedDate","bookedTime","flagHours")
    VALUES (ro_number,
        labor_array ->> 'sequenceNo',
        labor_array ->> 'lineCode',
        labor_array ->> 'opCode',
        labor_array ->> 'opCodeDesc',
        labor_array ->> 'mcdPercentage',
        labor_array ->> 'sale',
        labor_array ->> 'cost',
        labor_array ->> 'technicianIds',
        labor_array ->> 'actualHours',
        labor_array ->> 'soldHours',
        labor_array ->> 'otherHours',
        labor_array ->> 'timeCardHours',
        labor_array ->> 'comebackFlag',
        labor_array ->> 'comebackRO',
        labor_array ->> 'comebackSA',
        labor_array ->> 'comebackTech',
        labor_array ->> 'type',
        labor_array ->> 'forcedShopCharge',
        labor_array ->> 'bookedDate',
        labor_array ->> 'bookedTime',
        labor_array ->> 'flagHours'
    );
    parts_array = labor_array -> 'parts';

    parts_input_type = json_typeof(parts_array);

    IF parts_input_type = 'array' THEN
        FOR parts_array IN
        SELECT
        *
        FROM
        json_array_elements(parts_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_parts
    ("roNumber","sequenceNo","lineCode","laborSequenceNo","desc","source","bin1","employeeId","outsideSalesmanId","specialStatus","compLineCode","qtyOrdered","qtySold",
    "qtyOnHand","qtyFilled","qtyBackOrdered","laborType","mcdPercentage","extendedSale","extendedCost","sale","cost","list","comp",
    "coreSale","coreCost","number","partClass")
    VALUES (ro_number,
        parts_array ->> 'sequenceNo',
        parts_array ->> 'lineCode',
        parts_array ->> 'laborSequenceNo',
        parts_array ->> 'desc',
        parts_array ->> 'source',
        parts_array ->> 'bin1',
        parts_array ->> 'employeeId',
        parts_array ->> 'outsideSalesmanId',
        parts_array ->> 'specialStatus',
        parts_array ->> 'compLineCode',
        parts_array ->> 'qtyOrdered',
        parts_array ->> 'qtySold',
        parts_array ->> 'qtyOnHand',
        parts_array ->> 'qtyFilled',
        parts_array ->> 'qtyBackOrdered',
        parts_array ->> 'laborType',
        parts_array ->> 'mcdPercentage',
        parts_array ->> 'extendedSale',
        parts_array ->> 'extendedCost',
        parts_array ->> 'sale',
        parts_array ->> 'cost',
        parts_array ->> 'list',
        parts_array ->> 'comp',
        parts_array ->> 'coreSale',
        parts_array ->> 'coreCost',
        parts_array ->> 'number',
        parts_array ->> 'partClass'
    );
    fees_array = parts_array -> 'fees';
    fees_input_type = json_typeof(fees_array);
    IF fees_input_type = 'array' THEN
        FOR fees_array IN
        SELECT
        *
        FROM
        json_array_elements(fees_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END LOOP;
    ELSIF fees_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END IF;
    END LOOP;
    ELSIF parts_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_parts
    ("roNumber","sequenceNo","lineCode","laborSequenceNo","desc","source","bin1","employeeId","outsideSalesmanId","specialStatus","compLineCode","qtyOrdered","qtySold",
    "qtyOnHand","qtyFilled","qtyBackOrdered","laborType","mcdPercentage","extendedSale","extendedCost","sale","cost","list","comp",
    "coreSale","coreCost","number","partClass")
    VALUES (ro_number,
        parts_array ->> 'sequenceNo',
        parts_array ->> 'lineCode',
        parts_array ->> 'laborSequenceNo',
        parts_array ->> 'desc',
        parts_array ->> 'source',
        parts_array ->> 'bin1',
        parts_array ->> 'employeeId',
        parts_array ->> 'outsideSalesmanId',
        parts_array ->> 'specialStatus',
        parts_array ->> 'compLineCode',
        parts_array ->> 'qtyOrdered',
        parts_array ->> 'qtySold',
        parts_array ->> 'qtyOnHand',
        parts_array ->> 'qtyFilled',
        parts_array ->> 'qtyBackOrdered',
        parts_array ->> 'laborType',
        parts_array ->> 'mcdPercentage',
        parts_array ->> 'extendedSale',
        parts_array ->> 'extendedCost',
        parts_array ->> 'sale',
        parts_array ->> 'cost',
        parts_array ->> 'list',
        parts_array ->> 'comp',
        parts_array ->> 'coreSale',
        parts_array ->> 'coreCost',
        parts_array ->> 'number',
        parts_array ->> 'partClass'
    );
    fees_array = parts_array -> 'fees';
    fees_input_type = json_typeof(fees_array);
    IF fees_input_type = 'array' THEN
        FOR fees_array IN
        SELECT
        *
        FROM
        json_array_elements(fees_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END LOOP;
    ELSIF fees_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END IF;
    END IF;
    END IF;
    END LOOP;
    ELSIF job_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_job
    ("roNumber","lineCode","complaintCode","serviceRequest","campaignCode","addOnFlag","statusCode",
    "statusDesc","comebackFlag","dispatchCode","estimatedDuration","cause","storySequenceNo","storyEmployeeNo",
    "storyText","bookerNo","actualWork")
    VALUES (ro_number,
        job_array -> 'line' ->>'lineCode',
        job_array -> 'line' ->>'complaintCode',
        job_array -> 'line' ->>'serviceRequest',
        job_array -> 'line' ->>'campaignCode',
        job_array -> 'line' ->>'addOnFlag',
        job_array -> 'line' ->>'statusCode',
        job_array -> 'line' ->>'statusDesc',
        job_array -> 'line' ->>'comebackFlag',
        job_array -> 'line' ->>'dispatchCode',
        job_array -> 'line' ->>'estimatedDuration',
        job_array -> 'line' ->>'cause',
        job_array -> 'line' ->>'storySequenceNo',
        job_array -> 'line' ->>'storyEmployeeNo',
        job_array -> 'line' ->>'storyText',
        job_array -> 'line' ->>'bookerNo',
        job_array -> 'line' ->>'actualWork'
    );
        
    labor_array = job_array -> 'line' -> 'laborOperations';
    labor_input_type = json_typeof(labor_array);
    IF labor_input_type = 'array' THEN
        FOR labor_array IN
        SELECT
        *
        FROM
        json_array_elements(labor_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_labor
    ("roNumber","sequenceNo","lineCode","opCode","opCodeDesc","mcdPercentage","sale","cost","technicianIds",
    "actualHours","soldHours","otherHours","timeCardHours","comebackFlag","comebackRO","comebackSA",
    "comebackTech","type","forcedShopCharge","bookedDate","bookedTime","flagHours")
    VALUES (ro_number,
        labor_array ->> 'sequenceNo',
        labor_array ->> 'lineCode',
        labor_array ->> 'opCode',
        labor_array ->> 'opCodeDesc',
        labor_array ->> 'mcdPercentage',
        labor_array ->> 'sale',
        labor_array ->> 'cost',
        labor_array ->> 'technicianIds',
        labor_array ->> 'actualHours',
        labor_array ->> 'soldHours',
        labor_array ->> 'otherHours',
        labor_array ->> 'timeCardHours',
        labor_array ->> 'comebackFlag',
        labor_array ->> 'comebackRO',
        labor_array ->> 'comebackSA',
        labor_array ->> 'comebackTech',
        labor_array ->> 'type',
        labor_array ->> 'forcedShopCharge',
        labor_array ->> 'bookedDate',
        labor_array ->> 'bookedTime',
        labor_array ->> 'flagHours'
    );
    parts_array = labor_array -> 'parts';

    parts_input_type = json_typeof(parts_array);

    IF parts_input_type = 'array' THEN
        FOR parts_array IN
        SELECT
        *
        FROM
        json_array_elements(parts_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_parts
    ("roNumber","sequenceNo","lineCode","laborSequenceNo","desc","source","bin1","employeeId","outsideSalesmanId","specialStatus","compLineCode","qtyOrdered","qtySold",
    "qtyOnHand","qtyFilled","qtyBackOrdered","laborType","mcdPercentage","extendedSale","extendedCost","sale","cost","list","comp",
    "coreSale","coreCost","number","partClass")
    VALUES (ro_number,
        parts_array ->> 'sequenceNo',
        parts_array ->> 'lineCode',
        parts_array ->> 'laborSequenceNo',
        parts_array ->> 'desc',
        parts_array ->> 'source',
        parts_array ->> 'bin1',
        parts_array ->> 'employeeId',
        parts_array ->> 'outsideSalesmanId',
        parts_array ->> 'specialStatus',
        parts_array ->> 'compLineCode',
        parts_array ->> 'qtyOrdered',
        parts_array ->> 'qtySold',
        parts_array ->> 'qtyOnHand',
        parts_array ->> 'qtyFilled',
        parts_array ->> 'qtyBackOrdered',
        parts_array ->> 'laborType',
        parts_array ->> 'mcdPercentage',
        parts_array ->> 'extendedSale',
        parts_array ->> 'extendedCost',
        parts_array ->> 'sale',
        parts_array ->> 'cost',
        parts_array ->> 'list',
        parts_array ->> 'comp',
        parts_array ->> 'coreSale',
        parts_array ->> 'coreCost',
        parts_array ->> 'number',
        parts_array ->> 'partClass'
    );
    fees_array = parts_array -> 'fees';
    fees_input_type = json_typeof(fees_array);
    IF fees_input_type = 'array' THEN
        FOR fees_array IN
        SELECT
        *
        FROM
        json_array_elements(fees_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END LOOP;
    ELSIF fees_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END IF;
    END LOOP;
    ELSIF parts_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_parts
    ("roNumber","sequenceNo","lineCode","laborSequenceNo","desc","source","bin1","employeeId","outsideSalesmanId","specialStatus","compLineCode","qtyOrdered","qtySold",
    "qtyOnHand","qtyFilled","qtyBackOrdered","laborType","mcdPercentage","extendedSale","extendedCost","sale","cost","list","comp",
    "coreSale","coreCost","number","partClass")
    VALUES (ro_number,
        parts_array ->> 'sequenceNo',
        parts_array ->> 'lineCode',
        parts_array ->> 'laborSequenceNo',
        parts_array ->> 'desc',
        parts_array ->> 'source',
        parts_array ->> 'bin1',
        parts_array ->> 'employeeId',
        parts_array ->> 'outsideSalesmanId',
        parts_array ->> 'specialStatus',
        parts_array ->> 'compLineCode',
        parts_array ->> 'qtyOrdered',
        parts_array ->> 'qtySold',
        parts_array ->> 'qtyOnHand',
        parts_array ->> 'qtyFilled',
        parts_array ->> 'qtyBackOrdered',
        parts_array ->> 'laborType',
        parts_array ->> 'mcdPercentage',
        parts_array ->> 'extendedSale',
        parts_array ->> 'extendedCost',
        parts_array ->> 'sale',
        parts_array ->> 'cost',
        parts_array ->> 'list',
        parts_array ->> 'comp',
        parts_array ->> 'coreSale',
        parts_array ->> 'coreCost',
        parts_array ->> 'number',
        parts_array ->> 'partClass'
    );
    fees_array = parts_array -> 'fees';
    fees_input_type = json_typeof(fees_array);
    IF fees_input_type = 'array' THEN
        FOR fees_array IN
        SELECT
        *
        FROM
        json_array_elements(fees_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END LOOP;
    ELSIF fees_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END IF;
    END IF;
    END LOOP;
    ELSIF labor_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_labor
    ("roNumber","sequenceNo","lineCode","opCode","opCodeDesc","mcdPercentage","sale","cost","technicianIds",
    "actualHours","soldHours","otherHours","timeCardHours","comebackFlag","comebackRO","comebackSA",
    "comebackTech","type","forcedShopCharge","bookedDate","bookedTime","flagHours")
    VALUES (ro_number,
        labor_array ->> 'sequenceNo',
        labor_array ->> 'lineCode',
        labor_array ->> 'opCode',
        labor_array ->> 'opCodeDesc',
        labor_array ->> 'mcdPercentage',
        labor_array ->> 'sale',
        labor_array ->> 'cost',
        labor_array ->> 'technicianIds',
        labor_array ->> 'actualHours',
        labor_array ->> 'soldHours',
        labor_array ->> 'otherHours',
        labor_array ->> 'timeCardHours',
        labor_array ->> 'comebackFlag',
        labor_array ->> 'comebackRO',
        labor_array ->> 'comebackSA',
        labor_array ->> 'comebackTech',
        labor_array ->> 'type',
        labor_array ->> 'forcedShopCharge',
        labor_array ->> 'bookedDate',
        labor_array ->> 'bookedTime',
        labor_array ->> 'flagHours'
    );
    parts_array = labor_array -> 'parts';

    parts_input_type = json_typeof(parts_array);

    IF parts_input_type = 'array' THEN
        FOR parts_array IN
        SELECT
        *
        FROM
        json_array_elements(parts_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_parts
    ("roNumber","sequenceNo","lineCode","laborSequenceNo","desc","source","bin1","employeeId","outsideSalesmanId","specialStatus","compLineCode","qtyOrdered","qtySold",
    "qtyOnHand","qtyFilled","qtyBackOrdered","laborType","mcdPercentage","extendedSale","extendedCost","sale","cost","list","comp",
    "coreSale","coreCost","number","partClass")
    VALUES (ro_number,
        parts_array ->> 'sequenceNo',
        parts_array ->> 'lineCode',
        parts_array ->> 'laborSequenceNo',
        parts_array ->> 'desc',
        parts_array ->> 'source',
        parts_array ->> 'bin1',
        parts_array ->> 'employeeId',
        parts_array ->> 'outsideSalesmanId',
        parts_array ->> 'specialStatus',
        parts_array ->> 'compLineCode',
        parts_array ->> 'qtyOrdered',
        parts_array ->> 'qtySold',
        parts_array ->> 'qtyOnHand',
        parts_array ->> 'qtyFilled',
        parts_array ->> 'qtyBackOrdered',
        parts_array ->> 'laborType',
        parts_array ->> 'mcdPercentage',
        parts_array ->> 'extendedSale',
        parts_array ->> 'extendedCost',
        parts_array ->> 'sale',
        parts_array ->> 'cost',
        parts_array ->> 'list',
        parts_array ->> 'comp',
        parts_array ->> 'coreSale',
        parts_array ->> 'coreCost',
        parts_array ->> 'number',
        parts_array ->> 'partClass'
    );
    fees_array = parts_array -> 'fees';
    fees_input_type = json_typeof(fees_array);
    IF fees_input_type = 'array' THEN
        FOR fees_array IN
        SELECT
        *
        FROM
        json_array_elements(fees_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END LOOP;
    ELSIF fees_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END IF;
    END LOOP;
    ELSIF parts_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_parts
    ("roNumber","sequenceNo","lineCode","laborSequenceNo","desc","source","bin1","employeeId","outsideSalesmanId","specialStatus","compLineCode","qtyOrdered","qtySold",
    "qtyOnHand","qtyFilled","qtyBackOrdered","laborType","mcdPercentage","extendedSale","extendedCost","sale","cost","list","comp",
    "coreSale","coreCost","number","partClass")
    VALUES (ro_number,
        parts_array ->> 'sequenceNo',
        parts_array ->> 'lineCode',
        parts_array ->> 'laborSequenceNo',
        parts_array ->> 'desc',
        parts_array ->> 'source',
        parts_array ->> 'bin1',
        parts_array ->> 'employeeId',
        parts_array ->> 'outsideSalesmanId',
        parts_array ->> 'specialStatus',
        parts_array ->> 'compLineCode',
        parts_array ->> 'qtyOrdered',
        parts_array ->> 'qtySold',
        parts_array ->> 'qtyOnHand',
        parts_array ->> 'qtyFilled',
        parts_array ->> 'qtyBackOrdered',
        parts_array ->> 'laborType',
        parts_array ->> 'mcdPercentage',
        parts_array ->> 'extendedSale',
        parts_array ->> 'extendedCost',
        parts_array ->> 'sale',
        parts_array ->> 'cost',
        parts_array ->> 'list',
        parts_array ->> 'comp',
        parts_array ->> 'coreSale',
        parts_array ->> 'coreCost',
        parts_array ->> 'number',
        parts_array ->> 'partClass'
    );
    fees_array = parts_array -> 'fees';
    fees_input_type = json_typeof(fees_array);
    IF fees_input_type = 'array' THEN
        FOR fees_array IN
        SELECT
        *
        FROM
        json_array_elements(fees_array)
        LOOP 
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END LOOP;
    ELSIF fees_input_type = 'object' THEN
    INSERT INTO du_dms_fortellis_model.etl_fees
    ("roNumber","id","cost","laborType","lineCode","lopOrPartFlag","mcdPercentage","opCode","opCodeDesc","sale","sequenceNo","type","LoporPartSeqNo")
    VALUES (ro_number,
    fees_array ->> 'id',
    fees_array ->> 'cost',
    fees_array ->> 'laborType',
    fees_array ->> 'lineCode',
    fees_array ->> 'lopOrPartFlag',
    fees_array ->> 'mcdPercentage',
    fees_array ->> 'opCode',
    fees_array ->> 'opCodeDesc',
    fees_array ->> 'sale',
    fees_array ->> 'sequenceNo',
    fees_array ->> 'type',
    fees_array ->> 'lopOrPartSeqNo'
    );
    END IF;
    END IF;
    END IF;
    END IF;
END;
$func$;
