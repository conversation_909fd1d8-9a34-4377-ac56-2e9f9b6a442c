BEGIN;
\pset pager off

CREATE OR REPLACE VIEW du_dms_fortellis_model.accounting_details
AS WITH gl_data AS (
    SELECT
        split_part(etl_accounts."hostItemId":: text, '*' :: text, 1)   AS company_id,
        split_part(etl_accounts."hostItemId":: text, '*' :: text,
                   2)                                                                              AS journal_id,
        split_part(etl_accounts."hostItemId":: text, '*' :: text,
                   3)                                                                              AS ro_number,
        split_part(etl_accounts."hostItemId":: text, '*' :: text,
                   4)                                                                              AS book_number,
        split_part(etl_accounts."hostItemId":: text, '*' :: text,
                   5)                                                                              AS posting_time,
        split_part(etl_accounts."hostItemId":: text, '*' :: text,
                   6)                                                                              AS posting_order,
        etl_accounts."accountNumber"                                                        AS account_number,
        abs(((etl_accounts."postingAmount":: text) :: numeric) *
            100 :: numeric)                                                                        AS posting_amount
    FROM etl_accounts
)
, coa_data AS (
    SELECT
        btrim(regexp_replace(etl_account_coa."accountNumber":: text, '[\n	
                                ]+' :: text, ' ' :: text, 'g' :: text))                            AS account_number,
        split_part(btrim(regexp_replace(etl_account_coa."chain":: text, '[\n	
                                ]+' :: text, ' ' :: text, 'g' :: text)), '*' :: text, 2)           AS chain,
        btrim(regexp_replace(etl_account_coa."accountType":: text, '[\n	
                                ]+' :: text, ' ' :: text, 'g' :: text))                            AS account_type
    FROM etl_account_coa
)
, coa_raw AS (
    SELECT
        t.ro_number,
        t.account_number,
        case when t.account_type='C' then sum(t.cost) else sum(t.sale) end AS                   sale,
        case when t.account_type='C' then sum(t.sale) else sum(t.cost) end AS                   cost,
        t.company_id,
        t.account_type,
        count(DISTINCT posting_time) > 1 posting_count,
        count(DISTINCT journal_id) > 1   journal_count
    FROM (SELECT
              gd.ro_number,
              COALESCE(gd1.account_number, gd.account_number) AS account_number,
              CASE
              WHEN gd1.account_number IS NULL
                  THEN gd.posting_amount
              ELSE gd1.posting_amount
              END                                             AS sale,
              CASE
              WHEN COALESCE(cd.chain, '0' :: text) = cd.account_number
                  THEN '0' :: numeric
              ELSE
                  CASE
                  WHEN gd1.account_number IS NULL
                      THEN gd1.posting_amount
                  ELSE gd.posting_amount
                  END
              END                                             AS cost,
              CASE
              WHEN gd1.account_number IS NULL
                  THEN cd.account_type
              ELSE (cd1.account_type || ',' :: text) || cd.account_type
              END                                             AS account_type,
              gd.company_id,
              gd.posting_time,
              gd.journal_id
          FROM coa_data cd
              JOIN gl_data gd USING (account_number)
              LEFT JOIN gl_data gd1 ON cd.chain = gd1.account_number AND gd.ro_number = gd1.ro_number AND
                                       gd1.account_number <> cd.account_number
              LEFT JOIN coa_data cd1 ON cd.chain = cd1.account_number AND gd1.ro_number IS NOT NULL) t
    GROUP BY t.ro_number, t.account_number, t.account_type, t.company_id
    ORDER BY (
        CASE t.account_type
        WHEN 'S' :: text
            THEN 1
        ELSE 2
        END)
)
, coa_final AS (
    SELECT
        c1.ro_number,
        c1.account_number,
        c1.sale,
        c1.cost,
        c1.company_id,
        c1.account_type,
        c1.posting_count,
        c1.journal_count
    FROM coa_raw c1
        JOIN coa_raw c2 USING (ro_number, account_number)
    WHERE c1.cost <> c2.cost AND c1.cost = 0 :: numeric
)
, account_data AS (
    SELECT
        coa_raw.ro_number,
        coa_raw.account_number,
        coa_raw.sale,
        coa_raw.cost,
        coa_raw.company_id,
        coa_raw.account_type,
        coa_raw.posting_count,
        coa_raw.journal_count
    FROM coa_raw
        LEFT JOIN coa_final USING (ro_number, account_number, cost)
    WHERE coa_final.* IS NULL
)
, acct_data AS (
    SELECT
        account_data.ro_number,
        account_data.account_number,
        trunc(account_data.sale) AS sale,
        CASE
        WHEN account_data.account_type = 'A' :: text AND
             (account_data.cost IS NULL OR account_data.cost = '0' :: numeric)
            THEN '0' :: text
        ELSE trunc(COALESCE(account_data.cost, '0' :: numeric)) :: text
        END                      AS cost,
        account_data.company_id,
        account_data.account_type,
        account_data.posting_count,
        account_data.journal_count
    FROM account_data
)
SELECT
    acct_data.ro_number,
    acct_data.account_number,
    acct_data.sale,
    acct_data.cost,
    acct_data.company_id,
    acct_data.account_type,
    acct_data.journal_count,
    acct_data.posting_count
FROM acct_data;



CREATE OR REPLACE VIEW du_dms_fortellis_model.exception_ro_list
AS WITH RECURSIVE r AS (SELECT
                            x.slno,
                            x.ro_number,
                            x.account_number,
                            x.sale,
                            CAST(x.sale AS varchar(100)) AS combi,
                            sale :: numeric              AS journal_tot,
                            x.cost,
                            CAST(x.cost AS varchar(100)) AS cost_combi,
                            cost :: numeric              AS cost_journal_tot,
                            x.account_type
                        FROM (SELECT
                                  row_number()
                                  OVER () AS slno,
                                  *
                              FROM du_dms_fortellis_model.accounting_details
                                 --where exception is null
                             ) x
                        UNION ALL
                        SELECT
                            x.slno,
                            x.ro_number,
                            x.account_number,
                            x.sale,
                            CAST(r.combi || CAST(',' AS varchar(1)) || x.sale AS varchar(100)),
                            x.sale :: numeric + r.journal_tot :: numeric      AS journal_tot,
                            x.cost,
                            CAST(r.cost_combi || CAST(',' AS varchar(1)) || x.cost AS varchar(100)),
                            x.cost :: numeric + r.cost_journal_tot :: numeric AS cost_journal_tot,
                            x.account_type
                        FROM r
                            JOIN (SELECT
                                      row_number()
                                      OVER () AS slno,
                                      *
                                  FROM du_dms_fortellis_model.accounting_details
                                     --where exception is null
                                 ) x ON x.ro_number = r.ro_number  -- AND x.account_number = r.account_number
								        AND (x.account_type = split_part(r.account_type, ',', 1) 
                                          		or x.account_type = split_part(r.account_type, ',', 2)
                                          	    or x.account_type = r.account_type)
                                        AND (x.slno > r.slno) -- and X.sale::numeric < R.sale

)
    ,accounting_data AS (SELECT *
                            FROM r
                            WHERE ((journal_tot IS NOT NULL) OR (cost_journal_tot IS NOT NULL))
                                   AND ro_number IN (SELECT "roNumber" FROM etl_head)
)
    , proxy_data AS (  SELECT
                           lbr."roNumber"                                           AS ro_number,
                           sum(coalesce(nullif("sale", '') :: numeric, 0)) * 100 AS sale_amount,
                           sum(coalesce(nullif("cost", '') :: numeric, 0)) * 100 AS cost_amount,
                           left(lbr."type", 1)                              AS prt_billing_type
                       FROM etl_labor lbr
                           JOIN etl_job job
                               ON lbr."roNumber" = job."roNumber"
                                  AND lbr."lineCode" = job."lineCode"
                       GROUP BY lbr."roNumber", left(lbr."type", 1)
                       HAVING sum(coalesce(nullif("sale", '') :: numeric, 0)) * 100 != 0
			 OR sum(coalesce(nullif("cost", '') :: numeric, 0)) * 100 != 0
)
    ,part_data AS (  SELECT
                         "roNumber"                                                     AS ro_number,
                         left("laborType", 1)                                        AS prt_billing_type,
                         sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("sale"), '') :: numeric, 0)) * 100 AS sale_amount,
                         sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("cost"), '') :: numeric, 0)) * 100 AS cost_amount
                     FROM etl_parts
                     GROUP BY "roNumber", left("laborType", 1)
                     HAVING  sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("sale"), '') :: numeric, 0)) * 100 != 0
			 OR sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("cost"), '') :: numeric, 0)) * 100 != 0
)
    ,tax_data AS ( SELECT
                       "roNumber"                       AS ro_number,
                       left("payType", 1)            AS prt_billing_type,
                       sum("roTax" :: numeric) * 100 AS sale_amount
                   FROM etl_total
                   GROUP BY "roNumber", left("payType", 1)
                   HAVING sum("roTax" :: numeric) * 100 != 0
)

SELECT
    ro_number,
    string_agg(DISTINCT exception, ',') AS exception_list
FROM (SELECT
          ro_number,
          'Labor Sale Amount Mismatch' AS exception
      FROM du_dms_fortellis_model.accounting_details
      WHERE ro_number IN (SELECT DISTINCT ro_number
                          FROM proxy_data
                              LEFT JOIN (SELECT DISTINCT
                                             ro_number,
                                             prt_billing_type
                                         FROM accounting_data
                                             JOIN proxy_data USING (ro_number)
                                         WHERE abs(sale_amount) = abs(sale) OR abs(sale_amount) = abs(journal_tot)) t
                              USING (ro_number, prt_billing_type)
                          WHERE t.ro_number IS NULL)

      UNION ALL

      SELECT
          ro_number,
          'Labor Cost Amount Mismatch' AS exception
      FROM du_dms_fortellis_model.accounting_details
      WHERE ro_number IN (SELECT DISTINCT ro_number
                          FROM proxy_data
                              LEFT JOIN (SELECT DISTINCT
                                             ro_number,
                                             prt_billing_type
                                         FROM accounting_data
                                             JOIN proxy_data USING (ro_number)
                                         WHERE abs(cost_amount)  = abs(cost::numeric) OR abs(cost_amount) = abs(cost_journal_tot)) t
                              USING (ro_number, prt_billing_type)
                          WHERE t.ro_number IS NULL)

      UNION ALL

      SELECT
          ro_number,
          'Part Sale Amount Mismatch' AS exception
      FROM du_dms_fortellis_model.accounting_details
      WHERE ro_number IN (SELECT DISTINCT ro_number
                          FROM part_data
                              LEFT JOIN (SELECT DISTINCT
                                             ro_number,
                                             prt_billing_type
                                         FROM accounting_data
                                             JOIN part_data USING (ro_number)
                                         WHERE abs(sale_amount) = abs(sale) OR abs(sale_amount) = abs(journal_tot)) t
                              USING (ro_number, prt_billing_type)
                          WHERE t.ro_number IS NULL)

      UNION ALL

      SELECT
          ro_number,
          'Part Cost Amount Mismatch' AS exception
      FROM du_dms_fortellis_model.accounting_details
      WHERE ro_number IN (SELECT DISTINCT ro_number
                          FROM part_data
                              LEFT JOIN (SELECT DISTINCT
                                             ro_number,
                                             prt_billing_type
                                         FROM accounting_data
                                             JOIN part_data USING (ro_number)
                                         WHERE abs(cost_amount) = abs(cost::numeric) OR abs(cost_amount) = abs(cost_journal_tot)) t
                              USING (ro_number, prt_billing_type)
                          WHERE t.ro_number IS NULL)

      UNION ALL

      SELECT
          ro_number,
          'Tax Sale Amount Mismatch' AS exception
      FROM du_dms_fortellis_model.accounting_details
      WHERE ro_number IN (SELECT DISTINCT ro_number
                          FROM tax_data
                              LEFT JOIN (SELECT DISTINCT
                                             ro_number,
                                             prt_billing_type
                                         FROM accounting_data
                                             JOIN tax_data USING (ro_number)
                                         WHERE abs(sale_amount) = abs(sale) OR abs(sale_amount) = abs(journal_tot)) t
                              USING (ro_number, prt_billing_type)
                          WHERE t.ro_number IS NULL)
     ) x
GROUP BY ro_number;

    --     \o 'Exception_ROs.csv'
    --     COPY (
	-- 	WITH cte AS (SELECT
    --              		row_number()
    --              		OVER () AS slno,
    --              		*
    --          		      FROM du_dms_fortellis_model.exception_ro_list
	-- 	)
    -- 		  , ro_count AS ( SELECT count(*)
    --                 		  FROM du_dms_fortellis_model.etl_head
	-- 	)
	-- 	SELECT
    -- 			ro_number,
    -- 			exception_list
	-- 	FROM cte

	-- 	UNION ALL

	-- 	SELECT
    -- 			NULL,
    -- 			(SELECT max(slno) FROM cte) || ' mismatch out of ' 
	-- 		  || (SELECT * FROM ro_count) || '[' ||
    -- 			  (((SELECT max(slno) FROM cte) * 100) / 
	-- 			(SELECT *  FROM ro_count)) || '% mismatch]'
    --   ) TO stdout WITH (FORMAT csv, HEADER true);

      \o 'Multiple_Journal_Posting_Exception.csv'
        COPY ( WITH coa_details AS (SELECT
                         TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                             'g')) AS accountNumber,
                         TRIM(regexp_replace("accountDesc", E'[\\n\t\r]+', ' ',
                                             'g')) AS accountdescription,
                         TRIM(regexp_replace("accountType", E'[\\n\t\r]+', ' ',
                                             'g')) AS accounttype,
                         TRIM(regexp_replace("incBalReportGrpDesc", E'[\\n\t\r]+', ' ',
                                             'g')) AS incbalgrpdesc,
                         TRIM(regexp_replace("incBalReportSubGrp", E'[\\n\t\r]+', ' ',
                                             'g')) AS incbalsubgrp
                     FROM du_dms_fortellis_model.etl_account_coa
	 ),
         gl_details AS (SELECT
                           TRIM(regexp_replace("hostItemId", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS hostItemId,
                           TRIM(regexp_replace("accountingDate", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS accountingdate,
                           TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS accountNumber,
                           split_part("hostItemId", '*', 3) AS invoice,
                           TRIM(regexp_replace("companyId", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS companyid,
                           TRIM(regexp_replace("controlNo", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS control,
                           TRIM(regexp_replace("journalId", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS journalid,
                           TRIM(regexp_replace("postingAmount", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingAmount,
                           TRIM(regexp_replace("postingSequence", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingsequence,
                           TRIM(regexp_replace("postingTime", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingtime,
                           TRIM(regexp_replace("referenceNo", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS refer
                       FROM du_dms_fortellis_model.etl_accounts
    	),
    	journal_ros as(select distinct ro_number from du_dms_fortellis_model.accounting_details
where journal_count or posting_count
)
	SELECT
            hostItemId AS "Host Item ID",
    	    invoice AS "#Invoice",
    	    accountingdate AS "Accounting Date",
    	    accountNumber AS "Account Number",
    	    accountdescription AS "Acccount Description",
    	    accounttype AS "Account Type",
    	    incbalgrpdesc AS "incBalReportGrpDesc",
    	    incbalsubgrp AS "incBalReportSubGrp",
    	    companyid AS "Company ID",
    	    control AS "controlNo",
    	    journalid AS "Journal ID",
    	    postingAmount AS "Posting Amount",
    	    postingsequence AS "Posting Sequence",
    	    postingtime AS "Posting Time",
    	    'Multiple journalId/postingTime' as "Exception"
        FROM journal_ros 
            JOIN gl_details on invoice = ro_number
    		LEFT JOIN coa_details USING (accountNumber)
      ) TO stdout WITH (FORMAT csv, HEADER true);

     \o 'Mismatch_Exception.csv'
        COPY (
		WITH coa_details AS (SELECT
                         TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                             'g')) AS accountNumber,
                         TRIM(regexp_replace("accountDesc", E'[\\n\t\r]+', ' ',
                                             'g')) AS accountdescription,
                         TRIM(regexp_replace("accountType", E'[\\n\t\r]+', ' ',
                                             'g')) AS accounttype,
                         TRIM(regexp_replace("incBalReportGrpDesc", E'[\\n\t\r]+', ' ',
                                             'g')) AS incbalgrpdesc,
                         TRIM(regexp_replace("incBalReportSubGrp", E'[\\n\t\r]+', ' ',
                                             'g')) AS incbalsubgrp
                     FROM du_dms_fortellis_model.etl_account_coa
	 ),
         gl_details AS (SELECT
                           TRIM(regexp_replace("hostItemId", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS hostItemId,
                           TRIM(regexp_replace("accountingDate", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS accountingdate,
                           TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS accountNumber,
                           split_part("hostItemId", '*', 3) AS invoice,
                           TRIM(regexp_replace("companyId", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS companyid,
                           TRIM(regexp_replace("controlNo", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS control,
                           TRIM(regexp_replace("journalId", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS journalid,
                           TRIM(regexp_replace("postingAmount", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingAmount,
                           TRIM(regexp_replace("postingSequence", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingsequence,
                           TRIM(regexp_replace("postingTime", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingtime,
                           TRIM(regexp_replace("referenceNo", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS refer
                       FROM du_dms_fortellis_model.etl_accounts
    	),
    	journal_ros as(select ro_number,regexp_split_to_table(exception_list, E',') AS exception_list from du_dms_fortellis_model.exception_ro_list
)
	SELECT
            hostItemId AS "Host Item ID",
    	    invoice AS "#Invoice",
    	    accountingdate AS "Accounting Date",
    	    accountNumber AS "Account Number",
    	    accountdescription AS "Acccount Description",
    	    accounttype AS "Account Type",
    	    incbalgrpdesc AS "incBalReportGrpDesc",
    	    incbalsubgrp AS "incBalReportSubGrp",
    	    companyid AS "Company ID",
    	    control AS "controlNo",
    	    journalid AS "Journal ID",
    	    postingAmount AS "Posting Amount",
    	    postingsequence AS "Posting Sequence",
    	    postingtime AS "Posting Time",
    	    exception_list as "Exception"
        FROM exception_ro_list 
            JOIN gl_details on invoice = ro_number
    		LEFT JOIN coa_details USING (accountNumber)
      ) TO stdout WITH (FORMAT csv, HEADER true);

  \o 'GL_ro_not_found.csv'
        COPY ( 
                with proxy_data AS (  SELECT
                           lbr."roNumber"                                           AS ro_number,
                           sum(coalesce(nullif("sale", '') :: numeric, 0)) * 100 AS sale_amount,
                           sum(coalesce(nullif("cost", '') :: numeric, 0)) * 100 AS cost_amount,
                           left(lbr."type", 1)                              AS prt_billing_type
                       FROM etl_labor lbr
                           JOIN etl_job job
                               ON lbr."roNumber" = job."roNumber"
                                  AND lbr."lineCode" = job."lineCode"
                       GROUP BY lbr."roNumber", left(lbr."type", 1)
                       HAVING sum(coalesce(nullif("sale", '') :: numeric, 0)) * 100 != 0
			 OR sum(coalesce(nullif("cost", '') :: numeric, 0)) * 100 != 0
)
    ,part_data AS (  SELECT
                         "roNumber"                                                     AS ro_number,
                         left("laborType", 1)                                        AS prt_billing_type,
                         sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("sale"), '') :: numeric, 0)) * 100 AS sale_amount,
                         sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("cost"), '') :: numeric, 0)) * 100 AS cost_amount
                     FROM etl_parts
                     GROUP BY "roNumber", left("laborType", 1)
                     HAVING  sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("sale"), '') :: numeric, 0)) * 100 != 0
			 OR sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("cost"), '') :: numeric, 0)) * 100 != 0
)
    ,tax_data AS ( SELECT
                       "roNumber"                       AS ro_number,
                       left("payType", 1)            AS prt_billing_type,
                       sum("roTax" :: numeric) * 100 AS sale_amount
                   FROM etl_total
                   GROUP BY "roNumber", left("payType", 1)
                   HAVING sum("roTax" :: numeric) * 100 != 0
)

	SELECT 
    eh."roNumber", 
    eh."openDate", 
    eh."closedDate", 
    eh."voidedDate", 
    ev.make
FROM 
    du_dms_fortellis_model.etl_head eh
LEFT JOIN 
    du_dms_fortellis_model.etl_vehicle ev 
    ON eh."roNumber" = ev."roNumber" 
WHERE 
    eh."roNumber" NOT IN (
        SELECT split_part("hostItemId", '*', 3) 
        FROM du_dms_fortellis_model.etl_accounts
    )
    AND (
        eh."roNumber" IN (SELECT ro_number FROM proxy_data)
        OR eh."roNumber" IN (SELECT ro_number FROM part_data)
        OR eh."roNumber" IN (SELECT ro_number FROM tax_data)
    )
      ) TO stdout WITH (FORMAT csv, HEADER true);
COMMIT;
