BEGIN;
\pset pager off
SET client_min_messages TO WARNING;
WITH acc_details AS(
    SELECT  DISTINCT
        REPLACE((d."referenceNo")::text,'"','')                         AS "RO#",
        REPLACE((d."accountingDate")::text, '"','')               AS "accountingDate",
        REPLACE((d."journalId")::text, '"','')                    AS "journalId"
    FROM etl_accounts d
        JOIN etl_account_coa c
            ON (d."accountNumber")::text = (c."accountNumber")::text
    WHERE  REPLACE((c."accountType")::text, '"','')  in ('S','C')
    ORDER BY REPLACE((d."referenceNo")::text,'"','')
)
,rowise_journlid AS(
    SELECT DISTINCT
        REPLACE((d."referenceNo")::text,'"','')                        AS "RO#",
        REPLACE((d."journalId")::text, '"','')                   AS "journalId",
        REPLACE((d."accountingDate")::text, '"','')              AS "accountingDate"
    FROM etl_accounts d
)
,delete_journal_id AS(
    SELECT r."RO#",r."journalId",r."accountingDate"
    FROM rowise_journlid r
    LEFT JOIN acc_details a
             ON a."RO#"= r."RO#" AND a."journalId"=r."journalId" 
    WHERE a."journalId" IS NULL 
      order by a."RO#"
)
,delete_journal_id_account AS(
    SELECT rs."RO#",rs."journalId",rs."accountingDate"
    FROM delete_journal_id rs
    JOIN acc_details acs
             ON acs."RO#"= rs."RO#"  
             and  acs."accountingDate" <> rs."accountingDate"    
)
DELETE
FROM etl_accounts atd
USING delete_journal_id_account dr
WHERE  REPLACE((atd."referenceNo")::text,'"','')  =  dr."RO#"
AND REPLACE((atd."journalId")::text, '"','') = dr."journalId"
AND REPLACE((atd."accountingDate")::text, '"','')=dr."accountingDate";


CREATE TABLE proxy_accounting as
    WITH pay_summary AS (
            SELECT "roNumber"::text, SUM("paymentAmount"::numeric) AS "paymentAmount"
                FROM etl_pay
                WHERE "insuranceFlag" = 'N'
                GROUP BY "roNumber"
            )
            , coa AS (
                SELECT
                    (ARRAY_AGG("accountType"))[1]               AS "accountType",
                    STRING_AGG("accountDesc", ', ')  AS "accountDesc",
                    "accountNumber"                             AS "accountNumber",
                    (ARRAY_AGG("chain"))[1]                     AS "chain"
                FROM etl_account_coa GROUP BY "accountNumber"
            )
            , etl_accts AS (
                SELECT
                    d."hostItemId"          AS "hostItemId",
                    d."accountingDate"      AS "accountingDate",
                    d."accountNumber"       AS "accountNumber",
                    d."companyId"           AS "companyId",
                    d."controlNo"             AS "controlNo",
                    d."controlNo2"            AS "controlNo2",
                    d."controlTypeCode"         AS "controlTypeCode",
                    d."controlType2Code"        AS "controlType2Code",
                    d."currentMonth"        AS "currentMonth",
                    d."detailDescription"   AS "detailDescription",
                    d."distillControlTypeCode"  AS "distillControlTypeCode",
                    d."distillControlType2Code" AS "distillControlType2Code",
                    d."journalId"           AS "journalId",
                    d."postingAmount"       AS "postingAmount",
                    d."postingSequence"     AS "postingSequence",
                    d."postingTime"         AS "postingTime",
                    d."productivityNo"              AS "productivityNo",
                    d."productivityNo2"             AS "productivityNo2",
                    d."productivityNoType"            AS "productivityNoType",
                    d."productivityNoType2"           AS "productivityNoType2",
                    d."referenceNo"               AS "referenceNo",
                    d."scheduleNumber"      AS "scheduleNumber",
                    d."statCount"           AS "statCount",
                    coa."accountType"
                FROM etl_accounts d
                    JOIN coa
                        ON (d."accountNumber"::text) = (coa."accountNumber")
                --WHERE replace((d."referenceNo"::json -> '_text'::text)::text, '"'::text, ''::text) = '117286' --'457014'--'457012'--'457023' --'456989'
            )
            , etl_accts_window1 AS (
                SELECT d."hostItemId",
                    d."accountingDate",
                    d."accountNumber",
                    d."companyId",
                    d."controlNo",
                    d."controlNo2",
                    d."controlTypeCode",
                    d."controlType2Code",
                    d."currentMonth",
                    d."detailDescription",
                    d."distillControlTypeCode",
                    d."distillControlType2Code",
                    d."journalId",
                    d."postingAmount",
                    d."postingSequence",
                    d."postingTime",
                    d."productivityNo",
                    d."productivityNo2",
                    d."productivityNoType",
                    d."productivityNoType2",
                    d."referenceNo",
                    d."scheduleNumber",
                    d."statCount",
                    d."accountType",
                    COUNT(*) OVER(Partition By d."referenceNo", d."postingTime")                       AS posting_time_count,
                    bool_or(d."accountType" = 'S')
                        OVER(Partition By d."referenceNo", d."postingTime")                            AS any_sale_in_posting,
                    array_agg(CASE WHEN (d."accountType" = 'S' AND to_number("postingAmount",'**********.99') != 0)
                                    OR d."accountType" != 'S'
                                THEN "accountNumber" END)
                        OVER(Partition By d."referenceNo", d."postingTime")                            AS acct_no_list_of_posting,
                    SUM("postingAmount"::numeric) 
                        OVER(Partition By d."referenceNo", d."accountNumber", d."postingTime")         AS account_posting_amount
                FROM etl_accts d
                    
            )
            ,etl_accts_window2 AS (
                SELECT *,
                bool_or(ps."roNumber" IS NOT NULL)
                    OVER(Partition By d."referenceNo", d."postingTime")                  AS have_matching_pay_payment
                FROM etl_accts_window1 d
                    LEFT JOIN pay_summary ps
                        ON ps."roNumber" = d."referenceNo"
                        AND ps."paymentAmount" = d.account_posting_amount
                        AND d."accountType" = 'A'
            )
            , matching_payments AS (
                SELECT *, 
                    rank() OVER (Partition BY "referenceNo" ORDER BY any_sale_in_posting DESC, "postingTime") AS rn
                FROM etl_accts_window2
                WHERE have_matching_pay_payment
            )
            -- , matching_posting_ros as (
            --   SELECT MAX("postingTime") AS mp_posting, "referenceNo" AS mp_ro 
            --   FROM matching_payments GROUP BY "referenceNo"
            -- )
            , matching_payment_posting AS (
                SELECT m.*
                FROM matching_payments m 
                WHERE rn = 1
                --JOIN matching_posting_ros mr ON "referenceNo" = mp_ro
                --WHERE "postingTime" = mp_posting
            )
            , sale_acct_of_matching_posting AS (
                SELECT
                    "referenceNo",
                    array_agg("accountNumber")
                        FILTER(WHERE "accountType" = 'S') AS sale_acct
                FROM matching_payment_posting
                GROUP BY "referenceNo"
            )
            , unmatch_pay_selectable_posting AS (
                SELECT acct.*, 1
                FROM etl_accts_window2 acct
                    LEFT JOIN sale_acct_of_matching_posting mp USING ("referenceNo")
                WHERE NOT have_matching_pay_payment
                    AND (mp."referenceNo" IS NULL OR any_sale_in_posting)
                    AND (mp."referenceNo" IS NULL OR posting_time_count > 2)
                    AND (mp.sale_acct IS NULL OR NOT (acct.acct_no_list_of_posting && mp.sale_acct))
                    AND to_number(acct."postingAmount",'**********.99') != 0
            )
            , selectable_postings AS (
                SELECT * FROM matching_payment_posting
                WHERE ("accountType" = 'A' AND account_posting_amount > 0)
                    OR "accountType" != 'A'
                UNION ALL
                SELECT * FROM unmatch_pay_selectable_posting
                WHERE ("accountType" = 'A' AND account_posting_amount > 0)
                    OR "accountType" != 'A'
            )
            , acc_details AS (
                SELECT
                    d."referenceNo"                                               AS "RO#",
                    d."accountNumber"                                       AS "Account#",
                    d."accountType"                                         AS "Acc.Type(COA)",
                    SUM(to_number(d."postingAmount",'**********.99'))::text AS "postingAmount",
                    d."postingTime"
                FROM selectable_postings d
                GROUP BY d."referenceNo", d."accountNumber", d."accountType", d."postingTime"
            ) 

            , gl_data AS ( 
                SELECT DISTINCT
                    coalesce("referenceNo", split_part("hostItemId", '*', 3)) AS ro_number,
                    "accountNumber"                                     AS account_number,
                    "companyId"                                         AS company_id,
                    "postingAmount"::numeric     						AS posting_amount,
                    "postingTime"                                       AS posting_time
                FROM selectable_postings
            )
            , gl_data_with_posting_amount AS(
                SELECT 
                    ro_number,
                    account_number, 
                    ABS(SUM(nullif((posting_amount)*100, 0))) AS posting_amount,
                    company_id,
                    STRING_AGG(posting_time, ', ') AS posting_time
                FROM gl_data GROUP BY ro_number, account_number, company_id
            )
            , coa_data AS ( 
                SELECT
                    TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ', 'g'))             AS account_number,
                    split_part(TRIM(regexp_replace("chain", E'[\\n\t\r]+', ' ', 'g')), '*', 2) AS chain,
                    TRIM(regexp_replace("accountType", E'[\\n\t\r]+', ' ', 'g'))               AS account_type
                FROM coa
                WHERE (coa."accountDesc" NOT LIKE '%INV%' OR coa."accountDesc" LIKE '%CASH%')
                    AND "accountDesc" NOT LIKE '%WORK-IN-PROCESS%' 
                    AND "accountDesc" NOT LIKE '%WIP%' 
                    AND  "accountDesc" NOT LIKE '%WORK IN PROCESS%'
                )
            , coa_raw as( 
                SELECT 
                    ro_number,
                    account_number,
                    account_type,
                    SUM(sale)                                                 AS sale,
                    Cost_account_no,
                    Cost_account_type,
                    sum(cost)                                                 AS cost,
                    company_id,
 				    STRING_AGG(posting_time, ', ')                         AS posting_time
                FROM (
                SELECT
                        gd.ro_number,
                        coalesce(gd1.account_number, gd.account_number)     AS account_number,
                        CASE 
                        WHEN gd1.account_number IS NULL AND cd.account_type='C'
                            THEN gd1.posting_amount
                            WHEN gd1.account_number IS NULL
                            THEN gd.posting_amount
                        ELSE gd1.posting_amount END                         AS sale,
                        case when gd.account_number=gd1.account_number is NULL
                            then null
                            else gd.account_number
                            END                                             AS Cost_account_no,
                        CASE 
                        WHEN coalesce(cd.chain, '0') = cd.account_number
                            THEN '0'
                        ELSE CASE 
                                WHEN gd1.account_number IS NULL AND cd.account_type='C'
                                THEN gd.posting_amount
                                WHEN gd1.account_number IS NULL
                                THEN gd1.posting_amount
                            ELSE gd.posting_amount END
                        END                                                 AS cost,
                        CASE WHEN gd1.account_number IS NULL
                            THEN cd.account_type
                            when gd1.account_number IS not null and gd.account_number IS not null 
                            then 
                                cd1.account_type
                        ELSE NULL END                                       AS account_type, 
                        CASE WHEN gd1.account_number IS not NULL
                            THEN cd.account_type
                        else null end                                      as  Cost_account_type,  
                        gd.company_id,
                        gd.posting_time					
                    FROM coa_data cd
                        JOIN gl_data_with_posting_amount gd USING (account_number)
                        LEFT JOIN gl_data_with_posting_amount gd1 
                            ON cd.chain = gd1.account_number 
                                AND gd.ro_number = gd1.ro_number
                                AND gd.company_id = gd1.company_id
                                AND gd1.account_number != cd.account_number
                        LEFT JOIN coa_data cd1 
                            ON cd.chain = cd1.account_number 
                                AND gd1.ro_number IS NOT NULL
                ) t
                GROUP BY ro_number, account_number,Cost_account_no, account_type,Cost_account_type, company_id  
                ORDER BY CASE account_type
                        WHEN 'S' 
                            THEN 1
                        ELSE 2 END
            )
            , coa_final AS ( 
                SELECT c1.*
                FROM coa_raw c1
                    JOIN coa_raw c2 USING (ro_number, account_number)
                WHERE c1.cost != c2.cost AND c1.cost = 0
            )
            SELECT DISTINCT coa_raw.ro_number,coa_raw.account_number, coa_raw.sale, coa_raw.cost, coa_raw.company_id, coa_raw.account_type, coa_raw.posting_time
            FROM coa_raw
                LEFT JOIN coa_final USING (ro_number, account_number, cost)
            WHERE coa_final IS NULL;


CREATE  TABLE ro_total as
    WITH repair_order AS (
                SELECT DISTINCT
                    "roNumber"                          AS ro_number
                FROM etl_head 
            )

            ,discount_detail AS (
            SELECT
                    "roNumber"                AS ro_number,
                    "appliedBy"               AS disc_applied,
                    "laborDiscount"::numeric AS labor_discount,
                    "partsDiscount"::numeric AS parts_discount,
                    "totalDiscount"::numeric AS total_discount
                FROM etl_discount)
            ,discount_qry AS (
                SELECT ro_number, SUM(total_discount) FILTER(WHERE disc_applied ~ '^C')  AS customer_discount,
                            SUM(total_discount) FILTER(WHERE disc_applied ~ '^I')  AS internal_discount,
                            SUM(total_discount) FILTER(WHERE disc_applied ~ '^W')  AS warranty_discount  FROM discount_detail GROUP BY ro_number
            )				  
            ,job_qry_cat AS (
                    SELECT lbr."roNumber" AS ro_number,
                    CASE
                    WHEN ("type" LIKE 'C%' OR "type"='C') THEN
                        coalesce(nullif("cost", '')::numeric, 0)
                    ELSE 
                        null 
                    END customer_lbrcost,
                    CASE
                    WHEN ("type" LIKE 'C%' OR "type"='C') THEN
                        coalesce(nullif("sale", '')::numeric, 0)
                    ELSE 
                        null 
                    END customer_lbrsale,
                    CASE
                    WHEN ("type" LIKE 'I%' OR "type"='I') THEN
                        coalesce(nullif("cost", '')::numeric, 0)
                    ELSE 
                        null 
                    END internal_lbrcost,
                    CASE
                    WHEN ("type" LIKE 'I%' OR "type"='I') THEN
                        coalesce(nullif("sale", '')::numeric, 0)
                    ELSE 
                        null 
                    END internal_lbrsale,
                    CASE
                    WHEN ("type" LIKE 'W%' OR "type"='W') THEN
                        coalesce(nullif("cost", '')::numeric, 0)
                    ELSE 
                        null 
                    END warranty_lbrcost,
                    CASE
                    WHEN ("type" LIKE 'W%' OR "type"='W') THEN
                        coalesce(nullif("sale", '')::numeric, 0)
                    ELSE 
                        null 
                    END warranty_lbrsale 
                        FROM etl_labor lbr
                        JOIN etl_job job
                        ON lbr."roNumber" = job."roNumber"
                        AND lbr."lineCode" = job."lineCode"
                        AND coalesce(nullif("sale", '')::numeric, 0) <>0
            )
            ,parts_detail_etl AS (
                SELECT "roNumber", "laborType", "qtySold", "cost", "sale" FROM etl_parts
                UNION ALL
                SELECT "roNumber", "laborType", "qtySold", "coreCost", "coreSale" FROM etl_parts 
                WHERE "coreCost"::numeric <> 0 OR "coreSale"::numeric <> 0
            )
            ,part_qry_cat AS (
                SELECT "roNumber" AS ro_number1,CASE
                    WHEN ("laborType" LIKE 'C%' OR "laborType"='C') THEN
                        coalesce(nullif(trim("qtySold"), '')::numeric, 0)*coalesce(nullif(trim("cost"), '')::numeric, 0)
                    else 
                        null 
                    end customer_partcost,
                    CASE
                    WHEN ("laborType" LIKE 'C%' OR "laborType"='C') THEN
                        coalesce(nullif(trim("qtySold"), '')::numeric, 0)*coalesce(nullif(trim("sale"), '')::numeric, 0)
                    else 
                        null 
                    end customer_partsale,
                    case
                    WHEN ("laborType" LIKE 'I%' OR "laborType"='I') THEN
                        coalesce(nullif(trim("qtySold"), '')::numeric, 0)*coalesce(nullif(trim("cost"), '')::numeric, 0)
                    else 
                        null 
                    end internal_partcost,
                    CASE
                    WHEN ("laborType" LIKE 'I%' OR "laborType"='I') THEN
                        coalesce(nullif(trim("qtySold"), '')::numeric, 0)*coalesce(nullif(trim("sale"), '')::numeric, 0)
                    else 
                        null 
                    end internal_partsale,
                        case
                    WHEN ("laborType" LIKE 'W%' OR "laborType"='W') THEN
                        coalesce(nullif(trim("qtySold"), '')::numeric, 0)*coalesce(nullif(trim("cost"), '')::numeric, 0)
                    else 
                        null 
                    end warranty_partcost,
                    CASE
                    WHEN ("laborType" LIKE 'W%' OR "laborType"='W') THEN
                        coalesce(nullif(trim("qtySold"), '')::numeric, 0)*coalesce(nullif(trim("sale"), '')::numeric, 0)
                    else 
                        null 
                    end warranty_partsale  
                    FROM parts_detail_etl prt
                    WHERE coalesce(nullif(trim("qtySold"), '')::numeric, 0) <> 0

            )
                ,repair_other AS (
                SELECT
                    "roNumber"                       AS ro_number,
                    CASE
                    WHEN "type" = 'M'
                        THEN 'MISC'
                    WHEN "type" = 'L'
                        THEN 'GOG'
                    WHEN "type" = 'S'
                        THEN 'SUBLET'
                    END                              AS item_type,
                    "type"    AS other_billing_type,
                    "cost"::numeric      AS other_cost,
                    "sale"::numeric      AS other_sale,
                    0                                AS misc_quantity
                FROM etl_mls                    
                union all
                    SELECT
                    "roNumber"                                   AS ro_number,
                    'DEDUCTIBLE'                                 AS item_type,
                    "laborType"                AS other_billing_type,
                    "actualAmount"::numeric AS other_cost,
                    "actualAmount"::numeric AS other_sale,
                    0                                AS misc_quantity
                FROM etl_deductible                    
                union all
                    SELECT 
                po.ro_number, 
                po.item_type,
                po.other_billing_type,
                po.other_cost,
                po.other_sale,
                CASE
                WHEN po.other_flag = 'P'
                THEN  epd."qtySold"::numeric
                ELSE NULL
                END                              AS misc_quantity 
            FROM ( SELECT
                    "roNumber"                       AS ro_number,
                    CASE
                    WHEN "type" = 'M'
                        THEN 'MISC'
                    WHEN "type" = 'L'
                        THEN 'GOG'
                    WHEN "type" = 'S'
                        THEN 'SUBLET'
                    END                              AS item_type,
                    "laborType"    AS other_billing_type,
                    "cost"::numeric      AS other_cost,
                    "sale"::numeric      AS other_sale,
                    "LoporPartSeqNo",
                    "lopOrPartFlag"      AS other_flag
                FROM etl_fees)po
                        LEFT JOIN etl_parts epd
                    ON po.ro_number = epd."roNumber" 
                        AND po."LoporPartSeqNo" = epd."sequenceNo"    
                    union all 
                    SELECT
                    "roNumber"          AS ro_number,
                    'TAX'               AS item_type,
                    "payType"        AS other_billing_type,
                    null                AS other_cost,          
                    "roTax"::numeric AS other_sale,
                    0                   AS misc_quantity
                FROM etl_total
                WHERE "roTax"::numeric != 0
                union all 
                SELECT
                    "roNumber"                    AS ro_number,
                    'FEE'                         AS item_type,
                    "payType"                  AS other_billing_type,
                    "shopChargeCost"::numeric  AS other_cost,
                    "shopChargeSale"::numeric  AS other_sale,
                    0                             AS misc_quantity
                FROM etl_total
                WHERE "shopChargeSale"::numeric != 0 
                )                
            ,job_qry AS (
                SELECT 
                    ro_number,
                    COALESCE(SUM(customer_lbrcost), 0) AS customer_lbrcost,
                    COALESCE(SUM(customer_lbrsale), 0) AS customer_lbrsale,
                    COALESCE(SUM(internal_lbrcost), 0) AS internal_lbrcost,
                    COALESCE(SUM(internal_lbrsale), 0) AS internal_lbrsale,
                    COALESCE(SUM(warranty_lbrcost), 0) AS warranty_lbrcost,
                    COALESCE(SUM(warranty_lbrsale), 0) AS warranty_lbrsale
                FROM job_qry_cat
                GROUP BY ro_number)
            ,part_qry AS (
                    SELECT ro_number1 ,sum(customer_partcost) AS customer_partcost,sum(customer_partsale) AS customer_partsale,
                    sum(internal_partcost) AS internal_partcost,sum(internal_partsale) AS Internal_partsale,sum(warranty_partcost) AS warranty_partcost,
                    sum(warranty_partsale) AS warranty_partsale FROM part_qry_cat group by ro_number1
            )
            ,job_part_qry AS (
                SELECT  *   FROM job_qry jQ
                FULL OUTER JOIN part_qry pq 
            on jq.ro_number =pq.ro_number1 
            -- and jq.LABOR_BILL_TYPE=pq.PART_BILL_TYPE
            )
            ,other_cost_qry as (
                    SELECT ro.ro_number,
                CASE
                    WHEN roth.item_type='MISC' and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C')  THEN
                    other_cost
                    ELSE
                    NULL
                END AS cust_misc_cost,
                CASE
                    WHEN roth.item_type='MISC' and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I')  THEN
                    other_cost
                    ELSE
                    NULL
                END AS internal_misc_cost,    
                    CASE 
                    WHEN roth.item_type='MISC' and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W')  THEN
                    other_cost
                    ELSE
                    NULL
                END AS warranty_misc_cost,    
                CASE
                WHEN roth.item_type='SUBLET' and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C')  THEN
                    other_cost
                ELSE
                    NULL
                END AS cust_sublet_cost,
                CASE
                WHEN roth.item_type='SUBLET' and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I')  THEN
                    other_cost
                ELSE
                    NULL
                END AS internal_sublet_cost,
                CASE
                WHEN roth.item_type='SUBLET' and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W')  THEN
                    other_cost
                ELSE
                    NULL
                END AS warranty_sublet_cost,
                CASE
                WHEN roth.item_type='TAX' and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C') THEN
                    other_cost
                ELSE
                    NULL
                END AS cust_tax_cost,
                CASE
                WHEN roth.item_type='TAX' and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I') THEN
                    other_cost
                ELSE
                    NULL
                END AS internal_tax_cost,
                CASE
                WHEN roth.item_type='TAX' and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W') THEN
                    other_cost
                ELSE
                    NULL
                END AS warranty_tax_cost,
                CASE
                WHEN roth.item_type='GOG' and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C') THEN
                    other_cost
                ELSE
                    NULL
                END AS cust_GOG_cost,
                CASE
                WHEN roth.item_type='GOG' and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I') THEN
                    other_cost
                ELSE
                    NULL
                END AS internal_GOG_cost,
                CASE
                WHEN roth.item_type='GOG' and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W') THEN
                    other_cost
                ELSE
                    NULL
                END AS warranty_GOG_cost,               
                CASE
                WHEN roth.item_type not in ('MISC','SUBLET','TAX','GOG','DEDUCTIBLE') and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C')  THEN
                    other_cost
                ELSE
                    NULL
                END AS cust_othcost,
                CASE
                WHEN roth.item_type not in ('MISC','SUBLET','TAX','GOG','DEDUCTIBLE') and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I')  THEN
                    other_cost
                ELSE
                    NULL
                END AS internal_othcost,
                CASE
                WHEN roth.item_type not in ('MISC','SUBLET','TAX','GOG','DEDUCTIBLE') and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W')  THEN
                    other_cost
                ELSE
                    NULL
                END AS warranty_othcost,    
                CASE
                WHEN roth.item_type='MISC' and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C')  THEN
                    other_sale
                ELSE
                    NULL
                END AS cust_misc_sale,
                CASE
                WHEN roth.item_type='MISC' and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I')  THEN
                    other_sale
                ELSE
                    NULL
                END AS internal_misc_sale,
                CASE
                WHEN roth.item_type='MISC' and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W')  THEN
                    other_sale
                ELSE
                    NULL
                END AS warranty_misc_sale,    
                CASE
                WHEN roth.item_type='SUBLET' and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C') THEN
                    other_sale
                ELSE
                    NULL
                END AS cust_sublet_sale,
                CASE
                WHEN roth.item_type='SUBLET' and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I') THEN
                    other_sale
                ELSE
                    NULL
                END AS internal_sublet_sale,
                CASE
                WHEN roth.item_type='SUBLET' and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W') THEN
                    other_sale
                ELSE
                    NULL
                END AS warranty_sublet_sale,
                CASE
                WHEN roth.item_type='TAX' and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C') THEN
                    other_sale
                ELSE
                    NULL
                END AS cust_tax_sale,
                CASE
                WHEN roth.item_type='TAX' and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I') THEN
                    other_sale
                ELSE
                    NULL
                END AS internal_tax_sale,    
                CASE
                WHEN roth.item_type='TAX' and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W') THEN
                    other_sale
                ELSE
                    NULL
                END AS warranty_tax_sale,  
                CASE
                WHEN roth.item_type='GOG' and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C') THEN
                    other_sale
                ELSE
                    NULL
                END AS cust_GOG_sale,
                CASE
                WHEN roth.item_type='GOG' and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I') THEN
                    other_sale
                ELSE
                    NULL
                END AS internal_GOG_sale,
                CASE
                WHEN roth.item_type='GOG' and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W') THEN
                    other_sale
                ELSE
                    NULL
                END AS warranty_GOG_sale,   
                CASE
                WHEN roth.item_type='DEDUCTIBLE' and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C') THEN
                    other_sale
                ELSE
                    NULL
                END AS cust_deduct_sale,
                CASE
                WHEN roth.item_type='DEDUCTIBLE' and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I') THEN
                    other_sale
                ELSE
                    NULL
                END AS internal_deduct_sale, 
                CASE
                WHEN roth.item_type='DEDUCTIBLE' and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W') THEN
                    other_sale
                ELSE
                    NULL
                END AS warranty_deduct_sale,    
                
                CASE
                WHEN roth.item_type not in ('MISC','SUBLET','TAX','GOG','DEDUCTIBLE') and  (roth.other_billing_type like 'C%' or roth.other_billing_type='C') THEN
                    other_sale
                ELSE
                    NULL
                END AS cust_othsale,
                CASE
                WHEN roth.item_type not in ('MISC','SUBLET','TAX','GOG','DEDUCTIBLE') and  (roth.other_billing_type like 'I%' or roth.other_billing_type='I') THEN
                    other_sale
                ELSE
                    NULL
                END AS internal_othsale,  
                CASE
                WHEN roth.item_type not in ('MISC','SUBLET','TAX','GOG','DEDUCTIBLE') and  (roth.other_billing_type like 'W%' or roth.other_billing_type='W') THEN
                    other_sale
                ELSE
                    NULL
                END AS warranty_othsale
                FROM repair_order ro 
                LEFT OUTER JOIN repair_other roth
                on ro.ro_number =roth.ro_number   )
            ,other_cost_qry_tot AS (
                SELECT   ro_number,sum(cust_misc_cost) AS cust_misc_cost,sum(cust_misc_sale) AS cust_misc_sale,sum(internal_misc_cost) AS internal_misc_cost,sum(internal_misc_sale) AS internal_misc_sale,sum(warranty_misc_cost) AS warranty_misc_cost,sum(warranty_misc_sale) AS warranty_misc_sale,sum(cust_sublet_cost) AS cust_sublet_cost,sum(cust_sublet_sale) AS cust_sublet_sale,sum(internal_sublet_cost) AS internal_sublet_cost,sum(internal_sublet_sale) AS internal_sublet_sale,sum(warranty_sublet_cost) AS warranty_sublet_cost,sum(warranty_sublet_sale) AS warranty_sublet_sale,sum(cust_tax_cost) AS cust_tax_cost,sum(cust_tax_sale) AS cust_tax_sale,sum(internal_tax_cost) AS internal_tax_cost,sum(internal_tax_sale) AS internal_tax_sale,sum(warranty_tax_cost) AS warranty_tax_cost,sum(warranty_tax_sale) AS warranty_tax_sale,sum(cust_GOG_cost) AS cust_gog_cost,sum(cust_GOG_sale) AS cust_GOG_sale,sum(internal_GOG_cost) AS internal_gog_cost,sum(internal_GOG_sale) AS internal_gog_sale,sum(warranty_GOG_cost) AS Warranty_gog_cost,sum(Warranty_GOG_sale) AS warranty_gog_sale,sum(cust_deduct_sale) AS cust_deduct_sale,sum(internal_deduct_sale) AS internal_deduct_sale,sum(warranty_deduct_sale) AS warranty_deduct_sale,sum(Cust_othcost) AS cust_othcost,sum(cust_othsale) AS cust_othsale,sum(Internal_othcost) AS internal_othcost,sum(Internal_othsale) AS internal_othsale,sum(warranty_othcost) AS warranty_othcost,sum(warranty_othsale) AS warranty_othsale from other_cost_qry
                GROUP BY ro_number  ORDER BY ro_number
                )
            SELECT COALESCE(jpq.ro_number, jpq.ro_number1) as ro_number,jpq.customer_lbrcost,jpq.customer_lbrsale,jpq.internal_lbrcost,jpq.internal_lbrsale,
            jpq.warranty_lbrcost,jpq.warranty_lbrsale,jpq.customer_partcost,jpq.customer_partsale,jpq.internal_partcost,
            jpq.internal_partsale,jpq.warranty_partcost,jpq.warranty_partsale,ocq.cust_misc_cost,ocq.cust_misc_sale,
            ocq.internal_misc_cost,ocq.internal_misc_sale,ocq.warranty_misc_cost,ocq.warranty_misc_sale,ocq.cust_sublet_cost,
            ocq.cust_sublet_sale,ocq.internal_sublet_cost,ocq.Internal_sublet_sale,ocq.warranty_sublet_cost,ocq.warranty_sublet_sale,
            ocq.Cust_tax_cost,ocq.cust_tax_sale,ocq.Internal_tax_cost,ocq.Internal_tax_sale,ocq.warranty_tax_cost,ocq.warranty_tax_sale,
            ocq.cust_GOG_cost,ocq.cust_GOG_sale,ocq.Internal_GOG_cost,ocq.Internal_GOG_sale,ocq.warranty_GOG_cost,ocq.Warranty_GOG_sale,
            ocq.cust_deduct_sale,ocq.internal_deduct_sale,ocq.warranty_deduct_sale,ocq.cust_othcost,ocq.cust_othsale,ocq.Internal_othcost,
            ocq.Internal_othsale,ocq.warranty_othcost,ocq.warranty_othsale,dq.customer_discount,dq.internal_discount,dq.warranty_discount 
            FROM job_part_qry jpq
            LEFT OUTER JOIN other_cost_qry_tot ocq 
            on COALESCE(jpq.ro_number, jpq.ro_number1)=ocq.ro_number
            LEFT OUTER JOIN discount_qry dq 
            on COALESCE(jpq.ro_number, jpq.ro_number1)=dq.ro_number;

CREATE  TABLE gl_account_exception_report as
WITH recursive account_det AS (
-------------------------------initial data for recursion--------------------------------------------------------------------
	 			SELECT pa.ro_number, pa.account_number, pa.sale, pa.cost, pa.account_type,
				 	COUNT(pa.account_number) OVER(PARTITION 
                        BY pa.ro_number) AS accnumberlength, 
                    ROW_NUMBER() OVER(PARTITION BY pa.ro_number ORDER BY pa.ro_number, pa.account_number  DESC) AS row_no
  				 FROM proxy_accounting pa 
				 WHERE pa.account_type NOT IN ('S', 'C','L','E', 'X', 'I') 
                 )
,account_details AS (
	 			SELECT  pa.ro_number, pa.account_number, pa.sale, pa.cost, pa.account_type,
				 		pa.accnumberlength
  				 FROM account_det pa 
				 WHERE pa.row_no <= 5 )
,pay_summary AS (
 SELECT "roNumber"::text, SUM("paymentAmount"::numeric) AS "paymentAmount"
    FROM etl_pay
    WHERE "insuranceFlag" = 'N'
    GROUP BY "roNumber"
),sumpay AS (
	SELECT "referenceNo" AS "ro_num", SUM(("postingAmount")::numeric) FROM etl_accounts
	GROUP BY "referenceNo"
)
, coa AS (
    SELECT
        (ARRAY_AGG("accountType"))[1]               AS "accountType",
        STRING_AGG("accountDesc", ', ')  AS "accountDesc",
        "accountNumber"                             AS "accountNumber",
        (ARRAY_AGG("chain"))[1]                     AS "chain"
    FROM etl_account_coa GROUP BY "accountNumber"
)
, etl_accts AS (
    SELECT
        d."hostItemId"          AS "hostItemId",
    	d."accountingDate"      AS "accountingDate",
	    d."accountNumber"       AS "accountNumber",
	    d."companyId"           AS "companyId",
	    d."controlNo"             AS "controlNo",
	    d."controlNo2"            AS "controlNo2",
	    d."controlTypeCode"         AS "controlTypeCode",
	    d."controlType2Code"        AS "controlType2Code",
	    d."currentMonth"        AS "currentMonth",
	    d."detailDescription"   AS "detailDescription",
	    d."distillControlTypeCode"  AS "distillControlTypeCode",
	    d."distillControlType2Code" AS "distillControlType2Code",
	    d."journalId"           AS "journalId",
	    d."postingAmount"       AS "postingAmount",
	    d."postingSequence"     AS "postingSequence",
	    d."postingTime"         AS "postingTime",
	    d."productivityNo"              AS "productivityNo",
	    d."productivityNo2"             AS "productivityNo2",
	    d."productivityNoType"            AS "productivityNoType",
	    d."productivityNoType2"           AS "productivityNoType2",
	    d."referenceNo"               AS "referenceNo",
	    d."scheduleNumber"      AS "scheduleNumber",
	    d."statCount"           AS "statCount",
	    coa."accountType"
	FROM etl_accounts d
        JOIN coa
            ON (d."accountNumber"::text) = (coa."accountNumber")
	--WHERE replace((d."referenceNo"::json -> '_text'::text)::text, '"'::text, ''::text) = '117286' --'457014'--'457012'--'457023' --'456989'
)
, etl_accts_window1 AS (
    SELECT d."hostItemId",
    	d."accountingDate",
	    d."accountNumber",
	    d."companyId",
	    d."controlNo",
	    d."controlNo2",
	    d."controlTypeCode",
	    d."controlType2Code",
	    d."currentMonth",
	    d."detailDescription",
	    d."distillControlTypeCode",
	    d."distillControlType2Code",
	    d."journalId",
	    d."postingAmount",
	    d."postingSequence",
	    d."postingTime",
	    d."productivityNo",
	    d."productivityNo2",
	    d."productivityNoType",
	    d."productivityNoType2",
	    d."referenceNo",
	    d."scheduleNumber",
	    d."statCount",
	    d."accountType",
	    COUNT(*) OVER(Partition By d."referenceNo", d."postingTime")                       AS posting_time_count,
	    bool_or(d."accountType" = 'S')
	        OVER(Partition By d."referenceNo", d."postingTime")                            AS any_sale_in_posting,
	    array_agg(CASE WHEN (d."accountType" = 'S' AND to_number("postingAmount",'**********.99') != 0)
	                       OR d."accountType" != 'S'
	                   THEN "accountNumber" END)
	        OVER(Partition By d."referenceNo", d."postingTime")                            AS acct_no_list_of_posting,
	     SUM("postingAmount"::numeric) 
            OVER(Partition By d."referenceNo", d."accountNumber", d."postingTime")         AS account_posting_amount
	FROM etl_accts d
        
)
,etl_accts_window2 AS (
    SELECT *,
    bool_or(ps."roNumber" IS NOT NULL)
	    OVER(Partition By d."referenceNo", d."postingTime")                  AS have_matching_pay_payment
    FROM etl_accts_window1 d
        LEFT JOIN pay_summary ps
            ON ps."roNumber" = d."referenceNo"
               AND ps."paymentAmount" = d.account_posting_amount
               AND d."accountType" = 'A'
)
, matching_payments AS (
	SELECT *, 
        rank() OVER (Partition BY "referenceNo" ORDER BY any_sale_in_posting DESC, "postingTime") AS rn
	FROM etl_accts_window2
	WHERE have_matching_pay_payment
)
-- , matching_posting_ros as (
--   SELECT MAX("postingTime") AS mp_posting, "referenceNo" AS mp_ro 
--   FROM matching_payments GROUP BY "referenceNo"
-- )
, matching_payment_posting AS (
	SELECT m.*
	FROM matching_payments m 
    WHERE rn = 1
	--JOIN matching_posting_ros mr ON "referenceNo" = mp_ro
	--WHERE "postingTime" = mp_posting
)
, sale_acct_of_matching_posting AS (
    SELECT
        "referenceNo",
        array_agg("accountNumber")
            FILTER(WHERE "accountType" = 'S') AS sale_acct
    FROM matching_payment_posting
    GROUP BY "referenceNo"
)
, unmatch_pay_selectable_posting AS (
    SELECT acct.*, 1
    FROM etl_accts_window2 acct
        LEFT JOIN sale_acct_of_matching_posting mp USING ("referenceNo")
    WHERE NOT have_matching_pay_payment
        AND (mp."referenceNo" IS NULL OR any_sale_in_posting)
        AND (mp."referenceNo" IS NULL OR posting_time_count > 2)
        AND (mp.sale_acct IS NULL OR NOT (acct.acct_no_list_of_posting && mp.sale_acct))
        AND to_number(acct."postingAmount",'**********.99') != 0
)
, selectable_postings AS (
	SELECT * FROM matching_payment_posting
	WHERE ("accountType" = 'A' AND account_posting_amount > 0)
	    OR "accountType" != 'A'
	UNION ALL
	SELECT * FROM unmatch_pay_selectable_posting
	WHERE ("accountType" = 'A' AND account_posting_amount > 0)
	    OR "accountType" != 'A'
)
, acc_details AS (
    SELECT
        d."referenceNo"                                               AS "RO#",
        d."accountNumber"                                       AS "Account#",
        d."accountType"                                         AS "Acc.Type(COA)",
        SUM(to_number(d."postingAmount",'**********.99'))::text AS "postingAmount",
        d."postingTime"
    FROM selectable_postings d
    GROUP BY d."referenceNo", d."accountNumber", d."accountType", d."postingTime"
) 

, gl_data AS ( 
    SELECT DISTINCT
        coalesce("referenceNo", split_part("hostItemId", '*', 3)) AS ro_number,
        "accountNumber"                                     AS account_number,
        "companyId"                                         AS company_id,
        "postingAmount"::numeric     						AS posting_amount,
        "postingTime"                                       AS posting_time
    FROM selectable_postings
)
, gl_data_with_posting_amount AS(
    SELECT 
        ro_number,
        account_number, 
        ABS(SUM(nullif((posting_amount)*100, 0))) AS posting_amount,
        company_id
    FROM gl_data GROUP BY ro_number, account_number, company_id
)
, coa_data AS ( 
    SELECT
        TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ', 'g'))             AS account_number,
        split_part(TRIM(regexp_replace("chain", E'[\\n\t\r]+', ' ', 'g')), '*', 2) AS chain,
        TRIM(regexp_replace("accountType", E'[\\n\t\r]+', ' ', 'g'))               AS account_type
    FROM coa
    WHERE (coa."accountDesc" NOT LIKE '%INV%' OR coa."accountDesc" LIKE '%CASH%')
          AND "accountDesc" NOT LIKE '%WORK-IN-PROCESS%' 
          AND "accountDesc" NOT LIKE '%WIP%' 
          AND  "accountDesc" NOT LIKE '%WORK IN PROCESS%'
    )
, coa_raw as( 
    SELECT 
        ro_number,
        account_number,
        account_type,
        sum(sale)                                                 AS sale,
        Cost_account_no,
        Cost_account_type,
        sum(cost)                                                 AS cost,
        company_id
    FROM (
    SELECT
              gd.ro_number,
              coalesce(gd1.account_number, gd.account_number)     AS account_number,
              CASE 
              WHEN gd1.account_number IS NULL AND cd.account_type='C'
                  THEN gd1.posting_amount
                  WHEN gd1.account_number IS NULL
                  THEN gd.posting_amount
              ELSE gd1.posting_amount END                         AS sale,
              case when gd.account_number=gd1.account_number is NULL
                  then null
                  else gd.account_number
                  END                                             AS Cost_account_no,
              CASE 
              WHEN coalesce(cd.chain, '0') = cd.account_number
                  THEN '0'
              ELSE CASE 
                    WHEN gd1.account_number IS NULL AND cd.account_type='C'
                       THEN gd.posting_amount
                       WHEN gd1.account_number IS NULL
                       THEN gd1.posting_amount
                   ELSE gd.posting_amount END
              END                                                 AS cost,
              CASE WHEN gd1.account_number IS NULL
                  THEN cd.account_type
                  when gd1.account_number IS not null and gd.account_number IS not null 
                  then 
                       cd1.account_type
              ELSE NULL END                                       AS account_type, 
              CASE WHEN gd1.account_number IS not NULL
                  THEN cd.account_type
               else null end                                      as  Cost_account_type,  
              gd.company_id 
          FROM coa_data cd
              JOIN gl_data_with_posting_amount gd USING (account_number)
              LEFT JOIN gl_data_with_posting_amount gd1 
                  ON cd.chain = gd1.account_number 
                      AND gd.ro_number = gd1.ro_number
                      AND gd1.account_number != cd.account_number
              LEFT JOIN coa_data cd1 
                  ON cd.chain = cd1.account_number 
                      AND gd1.ro_number IS NOT NULL
    ) t
    GROUP BY ro_number, account_number,Cost_account_no, account_type,Cost_account_type, company_id  
    ORDER BY CASE account_type
             WHEN 'S' 
                 THEN 1
             ELSE 2 END
)
, coa_final AS ( 
    SELECT c1.*
    FROM coa_raw c1
        JOIN coa_raw c2 USING (ro_number, account_number)
    WHERE c1.cost != c2.cost AND c1.cost = 0
)
-- SELECT * FROM coa_final
, printproxy AS (
SELECT coa_raw.ro_number, coa_raw.sale::text, coa_raw.cost::text, coa_raw.account_type
 FROM coa_raw
    LEFT JOIN coa_final USING (ro_number, account_number, cost)
 WHERE coa_final IS null
 )
,generate_finalressult AS (
  SELECT * FROM printproxy p LEFT JOIN pay_summary s ON p."ro_number" = s."roNumber" AND ("paymentAmount"::numeric * 100)::numeric = sale::numeric
 LEFT JOIN sumpay sp ON p."ro_number" = sp."ro_num"
  WHERE p.account_type = 'A'  
  UNION ALL 
  SELECT ro_number, null, null, null, null, null, null, null FROM printproxy p WHERE ro_number NOT IN (SELECT ro_number FROM printproxy p WHERE p.account_type = 'A') GROUP BY ro_number
 ORDER BY ro_number
 )
 ,exception_only AS (
 SELECT * FROM generate_finalressult WHERE ro_number IN (SELECT "roNumber" FROM pay_summary WHERE "paymentAmount"::numeric <>0) AND "paymentAmount" IS NOT NULL AND sum::numeric = 0 
 )
 ,excep_res AS (
 SELECT ro_number,string_agg(sale,',') as sale, sum(sale::numeric) as sum_sale, ps."paymentAmount", "sum" as mismatch FROM generate_finalressult g
 LEFT JOIN pay_summary ps ON ps."roNumber" = g.ro_number WHERE ro_number NOT IN (SELECT ro_number FROM exception_only)
 AND ps."paymentAmount"::numeric > 0 GROUP BY 1,4,5 ORDER BY ro_number
 ),
 	
				 
---------------------------recursion-----------------------------------------------------------------------------------------
 recur AS (     
  SELECT ad.ro_number,ad.account_number,
         ARRAY[ad.account_number::text] AS accounts, 
         ARRAY[COALESCE(ad.sale,ad.cost)::text] AS amounts, 
         COALESCE(ad.sale,ad.cost)::numeric AS sum
    FROM account_details ad
	 
  UNION ALL
	 
  SELECT ad.ro_number,ad.account_number,
         accounts || ad.account_number::text, 
         amounts || COALESCE(ad.sale,ad.cost)::text, 
         sum + COALESCE(ad.sale,ad.cost)::numeric
    FROM account_details ad
    JOIN recur r ON r.account_number < ad.account_number
	AND r.ro_number = ad.ro_number
    AND cardinality(accounts) <= accnumberlength
),----termination condition
---------------total amount calculation for checking------------------------------------------------------------
customerpay AS (
SELECT
    ro_number,
	ROUND((COALESCE(customer_lbrsale,0)::numeric + COALESCE(customer_partsale,0)::numeric + COALESCE(cust_misc_sale,0)::numeric + COALESCE(cust_sublet_sale,0)::numeric + 
	COALESCE(cust_tax_sale,0)::numeric + COALESCE(cust_gog_sale,0)::numeric + COALESCE(cust_deduct_sale,0)::numeric + COALESCE(cust_othsale,0)::numeric
    + COALESCE(warranty_deduct_sale,0)::numeric + COALESCE(internal_deduct_sale,0)::numeric + 
	COALESCE(customer_discount,0)::numeric)*100) As customer_amt 
FROM ro_total
)
,insurance AS (
    SELECT "roNumber" AS ronum, ROUND((SUM("paymentAmount"::numeric))*100) AS insu_amt FROM etl_pay WHERE "insuranceFlag" = 'Y' GROUP BY "roNumber"
)
,finalpay AS (
    SELECT ro_number, COALESCE(customer_amt::numeric,0) - COALESCE(insu_amt::numeric,0) AS final_amt  FROM customerpay LEFT JOIN insurance ON ro_number=ronum 
)
,totalpay as (
	SELECT ro_number,final_amt FROM finalpay WHERE final_amt > 0
 )

----------------------------- checking for combinations after recursion---------------------------------------------------------------	
,cte_cost_match AS (
	SELECT DISTINCT rt.ro_number,
					 CASE WHEN rt.final_amt = r.sum THEN true ELSE false END AS find_final_amount,
					 CASE WHEN (rt."paymentAmount") * 100 = r.sum THEN true ELSE false END AS find_payment_amount
	FROM (SELECT COALESCE(tp.ro_number,ps."roNumber") ro_number,
		  tp.final_amt,
		  ps."paymentAmount" 
		  FROM totalpay tp 
		  FULL OUTER JOIN pay_summary ps ON ps."roNumber" = tp.ro_number) rt
	LEFT JOIN recur r USING(ro_number)
),
---------------------------getting final distinct data------------------------------------------------------------------------------------
 cte_final AS(
	 SELECT DISTINCT ro_number,
	 				CASE WHEN bool_or(find_final_amount) THEN TRUE ELSE FALSE END AS find_final_amount,
	 				CASE WHEN bool_or(find_payment_amount) THEN TRUE ELSE FALSE END AS find_payment_amount	 
	 FROM cte_cost_match 
 	 GROUP BY ro_number
 )
,combined_union AS(
        SELECT DISTINCT coa_raw.ro_number, coa_raw.sale::text, coa_raw.cost::text, coa_raw.account_type, account_number, 'invalid_cost_anomaly' AS exception_type
         FROM coa_raw LEFT JOIN coa_final USING (ro_number, account_number, cost)
         WHERE coa_final IS null AND ((coa_raw.sale IS NULL OR coa_raw.sale = '0') AND  coa_raw.cost::numeric <> 0 ) AND coa_raw.account_type = 'C'
          AND coa_raw.ro_number IN (SELECT "roNumber" FROM pay_summary WHERE "paymentAmount"::numeric <>0)
        -- UNION ALL
        --       SELECT  DISTINCT "referenceNo", NULL, NULL, NULL, NULL, 'no_sale' AS exception_type FROM matching_payment_posting WHERE NOT any_sale_in_posting 
        --  AND "referenceNo" IN (SELECT "roNumber" FROM pay_summary WHERE "paymentAmount"::numeric <>0)
        UNION ALL
            SELECT excep_res.ro_number, excep_res.sale, excep_res.sum_sale::text, excep_res."paymentAmount"::text, excep_res.mismatch::text, 'customer_pay_exception' AS exception_type 
            FROM excep_res WHERE ((sum_sale::numeric != ("paymentAmount" * 100)::numeric) OR sum_sale IS NULL) AND  ro_number NOT IN (
            SELECT ro_number FROM cte_final WHERE find_payment_amount
            )
        UNION ALL
            SELECT ro_number, null,null,null,null,'amount_mismatch' FROM totalpay WHERE ro_number NOT IN (
             SELECT ro_number
		  FROM cte_final WHERE find_final_amount 
        ) AND final_amt::numeric <> 0 ORDER BY ro_number
)
,remove_noglros AS (
    SELECT * FROM combined_union WHERE ro_number NOT IN (
    SELECT "roNumber" FROM etl_head WHERE "closedDate" IS NULL
) 
)
SELECT * FROM remove_noglros WHERE ro_number IN (
    SELECT "referenceNo" FROM  etl_accts GROUP BY "referenceNo"
) ORDER BY ro_number ASC;


        \o 'invalid_cost_anomaly_detected.csv'
        COPY(SELECT 
                ro_number 
            FROM gl_account_exception_report 
            WHERE exception_type= 'invalid_cost_anomaly')
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );
        
        \o 'invalid_gl_with_no_sale_account.csv' 
        COPY(SELECT ro_number,
                sale,
                cost,
                account_type,
                account_number 
            FROM gl_account_exception_report 
            WHERE exception_type= 'no_sale')
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );
        
        \o 'customer_pay_exception.csv'
        COPY(SELECT *
            FROM gl_account_exception_report 
            WHERE exception_type= 'customer_pay_exception')
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

         \o '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/gl_account_exception_report.csv'
        COPY(SELECT *
            FROM gl_account_exception_report)
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

        \o '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/exception.csv'
        COPY(WITH exceptions_list AS (
            SELECT gl.ro_number,
                CASE WHEN gl.exception_type = 'invalid_cost_anomaly' THEN 1 ELSE 0 END AS cost_anomaly,
                CASE WHEN gl.exception_type = 'no_sale' THEN 1 ELSE 0 END AS no_sale,
                CASE WHEN gl.exception_type = 'customer_pay_exception' THEN 1 ELSE 0 END AS cust_pay,
                CASE WHEN gl.exception_type = 'amount_mismatch' THEN 1 ELSE 0 END AS amount_mismatch
                FROM gl_account_exception_report gl JOIN etl_head
                ON "roNumber" = gl.ro_number WHERE "closedDate" IS NOT NULL 
        )
        ,exceptions AS (
          SELECT ro_number, SUM(cust_pay) AS cust_pay,SUM(no_sale) AS no_sale, SUM(cost_anomaly) AS cost_anomaly, SUM(amount_mismatch) AS amount_mismatch FROM exceptions_list GROUP BY ro_number
        )
        ,json_documents AS (
            SELECT REPLACE(REPLACE("RONumber", './', ''), '.json', '') AS ro_number,json_document FROM etl_json
        )
        select exceptions.ro_number,
        CASE WHEN cust_pay > 0 THEN true ELSE false END AS cust_pay,
        CASE WHEN no_sale > 0 THEN true ELSE false END AS no_sale,
        CASE WHEN cost_anomaly > 0 THEN true ELSE false END AS cost_anomaly,
        CASE WHEN amount_mismatch > 0 THEN true ELSE false END AS amount_mismatch,
        jd.json_document, null as text_proxy from exceptions
        LEFT JOIN json_documents jd ON exceptions.ro_number = jd.ro_number)
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

        \o 'paymentcode_summary_report.csv'
        COPY(WITH summary_gl AS (
                SELECT 
                    "code" As paycode , 
                    "roNumber",   COUNT(*) OVER(Partition By "roNumber") AS multiple
                FROM etl_pay 
                GROUP BY "roNumber", "code"  
                ORDER BY "roNumber"
                )
            ,grouped_summary AS (
  	            SELECT 
                    paycode,
  	                COUNT("multiple")
                    FILTER(WHERE "multiple" > 1) AS count_multi,
	                COUNT("multiple")
                    FILTER(WHERE "multiple" = 1) AS count_single FROM summary_gl 
                    GROUP BY paycode
            )
            ,ro_pay AS (
                SELECT 
                    "code",
	                COUNT("insuranceFlag")
                    FILTER(WHERE "insuranceFlag" = 'Y') AS count_y,
	                COUNT("insuranceFlag")
                    FILTER(WHERE "insuranceFlag" = 'N') AS count_n         
                FROM  etl_pay  
                GROUP BY "code"
            )
            SELECT "code" AS "Payment Code",
                count_y AS "Less Insurance Yes",
                count_n AS "Less Insurance No",
                count_multi AS "Comes in multiple payment",
                count_single AS "Comes in single payment"
                FROM ro_pay r JOIN grouped_summary g ON r."code" = g.paycode
                ORDER BY count_y DESC) 
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );
COMMIT;
