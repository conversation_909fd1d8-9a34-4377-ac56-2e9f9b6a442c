/*Generate Data for DU-Jobs loading*/

INSERT INTO :SRC_SCHEMA.client_invoicedetails (
    invoicenumber, open_date, close_date,
    vehicle_year, vehicle_make, vehicle_model, vin,
    customer_id,customer_name, advisor_id, mileage, deptname, comment)
    SELECT eh."roNumber",
           "openDate"::date,
           "closedDate"::date,
           "year",
           UPPER(COALESCE("makeDesc", "make", 'MISSING')),
           UPPER(COALESCE("modelDesc", "model")),
           COALESCE("vin", 'Missing'),
           COALESCE("customerId", 'N/A'),
           "name1",
           "serviceAdvisor",
           "mileage",
           'NA',
           TRIM(COALESCE("comments",'')||' '||"remarks")
      FROM :IMPORT_SCHEMA.etl_head eh
      LEFT JOIN :IMPORT_SCHEMA.etl_vehicle ev ON (eh."roNumber"=ev."roNumber")
    --   LEFT JOIN make_lookup ON (make_code = "Make")
     ORDER BY 1;

INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
 ro_line, paytype, labor_discount, labor_discount_percent, part_discount, part_discount_percent, total_discount, summary, need_estimation)
     WITH disctable AS (
        SELECT "roNumber" AS ro,
               'AMOUNT' AS d_type,
               "level" AS "level",
               "lineCode" AS "lineCode",
               "classOrType" AS "classOrType",
               "laborDiscount" AS "laborDiscount",
               NULL AS labor_d_percent,
               "partsDiscount" AS "partsDiscount",
               NULL AS part_d_percent,
               "totalDiscount" AS "totalDiscount",
               "desc" AS "desc"
        FROM :IMPORT_SCHEMA.etl_discount
        ),discprocessed AS (
        SELECT ro AS ro,
               d_type AS d_type,
               "level" AS "level",
               "lineCode" AS "lineCode",
               "classOrType"  AS "classOrType",
               coalesce("laborDiscount"::text, '0') AS "laborDiscount",
               NULL AS labor_d_percent,
               coalesce("partsDiscount"::text, '0') AS "partsDiscount",
               NULL AS part_d_percent,
               "totalDiscount" AS "totalDiscount",
               "desc" AS "desc"
        FROM disctable
        ), combinedro AS (   
        SELECT ro,
               d_type :: text,
               CASE WHEN "level" = 'LINE' 
                    THEN 'JOB'
                    WHEN "level" = 'LOP' 
                    THEN 'JOB' 
               ELSE "level" END AS disclevel,
               CASE WHEN "lineCode" = 'RO'
                   THEN NULL
               ELSE "lineCode"||'1' END AS dislinecode,
               "classOrType"  AS paytype,
               ABS("laborDiscount"::numeric),
               labor_d_percent :: text,
               NULL,
               NULL,
               ABS("laborDiscount"::numeric),
               "desc"||'-Labor',
               UPPER(COALESCE("desc", '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE' 
            FROM discprocessed 
                WHERE  ABS("laborDiscount"::numeric) <> 0 AND ABS("partsDiscount"::numeric) <> 0
                UNION ALL 
        SELECT ro,
               d_type :: text,
               CASE WHEN "level" = 'LINE' 
                    THEN 'JOB'
                    WHEN "level" = 'LOP' 
                    THEN 'JOB' 
               ELSE "level" END AS disclevel,
               CASE WHEN "lineCode" = 'RO'
                   THEN NULL
               ELSE "lineCode"||'1' END AS dislinecode,
               "classOrType"  AS paytype,
               NULL,
               NULL,
               ABS("partsDiscount"::numeric),
               part_d_percent :: text,
               ABS("partsDiscount"::numeric),
               "desc"||'-Parts',
               UPPER(COALESCE("desc", '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE' 
            FROM discprocessed 
                WHERE  ABS("partsDiscount"::numeric) <> 0 AND ABS("laborDiscount"::numeric) <> 0
                ORDER BY ro
             )
            SELECT * FROM combinedro
             UNION ALL
            SELECT ro,
               d_type :: text,
               CASE WHEN "level" = 'LINE' 
                    THEN 'JOB'
                    WHEN "level" = 'LOP' 
                    THEN 'JOB' 
               ELSE "level" END AS disclevel,
               CASE WHEN "lineCode" = 'RO'
                   THEN NULL
               ELSE "lineCode"||'1' END AS dislinecode,
               "classOrType"  AS paytype,
               ABS("laborDiscount"::numeric),
               labor_d_percent :: text,
               ABS("partsDiscount"::numeric),
               part_d_percent :: text,
               ABS("totalDiscount"::numeric),
               "desc",
               UPPER(COALESCE("desc", '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE'  
            FROM discprocessed 
                WHERE (COALESCE("partsDiscount"::NUMERIC, 0) = 0 OR COALESCE("laborDiscount"::NUMERIC, 0) = 0)
                AND (COALESCE("partsDiscount"::numeric, 0) + COALESCE("laborDiscount"::numeric, 0) ) = 0
                ORDER BY ro;

INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, labor_discount, part_discount, total_discount, summary, need_estimation)
    WITH discount AS (
        SELECT
            "roNumber"                                                      AS ro,
            'AMOUNT'::text                                                       AS d_type,
            "type"       AS "disLevel",
            "lineCode"   AS "disLineCode",
            sale::numeric       AS "amount",
            "opCodeDesc" AS "disDesc"
        FROM :IMPORT_SCHEMA.etl_fees
        
        ),final_disc AS (
        	SELECT ro, d_type, 
        	"disLevel", "disLineCode",
        	SUM("amount") AS "disTotalDiscount",
        	 "disDesc"
        	FROM discount GROUP BY 1,2,3,4,6
        )
    SELECT
        ro,
        d_type::text,
        CASE WHEN "disLineCode" = 'RO'
            THEN 'RO'
        ELSE 'JOB' END,
        CASE WHEN "disLineCode" = 'RO'
            THEN NULL
        ELSE "disLineCode"||'1' END,
        NULL,
        NULL,
        ABS("disTotalDiscount" :: numeric),
        "disDesc",
        UPPER(COALESCE("disDesc", '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE' 
    FROM final_disc
    WHERE "disTotalDiscount" :: numeric < 0;

INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, labor_discount, part_discount, total_discount, summary, need_estimation)
       WITH discount AS (
        SELECT
            "roNumber"                                                      AS ro,
            'AMOUNT'::text                                                       AS d_type,
            "type"       AS "disLevel",
            "lineCode"   AS "disLineCode",
            sale::numeric       AS "amount",
            "opCodeDesc" AS "disDesc"
        FROM :IMPORT_SCHEMA.etl_mls
        
        )
        ,final_disc AS (
        	SELECT ro, d_type, 
        	"disLevel", "disLineCode",
        	SUM("amount") AS "disTotalDiscount",
        	 "disDesc"
        	FROM discount GROUP BY 1,2,3,4,6
        )
    SELECT
        ro,
        d_type::text,
        CASE WHEN "disLineCode" = 'RO'
            THEN 'RO'
        ELSE 'JOB' END,
        CASE WHEN "disLineCode" = 'RO'
            THEN NULL
        ELSE "disLineCode"||'1' END,
        NULL,
        NULL,
        ABS("disTotalDiscount" :: numeric),
        "disDesc",
        UPPER(COALESCE("disDesc", '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE' 
    FROM final_disc
    WHERE "disTotalDiscount" :: numeric < 0;

INSERT INTO  :SRC_SCHEMA.client_gl_accounting (invoicenumber, hostitemid, accountingdate,
				   accountnumber, accountdescription, accounttype,
				   incbalgrpdesc, incbalsubgrp, companyid,
    				   control, journalid, postingamount, postingsequence, postingtime, paytype, insuranceamount)
 WITH coas AS (SELECT
                         TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                             'gi')) AS accountnumber,
                         TRIM(regexp_replace("accountDesc", E'[\\n\t\r]+', ' ',
                                             'gi')) AS accountdescription,
                         TRIM(regexp_replace("accountType", E'[\\n\t\r]+', ' ',
                                             'gi')) AS accounttype,
                         TRIM(regexp_replace("incBalReportGrpDesc", E'[\\n\t\r]+', ' ',
                                             'gi')) AS incbalgrpdesc,
                         TRIM(regexp_replace("incBalReportGrp", E'[\\n\t\r]+', ' ',
                                             'gi')) AS incbalsubgrp
                     FROM :IMPORT_SCHEMA.etl_account_coa
	 ),
         gls AS (SELECT
                           TRIM(regexp_replace("hostItemId", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS hostitemid,
                           TRIM(regexp_replace("accountingDate", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS accountingdate,
                           TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS accountnumber,
                           TRIM(regexp_replace("referenceNo", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS invoice,
                           TRIM(regexp_replace("companyId", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS companyid,
                           TRIM(regexp_replace("controlNo", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS control,
                           TRIM(regexp_replace("journalId", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS journalid,
                           TRIM(regexp_replace("postingAmount", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS postingamount,
                           TRIM(regexp_replace("postingSequence", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS postingsequence,
                           TRIM(regexp_replace("postingTime", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS postingtime,
                           TRIM(regexp_replace("referenceNo", E'[\\n\t\r]+', ' ',
                                               'gi'))                            AS refer
                       FROM :IMPORT_SCHEMA.etl_accounts
    	),
	cte_insuarance AS (SELECT
		               "roNumber",
		               SUM(duration :: decimal)         AS "Total Punch Duration",
		               STRING_AGG(DISTINCT "technicianId", ',') AS "Tech ID"
		           FROM :IMPORT_SCHEMA.etl_tech_punch
		           GROUP BY "roNumber"
	),
	less_insurance AS (SELECT
		               lbr."roNumber"                                       AS "roNumber",
		               lbr."Pay Type"                                        AS "Pay Type",
		               SUM(pay."paymentAmount" :: numeric)
		                   FILTER (WHERE pay."insuranceFlag" :: boolean) AS "Total Insurance Amount"
		           FROM (
		                    SELECT
		                        "roNumber",
		                        STRING_AGG(DISTINCT "type", ', ') AS "Pay Type"
		                    FROM :IMPORT_SCHEMA.etl_labor
		                    GROUP BY "roNumber"
		                    ORDER BY "roNumber") lbr
		               LEFT JOIN :IMPORT_SCHEMA.etl_pay pay USING ("roNumber")
		               LEFT JOIN :IMPORT_SCHEMA.etl_head hd USING ("roNumber")
                    LEFT JOIN :IMPORT_SCHEMA.etl_vehicle ev USING ("roNumber")
		               LEFT JOIN cte_insuarance AS cte USING ("roNumber")
		           GROUP BY "roNumber", lbr."Pay Type", ev.make, cte."Total Punch Duration", cte."Tech ID"
		           ORDER BY "roNumber"
	)
	SELECT
    	    invoice AS "#Invoice",
            hostitemid AS "Host Item ID",
    	    accountingdate AS "Accounting Date",
    	    accountnumber AS "Account Number",
    	    accountdescription AS "Acccount Description",
    	    accounttype AS "Account Type",
    	    incbalgrpdesc AS "incBalReportGrpDesc",
    	    incbalsubgrp AS "incBalReportGrp",
    	    companyid AS "Company ID",
    	    control AS "controlNo",
    	    journalid AS "Journal ID",
    	    postingamount AS "Posting Amount",
    	    postingsequence AS "Posting Sequence",
    	    postingtime AS "Posting Time",
            "Pay Type",
            "Total Insurance Amount"
        FROM gls
    		LEFT JOIN coas USING (accountnumber)
                LEFT JOIN less_insurance ON invoice = "roNumber" WHERE invoice IS NOT NULL;
   

-- INSERT INTO :SRC_SCHEMA.client_labordetails (ldid, invoicenumber, jobid,
--                                 department, base_paytype, base_paytype_suffix,
--                                 opcode, opdescription,
--                                 unit_hours, unit_sale, extended_sale
--                                 )
--      SELECT
--     row_number() OVER (ORDER BY "roNumber", "lineCode" ) AS row_number,
--     "roNumber",
--     jobid,
--     department,
--     base_paytype,
--     base_paytype_suffix,
--     opcode,
--     opdescription,
--     unit_hours,
--     unit_sale,
--     extended_sale
-- FROM ( SELECT  
--            "roNumber",
--            "lineCode",
--            "lineCode"|| row_number() OVER (PARTITION BY "roNumber", "lineCode" 
--                   ORDER BY "sale"::numeric > 0 DESC, "sequenceNo")                     AS jobid,
--            '' AS department,
--            "type"     AS base_paytype,
--            CASE WHEN "opCode" IS NULL THEN 'O'
--                        ELSE NULL END     AS base_paytype_suffix,
--            upper(COALESCE("opCode", 'NO-OP'))   AS opcode,
--            COALESCE(regexp_replace(regexp_replace(regexp_replace(
--                                                          regexp_replace("opCodeDesc", 'CUSTOMER STATES.',
--                                                           'C/S '), 'CUSTOMER REQUESTS.',
--                                                           'C/R '), 'CUSTOMER STATE.', '', 'gi'),
--                                                'CUSTOMER REQUEST.', '', 'gi'), 'OP: ' || "opCode")
--                                                                                              AS opdescription,
--            CASE WHEN "soldHours"::numeric < 0 THEN 0 ELSE "soldHours" :: numeric END   AS unit_hours,
--            CASE WHEN "sale" :: numeric < 0 THEN 0 ELSE  "sale" :: numeric END          AS unit_sale,
--            CASE WHEN  ("soldHours" :: numeric *  "sale" :: numeric  ) < 0 THEN 0 
--            ELSE     "soldHours" :: numeric *  "sale" :: numeric  END                   AS extended_sale, 
--            "sale"                                                                         AS lbr_sale
--        FROM :IMPORT_SCHEMA.etl_labor
--            LEFT JOIN :IMPORT_SCHEMA.etl_head USING ("roNumber"))eld
-- WHERE NOT(lbr_sale::numeric < 0 AND jobid NOT LIKE '%1'); 


-- INSERT INTO :SRC_SCHEMA.client_partsdetails (
--     pdid, invoicenumber, jobid,
--     department, base_paytype,
--     base_partsource, partnumber, partdescription,
--     unitcost, unitsale, unitcount,
--     extendedcost, extendedSale,
--     part_line,
--     corecharge_sale, corecharge_cost, corereturn_sale, corereturn_cost,
--     kit_name)
--     SELECT row_number() OVER (ORDER BY pd."roNumber", pd."sequenceNo"),
--            pd."roNumber" AS invoicenumber,
--            pd."lineCode"|| dense_rank() over(PARTITION BY pd."roNumber", pd."lineCode"
--            			ORDER BY pd."laborSequenceNo") AS jobid,
--            '' AS department,
--            NULL AS base_paytype,
--            CASE WHEN pd.source_letter_arr IS NULL
--                 THEN 'NA'
--                 ELSE pd.source_letter_arr[1]
--             END AS base_partsource,
--            COALESCE(upper(pd."number"), 'NPN-' || pd."roNumber" || '-' || pd."lineCode") AS partnumber,
--            COALESCE(pd."desc", 'P/N: ' || COALESCE(pd."number", 'NPN')) AS partdescription,
--            pd."cost"::numeric AS unitcost,
--            pd."sale"::numeric AS unitsale,
--            ceil(pd."qtySold"::numeric)::integer AS unitcount, -- avoids rounding fractions < 1 to zero
--            (pd."cost"::numeric * pd."qtySold"::numeric)::numeric(20,2) AS extendedcost,
--            round((pd."sale"::numeric * pd."qtySold"::numeric)::numeric,2) AS extendedSale,
--            row_number() OVER (PARTITION BY pd."roNumber", pd."lineCode"
--                                   ORDER BY pd."sequenceNo") AS part_line,
--            pd."coreSale"::numeric * ceil(pd."qtySold"::numeric)::integer,
--            pd."coreCost"::numeric * ceil(pd."qtySold"::numeric)::integer,
--            NULL,
--            NULL,
--            NULL
--       FROM (SELECT *,
--                    (SELECT regexp_matches(upper("number"), '^[^A-Z0-9]*([A-Z0-9])')) AS source_letter_arr
--               FROM :IMPORT_SCHEMA.etl_parts
--            ) AS pd
--       JOIN :IMPORT_SCHEMA.etl_head  AS ro ON (ro."roNumber" = pd."roNumber");


WITH parts AS(
    SELECT 
        row_number() OVER (ORDER BY pd."roNumber", pd."sequenceNo"), pd."roNumber"          AS invoicenumber,
        pd."lineCode",
        pd."sequenceNo"                                                                     AS seqno,
        pd."lineCode"|| dense_rank() over(PARTITION BY pd."roNumber", pd."lineCode"
                                             ORDER BY pd."laborSequenceNo")                 AS jobid,
        ''                                                                                  AS department,
        "laborType"                                                                         AS base_paytype,
        "source"                                                                            AS base_partsource,
        COALESCE(upper(pd."number"), 'NPN-' || pd."roNumber" || '-' || pd."lineCode")       AS partnumber,
        COALESCE(pd."desc", 'P/N: ' || COALESCE(pd."number", 'NPN'))                        AS partdescription,
        pd."cost"::numeric                                                                  AS unitcost,
        pd."sale"::numeric                                                                  AS unitsale,
        ceil(pd."qtySold"::numeric)::integer                                                AS unitcount, -- avoids rounding fractions < 1 to zero
        (pd."cost"::numeric * pd."qtySold"::numeric)::numeric(20,2)                         AS extendedcost,
        round((pd."sale"::numeric * pd."qtySold"::numeric)::numeric,2)                      AS extendedSale,
        row_number() OVER (PARTITION BY pd."roNumber", pd."lineCode"
                               ORDER BY pd."sequenceNo")                                    AS part_line,
        pd."coreSale"::numeric * ceil(pd."qtySold"::numeric)::integer                       AS coreSale,
        pd."coreCost"::numeric * ceil(pd."qtySold"::numeric)::integer                       AS coreCost
      FROM :IMPORT_SCHEMA.etl_parts AS pd
      JOIN :IMPORT_SCHEMA.etl_head  AS ro ON (ro."roNumber" = pd."roNumber") 
)  
, labor AS(
    SELECT
        row_number() OVER (ORDER BY "roNumber", "lineCode" ) AS row_number, "lineCode",
        "roNumber",
        jobid,
        jobid1,
        department,
        base_paytype,
        base_paytype_suffix,
        opcode,
        opdescription,
        unit_hours,
        unit_sale,
        extended_sale
    FROM ( SELECT  
               "roNumber",
               "lineCode",
               "lineCode"|| row_number() OVER (PARTITION BY "roNumber", "lineCode" 
                      ORDER BY "sale"::numeric > 0 DESC, "sequenceNo")                     AS jobid,
               row_number() OVER (PARTITION BY "roNumber", "lineCode", "type"
                      ORDER BY "sale"::numeric > 0 DESC, "sequenceNo")                     AS jobid1,
               '' AS department,
               "type"     AS base_paytype,
               CASE WHEN "opCode" IS NULL THEN 'O'
                           ELSE NULL END     AS base_paytype_suffix,
               upper(COALESCE("opCode", 'NO-OP'))   AS opcode,
               CASE WHEN LENGTH("opCodeDesc") < '38'
                THEN "opCodeDesc"           
               ELSE
                substring(COALESCE(regexp_replace(regexp_replace(regexp_replace(
                                                             regexp_replace("opCodeDesc", 'CUSTOMER STATES.',
                                                              'C/S '), 'CUSTOMER REQUESTS.',
                                                              'C/R '), 'CUSTOMER STATE.', '', 'gi'),
                                                   'CUSTOMER REQUEST.', '', 'gi'), 'OP: ' || "opCode"), 1, 38)
               END                                                  AS opdescription,
               CASE WHEN "soldHours"::numeric < 0 THEN 0 ELSE "soldHours" :: numeric END   AS unit_hours,
               CASE WHEN "sale" :: numeric < 0 THEN 0 ELSE  "sale" :: numeric END          AS unit_sale,
               CASE WHEN  ("soldHours" :: numeric *  "sale" :: numeric  ) < 0 THEN 0 
               ELSE     "soldHours" :: numeric *  "sale" :: numeric  END                   AS extended_sale, 
               "sale"                                                                         AS lbr_sale
           FROM :IMPORT_SCHEMA.etl_labor
               LEFT JOIN :IMPORT_SCHEMA.etl_head USING ("roNumber"))eld
      WHERE lbr_sale::numeric >= 0
    --WHERE NOT(lbr_sale::numeric < 0 AND jobid NOT LIKE '%1')
)
, labor_unique_ro_line_paytype AS(
   SELECT 
       row_number,   
       "lineCode", 
       "roNumber", 
       "jobid",   
       "base_paytype"
   FROM labor
   WHERE jobid1 = 1
)
, insert_labor AS(
    INSERT INTO :SRC_SCHEMA.client_labordetails (ldid, invoicenumber, jobid,
                                     department, base_paytype, base_paytype_suffix,
                                     opcode, opdescription, unit_hours, 
                                     unit_sale, extended_sale)
    SELECT 
        row_number,  
        "roNumber",    
        jobid,   
        department,  
        base_paytype,    
        base_paytype_suffix, 
        opcode,  
        opdescription,   
        unit_hours,  
        unit_sale,  
        extended_sale
    FROM labor
)
-- insert_parts
INSERT INTO :SRC_SCHEMA.client_partsdetails (pdid, invoicenumber, jobid,
                                 department, base_paytype,
                                 base_partsource, partnumber, partdescription,
                                 unitcost, unitsale, unitcount,
                                 extendedcost, extendedsale,
                                 part_line, corecharge_sale, corecharge_cost, 
                                 corereturn_sale, corereturn_cost, kit_name) 
SELECT 
    p.row_number,  
    p.invoicenumber,   
    COALESCE(l."jobid", p."jobid") AS jobid,   
    p.department,  
    p.base_paytype,   
    p.base_partsource, 
    p.partnumber,  
    p.partdescription, 
    p.unitcost,    
    p.unitsale,    
    p.unitcount,   
    p.extendedcost,    
    p.extendedSale,   
    p.part_line,   
    p.coreSale, 
    p.coreCost,
    NULL,
    NULL,
    NULL
FROM parts p
    LEFT JOIN labor_unique_ro_line_paytype l
        ON p.invoicenumber = l."roNumber"
            AND p."lineCode" = l."lineCode"
            AND p.base_paytype = l.base_paytype
            ORDER BY p.invoicenumber,p."jobid", p.seqno::numeric;


WITH core_data AS (SELECT
                       pd.invoicenumber,
                       pd.jobid,
                       pd.partnumber,
                       pd.unitcount,
                       pd.unitcost,
                       pd.unitsale
                   FROM :SRC_SCHEMA.client_partsdetails pd
                   WHERE (pd.partdescription = 'CORE RETURN'
                          OR
                          (pd.unitcost = pd.unitsale AND pd.unitcount = -1)
                   )
)
    , update_core AS (
    UPDATE
        :SRC_SCHEMA.client_partsdetails
    SET
        corereturn_sale = (cd.unitcount * cd.unitsale),
        corereturn_cost = (cd.unitcount * cd.unitcost)
    FROM
        core_data cd
    WHERE cd.invoicenumber = :SRC_SCHEMA.client_partsdetails.invoicenumber AND (cd.partnumber = :SRC_SCHEMA.client_partsdetails.partnumber OR cd.partnumber = :SRC_SCHEMA.client_partsdetails.partnumber||'C')
          AND abs(cd.unitcount) = :SRC_SCHEMA.client_partsdetails.unitcount AND cd.jobid = :SRC_SCHEMA.client_partsdetails.jobid
RETURNING *
)
DELETE FROM :SRC_SCHEMA.client_partsdetails
USING core_data
WHERE :SRC_SCHEMA.client_partsdetails.invoicenumber = core_data.invoicenumber
      AND core_data.jobid = :SRC_SCHEMA.client_partsdetails.jobid 
      AND (core_data.partnumber = :SRC_SCHEMA.client_partsdetails.partnumber OR core_data.partnumber = :SRC_SCHEMA.client_partsdetails.partnumber||'C')
      AND core_data.unitcount = :SRC_SCHEMA.client_partsdetails.unitcount;

UPDATE :SRC_SCHEMA.client_partsdetails
SET corereturn_cost = unitcost,
    corereturn_sale = unitsale,
    corecharge_sale = NULL,
    corecharge_cost = NULL
WHERE unitcount < 0 and (partdescription = 'CORE RETURN'
                          OR
                          (unitcost = unitsale));

UPDATE :SRC_SCHEMA.client_partsdetails
SET extendedcost = ROUND( COALESCE(extendedcost,0)::numeric + 
      						
      						 CASE WHEN NULLIF(NULLIF(corecharge_sale::text,''),'0.00') != '' AND NULLIF(NULLIF(corecharge_cost::text,''),'0.00') != '' THEN 
      							COALESCE(corecharge_cost,0)::numeric  
      							ELSE 
      							0 
      						  END + 
      						
      						CASE WHEN NULLIF(NULLIF(corereturn_sale::text,''),'0.00') != '' AND NULLIF(NULLIF(corereturn_cost::text,''),'0.00') != '' THEN 
    						   COALESCE(corereturn_cost,0)::numeric
						      ELSE 
						      0 
						      END
      						, 2),
       extendedSale = ROUND( COALESCE(extendedSale,0)::numeric + 
       						 CASE WHEN NULLIF(NULLIF(corecharge_sale::text,''),'0.00') != '' AND NULLIF(NULLIF(corecharge_cost::text,''),'0.00') != '' THEN 
      							COALESCE(corecharge_sale,0)::numeric  
      							ELSE 
      							0 
      						  END + 
      						
      						CASE WHEN NULLIF(NULLIF(corereturn_sale::text,''),'0.00') != '' AND NULLIF(NULLIF(corereturn_cost::text,''),'0.00') != '' THEN 
    						   COALESCE(corereturn_sale,0)::numeric
						      ELSE 
						      0 
						      END
      						, 2),
      	unitcost     = ROUND( COALESCE(unitcost,0)::numeric +  
      					(( 
      					CASE WHEN NULLIF(NULLIF(corecharge_sale::text,''),'0.00') != '' AND NULLIF(NULLIF(corecharge_cost::text,''),'0.00') != '' THEN 
      							COALESCE(corecharge_cost,0)::numeric  
      							ELSE 
      							0 
      						  END + 
      						
      						CASE WHEN NULLIF(NULLIF(corereturn_sale::text,''),'0.00') != '' AND NULLIF(NULLIF(corereturn_cost::text,''),'0.00') != '' THEN 
    						   COALESCE(corereturn_cost,0)::numeric
						      ELSE 
						      0 
						      END
      					
      					)/COALESCE(unitcount,0)::numeric), 2),
       unitsale     = ROUND( COALESCE(unitsale,0)::numeric + ((
                       CASE WHEN NULLIF(NULLIF(corecharge_sale::text,''),'0.00') != '' AND NULLIF(NULLIF(corecharge_cost::text,''),'0.00') != '' THEN 
      							COALESCE(corecharge_sale,0)::numeric  
      							ELSE 
      							0 
      						  END + 
      						
      						CASE WHEN NULLIF(NULLIF(corereturn_sale::text,''),'0.00') != '' AND NULLIF(NULLIF(corereturn_cost::text,''),'0.00') != '' THEN 
    						   COALESCE(corereturn_sale,0)::numeric
						      ELSE 
						      0 
						      END
                       
                       )/COALESCE(unitcount,0)::numeric), 2)
WHERE (corereturn_cost IS NOT NULL OR corereturn_sale IS NOT NULL 
      OR corecharge_cost IS NOT NULL 
      OR corecharge_sale IS NOT NULL) AND (unitcount IS NOT NULL AND unitcount::numeric > 0);

---parts there but no labor
INSERT INTO :SRC_SCHEMA.client_labordetails (ldid, invoicenumber, jobid,
                                department, base_paytype, base_paytype_suffix,
                                opcode, opdescription,
                                unit_hours, unit_sale, extended_sale
                                ) 
      
SELECT
        (SELECT MAX(ldid) FROM :SRC_SCHEMA.client_labordetails) + (row_number() OVER (ORDER BY pd.invoicenumber, pd.jobid)),
         	pd.invoicenumber::text,
        	pd.jobid::text,
        	NULL,
        	(array_agg(pd.base_paytype))[1] as paytype,
        	NULL,
        	'NO-OP' AS "opCode",
         'NO-OP' AS opdescription,
         '0.00' AS "unit_hours",
         '0.00' AS "unit_sale",
         '0.00' AS "extended_sale"
      FROM :SRC_SCHEMA.client_partsdetails pd 
      WHERE invoicenumber||'--'||jobid NOT IN  (
         SELECT invoicenumber||'--'||jobid FROM :SRC_SCHEMA.client_labordetails
      ) 
GROUP BY pd.invoicenumber, pd.jobid;

INSERT INTO :SRC_SCHEMA.client_job_details (invoicenumber, jobid, job_description, complaint, cause, correction)
SELECT 
    invoicenumber,
    jobid,
    job_description,
    complaint,
    cause,
    correction
FROM (
    SELECT
        jd."roNumber" AS invoicenumber,
        jd."lineCode" || ROW_NUMBER() OVER (
            PARTITION BY jd."roNumber", jd."lineCode" 
            ORDER BY ld."sale"::numeric > 0 DESC, ld."sequenceNo" 
        ) AS jobid,
        ld."opCodeDesc" AS job_description,
        jd."serviceRequest" AS complaint,
        jd."cause" AS cause,
        jd."storyText" AS correction,
        ld."sale" AS lbr_sale
    FROM :IMPORT_SCHEMA.etl_job jd
    LEFT JOIN :IMPORT_SCHEMA.etl_labor ld
        ON jd."roNumber" = ld."roNumber" AND jd."lineCode" = ld."lineCode"
) ejd
WHERE NOT (lbr_sale::numeric < 0 AND jobid NOT LIKE '%1') 

UNION ALL

SELECT
    pd.invoicenumber::text,
    pd.jobid::text,
    '' AS job_description,
    jd."serviceRequest",
    jd."cause",
    jd."storyText"
FROM :SRC_SCHEMA.client_partsdetails pd
LEFT JOIN (
    SELECT
        jd."roNumber",
        jd."lineCode" || ROW_NUMBER() OVER (
            PARTITION BY jd."roNumber", jd."lineCode"
            ORDER BY ld."sequenceNo"
        ) AS "lineCode",
        jd."serviceRequest",
        jd."cause",
        jd."storyText"
    FROM :IMPORT_SCHEMA.etl_job jd
    LEFT JOIN :IMPORT_SCHEMA.etl_labor ld
        ON jd."roNumber" = ld."roNumber" AND jd."lineCode" = ld."lineCode"
) jd ON pd.invoicenumber = jd."roNumber" AND pd.jobid::text = jd."lineCode"::text
WHERE jd."roNumber" IS NULL AND pd.invoicenumber IS NOT NULL;

    
INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, paytype, labor_discount, part_discount, total_discount, summary, need_estimation)    
	(SELECT 
	    invoicenumber,
	    'AMOUNT',
	    'JOB',
	    jobid,
	    LEFT(base_paytype,1),
	    ABS(extendedSale :: numeric) AS discount,
	    NULL,
	    ABS(extendedSale :: numeric) AS total,
	    partdescription,
        UPPER(COALESCE(partdescription, '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE' 
 	FROM :SRC_SCHEMA.client_partsdetails  
      WHERE unitcount::numeric < 0 AND unitcost::numeric = 0 AND extendedSale::numeric != 0
       AND ((UPPER(partdescription) LIKE '%DISCOUNT%' OR UPPER(partdescription) LIKE '%COUPON%') 
       AND (UPPER(partdescription) LIKE '%LABOR%' OR UPPER(partdescription) LIKE '%SERVICE%')))
       
       UNION 
       
       (SELECT 
	    invoicenumber,
	    'AMOUNT',
	    'JOB',
	    jobid,
	    LEFT(base_paytype,1),
	    NULL,
	    ABS(extendedSale :: numeric) AS discount,
	    ABS(extendedSale :: numeric) AS total,
	    partdescription,
        UPPER(COALESCE(partdescription, '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE' 
 	FROM :SRC_SCHEMA.client_partsdetails  
      WHERE unitcount::numeric < 0 AND unitcost::numeric = 0 AND extendedSale::numeric != 0
       AND ((UPPER(partdescription) LIKE '%DISCOUNT%' OR UPPER(partdescription) LIKE '%COUPON%') 
       AND UPPER(partdescription) LIKE '%PART%' ));

INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, paytype, labor_discount, part_discount, total_discount, summary, need_estimation) 
    SELECT 
	    invoicenumber,
	    'AMOUNT',
	    'JOB',
	    jobid,
	    LEFT(base_paytype,1),
	    NULL,
	    NULL,
	    ABS(extendedSale :: numeric) AS discount,
	    partdescription,
        UPPER(COALESCE(partdescription, '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE' 
 	FROM :SRC_SCHEMA.client_partsdetails  
      WHERE unitcount::numeric < 0 AND unitcost::numeric = 0 AND extendedSale::numeric != 0
       AND (UPPER(partdescription) LIKE '%DISCOUNT%' OR UPPER(partdescription) LIKE '%COUPON%') AND (UPPER(partdescription) NOT LIKE '%PART%' AND UPPER(partdescription) NOT LIKE '%LABOR%' AND UPPER(partdescription) NOT LIKE '%SERVICE%');

DELETE	FROM :SRC_SCHEMA.client_partsdetails WHERE unitcount::numeric < 0 AND unitcost::numeric = 0 AND extendedSale::numeric != 0
       AND (UPPER(partdescription) LIKE '%DISCOUNT%' OR UPPER(partdescription) LIKE '%SERVICE%');

INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, paytype, labor_discount, part_discount, total_discount, summary, need_estimation, source_type) 
    SELECT 
	    invoicenumber,
	    'AMOUNT',
	    'JOB',
	    jobid,
	    LEFT(base_paytype,1),
	    NULL,
	    ABS(extendedSale :: numeric) AS parts_discount,
	    ABS(extendedSale :: numeric) AS discount,
	    partdescription,
        false,
        'Negative Sale'
 	FROM :SRC_SCHEMA.client_partsdetails  
      WHERE (unitcount::numeric * unitsale ::numeric) < 0;

INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, labor_discount, part_discount, total_discount, summary, need_estimation, source_type) 
     SELECT
        "roNumber"                        AS ro_number,
        'AMOUNT',
	    'RO',
	    NULL,
	    NULL,
	    NULL,
        "paymentAmount"::numeric  AS pay_amount,
        code,
        false,
        'Payment'
     FROM :IMPORT_SCHEMA.etl_pay WHERE "insuranceFlag"::boolean;



DELETE	FROM :SRC_SCHEMA.client_partsdetails WHERE (unitcount::numeric * unitsale ::numeric) < 0;
DELETE	FROM :SRC_SCHEMA.client_partsdetails WHERE unitcount::numeric = 0;

CREATE TABLE IF NOT EXISTS :IMPORT_SCHEMA.etl_labor_type(
     "HostItemID"     text,
     "Desc"           text);        

INSERT INTO :SRC_SCHEMA.client_paytype_details (paytype, description)
     SELECT "hostItemId" as paytype, COALESCE("laborDetails"::json->>'desc','NA') as description 
     FROM :IMPORT_SCHEMA.etl_labor_type;

WITH discount_identification AS (
        SELECT 
	    "roNumber",
	    'AMOUNT',
	    CASE WHEN "opCode" = 'RO'
		THEN 'RO'
            ELSE 'JOB' END,
            CASE WHEN "opCode" = 'RO'
            	THEN NULL
            ELSE "lineCode"||'1' END AS jobline,
	    ABS("sale" :: numeric),
	    NULL,
        ABS("sale" :: numeric),
	    "opCodeDesc",
         (UPPER(COALESCE("opCodeDesc", '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE' AND "type" !~ '^[WI]'),
        "type",
        'Negative Labor'
 	FROM :IMPORT_SCHEMA.etl_labor  
        WHERE "sale" :: numeric < 0 AND ("opCodeDesc" LIKE ANY(ARRAY['%LBR.DISCOUNT%','%LABOR DISCOUNT%', '%SERVICE DISCOUNT%']))
        UNION
        SELECT 
	    "roNumber",
	    'AMOUNT',
	    CASE WHEN "opCode" = 'RO'
		THEN 'RO'
            ELSE 'JOB' END,
            CASE WHEN "opCode" = 'RO'
            	THEN NULL
            ELSE "lineCode"||'1' END  AS jobline,
	    ABS("sale" :: numeric),
	    NULL,
        ABS("sale" :: numeric),
	    "opCodeDesc",
        (UPPER(COALESCE("opCodeDesc", '')) !~* 'WARRANTY|AUTOGUARD|AUTOGAURD|AUTO GUARD|AUTOFUARD|SKY AUTO|CNA|ZURICH|PROTECTIVE|PROGRESSIVE' AND "type" !~ '^[WI]'),
        "type",
        'Negative Labor'
 	FROM :IMPORT_SCHEMA.etl_labor  
        WHERE "sale" :: numeric < 0 AND NOT COALESCE(("opCodeDesc" LIKE ANY(ARRAY['%LBR.DISCOUNT%','%LABOR DISCOUNT%','%SERVICE DISCOUNT%'])),false)
        )
        -- , updatedata AS (
        --     UPDATE :SRC_SCHEMA.client_labordetails
        --         SET unit_sale ='0.00',extended_sale ='0.00'
        --     FROM discount_identification cd
        --     WHERE cd."roNumber" = :SRC_SCHEMA.client_labordetails.invoicenumber and cd.jobline = :SRC_SCHEMA.client_labordetails.jobid
        -- )
 INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, labor_discount, part_discount, total_discount, summary, need_estimation, paytype, source_type)
    SELECT * FROM discount_identification;

 INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, paytype, labor_discount, part_discount, total_discount, summary, need_estimation, source_type) 
     SELECT 
	    invoicenumber,
	    'AMOUNT',
	    'JOB',
	    jobid,
	    LEFT(base_paytype,1),
	    ABS("unit_sale" :: numeric) AS labor_discount,
	    NULL,
	    ABS("unit_sale" :: numeric) AS discount,
	    opdescription,
        true,
        'Negative Labor'
 	FROM :SRC_SCHEMA.client_labordetails  
      WHERE unit_sale ::numeric < 0;

UPDATE :SRC_SCHEMA.client_labordetails SET
 unit_sale = '0.00',
 unit_hours = '0.00',
 extended_sale = '0.00' 
WHERE unit_sale::numeric < 0;

--Insert null labor
INSERT INTO :SRC_SCHEMA.client_labordetails (ldid, invoicenumber, jobid,
                                department, base_paytype, base_paytype_suffix,
                                opcode, opdescription,
                                unit_hours, unit_sale, extended_sale
                                )
SELECT
        (SELECT MAX(ldid) FROM :SRC_SCHEMA.client_labordetails) + (row_number() OVER (ORDER BY "roNumber", "lineCode")),
        "roNumber",
        "lineCode"||'1',
        NULL,
        (array_agg("type") FILTER (WHERE NULLIF("type",'') IS NOT NULL))[1]  AS "LinePaymentMethod",
        NULL,
        (array_agg("opCode"))[1] AS "opCode",
        (array_agg("opCodeDesc"))[1] AS opdescription,
        '0.00' AS "unit_hours",
        '0.00' AS "unit_sale",
        '0.00' AS "extended_sale"
 FROM
      :IMPORT_SCHEMA.etl_labor
       WHERE "roNumber"||'--'||"lineCode"||'1' NOT IN  (
         SELECT  invoicenumber||'--'||jobid FROM :SRC_SCHEMA.client_labordetails
      ) 
 GROUP BY
      "roNumber",
      "lineCode";

-- INSERT INTO :SRC_SCHEMA.client_payments (invoicenumber, payment_code, payment_amount, is_insurance) 
-- SELECT 
--     "roNumber",
--     code,
--     "paymentAmount",
--     CASE WHEN UPPER(COALESCE("insuranceFlag", '')) = 'Y'
-- 		 THEN true
--     ELSE false END
-- FROM :IMPORT_SCHEMA.etl_pay 
-- ORDER BY "roNumber";

INSERT INTO :SRC_SCHEMA.client_scheduler_id(scheduler_id)
SELECT unique_id FROM :IMPORT_SCHEMA.etl_uuid_detail;

INSERT INTO :SRC_SCHEMA.department_details
SELECT * FROM :IMPORT_SCHEMA.etl_department_detail WHERE is_allowed = false;

INSERT INTO :SRC_SCHEMA.paytype_details
SELECT * FROM :IMPORT_SCHEMA.etl_paytype_detail WHERE is_allowed = false;

INSERT INTO :SRC_SCHEMA.client_scenarioinfo
SELECT manufacturer, valid_makes_array::text[], NULL, NULL FROM :IMPORT_SCHEMA.etl_manufacturer_detail;

INSERT INTO all_manufacturer_details
SELECT manufacturer, valid_makes_array::text[] FROM :IMPORT_SCHEMA.etl_all_manufacturer_detail;

INSERT INTO :SRC_SCHEMA.client_makerenames
SELECT * FROM :IMPORT_SCHEMA.etl_makerenames_detail;

INSERT INTO :SRC_SCHEMA.sequence_detail
SELECT * FROM :IMPORT_SCHEMA.etl_sequence_detail;
