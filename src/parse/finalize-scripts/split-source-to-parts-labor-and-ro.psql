--source_ro
INSERT INTO source_ro ( "R<PERSON>", "Dealer", "Department", "Opened Date", "Closed Date", "Void Flag", "RO Status",
                        "VIN", "Year", "Make", "Model", "Mileage In", "Mileage Out", "Customer Number", "Advisor Name",
                        "Advisor Number", "RO Comments", "RO Disc Prt", "RO Disc Lbr", "RO Misc")
    SELECT  
        DISTINCT "RO",
        "Dealer",
        "Department",
        "Opened Date",
        "Closed Date",
        "Void Flag",
        "RO Status",
        "VIN",
        "Year",
        "Make",
        "Model",
        "Mileage In",
        "Mileage Out",
        "Customer Number",
        "Advisor Name",
        "Advisor Number",
        "RO Comments",
        "RO Disc Prt",
        "RO Disc Lbr",
        "RO Misc"
    FROM du_dms_fortellis.source_ro_details;

--source_ro_labor
INSERT INTO source_ro_labor ( "RO", "Job No", "Op Complaint", "Op Cause", "Op Correction", "Line Code", "Labor Type", "Op Sale Type", 
                                              "Account Code", "Op Code", "Op Description", "Billable Hours", "Labor Total")
    SELECT "RO", 
        "Job No", 
        regexp_replace(regexp_replace(regexp_replace(regexp_replace("Op Complaint", 'CUSTOMER STATES.', 'C/S '),
                       'CUSTOMER REQUESTS.', 'C/R '), 'CUSTOMER STATE.', '', 'g'),'CUSTOMER REQUEST.', '', 'g'),
        "Op Cause",
        "Op Correction",
        "Line Code",
        "Labor Type",
        "Op Sale Type",
        "Account Code",
        "Op Code", 
        regexp_replace(regexp_replace(regexp_replace(regexp_replace("Op Description", 'CUSTOMER STATES.', 'C/S '),
                       'CUSTOMER REQUESTS.', 'C/R '), 'CUSTOMER STATE.', '', 'g'),'CUSTOMER REQUEST.', '', 'g'),
        "Billable Hours", 
        "Labor Total"
    FROM source_ro_details;

--source_ro_parts
INSERT INTO source_ro_parts ( "RO", "Job No", "Part Line Number", "Part Number", 
                                             "Part Description", "Part Quantity", "Part Cost", "Part Price")
    SELECT "RO",
    "Job No",
    "Part Line Number",
    unnest(string_to_array("Part Number", '|'))         AS part_number,
    unnest(string_to_array("Part Description", '|'))    AS part_description,
    unnest(string_to_array("Part Quantity", '|'))       AS part_quantity,
    unnest(string_to_array("Part Cost", '|'))           AS part_cost,
    unnest(string_to_array("Part Price", '|'))          AS part_price
    FROM source_ro_details WHERE has_matching_array_lengths OR has_matching_array_lengths IS NULL;

