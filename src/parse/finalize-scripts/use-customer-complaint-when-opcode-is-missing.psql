UPDATE source_ro_labor
   SET "Op Code"         = 'NO-OP',
       "Op Description"  = "Op Complaint",
       paytype_suffix    = 'RN'
 WHERE COALESCE(trim("Op Code"), '')        = ''
   AND COALESCE(trim("Op Description"), '') = '';

UPDATE source_ro_labor
   SET "Op Description"  = "Op Complaint",
       paytype_suffix = 'N'
 WHERE COALESCE(trim("Op Code"), '')        <> ''
   AND COALESCE(trim("Op Description"), '') =  '';

UPDATE source_ro_labor
   SET "Op Description" = trim("Op Description")
 WHERE paytype_suffix IS NOT NULL;

UPDATE source_ro_labor
   SET "Op Code"         = 'NONE',
       paytype_suffix    = 'C'
 WHERE COALESCE(trim("Op Code"), '')        = ''
   AND COALESCE(trim("Op Description"), '') <> '';

UPDATE source_ro_labor
   SET "Op Description"  = "Op Code"
 WHERE COALESCE(trim("Op Description"), '') =  '';
