--ALTER TABLE du_dms_fortellis_model.etl_head ADD PRIMARY KEY ("roNumber");

CREATE INDEX sol_ronum ON du_dms_fortellis_model.etl_job ("roNumber");

ALTER TABLE du_dms_fortellis_model.etl_job
  ADD FOREIGN KEY ("roNumber")
      REFERENCES du_dms_fortellis_model.etl_head ("roNumber")
      ON UPDATE CASCADE
      ON DELETE CASCADE
;

CREATE INDEX sop_ronum ON du_dms_fortellis_model.etl_parts ("roNumber");

ALTER TABLE du_dms_fortellis_model.etl_parts
  ADD FOREIGN KEY ("roNumber")
      REFERENCES du_dms_fortellis_model.etl_head ("roNumber")
      ON UPDATE CASCADE
      ON DELETE CASCADE
;
