INSERT INTO du_dms_fortellis.invoice_master
    SELECT 
        DISTINCT(h."roNumber"),
        to_timestamp(h."createdTime"::numeric / 1000),
        to_timestamp(h."promiseTime"::numeric / 1000) ,
        h."checkinTime"       ,
        h."vin"               ,
        h."make"              ,
        h."model"             ,
        h."year"              ,
        h."mileageIn"         ,
        h."type"              ,
        h."status"            ,
        c."id"         ,
        c."firstName"         ,
        c."lastName"          ,
        c."address"    ,
        c."address"    ,
        c."address"              ,
        c."address"             ,
        c."address"        ,
        c."address"            ,
        c."address"           ,
        c."phones"      ,
        c."email"             ,
        t."employeeDisplayNumber"        
    FROM du_dms_fortellis_model.etl_head h
    LEFT JOIN du_dms_fortellis_model.etl_customer c USING ("roNumber")
    LEFT JOIN du_dms_fortellis_model.etl_tech t USING ("roNumber");


WITH cte AS (
        SELECT
            "roNo",
            "jobNumber",
            "concern",
            "opcode",
            "opcodeDescription",
            "causeText",
            "storyLineText",
            "totalActualTimeHrs",
            "totalLaborTimeHrs",
            "totalBillingTimeHrs",
            "payType",
            "amount",
            "laborAmount",
            "laborCostAmount",
            "employeeId",
            (row_number() OVER (PARTITION BY "roNo", "jobNumber"
                    ORDER BY "roNo")) AS "indexNumber"
        FROM du_dms_fortellis.source_ros 
        GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15
    )
UPDATE du_dms_fortellis.source_ros a
SET "jobNumber" = ((a."jobNumber":: integer + 9 )::text || b."indexNumber"):: integer
FROM cte b
    WHERE  COALESCE(a."roNo",'')     = COALESCE(b."roNo",'')                                AND         
           COALESCE(a."jobNumber",'')             = COALESCE(b."jobNumber"  ,'')            AND              
           COALESCE(a."concern",'')               = COALESCE(b."concern" ,'')               AND                
           COALESCE(a."opcode" ,'')               = COALESCE(b."opcode"    ,'')             AND                 
           COALESCE(a."opcodeDescription" ,'')    = COALESCE(b."opcodeDescription" ,'')     AND      
           COALESCE(a."causeText"     ,'')        = COALESCE(b."causeText" ,'')             AND               
           COALESCE(a."storyLineText",'')         = COALESCE(b."storyLineText"  ,'')        AND           
           COALESCE(a."totalActualTimeHrs" ,'')   = COALESCE(b."totalActualTimeHrs",'')     AND      
           COALESCE(a."totalLaborTimeHrs" ,'')    = COALESCE(b."totalLaborTimeHrs" ,'')     AND       
           COALESCE(a."totalBillingTimeHrs" ,'')  = COALESCE(b."totalBillingTimeHrs" ,'')   AND      
           COALESCE(a."payType"     ,'')          = COALESCE(b."payType" ,'')               AND                  
           COALESCE(a."amount"  ,'')              = COALESCE(b."amount"  ,'')               AND                  
           COALESCE(a."laborAmount" ,'')          = COALESCE(b."laborAmount" ,'')           AND            
           COALESCE(a."laborCostAmount" ,'')      = COALESCE(b."laborCostAmount"  ,'')      AND         
           COALESCE(a."employeeId" ,'')           = COALESCE(b."employeeId" ,'');
           
INSERT INTO du_dms_fortellis.labor_details
 WITH cte AS (
        SELECT
            "roNo",
            "jobNumber",
            "concern",
            "opcode",
            "opcodeDescription",
            "causeText",
            "storyLineText",
            "totalActualTimeHrs",
            "totalLaborTimeHrs",
            "totalBillingTimeHrs",
            "payType",
            "amount",
            "laborAmount",
            "laborCostAmount",
            "employeeId"
        FROM du_dms_fortellis.source_ros
        GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15
    )
    SELECT
        "roNo",
        "jobNumber",
        "concern",
        string_agg("opcode", ';')             AS opcode,
        string_agg("opcodeDescription", ';')  AS opcodedescription,
        string_agg("causeText", ';')          AS causetext,
        string_agg("storyLineText", ';')      AS storylinetext,
        "totalActualTimeHrs",
        "totalLaborTimeHrs",
        "totalBillingTimeHrs",
        "payType",
        sum("amount" :: numeric)              AS amount,
        sum("laborAmount" :: numeric)         AS laboramount,
        sum("laborCostAmount" :: numeric)     AS laborcostamount,
        "employeeId"
    FROM cte
    GROUP BY 1,2,3,8,9,10,11,15;

INSERT INTO du_dms_fortellis.parts_details
    SELECT
        "roNo", 
        "jobNumber",
        "partNumber",
        "partDescription",
        cast("deliveredQty"::numeric as integer),
        "payType",
        "sellingPricePerPart_amount",
        "costPricePerPart_amount",
        "listPricePerPart_amount",
        "source_description" 
    FROM du_dms_fortellis.source_ros 
    WHERE "partNumber" IS NOT NULL;


