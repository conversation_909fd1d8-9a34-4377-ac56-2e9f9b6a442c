-- CREATE TABLE make_lookup (
--   make_code text NOT NULL PRIMARY KEY,
--   make_name text NOT NULL
-- );

-- INSERT INTO make_lookup
-- SELECT DISTINCT ON (make_code)
--        make_code,
--        make_name
--   FROM (
--         SELECT "Make"          AS make_code,
--                COALESCE("FullMakelName", "Make") AS make_name,
--                count(*)        AS cnt
--           FROM etl_make_model
--          GROUP BY 1, 2
--          ORDER BY 3 DESC
--        ) src;

INSERT INTO client_invoicemaster (
    invoicenumber, open_date, close_date,
    vehicle_year, vehicle_make, vehicle_model, vin,
    customer_id, advisor_id, mileage)
    SELECT "roNumber",
           "openDate"::date,
           "closedDate"::date,
           "year",
           COALESCE("makeDesc", "make", 'MISSING'),
           COALESCE("modelDesc", "model"),
           COALESCE("vin", 'Missing'),
           COALESCE("customerId", 'N/A'),
           "serviceAdvisor",
           "mileage"
      FROM du_dms_fortellis_model.etl_head
      LEFT JOIN etl_vehicle ON ("roNumber") ev
     --  LEFT JOIN make_lookup ON (make_code = "Make")
     ORDER BY 1;

INSERT INTO client_labordetail (ldid, invoicenumber, jobid,
                                department, base_paytype, base_paytype_suffix,
                                opcode, opdescription,
                                sold_hours, sold_dollars,
                                is_warranty, is_internal)
     SELECT row_number() OVER (ORDER BY "roNumber", "sequenceNo"),
            "roNumber",
            "lineCode"                 AS line_number,
            null,
            "type"                AS base_paytype,
            CASE WHEN "opCode" IS NULL THEN 'O' ELSE null END AS paytype_suffix,
            upper(COALESCE("opCode", 'NO-OP')),
            CASE WHEN LENGTH("opCodeDesc") < '38'
                THEN "opCodeDesc"           
            ELSE
               COALESCE(regexp_replace(regexp_replace(regexp_replace(regexp_replace("opCodeDesc", 'CUSTOMER STATES.', 'C/S '), 'CUSTOMER REQUESTS.', 'C/R '), 'CUSTOMER STATE.', '', 'gi'), 'CUSTOMER REQUEST.', '', 'gi'), 'OP: ' || "opCode")
            END,
            "soldHours"::numeric       AS basehours,
            "sale"::numeric            AS sale_amount,
            "type" ~ '^[W]',
            "type" ~ '^[I]'
       FROM du_dms_fortellis_model.etl_labor
       JOIN du_dms_fortellis_model.etl_head USING ("roNumber");

INSERT INTO client_partsdetail (
    pdid, ldid, invoicenumber, jobid,
    department, base_paytype,
    base_partsource, partnumber, partdescription,
    unitcost, unitsale, unitcount,
    extendedcost, extendedsale,
    is_warranty, is_internal,
    part_line, is_core_related)
    --kit_pseudo_job, allocated_sale_amount, allocated_kit_sale_item)
    select row_number() OVER ( ORDER BY  "roNumber","sequenceNo"),null AS ldid,"roNumber","lineCode","department","base_paytype",
    "part_source","prt_no","prt_desc","cost","sale","qtySold","pc","ps",t.warrantyType,t.internalType,"ro","is_core_related" from (
    SELECT 
           pd."roNumber",
           pd."lineCode",
           pd."sequenceNo",
           null AS department,
           pd."type"::text AS base_paytype,
           CASE WHEN pd.source_letter_arr IS NULL
                THEN 'NA'
                ELSE pd.source_letter_arr[1]
            END AS part_source,
           COALESCE(upper(pd."number"), 'NPN-' || pd."roNumber" || '-' || pd."lineCode") as prt_no,
           substring(COALESCE(pd."desc", 'P/N: ' || COALESCE(pd."number", 'NPN')), 1, 38)as prt_desc,
           pd."cost"::numeric,
           pd."sale"::numeric,
           ceil(pd."qtySold"::numeric)::integer as qtySold, -- avoids rounding fractions < 1 to zero
           (pd."cost"::numeric * pd."qtySold"::numeric)::numeric(20,2) as pc,
           round((pd."sale"::numeric * pd."qtySold"::numeric)::numeric,2) AS ps,
           pd."type" ~ '^[W]' as warrantyType,
           pd."type" ~ '^[I]'as internalType,
           row_number() OVER (PARTITION BY pd."roNumber", pd."lineCode"
                                  ORDER BY pd."sequenceNo") as ro,
           (pd."desc" = 'CORE RETURN'
            OR
            (pd."desc" ~* 'core' AND pd."cost" = pd."sale" AND pd."qtySold"::numeric = -1)
           ) AS is_core_related
      FROM (SELECT *,
                   (SELECT regexp_matches(upper("number"), '^[^A-Z0-9]*([A-Z0-9])')) AS source_letter_arr
              FROM du_dms_fortellis_model.etl_parts
           ) AS pd
      JOIN du_dms_fortellis_model.etl_head  AS ro ON (ro."roNumber" = pd."roNumber")
       
       union 
       
    SELECT 
           pd."roNumber",
           pd."lineCode",
           pd."sequenceNo",
           null AS department,
           pd."type"::text AS base_paytype,
           CASE WHEN pd.source_letter_arr IS NULL
                THEN 'NA'
                ELSE pd.source_letter_arr[1]
            END AS part_source,
           COALESCE(upper(pd."number"), 'NPN-' || pd."roNumber" || '-' || pd."lineCode") as prt_no,
           'CORE CHARGE',
           pd."coreCost"::numeric,
           pd."coreSale"::numeric,
           ceil(pd."qtySold"::numeric)::integer as qtySold, -- avoids rounding fractions < 1 to zero
           (pd."coreCost"::numeric * pd."qtySold"::numeric)::numeric(20,2) as pc,
           (pd."coreSale"::numeric * pd."qtySold"::numeric)::numeric(20,2)as ps,
           pd."type" ~ '^[W]',
           pd."type" ~ '^[I]',
           row_number() OVER (PARTITION BY pd."roNumber", pd."lineCode"
                                  ORDER BY pd."sequenceNo") as ro,
           true AS is_core_related
      FROM (SELECT *,
                   (SELECT regexp_matches(upper("number"), '^[^A-Z0-9]*([A-Z0-9])')) AS source_letter_arr
              FROM du_dms_fortellis_model.etl_parts
           ) AS pd
      JOIN du_dms_fortellis_model.etl_head  AS ro ON (ro."roNumber" = pd."roNumber")
           
      where "coreCost"::numeric != 0 OR "coreSale"::numeric != 0)t;


WITH core_charge AS (
     SELECT  * FROM du_dms_fortellis.client_partsdetail WHERE is_core_related AND partdescription = 'CORE CHARGE'
),
core_return AS (
     SELECT  * FROM du_dms_fortellis.client_partsdetail WHERE is_core_related AND  partdescription != 'CORE CHARGE'
),
update_core AS (
   UPDATE du_dms_fortellis.client_partsdetail SET is_nopair_core = true WHERE pdid IN (
	SELECT c.pdid FROM core_charge c LEFT JOIN core_return r ON  c.invoicenumber = r.invoicenumber 
	AND c.jobid =r.jobid 
	WHERE r.invoicenumber IS NULL
	UNION
	SELECT r.pdid FROM core_return r LEFT JOIN core_charge c ON  r.invoicenumber = c.invoicenumber 
	AND r.jobid =c.jobid 
	WHERE c.invoicenumber IS NULL 
	)
),
update_diff AS (
     UPDATE du_dms_fortellis.client_partsdetail SET is_nopair_core = false WHERE pdid IN (
       SELECT c.pdid FROM core_charge c  JOIN core_return r ON  c.invoicenumber = r.invoicenumber 
          AND c.jobid =r.jobid AND c.partnumber = r.partnumber
          AND (ABS(c.unitcost) != ABS(r.unitcost) OR ABS(c.unitsale) != ABS(r.unitsale)) 
          UNION
          SELECT c.pdid FROM core_charge c  JOIN core_return r ON  c.invoicenumber = r.invoicenumber 
          AND c.jobid =r.jobid AND c.partnumber = r.partnumber
          AND (ABS(c.unitcost) != ABS(r.unitcost) OR ABS(c.unitsale) != ABS(r.unitsale)) 
     )
)
DELETE FROM du_dms_fortellis.client_partsdetail WHERE pdid in (
     SELECT c.pdid FROM core_charge c JOIN core_return r ON  c.invoicenumber = r.invoicenumber 
     AND c.jobid =r.jobid AND ABS(c.unitcost) = ABS(r.unitcost) AND ABS(c.unitsale) =ABS(r.unitsale)
     UNION
     SELECT r.pdid FROM core_charge c JOIN core_return r ON  c.invoicenumber = r.invoicenumber 
     AND c.jobid =r.jobid AND ABS(c.unitcost) = ABS(r.unitcost) AND ABS(c.unitsale) =ABS(r.unitsale)
);
DELETE FROM du_dms_fortellis.client_partsdetail WHERE is_core_related AND unitsale::numeric = 0;

\C 'Reporting detail record counts'
SELECT
    (SELECT count(*) FROM client_invoicemaster)  AS invoice_count,
    (SELECT count(*) FROM client_labordetail)    AS labor_count,
    (SELECT count(*) FROM client_partsdetail)    AS parts_count
;

