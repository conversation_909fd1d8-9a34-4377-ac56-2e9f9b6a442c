BEGIN;

-- \ir './finalize-scripts/create-linkages.psql'

UPDATE etl_vehicle 
  SET "make" = 'MISSING' 
WHERE "make" = '{}';
                              
\C 'ROs Count Present Before 6 months'
 SELECT count(*) FROM etl_head WHERE "openDate"::date <  CURRENT_DATE - INTERVAL '6 months';

CREATE TABLE IF NOT EXISTS etl_uuid_detail
    (
        unique_id       text NOT NULL  
    );
CREATE TABLE IF NOT EXISTS :IMPORT_SCHEMA.etl_manufacturer_detail (
        manufacturer      text NOT NULL,
        valid_makes_array       text[] NOT NULL,
        is_allowed    boolean,
        is_default    boolean NOT NULL DEFAULT true
    );
CREATE TABLE IF NOT EXISTS :IMPORT_SCHEMA.etl_all_manufacturer_detail (
        manufacturer      text NOT NULL,
        valid_makes_array       text[] NOT NULL,
        is_allowed    boolean,
        is_default    boolean NOT NULL DEFAULT true
    );

CREATE TABLE IF NOT EXISTS :IMPORT_SCHEMA.etl_rename_rules_detail (
        original_name       text NOT NULL,
        renamed_name  text NOT NULL,
        is_allowed    boolean,
        is_default    boolean NOT NULL DEFAULT true
);

CREATE TABLE IF NOT EXISTS :IMPORT_SCHEMA.etl_paytype_detail (
        type          text NOT NULL,
        paytype       text NOT NULL,
        project_type  text NOT NULL,
        store         text NOT NULL,
        is_allowed    boolean,
        is_default    boolean NOT NULL DEFAULT true
);

CREATE TABLE IF NOT EXISTS :IMPORT_SCHEMA.etl_department_detail (
        department_name       text NOT NULL,
        is_allowed    boolean,
        is_default    boolean NOT NULL DEFAULT true
);

-- \ir './finalize-scripts/transfer-data-to-client-schema.psql'
\ir './finalize-scripts/transfer-data-to-job-client-schema.psql'

COMMIT;
