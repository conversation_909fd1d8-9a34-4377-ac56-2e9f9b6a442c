DROP SCHEMA IF EXISTS du_dms_fortellis_import CASCADE;
CREATE SCHEMA du_dms_fortellis_import;

CREATE UNLOGGED TABLE du_dms_fortellis_import.ro_details (
    invoicenumber text PRIMARY KEY,
    open_date     date NOT NULL,
    close_date    date NULL,
    void_date     date NULL,
    vehicle_year  text NULL,
    vehicle_make  text NOT NULL,
    vehicle_model text NULL,
    vin           text NULL,
    customer_id   text NOT NULL,
    advisor_id    text NULL,
    store_code    text NOT NULL DEFAULT 'N/A',
    -- The invoice master for some stores may contain data
    -- for non-current stores as well.  We identify these
    -- so that process may choose to limit what they consider
    -- to those where the record belongs to the current store.
    -- We can assume that absent the explicit setting of this
    -- value that all records belong to the store being processed.
    is_current_store boolean NOT NULL DEFAULT true,
    branch        text NULL,
    deptname      text NULL,
    mileage       text NULL
);

CREATE UNLOGGED TABLE du_dms_fortellis_import.ro_discount_details (
    invoicenumber           text NOT NULL,
    discount_type           text,
    discount_level          text,
    ro_line                 text,
    labor_discount          text,
    labor_discount_percent  text,    
    part_discount           text,
    part_discount_percent   text,
    total_discount          text,
    summary                 text,
    need_estimation         bool NOT NULL DEFAULT false
);

CREATE UNLOGGED TABLE du_dms_fortellis_import.ro_gl_accounting (    
    invoicenumber	text NOT NULL,
    hostitemid 		text,
    accountingdate 	text,
    accountnumber 	text,
    accountdescription 	text,
    accounttype 	text,
    incbalgrpdesc 	text,
    incbalsubgrp 	text,
    companyid 		text,
    control 		text,
    journalid 		text,
    postingamount 	text,
    postingsequence 	text,
    postingtime 	text,
    paytype             text,
    insuranceamount     text
);

CREATE UNLOGGED TABLE du_dms_fortellis_import.ro_job_details (
    invoicenumber           text NOT NULL,
    jobid                   text,
    job_description         text,
    Complaint               text,
    Cause                   text,
    Correction              text    
    );

CREATE UNLOGGED TABLE du_dms_fortellis_import.ro_labordetails (
    ldid                bigint NOT NULL PRIMARY KEY,
    invoicenumber       text NOT NULL,
    jobid               text,
    department          text,
    base_paytype        text,
    base_paytype_suffix text,
    opcode              text,
    opdescription       text,
    unit_hours          numeric,
    unit_sale           numeric,
    extended_sale       numeric,
    has_multiple_desc   boolean NOT NULL DEFAULT false,
    subgroup_size       integer NOT NULL DEFAULT 0,
    multiple_rank       integer NOT NULL DEFAULT 0,
    alt_group_desc      text NULL,
    use_alt_desc        boolean NOT NULL DEFAULT false
);

CREATE UNLOGGED TABLE du_dms_fortellis_import.ro_partsdetails (
    pdid                bigint NOT NULL PRIMARY KEY,
    invoicenumber       text NOT NULL ,
    jobid               text,
    department          text,
    base_paytype        text,
    base_paytype_suffix text,
    base_partsource     text,
    partnumber          text,
    partdescription     text,
    unitcost            numeric,
    unitsale            numeric,
    unitcount           integer,
    extendedcost        numeric,
    extendedsale        numeric,
    part_line           integer,
    corecharge_sale     numeric NULL,
    corecharge_cost     numeric NULL,
    corereturn_sale     numeric NULL,
    corereturn_cost     numeric NULL,
    kit_name            text NULL
);

CREATE UNLOGGED TABLE du_dms_fortellis_import.ro_sequence (
    sequence_no     bigint NOT NULL PRIMARY KEY,
    ro_number       text NOT NULL ,
    open_date       date,
    close_date      date,
    void_date       date
);

/*Generate Data for DU-Jobs loading*/

INSERT INTO du_dms_fortellis_import.ro_details (
    invoicenumber, open_date, close_date,
    vehicle_year, vehicle_make, vehicle_model, vin,
    customer_id, advisor_id, mileage)
    SELECT "roNumber",
           "openDate"::date,
           "closedDate"::date,
           evd."year",
           COALESCE(NULLIF(evd."make",''),'MISSING'),
           COALESCE(evd."model",'MISSING'),
           evd."vehId",
           COALESCE("customerId",'NA'),
           "serviceAdvisor",
           "mileage"
      FROM du_dms_fortellis_model.etl_head
      JOIN du_dms_fortellis_model.etl_vehicle evd USING ("roNumber")
      ORDER BY 1;

-- INSERT INTO du_dms_fortellis_import.ro_discount_details (invoicenumber, discount_type, discount_level,
--      ro_line, labor_discount, part_discount, total_discount)
--      SELECT "RO",
--             'AMOUNT',
--             'RO',
--             NULL,
--             ABS("RO Disc Prt"::numeric),
--             ABS("RO Disc Lbr"::numeric),
--             ABS("RO Disc Prt"::numeric) + ABS("RO Disc Lbr"::numeric)
--       FROM du_dms_fortellis.source_ro
--       WHERE ABS("RO Disc Prt"::numeric) > 0 OR ABS("RO Disc Lbr"::numeric) > 0;


INSERT INTO du_dms_fortellis_import.ro_labordetails (ldid, invoicenumber, jobid,
                                department, base_paytype, base_paytype_suffix,
                                opcode, opdescription,
                                unit_hours, unit_sale, extended_sale
                                )
     SELECT row_number() OVER (ORDER BY "roNumber", du_dms_fortellis_model.etl_labor."lineCode"),
            "roNumber"                                         AS invoicenumber,
            "lineCode"                                         AS jobid,
            -- SUBSTRING(source_ro."Department",1,1)              AS department,
            NULL,
            COALESCE("type",'OTHER')                  AS base_paytype,
            NULL                                     AS base_paytype_suffix,
            "opCode"                                      AS opcode,
            "opCodeDesc"                                    AS opdescription,
            -- "LaborAllowanceHoursNumeric"
            COALESCE(nullif("actualHours", '')::numeric,0)  AS unit_hours,       
            COALESCE(nullif("sale", '')::numeric,0)       AS unit_sale,
            COALESCE(nullif("actualHours", '')::numeric,0) * COALESCE(nullif("sale", '')::numeric,0) AS extended_sale
       FROM du_dms_fortellis_model.etl_labor;


INSERT INTO du_dms_fortellis_import.ro_partsdetails (
    pdid, invoicenumber, jobid,
    department, base_paytype,
    base_partsource, partnumber, partdescription,
    unitcost, unitsale, unitcount,
    extendedcost, extendedsale,
    part_line)
    SELECT row_number() OVER (ORDER BY pd."roNumber", pd."lineCode"),
           pd."roNumber" AS invoicenumber,
           ld."lineCode"::text AS jobid,
        --    SUBSTRING(ro."Department",1,1)       AS department,
            NULL,
           ld."type"::text             AS base_paytype,
           pd."source"                       AS base_partsource,
           pd."number"                          AS partnumber,
           COALESCE(pd."desc", 'P/N: ' || COALESCE(pd."desc", 'NPN')) AS partdescription,
           pd."cost"::numeric AS unitcost,
           pd."sale"::numeric     AS unitsale,
           pd."qtySold"::integer AS unitcount,
           (pd."extendedCost"::numeric * pd."qtySold"::numeric)::numeric(20,2) AS extendedcost,
           (pd."extendedSale"::numeric * pd."qtySold"::numeric)::numeric(20,2)     AS extendedsale,
           row_number() OVER (PARTITION BY pd."roNumber", ld."lineCode"
                                  ORDER BY pd."lineCode") AS part_line
      FROM (SELECT * FROM du_dms_fortellis_model.etl_parts) AS pd
      JOIN (SELECT *,
                   row_number() OVER (ORDER BY "roNumber", du_dms_fortellis_model.etl_labor."lineCode") AS ldid
              FROM du_dms_fortellis_model.etl_labor
           ) AS ld ON (pd."lineCode" = ld."lineCode" AND pd."roNumber" = ld."roNumber");

DELETE FROM du_dms_fortellis_import.ro_partsdetails WHERE unitcount::numeric < 0;

INSERT INTO du_dms_fortellis_import.ro_job_details (invoicenumber, jobid, job_description, Complaint, Cause, Correction)
    (SELECT  "roNumber"::text,
             "lineCode"::text,
             NULL,
             "actualWork",
             "storyText",
             NULL
    FROM du_dms_fortellis_model.etl_job)
    UNION 
    (SELECT pd.invoicenumber::text,
            pd.jobid::text,
            NULL,
            jd."actualWork",
            jd."storyText",
            null FROM du_dms_fortellis_import.ro_partsdetails pd LEFT JOIN ( SELECT  "roNumber",
            "lineCode", NULL, "actualWork", "storyText", NULL
    FROM du_dms_fortellis_model.etl_job) jd ON pd.invoicenumber = jd."roNumber"
    AND pd.jobid::text = jd."lineCode"::text WHERE jd."roNumber" IS NULL AND pd.invoicenumber IS NOT NULL);
    


INSERT INTO du_dms_fortellis_import.ro_sequence (sequence_no, ro_number, open_date, close_date)
WITH inv_master AS (select
                        "roNumber" as invoicenumber,
                        regexp_replace("roNumber", '[[:alpha:]s]', '', 'g')::integer inv_number,
                       "openDate"::date as open_date,
                       "closedDate"::date as close_date
                   FROM du_dms_fortellis_model.etl_head
                    )
    , ro_sequence AS (SELECT generate_series(min_invoicenumber,
                                             max_invoicenumber) AS ro_number
                      FROM (SELECT
                                min(inv_number) AS min_invoicenumber,
                                max(inv_number) AS max_invoicenumber
                            FROM inv_master) t
)
SELECT														
    ROW_NUMBER()												
    OVER (													
        ORDER BY COALESCE(number_part::integer, ro_number::integer ), code_part nulls first) AS sequence_no,			
    ro_number,													
    open_date,													
    close_date													
FROM (SELECT													
          coalesce(ro_number::text , invoicenumber)                                        AS ro_number,	
          substring(invoicenumber from '^[0-9]+')                                          AS number_part,	
          substring(invoicenumber from '[A-Za-z]+$')                                       AS code_part,	
          open_date,												
          close_date												
      FROM ro_sequence												
          FULL OUTER JOIN inv_master ON ro_number :: text = invoicenumber						
     ) t;
