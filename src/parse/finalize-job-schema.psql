BEGIN;

-- \ir './finalize-scripts/create-linkages.psql'
                              
ALTER TABLE du_dms_fortellis_model.etl_parts
   ADD COLUMN has_matching_array_lengths boolean NULL;
      
UPDATE du_dms_fortellis_model.etl_parts SET has_matching_array_lengths = array_length(string_to_array("sequenceNo", '|'), 1) =
                 ALL(ARRAY[array_length(string_to_array("qtySold", '|'),   1),
                           array_length(string_to_array("desc", '|'),   1),
                           array_length(string_to_array("cost", '|'), 1),
                           array_length(string_to_array("sale", '|'), 1)]);

-- \ir './finalize-scripts/split-source-to-parts-labor-and-ro.psql'

ALTER TABLE du_dms_fortellis_model.etl_job
   ADD COLUMN paytype_suffix text NULL;

-- \ir './finalize-scripts/use-customer-complaint-when-opcode-is-missing.psql'

-- \C 'Removing ROs Opened Prior to Earliest Closed RO'
-- WITH dels AS (
--     DELETE FROM du_dms_fortellis_model.etl_head WHERE "openDate"::date < (SELECT min("closedDate"::date) FROM du_dms_fortellis_model.etl_head)
--     RETURNING *
-- )
-- SELECT count(*)                                                                                       AS ros_removed,
--        (SELECT min("closedDate"::date) FROM du_dms_fortellis_model.etl_head)  AS earliest_closed,
--        (SELECT count(*)
--           FROM du_dms_fortellis_model.etl_head
--          WHERE "closedDate" IS NULL)                                                            AS open_count,
--        (SELECT min("roNumber"::integer) FROM du_dms_fortellis_model.etl_head WHERE "roNumber" ~ '^\d+$')    AS min_ronum,
--        (SELECT max("roNumber"::integer) FROM du_dms_fortellis_model.etl_head WHERE "roNumber" ~ '^\d+$')    AS max_ronum,
--        (SELECT count(*) FROM du_dms_fortellis_model.etl_head WHERE "roNumber" ~ '[A-Z]')                    AS non_numeric_ro_count
--   FROM dels;
  
\C 'Monthly Invoice Count Summary'
SELECT
  to_char("openDate"::date, 'YYYY-MM')                                                               AS "Month",
  COUNT("openDate")  FILTER (WHERE "openDate" IS NOT NULL)                          AS "Open RO",
  COUNT("closedDate") FILTER (WHERE "closedDate" IS NOT NULL)                     AS "Closed RO",
  COUNT("roNumber") FILTER (WHERE "openDate" IS NOT NULL AND "closedDate" IS NULL) AS "Open RO Only"
FROM du_dms_fortellis_model.etl_head
GROUP BY 1
ORDER BY 1;

\C 'ROs Count Present Before 6 months'
SELECT count(*) FROM du_dms_fortellis_model.etl_head WHERE "openDate"::date <  CURRENT_DATE - INTERVAL '6 months';

\ir './populate-fortellis-job-schema.psql'

COMMIT;
