#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
function psql_general() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}'" \
        --set=ON_ERROR_STOP=1 "$@" \
        --set=START_INV="${START_INV}" \
        --set=END_INV="${END_INV}" \
        --set=RESTART_AT="${RESTART_AT}" \
        --set=BREAK_AT="${BREAK_AT}" \
        --set=DMS="${DMS}"
    return
}

function save_disallowed_dept() {
    psql_general <<SQL
         WITH parts_excl_paytypes AS (           
               	 SELECT           
	                    retail.paytype,bp.isexcluded           
	                FROM           
	                    paytype_decision_parts retail           
	                    LEFT JOIN paytype_decision_parts bp            
	                        ON bp.paytype =LEFT(retail.paytype,POSITION('_' IN retail.paytype)+1)           
	                WHERE           
	                    retail.isretail  and SUBSTRING(retail.paytype FROM POSITION('_' in retail.paytype) + 1 for 1) !='Z'           
        ), labor_excl_paytypes AS (            
			    SELECT  retail.paytype, bp.isexcluded            
			        FROM            
			        paytype_decision_labor  retail            
			        LEFT JOIN paytype_decision_labor bp             
			        ON bp.paytype =LEFT(retail.paytype,POSITION('_' IN retail.paytype)+1)            
			        WHERE            
			        retail.isretail  and SUBSTRING(retail.paytype FROM POSITION('_' in retail.paytype) + 1 for 1) !='Z'            
 		), parts_excl_dept AS (

 				SELECT DISTINCT SUBSTRING(COALESCE(LEFT(paytype, POSITION('_' in paytype) - 1), ''),2,1) AS dept FROM parts_excl_paytypes WHERE isexcluded AND LEFT(paytype,1) ~'[CWI]' 
                AND LENGTH(LEFT(paytype, POSITION('_' in paytype) - 1)) > 1
   		), labor_excl_dept AS(

 				SELECT DISTINCT SUBSTRING(COALESCE(LEFT(paytype, POSITION('_' in paytype) - 1), ''),2,1) AS dept FROM labor_excl_paytypes WHERE isexcluded AND LEFT(paytype,1) ~'[CWI]' 
                AND LENGTH(LEFT(paytype, POSITION('_' in paytype) - 1)) > 1
        )
        UPDATE client_department_details 
        	SET disallowed_dept = (SELECT ARRAY_AGG(p.dept) FROM parts_excl_dept p WHERE p.dept IN (SELECT * FROM labor_excl_dept));
        UPDATE client_department_details 
        SET allowed_dept = (SELECT ARRAY (
        	SELECT unnest(allowed_dept)
      		EXCEPT ALL
      		SELECT unnest(disallowed_dept)
      	) FROM client_department_details);
        
SQL
}

save_disallowed_dept
