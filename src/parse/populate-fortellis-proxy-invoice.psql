-- Search Path has already been set
SET client_min_messages = warning;

-- Get Payment code for specified RO
 CREATE OR REPLACE FUNCTION get_payment_code_for_ro
     (
         in_ro_number text
     )
     RETURNS text
 LANGUAGE SQL
 STRICT
 AS $function$
     SELECT "code"
     FROM etl_pay
     WHERE "roNumber" = in_ro_number
     limit 1;
 $function$;

WITH acc_details AS(
	SELECT
       d."referenceNo"                      AS "RO#",
       d."accountingDate"                   AS "AccountingDate",
       d."journalId"                        AS "JournalID"
    FROM du_dms_fortellis_model.etl_accounts d
        JOIN du_dms_fortellis_model.etl_account_coa c
            ON d."accountNumber" = c."accountNumber"
    WHERE  c."accountType"  in ('S','C')
    ORDER BY d."referenceNo"
)
,rowise_journlid AS(
    SELECT DISTINCT
        d."referenceNo"                 AS "RO#",
        d."journalId"                   AS "JournalID",
        d."accountingDate"              AS "AccountingDate"
    FROM du_dms_fortellis_model.etl_accounts d
)
,delete_journal_id AS(
    SELECT r."RO#",r."JournalID",r."AccountingDate"
    FROM rowise_journlid r
    LEFT JOIN acc_details a
             ON a."RO#"= r."RO#" AND a."JournalID"=r."JournalID" 
    WHERE a."JournalID" IS NULL 
      order by a."RO#"
)
,delete_journal_id_account AS(
    SELECT rs."RO#",rs."JournalID",rs."AccountingDate"
    FROM delete_journal_id rs
    JOIN acc_details acs
             ON acs."RO#"= rs."RO#"  
             and  acs."AccountingDate" <> rs."AccountingDate"    
)
DELETE
FROM du_dms_fortellis_model.etl_accounts atd
USING delete_journal_id_account dr
WHERE atd."referenceNo" = dr."RO#"
AND atd."journalId" = dr."JournalID"
AND atd."accountingDate" = dr."AccountingDate";

DELETE FROM etl_labor
WHERE NOT EXISTS (
    SELECT 1
    FROM etl_head
    WHERE etl_head."roNumber" = etl_labor."roNumber" 
);


DELETE FROM etl_parts
WHERE NOT EXISTS (
    SELECT 1
    FROM etl_head
    WHERE etl_head."roNumber" = etl_parts."roNumber" 
);


DELETE FROM etl_job
WHERE NOT EXISTS (
    SELECT 1
    FROM etl_head
    WHERE etl_head."roNumber" = etl_job."roNumber" 
);


-- repair_order
INSERT INTO repair_order (
    ro_number, creation_date, completion_date, department, branch, sub_branch, advisor,
    tag_no, delivery_date, promised_date, promised_time, open_time, booked_date,
    booked_time, payment_code, comments, remarks
)
SELECT DISTINCT
    h."roNumber"                          AS ro_number,
    h."openDate"::date                    AS creation_date,
    h."closedDate"::date                  AS completion_date,
    NULL                                  AS department,
    NULL                                  AS branch,
    NULL                                  AS sub_branch,
    h."serviceAdvisor"                    AS advisor,
    h."tagNo"                             AS tag_no,
    v."deliveryDate"::date                AS delivery_date,
    h."promisedDate"::date                AS promised_date,
    h."promisedTime"::time                AS promised_time,
    h."openTime"::time                    AS open_time,
    h."bookedDate"::date                  AS booked_date,
    h."bookedTime"::time                  AS booked_time,
    get_payment_code_for_ro(h."roNumber") AS payment_code,
    REGEXP_REPLACE(h."comments", '~', ' ', 'g') AS comments,
    h."remarks"                                 AS remarks
FROM etl_head h
LEFT JOIN etl_vehicle v ON h."roNumber" = v."roNumber"
ORDER BY h."roNumber" ASC;

--repair_order_customer
INSERT INTO repair_order_customer (ro_number, customer_number, customer_name, customer_address, customer_city, customer_state,
                                   customer_zip, customer_phone, home_phone, main_phone, cell_phone, customer_email, license_number)
    SELECT DISTINCT ON (etl_head."roNumber")
        etl_head."roNumber"                                     AS ro_number,
        etl_head."customerId"                                   AS customer_number,
        "name1"                                                 AS customer_name,
        "addressLine1"                                          AS customer_address,
        "cityStateZip"                                          AS customer_city,
        null                                                    AS customer_state,
        null                                                    AS customer_zip,
        "contactPhoneNumber"  AS customer_phone,
        -- Customer Phone (MainPhone)
    --(
    --   SELECT p->>'number'
    --    FROM jsonb_array_elements(eh."number"::jsonb) AS p
    --    WHERE p->>'description' = 'MainPhone'
    --   LIMIT 1
    --   ) AS customer_phone,

    -- -- Home Phone
    -- (
    --     SELECT p->>'number'
    --     FROM jsonb_array_elements("number"::jsonb) AS p
    --     WHERE p->>'description' = 'HomePhone'
    --     LIMIT 1
    -- ) AS home_phone,

    -- -- Main Phone
    -- (
    --     SELECT p->>'number'
    --     FROM jsonb_array_elements("number"::jsonb) AS p
    --     WHERE p->>'description' = 'WorkPhone'
    --     LIMIT 1
    -- ) AS main_phone,

    -- -- Cell Phone
    -- (
    --     SELECT p->>'number'
    --     FROM jsonb_array_elements("number"::jsonb) AS p
    --     WHERE p->>'description' = 'Cellular'
    --     LIMIT 1
    -- ) AS cell_phone,

    -- Home Phone
        CASE
            WHEN jsonb_typeof("number"::jsonb) = 'array' THEN (
                SELECT p->>'number'
                FROM jsonb_array_elements("number"::jsonb) AS p
                WHERE p->>'description' = 'HomePhone'
                LIMIT 1
            )
            ELSE NULL
        END AS home_phone,
    -- Main Phone
        CASE
            WHEN jsonb_typeof("number"::jsonb) = 'array' THEN (
                SELECT p->>'number'
                FROM jsonb_array_elements("number"::jsonb) AS p
                WHERE p->>'description' = 'WorkPhone'
                LIMIT 1
            )
            ELSE NULL
        END AS main_phone,
    -- Cell Phone
        CASE
            WHEN jsonb_typeof("number"::jsonb) = 'array' THEN (
                SELECT p->>'number'
                FROM jsonb_array_elements("number"::jsonb) AS p
                WHERE p->>'description' = 'Cellular'
                LIMIT 1
            )
            ELSE NULL
        END AS cell_phone,
        "address"                                               AS customer_email,
        "licenseNumber"                                         AS license_number
    FROM etl_head
        LEFT JOIN etl_vehicle USING ("roNumber")
         ORDER BY etl_head."roNumber";
--         LEFT JOIN (SELECT "roNumber", string_agg(HomePhone,',') AS HomePhone, string_agg(MainPhone,',') AS MainPhone, string_agg(CellularPhone,',') AS CellularPhone FROM (SELECT "roNumber",  CASE WHEN PhoneDesc_text= 'HomePhone' THEN PhoneNumber_text END AS HomePhone, 
--                             CASE WHEN PhoneDesc_text= 'MainPhone' THEN PhoneNumber_text END AS MainPhone, 
--                             CASE WHEN PhoneDesc_text= 'Cellular' THEN PhoneNumber_text END AS CellularPhone FROM etl_head, lateral(SELECT jsonb_array_elements(jsonb("PhoneNumber")->'V')->'_attributes'->>'Idx' AS PhoneNumber_idx, jsonb_array_elements(jsonb("PhoneNumber")->'V')->>'_text' AS PhoneNumber_text) t 
--                     LEFT JOIN lateral(SELECT jsonb_array_elements(jsonb("PhoneDesc")->'V')->'_attributes'->>'Idx' AS PhoneDesc_idx, jsonb_array_elements(jsonb("PhoneDesc")->'V')->>'_text' AS PhoneDesc_text) t1 ON t.PhoneNumber_idx = t1.PhoneDesc_idx ) ph
--         GROUP BY "roNumber"
-- ) cph ON cph."roNumber" = etl_head."roNumber"
--     ORDER BY etl_head."roNumber", etl_cust_mail."EmailAddress_idx";

--	repair_order_vehicle
INSERT INTO repair_order_vehicle (ro_number, vehicle_year, vehicle_make, vehicle_model, vehicle_vin,
                                  vehicle_color, mileage_in, mileage_out)
    SELECT DISTINCT
        "roNumber"         AS ro_number,
        "year"             AS vehicle_year,
        "makeDesc"         AS vehicle_make,
        "modelDesc"        AS vehicle_model,
        "vin"              AS vehicle_vin,
        "vehicleColor"     AS vehicle_color,
        "mileage"::int8    AS mileage_in,
        "mileageOut"::int8 AS mileage_out
    FROM etl_vehicle
    LEFT JOIN etl_head USING ("roNumber");

--repair_line
INSERT INTO repair_line (ro_number, ro_line, complaint, add_on_flag)
    SELECT
        "roNumber"          AS ro_number,
        "lineCode"       AS ro_line,
        REGEXP_REPLACE("serviceRequest",'~',' ','g') AS complaint,
        CASE
        WHEN "addOnFlag" = 'true'
        THEN TRUE
        ELSE FALSE END      AS add_on_flag
    FROM etl_job;

--repair_job
INSERT INTO repair_job (ro_number, ro_line, ro_job_line, billing_code, op_code,
                        op_description, sold_hours, actual_hours, labor_cost, sale_amount, cause, correction)
                        --paytype_description)
    SELECT
        lbr."roNumber"                                     AS ro_number,
        lbr."lineCode"                                      AS ro_line,
        "sequenceNo"::int                               AS ro_job_line,
        "type"                                     AS billing_code,
        "opCode"                                        AS op_code,
        REGEXP_REPLACE("opCodeDesc",'~',' ','g')        AS op_description,
        coalesce(nullif("soldHours", '')::numeric, 0)   AS sold_hours,
        coalesce(nullif("actualHours", '')::numeric, 0) AS actual_hours,
        coalesce(nullif("cost", '')::numeric, 0)        AS labor_cost,
        coalesce(nullif("sale", '')::numeric, 0)        AS sale_amount,
        coalesce(REGEXP_REPLACE(job."cause",'~',' ','g'), '')                       AS cause,
        coalesce(REGEXP_REPLACE(job."storyText",'~',' ','g'), '')                   AS correction
        -- lbt."Desc"::json->>'_text'                      AS paytype_description
    FROM etl_labor lbr
        -- LEFT JOIN etl_labor_type lbt ON (lbt."HostItemID"::json->>'_text' = lbr."lbrLaborType")
        JOIN etl_job job
            ON lbr."roNumber" = job."roNumber"
               AND lbr."lineCode" = job."lineCode";

--repair_part
INSERT INTO repair_part (ro_number, ro_line, ro_job_line, ro_part_line, prt_billing_type, part_number,
                         part_description, quantity_sold, unit_cost, unit_sale, unit_core_cost, unit_core_sale, unit_part_list)
    SELECT
        "roNumber"                                                        AS ro_number,
        "lineCode"                                                     AS ro_line,
        "laborSequenceNo"::int                                         AS ro_job_line,
        row_number() OVER(PARTITION BY "roNumber", "lineCode", "laborSequenceNo")         AS ro_part_line,
        coalesce(nullif("laborType", '')::text, 'C')                  AS prt_billing_type,
        "number"                                                       AS part_number,
        REGEXP_REPLACE("desc",'~',' ','g')                             AS part_description,
        coalesce(nullif(trim("qtySold"), '')::numeric, 0)              AS quantity_sold,
        coalesce(nullif(trim("cost"), '')::numeric, 0)                 AS unit_cost,
        coalesce(nullif(trim("sale"), '')::numeric, 0)                 AS unit_sale,
        coalesce(nullif(trim("coreCost"), '')::numeric, 0)             AS unit_core_cost,
        coalesce(nullif(trim("coreSale"), '')::numeric, 0)             AS unit_core_sale,
        coalesce(nullif(trim("list"), '')::numeric, 0)                 AS unit_part_list
    FROM etl_parts prt  ORDER BY "roNumber", "lineCode", "sequenceNo"::numeric;

--repair_job_technician
-- INSERT INTO repair_job_technician_detail (ro_number, ro_line, ro_job_line, ro_job_tech_line, tech_id,
--                                           tech_billing_type, actual_hours, booked_hours, tech_lbr_cost, tech_lbr_sale)
--     SELECT
--         "roNumber"                AS ro_number,
--         "lineCode"             AS ro_line,
--         "sequenceNo"::int      AS ro_job_line,
--         row_number() OVER(
--             PARTITION BY "roNumber", "sequenceNo"
--         )                         AS ro_job_tech_line,                       
--         ARRAY_TO_STRING(ARRAY(SELECT json_array_elements_text("technicianIds"::json)), ', ') AS tech_id,
--         "type"            AS tech_billing_type,
--         "actualHours"::numeric AS actual_hours,
--         "soldHours"::numeric   AS booked_hours,
--         coalesce(nullif("cost", '')::numeric, 0)        AS tech_lbr_cost,
--         coalesce(nullif(sale, '')::numeric, 0)        AS tech_lbr_sale
--     FROM etl_labor tech WHERE "lineCode" IS NOT NULL
--     GROUP BY "roNumber", "lineCode", "sequenceNo", "tech_id", "type", "actualHours", "soldHours", "cost", sale;
 INSERT INTO repair_job_technician_detail 
    (ro_number, ro_line, ro_job_line, ro_job_tech_line, tech_id,
     tech_billing_type, actual_hours, booked_hours, tech_lbr_cost, tech_lbr_sale)
SELECT
    tech."roNumber"              AS ro_number,
    tech."lineCode"              AS ro_line,
    tech."sequenceNo"::int       AS ro_job_line,
    row_number() OVER(
        PARTITION BY tech."roNumber", tech."sequenceNo"
    )                            AS ro_job_tech_line,
    -- STRING_AGG(DISTINCT hrs."technicianId", ', ') AS tech_id,
    hrs."technicianId"           AS tech_id,
    tech."type"                  AS tech_billing_type,
    tech."actualHours"::numeric  AS actual_hours,
    tech."soldHours"::numeric    AS booked_hours,
    coalesce(nullif(tech."cost", '')::numeric, 0) AS tech_lbr_cost,
    coalesce(nullif(tech.sale, '')::numeric, 0)   AS tech_lbr_sale
FROM etl_labor tech
LEFT JOIN etl_hours hrs
    ON hrs."roNumber"   = tech."roNumber"
   AND hrs."sequenceNo" = tech."sequenceNo"
WHERE tech."lineCode" IS NOT NULL
GROUP BY tech."roNumber", tech."lineCode", tech."sequenceNo", tech."type", tech."actualHours", tech."soldHours", tech."cost", tech.sale, hrs."technicianId";

-- --repair_tech_punch
INSERT INTO repair_tech_punch (ro_number, ro_line, tech_id, work_date, work_start_time,
                               work_end_time, work_type, work_duration)
    SELECT
        "roNumber"             AS ro_number,
        "lineCode"          AS ro_line,
        "technicianId"            AS tech_id,
        "workDate"::date    AS work_date,
        "timeOn"::time      AS work_start_time,
        "timeOff"::time     AS work_end_time,
        "workType"          AS work_type,
        "duration"::numeric AS work_duration
    FROM etl_tech_punch;

-- --repair_other - MISC/GOG(LUBE)/SUBLET

INSERT INTO repair_other (ro_number, item_type, ro_line, other_billing_type, other_code,
                          other_description, other_cost, other_sale)
    SELECT
        "roNumber"                       AS ro_number,
        CASE
        WHEN "type" = 'M'
            THEN 'MISC'
        WHEN "type" = 'L'
            THEN 'GOG'
        WHEN "type" = 'S'
            THEN 'SUBLET'
        END                              AS item_type,
        CASE
        WHEN lower("lineCode") = 'ro'
            THEN NULL
        ELSE "lineCode"
        END                              AS ro_line,
        "laborType"                           AS other_billing_type,
        "opCode"                         AS other_code,
        REGEXP_REPLACE("opCodeDesc",'~',' ','g') AS other_description,
        CASE
        WHEN "type" = 'M'
            THEN "miscCost"::numeric
        WHEN "type" = 'L'
            THEN "gogCost"::numeric
        WHEN "type" = 'S'
            THEN "subletCost"::numeric
        END                              AS other_cost,
        CASE
        WHEN "type" = 'M'
            THEN "miscPrice"::numeric
        WHEN "type" = 'L'
            THEN "gogPrice"::numeric
        WHEN "type" = 'S'
            THEN "subletPrice"::numeric
        END                              AS other_sale
    FROM du_dms_fortellis_model.etl_mls
    WHERE lower("lineCode") = 'ro'
       OR EXISTS (
           SELECT 1 FROM repair_line rl
           WHERE rl.ro_number = "roNumber"
             AND rl.ro_line = "lineCode"
       );

--repair_other - Deductible
INSERT INTO repair_other (ro_number, item_type, ro_line, other_billing_type, other_code, other_cost, other_sale)
    SELECT
        "roNumber"                                   AS ro_number,
        'DEDUCTIBLE'                                 AS item_type,
        CASE
        WHEN lower("lineCodes") = 'ro'
            THEN NULL
        ELSE "lineCodes" END       AS ro_line,
        "laborType"                AS other_billing_type,
        "laborType"                AS other_code,
        "actualAmount"::numeric AS other_cost,
        "actualAmount"::numeric AS other_sale
    FROM du_dms_fortellis_model.etl_deductible;

--repair_other - FEE


INSERT INTO repair_other (ro_number, item_type, ro_line, other_billing_type, other_code, other_description, other_cost, other_sale,misc_quantity)

    SELECT
        f."roNumber"              AS ro_number,
        'MISC'                   AS item_type,
        f."lineCode"              AS ro_line,
        f."laborType"             AS other_billing_type,
        f."id"                    AS other_code,
        f."opCodeDesc"            AS other_description,
        f.cost::numeric           AS other_cost,
        f.sale::numeric           AS other_sale,
        CASE
        WHEN "lopOrPartFlag" = 'P'
            THEN epd."qtySold"
        ELSE NULL END                AS misc_quantity
    FROM etl_fees f JOIN
    (SELECT "roNumber", "lineCode", "number", "qtySold", "sequenceNo" FROM etl_parts GROUP BY 1,2,3,4,5)
    epd ON f."roNumber" = epd."roNumber" AND f."LoporPartSeqNo" = epd."sequenceNo"
    WHERE f.sale::numeric != 0 AND source = 'parts';


    INSERT INTO repair_other (ro_number, item_type, ro_line, other_billing_type, other_code, other_description, other_cost, other_sale)

    SELECT
        f."roNumber"              AS ro_number,
        'MISC'                   AS item_type,
        f."lineCode"              AS ro_line,
        f."laborType"             AS other_billing_type,
        f."id"                    AS other_code,
        f."opCodeDesc"            AS other_description,
        f.cost::numeric           AS other_cost,
        f.sale::numeric           AS other_sale
    FROM etl_fees f
    WHERE f.sale::numeric != 0 AND source = 'labor';

   -- SELECT
   --     "roNumber"              AS ro_number,
   --     'MISC'                   AS item_type,
   --     "lineCode"              AS ro_line,
   --     "laborType"             AS other_billing_type,
   --     "id"                    AS other_code,
   --     "opCodeDesc"            AS other_description,
  --      cost::numeric           AS other_cost,
 --       sale::numeric           AS other_sale,


-- --repair_other - TAX
INSERT INTO repair_other (ro_number, item_type, other_billing_type, other_code, other_description, other_sale)
    SELECT
        "roNumber"          AS ro_number,
        'TAX'               AS item_type,
        "payType"           AS other_billing_type,
        "payType"           AS other_code,
        'RO tax'            AS other_description,
        "roTax"::numeric AS other_sale
    FROM etl_total
    WHERE "roTax"::numeric != 0;

-- --repair_other - Shop Charge Fee
INSERT INTO repair_other (ro_number, item_type, other_billing_type, other_code, other_description, other_sale, other_cost)
    SELECT
        "roNumber"                    AS ro_number,
        'FEE'                         AS item_type,
        "payType"                  AS other_billing_type,
        "payType"                  AS other_code,
        'SHOP SUPPLIES/ DISPOSAL FEE' AS other_description,
        "shopChargeSale"::numeric  AS other_sale,
        "shopChargeCost"::numeric  AS other_cost
    FROM etl_total
    WHERE "shopChargeSale"::numeric != 0;

--repair_discount
INSERT INTO repair_discount (ro_number, ro_line, disc_code, disc_description, disc_applied, labor_discount, parts_discount, total_discount)
    SELECT
        "roNumber"                                     AS ro_number,
        CASE
        WHEN "lineCode" = 'RO'
            THEN NULL
        ELSE "lineCode" END                AS ro_line,
        id                AS disc_code,
        REGEXP_REPLACE("desc",'~',' ','g')                             AS disc_description,
        "classOrType"               AS disc_applied,
        "laborDiscount"::numeric AS labor_discount,
        "partsDiscount"::numeric AS parts_discount,
        "totalDiscount"::numeric AS total_discount
    FROM du_dms_fortellis_model.etl_discount;

-- --repair_order_payment - Payment details
INSERT INTO repair_order_payment (ro_number, pay_code, pay_amount, is_insurance)
    SELECT
        "roNumber"                   AS ro_number,
        "code"             AS pay_code,
        "paymentAmount"::numeric  AS pay_amount,
        "insuranceFlag"::boolean  AS is_insurance
    FROM etl_pay;

--pay_type_filter
INSERT INTO pay_type_filter ("label", "pay_type","insurance", "from_type", "target_type")
    SELECT
        "label"                AS label,
        "paytype"              AS pay_type,
        coalesce(nullif(trim("insurance"), '')::text, 'no'),
        "fromtype"             AS from_type,
        "targettype"           AS target_type          
    FROM etl_pay_type_filter;


-- --repair_account_gldetails - Deductible

CREATE OR REPLACE FUNCTION du_dms_fortellis_model.get_pattern_match_for_gl_accounting_report
    (
        in_data text
    )
    RETURNS numeric
LANGUAGE plpgsql
STRICT SECURITY DEFINER
AS $function$
DECLARE
    response numeric;
BEGIN

    WITH amount_list AS(
        SELECT UNNEST(ARRAY_REMOVE(STRING_TO_ARRAY(in_data, ','), '')) AS amount
    )
    , amount_list1 AS(
        SELECT 
            amount::numeric                        AS amount,
            bool_and(amount::numeric < 0) OVER ()  AS all_negative,
            bool_and(amount::numeric >= 0) OVER () AS all_positive
        FROM amount_list
    )
    , pattern1 AS(
        SELECT
            sum(amount)                            AS total_amount
        FROM amount_list1 
        WHERE all_negative OR all_positive
    )
    , pattern2 AS(
        SELECT 
            sum(amount)                            AS total_amount
        FROM (SELECT  
                  abs(amount)                      AS amount
              FROM (SELECT DISTINCT amount 
                    FROM amount_list1)c 
              GROUP BY 1
              HAVING count(abs(amount) ) > 1)p
    )
    SELECT 
        COALESCE(pattern1.total_amount, pattern2.total_amount)
    INTO response
    FROM pattern1, pattern2;    

    RETURN response;

END;
$function$;

INSERT INTO repair_account_gldetails (ro_number, ro_account_no, sale, cost, company_id, account_type)
WITH pay_summary AS (
 SELECT "roNumber"::text, SUM("paymentAmount"::numeric) AS "paymentAmount"
    FROM du_dms_fortellis_model.etl_pay
    WHERE "insuranceFlag" = 'N'
    GROUP BY "roNumber"
)
,sumpay AS (
	SELECT "referenceNo" AS "ro_num", SUM(("postingAmount")::numeric) FROM du_dms_fortellis_model.etl_accounts
	GROUP BY "referenceNo"
)
, coa AS (
    SELECT
        (ARRAY_AGG("accountType"))[1]        AS "accountType",
        STRING_AGG("accountDesc", ', ') AS "accountDesc",
        "accountNumber"      AS "accountNumber",
        (ARRAY_AGG("chain"))[1]               AS "chain"
    FROM du_dms_fortellis_model.etl_account_coa GROUP BY "accountNumber"
)
, etl_accts AS (
    SELECT
        d."hostItemId"          AS "hostItemId",
    	d."accountingDate"      AS "accountingDate",
	    d."accountNumber"       AS "accountNumber",
	    d."companyId"           AS "companyId",
	    d."controlNo"             AS "controlNo",
	    d."controlNo2"            AS "controlNo2",
	    d."controlTypeCode"         AS "controlTypeCode",
	    d."controlType2Code"        AS "controlType2Code",
	    d."currentMonth"        AS "currentMonth",
	    d."detailDescription"   AS "detailDescription",
	    d."distillControlTypeCode"  AS "distillControlTypeCode",
	    d."distillControlType2Code" AS "distillControlType2Code",
	    d."journalId"           AS "journalId",
	    d."postingAmount"       AS "postingAmount",
	    d."postingSequence"     AS "postingSequence",
	    d."postingTime"         AS "postingTime",
	    d."productivityNo"              AS "productivityNo",
	    d."productivityNo2"             AS "productivityNo2",
	    d."productivityNoType"            AS "productivityNoType",
	    d."productivityNoType2"           AS "productivityNoType2",
	    d."referenceNo"               AS "referenceNo",
	    d."scheduleNumber"      AS "scheduleNumber",
	    d."statCount"           AS "statCount",
	    coa."accountType"
	FROM du_dms_fortellis_model.etl_accounts d
        JOIN coa
            ON (d."accountNumber"::text) = (coa."accountNumber")
	--WHERE replace((d."referenceNo"::json -> '_text'::text)::text, '"'::text, ''::text) = '117286' --'457014'--'457012'--'457023' --'456989'
)
, etl_accts_window1 AS (
    SELECT d."hostItemId",
    	d."accountingDate",
	    d."accountNumber",
	    d."companyId",
	    d."controlNo",
	    d."controlNo2",
	    d."controlTypeCode",
	    d."controlType2Code",
	    d."currentMonth",
	    d."detailDescription",
	    d."distillControlTypeCode",
	    d."distillControlType2Code",
	    d."journalId",
	    d."postingAmount",
	    d."postingSequence",
	    d."postingTime",
	    d."productivityNo",
	    d."productivityNo2",
	    d."productivityNoType",
	    d."productivityNoType2",
	    d."referenceNo",
	    d."scheduleNumber",
	    d."statCount",
	    d."accountType",
	    COUNT(*) OVER(Partition By d."referenceNo", d."postingTime")                       AS posting_time_count,
	    bool_or(d."accountType" = 'S')
	        OVER(Partition By d."referenceNo", d."postingTime")                            AS any_sale_in_posting,
	    array_agg(CASE WHEN (d."accountType" = 'S' AND to_number("postingAmount",'*********9.99') != 0)
	                       OR d."accountType" != 'S'
	                   THEN "accountNumber" END)
	        OVER(Partition By d."referenceNo", d."postingTime")                            AS acct_no_list_of_posting,
	     SUM("postingAmount"::numeric) 
            OVER(Partition By d."referenceNo", d."accountNumber", d."postingTime")         AS account_posting_amount
	FROM etl_accts d
        
)
,etl_accts_window2 AS (
    SELECT *,
    bool_or(ps."roNumber" IS NOT NULL)
	    OVER(Partition By d."referenceNo", d."postingTime")                  AS have_matching_pay_payment
    FROM etl_accts_window1 d
        LEFT JOIN pay_summary ps
            ON ps."roNumber" = d."referenceNo"
               AND ps."paymentAmount" = d.account_posting_amount
               AND d."accountType" = 'A'
)
, matching_payments AS (
	SELECT *, 
        rank() OVER (Partition BY "referenceNo" ORDER BY any_sale_in_posting DESC, "postingTime") AS rn
	FROM etl_accts_window2
	WHERE have_matching_pay_payment
)
-- , matching_posting_ros as (
--   SELECT MAX("postingTime") AS mp_posting, "referenceNo" AS mp_ro 
--   FROM matching_payments GROUP BY "referenceNo"
-- )
, matching_payment_posting AS (
	SELECT m.*
	FROM matching_payments m 
    WHERE rn = 1
	--JOIN matching_posting_ros mr ON "referenceNo" = mp_ro
	--WHERE "postingTime" = mp_posting
)
, sale_acct_of_matching_posting AS (
    SELECT
        "referenceNo",
        array_agg("accountNumber")
            FILTER(WHERE "accountType" = 'S') AS sale_acct
    FROM matching_payment_posting
    GROUP BY "referenceNo"
)
, unmatch_pay_selectable_posting AS (
    SELECT acct.*, 1
    FROM etl_accts_window2 acct
        LEFT JOIN sale_acct_of_matching_posting mp USING ("referenceNo")
    WHERE NOT have_matching_pay_payment
        AND (mp."referenceNo" IS NULL OR any_sale_in_posting)
        AND (mp."referenceNo" IS NULL OR posting_time_count > 2)
        AND (mp.sale_acct IS NULL OR NOT (acct.acct_no_list_of_posting && mp.sale_acct))
        AND to_number(acct."postingAmount",'*********9.99') != 0
)
, selectable_postings AS (
	SELECT * FROM matching_payment_posting
	WHERE ("accountType" = 'A' AND account_posting_amount > 0)
	    OR "accountType" != 'A'
	UNION ALL
	SELECT * FROM unmatch_pay_selectable_posting
	WHERE ("accountType" = 'A' AND account_posting_amount > 0)
	    OR "accountType" != 'A'
)
, accs AS (
    SELECT
        d."referenceNo"                                               AS "RO#",
        d."accountNumber"                                       AS "Account#",
        d."accountType"                                         AS "Acc.Type(COA)",
        SUM(to_number(d."postingAmount",'*********9.99'))::text AS "postingAmount",
        d."postingTime"
    FROM selectable_postings d
    GROUP BY d."referenceNo", d."accountNumber", d."accountType", d."postingTime"
) 

, gl_data AS ( 
    SELECT DISTINCT
        coalesce("referenceNo", split_part("hostItemId", '*', 3)) AS ro_number,
        "accountNumber"                                     AS account_number,
        "companyId"                                         AS company_id,
        "postingAmount"::numeric     						AS posting_amount,
        "postingTime"                                       AS postingtime
    FROM selectable_postings
)
, gl_data_with_posting_amount AS(
    SELECT 
        ro_number,
        account_number, 
        (SUM(nullif((posting_amount)*100, 0))) AS posting_amount,
        company_id
    FROM gl_data GROUP BY ro_number, account_number, company_id
)
, coa_data AS ( 
    SELECT
        TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ', 'g'))             AS account_number,
        split_part(TRIM(regexp_replace("chain", E'[\\n\t\r]+', ' ', 'g')), '*', 2) AS chain,
        TRIM(regexp_replace("accountType", E'[\\n\t\r]+', ' ', 'g'))               AS account_type
    FROM coa
    WHERE (coa."accountDesc" NOT LIKE '%INV%' OR coa."accountDesc" LIKE '%CASH%')
          AND "accountDesc" NOT LIKE '%WORK-IN-PROCESS%' 
          AND "accountDesc" NOT LIKE '%WIP%' 
          AND  "accountDesc" NOT LIKE '%WORK IN PROCESS%'
    )
, coa_raw as( 
    SELECT 
        ro_number,
        account_number,
        account_type,
        SUM(sale)                                                 AS sale,
        Cost_account_no,
        Cost_account_type,
        sum(cost)                                                 AS cost,
        company_id
    FROM (
    SELECT
              gd.ro_number,
              coalesce(gd1.account_number, gd.account_number)     AS account_number,
              CASE 
		WHEN gd.posting_amount::numeric < 0 AND cd.account_type='C'
		THEN ABS(gd.posting_amount)
              WHEN gd1.account_number IS NULL AND cd.account_type='C'
                  THEN ABS(gd1.posting_amount)
                  WHEN gd1.account_number IS NULL
                  THEN ABS(gd.posting_amount)
              ELSE ABS(gd1.posting_amount) END                         AS sale,
              case when gd.account_number=gd1.account_number is NULL
                  then null
                  else gd.account_number
                  END                                             AS Cost_account_no,
              CASE 
              WHEN coalesce(cd.chain, '0') = cd.account_number
                  THEN '0'
              ELSE CASE 
		WHEN gd.posting_amount::numeric < 0 AND cd.account_type='C'
			THEN ABS(gd1.posting_amount)
                    WHEN gd1.account_number IS NULL AND cd.account_type='C'
                       THEN ABS(gd.posting_amount)
                       WHEN gd1.account_number IS NULL
                       THEN ABS(gd1.posting_amount)
                   ELSE ABS(gd.posting_amount) END
              END                                                 AS cost,
              CASE WHEN gd1.account_number IS NULL
                  THEN cd.account_type
                  when gd1.account_number IS not null and gd.account_number IS not null 
                  then 
                       cd1.account_type
              ELSE NULL END                                       AS account_type, 
              CASE WHEN gd1.account_number IS not NULL
                  THEN cd.account_type
               else null end                                      as  Cost_account_type,  
              gd.company_id 
          FROM coa_data cd
              JOIN gl_data_with_posting_amount gd USING (account_number)
              LEFT JOIN gl_data_with_posting_amount gd1 
                  ON cd.chain = gd1.account_number 
                      AND gd.ro_number = gd1.ro_number
                      AND gd1.account_number != cd.account_number
              LEFT JOIN coa_data cd1 
                  ON cd.chain = cd1.account_number 
                      AND gd1.ro_number IS NOT NULL
    ) t
    GROUP BY ro_number, account_number,Cost_account_no, account_type,Cost_account_type, company_id  
    ORDER BY CASE account_type
             WHEN 'S' 
                 THEN 1
             ELSE 2 END
)
, coa_final AS ( 
    SELECT c1.*
    FROM coa_raw c1
        JOIN coa_raw c2 USING (ro_number, account_number)
    WHERE c1.cost != c2.cost AND c1.cost = 0
)
SELECT DISTINCT coa_raw.ro_number,coa_raw.account_number, coa_raw.sale, coa_raw.cost, coa_raw.company_id, coa_raw.account_type
FROM coa_raw
    LEFT JOIN coa_final USING (ro_number, account_number, cost)
WHERE coa_final IS NULL;

-- GL ACCOUNTING

INSERT INTO repair_account_gldetails_tmp (timestampSequence, postingSequence, postingDate, journalId, accNo, accDesc, accType, incBalGrp, debit, credit, roNumber, postingTime)
SELECT 
    DENSE_RANK () OVER(ORDER BY REPLACE((ac."referenceNo")::text, '"',''),  
                                REPLACE((ac."postingTime")::text, '"','')) 
                                                                                               AS time_stamp_sequence,
    REPLACE((ac."postingSequence")::text, '"','')                                              AS line_index,
    to_char(REPLACE((ac."accountingDate")::text, '"','')::date,'MM-dd-YYYY')                   AS acc_date,
    REPLACE((ac."journalId")::text, '"','')                                                    AS jr_id,
    REPLACE((ac."accountNumber")::text, '"','')                                                AS acc_no,
    REPLACE((eac."accountDesc")::text, '"','')                                                 AS decr,
    REPLACE((eac."accountType")::text, '"','')                                                 AS acc_type,
    REPLACE((eac."incBalReportGrp")::text, '"','')                                             AS inc_bal_grp,
    CASE WHEN REPLACE((ac."postingAmount")::text, '"','')::numeric >0 
       THEN REPLACE((ac."postingAmount")::text, '"','')::numeric * -1
    ELSE null 
    END                                                                                        AS debit,
    CASE WHEN REPLACE((ac."postingAmount")::text, '"','')::numeric <0 
       THEN REPLACE((ac."postingAmount")::text, '"','')::numeric * -1
    ELSE null 
    END                                                                                        AS credit,
    REPLACE((ac."referenceNo")::text, '"','')                                                  AS ro_number,
    REPLACE((ac."postingTime")::text, '"','')                                                  AS posting_time
FROM du_dms_fortellis_model.etl_accounts   ac
    JOIN du_dms_fortellis_model.etl_account_coa eac 
        ON ac."accountNumber" = eac."accountNumber"  
ORDER BY REPLACE((ac."referenceNo")::text, '"',''),
                            REPLACE((ac."postingTime")::text, '"','');

CREATE OR REPLACE VIEW du_dms_fortellis_proxy.ro_status_view AS
SELECT DISTINCT replace((d."postingTime")::text, '"'::text, ''::text) AS "postingTime",
    replace((d."postingSequence")::text, '"'::text, ''::text) AS "postingSequence",
    replace((d."accountingDate")::text, '"'::text, ''::text) AS "accountingDate",
    replace((d."referenceNo")::text, '"'::text, ''::text) AS "RONo",
    replace((d."journalId")::text, '"'::text, ''::text) AS "journalId",
    replace((d."accountNumber")::text, '"'::text, ''::text) AS "AccountNo",
    replace((c."accountDesc")::text, '"'::text, ''::text) AS "accountDesc",
    replace((c."incBalReportGrp")::text, '"'::text, ''::text) AS "incBalReportGrp",
    replace((c."accountType")::text, '"'::text, ''::text) AS "AccType",
     CASE
            WHEN upper(replace((c."accountDesc")::text, '"'::text, ''::text)) ~~ '%INV'::text OR upper(replace((c."accountDesc")::text, '"'::text, ''::text)) ~~ '%INV%'::text OR upper(replace((c."accountDesc")::text, '"'::text, ''::text)) ~~ 'INV%'::text THEN 'N'::text
            WHEN to_number(REPLACE((d."postingAmount")::text,'"',''),'*********.99')=0 THEN 'N'
            WHEN count(f.*)=1  THEN 'Z'::text
            WHEN replace((c."accountType")::text, '"'::text, ''::text) = ANY (ARRAY['X'::text, 'S'::text, 'L'::text, 'I'::text, 'A'::text, 'E'::text]) THEN 'S'::text
            WHEN replace((c."accountType")::text, '"'::text, ''::text) = 'C'::text THEN 'C'::text
            ELSE NULL::text
    END AS newtype,
    CASE
        WHEN replace((c."accountType")::text, '"'::text, ''::text) = ANY (ARRAY['C'::text, 'A'::text, 'E'::text]) 
        THEN to_number(replace((d."postingAmount")::text, '"'::text, ''::text), '*********999.99'::text)
        ELSE NULL::numeric
    END AS "Debit",
    CASE
        WHEN replace((c."accountType")::text, '"'::text, ''::text) = ANY (ARRAY['X'::text, 'S'::text, 'L'::text, 'I'::text]) 
        THEN - to_number(replace((d."postingAmount")::text, '"'::text, ''::text), '*********999.99'::text)
        ELSE NULL::numeric
    END AS "Credit"
   FROM du_dms_fortellis_model.etl_accounts d
   JOIN du_dms_fortellis_model.etl_account_coa c 
       ON ((d."accountNumber")::text) = ((c."accountNumber")::text) 
   LEFT JOIN (select * from du_dms_fortellis_model.etl_accounts)AS f 
       ON replace((f."referenceNo")::text, '"'::text, ''::text) = replace((d."referenceNo")::text, '"'::text, ''::text) 
       AND replace((f."accountNumber")::text, '"'::text, ''::text) = replace((d."accountNumber")::text, '"'::text, ''::text)
       AND replace((f."postingTime")::text, '"'::text, ''::text) = replace((d."postingTime")::text, '"'::text, ''::text)
       AND to_number(replace((f."postingAmount")::text, '"'::text, ''::text), '*********.99'::text) = (- to_number(replace((d."postingAmount")::text, '"'::text, ''::text), '*********.99'::text))
  WHERE (replace((c."accountType")::text, '"'::text, ''::text) = ANY (ARRAY['S'::text, 'X'::text, 'L'::text, 'I'::text, 'C'::text, 'E'::text, 'A'::text]))
  GROUP BY replace((d."postingTime")::text, '"'::text, ''::text) ,
    replace((d."postingSequence")::text, '"'::text, ''::text),
    replace((d."accountingDate")::text, '"'::text, ''::text),
    replace((d."referenceNo")::text, '"'::text, ''::text) ,
    replace((d."journalId")::text, '"'::text, ''::text) ,
    replace((d."accountNumber")::text, '"'::text, ''::text) ,
    replace((c."accountDesc")::text, '"'::text, ''::text) ,
    replace((c."incBalReportGrp")::text, '"'::text, ''::text) ,
    replace((c."accountType")::text, '"'::text, ''::text),
    d."postingAmount" 
  ORDER BY (replace((d."referenceNo")::text, '"'::text, ''::text)), 
  (replace((d."postingTime")::text, '"'::text, ''::text)), 
  (replace((d."postingSequence")::text, '"'::text, ''::text));
  
