 BEGIN;
\pset pager off
SET client_min_messages TO WARNING;
    \o '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/gl_details.csv'
    COPY(WITH coa_details AS (SELECT
                                TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                                    'g')) AS accountnumber,
                                STRING_AGG(TRIM(regexp_replace("accountDesc", E'[\\n\t\r]+', ' ',
                                             'g')), ', ') AS accountdescription,
                                TRIM(regexp_replace("accountType", E'[\\n\t\r]+', ' ',
                                                    'g')) AS accounttype,
                                TRIM(regexp_replace("incBalReportGrp", E'[\\n\t\r]+', ' ',
                                                    'g')) AS incbalgrpdesc,
                                TRIM(regexp_replace("incBalReportSubGrp", E'[\\n\t\r]+', ' ',
                                                    'g')) AS incbalsubgrp
                            FROM etl_account_coa
                            GROUP BY "accountNumber", "accountType", "incBalReportGrp", "incBalReportSubGrp" 
        ),
            gl_details AS (SELECT
                            TRIM(regexp_replace("hostItemId", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS hostitemid,
                            TRIM(regexp_replace("accountingDate", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS accountingdate,
                            TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS accountnumber,
                            split_part("hostItemId", '*', 3) AS ro_number,
                            TRIM(regexp_replace("companyId", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS companyid,
                            TRIM(regexp_replace("controlNo", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS control,
                            TRIM(regexp_replace("journalId", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS journalid,
                            TRIM(regexp_replace("postingAmount", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS postingamount,
                            TRIM(regexp_replace("postingSequence", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS postingsequence,
                            TRIM(regexp_replace("postingTime", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS postingtime,
                            TRIM(regexp_replace("referenceNo", E'[\\n\t\r]+', ' ',
                                                'g'))                            AS refer
                        FROM etl_accounts 

            )
        SELECT
                hostitemid AS "host_item_id",
                ro_number AS "ro_number",
                accountingdate AS "accounting_date",
                accountnumber AS "account_number",
                accountdescription AS "acccount_description",
                accounttype AS "account_type",
                incbalgrpdesc AS "inc_bal_grp_desc",
                incbalsubgrp AS "inc_bal_sub_grp",
                companyid AS "company_id",
                control AS "control",
                journalid AS "journal_id",
                postingamount AS "posting_amount",
                postingsequence AS "posting_sequence",
                postingtime AS "posting_time"
                -- ,
                -- store_details.store_id
            FROM gl_details
                -- LEFT JOIN store_details 
                LEFT JOIN coa_details USING (accountnumber))
    TO STDOUT WITH (FORMAT CSV, HEADER TRUE );
COMMIT;