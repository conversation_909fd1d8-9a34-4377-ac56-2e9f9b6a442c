BEGIN;
\pset pager off
SET client_min_messages TO WARNING;
        \o '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/proxy_accounting.csv'
        COPY(WITH pay_summary AS (
            SELECT "roNumber"::text, SUM("paymentAmount"::numeric) AS "paymentAmount"
                FROM du_dms_fortellis_model.etl_pay
                WHERE "insuranceFlag" = 'N'
                GROUP BY "roNumber"
            )
            , coa AS (
             SELECT
                    (ARRAY_AGG("accountType"))[1]               AS "accountType",
                    STRING_AGG("accountDesc", ', ')  AS "accountDesc",
                    "accountNumber"                             AS "accountNumber",
                    (ARRAY_AGG("chain"))[1]                     AS "chain"
                FROM du_dms_fortellis_model.etl_account_coa GROUP BY "accountNumber"
            )
            , etl_acct_details AS (
                SELECT
                    d."hostItemId"          AS "hostItemId",
                    d."accountingDate"      AS "accountingDate",
                    d."accountNumber"       AS "accountNumber",
                    d."companyId"           AS "companyId",
                    d."controlNo"             AS "controlNo",
                    d."controlNo2"            AS "controlNo2",
                    d."controlTypeCode"         AS "controlTypeCode",
                    d."controlType2Code"        AS "controlType2Code",
                    d."currentMonth"        AS "currentMonth",
                    d."detailDescription"   AS "detailDescription",
                    d."distillControlTypeCode"  AS "distillControlTypeCode",
                    d."distillControlType2Code" AS "distillControlType2Code",
                    d."journalId"           AS "journalId",
                    d."postingAmount"       AS "postingAmount",
                    d."postingSequence"     AS "postingSequence",
                    d."postingTime"         AS "postingTime",
                    d."productivityNo"              AS "productivityNo",
                    d."productivityNo2"             AS "productivityNo2",
                    d."productivityNoType"            AS "productivityNoType",
                    d."productivityNoType2"           AS "productivityNoType2",
                    d."referenceNo"               AS "referenceNo",
                    d."scheduleNumber"      AS "scheduleNumber",
                    d."statCount"           AS "statCount",
                    coa."accountType"
                FROM du_dms_fortellis_model.etl_accounts d
                    JOIN coa
                        ON (d."accountNumber"::text) = (coa."accountNumber")
                --WHERE replace((d."referenceNo"::json -> '_text'::text)::text, '"'::text, ''::text) = '117286' --'457014'--'457012'--'457023' --'456989'
            )
            , etl_acct_details_window1 AS (
                SELECT d."hostItemId",
                    d."accountingDate",
                    d."accountNumber",
                    d."companyId",
                    d."controlNo",
                    d."controlNo2",
                    d."controlTypeCode",
                    d."controlType2Code",
                    d."currentMonth",
                    d."detailDescription",
                    d."distillControlTypeCode",
                    d."distillControlType2Code",
                    d."journalId",
                    d."postingAmount",
                    d."postingSequence",
                    d."postingTime",
                    d."productivityNo",
                    d."productivityNo2",
                    d."productivityNoType",
                    d."productivityNoType2",
                    d."referenceNo",
                    d."scheduleNumber",
                    d."statCount",
                    d."accountType",
                    COUNT(*) OVER(Partition By d."referenceNo", d."postingTime")                       AS posting_time_count,
                    bool_or(d."accountType" = 'S')
                        OVER(Partition By d."referenceNo", d."postingTime")                            AS any_sale_in_posting,
                    array_agg(CASE WHEN (d."accountType" = 'S' AND to_number("postingAmount",'**********.99') != 0)
                                    OR d."accountType" != 'S'
                                THEN "accountNumber" END)
                        OVER(Partition By d."referenceNo", d."postingTime")                            AS acct_no_list_of_posting,
                    SUM("postingAmount"::numeric) 
                        OVER(Partition By d."referenceNo", d."accountNumber", d."postingTime")         AS account_posting_amount
                FROM etl_acct_details d
                    
            )
            ,etl_acct_details_window2 AS (
                SELECT *,
                bool_or(ps."roNumber" IS NOT NULL)
                    OVER(Partition By d."referenceNo", d."postingTime")                  AS have_matching_pay_payment
                FROM etl_acct_details_window1 d
                    LEFT JOIN pay_summary ps
                        ON ps."roNumber" = d."referenceNo"
                        AND ps."paymentAmount" = d.account_posting_amount
                        AND d."accountType" = 'A'
            )
            , matching_payments AS (
                SELECT *, 
                    rank() OVER (Partition BY "referenceNo" ORDER BY any_sale_in_posting DESC, "postingTime") AS rn
                FROM etl_acct_details_window2
                WHERE have_matching_pay_payment
            )
            -- , matching_posting_ros as (
            --   SELECT MAX("postingTime") AS mp_posting, "referenceNo" AS mp_ro 
            --   FROM matching_payments GROUP BY "referenceNo"
            -- )
            , matching_payment_posting AS (
                SELECT m.*
                FROM matching_payments m 
                WHERE rn = 1
                --JOIN matching_posting_ros mr ON "referenceNo" = mp_ro
                --WHERE "postingTime" = mp_posting
            )
            , sale_acct_of_matching_posting AS (
                SELECT
                    "referenceNo",
                    array_agg("accountNumber")
                        FILTER(WHERE "accountType" = 'S') AS sale_acct
                FROM matching_payment_posting
                GROUP BY "referenceNo"
            )
            , unmatch_pay_selectable_posting AS (
                SELECT acct.*, 1
                FROM etl_acct_details_window2 acct
                    LEFT JOIN sale_acct_of_matching_posting mp USING ("referenceNo")
                WHERE NOT have_matching_pay_payment
                    AND (mp."referenceNo" IS NULL OR any_sale_in_posting)
                    AND (mp."referenceNo" IS NULL OR posting_time_count > 2)
                    AND (mp.sale_acct IS NULL OR NOT (acct.acct_no_list_of_posting && mp.sale_acct))
                    AND to_number(acct."postingAmount",'**********.99') != 0
            )
            , selectable_postings AS (
                SELECT * FROM matching_payment_posting
                WHERE ("accountType" = 'A' AND account_posting_amount > 0)
                    OR "accountType" != 'A'
                UNION ALL
                SELECT * FROM unmatch_pay_selectable_posting
                WHERE ("accountType" = 'A' AND account_posting_amount > 0)
                    OR "accountType" != 'A'
            )
            , acc_details AS (
                SELECT
                    d."referenceNo"                                               AS "RO#",
                    d."accountNumber"                                       AS "Account#",
                    d."accountType"                                         AS "Acc.Type(COA)",
                    SUM(to_number(d."postingAmount",'**********.99'))::text AS "postingAmount",
                    d."postingTime"
                FROM selectable_postings d
                GROUP BY d."referenceNo", d."accountNumber", d."accountType", d."postingTime"
            ) 

            , gl_data AS ( 
                SELECT DISTINCT
                    coalesce("referenceNo", split_part("hostItemId", '*', 3)) AS ro_number,
                    "accountNumber"                                     AS account_number,
                    "companyId"                                         AS company_id,
                    "postingAmount"::numeric     						AS posting_amount,
                    "postingTime"                                       AS posting_time
                FROM selectable_postings
            )
            , gl_data_with_posting_amount AS(
                SELECT 
                    ro_number,
                    account_number, 
                    ABS(SUM(nullif((posting_amount)*100, 0))) AS posting_amount,
                    company_id,
                    STRING_AGG(posting_time, ', ') AS posting_time
                FROM gl_data GROUP BY ro_number, account_number, company_id
            )
            , coa_data AS ( 
                SELECT
                    TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ', 'g'))             AS account_number,
                    split_part(TRIM(regexp_replace("chain", E'[\\n\t\r]+', ' ', 'g')), '*', 2) AS chain,
                    TRIM(regexp_replace("accountType", E'[\\n\t\r]+', ' ', 'g'))               AS account_type
                FROM coa
                WHERE (coa."accountDesc" NOT LIKE '%INV%' OR coa."accountDesc" LIKE '%CASH%')
                    AND "accountDesc" NOT LIKE '%WORK-IN-PROCESS%' 
                    AND "accountDesc" NOT LIKE '%WIP%' 
                    AND  "accountDesc" NOT LIKE '%WORK IN PROCESS%'
                )
            , coa_raw as( 
                SELECT 
                    ro_number,
                    account_number,
                    account_type,
                    SUM(sale)                                                 AS sale,
                    Cost_account_no,
                    Cost_account_type,
                    sum(cost)                                                 AS cost,
                    company_id,
 				    STRING_AGG(posting_time, ', ')                         AS posting_time
                FROM (
                SELECT
                        gd.ro_number,
                        coalesce(gd1.account_number, gd.account_number)     AS account_number,
                        CASE 
                        WHEN gd1.account_number IS NULL AND cd.account_type='C'
                            THEN gd1.posting_amount
                            WHEN gd1.account_number IS NULL
                            THEN gd.posting_amount
                        ELSE gd1.posting_amount END                         AS sale,
                        case when gd.account_number=gd1.account_number is NULL
                            then null
                            else gd.account_number
                            END                                             AS Cost_account_no,
                        CASE 
                        WHEN coalesce(cd.chain, '0') = cd.account_number
                            THEN '0'
                        ELSE CASE 
                                WHEN gd1.account_number IS NULL AND cd.account_type='C'
                                THEN gd.posting_amount
                                WHEN gd1.account_number IS NULL
                                THEN gd1.posting_amount
                            ELSE gd.posting_amount END
                        END                                                 AS cost,
                        CASE WHEN gd1.account_number IS NULL
                            THEN cd.account_type
                            when gd1.account_number IS not null and gd.account_number IS not null 
                            then 
                                cd1.account_type
                        ELSE NULL END                                       AS account_type, 
                        CASE WHEN gd1.account_number IS not NULL
                            THEN cd.account_type
                        else null end                                      as  Cost_account_type,  
                        gd.company_id,
                        gd.posting_time					
                    FROM coa_data cd
                        JOIN gl_data_with_posting_amount gd USING (account_number)
                        LEFT JOIN gl_data_with_posting_amount gd1 
                            ON cd.chain = gd1.account_number 
                                AND gd.ro_number = gd1.ro_number
                                 AND gd.company_id = gd1.company_id
                                AND gd1.account_number != cd.account_number
                        LEFT JOIN coa_data cd1 
                            ON cd.chain = cd1.account_number 
                                AND gd1.ro_number IS NOT NULL
                ) t
                GROUP BY ro_number, account_number,Cost_account_no, account_type,Cost_account_type, company_id  
                ORDER BY CASE account_type
                        WHEN 'S' 
                            THEN 1
                        ELSE 2 END
            )
            , coa_final AS ( 
                SELECT c1.*
                FROM coa_raw c1
                    JOIN coa_raw c2 USING (ro_number, account_number)
                WHERE c1.cost != c2.cost AND c1.cost = 0
            )
            SELECT DISTINCT coa_raw.ro_number,coa_raw.account_number, coa_raw.sale, coa_raw.cost, coa_raw.company_id, coa_raw.account_type, coa_raw.posting_time
            FROM coa_raw
                LEFT JOIN coa_final USING (ro_number, account_number, cost)
            WHERE coa_final IS NULL)
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );


        -- CREATE TABLE IF NOT EXISTS process_data.payment as
        \o '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/payment.csv'
        COPY(
            -- with payment_details as (
                SELECT 
            "roNumber" AS ro_number,
            "code" AS payment_code,
            "paymentAmount" AS payment_amount,
            CASE WHEN "insuranceFlag" = 'Y' THEN true ELSE false END AS is_insurance
            FROM du_dms_fortellis_model.etl_pay --WHERE "roNumber" in (SELECT ro_number FROM process_data.exception)
            -- )
        )
        -- SELECT * FROM payment_details)
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );        
        -- CREATE TABLE IF NOT EXISTS process_data.coa_details as
        \o '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/coa_details.csv'
        COPY(
            -- with coa_details as(
            SELECT
            "accountType"        AS "accountType",
            "accountDesc" AS "accountDesc",
            "accountNumber"      AS "accountNumber",
            "chain"              AS "chain"
        FROM du_dms_fortellis_model.etl_account_coa
        )
        -- select *
        -- from coa_details)
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

        \o '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/ro_total.csv'
        COPY(
            SELECT * FROM  du_dms_fortellis_model.ro_total 
            )
            TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

        \o '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/gl_missing.csv'
        COPY(
--SELECT "roNumber", "sequenceNo", "lineCode", "laborSequenceNo", "desc", "source", bin1, "employeeId", "outsideSalesmanId", "specialStatus", "compLineCode", "qtyOrdered", "qtySold", "qtyOnHand", "qtyFilled", "qtyBackOrdered", "laborType", "mcdPercentage", "extendedSale", "extendedCost", sale, "cost", list, "unitServiceCharge", comp, "coreSale", "coreCost", "number", "partClass"
--FROM du_dms_fortellis_model.etl_parts;

            WITH proxy_data AS (  SELECT
                            lbr."roNumber"                                           AS ro_number,
                            sum(coalesce(nullif("sale", '') :: numeric, 0)) * 100 AS sale_amount,
                            sum(coalesce(nullif("cost", '') :: numeric, 0)) * 100 AS cost_amount,
                            left(lbr."type", 1)                              AS prt_billing_type
                        FROM du_dms_fortellis_model.etl_labor lbr
                            JOIN du_dms_fortellis_model.etl_job job
                                ON lbr."roNumber" = job."roNumber"
                                    AND lbr."lineCode" = job."lineCode"
                        GROUP BY lbr."roNumber", left(lbr."type", 1)
                        HAVING sum(coalesce(nullif("sale", '') :: numeric, 0)) * 100 != 0
                OR sum(coalesce(nullif("cost", '') :: numeric, 0)) * 100 != 0
    )
    ,part_data AS (  SELECT
                            "roNumber"                                                     AS ro_number,
                            left("laborType", 1)                                        AS prt_billing_type,
                            sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                                coalesce(nullif(trim("sale"), '') :: numeric, 0)) * 100 AS sale_amount,
                            sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                                coalesce(nullif(trim("cost"), '') :: numeric, 0)) * 100 AS cost_amount
                        FROM du_dms_fortellis_model.etl_parts
                        GROUP BY "roNumber", left("laborType", 1)
                        HAVING  sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                                coalesce(nullif(trim("sale"), '') :: numeric, 0)) * 100 != 0
                OR sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                                coalesce(nullif(trim("cost"), '') :: numeric, 0)) * 100 != 0
    )
    ,tax_data AS ( SELECT
                        "roNumber"                       AS ro_number,
                        left("payType", 1)            AS prt_billing_type,
                        sum("roTax" :: numeric) * 100 AS sale_amount
                    FROM du_dms_fortellis_model.etl_total
                    GROUP BY "roNumber", left("payType", 1)
                    HAVING sum("roTax" :: numeric) * 100 != 0
    )

    SELECT eh."roNumber", "openDate", "closedDate", "voidedDate", ev."make" FROM du_dms_fortellis_model.etl_head eh
    JOIN du_dms_fortellis_model.etl_vehicle ev 
                                ON eh."roNumber" = ev."roNumber"
            WHERE "closedDate" IS NOT NULL AND 
            eh."roNumber" NOT IN(SELECT split_part("hostItemId", '*', 3) 
            FROM du_dms_fortellis_model.etl_accounts)
                                AND  (eh."roNumber" IN(SELECT ro_number FROM proxy_data)
                OR eh."roNumber" IN(SELECT ro_number FROM part_data)
                OR eh."roNumber" IN(SELECT ro_number FROM tax_data)))
        TO STDOUT WITH (FORMAT CSV, HEADER TRUE );


COMMIT;
