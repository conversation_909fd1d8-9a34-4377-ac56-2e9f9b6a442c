

DROP SCHEMA IF EXISTS du_etl_load_exception CASCADE; 
CREATE SCHEMA  IF NOT EXISTS du_etl_load_exception;

DROP TABLE IF EXISTS du_etl_load_exception.proxy_accounting;
DROP TABLE IF EXISTS du_etl_load_exception.exception;
DROP TABLE IF EXISTS du_etl_load_exception.coa_details;
DROP TABLE IF EXISTS du_etl_load_exception.gl_details;
DROP TABLE IF EXISTS du_etl_load_exception.payment;
DROP TABLE IF EXISTS du_etl_load_exception.ro_total;

CREATE TABLE IF NOT EXISTS du_etl_load_exception.proxy_accounting
(
    -- store_id       text,
    ro_number      text,
    account_number text, 
    sale           text, 
    cost           text, 
    company_id     text, 
    account_type   text,
    posting_time   text    
        
    
);
CREATE TABLE IF NOT EXISTS du_etl_load_exception.exception
(
    -- store_id       text,
    ro_number      text,
    cust_pay       text,
    no_sale        text,
    cost_anomaly   text,
    amount_mismatch text,
    json_data      json,
    text_proxy     text     
    
);

CREATE TABLE IF NOT EXISTS du_etl_load_exception.coa_details
(
    -- store_id             text,
    "AccountType"        text,
    "AccountDescription" text,
    "AccountNumber"      text,
    "Chain"              text    
    
);

CREATE TABLE IF NOT EXISTS du_etl_load_exception.gl_details
(
    -- store_id               text,
    "host_item_id"         text,
    "ro_number"            text,
    "accounting_date"      text,
    "account_number"       text,
    "acccount_description" text,
    "account_type"         text,
    "inc_bal_grp_desc"     text,
    "inc_bal_sub_grp"      text,
    "company_id"           text,
    "control"              text,
    "journal_id"           text,
    "posting_amount"       text,
    "posting_sequence"     text,
    "posting_time"         text    
    
);

CREATE TABLE IF NOT EXISTS du_etl_load_exception.payment
(
    ro_number      text,
    payment_code   text,
    payment_amount text,
    is_insurance   boolean
    -- ,
    -- store_id       text     
    
);

CREATE TABLE IF NOT EXISTS du_etl_load_exception.ro_total
(
ro_number                    text,
customer_lbrcost             text,
customer_lbrsale             text,
internal_lbrcost             text,
internal_lbrsale             text,
warranty_lbrcost             text,
warranty_lbrsale             text,
customer_partcost            text,
customer_partsale            text,
internal_partcost            text,
internal_partsale            text,
warranty_partcost            text,
warranty_partsale            text,
cust_misc_cost               text,
cust_misc_sale               text,
internal_misc_cost           text,
internal_misc_sale           text,
warranty_misc_cost           text,
warranty_misc_sale           text,
cust_sublet_cost             text,
cust_sublet_sale             text,
internal_sublet_cost         text,
internal_sublet_sale         text,
warranty_sublet_cost         text,
warranty_sublet_sale         text,
cust_tax_cost                text,
cust_tax_sale                text,
internal_tax_cost            text,
internal_tax_sale            text,
warranty_tax_cost            text,
warranty_tax_sale            text,
cust_gog_cost                text,
cust_gog_sale                text,
internal_gog_cost            text,
internal_gog_sale            text,
warranty_gog_cost            text,
warranty_gog_sale            text,
cust_deduct_sale             text,
internal_deduct_sale         text,
warranty_deduct_sale         text,
cust_othcost                 text,
cust_othsale                 text,
internal_othcost             text,
internal_othsale             text,
warranty_othcost             text,
warranty_othsale             text,
customer_discount            text,
internal_discount            text,
warranty_discount            text
);

CREATE TABLE IF NOT EXISTS du_etl_load_exception.gl_missing
(
    ro_number      text,  
    open_date      text, 
    closed_date    text, 
    void_date      text, 
    make           text
    
);
