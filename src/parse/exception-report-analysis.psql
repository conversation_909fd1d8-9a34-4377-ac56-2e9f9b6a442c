BEGIN;
\pset pager off
SET client_min_messages TO WARNING;

\ir './create-scheduler-schema.psql' 
        
\copy du_etl_load_exception.proxy_accounting FROM '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/proxy_accounting.csv' WITH (FORMAT csv, HEADER true)
\copy du_etl_load_exception.exception FROM '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/exception.csv' WITH (FORMAT csv, HEADER true)
\copy du_etl_load_exception.coa_details FROM '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/coa_details.csv' WITH (FORMAT csv, HEADER true)
\copy du_etl_load_exception.gl_details FROM '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/gl_details.csv' WITH (FORMAT csv, HEADER true)
\copy du_etl_load_exception.payment FROM '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/payment.csv' WITH (FORMAT csv, HEADER true)
\copy du_etl_load_exception.ro_total FROM '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/ro_total.csv' WITH (FORMAT csv, HEADER true)
\copy du_etl_load_exception.gl_missing FROM '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception_tables/gl_missing.csv' WITH (FORMAT csv, HEADER true)


        INSERT INTO process_data.store (store_name, dealer_id, project_id, secondary_project_id, parts_project_id, labor_project_id, created_on)
            VALUES ( :'store_name', :'dealer_id', :'project_id', :'sec_project_id', :'project_id', :'sec_project_id', NOW());

        INSERT INTO process_data.proxy_accounting 
        WITH
        store_details AS (
           SELECT store_id 
            FROM process_data.store 
            WHERE dealer_id=:'dealer_id'
            ORDER BY created_on DESC LIMIT 1
            ),
        accounting AS
            (SELECT * FROM du_etl_load_exception.proxy_accounting)

        SELECT store_details.store_id,accounting.*
        FROM accounting,store_details;


        INSERT INTO process_data.exception (store_id, ro_number, cust_pay, no_sale, cost_anomaly, amount_mismatch, json_data, text_proxy)
        WITH
        store_details AS (
           SELECT store_id 
            FROM process_data.store 
            WHERE dealer_id=:'dealer_id'
            ORDER BY created_on DESC LIMIT 1
            ),
        exceptions AS
            (SELECT ro_number,
                    cust_pay,
                    no_sale,
                    cost_anomaly,
                    amount_mismatch,
                    json_data,
                    text_proxy
             FROM du_etl_load_exception.exception)

        SELECT store_details.store_id,exceptions.*
        FROM exceptions,store_details;

        INSERT INTO process_data.coa_details         
        WITH
        store_details AS (
           SELECT store_id 
            FROM process_data.store 
            WHERE dealer_id=:'dealer_id'
            ORDER BY created_on DESC LIMIT 1
            ),
        coa_details AS
            (SELECT * FROM du_etl_load_exception.coa_details)

        SELECT store_details.store_id,coa_details.*
        FROM coa_details,store_details;

        INSERT INTO process_data.gl_details         
        WITH
        store_details AS (
           SELECT store_id 
            FROM process_data.store 
            WHERE dealer_id=:'dealer_id'
            ORDER BY created_on DESC LIMIT 1
            ),
        gl_details AS
            (SELECT * FROM du_etl_load_exception.gl_details)

        SELECT store_details.store_id,gl_details.*
        FROM gl_details,store_details;

        INSERT INTO process_data.payment         
        WITH
        store_details AS (
           SELECT store_id 
            FROM process_data.store 
            WHERE dealer_id=:'dealer_id'
            ORDER BY created_on DESC LIMIT 1
            ),
        payment AS
            (SELECT * FROM du_etl_load_exception.payment)

        SELECT store_details.store_id,payment.*
        FROM payment,store_details;

        INSERT INTO process_data.ro_total (store_id, ro_number, customer_lbrcost, customer_lbrsale, internal_lbrcost,
            internal_lbrsale, warranty_lbrcost, warranty_lbrsale, customer_partcost, customer_partsale,
            internal_partcost, internal_partsale, warranty_partcost, warranty_partsale, customer_misc_cost, customer_misc_sale,
            internal_misc_cost, internal_misc_sale, warranty_misc_cost, warranty_misc_sale, customer_sublet_cost,
            customer_sublet_sale, internal_sublet_cost, internal_sublet_sale, warranty_sublet_cost, warranty_sublet_sale,
            customer_tax_cost, customer_tax_sale, internal_tax_cost, internal_tax_sale, warranty_tax_cost, warranty_tax_sale,
            customer_gog_cost, customer_gog_sale, internal_gog_cost, internal_gog_sale, warranty_gog_cost, warranty_gog_sale,
            customer_deduct_sale, internal_deduct_sale, warranty_deduct_sale, customer_othcost, customer_othsale, internal_othcost,
            internal_othsale, warranty_othcost, warranty_othsale,customer_discount, internal_discount, 
            warranty_discount)        
        WITH
        store_details AS (
           SELECT store_id 
            FROM process_data.store 
            WHERE dealer_id=:'dealer_id'
            ORDER BY created_on DESC LIMIT 1
            ),
        ro_total AS
            (SELECT ro_number,
                    customer_lbrcost,
                    customer_lbrsale,
                    internal_lbrcost,
                    internal_lbrsale,
                    warranty_lbrcost,
                    warranty_lbrsale,
                    customer_partcost,
                    customer_partsale,
                    internal_partcost,
                    internal_partsale,
                    warranty_partcost,
                    warranty_partsale,
                    cust_misc_cost,
                    cust_misc_sale,
                    internal_misc_cost,
                    internal_misc_sale,
                    warranty_misc_cost,
                    warranty_misc_sale,
                    cust_sublet_cost,
                    cust_sublet_sale,
                    internal_sublet_cost,
                    internal_sublet_sale,
                    warranty_sublet_cost,
                    warranty_sublet_sale,
                    cust_tax_cost,
                    cust_tax_sale,
                    internal_tax_cost,
                    internal_tax_sale,
                    warranty_tax_cost,
                    warranty_tax_sale,
                    cust_gog_cost,
                    cust_gog_sale,
                    internal_gog_cost,
                    internal_gog_sale,
                    warranty_gog_cost,
                    warranty_gog_sale,
                    cust_deduct_sale,
                    internal_deduct_sale,
                    warranty_deduct_sale,
                    cust_othcost,
                    cust_othsale,
                    internal_othcost,
                    internal_othsale,
                    warranty_othcost,
                    warranty_othsale,
                    customer_discount,
                    internal_discount,
                    warranty_discount
             FROM du_etl_load_exception.ro_total)

        SELECT store_details.store_id,ro_total.*
        FROM ro_total,store_details;

        INSERT INTO process_data.gl_missing         
        WITH
        store_details AS (
           SELECT store_id 
            FROM process_data.store 
            WHERE dealer_id=:'dealer_id'
            ORDER BY created_on DESC LIMIT 1
            ),
        gl_missing AS
            (SELECT * FROM du_etl_load_exception.gl_missing)

        SELECT store_details.store_id,gl_missing.*
        FROM gl_missing,store_details;

COMMIT;