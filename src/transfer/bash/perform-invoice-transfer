#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

# Encapsulates various named transfer scripts.
# 1) Transfer Name
# 2) Source File
# 3) Group ID if Required
# 4) Store ID if Required
#
# Target locations are found via envrionment variables
#

set -o errexit

INV_TARGET="${DU_ETL_GGS_INV_REMOTE:?Please define DU_ETL_GGS_INV_REMOTE}"
INV_DIST="${DU_ETL_PROD_DROPBOX:?Please define DU_ETL_PROD_DROPBOX}"/Imports/"_All Invoices"
INV_LOCAL="$DU_ETL_WORK"/transfer-archive
INV_ARG=invoice

mkdir -p "$INV_LOCAL"

[[ -f "$2" ]]        || die "Non-existant file specified: $2"

[[ -e $INV_DIST ]]   || die "Invoice Distribution Directory Not Present! $INV_DIST"

[[ -e $INV_LOCAL ]]  || die "Invoice Local Archive Directory Not Present! $LOC_DIST"

[[ -e $INV_TARGET ]] || die "Invoice Target Directory Not Present! $INV_TARGET"

fullname=$(readlink -m "$2")
filename=$(basename "$2")
fileext="${filename##*.}"

ls -laho "$fullname"

echo "$fullname"
echo "$filename"
echo $fileext

IS_ZIP=false
case $fileext in
    [zZ][iI][pP]) IS_ZIP=true; ;;
esac

if [[ $1 = $INV_ARG ]]; then
    echo "Copying Locally"
    cp --verbose "$fullname" "$INV_LOCAL" || die "Local Copy Failed!"

    STOREGROUP="${3:?Group Identifier Required}"
    STORECODE="${4:?Store Identifier Required}"
    REMOTE_LOCATION="$INV_TARGET"/"$STOREGROUP"/"$STORECODE"
    mkdir --parents "${REMOTE_LOCATION}"

    if [[ $IS_ZIP = true ]]; then
        unzip -j -u "$fullname" -d "$REMOTE_LOCATION"
    else
        echo "Copying to Target"
        cp "$fullname" "$REMOTE_LOCATION"
    fi
    [[ "$?" = 0 ]] || die "Target Action Failed!"

    ls -laho "$REMOTE_LOCATION"

    echo "Copying to Distribution"
    cp "$fullname" "$INV_DIST" || die "Distribution Copy Failed!"

    echo "Remove Source"
    rm "$fullname"

    say "Invoice File(s) Ready for Import (use template:MAGE:FIXEDOPS_SERVICE_INV_LOADER)"
else
    die "Unrecognized transfer name: $1 - expecting [$INV_ARG]"
    exit 1
fi

exit
