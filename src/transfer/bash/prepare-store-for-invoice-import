#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

SERVICE_DEFAULT=${GGS_PROD_PG_SERVICE}
TARGET_DEFAULT=${DU_ETL_GGS_INV_REMOTE:?Please define DU_ETL_GGS_INV_REMOTE}
DO_CONNECT=false
DO_SETUP_ONLY=false
DMS=
GROUPID=
STOREPART=
TARGET_BASE_DIR=
SOURCE_UPLOAD_FILE=

IMPORT_SCRIPT="$DU_ETL_HOME"/src/transfer/bash/perform-invoice-transfer

timeout 3s test -f "$DU_ETL_GGS_INV_REMOTE"/README
dirtest=$?
if [[ "$dirtest" = '124' ]]; then
    die "Failed to connect test for $DU_ETL_GGS_INV_REMOTE/README in three seconds"
elif [[ "$dirtest" = '1' ]]; then
    die "Remote directory $DU_ETL_GGS_INV_REMOTE/README does not exist!"
fi

say "Remote Invoice Staging Location @ $DU_ETL_GGS_INV_REMOTE"

function usage_die() {
    scream "$@"
    usage
    exit
}

function usage() {
pre_usage "Sets up variable and provides for transferring invoice source files.  Actual import manually performed via GGS."
    cat <<TXT
Requires all of:
    --dms   {}      | -d {}      The DMS whose variables will need to be linked in
    --group {}      | -g {}      The store group to which we are going to upload (the PIMS in [PIMS]STORE)
    --store {}      | -s {}      The store to which we are going to upload (the STORE in [PIMS]STORE)

As well as a scope of work:
    --upload {}     | -u {}      Uploads (and optionally unzips) the named file in the target directory
                                   and, by default, places a copy of it in both a local and remote dropbox.
    --file {}       | -f {}      Alias for --upload
    --setup-only    |            Sets up the variables for the given store but doesn't transfer any data.
                                   The existing do-transfer command can manually be used to accomplish that later.

OR one of the following actions
    --connect       |            Attempts to connect the remote target location so that the link to TARGET_BASE_DIR resolves.

DMS Specific Values:
    --autosoft-name |            Sets the name of the store; it serves as a page header for the invoices.

System Overrides:
    --db-service {} |            Overrides the default value used for psql service identification
                                   Default: $SERVICE_DEFAULT
    --target-dir {} |            Overrides the base directory location under which group and store directories are located
                                   Default: $TARGET_DEFAULT
TXT
post_usage
}

[ $# -eq 0 ] && { usage; exit 1; }

while [ $# -gt 0 ]; do
    case "$1" in
        --dms|-d)
            DMS="${2:?DMS Required}"
            shift
            ;;
        --group|-g)
            GROUPID="${2:?Key Required}"
            shift
            ;;
        --store|-s)
            STOREPART="${2:?Store Required}"
            shift
            ;;
        --upload|-u|--file|-f)
            SOURCE_UPLOAD_FILE="${2:?File Path Required}"
            shift
            ;;
        --db-service)
            SERVICE_NAME="${2:?Service Name Required}"
            shift
            ;;
        --target-dir)
            TARGET_LOCATION="${2:?Target Location Required}"
            shift
            ;;
        --connect)
            DO_CONNECT=true
            ;;
        --setup-only)
            DO_SETUP_ONLY=true
            ;;
        --autosoft-name)
            AUTOSOFT_STORE_NAME="${2:?Name Required}"
            shift
            ;;
        * )
            usage
            die "Unrecognized option $1; exiting";
            ;;
    esac
    shift
done

PSQL_SERVICE="service=${SERVICE_NAME:-${SERVICE_DEFAULT}}"
TARGET_BASE_DIR=${TARGET_LOCATION:-${TARGET_DEFAULT}}
STOREID="[${GROUPID}]${STOREPART}"

function psql_do() {
    psql "$PSQL_SERVICE" "$@"
}

if [[ "$DO_CONNECT" = true ]]; then
    [[ -z "$TARGET_LOCATION" ]] || die "Can only --connect the default target location"

    say "Attempting Connection SSHFS du-client to $TARGET_BASE_DIR link target."
    sshfs du-client:/home/<USER>/invoicestaging/ \
          /mnt/du-client-invoicestaging \
          || yell "Could not connect to default remote target location"

    "$IMPORT_SCRIPT" connect \
          || yell "Could not connect to default remote source location"

    exit
fi

[[ -n "$DMS" ]]       || usage_die "Must specify --dms|-d"
[[ -n "$GROUPID" ]]   || usage_die "Must specify --group|-g"
[[ -n "$STOREPART" ]] || usage_die "Must specify --store|-s"

case "$DMS" in
    'Reynolds')
        psql_do --set=sg_id="$GROUPID" <<SQL
INSERT INTO storegroupmap (sgv_id, sg_id, v_id, sgv_value)
SELECT * FROM (
    SELECT sg_id || '-' || v_id AS sgv_id, sg_id, v_id, sgv_value
    FROM ( VALUES
          (:'sg_id', 'RegEx-FixedOpsServiceInvoiceNumber', '^.+(?:\d{2}/\d{2}/\d{2}\s+\w\w\+\w)(\S+)(?:.*)$')
        , (:'sg_id', 'RegExLibrary-DMS-PageWrapper', '%[RegExLibraryItem-Reynolds-PageWrapper]%')
        , (:'sg_id', 'RegExLibrary-DMSSpoolerCaptureCleanup', 'RegExLibrary-ReynoldsSpoolerCleanup')
    ) vals (sg_id, v_id, sgv_value)
) final
WHERE NOT EXISTS (SELECT 1 FROM storegroupmap sgm WHERE final.sgv_id = sgm.sgv_id)
RETURNING sgv_id, sgv_value
;
SQL
        ;;
    'DealerTrack')
        psql_do --set=sg_id="$GROUPID" <<SQL
INSERT INTO storegroupmap (sgv_id, sg_id, v_id, sgv_value)
SELECT * FROM (
    SELECT sg_id || '-' || v_id AS sgv_id, sg_id, v_id, sgv_value
    FROM ( VALUES
          (:'sg_id', 'RegEx-FixedOpsServiceInvoiceNumber', '^.*?\d{1,2}/\d{1,2}/\d{2}\x20+(\d+)/\d{1,2}\x20*$')
        , (:'sg_id', 'RegExLibrary-DMS-PageWrapper', '%[RegExLibraryItem-DealerTrack-PageWrapper]%')
        , (:'sg_id', 'RegExLibrary-DMSSpoolerCaptureCleanup', 'RegExLibrary-DealerTrackSpoolerCleanup')
    ) vals (sg_id, v_id, sgv_value)
) final
WHERE NOT EXISTS (SELECT 1 FROM storegroupmap sgm WHERE final.sgv_id = sgm.sgv_id)
RETURNING sgv_id, sgv_value
;
SQL
        ;;

    'ADP')
        psql_do --set=sg_id="$GROUPID" <<SQL
INSERT INTO storegroupmap (sgv_id, sg_id, v_id, sgv_value)
SELECT * FROM (
    SELECT sg_id || '-' || v_id AS sgv_id, sg_id, v_id, sgv_value
    FROM ( VALUES
          (:'sg_id', 'RegEx-FixedOpsServiceInvoiceNumber', '^CUSTOMER.+10h3Bk18\.72H(.+?)U0p10.+$')
        , (:'sg_id', 'RegExLibrary-DMS-PageWrapper', '%[RegExLibraryItem-ADP-PageWrapper]%')
        , (:'sg_id', 'RegExLibrary-DMSSpoolerCaptureCleanup', 'RegExLibrary-ADPSpoolerCleanup')
    ) vals (sg_id, v_id, sgv_value)
) final
WHERE NOT EXISTS (SELECT 1 FROM storegroupmap sgm WHERE final.sgv_id = sgm.sgv_id)
RETURNING sgv_id, sgv_value
;
SQL
        ;;

    'AutoSoft'|'Autosoft')
        psql_do --quiet \
                --set=sg_id="$GROUPID" \
                --set=s_id="$STOREID" \
                --set=invoice_store_name="${AUTOSOFT_STORE_NAME:?--autosoft-name}" <<'SQL'
BEGIN;

INSERT INTO storegroupmap (sgv_id, sg_id, v_id, sgv_value)
SELECT :'sg_id' || '-' || v_id, :'sg_id', v_id, sgv_value
FROM ( VALUES
  ('RegExLibrary-DMSSpoolerCaptureCleanup', 'RegExLibrary-AutoSoftSpoolerCleanup'),
  ('RegExLibrary-DMS-PageWrapper', '%[RegExLibraryItem-AutoSoft-PageWrapper]%'),
  ('RegEx-FixedOpsServiceInvoiceNumber', E'^\\s*(\\d+)\\s+Job\\s\\S+\\s+Archive.*$\r\n')
) vm (v_id, sgv_value)
WHERE (:'sg_id' || '-' || v_id) NOT IN (SELECT sgv_id FROM storegroupmap WHERE sg_id = :'sg_id')
RETURNING *
;

INSERT INTO storemap (sv_id, s_id, v_id, sv_value)
SELECT :'s_id' || '-' || v_id, :'s_id', v_id, sv_value
FROM ( VALUES
  ('RegExLibraryItem-AutoSoft-PageWrapper', E'>>BEGIN REGEX (StartOfPage) [X]\r\n^\\s+(?-x)<<INVOICE STORE NAME REPLACE>>(?x)$ \r\n#custom header for each store; need to copy this to store specific variable mapping\r\n>>END REGEX\r\n\r\n\r\n>>BEGIN REGEX (EndOfPage) [X] \r\n^\r\n\\s+(\\d+)\\s+Job\\s(\\S+)\\s.*\r\n$\r\n>>END REGEX\r\n\r\n>>BEGIN REGEX (PageNumber) [X]\r\n^\\s+Page\\s(\\d+)\\s+of\\s+(\\d+).*$\r\n>>END REGEX\r\n')
) vm (v_id, sv_value)
WHERE (:'s_id' || '-' || v_id) NOT IN (SELECT sv_id FROM storemap WHERE s_id = :'s_id')
RETURNING *
;

UPDATE storemap
    SET sv_value = replace(sv_value, '<<INVOICE STORE NAME REPLACE>>', :'invoice_store_name')
    WHERE v_id ~ '^RegExLibrary.*$'
    AND s_id = :'s_id'
    AND sv_value ~ '<<INVOICE STORE NAME REPLACE>>'
    RETURNING sv_value;

;

COMMIT;
SQL
        ;;

    'ACS')
        psql_do --set=sg_id="$GROUPID" <<'SQL'
INSERT INTO storegroupmap (sgv_id, sg_id, v_id, sgv_value)
SELECT :'sg_id' || '-' || v_id, :'sg_id', v_id, sgv_value
FROM ( VALUES
  ('RegExLibrary-DMS-PageWrapper', E'%[RegExLibraryItem-ACS-FullLibrary]%\r\n'),
  ('RegExLibrary-DMSSpoolerCaptureCleanup', E'RegExLibrary-DefaultSpoolerCleanup\r\n'),
  ('RegEx-FixedOpsServiceInvoiceNumber', E'^.+RO:[\\r\\n\\x20]*(?:[^\\s\\d]?)(\\d+)$'),
  ('RegExLibrary-FixedOpsServiceInvoice', E'%[RegExLibraryItem-ACS-FullLibrary]%\r\n'),
  ('Settings-GGCS-FixedOps-InvoiceParsing', E'Class-Loader = com.birdofire.gogetter.fixedopsservices.DocumentBasedServiceInvoiceLoader\r\nClass-DefaultParser = com.birdofire.gogetter.fixedopsservices.parsers.GenericInvoiceParser\r\nClass-Parser = com.birdofire.gogetter.fixedopsservices.parsers.ACSInvoiceParser_TypeA_1_7\r\nClass-SQLLoader = com.birdofire.gogetter.fixedopsservices.DirectInvoiceLoading\r\n\r\n#Source Only Parser Does Not Use This\r\nVariable-RegExLibrary = RegExLibrary-FixedOpsServiceInvoice\r\n\r\n#Update with the proper import\r\nVariable-DMSControlLines = %[RegExLibrary-DMSSpoolerCaptureCleanup]%\r\n\r\n#Need to setup page start and end + optional invoice number translation\r\nVariable-InvoiceCleanup = RegExLibrary-FixedOpsServiceInvoiceCleanup\r\n\r\n#Need to setup the specific invoice number capture pattern\r\nVariable-InvoiceNumberRegEx = RegEx-FixedOpsServiceInvoiceNumber\r\n')
  ) vm (v_id, sgv_value)
WHERE (:'sg_id' || '-' || v_id) NOT IN (SELECT sgv_id FROM storegroupmap WHERE sg_id = :'sg_id')
RETURNING *
;
SQL
        ;;

    'DS1000')
        psql_do --set=sg_id="$GROUPID" <<'SQL'
INSERT INTO storegroupmap (sgv_id, sg_id, v_id, sgv_value)
SELECT :'sg_id' || '-' || v_id, :'sg_id', v_id, sgv_value
FROM ( VALUES
  ('RegExLibrary-DMSSpoolerCaptureCleanup', 'RegExLibrary-DefaultSpoolerCleanup'),
  ('Settings-GGCS-FixedOps-InvoiceParsing', E'Class-Loader = com.birdofire.gogetter.fixedopsservices.DocumentBasedServiceInvoiceLoader\r\nClass-DefaultParser = com.birdofire.gogetter.fixedopsservices.parsers.GenericInvoiceParser\r\nClass-Parser = com.birdofire.gogetter.parser.dealersuite1000.DealerSuite100_RecapReportDetail_TypeA_1_0\r\nClass-SQLLoader = com.birdofire.gogetter.fixedopsservices.DirectInvoiceLoading\r\n\r\n#Source Only Parser Does Not Use This\r\nVariable-RegExLibrary = RegExLibrary-FixedOpsServiceInvoice\r\n\r\n#Update with the proper import\r\nVariable-DMSControlLines = %[RegExLibrary-DMSSpoolerCaptureCleanup]%\r\n\r\n#Need to setup page start and end + optional invoice number translation\r\nVariable-InvoiceCleanup = RegExLibrary-FixedOpsServiceInvoiceCleanup\r\n\r\n#Need to setup the specific invoice number capture pattern\r\nVariable-InvoiceNumberRegEx = RegEx-FixedOpsServiceInvoiceNumber\r\n\r\n'),
  ('RegExLibrary-DMS-PageWrapper', '%[RegExLibraryItem-DS1000-FullLibrary]%'),
  ('RegEx-FixedOpsServiceInvoiceNumber', '^RO:\x20+(\S+)\x20.+'),
  ('RegExLibrary-FixedOpsServiceInvoice', '%[RegExLibraryItem-DS1000-FullLibrary]%')
) vm (v_id, sgv_value)
WHERE (:'sg_id' || '-' || v_id) NOT IN (SELECT sgv_id FROM storegroupmap WHERE sg_id = :'sg_id')
RETURNING *
;
SQL
        ;;

    *)
        die "Unrecognized DMS: $DMS"
        ;;
esac

if [[ "$DO_SETUP_ONLY" = true ]]; then
    say "Only setup performed as requested"
    exit
fi

if [[ -e "$SOURCE_UPLOAD_FILE" ]]; then
    export ETL_TARGET_INVOICESTAGING="$TARGET_BASE_DIR"
    "$IMPORT_SCRIPT" \
        invoice \
        "$SOURCE_UPLOAD_FILE" \
        "$GROUPID" \
        "$STOREPART" \
        || die "Invoice Transfer Failed"

fi

exit
