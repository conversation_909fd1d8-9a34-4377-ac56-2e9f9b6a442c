#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

# Run all of the tests contained within this repository.
export DU_ETL_DEBUG_MODE=2

REPO_ROOT="$(cd $(dirname $0) && pwd)"

if [ ! -d "$DU_ETL_HOME" ] ; then
    echo "DU_ETL_HOME is not set" >&2
    exit 1
fi

(
    cd "$REPO_ROOT"
    pwd
    find "$REPO_ROOT"/src/test -mindepth 1 -maxdepth 1 -type d -printf '%f\n' | sort | while read testdir
    do
        "$REPO_ROOT"/run-test-auto.bash $testdir || die "Test $testdir in $REPO_ROOT failed"
    done
) || die "DMS Processing Test(s) Failed"
