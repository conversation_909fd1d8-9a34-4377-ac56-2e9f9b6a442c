# Source This In Testing Configuration
# Pass in the desired base directory when
# calling.  Cleans out the directory and prepares
# the needed subdirectories for a clean test run
# Does so outside of a function so that the created variables
# ETL/TW/TLI
# end up declared globally.
# Also sets up DU_ETL_DEBUG_TARGET
ETI="${BASE_DIR}"/test-run-extract-transform-interchange
TW="${BASE_DIR}"/test-run-transform-work
TLI="${BASE_DIR}"/test-run-transform-load-interchange

DU_ETL_DEBUG_TARGET="$BASE_DIR"/etl.log

reset_working_directories

ETL_DIST=/tmp/du-etl-test-distribution/
# Ensure the distribution is present since DU-Load won't create
# it out of an expectation that said directory is expected to be
# a permanent pre-agreed upon location.
mkdir -p "$ETL_DIST"

SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
