# Source this from run-test-auto.bash in the DMS repo
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

REPO_ROOT="$(cd $(dirname $0) && pwd)"
BASE_DIR="$HOME"/tmp/du-etl-integration-test-run-base-dir
TLI="${BASE_DIR}"/test-run-transform-load-interchange

function psql_script() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}'" --pset pager='off' --set=ON_ERROR_STOP=1 "$@"
}

function select_test_dir() {
    local prompt="Please select a test directory:"
    while :
    do
        options=( $(find "${REPO_ROOT}"/src/test -mindepth 1 -maxdepth 1 \
                         -type d -printf "%f\n" | sort | xargs -0) )

        if [[ ${#options[@]} == 0 ]] ; then
           yell "No source test dirctories found"
           return 0
        fi
        PS3="$prompt "
        select opt in "${options[@]}"  "Quit"
        do
            if [[ "$opt" = "Quit" ]]; then
                exit 0
            elif (( $REPLY > 0 && $REPLY <= ${#options[@]} )) ; then
                pushd src/test > /dev/null
                export TEST_DIR="${PWD}"/"${opt}"
                popd > /dev/null
                return 0
            else
                yell "Invalid option [ ${REPLY} ]"
            fi
            break
        done
    done
}

# The standard export routine does not really care about the entire decision table; just the retail/warranty mutable subset
# But for validation purposes we do.  Generate a psql table output file for the contents saved to whichever
# directory is specified in the first argument.
function export_decision_files() {
    psql_script --quiet <<SQL
\copy (SELECT paytype, itemcount, itemqty, itemsales, itemcosts, isexcluded, allowchange, base_paytype_name FROM paytype_decision_parts ORDER BY paytype) TO '$1/parts-decisions.tsv'
\copy (SELECT paytype, itemcount, itemsales, itemhours, isexcluded, allowchange, base_paytype_name FROM paytype_decision_labor ORDER BY paytype) TO '$1/labor-decisions.tsv'
SQL
}

function use_results_as_expected_output() {
    say "Copying this run's results to the test output directory."
    TEST_OUTPUT_DIR="${1:?Test Directory Required}"
    mkdir -p "$TEST_OUTPUT_DIR"
    export_decision_files "$TEST_OUTPUT_DIR"
    cp "$TLI"/invoicesequencemaster.csv    "$TEST_OUTPUT_DIR"/invoicesequencemaster.csv
    cp "$TLI"/magescenario-labor.csv       "$TEST_OUTPUT_DIR"/magescenario-labor.csv
    cp "$TLI"/magescenario-part.csv        "$TEST_OUTPUT_DIR"/magescenario-part.csv
}

function perform_standard_validation() {
    TEST_OUTPUT_DIR="${1:?Test Directory Required}"
    [[ -d "$TEST_OUTPUT_DIR" ]] || die "Test Directory Must Exist: $TEST_OUTPUT_DIR"
    local -i failed=0

    # Use base_dir so as to not pollute the TLI directory will stuff that isn't there in production
    export_decision_files "$BASE_DIR"

    diff "$TEST_OUTPUT_DIR"/parts-decisions.tsv "$BASE_DIR"/parts-decisions.tsv
    if [[ $? != 0 ]]; then
        scream "Invalid Parts Decisions"
        failed=1
    fi
    diff "$TEST_OUTPUT_DIR"/labor-decisions.tsv "$BASE_DIR"/labor-decisions.tsv
    if [[ $? != 0 ]]; then
        scream "Invalid Labor Decisions"
        failed=1
    fi
    # Test for invoice sequence but don't display diff as its likely to be excessively large
    # and can always be reviewed manually post-test
    diff --brief "$TEST_OUTPUT_DIR"/invoicesequencemaster.csv "$TLI"/invoicesequencemaster.csv >/dev/null
    if [[ $? != 0 ]]; then
        scream "Invalid Invoice Sequence"
        failed=1
    fi
    diff "$TEST_OUTPUT_DIR"/magescenario-labor.csv "$TLI"/magescenario-labor.csv
    if [[ $? != 0 ]]; then
        scream "Invalid Labor Scenario"
        failed=1
    fi
    diff "$TEST_OUTPUT_DIR"/magescenario-part.csv "$TLI"/magescenario-part.csv
    if [[ $? != 0 ]]; then
        scream "Invalid Parts Scenario"
        failed=1
    fi
    if [[ $failed = 0 ]]; then
        say "Validation Successful"
    else
        scream "Validation Failed"
    fi
    return $failed
}

function build_test_configuration() {
    FIXTURE_INPUT_DIR="${1:?Test Directory Required}"/data/input
    RULES_TEXT=$(cat <<'BLOCK'

SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}

source "$DU_ETL_HOME"/src/test-common/manipulate-test-dirs-under-base_dir.bash

FILECOUNT=$(find "${FIXTURE_ROOT}"/data/input -maxdepth 1 -type f | wc -l)
if [[ $FILECOUNT = 1 ]]; then
    unzip -j "${FIXTURE_ROOT}"/data/input/* -d "${ETI}" || die "Unzip failed"
else
    cp "${FIXTURE_ROOT}"/data/input/* "${ETI}" || die "file copy to ET-Interchange failed"
fi

PARTSKEY=$("${DU_ETL_HOME}"/DU-Load/construct-scenariokey --base-group-id "${GROUPCODE}" --manufacturer  "${MFG}" --db-service-name "${SERVICE_NAME}")

function populate_parts_rules() {
du_paytype_sub --populate-parts-rules --done <<TXT
0,"*","^.*$",0,0,0,0,"Exclude any zero cost part"
1,"*","^.*$",0,0,0.0001,999999999,"Exclude any zero sale part"
2,".01","^.*$",0.0000,9999999.0000,0.0090,0.0110,"Penny Cost"
3,"~","^.*$",-99999999,0,-999999999,0,"Exclude Negative Cost"
4,"~","^.*$",-99999999,0,0,999999999,"Exclude Negative Sale"
5,"!","^.*$",0,99999999,-999999999,0,"Mark Double Negative"
6,"@00","^[^WI].*$",1,1,0,99999999,"Cost Equals Sale - Retail"
7,"@00","^W.*$",1,1,0,99999999,"Cost Equals Sale - Warranty"
8,"+","^[^WI]",17,99999999,0,99999999,"Upper Default Include Pricing Bound"
9,"-","^[^WI]",-99999999,0.1,0,9999999999,"Exclude Negatives Through 10ths"
TXT
[[ "$?" = '0' ]] || die "Part Rule Population Failed"
}

function populate_labor_rules() {
du_paytype_sub --populate-labor-rules --done <<TXT
0,"*","^.*$",0,0,0,0,"Exclude any zero cost job"
1,"*","^.*$",0,0,0.0001,999999999,"Exclude any zero sale job"
2,"+","^.*$",350,99999999,0,99999999,"Upper Default Include Pricing Bound"
3,"-","^.*$",-99999999,5,0,9999999999,"Exclude Negatives Through 5 dollars/hr"
4,"~","^.*$",-99999999,0,-999999999,0,"Exclude Negative Hours"
5,"~","^.*$",-99999999,0,0,999999999,"Exclude Negative Dollars"
6,"!","^.*$",0,99999999,-999999999,0,"Mark Double Negative"
TXT
[[ "$?" = '0' ]] || die "Labor Rule Population Failed"
}

function perform_test_transform() {
    if [[ $PROCESS_MANUAL = false ]]; then
        du_transform --starting-invoice "$STARTING_RO_NUMBER" --process-no-interactive --done || die "Processing Failed"
    else
        du_transform --add-labor-defaults --add-parts-defaults --process --done || die "Processing Failed"
     fi
}

BLOCK
)
    echo "$RULES_TEXT"
}

function run_fixture() {

    function use_dms_specific_results_as_expected_output() {
        return
    }
    function perform_dms_specific_validation() {
        return
    }

    if [[ -n "${1}" ]]; then
        TEST_DIR="${REPO_ROOT}"/src/test/"${1}"
        if [[ ! -d "$TEST_DIR" ]]; then
            die "Supplied test directory does not exist: $TEST_DIR"
        fi
    else
       select_test_dir
    fi

    # Now give the DMS a chance to overwrite the above stubs/hooks
    # Its a bit less convoluted than checking for the name in the environment
    DMS_SPECIFIC_VALIDATION_SCRIPT=$(dirname "$TEST_DIR")/validate.bash
    if [[ -f "$DMS_SPECIFIC_VALIDATION_SCRIPT" ]]; then
        echo "Sourcing DMS-Specific Validation File: $(dirname "$TEST_DIR")/validate.bash"
        source "$DMS_SPECIFIC_VALIDATION_SCRIPT"
    fi

    TEST_FILE="$BASE_DIR"/dynamic-test-configuration.bash

    clear_dir "$BASE_DIR"

    cat "$TEST_DIR"/test-configuration.bash \
        <(echo BASE_DIR="$BASE_DIR") \
        <(echo FIXTURE_ROOT="$TEST_DIR") \
        <(build_test_configuration "${TEST_DIR}") \
        > "$TEST_FILE"

    "${DU_ETL_HOME}/main/run-template.bash" --test-file "${TEST_FILE}" || die 'Template Run Failed'

    if [[ "${2:-check}" = 'copy' ]]; then
        progress "Copying Results to Expected Output"
        use_results_as_expected_output "${TEST_DIR}"/data/output
        use_dms_specific_results_as_expected_output "${TEST_DIR}"/data/output
    fi

    perform_standard_validation "${TEST_DIR}"/data/output \
        || { echo "Validation Failed"; exit 1; }

    perform_dms_specific_validation "${TEST_DIR}"/data/output \
        || { echo "DMS Validation Failed"; exit 1; }
}
