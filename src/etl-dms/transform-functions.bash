# Sourced by /etl-dms

function perform_parse_only() {
    function du_transform() {
        "${DU_ETL_HOME}"/DU-Transform/du-parse.bash \
            --set-source "${ETI_DIR}" \
            --set-work "${TW:?TW Path Required}" \
            "$@"
        return $?
    }
    du_transform --dms "$DMS_TYPE"          --done   || die "Setting DMS failed"
    du_transform --select-file              --done   || die "Interactive File Selection Failed"
    du_transform --parse                    --done   || die "Parsing Failed"
    return
}

function generate_mock_config_template() {
echo "export DMS_BASE='${DMS_TYPE}'"
echo "MFG='${MOCK_BRAND:?Manufacturer Required}'"
echo "STATE='${MOCK_STATE:?Mock State Required}'"
cat <<'EOF'
# Bash Source Test Config File

source "$DU_ETL_HOME/DU-DMS/DMS-${DMS_BASE}/${DMS_BASE}.env"

DMS_BASE_DIR_VAR="DU_ETL_BASEDIR_${DMS_BASE}"
DMS_BASE_DIR=$(eval echo "\${${DMS_BASE_DIR_VAR}}")
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI_VAR="DU_ETL_ETI_DIR_${DMS_BASE}"
ETI=$(eval echo "\${${ETI_VAR}}")

PROMPT_FOR_FILE='true'

#ETL  DMS: <ETL_DMS>
#Base DMS: <DMS_BASE> (optional)
DATE_PART='200001'

GROUP_CODE='MOCK_DMS'
GROUPNAME='Mock Group'
STORENAME='Mock Store'
STORE_PART='MOCK_DMS'

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
ETL_DIST="${DU_ETL_DIST_DIR}"

EOF
}

function get_transient_config_file_name() {
    echo "${SCRIPT_WORK_DIR:?Script Work Dir Required}"/last-config-file-name.txt
}

function determine_config_file() {
    transient_file_name=$(get_transient_config_file_name)
    if [[ "$DIST_ONLY" = 'true' ]]; then
        custom_config_file=$(cat "$transient_file_name")
        [[ -f "$custom_config_file" ]] \
            || die "Last config file $transient_file_name does not exist..."
    else
        say "Please select a configuration file to import with." >&2
        custom_config_file=$(select_file "${CONFIG_DIR:?Config Dir Required}")
    fi
    echo "$custom_config_file"
}

function cleanup_after_dev_run() {
    transient_file_name=$(get_transient_config_file_name)
    if [[ "$DIST_ONLY" = 'true' ]]; then
        echo "Removing $transient_file_name"
        rm "$transient_file_name"
    else
        echo "Saving config file name for subsequent --distribute-only call"
        echo "${1:?Config File Name Required}" > "$transient_file_name"
    fi
}
