-- Search Path has already been set
-- The contents of these tables is NOT multi-tenant

CREATE TABLE dealership (
    dealership_name                               text NOT NULL PRIMARY KEY
);

CREATE TABLE repair_order (
    ro_number                                     text NOT NULL PRIMARY KEY,
    creation_date                                 date     NULL,
    completion_date                               date     NULL,
    department                                    text     NULL,
    branch                                        text     NULL,
    sub_branch                                    text     NULL,
    advisor                                       text     NULL,
    tag_no                                        text     NULL,
    delivery_date                                 date     NULL,
    promised_date                                 date     NULL,
    promised_time                                 time     NULL,
    open_time                                     time     NULL,
    booked_date                                   date     NULL,
    booked_time                                   time     NULL,
    payment_code                                  text     NULL,
    comments                                      text     NULL,
    ro_ext                                        text     NULL,
    status                                        text     NULL,
    in_service_date                               date     NULL,
    estimate_amount                               numeric  NULL
);

CREATE TABLE repair_order_vehicle (
    ro_number                                     text NOT NULL PRIMARY KEY,
    vehicle_year                                  text     NULL,
    vehicle_make                                  text     NULL,
    vehicle_model                                 text     NULL,
    vehicle_vin                                   text     NULL,
    vehicle_color                                 text     NULL,
    vehicle_body                                  text     NULL,
    mileage_in                                    bigint   NULL,
    mileage_out                                   bigint   NULL,
    delivery_miles                                bigint   NULL
);

CREATE TABLE repair_order_customer (
    ro_number                                     text NOT NULL PRIMARY KEY,
    customer_number                               text     NULL,
    customer_name                                 text     NULL,
    customer_address                              text     NULL,
    customer_city                                 text     NULL,
    customer_state                                text     NULL,
    customer_zip                                  text     NULL,
    customer_phone                                text     NULL,
    home_phone                                    text     NULL,
    main_phone                                    text     NULL,
    cell_phone                                    text     NULL,
    customer_email                                text     NULL,
    license_number                                text     NULL,
    control_number                                text     NULL,
    work_phone                                    text     NULL,
    other_phone                                   text     NULL
);

CREATE TABLE repair_line (
    ro_number                                     text NOT NULL,
    ro_line                                       text NOT NULL,
    PRIMARY KEY (ro_number,
                 ro_line),
    complaint                                     text     NULL,
    add_on_flag                                   boolean  NULL,
    recommendation                                text     NULL
);

CREATE TABLE repair_job (
    ro_number                                     text NOT NULL,
    ro_line                                       text NOT NULL,
    ro_job_line                                   text NOT NULL,
    billing_code                                  text     NULL,
    PRIMARY KEY (ro_number,
                ro_line,
                ro_job_line,
                billing_code),
    op_code                                       text     NULL,
    op_description                                text     NULL,
    sold_hours                                 numeric     NULL,
    actual_hours                               numeric     NULL,
    labor_cost                                 numeric     NULL,
    sale_amount                                numeric     NULL,
    cause                                         text     NULL,
    correction                                    text     NULL
);

CREATE TABLE repair_part (
    ro_number                                     text NOT NULL,
    ro_line                                       text NOT NULL,
    ro_job_line                                   text NOT NULL,
    ro_part_line                                   int NOT NULL,
    prt_billing_type                              text NOT NULL DEFAULT 'NA',
    PRIMARY KEY (ro_number,
                 ro_line,
                 ro_job_line,
                 ro_part_line,
                 prt_billing_type),
    part_number                                   text     NULL,
    part_description                              text     NULL,
    quantity_sold                              numeric     NULL,
    unit_cost                                  numeric     NULL,
    unit_sale                                  numeric     NULL,
    unit_core_cost                             numeric     NULL,
    unit_core_sale                             numeric     NULL
);

CREATE TABLE repair_other (
    ro_other_id                              bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    item_type                                     text     NULL
        CHECK (item_type IN ('MISC',
                             'GOG',
                             'SUBLET',
                             'DEDUCTIBLE',
                             'TAX',
                             'FEE',
                             'CDISC',
                             'PAYSHOP',
                             'SPECDEP',
                             'HAZMAT',
                             'RENTAL',
			     'WARRANTY_DEDUCT'
                              )),
    -- If present the item needs to be associated with with correspoding
    -- repair_line otherwise the item is associated with the repair_order
    -- as a whole.
    ro_line                                       text     NULL,
    FOREIGN KEY (ro_number,
                 ro_line)
        REFERENCES repair_line (ro_number,
                                ro_line),
    other_billing_type                            text     NULL,
    other_code                                    text     NULL,
    other_description                             text     NULL,
    other_cost                                 numeric     NULL,
    other_sale                                 numeric     NULL
);

CREATE TABLE repair_job_technician_detail (
    ro_number                                     text NOT NULL,
    ro_line                                       text NOT NULL,
    ro_job_line                                   text NOT NULL,
    ro_job_tech_line                               int NOT NULL,
    PRIMARY KEY (ro_number,
                 ro_line,
                 ro_job_line,
                 ro_job_tech_line),
    tech_id                                       text     NULL,
    tech_name                                     text     NULL,
    tech_billing_type                             text     NULL,
    work_date                                     date     NULL,
    work_start_time                               time     NULL,
    work_end_time                                 time     NULL,
    work_note                                     text     NULL,
    actual_hours                               numeric     NULL,
    booked_hours                               numeric     NULL,
    tech_lbr_cost                              numeric     NULL,
    tech_lbr_sale                              numeric     NULL
);

CREATE TABLE repair_job_labor_item (
    ro_number                                     text NOT NULL,
    ro_line                                       text NOT NULL,
    ro_job_line                                   text NOT NULL,
    ro_job_item_line                               int NOT NULL,
    PRIMARY KEY (ro_number,
                 ro_line,
                 ro_job_line,
                 ro_job_item_line),
    op_code                                       text     NULL,
    description                                   text     NULL,
    lbr_hrs                                    numeric     NULL,
    other_lbr_hrs                              numeric     NULL,
    setup_hrs                                  numeric     NULL,
    diagnosis_hrs                              numeric     NULL,
    paint_hrs                                  numeric     NULL,
    admin_hrs                                  numeric     NULL,
    additional_hrs                             numeric     NULL
);

CREATE TABLE repair_tech_punch (
    ro_punch_id                              bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    ro_line                                       text     NULL,
    tech_id                                       text     NULL,
    work_date                                     date     NULL,
    work_start_time                               time     NULL,
    work_end_time                                 time     NULL,
    work_type                                     text     NULL,
    work_duration                              numeric     NULL
);

CREATE TABLE repair_discount (
    ro_disc_id                               bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    ro_line                                       text     NULL,
    disc_code                                     text     NULL,
    disc_description                              text     NULL,
    disc_applied                                  text     NULL,
    labor_discount                             numeric     NULL,
    parts_discount                             numeric     NULL,
    total_discount                             numeric     NULL
);

CREATE TABLE repair_account (
    ro_account_id                               bigserial NOT NULL PRIMARY KEY,
    ro_number                                   text NOT NULL,
    ro_account_no                               text     NULL,
    account_description                         text     NULL,
    account_controlnumber                       text     NULL,
    account_referencenumber                     text     NULL,
    account_transactionamount                   text     NULL
);

CREATE TABLE repair_order_payment (
    ro_pay_id                                bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    pay_code                                      text     NULL,
    pay_description                               text     NULL,
    pay_amount                                 numeric     NULL,
    is_insurance                               boolean     NULL
);

CREATE TABLE repair_order_print (
    ro_number text NOT NULL PRIMARY KEY,
    document  text NOT NULL,
    FOREIGN KEY (ro_number) REFERENCES repair_order (ro_number)
);
CREATE TABLE pay_type_filter (
    label                                text NULL,
    pay_type                             text NOT NULL,
    insurance                            text NOT NULL,
    from_type                            text NOT NULL,
    target_type                          text NOT NULL
);
--Function to get the array with specific length
CREATE OR REPLACE FUNCTION get_array_with_fixed_length
    (
        in_array  text [],
        in_length integer
    )
    RETURNS text []
LANGUAGE plpgsql
STRICT
AS $function$
DECLARE
    array_len integer;
    diff      integer;
BEGIN
    SELECT coalesce(array_length(in_array, 1), 0)
    INTO array_len;
    IF array_len < in_length
    THEN
        diff := in_length - array_len;
        WHILE diff > 0
        LOOP
            in_array = array_append(in_array, NULL);
            diff := diff - 1;
        END LOOP;
    ELSE
        in_array = in_array [1 :in_length];
    END IF;
    RETURN array_replace(in_array, '', NULL);
END;
$function$;
