# Source this into top-level DU_ETL_HOME/run-* Scripts

# Each of these function wraps a semantically meaningful
# invocation of the appropriate command.
# These should largely be DMS-agnostic and so are placed
# here for use by all.

# The function confirms that required inputs are
# present and provides prompts for any mandatory variable
# that are as yet undefined.
# Due to the prompting nature it is not suitable
# for use with automated testing.

function finalize_interactive_configuration() {
    if [[ -z "$BASE_DIR" ]]; then
        die "BASE_DIR variable required to be set post-config"
    fi
    if [[ -z "$SCENARIOKEY" ]]; then
        SCENARIOKEY=
        # die "SCENARIOKEY variable required to be set post-config"
    fi
    : "${ETI:=${BASE_DIR}/run-du-etl-eti}"
    : "${TW:=${BASE_DIR}/run-du-etl-tw}"
    : "${TLI:=${BASE_DIR}/run-du-etl-tli}"

    if [[ -z "$SERVICE_NAME" ]]; then
        die "SERVICE_NAME required - location of target GGS database"
    fi

    PARTSKEY="${SCENARIOKEY}"
    local MAX_LEN=35
    local PARTIAL_SCN_NAME="${STORENAME}${MFG:?Need Manufacturer}"
    # if (( "${#PARTIAL_SCN_NAME}" > "$MAX_LEN" )); then
    #     die "Store Name and Manufacturer Combined Length Must Be Less Than $MAX_LEN (is ${#PARTIAL_SCN_NAME})"
    # fi
}

function reset_working_directories() {
    : "${ETI:=${BASE_DIR}/run-du-etl-eti}"
    : "${TW:=${BASE_DIR}/run-du-etl-tw}"
    : "${TLI:=${BASE_DIR}/run-du-etl-tli}"
    # if [[ "${PERMANENT_ETI:-false}" = 'false' ]]; then
    #     mkdir -p "${ETI}"
    #     [[ -d "${ETI}" ]] || die "--source-dir Transform Source directory required - loading all files"
    #     rm -rf "${ETI}"/*
    # fi

    mkdir -p "${TW}"
    mkdir -p "${TLI}"
    [[ -d "${TW}"  ]] || die "--work-dir Transform Working directory required"
    [[ -d "${TLI}" ]] || die "--target-dir Transform-Load Interchage directory required"
    rm -rf "${TW}"/*
    rm -rf "${TLI}"/*
    LOAD_FOLDER="${BASE_DIR}"/audit-load
    LOAD_FILE_FOLDER="${LOAD_FOLDER}"/run-du-etl-load-tli
    rm -rf "${LOAD_FILE_FOLDER}"/*
}

function du_export_mapper() {
    # if  [[ "$DMS" = 'ReynoldsRCI' ]]; then
        "${DU_ETL_HOME}/DU-Transform/du-export-mapper-audit.bash" \
            --schema "${SRC_SCHEMA}" \
            --scenariokey "${SCENARIOKEY}" \
            --manufacturer "${MFG}" \
            --state "${STATE}" \
            --company_id "${COMPANY_ID}" \
            "$@"
    # else
    #     "${DU_ETL_HOME}/DU-Transform/du-export-mapper.bash" \
    #         --schema "${SRC_SCHEMA}" \
    #         --scenariokey "${SCENARIOKEY}" \
    #         --manufacturer "${MFG}" \
    #         --state "${STATE}" \
    #         "$@"
    # fi
}

function du_transform() {
    "${DU_ETL_HOME}"/DU-Transform/du-parse.bash \
        --set-source "${ETI}" \
        --set-work "${TW}" \
        "$@"
    return $?
}

function du_paytype_sub() {
    "$DU_ETL_HOME"/DU-Transform/src/main/bash/du-process-paytypes "$@"
    return "$?"
}

function du_loadcheck() {
    "${DU_ETL_HOME}"/DU-Load/do-check-data "$@"
    return $?
}

function du_loadcreate() {
    "${DU_ETL_HOME}"/DU-Load/do-create-domain-scenario-and-users \
        --db-service-name "${SERVICE_NAME}" \
        "$@"
    return $?
}


function du_loadmerge() {
    "${DU_ETL_HOME}"/DU-Load/do-merge-and-load \
        --data-dir "${TLI}" \
        --db-service-name "${SERVICE_NAME}" \
        "$@"
    return $?
}

function du_audit_loadmerge() {

    "${DU_ETL_HOME}"/DU-Load/do-audit-merge-and-load \
        --data-dir "${BASE_DIR}"/run-du-etl-load-tli \
        --db-service-name "${AUDIT_SERVICE_NAME}" \
        "$@"
    return $?
}

function du_new_audit_loadmerge() {

    "${DU_ETL_HOME}"/DU-Load/do-new-audit-merge-and-load \
        --data-dir "${BASE_DIR}"/run-du-etl-load-tli \
        --db-service-name "${AUDIT_SERVICE_NAME}" \
        "$@"
    return $?
}


function du_generatedocs() {
    echo "SERVICE_NAME3: $SERVICE_NAME"
    SERVICE_NAME='audit_etl_local'
    "${DU_ETL_HOME}"/DU-Load/do-create-audit-load-summary \
        --db-service-name "${SERVICE_NAME}" \
        "$@"
    return $?
}

function du_distribute() {
    local ZIP_ARG="${2}"
    local SG_ID=''
    local S_CODE=''
    local YEARMONTH=''
    local OUT_DIR=''
    OUT_DIR="${ETL_DIST:?Distribution Directory Path Required}"
    [[ -d "${ETL_DIST}" ]] || die "Distribution Directory Presence ETL_DIST=${ETL_DIST} Expected - No Auto-Creation"

    # If argument 1 is a parts scenario key...
    if [[ "${1}" =~ ^\[([A-Z0-9_-]+)\](.+)\+([0-9]{6})_.*$ ]]; then
        SG_ID="${BASH_REMATCH[1]}"
        S_CODE="${BASH_REMATCH[2]}"
        YEARMONTH="${BASH_REMATCH[3]}"
    else
        die "Invalid Parts Key Specification [${1}]"
    fi
    "${DU_ETL_HOME}"/DU-Load/do-distribute \
        --input-dir  "${TLI}"              \
        --output-dir "${OUT_DIR}"          \
        --group-base "${SG_ID}"            \
        --store-base "${S_CODE}"           \
        --period     "${YEARMONTH}"        \
        --scenariokey "${1}"               \
        "${ZIP_ARG}"
    return $?
}
function read_audit_import_config(){
    CONFIG_FILE=$TW/config_*.bash
    echo "CONFIG_FILE in TW: $CONFIG_FILE"


    echo "TLI contents in etl commants 1.2"
    echo "**********************************************************************"
    echo "$(ls ${TLI})"
    echo "**********************************************************************"
    source $CONFIG_FILE
    export DMS
    export MFG
    export STATE
    export COMPANY_ID
    export SOURCE_COMPANY_ID
    export SCHEDULER_ID
    FIX_SCHEDULER_ID=$(echo "$SCHEDULER_ID" | tr -d '-')
    FIX_SCHEDULER_ID_LOWER=$(echo "$FIX_SCHEDULER_ID" | tr '[:upper:]' '[:lower:]')  
    SCHEMA_COMPANY="${SOURCE_COMPANY_ID:-$COMPANY_ID}"
    IMPORT_SCHEMA="du_dms_${FIX_SCHEDULER_ID_LOWER}_${SCHEMA_COMPANY}"
    export IMPORT_SCHEMA
    export PROJECT_ID
    export SECONDARY_PROJECT_ID
    export IMPORTED_BY
    export SUPPLEMENT_TAG
    export SECONDARY_SUPPLEMENT_TAG
    export IS_SHIPPED
    export SECONDARY_IS_SHIPPED
    echo "PROJECT_ID: $PROJECT_ID"
    echo "SECONDARY_PROJECT_ID: $SECONDARY_PROJECT_ID"
    echo "COMPANY_ID: $COMPANY_ID"
    echo "IMPORTED_BY: $IMPORTED_BY"
    echo "IS_SHIPPED: $IS_SHIPPED"
    echo "SECONDARY_IS_SHIPPED: $SECONDARY_IS_SHIPPED"
    echo "SUPPLEMENT_TAG: $SUPPLEMENT_TAG"
    echo "SECONDARY_SUPPLEMENT_TAG: $SECONDARY_SUPPLEMENT_TAG"
    echo "DMS: $DMS"
    echo "MFG: $MFG"
    echo "STATE: $STATE"
    echo "SCHEDULER_ID:$SCHEDULER_ID"
    echo "IMPORT_SCHEMA:$IMPORT_SCHEMA"

    echo "TLI contents in etl commants 1.3"
    echo "**********************************************************************"
    echo "$(ls ${TLI})"
    echo "**********************************************************************"
    cp ${CONFIG_FILE} ${TLI}

    echo "TLI contents in etl commants 1.4"
    echo "**********************************************************************"
    echo "$(ls ${TLI})"
    echo "**********************************************************************"

    CONFIG_FILE=$TLI/config_$COMPANY_ID.bash
    echo "CONFIG_FILE in TLI: $CONFIG_FILE"
    echo "$(cat ${TLI}/config_$COMPANY_ID.bash)"

    MACHINE="$(hostname)"
    echo "CONFIG_FILE in While adding source: $CONFIG_FILE"

    echo -e "\nMACHINE=${MACHINE}" >> ${CONFIG_FILE}
    SOURCE_FILE=$(basename $(< ${TW}/meta/source-files.txt))
    echo -e "\nSOURCE_FILE=${SOURCE_FILE}" >> ${CONFIG_FILE}
    echo "SOURCE_FILE: $SOURCE_FILE"

}


function du_audit_distribute() {
    local ZIP_ARG="${2}"
    local SG_ID=''
    local S_CODE=''
    local YEARMONTH=''
    local OUT_DIR=''
    OUT_DIR="${ETL_DIST:?Distribution Directory Path Required}"
    [[ -d "${ETL_DIST}" ]] || die "Distribution Directory Presence ETL_DIST=${ETL_DIST} Expected - No Auto-Creation"

    # If argument 1 is a parts scenario key...
    if [[ "${1}" =~ ^\[([A-Z0-9_-]+)\](.+)\+([0-9]{6})_.*$ ]]; then
        SG_ID="${BASH_REMATCH[1]}"
        S_CODE="${BASH_REMATCH[2]}"
        YEARMONTH="${BASH_REMATCH[3]}"
    else
        die "Invalid Parts Key Specification [${1}]"
    fi
    "${DU_ETL_HOME}"/DU-Load/do-audit-distribute \
        --input-dir  "${BASE_DIR}"/run-du-etl-load-tli              \
        --output-dir "${OUT_DIR}"          \
        --group-base "${SG_ID}"            \
        --store-base "${S_CODE}"           \
        --period     "${YEARMONTH}"        \
        --scenariokey "${1}"               \
        "${ZIP_ARG}"
    return $?
}

function perform_unattended_parse() {
    du_transform --add-all-files            --done   || die "Copying files to work directory failed"
    du_transform --dms "${DMS}"             --done   || die "Setting DMS failed"
    du_transform --manufacturer "${MFG}"    --done   || die "Setting MFG failed"
    du_transform --statecode "${STATE}"     --done   || die "Setting State Code failed"
    du_transform --parse                    --done   || die "Parsing Failed"
}

function perform_zip_file_selection_and_parse() {

    du_transform --select-file              --done   || die "Interactive File Selection Failed"
    read_audit_import_config                --done   || die "Reading Import Config Failed"   
    du_transform --parse                    --done   || die "Parsing Failed"
    du_transform --dms "${DMS}"             --done   || die "Setting DMS failed"
    du_transform --manufacturer "${MFG}"    --done   || die "Setting MFG failed"
    du_transform --statecode "${STATE}"     --done   || die "Setting State Code failed"
}

function prompt_production_load() {
    if [[ "$USE_CONFIG" = true ]]; then
        read -p "Type 'y' to perform loading: " RESPONSE
        if [[ ! "${RESPONSE:-n}" = 'y' ]]; then
            yell "Interactive Mode Stopped Before Loading"
            exit 0
        fi
    fi
}

function psql_ggs() {
    psql "service=${SERVICE_NAME}" --set=ON_ERROR_STOP=1 "$@"
    return $?
}

function perform_audit_load() {

    LABOR_PROJECT_ID=''
    PARTS_PROJECT_ID=''
    echo "COMP IGGGG $COMPANY_ID"
    if  [[ "$PROJECT_TYPE" = 'Labor UL' ]]; then
        LABOR_PROJECT_ID=${PROJECT_ID}
        PARTS_PROJECT_ID=${SECONDARY_PROJECT_ID}
    else
        LABOR_PROJECT_ID=${SECONDARY_PROJECT_ID}
        PARTS_PROJECT_ID=${PROJECT_ID}
    fi
 
    du_audit_loadmerge \
        --state "${STATE}"          \
        --group-name "${GROUP_CODE}" \
        --store-name "${STORE_PART}" \
        --company-id "${COMPANY_ID}" \
        --parts-project-id "${PARTS_PROJECT_ID}" \
        --labor-project-id "${LABOR_PROJECT_ID}" \
        --imported-by "${IMPORTED_BY}" \
        --dms "${DMS}"              \
        --manufacturer "${MFG}"     \
        || die "Audit Loading merged data failed!"

}

function perform_new_audit_load() {

    LABOR_PROJECT_ID=''
    PARTS_PROJECT_ID=''
    echo "COMP IGGGG $COMPANY_ID"
    if  [[ "$PROJECT_TYPE" = 'Labor UL' ]]; then
        LABOR_PROJECT_ID=${PROJECT_ID}
        PARTS_PROJECT_ID=${SECONDARY_PROJECT_ID}
    else
        LABOR_PROJECT_ID=${SECONDARY_PROJECT_ID}
        PARTS_PROJECT_ID=${PROJECT_ID}
    fi
 
    du_new_audit_loadmerge \
        --state "${STATE}"          \
        --group-name "${GROUP_CODE}" \
        --store-name "${STORE_PART}" \
        --company-id "${COMPANY_ID}" \
        --parts-project-id "${PARTS_PROJECT_ID}" \
        --labor-project-id "${LABOR_PROJECT_ID}" \
        --imported-by "${IMPORTED_BY}" \
        --dms "${DMS}"              \
        --manufacturer "${MFG}"     \
        || die "Audit Loading merged data failed!"

}

function perform_load() {
    # TODO: somehow the loader and the transformer want to be able to
    #       preview the interchange CSV data for accuracy and completeness.
    #       The transformer may wish, upon seeing the preview, to
    #       alter the process metadata tables and re-export.
    #       The indirection present here is possibly not ideal
    #       but a good starting point until a more robust solution
    #       is deemed required.  Keeping in mind that ultimately
    #       the actual presentation and feedback loop will be managed
    #       within a more official GUI.  The command-line capabilities
    #       can leave a bit more work for the more technically capable
    #       user who is potentially going to add some customization to the
    #       underlying codebase during their interaction anyway.
    #       Like the do-check-data command this interface would
    #       not require or even want to access the production database
    #       and would ultimately use the $DU_ETL_PG_SERVICE service name
    #       along with its own schema.

    # Create is an implemenation that requires
    # all data to be provided to it explicitly.
    # The fact that manufacturer and dms are noted
    # in the interchange directory is not something
    # it is aware of.  The main controller for the
    # module, or individual users, can obtain the
    # values to pass on the command-line from those
    # files.
    du_loadcreate                   \
        --key "${PARTSKEY}"         \
        --state "${STATE}"          \
        --group-name "${GROUPNAME}" \
        --store-name "${STORENAME}" \
        --dms "${DMS}"              \
        --manufacturer "${MFG}"     \
        --create-store              \
        --create-scenario           \
        --create-users                               || die "Scenario et al. creation failed!"

    du_loadmerge \
        --partskey "${PARTSKEY}" \
        --laborkey "${LABORKEY}"                     || die "Loading merged data failed!"
}

function perform_distribution() {
    local ZIP_ARG="${1:---no-zip}"
    du_generatedocs              \
        --partskey "${PARTSKEY}" \
        --laborkey "${LABORKEY}" \
        --output-dir "${TLI}"/load-output           || die "Generating Outputs Failed!"

    du_distribute "${PARTSKEY}" "$ZIP_ARG"          || scream "Distribution Failed! - Consider Manual Re-Distribution of Errors"
}

function perform_auditfile_distribution() {

    local ZIP_ARG="${1:---no-zip}"
    SUBMISSION_UUID=${2}

    du_generatedocs              \
        --state "${STATE}"          \
        --group-name "${GROUP_CODE}" \
        --store-name "${STORE_PART}" \
        --dms "${DMS}"              \
        --manufacturer "${MFG}"     \
        --submission_uuid "${SUBMISSION_UUID}"     \
        --partskey "${PARTSKEY}" \
        --laborkey "${LABORKEY}" \
        --output-dir "${BASE_DIR}"/run-du-etl-load-tli/load-output           || die "Generating Outputs Failed!"

    du_audit_distribute "${PARTSKEY}" "$ZIP_ARG" || scream "Distribution Failed! - Consider Manual Re-Distribution of Errors"
}

function distribute_via_email() {
    type send-etl-email >/dev/null 2>&1 || return 0
    send-etl-email --subject "$1" --body-file "$2"
}

function attempt_agent_reload() {
    if [[ -x "$HOME"/.du-etl/perform-agent-reload.bash ]]; then
        "$HOME"/.du-etl/perform-agent-reload.bash
        # Force a return zero since we only care about the attempt
        # We actually get a non-zero presently due to curl and SSL
        # and expired-self-signed certificates
        return 0
    else
        yell "Agent Reload Script Not Present @ $HOME/.du-etl/perform-agent-reload.bash"
    fi
}

function archive_source_file() {
    local source_files="${TW}/meta/source-files.txt"
    local file
    local dir

    if [[ ! -e "${source_files}" ]]; then
        return
    fi

    file=$(< "${source_files}")
    if [[ ! -e "${file}" ]]; then
        return
    fi

    dir=$(dirname "${file}")

    if [[ ! "${dir}" =~ .*archive$ ]]; then
        mv "${file}" "${dir}/archive"
    fi
}
