--
-- PostgreSQL database dump
--

-- Dumped from database version 9.6.5
-- Dumped by pg_dump version 9.6.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: du_proxy_invoice_test; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA du_proxy_invoice_test;


ALTER SCHEMA du_proxy_invoice_test OWNER TO postgres;

SET search_path = du_proxy_invoice_test, pg_catalog;

--
-- Name: get_array_with_fixed_length(text[], integer); Type: FUNCTION; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE FUNCTION get_array_with_fixed_length(in_array text[], in_length integer) RETURNS text[]
    LANGUAGE plpgsql STRICT
    AS $$
DECLARE
    array_len integer;
    diff      integer;
BEGIN
    SELECT coalesce(array_length(in_array, 1), 0)
    INTO array_len;
    IF array_len < in_length
    THEN
        diff := in_length - array_len;
        WHILE diff > 0
        LOOP
            in_array = array_append(in_array, NULL);
            diff := diff - 1;
        END LOOP;
    ELSE
        in_array = in_array [1 :in_length];
    END IF;
    RETURN array_replace(in_array, '', NULL);
END;
$$;


ALTER FUNCTION du_proxy_invoice_test.get_array_with_fixed_length(in_array text[], in_length integer) OWNER TO postgres;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: dealership; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE dealership (
    dealership_name text NOT NULL
);


ALTER TABLE dealership OWNER TO postgres;

--
-- Name: repair_job; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE repair_job (
    ro_number text NOT NULL,
    ro_line text NOT NULL,
    ro_job_line text NOT NULL,
    billing_code text NOT NULL,
    op_code text NOT NULL,
    op_description text NOT NULL,
    sold_hours numeric NOT NULL,
    sale_amount numeric NOT NULL,
    cause text NOT NULL,
    correction text NOT NULL
);


ALTER TABLE repair_job OWNER TO postgres;

--
-- Name: repair_job_technician_detail; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE repair_job_technician_detail (
    ro_number text NOT NULL,
    ro_line text NOT NULL,
    ro_job_line text NOT NULL,
    ro_job_tech_line integer NOT NULL,
    tech_id text NOT NULL,
    work_date date,
    work_start_time time without time zone,
    work_end_time time without time zone,
    work_note text,
    actual_hours numeric NOT NULL,
    booked_hours numeric
);


ALTER TABLE repair_job_technician_detail OWNER TO postgres;

--
-- Name: repair_line; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE repair_line (
    ro_number text NOT NULL,
    ro_line text NOT NULL,
    complaint text NOT NULL
);


ALTER TABLE repair_line OWNER TO postgres;

--
-- Name: repair_order; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE repair_order (
    ro_number text NOT NULL,
    creation_date date NOT NULL,
    completion_date date,
    department text NOT NULL,
    branch text NOT NULL,
    sub_branch text NOT NULL,
    advisor text
);


ALTER TABLE repair_order OWNER TO postgres;

--
-- Name: repair_order_customer; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE repair_order_customer (
    ro_number text NOT NULL,
    customer_name text NOT NULL,
    customer_address text,
    customer_city text,
    customer_state text,
    customer_zip text,
    customer_phone text,
    customer_email text
);


ALTER TABLE repair_order_customer OWNER TO postgres;

--
-- Name: repair_order_print; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE repair_order_print (
    ro_number text NOT NULL,
    document  text NOT NULL
);


ALTER TABLE repair_order_print OWNER TO postgres;

--
-- Name: repair_order_vehicle; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE repair_order_vehicle (
    ro_number text NOT NULL,
    vehicle_year text NOT NULL,
    vehicle_make text NOT NULL,
    vehicle_model text NOT NULL,
    vehicle_vin text NOT NULL,
    mileage_in bigint NOT NULL
);


ALTER TABLE repair_order_vehicle OWNER TO postgres;

--
-- Name: repair_other; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE repair_other (
    ro_other_id bigint NOT NULL,
    ro_number text NOT NULL,
    item_type text NOT NULL,
    ro_line text,
    other_code text,
    other_description text,
    other_cost numeric,
    other_sale numeric,
    CONSTRAINT repair_other_item_type_check CHECK ((item_type = ANY (ARRAY['MISC'::text, 'GOG'::text, 'SUBLET'::text, 'DEDUCTIBLE'::text])))
);


ALTER TABLE repair_other OWNER TO postgres;

--
-- Name: repair_other_ro_other_id_seq; Type: SEQUENCE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE SEQUENCE repair_other_ro_other_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE repair_other_ro_other_id_seq OWNER TO postgres;

--
-- Name: repair_other_ro_other_id_seq; Type: SEQUENCE OWNED BY; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER SEQUENCE repair_other_ro_other_id_seq OWNED BY repair_other.ro_other_id;


--
-- Name: repair_part; Type: TABLE; Schema: du_proxy_invoice_test; Owner: postgres
--

CREATE TABLE repair_part (
    ro_number text NOT NULL,
    ro_line text NOT NULL,
    ro_job_line text NOT NULL,
    ro_part_line integer NOT NULL,
    part_number text NOT NULL,
    part_description text,
    quantity_sold numeric NOT NULL,
    unit_cost numeric NOT NULL,
    unit_sale numeric NOT NULL
);


ALTER TABLE repair_part OWNER TO postgres;

--
-- Name: repair_other ro_other_id; Type: DEFAULT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_other ALTER COLUMN ro_other_id SET DEFAULT nextval('repair_other_ro_other_id_seq'::regclass);


--
-- Data for Name: dealership; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY dealership (dealership_name) FROM stdin;
VOSS-JoeMorganHonda
\.


--
-- Data for Name: repair_job; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY repair_job (ro_number, ro_line, ro_job_line, billing_code, op_code, op_description, sold_hours, sale_amount, cause, correction) FROM stdin;
603346	1	1	I	03JMZ01	CERTIFIED INSPECT	1.50	150.00	PER UCD	COMPLETED INSPECTION
603346	2	1	I	01JMZA	A SERVICE	0	12.50	MAINTENANCE	REPLACED ENGINE OIL, RESET SERVICE MAINTENANCE MINDER
603346	3	1	I	01JMZ2SERVICE	SERVICE 2	0.60	60.00	DIRTY FILTERS	REPLACED ENGINE AIR CLEANER AND REPLACED CABIN DUST AND POLLEN FILTER.
603346	4	1	I	12JMZ08	BRAKE VIBRATION	0	150.00	EXCESSIVE RUNOUT IN ROTORS	RESURFACED FRONT ROTORS AND RETAINED PADS
603346	5	1	I	01JMZ1SERVICE	SERVICE 1	0	13.99	MAINTENANCE	PERFORMED TIRE ROTATION
603346	6	1	I	16JMZ	WHEELS/TIRES	0	0.00	DAMAGED	REPLACED WHEEL COVER
603346	7	1	I	01JMZWIPERINSER	FRONT WIPER INSERTS	0	0.00	RECOMMENDED MAINTENANCE	COMPLETED REPLACEMENT OF WIPER INSERTS
603346	8	1	I	18JMZ07	BATTERY	0.30	30.00	OUT OF DATE FOR CERTIFICATION CRITERIA	REPLACED BATTERY
603346	9	1	I	27JMZ	ACCESSORIES	0.10	10.00	WORN	REPLACED ALL FLOOR MATS
603427	1	1	I	03JMZ01	CERTIFIED INSPECT	1.50	150.00	AS PER UCD REQUEST.	COMPLETED INSPECTION
603427	2	1	I	01JMZRED	LUBE OIL AND FILTER	0	13.50	RECOMMENDED MAINTENANCE	COMPLETED SERVICE
603427	3	1	I	01JMZ2SERVICE	SERVICE 2	0.30	30.00	FOUND BOTH ENGINE AND CABIN FILTERS DIRTY.	REPLACED ENGINE AIR CLEANER AND REPLACED CABIN DUST AND POLLEN FILTER.
603427	4	1	I	16JMZ05	ROTATE AND BALANCE	0	60.00	DUE TO VIBRATION WHILE DRIVING AT HIGHWAY SPEEDS.	PERFORMED BALANCE AND ROTATION.
603427	5	1	I	01JMZWIPERINSER	FRONT WIPER INSERTS	0	0.00	RECOMMENDED MAINTENANCE	COMPLETED REPLACEMENT OF WIPER INSERTS
603427	6	1	I	16JMZ07	MOUNT AND BALANCE	0	0.00		DONE
603713	1	1	C	28JMZ	BODY REPAIR	7.40	340.40	 	REPAIRED
603713	2	1	C	29JMZ	BODY REFINISH	6.50	299.00		REFINISHED
603869	1	1	I	27JMZ	ACCESSORIES	0	10.00	NEW CAR DEPT	INSTALLED
603955	1	1	I	03JMZ01	CERTIFIED INSPECT	1.50	150.00	PER UCD	COMPLETED INSPECTION
603955	2	1	I	01JM30K	30,60,90K SERVICE	0	242.17	DUE BY AGE/MILEAGE OF VEHICLE	COMPLETED 30K SERVICE
603955	3	1	I	12JMZ	BRAKES	0	150.00	VIBRATION WHILE BRAKING	RESURFACED FRONT ROTORS
603955	4	1	I	16JMZ07	MOUNT AND BALANCE	0	80.00	LOW TREAD	REPLACED 4 TIRES AND BALANCED
603955	5	1	I	01JMZALIGN	4WHEEL ALIGNMENT	0	59.99	RECOMMENDED MAINTENANCE	COMPLETED ALIGNMENT AND ROAD TESTED
603955	6	1	I	27JMZ	ACCESSORIES	0	10.00	WORN CARPET ON MATS	REPLACED ALL 4 FLOOR MATS
603955	7	1	I	01JMZWIPERINSER	FRONT WIPER INSERTS	0	0.00	RECOMMENDED MAINTENANCE	COMPLETED REPLACEMENT OF WIPER INSERTS
603955	8	1	I	16JMZ	WHEELS/TIRES	0	0.00	SCRATCHED	REPLACED RF WHEEL COVER
604023	1	1	C	01JMZ105KSERV4C	105K SERVICE 4CYL	0	239.67	DUE BY AGE/MILEAGE OF VEHICLE	PERFORMED 105K SERVICE: REPLACED SPARK PLUGS, PERFORMED VALVE ADJUSTMENT, LUBE OIL AND FILTER CHANGE, PERFORMED TIRE ROTATION AND BRAKE INSPECTION, PERFORMED COOLANT SYSTEM FLUID CHANGE, PERFORMED MULTIPOINT INSPECTION.
604023	2	1	C	07JMZTIMINGBELT	TIMING BELT PACKAGE	0	232.86	DUE BY TIME/MILEAGE OF VEHICLE	REPLACED TIMING BELT, WATER PUMP, DRIVE BELTS, TIMING BELT TENSIONER (IF APPLICABLE), AND COOLANT. INSPECTED CAM AND CRANK SEAL AND ASSOCIATED PARTS. OPERATING AS DESIGNED AT THIS TIME.
604041	1	1	I	07JMZ	ENGINE MINOR	0.50	35.00	PER AHM	COMPLETED PRESCAN PER HONDA GUIDELINES: NO DTC'S FOUND AT THIS TIME
604047	1	1	C	06JMZZ	DRIVEABILITY CONCERN	0	0.00	VERIFIED COMPLAINT.  FOUND NOISE IS COMING FROM THE^PASSENGER SIDE REAR BRAKE CALIPER.  THE CALIPER IS FROZEN^AND NOT FULLY RELEASING UNTIL VEHICLE HAS REACHED HIGHER^SPEEDS.  NEED TO REPLACE THE PASSENGER REAR BRAKE CALIPER^AND REPLACE THE REAR BRAKE PADS AND THE REAR BRAKE ROTORS. ^THE ROTORS ARE WARPING AND CAN NOT BE MACHINED AND THE BRAKE^PADS ARE WEARING UN EVEN.	RECOMMEND INSTALLING NEW PASSENGER REAR BRAKE CALIPER INSTALLING NEW REAR BRAKE PADS AND NEW REAR BRAKE ROTORS.
604058	1	1	C	28JMZ	BODY REPAIR	19.60	980.00	 	REPAIRED
604058	2	1	C	29JMZ	BODY REFINISH	9.70	465.60		REFINISHED
\.


--
-- Data for Name: repair_job_technician_detail; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY repair_job_technician_detail (ro_number, ro_line, ro_job_line, ro_job_tech_line, tech_id, work_date, work_start_time, work_end_time, work_note, actual_hours, booked_hours) FROM stdin;
603346	1	1	1	11517	\N	\N	\N	\N	1.50	\N
603346	2	1	1	11517	\N	\N	\N	\N	0.30	\N
603346	3	1	1	11517	\N	\N	\N	\N	0.60	\N
603346	4	1	1	11517	\N	\N	\N	\N	2.00	\N
603346	5	1	1	11517	\N	\N	\N	\N	0.30	\N
603346	6	1	1	11517	\N	\N	\N	\N	0.00	\N
603346	7	1	1	11517	\N	\N	\N	\N	0.00	\N
603346	8	1	1	11517	\N	\N	\N	\N	0.30	\N
603346	9	1	1	11517	\N	\N	\N	\N	0.10	\N
603427	1	1	1	10631	\N	\N	\N	\N	1.50	\N
603427	2	1	1	10631	\N	\N	\N	\N	0.30	\N
603427	3	1	1	10631	\N	\N	\N	\N	0.60	\N
603427	4	1	1	10631	\N	\N	\N	\N	0.80	\N
603427	5	1	1	10631	\N	\N	\N	\N	0.00	\N
603427	6	1	1	10631	\N	\N	\N	\N	0.00	\N
603713	1	1	1	JB00	\N	\N	\N	\N	0.00	\N
603713	1	1	2	10794	\N	\N	\N	\N	1.80	\N
603713	1	1	3	4184	\N	\N	\N	\N	1.80	\N
603713	1	1	4	10932	\N	\N	\N	\N	1.80	\N
603713	1	1	5	JP00	\N	\N	\N	\N	0.00	\N
603713	1	1	6	484	\N	\N	\N	\N	0.67	\N
603713	1	1	7	10874	\N	\N	\N	\N	0.67	\N
603713	1	1	8	11055	\N	\N	\N	\N	0.66	\N
603713	2	1	1	JP00	\N	\N	\N	\N	0.00	\N
603713	2	1	2	484	\N	\N	\N	\N	2.17	\N
603713	2	1	3	10874	\N	\N	\N	\N	2.17	\N
603713	2	1	4	11055	\N	\N	\N	\N	2.16	\N
603869	1	1	1	11025	\N	\N	\N	\N	0.10	\N
603955	1	1	1	11517	\N	\N	\N	\N	1.50	\N
603955	2	1	1	11517	\N	\N	\N	\N	3.00	\N
603955	3	1	1	11517	\N	\N	\N	\N	2.00	\N
603955	4	1	1	11517	\N	\N	\N	\N	1.60	\N
603955	5	1	1	11517	\N	\N	\N	\N	1.50	\N
603955	6	1	1	11517	\N	\N	\N	\N	0.10	\N
603955	7	1	1	11517	\N	\N	\N	\N	0.00	\N
603955	8	1	1	11517	\N	\N	\N	\N	0.00	\N
604023	1	1	1	11517	\N	\N	\N	\N	2.50	\N
604023	2	1	1	11517	\N	\N	\N	\N	5.50	\N
604041	1	1	1	11208	\N	\N	\N	\N	0.50	\N
604047	1	1	1	10277	\N	\N	\N	\N	0.00	\N
604058	1	1	1	JB00	\N	\N	\N	\N	0.00	\N
604058	1	1	2	10794	\N	\N	\N	\N	6.53	\N
604058	1	1	3	4184	\N	\N	\N	\N	6.53	\N
604058	1	1	4	10932	\N	\N	\N	\N	6.54	\N
604058	2	1	1	10874	\N	\N	\N	\N	4.85	\N
604058	2	1	2	11055	\N	\N	\N	\N	4.85	\N
\.


--
-- Data for Name: repair_line; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY repair_line (ro_number, ro_line, complaint) FROM stdin;
603346	1	PERFORM HONDA CERTIFIED USED VEHICLE INSPECTION
603346	2	PERFORM A SERVICE
603346	3	CUSTOMER REQUESTS SERVICE 2 ENGINE AIR FILTER AND PASSENGER COMPARTMENT FILTER REPLACE
603346	4	CUSTOMER STATES VIBRATION WHEN BRAKING
603346	5	CUSTOMER REQUESTS SERVICE 1, TIRE ROTATION AND SET TIRE PRESSURES.
603346	6	REPLACE DAMAGE WHEEL COVER
603346	7	REPLACE FRONT WIPER INSERTS
603346	8	BATTERY FAILED ED-18 TEST
603346	9	INSTALL NEW FLOOR MATS
603427	1	PERFORM HONDA CERTIFIED USED VEHICLE INSPECTION
603427	2	LUBE OIL AND FILTER SERVICE
603427	3	CUSTOMER REQUESTS SERVICE 2 ENGINE AIR FILTER AND PASSENGER COMPARTMENT FILTER REPLACE
603427	4	CUSTOMER REQUESTS TIRES ROTATED AND BALANCED
603427	5	REPLACE FRONT WIPER INSERTS
603427	6	PLEASE MOUNT AND BALANCE 2 TIRES
603713	1	BACK D/S
603713	2	REFINISH
603869	1	INSTALL ALL WEATHER FLOOR MATS
603955	1	PERFORM HONDA CERTIFIED USED VEHICLE INSPECTION
603955	2	CUSTOMER REQUESTS 30K SERVICE. OIL AND FILTER CHANGE, TIRE ROTATION AND ADJUST INFLATION TO PROPER PSI, BRAKE INSPECTION WHICH CHECKS BRAKE PAD THICKNES S AND WEAR, BRAKE LINES, BRAKE CALIPERS AND WHEEL CYLINDER (WHEN APPLICABLE). ENGINE AIR FILTER REPLACEMENT. PASSENGER COMPARTMENT HEPA FILTER REPLACEMENT. TRANSMISSION FLUID SERVICE DRAIN AND REFILL. BRAKE FLUID FLUSH. MULTI POINT INSPECTION. EXTERIOR CAR WASH(WEATHER PERMITTING). (AWD VEHICLES ADD REAR DIFFERENTIAL SERVICE, VEHICLES WITH 9 SPEED TRANSMISSION PRICE IS HIGHER).
603955	3	FRONT ROTOR RESURFACE
603955	4	PLEASE MOUNT AND BALANCE 4 TIRES
603955	5	PERFORM 4 WHEEL ALIGNMENT
603955	6	PLEASE INSTALL FLOOR MATS
603955	7	REPLACE FRONT WIPER INSERTS
603955	8	PLEASE INSTALL WHEEL COVER
604023	1	CUSTOMER REQUESTS 105K SERVICE ON A 4 CYLINDER VEHICLE.
604023	2	CUSTOMER REQUESTS TIMING BELT PACKAGE., INCLUDES CAM AND CRANK SEALS
604041	1	CUST STATES COMPLETE PRESCAN PER AHM GUIDELINES, PER ANN AT JOE MORGAN COLLISION PER RO 604009
604047	1	CUSTOMER STATES; HAD PARKING BRAKE ADJUSTED 01/06/2017. SINCE THEN HAS BEEN HEARING A TICKING NOISE COMING FROM THE REAR PASSENGER SIDE WHEN BRAKING. HAD IN BUT WAS UNABLE TO DUPLICATE AT THAT TIME. CUSTOMER STILL HEARD TICKING WHEN PULLING OUT OF THE DRIVE. SAYS THIS IS AT SLOW SPEEDS AND LIGHT BRAKING. (HAS SENT A VIDEO TO MY PHONE) CAN BE BEST HEARD IF SITTING IN THE REAR PASSENGER SEAT. NOT PRESENT ON HIGHWAY OR HIGHER SPEEDS. PLEASE CHECK AND ADVISE.
604058	1	REAR LEFT SIDE
604058	2	REFINISH
\.


--
-- Data for Name: repair_order; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY repair_order (ro_number, creation_date, completion_date, department, branch, sub_branch, advisor) FROM stdin;
603346	2017-06-21	2017-07-01	S	06S	JM	\N
603427	2017-06-21	2017-07-01	S	06S	JM	JESSIE KEETON
603713	2017-06-26	2017-06-30	B	06S	JM	ROBERT BROCKER
603869	2017-06-28	2017-07-03	S	06S	JM	\N
603955	2017-06-28	2017-07-01	S	06S	JM	JESSIE KEETON
604023	2017-06-30	2017-06-30	S	06S	JM	BRANDON WAGNER
604041	2017-06-30	2017-06-30	S	06S	JM	BRANDON WAGNER
604047	2017-06-30	2017-06-30	S	06S	JM	JESSIE KEETON
604058	2017-06-30	2017-07-12	B	06S	JM	ROBERT BROCKER
\.


--
-- Data for Name: repair_order_customer; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY repair_order_customer (ro_number, customer_name, customer_address, customer_city, customer_state, customer_zip, customer_phone, customer_email) FROM stdin;
603346	JOE MORGAN HONDA	\N	MONROE	OH	450501712	(*************	<EMAIL>
603427	JOE MORGAN HONDA	\N	MONROE	OH	450501712	(*************	<EMAIL>
603713	MICHAEL FOX	\N	CINCINNATI	OH	45233	\N	\N
603869	VOSS AUTO NETWORK	\N	CENTERVILLE	OH	454596523	(*************	\N
603955	JOE MORGAN HONDA	\N	MONROE	OH	450501712	(*************	<EMAIL>
604023	DAN ETHRIDGE	\N	MONROE	OH	450505496	\N	\N
604041	ROBERT SANDERS	\N	CINCINNATI	OH	45237	\N	\N
604047	GAYATHRI MAHESH	\N	CENTERVILLE	OH	45458	\N	<EMAIL>
604058	DAVID WILLIAMS	\N	LEBANON	OH	45036	\N	\N
\.


--
-- Data for Name: repair_order_print; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY repair_order_print (ro_number) FROM stdin;
\.


--
-- Data for Name: repair_order_vehicle; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY repair_order_vehicle (ro_number, vehicle_year, vehicle_make, vehicle_model, vehicle_vin, mileage_in) FROM stdin;
603346	2014	HONDA	CIVIC SEDAN	19XFB2F59EE051787	20115
603427	2014	HONDA	ACCORD SDN	1HGCR2F74EA079274	21538
603713	2009	HONDA	ODYSSEY	5FNRL38769B002531	168046
603869	2017	HONDA	CR-V	5J6RW2H54HL052396	1
603955	2014	HONDA	CIVIC SEDAN	19XFB2F56EE035515	31752
604023	2002	HONDA	CIVIC	1HGEM22902L029115	112672
604041	2016	HONDA	CIVIC COUPE	2HGFC3B78GH353445	14562
604047	2008	HONDA	ACCORD	1HGCP36858A060094	170468
604058	2008	HONDA	ACCORD SDN	1HGCP26458A012781	147052
\.


--
-- Data for Name: repair_other; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY repair_other (ro_other_id, ro_number, item_type, ro_line, other_code, other_description, other_cost, other_sale) FROM stdin;
1	603346	MISC	1	\N	\N	0	19.99
2	603427	MISC	1	\N	\N	0	19.99
3	603713	MISC	2	\N	\N	0	201.10
4	603869	MISC	1	\N	\N	0	0.80
5	603955	MISC	1	\N	\N	0	19.99
6	604023	MISC	1	\N	\N	0	1.55
7	604023	MISC	2	\N	\N	0	-22.16
8	604047	MISC	1	\N	\N	0	0.00
9	604058	MISC	2	\N	\N	0	336.90
10	603346	GOG	2	\N	\N	1.827	3.440
11	603427	GOG	2	\N	\N	1.395	2.400
12	603955	GOG	2	\N	\N	1.827	3.440
13	604023	GOG	1	\N	\N	1.395	2.400
\.


--
-- Name: repair_other_ro_other_id_seq; Type: SEQUENCE SET; Schema: du_proxy_invoice_test; Owner: postgres
--

SELECT pg_catalog.setval('repair_other_ro_other_id_seq', 13, true);


--
-- Data for Name: repair_part; Type: TABLE DATA; Schema: du_proxy_invoice_test; Owner: postgres
--

COPY repair_part (ro_number, ro_line, ro_job_line, ro_part_line, part_number, part_description, quantity_sold, unit_cost, unit_sale) FROM stdin;
603346	2	1	1	PK1	O/F KIT	1	0.00	0
603346	2	1	2	HP15400-PLM-A02	FILTER, OIL	1	4.38	5.48
603346	2	1	3	HP94109-14000	WASHER, DRAIN (14	1	0.28	0.35
603346	3	1	1	HP17220-R1A-A01	ELEMENT, AIR CLEA	1	11.69	14.61
603346	3	1	2	HP80292-SDA-407	ELEMENT FILTER	1	12.99	16.24
603346	6	1	1	HP44733-TR3-A00	TRIM, WHEEL (15)	1	15.25	19.06
603346	7	1	1	HP76623-SNA-A12	RUB, BLADE (575MM	1	3.69	4.61
603346	7	1	2	HP76622-STK-A02	RUB, BLADE (650MM	1	4.02	5.03
603346	8	1	1	HP31500-SR1-100M	*31500-SR1-100MS	1	85.19	102.74
603346	8	1	2	HP31500-SR1-100M	CORE RETURN	-1	15.00	15.00
603346	9	1	1	HP83600-TR3-A11ZA	FLR MAT SET *NH16	1	75.90	94.88
603427	2	1	1	PK1	O/F KIT	1	0.00	0
603427	2	1	2	HP15400-PLM-A02	FILTER, OIL	1	4.38	5.48
603427	2	1	3	HP94109-14000	WASHER, DRAIN (14	1	0.28	0.35
603427	3	1	1	HP17220-5A2-A00	ELEMENT, AIR CLEA	1	16.62	20.78
603427	5	1	1	HP80292-SDA-407	ELEMENT FILTER	1	12.99	16.24
603427	5	1	2	HP76622-STK-A02	RUB, BLADE (650MM	1	4.02	5.03
603427	5	1	3	HP76632-TA0-A02	RUB, BLADE (475MM	1	3.69	4.61
603427	6	1	1	HP42751-GYR-045	215/55R17 94V	2	103.50	116.33
603713	1	1	1	HP75722-SHJ-A20	EMBLEM, RR.	1	18.13	30.22
603713	1	1	2	HP04715-SHJ-A92ZZ	FACE, RR. (DOT)	1	299.37	321.75
603713	1	1	3	HP34155-SHJ-A51	LIGHT ASSY., L. L	1	126.26	152.00
603869	1	1	1	HP08P17-TLA-110	ALL SEASON MAT HI	1	102.00	127.50
603955	2	1	1	PK30A	30,000 SERVICE AT	1	0.00	0
603955	2	1	2	HPATF	A TRANS FLUID	3	3.95	4.94
603955	2	1	3	HP90471-PX4-000	WASHER, DRAIN (18	1	1.66	2.08
603955	2	1	4	HP94109-14000	WASHER, DRAIN (14	1	0.28	0.35
603955	2	1	5	HP15400-PLM-A02	FILTER, OIL	1	4.38	5.48
603955	2	1	6	HP08798-9108	FLUID, DOT 3 BRAK	1	6.74	8.43
603955	2	1	7	HP17220-R1A-A01	ELEMENT, AIR CLEA	1	11.69	14.61
603955	2	1	8	HP80292-SDA-407	ELEMENT FILTER	1	12.99	16.24
603955	4	1	1	HP42751-FIR-011	195/65R15 89H	4	89.50	103.00
603955	6	1	1	HP83600-TR3-A11ZA	FLR MAT SET *NH16	1	75.90	94.88
603955	7	1	1	HP44733-TR3-A00	TRIM, WHEEL (15)	1	15.25	19.06
603955	7	1	2	HP76630-TR0-A02	BLADE, WSW (575MM	1	16.33	20.41
603955	7	1	3	HP76620-TR0-A02	BLADE, WSW (650MM	1	19.97	24.96
604023	1	1	1	PK1	O/F KIT	1	0.00	0
604023	1	1	2	HP15400-PLM-A02	FILTER, OIL	1	4.38	7.30
604023	1	1	3	HP94109-14000	WASHER, DRAIN (14	1	0.28	0.47
604023	1	1	4	HPOL999-9011	COOLANT (TYPE-2)	1	11.37	18.95
604023	1	1	5	HP12341-PLC-000	GASKET, CYL HD CV	1	7.30	12.17
604023	1	1	6	HP12290-PGE-A01	S/PLG (PKJ20CR-M1	4	13.56	22.60
604023	2	1	1	HP14400-PMM-A02	BELT, TIMING	1	18.86	31.43
604023	2	1	2	HP19200-PLM-A01	WATER PUMP	1	58.70	97.83
604023	2	1	3	HP38920-PLR-003	BELT, COMPRESSOR	1	15.32	25.53
604023	2	1	4	HP56992-PLM-013	BELT,P/S P (4PK10	1	12.12	20.20
604023	2	1	5	HP91213-P2F-A01	OIL SEAL (29X45X8	1	3.29	5.48
604023	2	1	6	HP91212-PLM-A01	OIL SEAL (38X50X7	1	4.00	6.67
604023	2	1	7	HP11811-PLC-000	COVER, TIMING BEL	1	13.90	23.17
604058	1	1	1	HP74405-TA5-A00	TAPE, L.	1	2.05	3.42
604058	1	1	2	HP08P09-TA0-100R1	SPLASH GAURD, RR.	1	30.90	51.50
604058	1	1	3	HP04715-TA0-A91ZZ	FACE, RR. (DOT)	1	269.32	292.00
604058	1	1	4	HP71598-TA0-A00	SPACER, L. RR.	1	4.69	7.82
604058	1	1	5	HP71503-S9A-000ZD	CAP, RR. *B92P*	2	7.17	11.95
604058	1	1	6	HP71126-TA5-A00	MOLDING, FR. GRIL	1	101.75	169.58
\.


--
-- Name: dealership dealership_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY dealership
    ADD CONSTRAINT dealership_pkey PRIMARY KEY (dealership_name);


--
-- Name: repair_job repair_job_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_job
    ADD CONSTRAINT repair_job_pkey PRIMARY KEY (ro_number, ro_line, ro_job_line);


--
-- Name: repair_job_technician_detail repair_job_technician_detail_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_job_technician_detail
    ADD CONSTRAINT repair_job_technician_detail_pkey PRIMARY KEY (ro_number, ro_line, ro_job_line, ro_job_tech_line);


--
-- Name: repair_line repair_line_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_line
    ADD CONSTRAINT repair_line_pkey PRIMARY KEY (ro_number, ro_line);


--
-- Name: repair_order_customer repair_order_customer_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_order_customer
    ADD CONSTRAINT repair_order_customer_pkey PRIMARY KEY (ro_number);


--
-- Name: repair_order repair_order_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_order
    ADD CONSTRAINT repair_order_pkey PRIMARY KEY (ro_number);


--
-- Name: repair_order_print repair_order_print_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_order_print
    ADD CONSTRAINT repair_order_print_pkey PRIMARY KEY (ro_number);


--
-- Name: repair_order_vehicle repair_order_vehicle_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_order_vehicle
    ADD CONSTRAINT repair_order_vehicle_pkey PRIMARY KEY (ro_number);


--
-- Name: repair_other repair_other_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_other
    ADD CONSTRAINT repair_other_pkey PRIMARY KEY (ro_other_id);


--
-- Name: repair_part repair_part_pkey; Type: CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_part
    ADD CONSTRAINT repair_part_pkey PRIMARY KEY (ro_number, ro_line, ro_job_line, ro_part_line);


--
-- Name: repair_order_print repair_order_print_ro_number_fkey; Type: FK CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_order_print
    ADD CONSTRAINT repair_order_print_ro_number_fkey FOREIGN KEY (ro_number) REFERENCES repair_order(ro_number);


--
-- Name: repair_other repair_other_ro_number_fkey; Type: FK CONSTRAINT; Schema: du_proxy_invoice_test; Owner: postgres
--

ALTER TABLE ONLY repair_other
    ADD CONSTRAINT repair_other_ro_number_fkey FOREIGN KEY (ro_number, ro_line) REFERENCES repair_line(ro_number, ro_line);


--
-- PostgreSQL database dump complete
--

