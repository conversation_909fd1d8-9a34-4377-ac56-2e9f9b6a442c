import pandas as pd
import sys
import os.path
import codecs
import csv

proxy_file_path=sys.argv[1]
tsv_path=sys.argv[2]
tsv_file_name= tsv_path + "/proxy-text.tsv"
with open(tsv_file_name, 'w', encoding='utf8', newline='') as tsv_file:
    tsv_writer = csv.writer(tsv_file, delimiter='\t', lineterminator='\n')
    tsv_writer.writerow(["invoicenumber", "proxy"])

    for ro_file in os.listdir(proxy_file_path):
        ro = os.path.splitext(ro_file)[0]
        proxy_file = proxy_file_path + "/{0}.txt".format(ro)
        proxy_file_new = proxy_file_path + "/{0}_new.txt".format(ro)
        file_exists = os.path.isfile(proxy_file)
        convert_file = "iconv -f ISO-8859-1 -t UTF-8//TRANSLIT {0} -o {1} && mv -f {2} {3}".format(proxy_file, proxy_file_new, proxy_file_new, proxy_file)
        if file_exists is True:
            os.system(convert_file)
            proxy_file_data = codecs.open(proxy_file, "r")
            tsv_writer.writerow([ro, proxy_file_data.read()])
            # print (proxy_file_data.read())
            # df.loc[df[column_name]==ro,'text_proxy'] = proxy_file_data.read()

        else:
            print ("{0} Proxy doesnt exist".format(ro))
        # df.loc[df[column_name]==ro,'text_proxy'] = ''
# tsv_file.close()
    # print (ro)