Mon Sep 08 2025 03:39:42 GMT+0000 : CDK : Process XML schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : CDKFLEX : Process XML schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Automate :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Fortellis :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : DealerBuilt :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Adam :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Reynolds :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Autosoft :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Pbs :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Quorum :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Mpk Extraction Processing Not Enabled - Pass Job Type mpk-process-json To Enable
Mon Sep 08 2025 03:39:42 GMT+0000 : Tekion :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Tekionapi :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : Quorum :  Process JSON schedule started
Mon Sep 08 2025 03:39:42 GMT+0000 : FOPC :  Process JSON schedule started
Mon Sep 08 2025 03:40:09 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 03:40:09 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 03:40:09 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 03:40:11 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 03:40:11 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 03:40:12 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 03:40:12 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 03:40:12 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 03:40:12 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 03:40:12 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 03:40:12 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 03:40:12 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 03:40:12 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 03:40:12 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 03:40:53 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 03:40:53 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee7bc-0909-644b-005f-0d03296e7932/status
Mon Sep 08 2025 03:40:53 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee7bc-0909-644b-005f-0d03296e7932/status
Mon Sep 08 2025 03:41:25 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 03:41:25 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee7bc-0909-644b-005f-0d03296e7932/result
Mon Sep 08 2025 03:41:30 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 03:41:30 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:41:30 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:41:30 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 03:41:30 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 03:41:30 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 03:41:30 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 03:41:30 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 03:41:30 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 03:41:30 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 03:41:30 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 03:41:30 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 03:41:30 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 03:41:30 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 03:41:30 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 03:41:30 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 03:41:30 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 03:41:32 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 03:41:32 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee7bd-0909-644b-005f-0d03296e7ad2/status
Mon Sep 08 2025 03:41:32 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee7bd-0909-644b-005f-0d03296e7ad2/status
Mon Sep 08 2025 03:42:03 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 03:42:03 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 03:42:03 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee7bd-0909-644b-005f-0d03296e7ad2/result
Mon Sep 08 2025 03:42:04 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 03:42:04 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:42:04 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:42:04 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:42:04 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:42:04 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 03:42:04 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 03:42:04 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 03:42:04 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 03:42:05 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 03:42:05 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee7be-0909-64f5-005f-0d03296e4f96/status
Mon Sep 08 2025 03:42:05 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee7be-0909-64f5-005f-0d03296e4f96/status
Mon Sep 08 2025 03:42:36 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 03:42:36 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 03:42:36 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee7be-0909-64f5-005f-0d03296e4f96/result
Mon Sep 08 2025 03:42:37 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:42:37 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:42:37 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:42:37 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:42:37 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 03:42:37 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 03:42:37 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 03:42:37 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 03:42:37 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 03:42:37 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 03:42:37 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 03:42:37 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 03:43:01 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 03:43:01 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:43:01 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:43:01 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:43:01 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:43:01 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:43:01 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 03:43:01 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:43:01 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 03:43:01 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 03:43:08 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:43:08 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:43:08 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 03:43:08 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 03:43:08 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 03:43:08 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908034308258785","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 03:43:08 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 03:43:08 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 03:43:08 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908034308258785"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 03:43:08 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 03:43:08 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 03:43:08 GMT+0000 : Inside send mail method
Mon Sep 08 2025 03:43:08 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 03:43:09 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 03:45:50 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T03:40:11.012Z\",\"uniqueId\":\"fo20250908034308258785\",\"endTime\":\"2025-09-08T03:43:08.775Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 03:45:50 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 03:45:50 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 03:45:52 GMT+0000 : Inside send mail method
Mon Sep 08 2025 03:45:52 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 04:02:11 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 04:02:11 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 04:02:11 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 04:02:13 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 04:02:13 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 04:02:13 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 04:02:13 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:02:13 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 04:02:13 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 04:02:13 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 04:02:13 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 04:02:13 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 04:02:13 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 04:02:13 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 04:02:56 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 04:02:56 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee7d2-0909-5e62-005f-0d03296eb79e/status
Mon Sep 08 2025 04:02:56 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee7d2-0909-5e62-005f-0d03296eb79e/status
Mon Sep 08 2025 04:03:27 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 04:03:27 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee7d2-0909-5e62-005f-0d03296eb79e/result
Mon Sep 08 2025 04:03:32 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 04:03:32 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:03:32 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:03:32 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:03:32 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 04:03:32 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 04:03:32 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 04:03:33 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 04:03:33 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:03:33 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 04:03:33 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 04:03:33 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 04:03:33 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 04:03:33 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:03:33 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 04:03:33 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 04:03:33 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 04:03:34 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 04:03:34 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee7d3-0909-685c-005f-0d03296ea97e/status
Mon Sep 08 2025 04:03:34 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee7d3-0909-685c-005f-0d03296ea97e/status
Mon Sep 08 2025 04:04:05 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 04:04:05 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 04:04:05 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee7d3-0909-685c-005f-0d03296ea97e/result
Mon Sep 08 2025 04:04:06 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 04:04:06 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:04:06 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:04:06 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:04:06 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:04:06 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:04:06 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 04:04:06 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 04:04:06 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 04:04:06 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 04:04:06 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee7d4-0909-5e62-005f-0d03296eb826/status
Mon Sep 08 2025 04:04:06 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee7d4-0909-5e62-005f-0d03296eb826/status
Mon Sep 08 2025 04:04:37 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 04:04:37 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 04:04:37 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee7d4-0909-5e62-005f-0d03296eb826/result
Mon Sep 08 2025 04:04:38 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:04:38 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:04:38 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:04:38 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:04:38 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:04:38 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 04:04:38 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 04:04:38 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 04:04:38 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 04:04:38 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 04:04:38 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 04:04:38 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 04:04:49 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 04:04:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:04:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:04:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:04:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:04:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:04:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:04:49 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:04:49 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 04:04:49 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 04:05:01 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:05:01 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:05:01 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:05:01 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:05:02 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 04:05:02 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908040502038170","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 04:05:02 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 04:05:02 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 04:05:02 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908040502038170"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 04:05:02 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 04:05:02 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 04:05:02 GMT+0000 : Inside send mail method
Mon Sep 08 2025 04:05:02 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 04:05:02 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 04:07:04 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T04:02:13.009Z\",\"uniqueId\":\"fo20250908040502038170\",\"endTime\":\"2025-09-08T04:05:02.514Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 04:07:04 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 04:07:04 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 04:07:06 GMT+0000 : Inside send mail method
Mon Sep 08 2025 04:07:06 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 04:40:29 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 04:40:29 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 04:40:29 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 04:40:30 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 04:40:30 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 04:40:30 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 04:40:30 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:40:30 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 04:40:30 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 04:40:30 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 04:40:30 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 04:40:30 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 04:40:30 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 04:40:30 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 04:41:11 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 04:41:11 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee7f8-0909-6798-005f-0d03296fd452/status
Mon Sep 08 2025 04:41:11 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee7f8-0909-6798-005f-0d03296fd452/status
Mon Sep 08 2025 04:41:42 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 04:41:42 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee7f8-0909-6798-005f-0d03296fd452/result
Mon Sep 08 2025 04:41:48 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 04:41:48 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:41:48 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:41:48 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:41:48 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 04:41:48 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 04:41:48 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 04:41:48 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 04:41:48 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:41:48 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 04:41:48 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 04:41:48 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 04:41:48 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 04:41:48 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:41:48 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 04:41:48 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 04:41:48 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 04:41:49 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 04:41:49 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee7f9-0909-69d1-005f-0d03296fe30e/status
Mon Sep 08 2025 04:41:49 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee7f9-0909-69d1-005f-0d03296fe30e/status
Mon Sep 08 2025 04:42:20 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 04:42:20 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 04:42:20 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee7f9-0909-69d1-005f-0d03296fe30e/result
Mon Sep 08 2025 04:42:21 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 04:42:21 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:42:21 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:42:21 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:42:21 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:42:21 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:42:21 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 04:42:21 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 04:42:21 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 04:42:22 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 04:42:22 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee7fa-0909-5f75-005f-0d03296f8656/status
Mon Sep 08 2025 04:42:22 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee7fa-0909-5f75-005f-0d03296f8656/status
Mon Sep 08 2025 04:42:53 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 04:42:53 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 04:42:53 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee7fa-0909-5f75-005f-0d03296f8656/result
Mon Sep 08 2025 04:42:54 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:42:54 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:42:54 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:42:54 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:42:54 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:42:54 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 04:42:54 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 04:42:54 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 04:42:54 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 04:42:54 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 04:42:54 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 04:42:54 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 04:43:23 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 04:43:23 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:43:23 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:43:23 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:43:23 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:43:23 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:43:23 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:43:23 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 04:43:23 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 04:43:23 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 04:43:26 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:43:26 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:43:26 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 04:43:26 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 04:43:27 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 04:43:27 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908044327036784","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 04:43:27 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 04:43:27 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 04:43:27 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908044327036784"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 04:43:27 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 04:43:27 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 04:43:27 GMT+0000 : Inside send mail method
Mon Sep 08 2025 04:43:27 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 04:43:27 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 04:45:39 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T04:40:30.008Z\",\"uniqueId\":\"fo20250908044327036784\",\"endTime\":\"2025-09-08T04:43:27.517Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 04:45:39 GMT+0000 : doPayloadAction - Status: 502
Mon Sep 08 2025 04:45:39 GMT+0000 : doPayloadAction - body: "<html>\r\n<head><title>502 Bad Gateway</title></head>\r\n<body>\r\n<center><h1>502 Bad Gateway</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Mon Sep 08 2025 04:45:41 GMT+0000 : Inside send mail method
Mon Sep 08 2025 04:45:41 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 05:06:53 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T04:40:30.008Z\",\"uniqueId\":\"fo20250908044327036784\",\"endTime\":\"2025-09-08T04:43:27.517Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 05:06:53 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 05:06:53 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 05:06:55 GMT+0000 : Inside send mail method
Mon Sep 08 2025 05:06:55 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 05:13:05 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/15/2025\",\"endDate\":\"03/22/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 05:13:05 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 05:13:05 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 05:13:07 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 05:13:07 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 05:13:07 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 05:13:07 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:13:07 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 05:13:07 GMT+0000 : {}:Date batch array of closed RO pull is [["03/15/2025","03/22/2025"]] 
Mon Sep 08 2025 05:13:07 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 05:13:07 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/15/2025,03/22/2025
Mon Sep 08 2025 05:13:07 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-15T00:00:00.000Z  End date:2025-03-22T00:00:00.000Z
Mon Sep 08 2025 05:13:07 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 05:13:07 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 05:13:48 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 05:13:48 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee819-0909-69d1-005f-0d032970e11a/status
Mon Sep 08 2025 05:13:48 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee819-0909-69d1-005f-0d032970e11a/status
Mon Sep 08 2025 05:14:19 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 05:14:19 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee819-0909-69d1-005f-0d032970e11a/result
Mon Sep 08 2025 05:14:25 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-15T00:00:00.000Z  End date:2025-03-22T00:00:00.000Z
Mon Sep 08 2025 05:14:25 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:14:25 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:14:25 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:14:25 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 05:14:25 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 05:14:25 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 05:14:25 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 05:14:25 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:14:25 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 05:14:25 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 05:14:25 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 05:14:25 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 05:14:25 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:14:25 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 05:14:25 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 05:14:25 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 05:14:26 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 05:14:26 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee81a-0909-69d1-005f-0d032970e18e/status
Mon Sep 08 2025 05:14:26 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee81a-0909-69d1-005f-0d032970e18e/status
Mon Sep 08 2025 05:14:57 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 05:14:57 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 05:14:57 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee81a-0909-69d1-005f-0d032970e18e/result
Mon Sep 08 2025 05:14:59 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 05:14:59 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:14:59 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:14:59 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:14:59 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:14:59 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:14:59 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 05:14:59 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 05:14:59 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 05:15:00 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 05:15:00 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee81b-0909-5f75-005f-0d032970d366/status
Mon Sep 08 2025 05:15:00 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee81b-0909-5f75-005f-0d032970d366/status
Mon Sep 08 2025 05:15:31 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 05:15:31 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 05:15:31 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee81b-0909-5f75-005f-0d032970d366/result
Mon Sep 08 2025 05:15:32 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:15:32 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:15:32 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:15:32 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:15:32 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:15:32 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 05:15:32 GMT+0000 : {}:Date batch array of closed RO pull is [["03/15/2025","03/22/2025"]] 
Mon Sep 08 2025 05:15:32 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 05:15:32 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/15/2025,03/22/2025
Mon Sep 08 2025 05:15:32 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-15T00:00:00.000Z  End date:2025-03-22T00:00:00.000Z
Mon Sep 08 2025 05:15:32 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 05:15:32 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 05:15:59 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-15T00:00:00.000Z  End date:2025-03-22T00:00:00.000Z
Mon Sep 08 2025 05:15:59 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:15:59 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:15:59 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:15:59 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:15:59 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:15:59 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:15:59 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:15:59 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 05:15:59 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 05:16:06 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:16:06 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:16:06 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:16:06 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:16:06 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 05:16:06 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908051606321622","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 05:16:06 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 05:16:06 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 05:16:06 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908051606321622"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 05:16:06 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 05:16:06 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 05:16:06 GMT+0000 : Inside send mail method
Mon Sep 08 2025 05:16:06 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 05:16:07 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 05:18:04 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/15/2025\",\"endDate\":\"03/22/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T05:13:07.010Z\",\"uniqueId\":\"fo20250908051606321622\",\"endTime\":\"2025-09-08T05:16:06.840Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 05:18:04 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 05:18:04 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 05:18:06 GMT+0000 : Inside send mail method
Mon Sep 08 2025 05:18:06 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 05:18:14 GMT+0000 : Resume Processor {"extractFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip","dms":"FORTELLIS","processorUniqueId":"fo20250908051606321622-*************"}
Mon Sep 08 2025 05:19:16 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"resume","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/15/2025\",\"endDate\":\"03/22/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T05:13:07.010Z\",\"uniqueId\":\"fo20250908051606321622\",\"endTime\":\"2025-09-08T05:16:06.840Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\",\"haltOverRide\":true}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 05:19:16 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 05:19:16 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 05:21:03 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/15/2025\",\"endDate\":\"03/22/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T05:13:07.010Z\",\"uniqueId\":\"fo20250908051606321622\",\"endTime\":\"2025-09-08T05:16:06.840Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\",\"haltOverRide\":true}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 05:21:03 GMT+0000 : doPayloadAction - Status: 502
Mon Sep 08 2025 05:21:03 GMT+0000 : doPayloadAction - body: "<html>\r\n<head><title>502 Bad Gateway</title></head>\r\n<body>\r\n<center><h1>502 Bad Gateway</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Mon Sep 08 2025 05:21:05 GMT+0000 : Inside send mail method
Mon Sep 08 2025 05:21:05 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 05:56:37 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 05:56:37 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 05:56:37 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 05:56:39 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 05:56:39 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 05:56:39 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 05:56:39 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:56:39 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 05:56:39 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 05:56:39 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 05:56:39 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 05:56:39 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 05:56:39 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 05:56:39 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 05:57:20 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 05:57:20 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee844-0909-5e62-005f-0d03297152aa/status
Mon Sep 08 2025 05:57:20 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee844-0909-5e62-005f-0d03297152aa/status
Mon Sep 08 2025 05:57:52 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 05:57:52 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee844-0909-5e62-005f-0d03297152aa/result
Mon Sep 08 2025 05:57:56 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 05:57:56 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:57:56 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:57:56 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:57:56 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 05:57:56 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 05:57:56 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 05:57:56 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 05:57:56 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:57:56 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 05:57:56 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 05:57:56 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 05:57:57 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 05:57:57 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:57:57 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 05:57:57 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 05:57:57 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 05:57:57 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 05:57:57 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee845-0909-685c-005f-0d0329714612/status
Mon Sep 08 2025 05:57:57 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee845-0909-685c-005f-0d0329714612/status
Mon Sep 08 2025 05:58:28 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 05:58:28 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 05:58:28 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee845-0909-685c-005f-0d0329714612/result
Mon Sep 08 2025 05:58:30 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 05:58:30 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:58:30 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:58:30 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:58:30 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:58:30 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:58:30 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 05:58:30 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 05:58:30 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 05:58:30 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 05:58:30 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee846-0909-6798-005f-0d0329710cc6/status
Mon Sep 08 2025 05:58:30 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee846-0909-6798-005f-0d0329710cc6/status
Mon Sep 08 2025 05:59:01 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 05:59:01 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 05:59:01 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee846-0909-6798-005f-0d0329710cc6/result
Mon Sep 08 2025 05:59:02 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:59:02 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:59:02 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:59:02 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:59:02 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:59:02 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 05:59:02 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 05:59:02 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 05:59:02 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 05:59:02 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 05:59:02 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 05:59:02 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 05:59:27 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 05:59:27 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:59:27 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:59:27 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:59:27 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:59:27 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:59:27 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:59:27 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 05:59:27 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 05:59:27 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 05:59:33 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:59:33 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:59:33 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 05:59:33 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 05:59:33 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 05:59:33 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908055933303793","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 05:59:33 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 05:59:33 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 05:59:33 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908055933303793"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 05:59:33 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 05:59:33 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 05:59:33 GMT+0000 : Inside send mail method
Mon Sep 08 2025 05:59:33 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 05:59:34 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 06:01:50 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T05:56:39.012Z\",\"uniqueId\":\"fo20250908055933303793\",\"endTime\":\"2025-09-08T05:59:33.802Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 06:01:50 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 06:01:50 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 06:01:52 GMT+0000 : Inside send mail method
Mon Sep 08 2025 06:01:52 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 06:09:00 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/29/2025\",\"endDate\":\"03/31/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 06:09:00 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 06:09:00 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 06:09:02 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 06:09:02 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 06:09:02 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 06:09:02 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:09:02 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 06:09:02 GMT+0000 : {}:Date batch array of closed RO pull is [["03/29/2025","03/31/2025"]] 
Mon Sep 08 2025 06:09:02 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 06:09:02 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/29/2025,03/31/2025
Mon Sep 08 2025 06:09:02 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-29T00:00:00.000Z  End date:2025-03-31T00:00:00.000Z
Mon Sep 08 2025 06:09:02 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 06:09:02 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 06:09:43 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 06:09:43 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee851-0909-5f75-005f-0d032971a0d6/status
Mon Sep 08 2025 06:09:43 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee851-0909-5f75-005f-0d032971a0d6/status
Mon Sep 08 2025 06:10:14 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 06:10:14 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee851-0909-5f75-005f-0d032971a0d6/result
Mon Sep 08 2025 06:10:17 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-29T00:00:00.000Z  End date:2025-03-31T00:00:00.000Z
Mon Sep 08 2025 06:10:17 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:10:17 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:10:17 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:10:17 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 06:10:17 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 06:10:17 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 06:10:17 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 06:10:17 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:10:17 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 06:10:17 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 06:10:17 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 06:10:17 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 06:10:17 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:10:17 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 06:10:17 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 06:10:17 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 06:10:18 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 06:10:18 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee852-0909-6a61-005f-0d0329711cf2/status
Mon Sep 08 2025 06:10:18 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee852-0909-6a61-005f-0d0329711cf2/status
Mon Sep 08 2025 06:10:49 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 06:10:49 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 06:10:49 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee852-0909-6a61-005f-0d0329711cf2/result
Mon Sep 08 2025 06:10:49 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 06:10:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:10:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:10:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:10:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:10:49 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:10:49 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 06:10:49 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 06:10:49 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 06:10:50 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 06:10:50 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee852-0909-5f75-005f-0d032971a31a/status
Mon Sep 08 2025 06:10:50 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee852-0909-5f75-005f-0d032971a31a/status
Mon Sep 08 2025 06:11:20 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 06:11:20 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 06:11:20 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee852-0909-5f75-005f-0d032971a31a/result
Mon Sep 08 2025 06:11:21 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:11:21 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:11:21 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:11:21 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:11:21 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:11:21 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 06:11:21 GMT+0000 : {}:Date batch array of closed RO pull is [["03/29/2025","03/31/2025"]] 
Mon Sep 08 2025 06:11:21 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 06:11:21 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/29/2025,03/31/2025
Mon Sep 08 2025 06:11:21 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-29T00:00:00.000Z  End date:2025-03-31T00:00:00.000Z
Mon Sep 08 2025 06:11:21 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 06:11:21 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 06:11:44 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-29T00:00:00.000Z  End date:2025-03-31T00:00:00.000Z
Mon Sep 08 2025 06:11:44 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:11:44 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:11:44 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:11:44 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:11:44 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:11:44 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:11:44 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 06:11:44 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:11:44 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 06:11:50 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:11:50 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:11:50 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:11:50 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:11:50 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 06:11:50 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908061150328152","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 06:11:50 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 06:11:50 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 06:11:50 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908061150328152"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 06:11:50 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 06:11:50 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 06:11:50 GMT+0000 : Inside send mail method
Mon Sep 08 2025 06:11:50 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 06:11:50 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 06:13:37 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/29/2025\",\"endDate\":\"03/31/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T06:09:02.011Z\",\"uniqueId\":\"fo20250908061150328152\",\"endTime\":\"2025-09-08T06:11:50.557Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 06:13:37 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 06:13:37 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 06:13:39 GMT+0000 : Inside send mail method
Mon Sep 08 2025 06:13:39 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 06:25:46 GMT+0000 : Resume Processor {"extractFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip","dms":"FORTELLIS","processorUniqueId":"fo20250908061150328152-*************"}
Mon Sep 08 2025 06:26:44 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"resume","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/29/2025\",\"endDate\":\"03/31/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T06:09:02.011Z\",\"uniqueId\":\"fo20250908061150328152\",\"endTime\":\"2025-09-08T06:11:50.557Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\",\"haltOverRide\":true}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 06:26:44 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 06:26:44 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 06:26:57 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/29/2025\",\"endDate\":\"03/31/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T06:09:02.011Z\",\"uniqueId\":\"fo20250908061150328152\",\"endTime\":\"2025-09-08T06:11:50.557Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\",\"haltOverRide\":true}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 06:26:57 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 06:26:57 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 06:26:59 GMT+0000 : Inside send mail method
Mon Sep 08 2025 06:26:59 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 06:52:01 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/08/2025\",\"endDate\":\"03/15/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 06:52:01 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 06:52:01 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 06:52:03 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 06:52:03 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 06:52:03 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 06:52:03 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:52:03 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 06:52:03 GMT+0000 : {}:Date batch array of closed RO pull is [["03/08/2025","03/15/2025"]] 
Mon Sep 08 2025 06:52:03 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 06:52:03 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/08/2025,03/15/2025
Mon Sep 08 2025 06:52:03 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-08T00:00:00.000Z  End date:2025-03-15T00:00:00.000Z
Mon Sep 08 2025 06:52:03 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 06:52:03 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 06:52:45 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 06:52:45 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee87c-0909-69d1-005f-0d0329716e82/status
Mon Sep 08 2025 06:52:45 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee87c-0909-69d1-005f-0d0329716e82/status
Mon Sep 08 2025 06:53:16 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 06:53:16 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee87c-0909-69d1-005f-0d0329716e82/result
Mon Sep 08 2025 06:53:21 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-08T00:00:00.000Z  End date:2025-03-15T00:00:00.000Z
Mon Sep 08 2025 06:53:21 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:53:21 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:53:21 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:53:21 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 06:53:21 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 06:53:21 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 06:53:21 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 06:53:21 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:53:21 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 06:53:21 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 06:53:21 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 06:53:21 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 06:53:21 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:53:21 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 06:53:21 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 06:53:21 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 06:53:22 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 06:53:22 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee87d-0909-6a94-005f-0d0329718d76/status
Mon Sep 08 2025 06:53:22 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee87d-0909-6a94-005f-0d0329718d76/status
Mon Sep 08 2025 06:53:53 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 06:53:53 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 06:53:53 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee87d-0909-6a94-005f-0d0329718d76/result
Mon Sep 08 2025 06:53:54 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 06:53:54 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:53:54 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:53:54 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:53:54 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:53:54 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:53:54 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 06:53:54 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 06:53:54 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 06:53:55 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 06:53:55 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee87d-0909-5cb2-005f-0d0329713eba/status
Mon Sep 08 2025 06:53:55 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee87d-0909-5cb2-005f-0d0329713eba/status
Mon Sep 08 2025 06:54:25 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 06:54:25 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 06:54:25 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee87d-0909-5cb2-005f-0d0329713eba/result
Mon Sep 08 2025 06:54:26 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:54:26 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:54:26 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:54:26 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:54:26 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:54:26 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 06:54:26 GMT+0000 : {}:Date batch array of closed RO pull is [["03/08/2025","03/15/2025"]] 
Mon Sep 08 2025 06:54:26 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 06:54:26 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/08/2025,03/15/2025
Mon Sep 08 2025 06:54:26 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-08T00:00:00.000Z  End date:2025-03-15T00:00:00.000Z
Mon Sep 08 2025 06:54:26 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 06:54:26 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 06:54:49 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-08T00:00:00.000Z  End date:2025-03-15T00:00:00.000Z
Mon Sep 08 2025 06:54:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:54:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:54:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:54:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:54:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:54:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:54:49 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 06:54:49 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 06:54:49 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 06:54:51 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:54:51 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:54:51 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 06:54:51 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 06:54:51 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 06:54:51 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908065451345829","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 06:54:51 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 06:54:51 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 06:54:51 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908065451345829"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 06:54:51 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 06:54:51 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 06:54:51 GMT+0000 : Inside send mail method
Mon Sep 08 2025 06:54:51 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 06:54:52 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 06:56:34 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/08/2025\",\"endDate\":\"03/15/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T06:52:03.012Z\",\"uniqueId\":\"fo20250908065451345829\",\"endTime\":\"2025-09-08T06:54:51.853Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 06:56:34 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 06:56:34 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 06:56:36 GMT+0000 : Inside send mail method
Mon Sep 08 2025 06:56:36 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 06:56:37 GMT+0000 : Resume Processor {"extractFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip","dms":"FORTELLIS","processorUniqueId":"fo20250908065451345829-*************"}
Mon Sep 08 2025 06:56:41 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"resume","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/08/2025\",\"endDate\":\"03/15/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T06:52:03.012Z\",\"uniqueId\":\"fo20250908065451345829\",\"endTime\":\"2025-09-08T06:54:51.853Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\",\"haltOverRide\":true}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 06:56:41 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 06:56:41 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 06:58:27 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/08/2025\",\"endDate\":\"03/15/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T06:52:03.012Z\",\"uniqueId\":\"fo20250908065451345829\",\"endTime\":\"2025-09-08T06:54:51.853Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\",\"haltOverRide\":true}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 06:58:27 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 06:58:27 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 06:58:29 GMT+0000 : Inside send mail method
Mon Sep 08 2025 06:58:29 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 07:06:28 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 07:06:28 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 07:06:28 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 07:06:30 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 07:06:30 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 07:06:30 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 07:06:30 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:06:30 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 07:06:30 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 07:06:30 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 07:06:30 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 07:06:30 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 07:06:30 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 07:06:30 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 07:07:11 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 07:07:11 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee88a-0909-64f5-005f-0d0329717b12/status
Mon Sep 08 2025 07:07:11 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee88a-0909-64f5-005f-0d0329717b12/status
Mon Sep 08 2025 07:07:42 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 07:07:42 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee88a-0909-64f5-005f-0d0329717b12/result
Mon Sep 08 2025 07:07:46 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 07:07:46 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:07:46 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:07:47 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:07:47 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 07:07:47 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 07:07:47 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 07:07:47 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 07:07:47 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:07:47 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 07:07:47 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 07:07:47 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 07:07:47 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 07:07:47 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:07:47 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 07:07:47 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 07:07:47 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 07:07:47 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 07:07:47 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee88b-0909-5f75-005f-0d03297214b2/status
Mon Sep 08 2025 07:07:47 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee88b-0909-5f75-005f-0d03297214b2/status
Mon Sep 08 2025 07:08:18 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 07:08:18 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 07:08:18 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee88b-0909-5f75-005f-0d03297214b2/result
Mon Sep 08 2025 07:08:19 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 07:08:19 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:08:19 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:08:19 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:08:19 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:08:19 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:08:19 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 07:08:19 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 07:08:19 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 07:08:19 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 07:08:19 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee88c-0909-6a94-005f-0d0329718ef6/status
Mon Sep 08 2025 07:08:19 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee88c-0909-6a94-005f-0d0329718ef6/status
Mon Sep 08 2025 07:08:49 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 07:08:49 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 07:08:49 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee88c-0909-6a94-005f-0d0329718ef6/result
Mon Sep 08 2025 07:08:51 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:08:51 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:08:51 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:08:51 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:08:51 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:08:51 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 07:08:51 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 07:08:51 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 07:08:51 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 07:08:51 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 07:08:51 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 07:08:51 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 07:09:10 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 07:09:10 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:09:10 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:09:10 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:09:10 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:09:10 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:09:10 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:09:10 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:09:10 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 07:09:10 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 07:09:12 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:09:12 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:09:12 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:09:12 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:09:12 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 07:09:12 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908070912318573","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 07:09:12 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 07:09:12 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 07:09:12 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908070912318573"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 07:09:12 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 07:09:12 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 07:09:12 GMT+0000 : Inside send mail method
Mon Sep 08 2025 07:09:12 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 07:09:13 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 07:10:35 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T07:06:30.010Z\",\"uniqueId\":\"fo20250908070912318573\",\"endTime\":\"2025-09-08T07:09:12.808Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 07:10:35 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 07:10:35 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 07:10:37 GMT+0000 : Inside send mail method
Mon Sep 08 2025 07:10:37 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 07:25:36 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 07:25:36 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 07:25:36 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 07:25:38 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 07:25:38 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 07:25:38 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 07:25:38 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:25:38 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 07:25:38 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 07:25:38 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 07:25:38 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 07:25:38 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 07:25:38 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 07:25:38 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 07:26:19 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 07:26:19 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee89d-0909-5cb2-005f-0d0329723946/status
Mon Sep 08 2025 07:26:19 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee89d-0909-5cb2-005f-0d0329723946/status
Mon Sep 08 2025 07:26:50 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 07:26:50 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee89d-0909-5cb2-005f-0d0329723946/result
Mon Sep 08 2025 07:26:55 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 07:26:55 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:26:55 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:26:55 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:26:55 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 07:26:55 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 07:26:55 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 07:26:55 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 07:26:55 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:26:55 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 07:26:55 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 07:26:55 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 07:26:56 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 07:26:56 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:26:56 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 07:26:56 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 07:26:56 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 07:26:56 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 07:26:56 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee89e-0909-6798-005f-0d03297242e6/status
Mon Sep 08 2025 07:26:56 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee89e-0909-6798-005f-0d03297242e6/status
Mon Sep 08 2025 07:27:27 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 07:27:27 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 07:27:27 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee89e-0909-6798-005f-0d03297242e6/result
Mon Sep 08 2025 07:27:29 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 07:27:29 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:27:29 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:27:29 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:27:29 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:27:29 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 07:27:29 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:27:29 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 07:27:29 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 07:27:30 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 07:27:30 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee89f-0909-6a94-005f-0d0329727242/status
Mon Sep 08 2025 07:27:30 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee89f-0909-6a94-005f-0d0329727242/status
Mon Sep 08 2025 07:28:00 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 07:28:00 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 07:28:00 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee89f-0909-6a94-005f-0d0329727242/result
Mon Sep 08 2025 07:28:01 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:28:01 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:28:01 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:28:01 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:28:01 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:28:01 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 07:28:01 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 07:28:01 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 07:28:01 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 07:28:01 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 07:28:01 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 07:28:01 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 07:28:14 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 07:28:14 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:28:14 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:28:14 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:28:14 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:28:14 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:28:14 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:28:14 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 07:28:14 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 07:28:14 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 07:28:16 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:28:16 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:28:16 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 07:28:16 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 07:28:16 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 07:28:16 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908072816226119","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 07:28:16 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 07:28:16 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 07:28:16 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908072816226119"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 07:28:16 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 07:28:16 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 07:28:16 GMT+0000 : Inside send mail method
Mon Sep 08 2025 07:28:16 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 07:28:17 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 07:30:44 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T07:25:38.011Z\",\"uniqueId\":\"fo20250908072816226119\",\"endTime\":\"2025-09-08T07:28:16.663Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 07:30:44 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 07:30:44 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 07:30:46 GMT+0000 : Inside send mail method
Mon Sep 08 2025 07:30:46 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 07:36:49 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T07:25:38.011Z\",\"uniqueId\":\"fo20250908072816226119\",\"endTime\":\"2025-09-08T07:28:16.663Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 07:36:49 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 07:36:49 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 07:36:51 GMT+0000 : Inside send mail method
Mon Sep 08 2025 07:36:51 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 07:43:59 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":false,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T07:25:38.011Z\",\"uniqueId\":\"fo20250908072816226119\",\"endTime\":\"2025-09-08T07:28:16.663Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 07:43:59 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 07:43:59 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 07:44:01 GMT+0000 : Inside send mail method
Mon Sep 08 2025 07:44:01 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 08:36:11 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 08:36:11 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 08:36:11 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 08:36:13 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 08:36:13 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 08:36:13 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 08:36:13 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:36:13 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 08:36:13 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 08:36:13 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 08:36:13 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 08:36:13 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 08:36:13 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 08:36:13 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 08:36:54 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 08:36:54 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee8e4-0909-6b20-005f-0d03297331b6/status
Mon Sep 08 2025 08:36:54 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee8e4-0909-6b20-005f-0d03297331b6/status
Mon Sep 08 2025 08:37:25 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 08:37:25 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee8e4-0909-6b20-005f-0d03297331b6/result
Mon Sep 08 2025 08:37:29 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 08:37:29 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:37:29 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:37:29 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:37:29 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 08:37:29 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 08:37:29 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 08:37:29 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 08:37:29 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:37:29 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 08:37:29 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 08:37:29 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 08:37:30 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 08:37:30 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:37:30 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 08:37:30 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 08:37:30 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 08:37:31 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 08:37:31 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee8e5-0909-6b22-005f-0d032973420e/status
Mon Sep 08 2025 08:37:31 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee8e5-0909-6b22-005f-0d032973420e/status
Mon Sep 08 2025 08:38:02 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 08:38:02 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 08:38:02 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee8e5-0909-6b22-005f-0d032973420e/result
Mon Sep 08 2025 08:38:03 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 08:38:03 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:38:03 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:38:03 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:38:03 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:38:03 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:38:03 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 08:38:03 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 08:38:03 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 08:38:03 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 08:38:03 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee8e6-0909-644b-005f-0d032972bd22/status
Mon Sep 08 2025 08:38:03 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee8e6-0909-644b-005f-0d032972bd22/status
Mon Sep 08 2025 08:38:34 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 08:38:34 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 08:38:34 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee8e6-0909-644b-005f-0d032972bd22/result
Mon Sep 08 2025 08:38:35 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:38:35 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:38:35 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:38:35 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:38:35 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:38:35 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 08:38:35 GMT+0000 : {}:Date batch array of closed RO pull is [["03/01/2025","03/08/2025"]] 
Mon Sep 08 2025 08:38:35 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 08:38:35 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/01/2025,03/08/2025
Mon Sep 08 2025 08:38:35 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 08:38:35 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 08:38:35 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 08:38:49 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-01T00:00:00.000Z  End date:2025-03-08T00:00:00.000Z
Mon Sep 08 2025 08:38:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:38:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:38:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:38:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:38:49 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:38:49 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:38:49 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 08:38:49 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:38:49 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 08:38:55 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:38:55 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:38:55 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:38:55 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:38:55 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 08:38:55 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908083855427594","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 08:38:55 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 08:38:55 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 08:38:55 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908083855427594"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 08:38:55 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 08:38:55 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 08:38:55 GMT+0000 : Inside send mail method
Mon Sep 08 2025 08:38:55 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 08:38:56 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 08:41:00 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/01/2025\",\"endDate\":\"03/08/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\",\"startTime\":\"2025-09-08T08:36:13.013Z\",\"uniqueId\":\"fo20250908083855427594\",\"endTime\":\"2025-09-08T08:38:55.917Z\",\"status\":true,\"message\":\"Success\",\"processFileName\":\"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 08:41:00 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 08:41:00 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 08:41:02 GMT+0000 : Inside send mail method
Mon Sep 08 2025 08:41:02 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 08:45:43 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-09-08","inPerformedBy":"<EMAIL>","inData":"{\"dealerId\":\"D100047425\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"glAccountCompanyID\":\"1\",\"mageManufacturer\":\"FORD\",\"solve360Update\":true,\"buildProxies\":true,\"includeMetaData\":false,\"extractAccountingData\":true,\"dualProxy\":false,\"userName\":\"<EMAIL>\",\"startDate\":\"03/19/2025\",\"endDate\":\"03/20/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAAMF01\",\"mageStoreCode\":\"QAAMF16\",\"stateCode\":\"LA\",\"metaData\":\"{\\\"parent_id\\\":\\\"*********\\\",\\\"parent_name\\\":\\\"QA AHM Store 03 [D100047425]\\\",\\\"store_id\\\":\\\"QAAMF16\\\",\\\"store_name\\\":\\\"QAAMF16\\\",\\\"mageStoreName\\\":\\\"QAAMF15\\\",\\\"groupCode\\\":\\\"QAAMF02\\\",\\\"errors\\\":\\\"\\\",\\\"assignedtoCn\\\":\\\"Netspective Sales Team\\\",\\\"thirdPartyUsername\\\":\\\"1619859e-1e12-4113-bfe4-11acc509f3d5\\\"}\",\"serverName\":\"D100047425\",\"departmentId\":\"D100047424\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"*\",\"testData\":false,\"companyIds\":\"**********\",\"parentName\":\"QA AHM Store 03 [D100047425]\",\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QA AHM Store 03\\\",\\\"projectId\\\":\\\"*********\\\",\\\"secondProjectId\\\":\\\"\\\",\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":\\\"Labor UL\\\",\\\"projectName\\\":\\\"QA AHM-FORT Bundle: Parts R4\\\",\\\"secondaryProjectName\\\":\\\"QA AHM-FORT Bundle: Labor R4\\\"}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"errors\":\"\",\"thirdPartyUsername\":\"1619859e-1e12-4113-bfe4-11acc509f3d5\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"FORD*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":true,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-09-08\"}","inCreatedBy":"<EMAIL>"}
Mon Sep 08 2025 08:45:43 GMT+0000 : doPayloadAction - Status: 200
Mon Sep 08 2025 08:45:43 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Mon Sep 08 2025 08:45:45 GMT+0000 : {"Dealer ID":"D100047425"}:RO Pull started
Mon Sep 08 2025 08:45:45 GMT+0000 : {"Dealer ID":"D100047425"}:perform extractBundle
Mon Sep 08 2025 08:45:45 GMT+0000 : {"Dealer ID":"D100047425"}:bundle type is initial
Mon Sep 08 2025 08:45:45 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:45:45 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Closed RO Pull
Mon Sep 08 2025 08:45:45 GMT+0000 : {}:Date batch array of closed RO pull is [["03/19/2025","03/20/2025"]] 
Mon Sep 08 2025 08:45:45 GMT+0000 : {}:Ready for performROPullWithDateRangeBatch 
Mon Sep 08 2025 08:45:45 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/19/2025,03/20/2025
Mon Sep 08 2025 08:45:45 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-19T00:00:00.000Z  End date:2025-03-20T00:00:00.000Z
Mon Sep 08 2025 08:45:45 GMT+0000 : {}:executing performROPullWithDateRange
Mon Sep 08 2025 08:45:45 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 08:46:26 GMT+0000 : undefined:Fetch operation  id statusreceived
Mon Sep 08 2025 08:46:26 GMT+0000 : undefined:Fetched status url https://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee8ed-0909-644b-005f-0d032972bea2/status
Mon Sep 08 2025 08:46:26 GMT+0000 : undefined:Waiting 30 seconds before the 1 attempt to check the API status of the requesthttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee8ed-0909-644b-005f-0d032972bea2/status
Mon Sep 08 2025 08:46:57 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 08:46:57 GMT+0000 : undefined:fetched result urlhttps://api.fortellis.io/cdk-test/drive/servicerepairordersetup/v2/long-operations/01bee8ed-0909-644b-005f-0d032972bea2/result
Mon Sep 08 2025 08:46:59 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-19T00:00:00.000Z  End date:2025-03-20T00:00:00.000Z
Mon Sep 08 2025 08:46:59 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:46:59 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:46:59 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:46:59 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Customer Pull
Mon Sep 08 2025 08:46:59 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 08:46:59 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 08:46:59 GMT+0000 : undefined:Fetch operation id status: undefined
Mon Sep 08 2025 08:46:59 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:46:59 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 08:46:59 GMT+0000 : {}:executing EMPLOYEE pull
Mon Sep 08 2025 08:46:59 GMT+0000 : {}:Executing employee pull
Mon Sep 08 2025 08:46:59 GMT+0000 : undefined:Fetch operation  id statusundefined
Mon Sep 08 2025 08:46:59 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:46:59 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  labor data Pull
Mon Sep 08 2025 08:46:59 GMT+0000 : {}:executing labor pull
Mon Sep 08 2025 08:46:59 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 08:47:00 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 08:47:00 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee8ef-0909-6b20-005f-0d03297335b6/status
Mon Sep 08 2025 08:47:00 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee8ef-0909-6b20-005f-0d03297335b6/status
Mon Sep 08 2025 08:47:31 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 08:47:31 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 08:47:31 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/service-labor-type/v2/long-operations/01bee8ef-0909-6b20-005f-0d03297335b6/result
Mon Sep 08 2025 08:47:31 GMT+0000 : [object Object]:Labor response:
Mon Sep 08 2025 08:47:31 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:47:31 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:47:31 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:47:31 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:47:31 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:47:31 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  Ro bulk data Pull
Mon Sep 08 2025 08:47:31 GMT+0000 : undefined:executing open Ro pull
Mon Sep 08 2025 08:47:31 GMT+0000 : {}:executing pullOpenRO
Mon Sep 08 2025 08:47:32 GMT+0000 : undefined:Fetch operation id status: received
Mon Sep 08 2025 08:47:32 GMT+0000 : undefined:Fetched status URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee8ef-0909-5f75-005f-0d032972cc3e/status
Mon Sep 08 2025 08:47:32 GMT+0000 : undefined:Waiting 30 seconds before attempt 1 to check API status: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee8ef-0909-5f75-005f-0d032972cc3e/status
Mon Sep 08 2025 08:48:03 GMT+0000 : undefined:1 response: complete
Mon Sep 08 2025 08:48:03 GMT+0000 : undefined:Status is complete, stopping further attempts.
Mon Sep 08 2025 08:48:03 GMT+0000 : undefined:Fetched result URL: https://api.fortellis.io/cdk-test/drive/servicerepairorder/v1/long-operations/01bee8ef-0909-5f75-005f-0d032972cc3e/result
Mon Sep 08 2025 08:48:03 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:48:03 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:48:03 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:48:03 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:48:03 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:48:03 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  GL Pull
Mon Sep 08 2025 08:48:03 GMT+0000 : {}:Date batch array of closed RO pull is [["03/19/2025","03/20/2025"]] 
Mon Sep 08 2025 08:48:03 GMT+0000 : {}:Ready for performGlPullWithDateRangeBatch 
Mon Sep 08 2025 08:48:03 GMT+0000 : {"DealerID":"D100047425"}:Date batch array is:03/19/2025,03/20/2025
Mon Sep 08 2025 08:48:03 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull Starting start date:2025-03-19T00:00:00.000Z  End date:2025-03-20T00:00:00.000Z
Mon Sep 08 2025 08:48:03 GMT+0000 : {}:executing performGlPullWithDateRange
Mon Sep 08 2025 08:48:03 GMT+0000 : {}:executing pullCloseROdata
Mon Sep 08 2025 08:48:15 GMT+0000 : {"DealerID":"D100047425"}:Closed RO pull finished start date:2025-03-19T00:00:00.000Z  End date:2025-03-20T00:00:00.000Z
Mon Sep 08 2025 08:48:15 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:48:15 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:48:15 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:48:15 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:48:15 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:48:15 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:48:15 GMT+0000 : {"Dealer ID":"D100047425"}:Perform performAPICall
Mon Sep 08 2025 08:48:15 GMT+0000 : {"Dealer ID":"D100047425"}:Ready for  coa data Pull
Mon Sep 08 2025 08:48:15 GMT+0000 : {}:executing COA pull
Mon Sep 08 2025 08:48:17 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:48:17 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:48:17 GMT+0000 : undefined:Error in JSON parse: 
Mon Sep 08 2025 08:48:17 GMT+0000 : undefined:Invalid data type: 
Mon Sep 08 2025 08:48:17 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipfilePath:/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipJOB_TYPE:Fortellis
Mon Sep 08 2025 08:48:17 GMT+0000 : createConfigFile inputData: {"dms":"Fortellis","mageGrpData":{"state":"LA","mageGroupCode":"QAAMF02","mageGroupName":"QAAMF01","mageStoreName":"QAAMF15","mageStoreCode":"QAAMF16","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"FORD","mageProjectName":"QA AHM-FORT Bundle: Parts R4","mageProjectType":"Parts UL","secondaryProjectType":"Labor UL","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4","companyId":"*********","userName":"<EMAIL>","schedulerId":"fo20250908084817872945","sourceFile":"QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"}}
Mon Sep 08 2025 08:48:17 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 08:48:17 GMT+0000 : createConfigFile: inputData - [object Object]
Mon Sep 08 2025 08:48:17 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202509'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="FORD"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250908084817872945"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip"
Mon Sep 08 2025 08:48:18 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 08:48:18 GMT+0000 : Generating job.txt file
Mon Sep 08 2025 08:48:18 GMT+0000 : Inside send mail method
Mon Sep 08 2025 08:48:18 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 08:48:18 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/scheduler-temp/fortellis-zip-eti/QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 08:52:37 GMT+0000 : Inside send mail method
Mon Sep 08 2025 08:52:37 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 08:52:51 GMT+0000 : updateSolve360Data&&&&&&&&&&&&&&&&& {"projectId":"*********","secondProjectId":"","userName":"<EMAIL>","solve360Update":true,"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","dmsType":"FORTELLIS","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"secondProjectIdList":["",""],"testData":false,"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","totalRoCount":"   489\n","exceptionTypeCounts":{}}
Mon Sep 08 2025 08:52:51 GMT+0000 : Started SharePoint file upload functionality 
Mon Sep 08 2025 08:52:51 GMT+0000 : Started SharePoint file upload functionality /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip.FORTELLIS
Mon Sep 08 2025 08:52:51 GMT+0000 : File renamed successfully ',  /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zipto/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip
Mon Sep 08 2025 08:52:51 GMT+0000 : Finish file renaming&&&&&&&&&&&&&&&&&&&
Mon Sep 08 2025 08:52:51 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>{"fileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","filePath":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip","secretKey":"7e21b7ede6ec9b7edc53b74999e782ed7cfcf8dbf0147a06c9a6c895595b4b4717881c3a1b45","dmsType":"FORTELLIS","projectId":"*********","secondProjectId":"","solve360Update":true,"userName":"<EMAIL>","warningObj":{"scheduled_by":"<EMAIL>","partDetailsNullExceptionCount":0,"coreReturnExceptionCount":0,"coreChargeExceptionCount":0,"coreReturnNotEqualCoreChargeExceptionCount":0,"coreChargeWithNoSaleCount":0,"invalidCoreCostSaleMismatchCount":0,"invalidCoreAmountMismatchCount":0,"gl_missing_ro_count":0,"miscExceptionCount":0},"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","secondProjectIdList":["",""],"totalRoCount":"   489\n","exceptionTypeCounts":{},"exceptions":"","inSchedulerId":"","testData":""}
Mon Sep 08 2025 08:52:51 GMT+0000 : req.body.processFileName>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>PROC-QAAHMStore03-LA-D100047425-2509080846.zip
Mon Sep 08 2025 08:52:51 GMT+0000 : Dist file exist for sharepoint upload
Mon Sep 08 2025 08:52:51 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Mon Sep 08 2025 08:52:51 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Mon Sep 08 2025 08:52:51 GMT+0000 : sharedFolderPath
Mon Sep 08 2025 08:52:51 GMT+0000 : filePath
Mon Sep 08 2025 08:52:53 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:52:53 GMT+0000 : Share point file upload retry attempt : 0, DMS : FORTELLIS, file name:PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip ,error:{"name":"StatusCodeError","statusCode":400,"message":"400 - \"{\\\"errors\\\":[{\\\"status\\\":\\\"Failure\\\",\\\"message\\\":\\\"Token Mismatch Error\\\"}]}\"","error":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"secretKey\":\"7e21b7ede6ec9b7edc53b74999e782ed7cfcf8dbf0147a06c9a6c895595b4b4717881c3a1b45\",\"dmsType\":\"FORTELLIS\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":true,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"partDetailsNullExceptionCount\":0,\"coreReturnExceptionCount\":0,\"coreChargeExceptionCount\":0,\"coreReturnNotEqualCoreChargeExceptionCount\":0,\"coreChargeWithNoSaleCount\":0,\"invalidCoreCostSaleMismatchCount\":0,\"invalidCoreAmountMismatchCount\":0,\"gl_missing_ro_count\":0,\"miscExceptionCount\":0},\"thirdPartyUsername\":\"D100047425\",\"storeCode\":\"QAAMF16\",\"groupCode\":\"QAAMF01\",\"resumeUser\":\"\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"companyName\":\"QA AHM Store 03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"projectName\":\"QA AHM-FORT Bundle: Parts R4\",\"secondaryProjectName\":\"QA AHM-FORT Bundle: Labor R4\"}],\"processFileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"secondProjectIdList\":[\"\",\"\"],\"totalRoCount\":\"   489\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":400,"body":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","headers":{"date":"Mon, 08 Sep 2025 08:52:53 GMT","content-type":"application/json; charset=utf-8","content-length":"66","connection":"keep-alive","server":"nginx","x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","access-control-allow-origin":"*","access-control-allow-methods":"GET,POST,OPTIONS,PUT","access-control-allow-headers":"Origin, X-Requested-With, Content-Type, Accept, Authorization","etag":"W/\"42-0UYgAptlzmYB4V905v1gPnhwucA\"","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1349}}}}
Mon Sep 08 2025 08:52:53 GMT+0000 : updateSolve360Data&&&&&&&&&&&&&&&&& {"projectId":"*********","secondProjectId":"","userName":"<EMAIL>","solve360Update":true,"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","dmsType":"FORTELLIS","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"secondProjectIdList":["",""],"testData":false,"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","totalRoCount":"   489\n","exceptionTypeCounts":{}}
Mon Sep 08 2025 08:52:53 GMT+0000 : Started SharePoint file upload functionality 
Mon Sep 08 2025 08:52:53 GMT+0000 : Started SharePoint file upload functionality /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip.FORTELLIS
Mon Sep 08 2025 08:52:53 GMT+0000 : Error renaming file ',  {"errno":-2,"code":"ENOENT","syscall":"rename","path":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip","dest":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip"}
Mon Sep 08 2025 08:52:53 GMT+0000 : Finish file renaming&&&&&&&&&&&&&&&&&&&
Mon Sep 08 2025 08:52:53 GMT+0000 : Failed to upload file to SharePoint {PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip} StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:52:53 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>{"fileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","filePath":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip","secretKey":"06375cce525ac9f7fc30e802736b450e1a730b5f65a786bb2b21646bb4ca27b2da4fd7af9ecd","dmsType":"FORTELLIS","projectId":"*********","secondProjectId":"","solve360Update":true,"userName":"<EMAIL>","warningObj":{"scheduled_by":"<EMAIL>","partDetailsNullExceptionCount":0,"coreReturnExceptionCount":0,"coreChargeExceptionCount":0,"coreReturnNotEqualCoreChargeExceptionCount":0,"coreChargeWithNoSaleCount":0,"invalidCoreCostSaleMismatchCount":0,"invalidCoreAmountMismatchCount":0,"gl_missing_ro_count":0,"miscExceptionCount":0},"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","secondProjectIdList":["",""],"totalRoCount":"   489\n","exceptionTypeCounts":{},"exceptions":"","inSchedulerId":"","testData":""}
Mon Sep 08 2025 08:52:53 GMT+0000 : req.body.processFileName>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>PROC-QAAHMStore03-LA-D100047425-2509080846.zip
Mon Sep 08 2025 08:52:53 GMT+0000 : Dist file exist for sharepoint upload
Mon Sep 08 2025 08:52:53 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Mon Sep 08 2025 08:52:53 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Mon Sep 08 2025 08:52:53 GMT+0000 : sharedFolderPath
Mon Sep 08 2025 08:52:53 GMT+0000 : filePath
Mon Sep 08 2025 08:52:55 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:52:55 GMT+0000 : Share point file upload retry attempt : 1, DMS : FORTELLIS, file name:PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip ,error:{"name":"StatusCodeError","statusCode":400,"message":"400 - \"{\\\"errors\\\":[{\\\"status\\\":\\\"Failure\\\",\\\"message\\\":\\\"Token Mismatch Error\\\"}]}\"","error":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"secretKey\":\"06375cce525ac9f7fc30e802736b450e1a730b5f65a786bb2b21646bb4ca27b2da4fd7af9ecd\",\"dmsType\":\"FORTELLIS\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":true,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"partDetailsNullExceptionCount\":0,\"coreReturnExceptionCount\":0,\"coreChargeExceptionCount\":0,\"coreReturnNotEqualCoreChargeExceptionCount\":0,\"coreChargeWithNoSaleCount\":0,\"invalidCoreCostSaleMismatchCount\":0,\"invalidCoreAmountMismatchCount\":0,\"gl_missing_ro_count\":0,\"miscExceptionCount\":0},\"thirdPartyUsername\":\"D100047425\",\"storeCode\":\"QAAMF16\",\"groupCode\":\"QAAMF01\",\"resumeUser\":\"\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"companyName\":\"QA AHM Store 03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"projectName\":\"QA AHM-FORT Bundle: Parts R4\",\"secondaryProjectName\":\"QA AHM-FORT Bundle: Labor R4\"}],\"processFileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"secondProjectIdList\":[\"\",\"\"],\"totalRoCount\":\"   489\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":400,"body":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","headers":{"date":"Mon, 08 Sep 2025 08:52:55 GMT","content-type":"application/json; charset=utf-8","content-length":"66","connection":"keep-alive","server":"nginx","x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","access-control-allow-origin":"*","access-control-allow-methods":"GET,POST,OPTIONS,PUT","access-control-allow-headers":"Origin, X-Requested-With, Content-Type, Accept, Authorization","etag":"W/\"42-0UYgAptlzmYB4V905v1gPnhwucA\"","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1349}}}}
Mon Sep 08 2025 08:52:55 GMT+0000 : updateSolve360Data&&&&&&&&&&&&&&&&& {"projectId":"*********","secondProjectId":"","userName":"<EMAIL>","solve360Update":true,"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","dmsType":"FORTELLIS","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"secondProjectIdList":["",""],"testData":false,"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","totalRoCount":"   489\n","exceptionTypeCounts":{}}
Mon Sep 08 2025 08:52:55 GMT+0000 : Started SharePoint file upload functionality 
Mon Sep 08 2025 08:52:55 GMT+0000 : Started SharePoint file upload functionality /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip.FORTELLIS
Mon Sep 08 2025 08:52:55 GMT+0000 : Error renaming file ',  {"errno":-2,"code":"ENOENT","syscall":"rename","path":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip","dest":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip"}
Mon Sep 08 2025 08:52:55 GMT+0000 : Finish file renaming&&&&&&&&&&&&&&&&&&&
Mon Sep 08 2025 08:52:55 GMT+0000 : Failed to upload file to SharePoint {PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip} StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:52:55 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>{"fileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","filePath":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip","secretKey":"7ae89e63e91e0caadf9b93b8facae70524a3e4c830c49d1e6729ab0182e463f5d13729f43625","dmsType":"FORTELLIS","projectId":"*********","secondProjectId":"","solve360Update":true,"userName":"<EMAIL>","warningObj":{"scheduled_by":"<EMAIL>","partDetailsNullExceptionCount":0,"coreReturnExceptionCount":0,"coreChargeExceptionCount":0,"coreReturnNotEqualCoreChargeExceptionCount":0,"coreChargeWithNoSaleCount":0,"invalidCoreCostSaleMismatchCount":0,"invalidCoreAmountMismatchCount":0,"gl_missing_ro_count":0,"miscExceptionCount":0},"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","secondProjectIdList":["",""],"totalRoCount":"   489\n","exceptionTypeCounts":{},"exceptions":"","inSchedulerId":"","testData":""}
Mon Sep 08 2025 08:52:55 GMT+0000 : req.body.processFileName>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>PROC-QAAHMStore03-LA-D100047425-2509080846.zip
Mon Sep 08 2025 08:52:55 GMT+0000 : Dist file exist for sharepoint upload
Mon Sep 08 2025 08:52:55 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Mon Sep 08 2025 08:52:55 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Mon Sep 08 2025 08:52:55 GMT+0000 : sharedFolderPath
Mon Sep 08 2025 08:52:55 GMT+0000 : filePath
Mon Sep 08 2025 08:52:56 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:52:56 GMT+0000 : Share point file upload retry attempt : 2, DMS : FORTELLIS, file name:PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip ,error:{"name":"StatusCodeError","statusCode":400,"message":"400 - \"{\\\"errors\\\":[{\\\"status\\\":\\\"Failure\\\",\\\"message\\\":\\\"Token Mismatch Error\\\"}]}\"","error":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"secretKey\":\"7ae89e63e91e0caadf9b93b8facae70524a3e4c830c49d1e6729ab0182e463f5d13729f43625\",\"dmsType\":\"FORTELLIS\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":true,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"partDetailsNullExceptionCount\":0,\"coreReturnExceptionCount\":0,\"coreChargeExceptionCount\":0,\"coreReturnNotEqualCoreChargeExceptionCount\":0,\"coreChargeWithNoSaleCount\":0,\"invalidCoreCostSaleMismatchCount\":0,\"invalidCoreAmountMismatchCount\":0,\"gl_missing_ro_count\":0,\"miscExceptionCount\":0},\"thirdPartyUsername\":\"D100047425\",\"storeCode\":\"QAAMF16\",\"groupCode\":\"QAAMF01\",\"resumeUser\":\"\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"companyName\":\"QA AHM Store 03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"projectName\":\"QA AHM-FORT Bundle: Parts R4\",\"secondaryProjectName\":\"QA AHM-FORT Bundle: Labor R4\"}],\"processFileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"secondProjectIdList\":[\"\",\"\"],\"totalRoCount\":\"   489\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":400,"body":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","headers":{"date":"Mon, 08 Sep 2025 08:52:56 GMT","content-type":"application/json; charset=utf-8","content-length":"66","connection":"keep-alive","server":"nginx","x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","access-control-allow-origin":"*","access-control-allow-methods":"GET,POST,OPTIONS,PUT","access-control-allow-headers":"Origin, X-Requested-With, Content-Type, Accept, Authorization","etag":"W/\"42-0UYgAptlzmYB4V905v1gPnhwucA\"","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1349}}}}
Mon Sep 08 2025 08:52:56 GMT+0000 : updateSolve360Data&&&&&&&&&&&&&&&&& {"projectId":"*********","secondProjectId":"","userName":"<EMAIL>","solve360Update":true,"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","dmsType":"FORTELLIS","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"secondProjectIdList":["",""],"testData":false,"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","totalRoCount":"   489\n","exceptionTypeCounts":{}}
Mon Sep 08 2025 08:52:56 GMT+0000 : Started SharePoint file upload functionality 
Mon Sep 08 2025 08:52:56 GMT+0000 : Started SharePoint file upload functionality /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip.FORTELLIS
Mon Sep 08 2025 08:52:56 GMT+0000 : Error renaming file ',  {"errno":-2,"code":"ENOENT","syscall":"rename","path":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip","dest":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip"}
Mon Sep 08 2025 08:52:56 GMT+0000 : Finish file renaming&&&&&&&&&&&&&&&&&&&
Mon Sep 08 2025 08:52:56 GMT+0000 : Failed to upload file to SharePoint {PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip} StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:52:56 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>{"fileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","filePath":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip","secretKey":"5d995fa2d4f3c26f8622b6af7fbf92ffcafd08ad3f95001d231ab2721908400a7b788b1884dd","dmsType":"FORTELLIS","projectId":"*********","secondProjectId":"","solve360Update":true,"userName":"<EMAIL>","warningObj":{"scheduled_by":"<EMAIL>","partDetailsNullExceptionCount":0,"coreReturnExceptionCount":0,"coreChargeExceptionCount":0,"coreReturnNotEqualCoreChargeExceptionCount":0,"coreChargeWithNoSaleCount":0,"invalidCoreCostSaleMismatchCount":0,"invalidCoreAmountMismatchCount":0,"gl_missing_ro_count":0,"miscExceptionCount":0},"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080846.zip","secondProjectIdList":["",""],"totalRoCount":"   489\n","exceptionTypeCounts":{},"exceptions":"","inSchedulerId":"","testData":""}
Mon Sep 08 2025 08:52:56 GMT+0000 : req.body.processFileName>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>PROC-QAAHMStore03-LA-D100047425-2509080846.zip
Mon Sep 08 2025 08:52:56 GMT+0000 : Dist file exist for sharepoint upload
Mon Sep 08 2025 08:52:56 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Mon Sep 08 2025 08:52:56 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Mon Sep 08 2025 08:52:56 GMT+0000 : sharedFolderPath
Mon Sep 08 2025 08:52:56 GMT+0000 : filePath
Mon Sep 08 2025 08:52:57 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:52:57 GMT+0000 : Share point file upload retry attempt : 3, DMS : FORTELLIS, file name:PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip ,error:{"name":"StatusCodeError","statusCode":400,"message":"400 - \"{\\\"errors\\\":[{\\\"status\\\":\\\"Failure\\\",\\\"message\\\":\\\"Token Mismatch Error\\\"}]}\"","error":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"secretKey\":\"5d995fa2d4f3c26f8622b6af7fbf92ffcafd08ad3f95001d231ab2721908400a7b788b1884dd\",\"dmsType\":\"FORTELLIS\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":true,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"partDetailsNullExceptionCount\":0,\"coreReturnExceptionCount\":0,\"coreChargeExceptionCount\":0,\"coreReturnNotEqualCoreChargeExceptionCount\":0,\"coreChargeWithNoSaleCount\":0,\"invalidCoreCostSaleMismatchCount\":0,\"invalidCoreAmountMismatchCount\":0,\"gl_missing_ro_count\":0,\"miscExceptionCount\":0},\"thirdPartyUsername\":\"D100047425\",\"storeCode\":\"QAAMF16\",\"groupCode\":\"QAAMF01\",\"resumeUser\":\"\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"companyName\":\"QA AHM Store 03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"projectName\":\"QA AHM-FORT Bundle: Parts R4\",\"secondaryProjectName\":\"QA AHM-FORT Bundle: Labor R4\"}],\"processFileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080846.zip\",\"secondProjectIdList\":[\"\",\"\"],\"totalRoCount\":\"   489\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":400,"body":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","headers":{"date":"Mon, 08 Sep 2025 08:52:57 GMT","content-type":"application/json; charset=utf-8","content-length":"66","connection":"keep-alive","server":"nginx","x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","access-control-allow-origin":"*","access-control-allow-methods":"GET,POST,OPTIONS,PUT","access-control-allow-headers":"Origin, X-Requested-With, Content-Type, Accept, Authorization","etag":"W/\"42-0UYgAptlzmYB4V905v1gPnhwucA\"","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1349}}}}
Mon Sep 08 2025 08:52:57 GMT+0000 : File uploaded failed to SharePoint {PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:52:57 GMT+0000 : Inside send mail method
Mon Sep 08 2025 08:52:57 GMT+0000 : paytypeHaltundefined invSequenceHaltundefined makeHaltundefined undefined 
Mon Sep 08 2025 08:52:57 GMT+0000 : isPreImportExist : false
Mon Sep 08 2025 08:52:57 GMT+0000 : Inside sharepoint file upload completion mail
Mon Sep 08 2025 08:52:57 GMT+0000 : Send Mail: Sharepoint: Filename : QAAMF16
Mon Sep 08 2025 08:52:57 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QAAMF16":{"PROXY":false}}
Mon Sep 08 2025 08:52:57 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Mon Sep 08 2025 08:52:57 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip
Mon Sep 08 2025 08:52:57 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip","proxyFilePath":"","proxyMailSubject":"Fortellis Proxy Zip Failed"}
Mon Sep 08 2025 08:52:57 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip","proxyFilePath":"","proxyMailSubject":"Fortellis Proxy Zip Failed"}
Mon Sep 08 2025 08:52:57 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Mon Sep 08 2025 08:52:57 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Failed","proxy_file_name":"PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623.zip","proxy_url":"","efs_path":"/etl/etl-vagrant/etl-FORTELLIS/FORTELLIS-zip/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************20250908084623-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":0,"core_charge_exception_count":0,"core_return_not_equal_core_charge_exception_count":0,"core_charge_with_no_sale_count":0,"resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":0,"invalid_misc_paytype_count":"","punch_time_missing_count":"","inventory_ro_count":"","invalid_core_amount_mismatch_count":0,"coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":0,"gl_ro_not_found_count":0,"invoice_missing_count":"","suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":0,"multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"pre_import_halt_exist":false,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"invoice_missing_exception_exist_flag":false,"invalid_misc_paytype_flag":false,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"part_details_null_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"punch_time_missing_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Mon Sep 08 2025 08:52:57 GMT+0000 : Send Mail: Sharepoint: notification status: true
Mon Sep 08 2025 08:52:57 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 08:55:48 GMT+0000 : Inside send mail method
Mon Sep 08 2025 08:55:48 GMT+0000 : Send Mail: notification status > true
Mon Sep 08 2025 08:56:02 GMT+0000 : updateSolve360Data&&&&&&&&&&&&&&&&& {"projectId":"*********","secondProjectId":"","userName":"<EMAIL>","solve360Update":true,"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","dmsType":"FORTELLIS","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"secondProjectIdList":["",""],"uniqueId":"fo20250908084817872945-1757321577037","testData":false,"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","totalRoCount":"   116\n","exceptionTypeCounts":{}}
Mon Sep 08 2025 08:56:02 GMT+0000 : Started SharePoint file upload functionality 
Mon Sep 08 2025 08:56:02 GMT+0000 : Started SharePoint file upload functionality /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip.FORTELLIS
Mon Sep 08 2025 08:56:02 GMT+0000 : File renamed successfully ',  /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zipto/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip
Mon Sep 08 2025 08:56:02 GMT+0000 : Finish file renaming&&&&&&&&&&&&&&&&&&&
Mon Sep 08 2025 08:56:02 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>{"fileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","filePath":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip","secretKey":"72f40893c3acec8bbc90dec92363de96242d0159caecb19ff3ed9677316a974ca2cc20744dfb","dmsType":"FORTELLIS","projectId":"*********","secondProjectId":"","solve360Update":true,"userName":"<EMAIL>","warningObj":{"scheduled_by":"<EMAIL>","partDetailsNullExceptionCount":0,"coreReturnExceptionCount":0,"coreChargeExceptionCount":0,"coreReturnNotEqualCoreChargeExceptionCount":0,"coreChargeWithNoSaleCount":0,"invalidCoreCostSaleMismatchCount":0,"invalidCoreAmountMismatchCount":0,"gl_missing_ro_count":0,"miscExceptionCount":0},"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","secondProjectIdList":["",""],"totalRoCount":"   116\n","exceptionTypeCounts":{},"exceptions":"","inSchedulerId":"fo20250908084817872945-1757321577037","testData":""}
Mon Sep 08 2025 08:56:02 GMT+0000 : req.body.processFileName>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>PROC-QAAHMStore03-LA-D100047425-2509080852.zip
Mon Sep 08 2025 08:56:02 GMT+0000 : Dist file exist for sharepoint upload
Mon Sep 08 2025 08:56:02 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Mon Sep 08 2025 08:56:02 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Mon Sep 08 2025 08:56:02 GMT+0000 : sharedFolderPath
Mon Sep 08 2025 08:56:02 GMT+0000 : filePath
Mon Sep 08 2025 08:56:04 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:56:04 GMT+0000 : Share point file upload retry attempt : 0, DMS : FORTELLIS, file name:PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip ,error:{"name":"StatusCodeError","statusCode":400,"message":"400 - \"{\\\"errors\\\":[{\\\"status\\\":\\\"Failure\\\",\\\"message\\\":\\\"Token Mismatch Error\\\"}]}\"","error":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"secretKey\":\"72f40893c3acec8bbc90dec92363de96242d0159caecb19ff3ed9677316a974ca2cc20744dfb\",\"dmsType\":\"FORTELLIS\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":true,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"partDetailsNullExceptionCount\":0,\"coreReturnExceptionCount\":0,\"coreChargeExceptionCount\":0,\"coreReturnNotEqualCoreChargeExceptionCount\":0,\"coreChargeWithNoSaleCount\":0,\"invalidCoreCostSaleMismatchCount\":0,\"invalidCoreAmountMismatchCount\":0,\"gl_missing_ro_count\":0,\"miscExceptionCount\":0},\"thirdPartyUsername\":\"D100047425\",\"storeCode\":\"QAAMF16\",\"groupCode\":\"QAAMF01\",\"resumeUser\":\"\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"companyName\":\"QA AHM Store 03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"projectName\":\"QA AHM-FORT Bundle: Parts R4\",\"secondaryProjectName\":\"QA AHM-FORT Bundle: Labor R4\"}],\"processFileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"secondProjectIdList\":[\"\",\"\"],\"totalRoCount\":\"   116\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"fo20250908084817872945-1757321577037\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":400,"body":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","headers":{"date":"Mon, 08 Sep 2025 08:56:04 GMT","content-type":"application/json; charset=utf-8","content-length":"66","connection":"keep-alive","server":"nginx","x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","access-control-allow-origin":"*","access-control-allow-methods":"GET,POST,OPTIONS,PUT","access-control-allow-headers":"Origin, X-Requested-With, Content-Type, Accept, Authorization","etag":"W/\"42-0UYgAptlzmYB4V905v1gPnhwucA\"","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1385}}}}
Mon Sep 08 2025 08:56:04 GMT+0000 : updateSolve360Data&&&&&&&&&&&&&&&&& {"projectId":"*********","secondProjectId":"","userName":"<EMAIL>","solve360Update":true,"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","dmsType":"FORTELLIS","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"secondProjectIdList":["",""],"uniqueId":"fo20250908084817872945-1757321577037","testData":false,"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","totalRoCount":"   116\n","exceptionTypeCounts":{}}
Mon Sep 08 2025 08:56:04 GMT+0000 : Started SharePoint file upload functionality 
Mon Sep 08 2025 08:56:04 GMT+0000 : Started SharePoint file upload functionality /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip.FORTELLIS
Mon Sep 08 2025 08:56:04 GMT+0000 : Error renaming file ',  {"errno":-2,"code":"ENOENT","syscall":"rename","path":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip","dest":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip"}
Mon Sep 08 2025 08:56:04 GMT+0000 : Finish file renaming&&&&&&&&&&&&&&&&&&&
Mon Sep 08 2025 08:56:04 GMT+0000 : Failed to upload file to SharePoint {PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip} StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:56:04 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>{"fileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","filePath":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip","secretKey":"c7bacf70f2bb041dc98f2d6d284816058083d51957ba4fd8286a8618602362c8ba927efa0b7d","dmsType":"FORTELLIS","projectId":"*********","secondProjectId":"","solve360Update":true,"userName":"<EMAIL>","warningObj":{"scheduled_by":"<EMAIL>","partDetailsNullExceptionCount":0,"coreReturnExceptionCount":0,"coreChargeExceptionCount":0,"coreReturnNotEqualCoreChargeExceptionCount":0,"coreChargeWithNoSaleCount":0,"invalidCoreCostSaleMismatchCount":0,"invalidCoreAmountMismatchCount":0,"gl_missing_ro_count":0,"miscExceptionCount":0},"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","secondProjectIdList":["",""],"totalRoCount":"   116\n","exceptionTypeCounts":{},"exceptions":"","inSchedulerId":"fo20250908084817872945-1757321577037","testData":""}
Mon Sep 08 2025 08:56:04 GMT+0000 : req.body.processFileName>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>PROC-QAAHMStore03-LA-D100047425-2509080852.zip
Mon Sep 08 2025 08:56:04 GMT+0000 : Dist file exist for sharepoint upload
Mon Sep 08 2025 08:56:04 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Mon Sep 08 2025 08:56:04 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Mon Sep 08 2025 08:56:04 GMT+0000 : sharedFolderPath
Mon Sep 08 2025 08:56:04 GMT+0000 : filePath
Mon Sep 08 2025 08:56:05 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:56:05 GMT+0000 : Share point file upload retry attempt : 1, DMS : FORTELLIS, file name:PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip ,error:{"name":"StatusCodeError","statusCode":400,"message":"400 - \"{\\\"errors\\\":[{\\\"status\\\":\\\"Failure\\\",\\\"message\\\":\\\"Token Mismatch Error\\\"}]}\"","error":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"secretKey\":\"c7bacf70f2bb041dc98f2d6d284816058083d51957ba4fd8286a8618602362c8ba927efa0b7d\",\"dmsType\":\"FORTELLIS\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":true,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"partDetailsNullExceptionCount\":0,\"coreReturnExceptionCount\":0,\"coreChargeExceptionCount\":0,\"coreReturnNotEqualCoreChargeExceptionCount\":0,\"coreChargeWithNoSaleCount\":0,\"invalidCoreCostSaleMismatchCount\":0,\"invalidCoreAmountMismatchCount\":0,\"gl_missing_ro_count\":0,\"miscExceptionCount\":0},\"thirdPartyUsername\":\"D100047425\",\"storeCode\":\"QAAMF16\",\"groupCode\":\"QAAMF01\",\"resumeUser\":\"\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"companyName\":\"QA AHM Store 03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"projectName\":\"QA AHM-FORT Bundle: Parts R4\",\"secondaryProjectName\":\"QA AHM-FORT Bundle: Labor R4\"}],\"processFileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"secondProjectIdList\":[\"\",\"\"],\"totalRoCount\":\"   116\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"fo20250908084817872945-1757321577037\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":400,"body":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","headers":{"date":"Mon, 08 Sep 2025 08:56:05 GMT","content-type":"application/json; charset=utf-8","content-length":"66","connection":"keep-alive","server":"nginx","x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","access-control-allow-origin":"*","access-control-allow-methods":"GET,POST,OPTIONS,PUT","access-control-allow-headers":"Origin, X-Requested-With, Content-Type, Accept, Authorization","etag":"W/\"42-0UYgAptlzmYB4V905v1gPnhwucA\"","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1385}}}}
Mon Sep 08 2025 08:56:05 GMT+0000 : updateSolve360Data&&&&&&&&&&&&&&&&& {"projectId":"*********","secondProjectId":"","userName":"<EMAIL>","solve360Update":true,"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","dmsType":"FORTELLIS","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"secondProjectIdList":["",""],"uniqueId":"fo20250908084817872945-1757321577037","testData":false,"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","totalRoCount":"   116\n","exceptionTypeCounts":{}}
Mon Sep 08 2025 08:56:05 GMT+0000 : Started SharePoint file upload functionality 
Mon Sep 08 2025 08:56:05 GMT+0000 : Started SharePoint file upload functionality /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip.FORTELLIS
Mon Sep 08 2025 08:56:05 GMT+0000 : Error renaming file ',  {"errno":-2,"code":"ENOENT","syscall":"rename","path":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip","dest":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip"}
Mon Sep 08 2025 08:56:05 GMT+0000 : Finish file renaming&&&&&&&&&&&&&&&&&&&
Mon Sep 08 2025 08:56:05 GMT+0000 : Failed to upload file to SharePoint {PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip} StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:56:05 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>{"fileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","filePath":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip","secretKey":"9850300f6f6ea9e7f3fc42a0f18eef25fc683562ef8cc6bbdbf278f4d95548e001485fd3c2e9","dmsType":"FORTELLIS","projectId":"*********","secondProjectId":"","solve360Update":true,"userName":"<EMAIL>","warningObj":{"scheduled_by":"<EMAIL>","partDetailsNullExceptionCount":0,"coreReturnExceptionCount":0,"coreChargeExceptionCount":0,"coreReturnNotEqualCoreChargeExceptionCount":0,"coreChargeWithNoSaleCount":0,"invalidCoreCostSaleMismatchCount":0,"invalidCoreAmountMismatchCount":0,"gl_missing_ro_count":0,"miscExceptionCount":0},"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","secondProjectIdList":["",""],"totalRoCount":"   116\n","exceptionTypeCounts":{},"exceptions":"","inSchedulerId":"fo20250908084817872945-1757321577037","testData":""}
Mon Sep 08 2025 08:56:05 GMT+0000 : req.body.processFileName>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>PROC-QAAHMStore03-LA-D100047425-2509080852.zip
Mon Sep 08 2025 08:56:05 GMT+0000 : Dist file exist for sharepoint upload
Mon Sep 08 2025 08:56:05 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Mon Sep 08 2025 08:56:05 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Mon Sep 08 2025 08:56:05 GMT+0000 : sharedFolderPath
Mon Sep 08 2025 08:56:05 GMT+0000 : filePath
Mon Sep 08 2025 08:56:07 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:56:07 GMT+0000 : Share point file upload retry attempt : 2, DMS : FORTELLIS, file name:PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip ,error:{"name":"StatusCodeError","statusCode":400,"message":"400 - \"{\\\"errors\\\":[{\\\"status\\\":\\\"Failure\\\",\\\"message\\\":\\\"Token Mismatch Error\\\"}]}\"","error":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"secretKey\":\"9850300f6f6ea9e7f3fc42a0f18eef25fc683562ef8cc6bbdbf278f4d95548e001485fd3c2e9\",\"dmsType\":\"FORTELLIS\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":true,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"partDetailsNullExceptionCount\":0,\"coreReturnExceptionCount\":0,\"coreChargeExceptionCount\":0,\"coreReturnNotEqualCoreChargeExceptionCount\":0,\"coreChargeWithNoSaleCount\":0,\"invalidCoreCostSaleMismatchCount\":0,\"invalidCoreAmountMismatchCount\":0,\"gl_missing_ro_count\":0,\"miscExceptionCount\":0},\"thirdPartyUsername\":\"D100047425\",\"storeCode\":\"QAAMF16\",\"groupCode\":\"QAAMF01\",\"resumeUser\":\"\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"companyName\":\"QA AHM Store 03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"projectName\":\"QA AHM-FORT Bundle: Parts R4\",\"secondaryProjectName\":\"QA AHM-FORT Bundle: Labor R4\"}],\"processFileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"secondProjectIdList\":[\"\",\"\"],\"totalRoCount\":\"   116\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"fo20250908084817872945-1757321577037\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":400,"body":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","headers":{"date":"Mon, 08 Sep 2025 08:56:07 GMT","content-type":"application/json; charset=utf-8","content-length":"66","connection":"keep-alive","server":"nginx","x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","access-control-allow-origin":"*","access-control-allow-methods":"GET,POST,OPTIONS,PUT","access-control-allow-headers":"Origin, X-Requested-With, Content-Type, Accept, Authorization","etag":"W/\"42-0UYgAptlzmYB4V905v1gPnhwucA\"","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1385}}}}
Mon Sep 08 2025 08:56:07 GMT+0000 : updateSolve360Data&&&&&&&&&&&&&&&&& {"projectId":"*********","secondProjectId":"","userName":"<EMAIL>","solve360Update":true,"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","dmsType":"FORTELLIS","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"secondProjectIdList":["",""],"uniqueId":"fo20250908084817872945-1757321577037","testData":false,"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","totalRoCount":"   116\n","exceptionTypeCounts":{}}
Mon Sep 08 2025 08:56:07 GMT+0000 : Started SharePoint file upload functionality 
Mon Sep 08 2025 08:56:07 GMT+0000 : Started SharePoint file upload functionality /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip.FORTELLIS
Mon Sep 08 2025 08:56:07 GMT+0000 : Error renaming file ',  {"errno":-2,"code":"ENOENT","syscall":"rename","path":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip","dest":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip"}
Mon Sep 08 2025 08:56:07 GMT+0000 : Finish file renaming&&&&&&&&&&&&&&&&&&&
Mon Sep 08 2025 08:56:07 GMT+0000 : Failed to upload file to SharePoint {PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip} StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:56:07 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>{"fileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","filePath":"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip","secretKey":"7892f47f0f568a6375f8d7a1ac5f69ae07490f7d6f969432a81b230c3a9808fc0afcfae2acca","dmsType":"FORTELLIS","projectId":"*********","secondProjectId":"","solve360Update":true,"userName":"<EMAIL>","warningObj":{"scheduled_by":"<EMAIL>","partDetailsNullExceptionCount":0,"coreReturnExceptionCount":0,"coreChargeExceptionCount":0,"coreReturnNotEqualCoreChargeExceptionCount":0,"coreChargeWithNoSaleCount":0,"invalidCoreCostSaleMismatchCount":0,"invalidCoreAmountMismatchCount":0,"gl_missing_ro_count":0,"miscExceptionCount":0},"thirdPartyUsername":"D100047425","storeCode":"QAAMF16","groupCode":"QAAMF01","resumeUser":"","projectIds":["*********",""],"companyObj":[{"companyId":"*********","companyName":"QA AHM Store 03","projectId":"*********","secondProjectId":"","projectType":"Parts UL","secondaryProjectType":"Labor UL","projectName":"QA AHM-FORT Bundle: Parts R4","secondaryProjectName":"QA AHM-FORT Bundle: Labor R4"}],"processFileName":"PROC-QAAHMStore03-LA-D100047425-2509080852.zip","secondProjectIdList":["",""],"totalRoCount":"   116\n","exceptionTypeCounts":{},"exceptions":"","inSchedulerId":"fo20250908084817872945-1757321577037","testData":""}
Mon Sep 08 2025 08:56:07 GMT+0000 : req.body.processFileName>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>PROC-QAAHMStore03-LA-D100047425-2509080852.zip
Mon Sep 08 2025 08:56:07 GMT+0000 : Dist file exist for sharepoint upload
Mon Sep 08 2025 08:56:07 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Mon Sep 08 2025 08:56:07 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Mon Sep 08 2025 08:56:07 GMT+0000 : sharedFolderPath
Mon Sep 08 2025 08:56:07 GMT+0000 : filePath
Mon Sep 08 2025 08:56:08 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:56:08 GMT+0000 : Share point file upload retry attempt : 3, DMS : FORTELLIS, file name:PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip ,error:{"name":"StatusCodeError","statusCode":400,"message":"400 - \"{\\\"errors\\\":[{\\\"status\\\":\\\"Failure\\\",\\\"message\\\":\\\"Token Mismatch Error\\\"}]}\"","error":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/dist/PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"secretKey\":\"7892f47f0f568a6375f8d7a1ac5f69ae07490f7d6f969432a81b230c3a9808fc0afcfae2acca\",\"dmsType\":\"FORTELLIS\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":true,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"partDetailsNullExceptionCount\":0,\"coreReturnExceptionCount\":0,\"coreChargeExceptionCount\":0,\"coreReturnNotEqualCoreChargeExceptionCount\":0,\"coreChargeWithNoSaleCount\":0,\"invalidCoreCostSaleMismatchCount\":0,\"invalidCoreAmountMismatchCount\":0,\"gl_missing_ro_count\":0,\"miscExceptionCount\":0},\"thirdPartyUsername\":\"D100047425\",\"storeCode\":\"QAAMF16\",\"groupCode\":\"QAAMF01\",\"resumeUser\":\"\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"companyName\":\"QA AHM Store 03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":\"Labor UL\",\"projectName\":\"QA AHM-FORT Bundle: Parts R4\",\"secondaryProjectName\":\"QA AHM-FORT Bundle: Labor R4\"}],\"processFileName\":\"PROC-QAAHMStore03-LA-D100047425-2509080852.zip\",\"secondProjectIdList\":[\"\",\"\"],\"totalRoCount\":\"   116\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"fo20250908084817872945-1757321577037\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":400,"body":"{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}","headers":{"date":"Mon, 08 Sep 2025 08:56:08 GMT","content-type":"application/json; charset=utf-8","content-length":"66","connection":"keep-alive","server":"nginx","x-powered-by":"Express","vary":"Origin","access-control-allow-credentials":"true","access-control-allow-origin":"*","access-control-allow-methods":"GET,POST,OPTIONS,PUT","access-control-allow-headers":"Origin, X-Requested-With, Content-Type, Accept, Authorization","etag":"W/\"42-0UYgAptlzmYB4V905v1gPnhwucA\"","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1385}}}}
Mon Sep 08 2025 08:56:08 GMT+0000 : File uploaded failed to SharePoint {PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip},error:StatusCodeError: 400 - "{\"errors\":[{\"status\":\"Failure\",\"message\":\"Token Mismatch Error\"}]}"
Mon Sep 08 2025 08:56:08 GMT+0000 : Inside send mail method
Mon Sep 08 2025 08:56:08 GMT+0000 : paytypeHaltundefined invSequenceHaltundefined makeHaltundefined undefined 
Mon Sep 08 2025 08:56:08 GMT+0000 : isPreImportExist : false
Mon Sep 08 2025 08:56:08 GMT+0000 : Inside sharepoint file upload completion mail
Mon Sep 08 2025 08:56:08 GMT+0000 : Send Mail: Sharepoint: Filename : QAAMF16
Mon Sep 08 2025 08:56:08 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QAAMF16":{"PROXY":false}}
Mon Sep 08 2025 08:56:08 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Mon Sep 08 2025 08:56:08 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip
Mon Sep 08 2025 08:56:08 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip","proxyFilePath":"","proxyMailSubject":"Fortellis Proxy Zip Failed"}
Mon Sep 08 2025 08:56:08 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip","proxyFilePath":"","proxyMailSubject":"Fortellis Proxy Zip Failed"}
Mon Sep 08 2025 08:56:08 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Mon Sep 08 2025 08:56:08 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Failed","proxy_file_name":"PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************.zip","proxy_url":"","efs_path":"/etl/etl-vagrant/etl-FORTELLIS/FORTELLIS-zip/PROC-QAAMF01-QAAMF16-LA-INITIAL-D100047425-**************-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":0,"core_charge_exception_count":0,"core_return_not_equal_core_charge_exception_count":0,"core_charge_with_no_sale_count":0,"resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":0,"invalid_misc_paytype_count":"","punch_time_missing_count":"","inventory_ro_count":"","invalid_core_amount_mismatch_count":0,"coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":0,"gl_ro_not_found_count":0,"invoice_missing_count":"","suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":0,"multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"pre_import_halt_exist":false,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"invoice_missing_exception_exist_flag":false,"invalid_misc_paytype_flag":false,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"part_details_null_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"punch_time_missing_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Mon Sep 08 2025 08:56:08 GMT+0000 : Send Mail: Sharepoint: notification status: true
Mon Sep 08 2025 08:56:08 GMT+0000 : Send Mail: notification status > true
