#!/bin/bash

TARGET_DIR="/etl/dist/"
LOG_FILE="/etl/log/sharepoint-dropbox-files.txt"
DAYS_OLD=91

echo "Deleted files on $(date):" >> "$LOG_FILE"

# Find and delete .zip files
find "$TARGET_DIR" -maxdepth 1 -type f -name "*.zip" -mtime +$DAYS_OLD -print -delete >> "$LOG_FILE"

DST="sso1-onedrive:etl/dropbox-etl"
echo "Delete the same files from Sharepoint on  $(date)" >> "$LOG_FILE"
echo "--------------------------------------------" >> "$LOG_FILE"
rclone lsf "$DST" --min-age "$DAYS_OLD"d >> "$LOG_FILE"
rclone delete --min-age "$DAYS_OLD"d --max-depth 1 "$DST"
echo "--------------------------------------------" >> "$LOG_FILE"
