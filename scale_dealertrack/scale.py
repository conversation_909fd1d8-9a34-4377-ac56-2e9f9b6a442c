## Install
# pip3 install PyPDF2
# pip3 install reportlab

from PyPDF2 import PdfWriter, PdfReader
import os
import sys
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

# Get arguments
current_dir = sys.argv[1]
current_pdf = sys.argv[2]
out_dir = sys.argv[3]

# File paths
input_pdf_path = os.path.join(current_dir, current_pdf)
output_pdf_path = os.path.join(out_dir, current_pdf)

# Open input PDF file
with open(input_pdf_path, "rb") as in_f:
    input1 = PdfReader(in_f)
    output = PdfWriter()

    num_pages = len(input1.pages)  # Updated to use `len()` for page count
    print(f"Document has {num_pages} pages.")

    for i in range(num_pages):
        page = input1.pages[i]  # Access pages via `.pages` list
        print(f"Scaling page {i+1}...")
        page.scale_to(612, 792)  # Use `scale_to` instead of `scaleTo`
        output.add_page(page)

    # Write to output PDF file
    with open(output_pdf_path, "wb") as out_f:
        output.write(out_f)