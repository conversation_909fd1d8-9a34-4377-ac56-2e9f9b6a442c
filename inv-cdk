#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

# When doing bulk processing the script files that are created are buried within, and relative to, a deep directory
# which makes running them tedious.  This routine locates all of the scripts and transforms them so that they are relative
# to a common ancestor.  By navigating to that ancestor in the CMD shell and running the consolidated and repathed script
# all of the sub PDF files will be created in the ancestor.
if [[ ${1} = '--script-fix' ]]; then
    # find /vagrant/etl-cdk/inv-cdk/SG_ID/ -name bulk-bundling-script-copy-paste.txt -print0 | xargs -0 -I {} ./inv-cdk --script-fix {} \;
    basedir="$(dirname $(dirname $(dirname $2)))"

    sed -e "s|\./|${basedir}/|g" < "$2" \
        | sed -e "s|/vagrant/etl-cdk/inv-cdk|.|g" \
       >> /vagrant/etl-cdk/inv-cdk/fixed-script.txt

    exit
fi

SCN_KEY="${1}"
START_RO="${2:-}"
END_RO="${3:-}"

DMS_BASE="$DU_ETL_HOME"/DU-DMS/DMS-CDK/
ADP_PARSE_DIR="$DU_ETL_HOME"/DU-DMS/DMS-CDK/invoice-manipulation/src/parse/
DMS_INV_MANIP="$DMS_BASE"/invoice-manipulation/
SIS_INV_MANIP="$DU_ETL_HOME"/DU-DMS/DMS-SIS/invoice-manipulation/

TMP_DIR=/tmp/du-etl-inv-cdk-work/
OPEN_RO_COPY_SRC="$TMP_DIR"/openro-source.csv
RESEARCH_RO_COPY_SRC="$TMP_DIR"/researchro-source.csv
clear_dir "$TMP_DIR"

REMOTE_HOST='md-client'

LOCAL_RESULT_BASE=/vagrant/etl-cdk/inv-cdk/

function do_ssh() {
    ssh "$REMOTE_HOST" "$@"
}

mkdir -p "$LOCAL_RESULT_BASE"/SourceInvoices/
mkdir -p "$LOCAL_RESULT_BASE"/SplitInvoices/

if [[ -z "$SCN_KEY" ]]; then

prompt_continuation "Process Source PDFs (no ScenarioKey)? "

if do_ssh 'test -d $HOME/DU-ETL-INVM/SplitInvoices'; then
    say "SplitInvoices exists; can continue with processing (if necessary)."
else
    say "Zipping Local SplitInvoices Directory, Uploading SplitInvoices.zip, Unzipping it, and Processing it."
    (
        cd "$LOCAL_RESULT_BASE"
        if [[ ! -f ./SplitInvoices.zip ]]; then
            zip -qr ./SplitInvoices.zip ./SplitInvoices
        fi
        rsync -P ./SplitInvoices.zip md-client:/home/<USER>/DU-ETL-INVM/
        do_ssh 'unzip -q $HOME/DU-ETL-INVM/SplitInvoices.zip -d $HOME/DU-ETL-INVM/'
    )
fi

if [[ ! -d "$LOCAL_RESULT_BASE"/split-meta ]]; then
    if do_ssh 'test -d $HOME/DU-ETL-INVM/split-meta'; then
        say "Downloading split-meta"
        rsync -rP md-client:/home/<USER>/DU-ETL-INVM/split-meta/ \
              "$LOCAL_RESULT_BASE"/split-meta
        mkdir -p "$LOCAL_RESULT_BASE"/invoice-lists
        cp "$LOCAL_RESULT_BASE"/split-meta/found_invoices.txt \
           "$LOCAL_RESULT_BASE"/invoice-lists/all-found-files.txt
    fi
else
    say "Local split-meta directory already present."
fi

if do_ssh 'test -d $HOME/DU-ETL-INVM/Invoices'; then
    say "Invoices directory present, continuing with listing."
else
    say "Constructing Invoices Directory from SplitInvoices."
    do_ssh 'cd $HOME/DU-ETL-INVM/ && $DU_ETL_HOME/DU-DMS/DMS-CDK/invoice-manipulation/split-dsda-into-individual-invoices'
fi

if [[ ! -f "$LOCAL_RESULT_BASE"/Invoices.zip ]]; then
    if do_ssh 'test -d $HOME/DU-ETL-INVM/Invoices'; then
        if do_ssh 'test -f $HOME/DU-ETL-INVM/Invoices.zip'; then
            say "Download will use existing Invoices.zip file"
        else
            say "Zipping Up Invoices Directory For Download"
            do_ssh 'zip -q $HOME/DU-ETL-INVM/Invoices.zip $HOME/DU-ETL-INVM/Invoices/'
        fi
    else
        if do_ssh 'test -f $HOME/DU-ETL-INVM/Invoices.zip'; then
            say "Missing Invoices directory but Download will use existing Invoices.zip file"
        else
            yell "No remote Invoices directory or Zip file to Download"
        fi
    fi
    say "Downloading Invoices.zip"
    rsync -P md-client:/home/<USER>/DU-ETL-INVM/Invoices.zip \
          "$LOCAL_RESULT_BASE"/Invoices.zip
    say "Unzipping Invoices.zip"
    unzip -q "$LOCAL_RESULT_BASE"/Invoices.zip -d "$LOCAL_RESULT_BASE"/
else
    say "Local Invoices.zip file exists - no download required"
fi

if [[ ! -d "$LOCAL_RESULT_BASE"/invoice-list-files ]]; then
    say "Generating missing all-found-files invoice bundles"
    "$DU_ETL_HOME"/invoice-manipulation-common/segregate-ros \
         --base-dir   "$LOCAL_RESULT_BASE" \
         --label      all-found-files \
         --list       all-found-files.txt \
         --batch-size 100
else
    say "Skipping All Found Invoices Segregation - invoice-list-files already exists."
fi

function build_cdk_openro_file() {
    if [[ -f "$LOCAL_RESULT_BASE"/cdk-open-report.txt ]]; then
        say "Processing OpenRO Report"
        "$ADP_PARSE_DIR"/extract_ros_from_openro_file \
            < "$LOCAL_RESULT_BASE"/cdk-open-report.txt \
            > "$OPEN_RO_COPY_SRC"
    else
        warn "No OpenRO Report (cdk-open-report.txt) Present"
        touch "$OPEN_RO_COPY_SRC"
    fi
}

function build_cdk_research_file() {
    if [[ -f "$LOCAL_RESULT_BASE"/cdk-research-screens.txt ]]; then
        say "Processing Research Screens"
        "$ADP_PARSE_DIR"/extract_ros_from_research_file \
            < "$LOCAL_RESULT_BASE"/cdk-research-screens.txt \
            > "$RESEARCH_RO_COPY_SRC"
    else
        warn "No Research Screens (cdk-research-screens.txt) Present"
        touch "$RESEARCH_RO_COPY_SRC"
    fi
}

if do_ssh 'test ! -d $HOME/DU-ETL-INVM/invoicemaster-meta'; then
    if [[ -f "$LOCAL_RESULT_BASE"/invoice-master.sql ]]; then
        say "Found ADP invoice-master, processing."
        (
            cd $LOCAL_RESULT_BASE
            if [[ ! -d invoicemaster-meta ]]; then
                build_cdk_openro_file
                build_cdk_research_file
                "$DMS_INV_MANIP"/make-lists-from-adp-invoicemaster \
                    --invoice-master-source "$LOCAL_RESULT_BASE"/invoice-master.sql \
                    --open-ro "$OPEN_RO_COPY_SRC" \
                    --research "$RESEARCH_RO_COPY_SRC"
            else
                say "Using existing local invoicemaster-meta."
            fi
        )
    elif [[ -f "$LOCAL_RESULT_BASE"/sis-invoice-master.csv ]]; then
        say "Found SIS invoice-master, processing."
        (
            cd $LOCAL_RESULT_BASE
            if [[ ! -d invoicemaster-meta ]]; then
                build_cdk_openro_file
                build_cdk_research_file
                "$SIS_INV_MANIP"/make-lists-from-sis-invoicemaster \
                    --invoice-master-source "$LOCAL_RESULT_BASE"/sis-invoice-master.csv \
                    --open-ro "$OPEN_RO_COPY_SRC" \
                    --research "$RESEARCH_RO_COPY_SRC"
            else
                say "Using existing local invoicemaster-meta."
            fi
        )
    else
        die "invoice-master.sql OR sis-invoice-master.csv must exist in ${LOCAL_RESULT_BASE}"
    fi
    rsync -rP "$LOCAL_RESULT_BASE"/invoicemaster-meta/ \
          md-client:/home/<USER>/DU-ETL-INVM/invoicemaster-meta
else
    say "Using invoicemaster-meta presently on remote machine"
fi

fi # Do only if SCN_KEY is missing

if [[ -n "$SCN_KEY" ]]; then

prompt_continuation "Create ScenarioKey-specific bundles? "

LOCAL_KEY_PATH="$LOCAL_RESULT_BASE"/$(parse_key_to_path "$SCN_KEY")/
echo "$LOCAL_KEY_PATH"

if do_ssh 'test -d $HOME/DU-ETL-INVM/scenariokey-meta'; then
    do_ssh 'rm -rf $HOME/DU-ETL-INVM/scenariokey-meta'
fi
clear_dir "$LOCAL_KEY_PATH"/scenariokey-meta/
"$DU_ETL_HOME"/invoice-manipulation-common/extract-scenariokey-invoices \
    "$SCN_KEY" \
    "$LOCAL_KEY_PATH"/scenariokey-meta/ \
    "$START_RO" \
    "$END_RO"

rsync -rP "$LOCAL_KEY_PATH"/scenariokey-meta/ \
           md-client:/home/<USER>/DU-ETL-INVM/scenariokey-meta

do_ssh '$DU_ETL_HOME/invoice-manipulation-common/create-filtered-invoice-lists $HOME/DU-ETL-INVM/ ADP'

do_ssh 'rm -rf $HOME/DU-ETL-INVM/invoice-list-files/'
do_ssh 'rm -f  $HOME/DU-ETL-INVM/inv-cdk-results.zip'
do_ssh 'cd $HOME/DU-ETL-INVM/ && $DU_ETL_HOME/DU-DMS/DMS-CDK/invoice-manipulation/perform-list-bundling'

echo "Zipping up remote files"
do_ssh 'cd $HOME/DU-ETL-INVM && zip -qr inv-cdk-results.zip ./invoice-lists/ ./invoice-list-files/'

rsync -P md-client:/home/<USER>/DU-ETL-INVM/inv-cdk-results.zip \
          "$LOCAL_KEY_PATH"/inv-cdk-results.zip

echo "Unzipping to $LOCAL_KEY_PATH"

unzip -q "$LOCAL_KEY_PATH"/inv-cdk-results.zip -d "$LOCAL_KEY_PATH"

unix2dos -q "$LOCAL_KEY_PATH"/invoice-lists/*

fi # do only if SCN_KEY is present

exit
