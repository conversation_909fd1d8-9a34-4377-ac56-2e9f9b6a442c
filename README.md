# DealerUplift - Extract, Transform, and Load DMS Data

# Pre-Requisites for Testing

* "$HOME"/.bashrc.d - to install .bashrc sourced exports
* "$HOME"/pg-service.d - to install PG_SERVICE entries
* GGSMigration - installed for the ggs_staging database

Add this to your .bashrc
```
# PERSONAL BASHRC DIRECTORY SCAN
# The above line is tagged to detected presence
# or absence of this block in the user's .bashrc
# file so that it can generated idempotently.
#
# If present cycle through and source all files
# present in the .bashrc.d directory
if [[ -d "$HOME"/.bashrc.d ]]
then
  shopt -s nullglob
  for rcfile in "$HOME"/.bashrc.d/*
  do
    source "$rcfile"
  done
  shopt -u nullglob
else
  # If this code block is present the directory
  # should be as well.  Go ahead and create it
  # if its missing.  It doesn't matter that it
  # is empty.
 mkdir -p "$HOME"/.bashrc.d
fi
```

# Pre-Requisites for Support and Production

 * SOLVE360_API_EMAIL
 * SOLVE360_API_TOKEN
 * GGS_PROD_PG_SERVICE (with a corresponding pg-service.d definition)

And these as well, with somewhat self-describing suggestive names.  These are used by the `do-transfer` administration script.
```
# Designates where to push import results performed for production
export DU_ETL_DIST_DIR="/home/<USER>/du-etl-work/distribution"

# When this is defined the local machine has a link
# to the "ready-for-import" production files.
# The standard naming conventions are assumed to be
# in play i.e., "etl-<dms>/<dms>-{config,zip}"
# and the directory specified below points to the
# base under which the "etl-<dms>" exist.
export DU_ETL_WORK_REMOTE="/mnt/du-etl-work-remote"

# The dropoff location for invoices that need to be
# imported via GGS Task.
export DU_ETL_GGS_INV_REMOTE="/mnt/du-client-invoicestaging"

# File drop-off location on the main production file server
export DU_ETL_PROD_DROPBOX="/mnt/armatus/#Customers/#001 - Dropbox"
```

# Installation

Run `./install` while in the repository root and follow all applicable directives.

Note that while DU_ETL_HOME ends up being defined it is not added to the path.

# Usage

* `./run-tests` (can also run individual ones in the various DMS repos)
* `./etl-transform`
* `./etl-<dms> [test|pull|refresh|--distribute-only]`

The other root directory scripts are for development and administration.
