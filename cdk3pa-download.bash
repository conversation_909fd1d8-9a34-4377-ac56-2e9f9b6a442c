#!/usr/bin/env bash

COPY="${1:-true}"
SINCE="${2:-yesterday}"
SINCE_SECONDS=$(date -d "$SINCE" +%s)

DST="$DU_ETL_ETI_DIR_CDK3PA"
SRC="$DU_ETL_CDK3PA_SHAREPOINT"

TMP=~/tmp/cdk3pa-files.txt

rclone lsl "$SRC" --max-depth 1 > "$TMP"

while read SIZE DATE TIME FILE
do
    SECONDS=$(date -d "$DATE" +%s)
    if [[ $SECONDS -ge $SINCE_SECONDS && "$FILE" =~ ^PROC.*ETL ]]; then
         if [[ ! -f "$DST/$FILE" && ! -f "$DST/archive/$FILE" ]]; then
           echo "$DATE" "$FILE"
           if [[ "$COPY" = "true" ]]; then
              rclone copy "$SRC/$FILE" "$DST"
           fi
        fi
    fi
done < "$TMP"
