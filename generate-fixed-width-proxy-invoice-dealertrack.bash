#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

REPO_ROOT="$(cd $(dirname $0) && pwd)"

SCHEMA_NAME="${1:?Schema Name Required}"
TARGET_DIR="${2:?Target Directory Required}"
CONVERT_TO_PDF="${3:-true}"
PORSCHE_STORE="${6:-false}"
BRAND="${7}"
DEALER_ADDRESS="${8}"
DUAL_PROXY="${10}"
# Ver generic_proxy:- Generic proxy invoice,
# Ver merge_with_template:- Proxy invoice merged with CDK template,
# Ver invoice_mimic:- Mimic invoice merged with CDK template
VERSION="${4:-"generic_proxy"}"
BACKGROUND_TEMPLATE_PATH="${5:-none}"

echo "PORSCHE_STORE:$PORSCHE_STORE"

echo "DEALER_ADDRESS Proxy:$DEALER_ADDRESS"
APPLY_BACKGROUND='false'

function psql_local() {
    psql "service=${DU_ETL_PG_SERVICE}" --set=ON_ERROR_STOP=1 "$@"
}

if [[ -f "$BACKGROUND_TEMPLATE_PATH" ]]; then
    APPLY_BACKGROUND='true'
fi

if [[ "$VERSION" = "generic_proxy" ]]; then
    "$REPO_ROOT"/generate-fixed-width-proxy-invoice.bash \
            "$SCHEMA_NAME" \
            "$TARGET_DIR" \
            "$CONVERT_TO_PDF"
elif [[ "$VERSION" = "invoice_mimic" ]]; then
    TMP_DIR="/tmp/du-etl-dealertrack-proxyinvoice-work"
    mkdir -p "$TMP_DIR"
    clear_dir "$TMP_DIR"
    BOOK_DIR="$TMP_DIR"/bookmarking
    SCALED_DIR="$TMP_DIR"/scaled
    MASK_DIR="$TMP_DIR"/masked
    SPLITTEDPDF="$TMP_DIR"/splitted_pdf
    COMBINEDPDF="$TMP_DIR"/combined_pdf
    SPLITTEDOUT="$TMP_DIR"/splittedout
    BOOKMARKDIR="$TMP_DIR"/bookmark
    clear_dir "$SCALED_DIR"
    clear_dir "$MASK_DIR"
    clear_dir "$SPLITTEDPDF"
    clear_dir "$COMBINEDPDF"
    clear_dir "$SPLITTEDOUT"
    clear_dir "$BOOKMARKDIR"

    if [[ "$DUAL_PROXY" = 'true' ]]; then
        TEXT_OUT=text-porsche
        PDF_OUT=pdf-porsche
        BUNDLE_OUT=bundle-porsche
    else
        TEXT_OUT=text
        PDF_OUT=pdf
        BUNDLE_OUT=bundle
        clear_dir "$TARGET_DIR"
    fi

    [[ -d "$TARGET_DIR" ]] || die "$2 must exist and should be empty"

    mkdir "$TARGET_DIR/$TEXT_OUT" || die "Could not prepare text directory"
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        mkdir "$TARGET_DIR/$PDF_OUT"  || die "Could not prepare pdf directory"
    fi

    (
        cd "$TARGET_DIR/$TEXT_OUT" || die "Failed to set target directory as active"
        "$REPO_ROOT"/create-proxy-invoice-report-dealertrack-mimic.pl "${SCHEMA_NAME}" "${PORSCHE_STORE}" "${DEALER_ADDRESS}"

        #Create fixed width proxy invoice PDF report by iterating the proxy invoice txt reports
        loop_count=0
        if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
            progress "Beginning PDF Conversions - Count: $(find . -type f | wc -l)"
            echo "$(date +%H:%M:%S) Starting"
            for pi in ./*.txt; do
                ((loop_count++))
                if [[ $(($loop_count%200)) = 0 ]]; then
                    echo "$(date +'%H:%M:%S') Converted $loop_count"
                fi
                clear_dir "$BOOK_DIR"
                [ -f "$pi" ] || break
                filename=$(basename "$pi")
                filename="${filename%.*}"

		        enscript -q \
		                -fCourierStd-Bold10 \
		                --no-header \
                        --media=letter \
		                --margins=8:2:30:10 \
		                -L 73 "${pi}" \
		                -o - \
		            | ps2pdf - "$BOOK_DIR"/"${filename}.pdf"

                if [[ "$BRAND" = "AUDI" ]]; then
                        SEARCH_RES=$(pdftotext "${BOOK_DIR}/${filename}.pdf" - | grep 'Invoice Source Not Present:')
                        if [ "${SEARCH_RES}" ]; then
                            pdftk "$BOOK_DIR"/"${filename}.pdf" multibackground "$REPO_ROOT"/invoice-template/dealertrack_template.pdf output "$BOOK_DIR"/"${filename}-merged.pdf"
                        else
                            pdftk "$BOOK_DIR"/"${filename}.pdf" multibackground "$REPO_ROOT"/invoice-template/dtrack-2-page.pdf output "$BOOK_DIR"/"${filename}-merged.pdf"
                        fi
                else                
                    pdftk "$BOOK_DIR"/"${filename}.pdf" multibackground "$REPO_ROOT"/invoice-template/dealertrack_template.pdf output "$BOOK_DIR"/"${filename}-merged.pdf"
                fi
                    

                pdftk "$BOOK_DIR"/"${filename}.pdf" dump_data output "${BOOK_DIR}"/temp-pdf-metadata.txt

                printf 'BookmarkBegin\nBookmarkTitle: %s\nBookmarkLevel: 1\nBookmarkPageNumber: 1' "${filename}" \
                    > "${BOOK_DIR}"/temp-pdf-new-bookmark.txt

                cat "${BOOK_DIR}"/temp-pdf-metadata.txt \
                    "${BOOK_DIR}"/temp-pdf-new-bookmark.txt \
                    > "${BOOK_DIR}"/temp-pdf-new-metadata.txt

                pdftk "${BOOK_DIR}"/"${filename}-merged.pdf" \
                    update_info \
                    "${BOOK_DIR}"/temp-pdf-new-metadata.txt \
                    output - \
                    > ../"${PDF_OUT}/${filename}.pdf" 2>&1
                
            done
            echo "$(date +%H:%M:%S) Ending"
        else
            progress "Skipping PDF Conversion as Requested"
        fi

    ) || die "Failed during Proxy Invoice generation"


    # Create one or more PDF bundles from the individual PDF files
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        (
            cd "$TARGET_DIR/${PDF_OUT}"
            mkdir ../"${BUNDLE_OUT}"
            BUNDLE_PREFIXES=$(find . -mindepth 1 -maxdepth 1 -type f -exec basename {} \; | perl -ale 'print $1 if /(\d+)\d{2}.pdf/;' | sort | uniq)
            for prefix in $BUNDLE_PREFIXES; do
                FIRST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $1}')
                LAST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $NF}')
                FIRST=${FIRST_TMP::-4}
                LAST=${LAST_TMP::-4}
                pdftk ./"${prefix}"*.pdf cat output ../"${BUNDLE_OUT}"/bundle-"${FIRST}-${LAST}".pdf
            done
        )
    fi
    say "Done Creating Proxy Invoices"
else
    die "Invalid Proxy Version"
fi
