#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Time::Format qw(%time %strftime %manip);

no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];

# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;

my @header_section;
my @job_section;
my @grand_total_section;
my @end_of_invoice_section;
my @invoice_note_section;

my ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

my ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

my ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

my ($sale_amount, $labor_cost, $parts_cost, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;

my ($parts_entries, $misc_entries, $gog_entries, $sublet_entries, $ded_entries) = (0)x5;
my $lbr_type;
my $page_max_height = 36; # Maximum no of lines in page body
my $curr_page_height = 0;
my $invoice_note_lines = 22; # No of lines needed for invoice note
my $pages;
my $curr_page_num;

my $ro_qry = $conn->prepare("SELECT ro_number
                               FROM repair_order
                              ORDER BY ro_number");

$ro_qry->execute();
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number) = @ro_list;
    my $file = $ro_number . ".txt";
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;
    load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
    my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
    $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
    $ro_doc_insert->finish;
}

# Subroutine to prepare HEADER section of the invoice
sub make_header {
    my $ro_header_qry = $conn->prepare("SELECT
                                ro.creation_date,
                                ro.completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                ro.advisor,
                                roc.customer_number,
                                roc.customer_name,
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip,
                                roc.customer_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                rov.vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
    my ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor, $customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $customer_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in ) = @header_row;

    push (@header_section, sprintf(border(">")." %13s %-20s %17s  %-10s %35s ".border("|")."\n",
           "", $customer_number, "", $ro_number, ""));

    push (@header_section, sprintf(border(">")." %-100s ".border("|")."\n", $customer_name));

    push (@header_section, sprintf(border(">")." %-50s%-50s ".border("|")."\n",
           $customer_email, " "));
    push (@header_section, sprintf(border(">")." %-50s%-50s ".border("|")."\n",
           $customer_address, " "));
    push (@header_section, sprintf(border(">")." %-50s%-50s ".border("|")."\n",
           $customer_city.", ".$customer_state.", ".$customer_zip, " "));

    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
    push (@header_section, sprintf(border(">")." %59s %-3s %36s ".border("|")."\n", "", "<--pg_num-->", ""));

    push (@header_section, sprintf(border(">")." %6s %-20s     %-68s ".border("|")."\n",
           "", $customer_phone, $customer_phone));
    push (@header_section, sprintf(border(">")." %6s %-20s     %-35s  %-31s ".border("|")."\n",
           "", $customer_phone, $customer_phone, $advisor));
    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
    push (@header_section, sprintf(border(">")." %11s %-4s  %-25s  %-21s  %-11s  %-19s ".border("|")."\n",
           "", $vehicle_year, $vehicle_make."/".$vehicle_model, $vehicle_vin, "", $mileage_in."/" ));

    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line

    push (@header_section, sprintf(border(">")." %88s %-11s ".border("|")."\n",
           " ", $time{'mm/dd/yy', $completion_date}));

    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));

    push (@header_section, sprintf(border(">")." %-17s  %-17s %63s ".border("|")."\n",
          $time{'mm/dd/yy', $creation_date}, $time{'mm/dd/yy', $completion_date}, ""));
    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {
    my $job_qry = $conn->prepare("SELECT
                                    rl.ro_line,
                                    rl.complaint,
                                    rj.ro_job_line,
                                    left(rj.billing_code, 1) AS billing_code,
                                    rj.billing_code          AS billing_labor_type,
                                    rj.op_code,
                                    rj.op_description,
                                    rj.sold_hours,
                                    rj.labor_cost,
                                    rj.sale_amount,
                                    rj.cause,
                                    rj.correction
                                FROM repair_line rl
                                    JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ?");

    $job_qry->execute($ro_number);
    my ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours, $cause, $correction );
    while (my @job_row = $job_qry->fetchrow_array) {
        ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours, $labor_cost, $sale_amount, $cause, $correction ) = @job_row;
        my $lbr_billing;
        if ($billing_code eq 'I' || $billing_code eq 'i'){
            $lbr_type = "INTERNAL";
            $lbr_billing = "INTERNAL". (defined($billing_labor_type) ? " (" .$billing_labor_type. ")".("-"x(5-length($billing_labor_type))) : "-"x8);
        } elsif ($billing_code eq 'C' || $billing_code eq 'c'){
            $lbr_type = "CUSTOMER";
            $lbr_billing = "CUSTOMER". (defined($billing_labor_type) ? " (" .$billing_labor_type. ")".("-"x(5-length($billing_labor_type))) : "-"x8);
        } elsif ($billing_code eq 'W' || $billing_code eq 'w'){
            $lbr_type = "WARRANTY";
            $lbr_billing = "WARRANTY". (defined($billing_labor_type) ? " (" .$billing_labor_type. ")".("-"x(5-length($billing_labor_type))) : "-"x8);
        }
        my @job_header_section;
        push (@job_header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@job_header_section, sprintf(border(">")." %4sJOB#%3s CHARGES%s ".border("|")."\n", "", $ro_line,        "-"x81));
        push (@job_header_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@job_header_section, sprintf(border(">")." %4sLABOR----%-16s%-71s ".border("|")."\n",  "", $lbr_billing, "-"x66));

        my $techs_qry = "SELECT
                            string_agg(DISTINCT tech_id, ', '),
                            sum(actual_hours),
                            sum(booked_hours)
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?";

        my ($job_techs, $total_act_time, $total_booked_time) = $conn->selectrow_array($techs_qry, undef, ($ro_number, $ro_line, $ro_job_line));

        # Job, OpCode , Detail
        $Text::Wrap::columns = 45;
        my $wrapped_op_description = fill('', '', expand($op_description));
        my @op_description_list = split "\n", $wrapped_op_description;

        push (@job_header_section, sprintf(border(">")."     J# %-3s %-18s %-45s %6.2f  %7.2f  %7.2f ".border("|")."\n",$ro_line, $op_code,
                                            $op_description_list[0], $sold_hours, $labor_cost, $sale_amount));
        foreach my $i (1 .. $#op_description_list) {
            push (@job_header_section, sprintf(border(">")." %30s%-45s%25s ".border("|")."\n", " ", $op_description_list[$i], " "));
        }

        push (@job_header_section, sprintf(border(">")."     TECH(S): %-87s ".border("|")."\n", $job_techs));

        push (@job_section, [@job_header_section]);

        my @tech_section = make_tech_section($ro_line, $ro_job_line, $total_act_time, $total_booked_time);
        push (@job_section, [@tech_section]);

        # Job Complaint
        my @job_complaint_section;
        $Text::Wrap::columns = 80;
        my $wrapped_complaint = fill('', '', expand($complaint));
        my @complaint_list = split "\n", $wrapped_complaint;
        push (@job_complaint_section, sprintf(border(">")." %4s%-13s%-83s ".border("|")."\n", "", "COMPLAINT", $complaint_list[0]));
        foreach my $i (1 .. $#complaint_list) {
            push (@job_complaint_section, sprintf(border(">")." %17s%-83s ".border("|")."\n", "", $complaint_list[$i]));
        }

        push (@job_section, [@job_complaint_section]);

        # Job Cause
        my @job_cause_section;
        my $wrapped_cause = fill('', '', expand($cause));
        my @cause_list = split "\n", $wrapped_cause;
        push (@job_cause_section, sprintf(border(">")." %4s%-13s%-83s ".border("|")."\n", "", "CAUSE", $cause_list[0]));
        foreach my $i (1 .. $#cause_list) {
            push (@job_cause_section, sprintf(border(">")." %17s%-83s ".border("|")."\n", "", $cause_list[$i]));
        }

        push (@job_section, [@job_cause_section]);

        # Job Correction
        my @job_correction_section;
        my $wrapped_correction = fill('', '', expand($correction));
        my @correction_list = split "\n", $wrapped_correction;
        push (@job_correction_section, sprintf(border(">")." %4s%-13s%-83s ".border("|")."\n", "", "CORRECTION", $correction_list[0]));
        foreach my $i (1 .. $#correction_list) {
            push (@job_correction_section, sprintf(border(">")." %17s%-83s ".border("|")."\n", "", $correction_list[$i]));
        }

        push (@job_section, [@job_correction_section]);

        my @parts_section = make_parts_section($ro_line, $ro_job_line);
        push (@job_section, [@parts_section]);

        my @misc_section = make_misc_section($ro_line);
        push (@job_section, [@misc_section]);

        my @gog_section = make_gog_section($ro_line);
        push (@job_section, [@gog_section]);

        my @sublet_section = make_sublet_section($ro_line);
        push (@job_section, [@sublet_section]);

        my @ded_section = make_deductible_section($ro_line);
        push (@job_section, [@ded_section]);

        my @job_total_section = make_job_total_section($ro_line);
        push (@job_section, [@job_total_section]);

    }
}

# Subroutine to prepare the TECH section of a JOB
sub make_tech_section{
    my ($ro_line, $ro_job_line, $total_act_time, $total_booked_time) = @_;
    my @tech_section_array;
    my $tech_details_qry = $conn->prepare("SELECT
                                                ro_job_tech_line,
                                                tech_id,
                                                work_date,
                                                work_start_time,
                                                work_end_time,
                                                work_note,
                                                actual_hours,
                                                booked_hours
                                            FROM repair_job_technician_detail td
                                            WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
    $tech_details_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours);
    if($tech_details_qry->rows > 0){
        push (@tech_section_array, sprintf(border("#")." %-100s ".border("|")."\n",
                                            "               TECH#      DATE      START   FINISH      ACT     TIME    DESCRIPTION"));

        while (my @tech_row = $tech_details_qry->fetchrow_array) {
            ($ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours) = @tech_row;

            push (@tech_section_array, sprintf(border(">")."           %10s   %-10s   %5s   %6s   %6.2f   %6.2f   %29s ".border("|")."\n",
                                                $tech_id, $work_date, $work_start_time, $work_end_time, $actual_hours, $booked_hours, $work_note));
        }
        push (@tech_section_array, sprintf(border(">")." %35s%-18s%6.2f   %6.2f %31s ".border("|")."\n", "","TOTAL TECH TIME",
                                            $total_act_time, $total_booked_time, ""));
        push (@tech_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
    }
    return @tech_section_array;
}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line) = @_;
    my @parts_section_array;

    push (@parts_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
    my $parts_qry = $conn->prepare("SELECT
                                        ro_part_line,
                                        part_number,
                                        part_description,
                                        quantity_sold,
                                        unit_cost,
                                        unit_sale
                                    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ? AND quantity_sold > 0");
    $parts_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_part_line, $part_number, $part_description, $quantity_sold, $unit_cost, $unit_sale);
    $parts_sale = 0;
    $parts_cost = 0;
    $parts_entries = $parts_qry->rows;
    if($parts_entries > 0){
        push (@parts_section_array, sprintf(border("#")." %4s PARTS--QTY--FP--NUMBER%s--DESCRIPTION%s--%6s---%6s---%6s--%-7s ".border("|")."\n",
                                            "", "-"x12, "-"x13, "U/COST", "E/COST", "U/SALE", "-"x7));
        while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_number, $part_description, $quantity_sold, $unit_cost, $unit_sale) = @parts_row;

            $Text::Wrap::columns = 24;
            my $wrapped_part_description = fill('', '', expand($part_description));
            my @part_description_list = split "\n", $wrapped_part_description;
            push (@parts_section_array, sprintf(border(">")." %-10s  %3s  %2s  %-18s  %-24s %7.2f  %7.2f  %7.2f  %7.2f ".border("|")."\n",
                                                "", $quantity_sold, "", $part_number, $part_description_list[0], $unit_cost,
                                                $unit_cost*$quantity_sold, $unit_sale, $unit_sale*$quantity_sold ));
            foreach my $i (1 .. $#part_description_list) {
                push (@parts_section_array, sprintf(border(">")." %41s%-24s%-35s ".border("|")."\n", "", $part_description_list[$i], ""));
            }
            $parts_sale = $parts_sale + ($unit_sale*$quantity_sold);
            $parts_cost = $parts_cost + ($unit_cost*$quantity_sold);
        }
    }
    return @parts_section_array;
}

# Subroutine to prepare the MISC section of a JOB
sub make_misc_section{
    my ($ro_line) = @_;
    my @misc_section_array;
    my $misc_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       other_cost,
                                       other_sale
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line =? AND item_type = 'MISC'");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $misc_code, $misc_description, $misc_cost, $misc_sale);
    $misc_job_sale = 0;
    $misc_job_cost = 0;
    $misc_entries = $misc_qry->rows;
    if($misc_entries > 0){
        push (@misc_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@misc_section_array, sprintf(border("#")." %4s MISC%sCODE%sDESCRIPTION%sCOST%sSALE%s ".border("|")."\n",
                                            "", "-"x3, "-"x9, "-"x44, "-"x7, "-"x5));

        while (my @misc_row = $misc_qry->fetchrow_array) {
            ( $misc_code, $misc_description, $misc_cost, $misc_sale) = @misc_row;
            $Text::Wrap::columns = 48;
            my $wrapped_misc_description = fill('', '', expand($misc_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;
            push (@misc_section_array, sprintf(border(">")." %-12s% -10s   %-48s   %8.2f   %8.2f%5s ".border("|")."\n",
                                                "", $misc_code, $misc_description_list[0], $misc_cost, $misc_sale,"" ));
            foreach my $i (1 .. $#misc_description_list) {
                push (@misc_section_array, sprintf(border(">")." %25s%-48s%-27s ".border("|")."\n", "", $misc_description_list[$i], ""));
            }
            $misc_job_sale = $misc_job_sale + $misc_sale;
            $misc_job_cost = $misc_job_cost + $misc_cost;
        }
    }
    return @misc_section_array;
}

# Subroutine to prepare the GOG section of a JOB
sub make_gog_section{
    my ($ro_line) = @_;
    my @gog_section_array;
    my $gog_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'GOG'");
    $gog_qry->execute($ro_number, $ro_line);
    my ( $gog_code, $gog_description, $gog_cost, $gog_sale);
    $gog_job_sale = 0;
    $gog_job_cost = 0;
    $gog_entries = $gog_qry->rows;
    if($gog_qry->rows > 0){
        push (@gog_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@gog_section_array, sprintf(border("#")." %4s GOG%sCODE%sDESCRIPTION%sCOST%sSALE%s ".border("|")."\n",
                                            "", "-"x4, "-"x9, "-"x44, "-"x7, "-"x5));

        while (my @gog_row = $gog_qry->fetchrow_array) {
            ( $gog_code, $gog_description, $gog_cost, $gog_sale) = @gog_row;
            $Text::Wrap::columns = 48;
            my $wrapped_gog_description = fill('', '', expand($gog_description));
            my @gog_description_list = split "\n", $wrapped_gog_description;
            push (@gog_section_array, sprintf(border(">")." %-12s% -10s   %-48s   %8.2f   %8.2f%5s ".border("|")."\n","",
                                                $gog_code, $gog_description_list[0], $gog_cost, $gog_sale,"" ));
            foreach my $i (1 .. $#gog_description_list) {
                push (@gog_section_array, sprintf(border(">")." %25s%-48s%-27s ".border("|")."\n", "", $gog_description_list[$i], ""));
            }
            $gog_job_sale = $gog_job_sale + $gog_sale;
            $gog_job_cost = $gog_job_cost + $gog_cost;
        }
    }
    return @gog_section_array;
}

# Subroutine to prepare the SUBLET section of a JOB
sub make_sublet_section{
    my ($ro_line) = @_;
    my @sublet_section_array;
    my $sublet_qry = $conn->prepare("SELECT
                                        other_code,
                                        other_description,
                                        other_cost,
                                        other_sale
                                    FROM repair_other
                                    WHERE ro_number = ? AND ro_line =? AND item_type = 'SUBLET'");
    $sublet_qry->execute($ro_number, $ro_line);
    my ( $sublet_code, $sublet_description, $sublet_cost, $sublet_sale);
    $sublet_job_sale = 0;
    $sublet_job_cost = 0;
    $sublet_entries = $sublet_qry->rows;
    if($sublet_entries > 0){
        push (@sublet_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@sublet_section_array, sprintf(border("#")." %4s SUBLET%sCODE%sDESCRIPTION%sCOST%sSALE%s ".border("|")."\n",
                                            "", "-"x3, "-"x9, "-"x42, "-"x7, "-"x5));

        while (my @sublet_row = $sublet_qry->fetchrow_array) {
            ( $sublet_code, $sublet_description, $sublet_cost, $sublet_sale) = @sublet_row;
            $Text::Wrap::columns = 46;
            my $wrapped_sublet_description = fill('', '', expand($sublet_description));
            my @sublet_description_list = split "\n", $wrapped_sublet_description;
            push (@sublet_section_array, sprintf(border(">")." %-14s% -10s   %-46s   %8.2f   %8.2f%5s ".border("|")."\n",
                                                "", $sublet_code, $sublet_description_list[0], $sublet_cost, $sublet_sale, "" ));
            foreach my $i (1 .. $#sublet_description_list) {
                push (@sublet_section_array, sprintf(border(">")." %27s%-46s%-27s ".border("|")."\n", "", $sublet_description_list[$i], ""));
            }
            $sublet_job_sale = $sublet_job_sale + $sublet_sale;
            $sublet_job_cost = $sublet_job_cost + $sublet_cost;
        }
    }
    return @sublet_section_array;
}

# Subroutine to prepare the DEDUCTIBLE section of a JOB
sub make_deductible_section{
    my ($ro_line) = @_;
    my @ded_section_array;
    my $ded_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'DEDUCTIBLE'");
    $ded_qry->execute($ro_number, $ro_line);
    my ( $ded_code, $ded_description, $ded_cost, $ded_sale);
    $ded_job_sale = 0;
    $ded_job_cost = 0;
    $ded_entries = $ded_qry->rows;
    if($ded_entries > 0){
        push (@ded_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@ded_section_array, sprintf(border("#")." %4s DEDUCTIBLE%sCODE%sDESCRIPTION%sSALE%s ".border("|")."\n",
                                            "", "-"x3, "-"x12, "-"x42, "-"x9));

        while (my @ded_row = $ded_qry->fetchrow_array) {
            ( $ded_code, $ded_description, $ded_cost, $ded_sale) = @ded_row;
            $Text::Wrap::columns = 47;
            my $wrapped_ded_description = fill('', '', expand($ded_description));
            my @ded_description_list = split "\n", $wrapped_ded_description;
            push (@ded_section_array, sprintf(border(">")." %-18s%-13s   %-47s   %7.2f%9s ".border("|")."\n",
                                                "", $ded_code, $ded_description_list[0], $ded_sale, "" ));
            foreach my $i (1 .. $#ded_description_list) {
                push (@ded_section_array, sprintf(border(">")." %34s%-47s%-19s ".border("|")."\n", "", $ded_description_list[$i], ""));
            }
            $ded_job_sale = $ded_job_sale + $ded_sale;
            $ded_job_cost = $ded_job_cost + $ded_cost;
        }
    }
    return @ded_section_array;
}

# Subroutine to prepare the TOTAL section of a JOB
sub make_job_total_section{
    my ($ro_line) = @_;
    my @job_total_section_array;
    push (@job_total_section_array, sprintf(border("#")." %100s ".border("|")."\n", ""));

    push (@job_total_section_array, sprintf(border(">")." %4sJOB#%3s TOTALS %s%s%s%s%s%s%s ".border("|")."\n", "", $ro_line,
       "-"x10, "CUSTOMER",
       "-"x19, "WARRANTY",
       "-"x19, "INTERNAL",
       "-"x9));

    push (@job_total_section_array, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                            "", "COST", "SALE", "COST", "SALE", "COST", "SALE"));

    if ($lbr_type eq "INTERNAL"){
        push (@job_total_section_array, sprintf(border(">")." %9sLABOR      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", 0, 0, 0, 0, $labor_cost, $sale_amount));
        if($parts_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sPARTS      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, 0, 0, 0, $parts_cost, $parts_sale));
        }
        if($misc_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sMISC       %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, 0, 0, 0, $misc_job_cost, $misc_job_sale));
        }
        if($gog_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sGOG        %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, 0, 0, 0, $gog_job_cost, $gog_job_sale));
        }
        if($sublet_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sSUBLET     %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, 0, 0, 0, $sublet_job_cost, $sublet_job_sale));
        }
        if($ded_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sDEDUCTIBLE %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, $ded_job_sale, 0, 0, 0, 0));
        }

        push (@job_total_section_array, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                                "", "-"x11, "-"x11, "-"x11, "-"x11, "-"x11, "-"x11));
        push (@job_total_section_array, sprintf(border(">")." %9sTOTAL      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", 0, $ded_job_sale, 0, 0,
                                                $labor_cost+$parts_cost+$misc_job_cost+$gog_job_cost+$sublet_job_cost,
                                                $sale_amount+$parts_sale+$misc_job_sale+$gog_job_sale+$sublet_job_sale));

    } elsif ($lbr_type eq "CUSTOMER"){
        push (@job_total_section_array, sprintf(border(">")." %9sLABOR      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", $labor_cost, $sale_amount, 0, 0, 0, 0));
        if($parts_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sPARTS      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", $parts_cost, $parts_sale, 0, 0, 0, 0));
        }
        if($misc_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sMISC       %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", $misc_job_cost, $misc_job_sale, 0, 0, 0, 0));
        }
        if($gog_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sGOG        %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", $gog_job_cost, $gog_job_sale, 0, 0, 0, 0));
        }
        if($sublet_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sSUBLET     %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", $sublet_job_cost, $sublet_job_sale, 0, 0, 0, 0));
        }
        if($ded_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sDEDUCTIBLE %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, $ded_job_sale, 0, 0, 0, 0));
        }

        push (@job_total_section_array, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                                "", "-"x11, "-"x11, "-"x11, "-"x11, "-"x11, "-"x11));
        push (@job_total_section_array, sprintf(border(">")." %9sTOTAL      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n", "",
                                                $labor_cost+$parts_cost+$misc_job_cost+$gog_job_cost+$sublet_job_cost,
                                                $sale_amount+$parts_sale+$misc_job_sale+$gog_job_sale+$sublet_job_sale+$ded_job_sale,
                                                0, 0, 0, 0));

    } elsif ($lbr_type eq "WARRANTY"){
        push (@job_total_section_array, sprintf(border(">")." %9sLABOR      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", 0, 0, $labor_cost, $sale_amount, 0, 0));
        if($parts_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sPARTS      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, 0, $parts_cost, $parts_sale, 0, 0));
        }
        if($misc_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sMISC       %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, 0, $misc_job_cost, $misc_job_sale, 0, 0));
        }
        if($gog_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sGOG        %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, 0, $gog_job_cost, $gog_job_sale, 0, 0));
        }
        if($sublet_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sSUBLET     %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, 0, $sublet_job_cost, $sublet_job_sale, 0, 0));
        }
        if($ded_entries > 0){
            push (@job_total_section_array, sprintf(border(">")." %9sDEDUCTIBLE %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                    "", 0, $ded_job_sale, 0, 0, 0, 0));
        }

        push (@job_total_section_array, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                                "", "-"x11, "-"x11, "-"x11, "-"x11, "-"x11, "-"x11));
        push (@job_total_section_array, sprintf(border(">")." %9sTOTAL      %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                                "", 0, $ded_job_sale, $labor_cost+$parts_cost+$misc_job_cost+$gog_job_cost+$sublet_job_cost,
                                                $sale_amount+$parts_sale+$misc_job_sale+$gog_job_sale+$sublet_job_sale, 0, 0));
    }

    if ($lbr_type eq "INTERNAL"){
        $intern_lbr_cost = $intern_lbr_cost+$labor_cost;
        $intern_lbr_sale = $intern_lbr_sale+$sale_amount;
        $intern_prt_cost = $intern_prt_cost+$parts_cost;
        $intern_prt_sale = $intern_prt_sale+$parts_sale;
        $intern_misc_cost = $intern_misc_cost+$misc_job_cost;
        $intern_misc_sale = $intern_misc_sale+$misc_job_sale;
        $intern_gog_cost = $intern_gog_cost+$gog_job_cost;
        $intern_gog_sale = $intern_gog_sale+$gog_job_sale;
        $intern_sublet_cost = $intern_sublet_cost+$sublet_job_cost;
        $intern_sublet_sale = $intern_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    } elsif ($lbr_type eq "CUSTOMER"){
        $cust_lbr_cost = $cust_lbr_cost+$labor_cost;
        $cust_lbr_sale = $cust_lbr_sale+$sale_amount;
        $cust_prt_cost = $cust_prt_cost+$parts_cost;
        $cust_prt_sale = $cust_prt_sale+$parts_sale;
        $cust_misc_cost = $cust_misc_cost+$misc_job_cost;
        $cust_misc_sale = $cust_misc_sale+$misc_job_sale;
        $cust_gog_cost = $cust_gog_cost+$gog_job_cost;
        $cust_gog_sale = $cust_gog_sale+$gog_job_sale;
        $cust_sublet_cost = $cust_sublet_cost+$sublet_job_cost;
        $cust_sublet_sale = $cust_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    } elsif ($lbr_type eq "WARRANTY"){
        $warr_lbr_cost = $warr_lbr_cost+$labor_cost;
        $warr_lbr_sale = $warr_lbr_sale+$sale_amount;
        $warr_prt_cost = $warr_prt_cost+$parts_cost;
        $warr_prt_sale = $warr_prt_sale+$parts_sale;
        $warr_misc_cost = $warr_misc_cost+$misc_job_cost;
        $warr_misc_sale = $warr_misc_sale+$misc_job_sale;
        $warr_gog_cost = $warr_gog_cost+$gog_job_cost;
        $warr_gog_sale = $warr_gog_sale+$gog_job_sale;
        $warr_sublet_cost = $warr_sublet_cost+$sublet_job_cost;
        $warr_sublet_sale = $warr_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    }
    return @job_total_section_array;
}

#Subroutine to prepare the FOOTER section of the invoice
sub get_footer {
    my @footer_section;
    my ($is_end, $page_number) = @_;
    push (@footer_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
    if($is_end) {
        # no newline after the form-feed since a last newline is coming from somewhere and
        # causing the PDF document to paginate a blank page at the end.
        push (@footer_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@footer_section, sprintf(border("#")." %100s ".border("|")."\n", ""));

        push (@footer_section, sprintf(border("#")." %85s %11.2f %2s ".border("|")."\n",
              "", $cust_lbr_sale, ""));
        push (@footer_section, sprintf(border("#")." %85s %11.2f %2s ".border("|")."\n",
              "", $cust_prt_sale, ""));
        push (@footer_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@footer_section, sprintf(border("#")." %85s %11.2f %2s ".border("|")."\n",
              "", $cust_gog_sale, ""));
        push (@footer_section, sprintf(border("#")." %85s %11.2f %2s ".border("|")."\n",
              "", $cust_sublet_sale, ""));
        push (@footer_section, sprintf(border("#")." %85s %11.2f %2s ".border("|")."\n",
              "", $cust_misc_sale + $cust_ded_sale, ""));
        push (@footer_section, sprintf(border("#")." %85s %11.2f %2s ".border("|")."\n",
              "", $cust_lbr_sale + $cust_prt_sale + $cust_gog_sale + $cust_sublet_sale + $cust_misc_sale + $cust_ded_sale, ""));
        push (@footer_section, sprintf(border("#")." %85s %11.2f %2s ".border("|")."\n",
              "", 0, ""));
        push (@footer_section, sprintf(border("#")." %85s %11.2f %2s ".border("|")."\n",
              "", $cust_ro_tax, ""));
        push (@footer_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
        push (@footer_section, sprintf(border("#")." %85s %11.2f %2s ".border("|")."\n",
              "", $cust_lbr_sale + $cust_prt_sale + $cust_gog_sale + $cust_sublet_sale + $cust_misc_sale + $cust_ded_sale + $cust_ro_tax, ""));

    }
    push (@footer_section, sprintf(border("#")." %100s ".border("|")."\n\f", ""));
    return @footer_section;
}

# Subroutine to get the RO TAX amount
sub get_ro_tax_amount {
    my $tax_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ? AND item_type = 'TAX'");
    $tax_qry->execute($ro_number);
    my ( $tax_type, $tax_amount);

    while (my @tax_row = $tax_qry->fetchrow_array) {
        ( $tax_type, $tax_amount) = @tax_row;
        if ($tax_type eq "C"){
            $cust_ro_tax = $tax_amount;
        }elsif ($tax_type eq "W"){
            $warr_ro_tax = $tax_amount;
        }elsif ($tax_type eq "I"){
            $intern_ro_tax = $tax_amount;
        }
    }
}

# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_grand_total_section {
    get_ro_tax_amount();

    push (@grand_total_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@grand_total_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@grand_total_section, sprintf(border("#")." %4sGRAND TOTALS%s ".border("|")."\n", "", "-"x84));
    push (@grand_total_section, sprintf(border(">")." %19s%s%s%s%s%s%s%s ".border("|")."\n", "",
           "-"x10, "CUSTOMER",
           "-"x19, "WARRANTY",
           "-"x19, "INTERNAL",
           "-"x9));

    push (@grand_total_section, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                        "", "COST", "SALE", "COST", "SALE", "COST", "SALE"));

    if ($intern_lbr_cost!=0 || $intern_lbr_sale!=0 || $cust_lbr_cost!=0 || $cust_lbr_sale!=0 || $warr_lbr_cost!=0 || $warr_lbr_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sLABOR         %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n", "",
                                            $cust_lbr_cost, $cust_lbr_sale, $warr_lbr_cost, $warr_lbr_sale, $intern_lbr_cost, $intern_lbr_sale));
    }
    if ($intern_prt_cost!=0 || $intern_prt_sale!=0 || $cust_prt_cost!=0 || $cust_prt_sale!=0 || $warr_prt_cost!=0 || $warr_prt_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sPARTS         %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $cust_prt_cost, $cust_prt_sale, $warr_prt_cost, $warr_prt_sale, $intern_prt_cost, $intern_prt_sale));
    }
    if ($intern_misc_cost!=0 || $intern_misc_sale!=0 || $cust_misc_cost!=0 || $cust_misc_sale!=0 || $warr_misc_cost!=0 || $warr_misc_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sMISC          %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $cust_misc_cost, $cust_misc_sale, $warr_misc_cost, $warr_misc_sale, $intern_misc_cost,
                                            $intern_misc_sale));
    }
    if ($intern_gog_cost!=0 || $intern_gog_sale!=0 || $cust_gog_cost!=0 || $cust_gog_sale!=0 || $warr_gog_cost!=0 || $warr_gog_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sGOG           %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $cust_gog_cost, $cust_gog_sale, $warr_gog_cost, $warr_gog_sale, $intern_gog_cost, $intern_gog_sale));
    }
    if ($intern_sublet_cost!=0 || $intern_sublet_sale!=0 || $cust_sublet_cost!=0 || $cust_sublet_sale!=0 || $warr_sublet_cost!=0 || $warr_sublet_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sSUBLET        %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $cust_sublet_cost, $cust_sublet_sale, $warr_sublet_cost, $warr_sublet_sale, $intern_sublet_cost,
                                            $intern_sublet_sale));
    }
    if ($intern_ded_cost!=0 || $intern_ded_sale!=0 || $cust_ded_cost!=0 || $cust_ded_sale!=0 || $warr_ded_cost!=0 || $warr_ded_sale!=0){
        push (@grand_total_section, sprintf(border(">")." %6sDEDUCTIBLE    %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", $cust_ded_cost, $cust_ded_sale, $warr_ded_cost, $warr_ded_sale, $intern_ded_cost, $intern_ded_sale));
    }
    if ($cust_ro_tax > 0 || $warr_ro_tax > 0 || $intern_ro_tax > 0){
        push (@grand_total_section, sprintf(border(">")." %6sTAX           %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n",
                                            "", 0, $cust_ro_tax, 0, $warr_ro_tax, 0, $intern_ro_tax));
    }

    push (@grand_total_section, sprintf(border("#")." %19s %11s  %11s   %11s  %11s   %11s  %11s   ".border("|")."\n",
                                        "", "-"x11, "-"x11, "-"x11, "-"x11, "-"x11, "-"x11));
    push (@grand_total_section, sprintf(border(">")." %6sTOTAL         %11.2f  %11.2f   %11.2f  %11.2f   %11.2f  %11.2f   ".border("|")."\n", "",
                                        $cust_lbr_cost+$cust_prt_cost+$cust_misc_cost+$cust_gog_cost+$cust_sublet_cost+$cust_ded_cost,
                                        $cust_lbr_sale+$cust_prt_sale+$cust_misc_sale+$cust_gog_sale+$cust_sublet_sale+$cust_ded_sale+$cust_ro_tax,
                                        $warr_lbr_cost+$warr_prt_cost+$warr_misc_cost+$warr_gog_cost+$warr_sublet_cost+$warr_ded_cost,
                                        $warr_lbr_sale+$warr_prt_sale+$warr_misc_sale+$warr_gog_sale+$warr_sublet_sale+$warr_ded_sale+$warr_ro_tax,
                                        $intern_lbr_cost+$intern_prt_cost+$intern_misc_cost+$intern_gog_cost+$intern_sublet_cost+$intern_ded_cost,
                                        $intern_lbr_sale+$intern_prt_sale+$intern_misc_sale+$intern_gog_sale+$intern_sublet_sale+$intern_ded_sale+$intern_ro_tax));
}

# Subroutine to calculate the total no of pages in the invoice
# Update page count calculation section, if any modification is made in the body of the report
sub calculate_page_count {
    my $page_count = 1;
    my $page_height = 0;
    # Add page count calculation of job section
    for my $job(@job_section){
        if(scalar(@$job) <= ($page_max_height - $page_height)){
            $page_height = $page_height + scalar(@$job);
        } else {
            $page_count = $page_count + 1;
            $page_height = scalar(@$job);
        }
    }

    # Add page count calculation of grand total section
    if(scalar(@grand_total_section) <= ($page_max_height - $page_height)){
        $page_height = $page_height + scalar(@grand_total_section);
    } else {
        $page_count = $page_count + 1;
        $page_height = scalar(@grand_total_section);
    }

    #Add page count calculation of 'end of invoice'
    if((scalar(@end_of_invoice_section) + $invoice_note_lines) > ($page_max_height - $page_height)){
        $page_count = $page_count + 1;
    }
    return $page_count;
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")." %100s ".border("|")."\n", "");
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
 sub print_header_section {
    my ($page_num) = @_;
    my %hash = (pg_num => $page_num );
    foreach (@header_section)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
 }

# Subroutine for pagination
sub paginate_and_print_segment {
    my (@section_array) = @_;
    # Print section that fit in the current page
    if(scalar(@section_array) <= ($page_max_height - $curr_page_height)){
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    elsif(scalar(@section_array) > $page_max_height){
        for my $sub_section(@section_array){
            if($curr_page_height < $page_max_height)
            {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            } else{
                print FILE get_footer(0, $curr_page_num);
                $curr_page_num = $curr_page_num + 1;
                print_header_section ($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
            }
        }
    }
    # Print section that does not fit in the current page
    else{
        print_blank_line($page_max_height - $curr_page_height);
        print FILE get_footer(0, $curr_page_num);
        $curr_page_num = $curr_page_num + 1;
        print_header_section ($curr_page_num);
        print FILE @section_array;
        $curr_page_height = scalar(@section_array);
    }
}

# Subroutine to prepare end of invoice section
sub make_end_of_invoice_section {
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %4s%-31s%s%32s ".border("|")."\n",
           "",
           "*"x26,
           "A L L  D E T A I L  I N V O I C E",
           "*"x27));
}

# Subroutine to prepare invoice note section
sub make_invoice_note_section {
    for (my $i = 0; $i < $invoice_note_lines; $i++) {
        push (@invoice_note_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
    }
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
    ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

    ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

    ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

    ($sale_amount, $parts_cost, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

    ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;

    $curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @job_section;
    undef @grand_total_section;
    undef @end_of_invoice_section;
    undef @invoice_note_section;
    ########## Prepare invoice ##########
    make_header();
    make_job_section();
    make_grand_total_section();
    make_end_of_invoice_section();
    make_invoice_note_section();
    $pages = calculate_page_count();

    ########## Print invoice ##########
    print_header_section ($curr_page_num);

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }

    # Pagination of Grand total section
    paginate_and_print_segment(@grand_total_section);

    # Pagination of invoice end section
    paginate_and_print_segment(@end_of_invoice_section);

    # End Of Invoice Note
    paginate_and_print_segment(@invoice_note_section);

    # End of invoice footer
    if($curr_page_height == $page_max_height){
        print FILE get_footer(1, $curr_page_num);
    } else {
        print_blank_line($page_max_height - $curr_page_height);
        print FILE get_footer(1, $curr_page_num);
    }
}

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}
