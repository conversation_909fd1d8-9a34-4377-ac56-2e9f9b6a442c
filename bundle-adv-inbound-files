#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash


DIR_ETI="${DU_ETL_ETI_DIR_ADV:?ETI Directory Specification Required}"
[[ -d "$DIR_ETI" ]] || die "ETI Directory Must Exist @ $DIR_ETI"

DIR_IN="${DU_ETL_BASEDIR_ADV}/adv-in"
DIR_PENDING="${DU_ETL_BASEDIR_ADV}/adv-pending"
DIR_ERR="${DU_ETL_BASEDIR_ADV}/adv-err"

function rmlock() {
    if [[ $? != "0" ]]; then
        rm -f "${DIR_IN}/.lock"
    fi
}
trap rmlock exit

function email() {
    local SUBJECT="$1"
    local BODY="$2"

    if [[ ! "$SEND_TO_ETL_RAWDATA_NOTIFY" = '' ]]; then
        if type send-email >/dev/null 2>&1; then
            send-email --to="$SEND_TO_ETL_RAWDATA_NOTIFY" \
                       --subject "$SUBJECT" \
                       --body "$BODY" \
                       --send
        fi
    fi
}

function get_filename() {
    if [[ "$1" =~ /([^/_]+)_(OPENRO|SV)_([0-9]{8})_([0-9]+.*) ]]; then
        FILE_PREFIX="${BASH_REMATCH[1]}"
        STORE=$(echo "${FILE_PREFIX}" | tr [:punct:] _| tr [:blank:] _)
        FILE_TYPE=${BASH_REMATCH[2]}
        TIMESTAMP=${BASH_REMATCH[3]}
        FILE_SUFFIX=${BASH_REMATCH[4]}
        NEW_FILE="${STORE}_${FILE_TYPE}_${TIMESTAMP}_${FILE_SUFFIX}"
        ZIP_FILE=${STORE}-${TIMESTAMP}.zip
    else
        return 1
    fi  
}

if [[ -e "${DIR_IN}/.lock" ]]; then
    exit 0
fi

touch "${DIR_IN}/.lock"
while true
do
    FILE="$(find ${DIR_IN} -maxdepth 1 -regextype 'posix-extended' -regex '.*(SV|OPENRO).*' -print \
        | sort --reverse | head -n 1)"

    if [[ -z "${FILE}" ]]; then
        break
    fi

    if ! get_filename "${FILE}"; then
        mv "${FILE}" "${DIR_ERR}"
        email "Authenticom error" "Invalid file name: $FILE"
        continue
    fi

    PENDING="$(find ${DIR_PENDING} -maxdepth 1  -name ${STORE}* -print | head -n 1)"

    mv "${FILE}" "${DIR_PENDING}/${NEW_FILE}"

    if [[ -n "${PENDING}" ]]; then
        zip -vjm "${DIR_ETI}"/${ZIP_FILE} "${DIR_PENDING}"/${STORE}*
        email "Authenticom file available for store $STORE" "File: $ZIP_FILE"
    fi
done

while true
do
    PENDING="$(find ${DIR_PENDING} -maxdepth 1  -type f -mmin +60 -print | head -n 1)"
    
    if [[ -z "${PENDING}" ]]; then
        break
    else
        mv "${PENDING}" "${DIR_ERR}"
        email "Authenticom error" "Paired file not found for file $PENDING"
    fi
done

rm -f "${DIR_IN}/.lock"

exit
