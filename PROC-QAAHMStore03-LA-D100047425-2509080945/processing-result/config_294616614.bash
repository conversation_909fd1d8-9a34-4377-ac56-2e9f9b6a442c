# Bash Source Test Config File

export DMS_BASE='CDKFortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-CDKFortellis/CDKFortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_CDKFortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: CDKFortellis
#Base DMS: CDKFortellis (optional)

DMS="CDKFortellis"
DATE_PART='202509'

GROUP_CODE="CACT01"
GROUPNAME="CACT02"
STORENAME="CACT04"
STORE_PART="CACT03"
MFG="KIA"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

SOURCE_COMPANY_ID="*********"
COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA_CDK-ACT Import Project: Parts R1"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'