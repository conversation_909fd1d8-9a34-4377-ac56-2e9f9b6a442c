# Bash Source Test Config File

export DMS_BASE='Fortellis'
source "$DU_ETL_HOME/DU-DMS/DMS-Fortellis/Fortellis.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_Fortellis}"
PROMPT_FOR_FILE='true'

#ETL  DMS: Fortellis
#Base DMS: Fortellis (optional)

DMS="Fortellis"
DATE_PART='202508'

GROUP_CODE="QAAMF02"
GROUPNAME="QAAMF01"
STORENAME="QAAMF15"
STORE_PART="QAAMF16"
MFG="KIA"
STATE='LA'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QA AHM-FORT Bundle: Parts R4"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE="Labor UL"
SECONDARY_PROJECT_NAME="QA AHM-FORT Bundle: Labor R4"
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="fo20250826085303165360"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAAMF01-QAAMF16-LA-INITIAL-D100047425-20250826085023.zip"