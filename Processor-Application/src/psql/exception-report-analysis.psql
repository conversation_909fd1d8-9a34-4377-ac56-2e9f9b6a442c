BEGIN;
\pset pager off
SET client_min_messages TO WARNING;
CREATE TEMPORARY TABLE exception_analysis (
    exception_type    text,
    invoicenumber     text,
    ro_data           json
);

\copy exception_analysis FROM 'All_exception_details.csv' WITH (FORMAT csv, HEADER true)

INSERT INTO schedule_detail
(scheduler_id, total_ros, created_by, dms)
VALUES(
    :'uuid', 
    :'total_ro_count',
    :'performed_by',
    :'dms');

INSERT INTO schedule_exceptions
(scheduler_id, exception_key, ro_number, additional_info, created_by)
SELECT
    :'uuid',
    exception_type,
    invoicenumber,
    ro_data,
    :'performed_by'
    FROM exception_analysis;

COMMIT;