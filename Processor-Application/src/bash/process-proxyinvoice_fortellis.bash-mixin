# source into process-json

function generate_proxies_from_zip() {
    progress "Generating Proxies from Post-Processed Zip File"

    [[ -f "$POST_PROCESSED_ZIP_FILE" ]] || die "Input Zip File Must Exist"
    local local_work_dir="$WORK_DIR"/post-process-proxies
    clear_dir "$local_work_dir"
    unzip "$POST_PROCESSED_ZIP_FILE" -d "$local_work_dir"
    restore_dump_file_to_database "$local_work_dir"/process-json-csv-results.pgdump
    build_proxy_repair_orders "$local_work_dir"
    compress_proxy_output "$local_work_dir"
    prompt_continuation "Review and Continue"
}

function restore_dump_file_to_database() {
    function psql_local() {
        psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA},${SRC_SCHEMA}_model'" --set=ON_ERROR_STOP=1 "$@"
        return $?
    }
    psql_local --quiet <<SQL
        SET client_min_messages = 'WARNING';
        DROP SCHEMA IF EXISTS ${SRC_SCHEMA}_model CASCADE;
        CREATE SCHEMA ${SRC_SCHEMA}_model;
SQL
    progress "Restoring $1"
    pg_restore --verbose -d "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}_model'" "$1"
}

function build_proxy_repair_orders() {
    say "Generating Proxy Repair Orders per Request"
    PROXY_SCHEMA="${SRC_SCHEMA}"_proxy
    SRC_PROXY_SCHEMA="${SRC_SCHEMA}"_model

    (
        cd "$DU_ETL_HOME"/DU-ProxyInvoice
        "$DU_ETL_HOME"/DU-ProxyInvoice/initialize-schema "$PROXY_SCHEMA"
    ) || die "Initialization Failed"

    (
        cd "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis
        psql_etl_proxy --quiet --file ./src/parse/Fortellis-proxy-schema-table-alter.psql
        psql_etl_proxy --quiet --file ./src/parse/populate-Fortellis-proxy-invoice.psql
    ) || die "Failed to Populate Fortellis Proxy Invoice"

    (
        mkdir -p "$1"/proxy-invoice
        TEMPLATE_BACKGROUND_ARG=
        TEMPLATE_TYPE=
        if [[ -f "$1"/proxy-background.pdf ]]; then
            TEMPLATE_BACKGROUND_ARG="$1"/proxy-background.pdf
            TEMPLATE_TYPE='invoice_mimic'
        else
            TEMPLATE_BACKGROUND_ARG='none'
            TEMPLATE_TYPE='invoice_mimic'
        fi
        cd "$DU_ETL_HOME"/DU-ProxyInvoice
        "$DU_ETL_HOME"/DU-ProxyInvoice/generate-fixed-width-proxy-invoice-Fortellis.bash \
            "$PROXY_SCHEMA" \
            "$1"/proxy-invoice \
            'true' "$TEMPLATE_TYPE" \
            "$TEMPLATE_BACKGROUND_ARG"
            #'true' 'merge_with_template'
            #'true' 'invoice_mimic'

    ) || die "Proxy invoice generation Failed"
}

function compress_proxy_output() {
(

    base_output_dir="$1"/proxy-invoice
        progress "compress $base_output_dir"

    txt_output_dir="$base_output_dir"/text
    pdf_output_dir="$base_output_dir"/pdf
    bundle_output_dir="$base_output_dir"/bundle

    cd "$base_output_dir"
echo $base_output_dir
    if [[ -d "$txt_output_dir" ]]; then
        zip -j -qm Proxy-Invoices-TXT.zip "$txt_output_dir"/*
        rmdir "$txt_output_dir"
    fi

    if [[ -d "$pdf_output_dir" ]]; then
        zip -j -qm Proxy-Invoices-PDF.zip "$pdf_output_dir"/*
        rmdir "$pdf_output_dir"
    fi

    if [[ -d "$bundle_output_dir" ]]; then
        zip -j -qm Proxy-Invoices-Bundles.zip "$bundle_output_dir"/*
        rmdir "$bundle_output_dir"
    fi

)
}
