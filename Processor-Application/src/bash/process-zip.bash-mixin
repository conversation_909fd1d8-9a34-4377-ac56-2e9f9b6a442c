# Sourced by process-json; provides processing implementation functions for iterating
# through the provided zip file.

NAME_JSONCONV=jsonconversions
NAME_EXT_ARC=extraction-archive
NAME_PROC_LOG=processing-log
NAME_EXTR_LOG=extraction-log
NAME_PROC_RST=processing-result
NAME_IMPORT_FILES=import-files

WORK_DIR_EXTRACTION_LOG_DIR="$ZIP_WORK_DIR"/"$NAME_EXTR_LOG"
WORK_DIR_EXTRACTION_ARCHIVE_DIR="$ZIP_WORK_DIR"/"$NAME_EXT_ARC"
WORK_DIR_PROCESSING_LOG_DIR="$ZIP_WORK_DIR"/"$NAME_PROC_LOG"
WORK_DIR_PROCESSING_RESULTS_DIR="$ZIP_WORK_DIR"/"$NAME_PROC_RST"
WORK_DIR_DEAD_LETTER="$ZIP_WORK_DIR"/dead-letter
WORK_DIR_IMPORT_FILES_DIR="$ZIP_WORK_DIR"/"$NAME_IMPORT_FILES"

EXCEPTION_TABLES_DIR='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception_tables'

CORE_RETURN_EXCEPTION_CSV_FILE_PATH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/corereturn_without_corecharge.csv'
CORE_CHARGE_EXCEPTION_CSV_FILE_PATH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/corecharge_without_corereturn.csv'
CORE_RETURN_NOT_EQUAL_TO_CORE_CHARGE_EXCEPTION_CSV_FILE_PATH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/corecharge_corereturn_mismatch.csv'
CORE_CHARGE_WITH_NO_SALE='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/corecharge_with_nosale.csv'
INVALID_CORE_COST_SALE_MISMATCH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/invalid_core_cost_sale_mismatch.csv'
INVALID_CORE_AMOUNT_MISMATCH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/invalid_core_amount_mismatch.csv'
GL_MISSING_RO_LIST='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/gl_missing_ro_list.csv'
PART_DETAILS_NULL_EXCEPTION_FILEPATH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/part_details_null.csv'

source "$DU_ETL_HOME"/config-generation/process.bash
source "$DU_ETL_HOME"/scheduler_halt/scheduler_halt.sh
INPUT_TYPE="json"
SAVE_FILE=false

function process_zip_file() {
    echo "Processor status: Processing Started"
    sleep 1
    JSON_CONVERSION_WORK_DIR="$ZIP_WORK_DIR"/"$NAME_JSONCONV"
    SINGLE_RO_JSON_DIR="$JSON_CONVERSION_WORK_DIR"/single-ro-json

    echo "HALT_OVER_RIDE:${HALT_OVER_RIDE}"

    [[ -f "$INPUT_BUNDLE_ZIP" ]] || die "Zip file required for zip mode: $INPUT_BUNDLE_ZIP"
    say "Processing Input Zip Archive: $INPUT_BUNDLE_ZIP"
    echo "Processor status: 1/25 Unzipping Input to Work Started"
    sleep 1
    prepAndUnzipGlobalInputZipFile
    echo "Processor status: 1/25 Unzipping Input to Work Completed"
    sleep 1
    echo "Processor status: 2/25 Creating Schema from Model Started"
    sleep 1
    create_schema_from_model                              || die "Could not initialize schema"
    echo "Processor status: 2/25 Creating Schema from Model Completed"
    sleep 1
    save_scheduler_id                                     || die "Could not Save UUID"

    generate_jq_transforms                                || die "Failed to create jq transforms"
    echo "Processor status: 3/25 Iterating Over Zip File Contents Started"
    sleep 1
    processZipFileContents                                || die "Content Processing Failed"
    echo "Processor status: 3/25 Iterating Over Zip File Contents Completed"    
    sleep 1

    echo "Processor status: 4/25 Loading Individual ROs Started"
    sleep 1
    load_individual_json_files                            || die "Could not load at least one json repair order"
    echo "Processor status: 4/25 Loading Individual ROs Completed"
    sleep 1

    echo "Processor status: 5/25 Detecting Problematic ROs Started"
    sleep 1 
    isolate_problematic_ros                               || die "Problematic RO isolation failed"
    echo "Processor status: 5/25 Detecting Problematic ROs Completed"
    sleep 1

    if [[ "$SCHEDULER_DB" = 'true' ]]; then
        report_excluded_results                           || die "Excluded report generation failed"
    else
        dummy_report_excluded_results                     || die "Dummy Excluded report generation failed"
    fi
 
   enumerate_present_ro                                  || die "RO Number Enumeration Failed"

   persist_and_export_data_and_schema "$ZIP_WORK_DIR"    || die "Persistence and exporting failed"

   export_job_data_to_csv                                || die "Exporting job data Failed"
    echo "Processor status: 6/25 Detecting Open/Void RO data and reporting Started"
    sleep 1
   report_open_or_void_ros                               || die "Open/Void RO report generation failed"
    echo "Processor status: 6/25 Detecting Open/Void RO data and reporting Completed"
    sleep 1

    echo "Processor status: 7/25 Generating report of all customer paytypes Started"
    sleep 1
   generate_report_customer_pay_types                    || die "Customer paytype report generation failed"    
    echo "Processor status: 7/25 Generating report of all customer paytypes Completed"
    sleep 1
    echo "Processor status: 8/25 Generating report of total discount Started"
    sleep 1 
   generate_report_total_discount                        || die "Total discount report generation failed"
    echo "Processor status: 8/25 Generating report of total discount Completed"
    sleep 1
    echo "Processor status: 9/25 Generating exception Report Started"
    sleep 1
   # generate_exception_report                             || die "Exception report generation failed"
    echo "Processor status: 9/25 Generating exception Report Completed"
    sleep 1
   generate_core_exception_report                      || die "Exception core report generation failed"
    echo "Processor status: 10/25 Checking for Missing ROs in Original Raw Data Started"
    sleep 1
   check_missing_against_extraction                      || die "Failed to check missing against original extraction"
    echo "Processor status: 10/25 Checking for Missing ROs in Original Raw Data Completed"
    sleep 1
    echo "Processor status: 11/25 Generating scheduler id Started"
    sleep 1
    generate_scheduler_id_file                            || die "Scheduler id file generation failed"
    echo "Processor status: 11/25 Generating scheduler id Completed"
    sleep 1
  if [[ "$EXCEPTION_ANALYSIS" = 'true' ]]; then
        echo "Processor status: 12/25 Generate GL report for Analysis Started"
        sleep 1
        generate_gl_report_for_analysis                   || die "Analysis GL report generation failed"
        echo "Processor status: 12/25 Generate GL report for Analysis Completed"
        sleep 1
        echo "Processor status: 13/25 Generating Customerpay Report Started"
        sleep 1
        generate_customerpay_report                       || die "Customer pay generation failed"
        echo "Processor status: 13/25 Generating Customerpay Report Completed"
        sleep 1
        echo "Processor status: 14/25 Copy tables Started"
        sleep 1
       # copy_analysis_table                               || die "analysis table generation failed"
        echo "Processor status: 14/25 Copy tables Completed"
        sleep 1                             || die "analysis table generation failed"
  fi
    if [[ "$SOLVE_DB" = 'true' ]]; then
    echo "Processor status: 15/25 Generate Config File Started"
    sleep 1
        if [[ "$COMPANY_IDS" ]]; then
            IFS=',' read -ra ALL_COMPANY_IDS <<< "$COMPANY_IDS"
            for COMPANY_ID in "${ALL_COMPANY_IDS[@]}"; do
                echo "COMPANY_ID: $COMPANY_ID"
                if [[ "$COMPANY_ID" -eq 0 ]]; then
                    SAVE_FILE=true
                    break
                fi
            done
            if [[ "$SAVE_FILE" = true ]]; then
                generate_mock_config_file
            else
                generate_config_file
            fi
        fi
    else
        generate_mock_config_file
    fi
    echo "Processor status: 15/25 Generate Config File Completed"
    sleep 1
    echo "Processor status: 16/25 Load From Scheduler DB Started"
    sleep 1
    load_data_from_scheduler_database                     || die "Load to scheduler database Failed"
    echo "Processor status: 16/25 Load From Scheduler DB Completed"
    sleep 1

    echo "Processor status: 17/25 Compressing Directory Started"
    sleep 1
   compress_extraction_directory
   compress_jsonconversion_directory
   compress_extractionlog_directory
   compress_processinglog_directory
   compress_or_remove_deadletter_directory
   compress_import_files_directory
    echo "Processor status: 17/25 Compressing Directory Completed"
    sleep 1

   if [[ "$PERFORM_PROXY_RO_BUILD" = 'true' ]]; then
        echo "Processor status: 18/25 Generating Proxy Repair Orders per Request Started"
        sleep 1
       build_proxy_repair_orders "$ZIP_WORK_DIR" "$SHOW_ACCOUNTING_IN_PROXY" "$PORSCHE_STORE" "$DUAL_PROXY" "$BRAND" "$STATE"
        echo "Processor status: 18/25 Generating Proxy Repair Orders per Request Completed"
        sleep 1
        echo "Processor status: 19/25 Extracting Text ROs to TSV Started"
        sleep 1
       extract_proxy_to_tsv "$ZIP_WORK_DIR"/proxy-invoice/text "$WORK_DIR_PROCESSING_RESULTS_DIR" || echo "Proxy extraction to tsv failed"
        echo "Processor status: 19/25 Extracting Text ROs to TSV Completed"
        sleep 1
    if [[ "$EXCEPTION_REPORT" = 'true' ]]; then
        echo "Processor status: 20/25 Generate Exception Analysis Started"
        sleep 1
       # exception_report_analysis "$WORK_DIR_PROCESSING_RESULTS_DIR" "$WORK_DIR_PROCESSING_RESULTS_DIR" || echo "Exception report analysis generation failed"
        echo "Processor status: 20/25 Generate Exception Analysis Completed"
        sleep 1
        echo "Processor status: 21/25 Loading Exception Analysis Started"
        sleep 1
        #load_exception_analysis_report                              || die "Exception analysis report loading failed"
        echo "Processor status: 21/25 Loading Exception Analysis Completed"
        sleep 1
    fi
        echo "Processor status: 22/25 Compressing Proxy Directory Started"
        sleep 1
       compress_proxy_output "$ZIP_WORK_DIR"
       compress_dual_proxy_output "$ZIP_WORK_DIR"
        echo "Processor status: 22/25 Compressing Directory Completed"
        sleep 1
   fi

   if [[ "$EXCEPTION_ANALYSIS" = 'true' ]]; then
        echo "Processor status: 23/25 Generate Report Analysis Started"
        sleep 1
       generate_analysis_report                              || die "analysis report generation failed"
        echo "Processor status: 23/25 Generate Report Analysis Completed"
        sleep 1    
   fi
    echo "Processor status: 24/25 Pre-import Halt Detection Started"
    sleep 1
    pre_import_halt_detection  || die "Pre-import halt detection failed"
    echo "Processor status: 24/25 Pre-import Halt Detection Completed"
    sleep 1
    alter_schema "$INPUT_TYPE" || die "Altering schema failed"
    save_scheduler_details || die "Saving scheduler details failed"   
    echo "Processor status: 25/25 Moving Work to Bundle Directory Started"
    sleep 1
   distribute_and_clear_work
    echo "Processor status: 25/25 Moving Work to Bundle Directory Completed"
    sleep 1
    echo "Processor status: Processing Completed"
    sleep 1
}

function save_scheduler_id() {     
progress "Saving UUID to model"
    psql_local --quiet \
               --set=UUID="$UUID" \
               >/dev/null \
<<'SQL'
CREATE TABLE etl_uuid_detail (
    unique_id       text NOT NULL
);
INSERT INTO etl_uuid_detail(unique_id) VALUES(:'UUID');
SQL

}

function generate_scheduler_id_file(){
    cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
    echo "${UUID}" >> "scheduler-id.txt"
}

function processZipFileContents() {
    progress "Iterating Over Zip File Contents"
    export DU_ETL_DEBUG_TARGET="$WORK_DIR_PROCESSING_LOG_DIR"/Processing.log
    (
        cd "$ZIP_WORK_DIR"
        mkdir "$WORK_DIR_EXTRACTION_LOG_DIR"
        mkdir "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"
        mkdir "$WORK_DIR_PROCESSING_LOG_DIR"
        mkdir "$WORK_DIR_PROCESSING_RESULTS_DIR"
        mkdir "$WORK_DIR_DEAD_LETTER"

        for filename in ./*; do
            case "$(basename $filename)" in
                extraction-log)     : ;;
                extraction-archive) : ;;
                processing-log)     : ;;
                processing-result)  : ;;
                dead-letter)        : ;;                
                Customers.json)  relocate_to_extraction_dir                         "$filename" ;;
                Customers.time)  relocate_to_log_dir                                "$filename" ;;
                Customers.req)   relocate_to_log_dir                                "$filename" ;;
                Customers.res)   relocate_to_log_dir                                "$filename" ;;
                
                Employees.json)  relocate_to_extraction_dir                         "$filename" ;;
                Employees.time)  relocate_to_log_dir                                "$filename" ;;
                Employees.req)   relocate_to_log_dir                                "$filename" ;;
                Employees.res)   relocate_to_log_dir                                "$filename" ;;

                HelpModels.json)  relocate_to_extraction_dir                        "$filename" ;;
                HelpModels.time)  relocate_to_log_dir                               "$filename" ;;
                HelpModels.req)   relocate_to_log_dir                               "$filename" ;;
                HelpModels.res)   relocate_to_log_dir                               "$filename" ;;

                WipRO-Open.json)  relocate_to_extraction_dir                        "$filename" ;;
                WipRO-Open.time)  relocate_to_log_dir                               "$filename" ;;
                WipRO-Open.req)   relocate_to_log_dir                               "$filename" ;;
                WipRO-Open.res)   relocate_to_log_dir                               "$filename" ;;

                WipRO-Closed.json)  relocate_to_extraction_dir                      "$filename" ;;
                WipRO-Closed.time)  relocate_to_log_dir                             "$filename" ;;
                WipRO-Closed.req)   relocate_to_log_dir                             "$filename" ;;
                WipRO-Closed.res)   relocate_to_log_dir                             "$filename" ;;
                
                LaborType.json)  process_labor_type_json                            "$filename" ;;
                LaborType.time)  relocate_to_log_dir                                "$filename" ;;
                LaborType.req)   relocate_to_log_dir                                "$filename" ;;
                LaborType.res)   relocate_to_log_dir                                "$filename" ;;

                AccountingCOA*.json)  process_accounting_coa_json                  "$filename" ;;
                AccountingCOA*.time)  relocate_to_log_dir                          "$filename" ;;
                AccountingCOA*.req)   relocate_to_log_dir                          "$filename" ;;
                AccountingCOA*.res)   relocate_to_log_dir                          "$filename" ;;
                
                AccountingGLDetails_*.json)  process_accounting_details_json        "$filename" ;;
                AccountingGLDetails_*.time)  relocate_to_log_dir                    "$filename" ;;
                AccountingGLDetails_*.req)   relocate_to_log_dir                    "$filename" ;;
                AccountingGLDetails_*.res)   relocate_to_log_dir                    "$filename" ;;

                ROs_*.json)      process_closed_ro_json                             "$filename" ;;
                ROs_*.time)      relocate_to_log_dir                                "$filename" ;;
                ROs_*.req)       relocate_to_log_dir                                "$filename" ;;
                ROs_*.res)       relocate_to_log_dir                                "$filename" ;;
                OpenRO.json)     process_closed_ro_json                             "$filename" ;;
                OpenRO.time)     relocate_to_log_dir                                "$filename" ;;
                OpenRO.req)      relocate_to_log_dir                                "$filename" ;;
                OpenRO.res)      relocate_to_log_dir                                "$filename" ;;

                *.err)             	relocate_to_log_dir_and_log_error_die           "$filename" ;;
                *)                  yell "File $filename Not Recognized"                        ;;
            esac
            proc_result=$?
            if [[ "$proc_result" != 0 ]]; then
                die "File Processing Failed"
            fi
        done
    ) || die "Zip File Processing Failed"
    export DU_ETL_DEBUG_TARGET=/dev/null
    return $?
}

function relocate_to_log_dir() {
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
}

function relocate_to_extraction_dir() {
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
}

function relocate_to_log_dir_and_log_error_die(){
    echo "$1 Error content is"
    head -20 $1
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
    die  "Error File Present: $1"
}

function prepAndUnzipGlobalInputZipFile() {
    progress "Unzipping Input to Work"
    mkdir -p "$ZIP_WORK_DIR"
    clear_dir "$ZIP_WORK_DIR"
    # junk the input directories since none are present for the initial import
    # and re-processing the extraction archive is much simpler if the embedded directory
    # that is part of the zip file be handled transparently.
    unzip -j -q "$INPUT_BUNDLE_ZIP" -d "$ZIP_WORK_DIR"
}

function compress_extraction_directory() {
(
    cd "$ZIP_WORK_DIR"
    zip -q -mr "$NAME_EXT_ARC".zip ./"$NAME_EXT_ARC"
)
}

function compress_jsonconversion_directory() {
(
    if [[ -d "$JSON_CONVERSION_WORK_DIR" ]]; then
        cd "$ZIP_WORK_DIR"
        zip -q -mr "$NAME_JSONCONV".zip ./"$NAME_JSONCONV"
    fi
)
}

function compress_processinglog_directory() {
(
    cd "$ZIP_WORK_DIR"
    zip -q -mr "$NAME_PROC_LOG".zip ./"$NAME_PROC_LOG"
)
}

function compress_extractionlog_directory() {
(
    cd "$ZIP_WORK_DIR"
    zip -q -mr "$NAME_EXTR_LOG".zip ./"$NAME_EXTR_LOG"
)
}

function compress_or_remove_deadletter_directory() {
(
    if has_files "$WORK_DIR_DEAD_LETTER"; then
        cd "$ZIP_WORK_DIR"
        zip -q -mr 'dead-letter.zip' ./'dead-letter'
    else
        rmdir "$WORK_DIR_DEAD_LETTER"
    fi
)
}

function compress_import_files_directory() {
(
    cd "$ZIP_WORK_DIR"
    zip -q -jmr "$NAME_IMPORT_FILES".zip ./"$NAME_IMPORT_FILES"
    rmdir ./"$NAME_IMPORT_FILES"
)
}

function distribute_and_clear_work() {
    progress "Moving Work to Bundle Directory $PERFORM_ZIP" 
    if [[ "$PERFORM_ZIP" = 'true' ]]; then
        distribute_as_zip_file
    else
        distribute_as_directory
    fi
    progress "Distribute Function Result: $?"
    clear_dir "$ZIP_WORK_DIR"
}

function distribute_as_zip_file() {
(
    cd "$ZIP_WORK_DIR"
    ls
    echo "***************1***********************"
    zip -qr "$WORK_DIR"/"$INPUT_ZIP_FILE_NAME" ./*
        echo "*****************2*********************$WORK_DIR/$INPUT_ZIP_FILE_NAME"
        echo "------BUNDLE_OUTPUT_DIRECTORY-----------$BUNDLE_OUTPUT_DIRECTORY"
        echo "------ZIP_WORK_DIR-----------$ZIP_WORK_DIR"
ls "$WORK_DIR"/"$INPUT_ZIP_FILE_NAME"
    mv "$WORK_DIR"/"$INPUT_ZIP_FILE_NAME" "${BUNDLE_OUTPUT_DIRECTORY:?Specify bundle directory first}"/"${OUTPUT_FILE_PREFIX}""$(basename $INPUT_ZIP_FILE_NAME)"
)
return $?
}

function distribute_as_directory() {
(
    cd "$ZIP_WORK_DIR"/..
    mv ./"$(basename $ZIP_WORK_DIR)" \
       "${BUNDLE_OUTPUT_DIRECTORY:?Specify bundle directory first}"/"$(basename $INPUT_ZIP_FILE_NAME .zip)"
)
return $?
}

function process_session_log() {
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
}

function move_to_work_dead_letter() {
    mkdir -p "$WORK_DIR_DEAD_LETTER"
    mv "$1" "$WORK_DIR_DEAD_LETTER"
}

function enumerate_present_ro() {
    progress "Enumerating ROs"
    (
        cd "$SINGLE_RO_JSON_DIR"
        find . -type f -exec basename {} .json \; | sort > "$WORK_DIR_PROCESSING_RESULTS_DIR"/single-ro-json-present-list.txt
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;
        DROP TABLE IF EXISTS present_repair_order_number;
        DROP TABLE IF EXISTS repair_order_number_summary;
        CREATE TABLE present_repair_order_number (
            invoice_number text primary key
        );
        \copy present_repair_order_number from single-ro-json-present-list.txt with (format csv, header false)
        select count(*) AS count_all, count(*) filter (where invoice_number !~ '^\d+$') AS count_non_numeric
          from present_repair_order_number;

        CREATE UNLOGGED TABLE repair_order_number_summary AS
        WITH
        split_ranges AS (
             SELECT min(invoice_number::int) AS first_invoicenumber, max(invoice_number::int) AS last_invoicenumber
               FROM present_repair_order_number
              WHERE invoice_number ~ '^\d+$'
        ),
        explode_invoice_numbers AS (
              SELECT generate_series(first_invoicenumber, last_invoicenumber, 1) AS invoice_number
                FROM split_ranges
        ),
        present AS (
            SELECT invoice_number::int, true AS is_present
              FROM present_repair_order_number
             WHERE invoice_number ~ '^\d+$'
        ),
        combined AS (
            SELECT *, invoice_number::integer / 100 AS inv_grp FROM explode_invoice_numbers NATURAL LEFT JOIN present
        )
        SELECT
          (sum("Present #") OVER () / sum("Total #") OVER ())::numeric(10,2) AS "Overall %"
        , sum("Present #") OVER () AS "Included #"
        , sum("Total #") OVER () AS "Overall #"
        , ("Present #" / "Total #"::numeric)::numeric(10,2) AS "Tranche %"
        , *
        FROM (
            SELECT
                  sum(CASE WHEN is_present IS TRUE THEN 1 ELSE 0 END) AS "Present #"
                , sum(CASE WHEN is_present IS TRUE THEN 0 ELSE 1 END) AS "Missing #"
                , max(invoice_number) - min(invoice_number) + 1 AS "Total #"
                , min(invoice_number) AS "Starting"
                , max(invoice_number) AS "Ending"
                , array_agg(DISTINCT CASE WHEN is_present IS NOT TRUE
                                          THEN invoice_number
                                          ELSE NULL END) AS "Missing Invoices"
                , inv_grp AS "Inv Grp ID"
            FROM combined
            GROUP BY inv_grp
            ORDER BY inv_grp
        ) overall_calc
        ;

        \o 'repair-order-sequence-non-numeric.txt'
        \C 'Non-Numeric Repair Order Numbers'
        SELECT * FROM present_repair_order_number WHERE invoice_number !~ '^\d+$';

        \o repair-order-sequence-summary.txt
        \C 'Numeric-only Repair Order Number Summary'
        SELECT "Overall %", "Included #", "Overall #",
               "Tranche %", "Present #", "Missing #", "Total #",
               "Starting", "Ending"
          FROM repair_order_number_summary
         WHERE "Present #" > 0;

        \C
        \t
        \pset format unaligned
        \o 'repair-order-sequence-missing-numerics.txt'
        SELECT invoice_number
          FROM (
            SELECT unnest("Missing Invoices") AS invoice_number
              FROM repair_order_number_summary
             WHERE "Present #" > 0
             ORDER BY 1
               ) AS src
         WHERE invoice_number IS NOT NULL;

        \o
SQL
        rm single-ro-json-present-list.txt

        "$DU_ETL_HOME"/invoice-manipulation-common/summarize-ro-list \
            repair-order-sequence-missing-numerics.txt \
            > repair-order-sequence-missing-numerics-summary.txt

    )
}

function check_missing_against_extraction() {
    progress "Checking for Missing ROs in Original Raw Data"
    (
        cd "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"
        grep -o \
             -f "$WORK_DIR_PROCESSING_RESULTS_DIR"/repair-order-sequence-missing-numerics.txt \
             ./* \
             > "$WORK_DIR_PROCESSING_RESULTS_DIR"/missing-lookup-in-extraction.txt
        if [[ $? != 0 ]]; then
            progress "No missing RO#s found in extraction data"
        else
            yell "At least one missing RO# found in extraction data (false positives possible)"
        fi
    )
}

function report_open_or_void_ros() {
    (
        progress "Detecting Open/Void RO data and reporting"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;

        \o 'repair-order-sequence-open-list.txt'
        SELECT "roNumber","openDate", "statusDesc"
          FROM du_dms_fortellis_model.etl_head
         WHERE "closedDate" IS NULL
         ORDER BY "roNumber";

        \o 'repair-order-sequence-closed-by-date-then-ro.txt'
        \t
        \pset format unaligned
        SELECT "roNumber", "closedDate", "openDate", "statusDesc"
          FROM du_dms_fortellis_model.etl_head
         WHERE "closedDate" IS NOT NULL
         ORDER BY "closedDate", "roNumber";

        \o 'repair-order-sequence-closed-by-ro-then-date.txt'
        SELECT "roNumber", "closedDate", "openDate", "statusDesc"
          FROM etl_head
         WHERE "closedDate" IS NOT NULL
         ORDER BY "roNumber";
           
        \o 'invoice-master.csv'
        COPY (
                        WITH inv_master AS (
            SELECT
                substring("roNumber" FROM '^[0-9]+')::integer AS number_part,
                substring("roNumber" FROM '[A-Za-z]+$') AS code_part
            FROM etl_head
                ),
                start_end AS (
                    SELECT
                        min(number_part) AS min_ro,
                        max(number_part) AS max_ro
                    FROM inv_master
                )
                SELECT *
                FROM (
                    SELECT
                        etl_head."roNumber",
                        CASE 
                            WHEN "closedDate" IS NOT NULL THEN 'Closed'
                            ELSE 'Open'
                        END AS "Status",
                        "openDate",
                        "closedDate",
                        ev."year",
                        ev."makeDesc" AS "Make",
                        ev."modelDesc" AS "Model",
                        ev."vin",
                        "customerId" AS "Customer",
                        "serviceAdvisor" AS "Advisor",
                        COALESCE("statusDesc", 'WIP') AS "Sub-Status"
                    FROM etl_head
                    LEFT JOIN etl_vehicle ev ON etl_head."roNumber" = ev."roNumber"
                    UNION ALL
                    SELECT
                        ro_num::text AS "roNumber",
                        'Voided' AS "Status",
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        NULL,
                        NULL
                    FROM start_end, generate_series(min_ro, max_ro) gs (ro_num)
                    WHERE NOT EXISTS (
                        SELECT 1
                        FROM etl_head
                        WHERE substring(etl_head."roNumber" FROM '^[0-9]+')::integer = ro_num
                    )
                ) ua
                ORDER BY substring("roNumber" FROM '^[0-9]+')::integer
        ) TO stdout WITH (FORMAT csv, HEADER true);
     
        \o 'coa-report.csv'
	COPY (
    		SELECT
        	    TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                         'g')) AS account_number,
        	    TRIM(regexp_replace("accountDesc", E'[\\n\t\r]+', ' ',
                         'g')) AS account_description
    		FROM du_dms_fortellis_model.etl_account_coa
	) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

--        \o 'GL-Accounting_report.csv'
--     COPY (
-- 	   WITH coa_details AS (SELECT
--                          TRIM(regexp_replace("AccountNumber" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                              'g')) AS accountnumber,
--                          TRIM(regexp_replace("AccountDescription" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                              'g')) AS accountdescription,
--                          TRIM(regexp_replace("AccountType" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                              'g')) AS accounttype,
--                          TRIM(regexp_replace("IncBalGrpDesc" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                              'g')) AS incbalgrpdesc,
--                          TRIM(regexp_replace("IncBalSubGrp" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                              'g')) AS incbalsubgrp
--                      FROM du_dms_fortellis_model.etl_account_coa
-- 	 ),
--          gl_details AS (SELECT
--                            TRIM(regexp_replace("HostItemID" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS hostitemid,
--                            TRIM(regexp_replace("AccountingDate" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS accountingdate,
--                            TRIM(regexp_replace("AccountNumber" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS accountnumber,
--                            split_part("HostItemID" :: json ->> '_text', '*', 3) AS invoice,
--                            TRIM(regexp_replace("CompanyID" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS companyid,
--                            TRIM(regexp_replace("Control" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS control,
--                            TRIM(regexp_replace("JournalID" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS journalid,
--                            TRIM(regexp_replace("PostingAmount" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS postingamount,
--                            TRIM(regexp_replace("PostingSequence" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS postingsequence,
--                            TRIM(regexp_replace("PostingTime" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS postingtime,
--                            TRIM(regexp_replace("Refer" :: json ->> '_text', E'[\\n\t\r]+', ' ',
--                                                'g'))                            AS refer
--                        FROM du_dms_fortellis_model.etl_account_details
--     	)
-- 	SELECT
--             hostitemid AS "Host Item ID",
--     	    invoice AS "#Invoice",
--     	    accountingdate AS "Accounting Date",
--     	    accountnumber AS "Account Number",
--     	    accountdescription AS "Acccount Description",
--     	    accounttype AS "Account Type",
--     	    incbalgrpdesc AS "IncBalGrpDesc",
--     	    incbalsubgrp AS "IncBalSubGrp",
--     	    companyid AS "Company ID",
--     	    control AS "Control",
--     	    journalid AS "Journal ID",
--     	    postingamount AS "Posting Amount",
--     	    postingsequence AS "Posting Sequence",
--     	    postingtime AS "Posting Time"
--         FROM gl_details
--     		LEFT JOIN coa_details USING (accountnumber)
--  ) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

    \o 'technician_labor_ros.csv'
	COPY (
     SELECT
        "roNumber"                AS "ronumber",
        ARRAY_TO_STRING(ARRAY(SELECT json_array_elements_text("technicianIds"::json)), ', ')               AS "TechNo",
        "type"            AS "PayType",
        "actualHours"::numeric AS "ActHours",
        "soldHours"::numeric   AS "BookHours",
        coalesce(nullif("cost", '')::numeric, 0)        AS "Cost",
        coalesce(nullif(sale, '')::numeric, 0)        AS "Sale"
    FROM etl_labor tech where "lineCode" is NULL
	) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );


      \o 'ro_with_no_sale_cost_in_gl.csv'
	COPY (
        WITH ro AS(
	SELECT DISTINCT
    	REPLACE((d."referenceNo")::text,'"','')              AS "RO#"
    FROM du_dms_fortellis_model.etl_accounts d
    	JOIN du_dms_fortellis_model.etl_account_coa c
            ON (d."accountNumber")::text = (c."accountNumber")::text
    WHERE REPLACE((c."accountType")::text, '"','')  IN ('S','C') 
    ORDER BY REPLACE((d."referenceNo")::text,'"','')
    )
    , ro_all AS(
    	SELECT DISTINCT
    		REPLACE((d."referenceNo")::text,'"','')              AS "RO#"
    	FROM du_dms_fortellis_model.etl_accounts d
    )
    ,ro_without AS(
    	SELECT a."RO#" 
    	FROM ro_all a
    		LEFT JOIN ro r 
    			ON a."RO#"=r."RO#"
    	WHERE r."RO#" IS NULL
    )
    SELECT 
    	ro."RO#",
        string_agg(REPLACE((c."accountType")::text, '"',''),', ')   AS "accountType" ,
    	string_agg(REPLACE((d."postingAmount")::text, '"',''),', ') AS "Amount",
        string_agg(REPLACE((d."journalId")::text, '"',''),', ')     AS "journalId"
    FROM ro_without ro 
    	JOIN du_dms_fortellis_model.etl_accounts d
    		ON REPLACE((d."referenceNo")::text,'"','') =ro."RO#"
        JOIN du_dms_fortellis_model.etl_account_coa c
            ON (d."accountNumber")::text = (c."accountNumber")::text
    GROUP BY ro."RO#"
    ORDER BY ro."RO#"
	) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

       \o 'GL-Accounting_report.csv'
    COPY (
	   WITH coa_details AS (SELECT
                         TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                             'g')) AS accountnumber,
                         TRIM(regexp_replace("accountDesc", E'[\\n\t\r]+', ' ',
                                             'g')) AS accountdescription,
                         TRIM(regexp_replace("accountType", E'[\\n\t\r]+', ' ',
                                             'g')) AS accounttype,
                         TRIM(regexp_replace("incBalReportGrpDesc", E'[\\n\t\r]+', ' ',
                                             'g')) AS incBalReportGrpDesc,
                         TRIM(regexp_replace("incBalReportSubGrp", E'[\\n\t\r]+', ' ',
                                             'g')) AS incbalsubgrp
                     FROM du_dms_fortellis_model.etl_account_coa
	 ),
         gl_details AS (SELECT
                           TRIM(regexp_replace("hostItemId", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS hostitemid,
                           TRIM(regexp_replace("accountingDate", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS accountingdate,
                           TRIM(regexp_replace("accountNumber", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS accountnumber,
                           split_part("hostItemId", '*', 3) AS invoice,
                           TRIM(regexp_replace("companyId", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS companyid,
                           TRIM(regexp_replace("controlNo", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS control,
                           TRIM(regexp_replace("journalId", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS journalid,
                           TRIM(regexp_replace("postingAmount", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingamount,
                           TRIM(regexp_replace("postingSequence", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingsequence,
                           TRIM(regexp_replace("postingTime", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingtime,
                           TRIM(regexp_replace("referenceNo", E'[\\n\t\r]+', ' ',
                                               'g'))                            AS refer
                       FROM du_dms_fortellis_model.etl_accounts
    	)
	SELECT
            hostitemid AS "Host Item ID",
    	    invoice AS "#Invoice",
    	    accountingdate AS "Accounting Date",
    	    accountnumber AS "Account Number",
    	    accountdescription AS "Acccount Description",
    	    accounttype AS "Account Type",
    	    incBalReportGrpDesc AS "incBalReportGrpDesc",
    	    incbalsubgrp AS "incBalReportSubGrp",
    	    companyid AS "Company ID",
    	    control AS "controlNo",
    	    journalid AS "Journal ID",
    	    postingamount AS "Posting Amount",
    	    postingsequence AS "Posting Sequence",
    	    postingtime AS "Posting Time"
        FROM gl_details
    		LEFT JOIN coa_details USING (accountnumber)
 ) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

 \o 'exception_cust_amount_notmatch_with_gl.csv'
    COPY(
        WITH pay_summary AS (
            SELECT "roNumber"::text, SUM("paymentAmount"::numeric)::text AS "paymentAmount"
            FROM du_dms_fortellis_model.etl_pay 
            WHERE "insuranceFlag" = 'N'
            GROUP BY "roNumber" HAVING SUM("paymentAmount"::numeric) > 0
        ), matching_cust_pay_amt AS (
            SELECT
                d."accountNumber"       AS "accountNumber",
                d."journalId"           AS "journalId",
                d."postingAmount"       AS "postingAmount",
                d."postingTime"         AS "postingTime",
                d."referenceNo"               AS "referenceNo",
                ps. "paymentAmount"
            FROM du_dms_fortellis_model.etl_accounts d JOIN pay_summary ps
            ON ps."roNumber"::text = d."referenceNo"::text AND d."postingAmount" = ps."paymentAmount"
        )
        SELECT  "roNumber" AS "RO Number", "paymentAmount" AS "Customer Pay Amount" FROM pay_summary WHERE "roNumber" NOT IN (
            SELECT DISTINCT "referenceNo" FROM matching_cust_pay_amt
        ) ORDER BY "roNumber"
    ) TO STDOUT WITH (FORMAT csv, HEADER true);

  \o 'corereturn_without_corecharge.csv'
    COPY(with core_charge as (
            select
                *
            from
                du_dms_fortellis_model.etl_parts
            where
                "coreSale" :: numeric != 0
                or "coreCost" :: numeric != 0
        ),
        core_return as (
            select
                *
            from
                du_dms_fortellis_model.etl_parts
            where
                "qtySold" :: numeric < 0
                 AND "desc" like '%CORE%'
        ),
        exp_return as(
            select
                *
            From
                core_return r
            where
                not exists(
                    select
                        *
                    from
                        core_charge c
                    where
                        c."roNumber" = r."roNumber"
                        and c."lineCode" = r."lineCode"
                )
        )
        SELECT
            "roNumber"    AS "RO Number",
            "lineCode" AS "Line",
            "number"   AS "Part No" 
        FROM
            exp_return
        UNION
        ALL
        SELECT
            ' Exception Count: ' || (
                SELECT
                    count(*)
                FROM
                    exp_return
            ),
            ' Total RO: ' || (
                SELECT
                    count(DISTINCT "roNumber")
                FROM
                    du_dms_fortellis_model.etl_head
            ),
            'Exception Percentage: ' || Round(
                (
                    (
                        (
                            select
                                count(*)
                            FROM
                                exp_return
                        ) * 100
                    ) :: numeric / (
                        SELECT
                            count(DISTINCT "roNumber")
                        FROM
                            du_dms_fortellis_model.etl_head
                    ) :: numeric
                ),
                2
            ) || '%'
        ) TO stdout WITH (FORMAT csv, HEADER true);


     \o 'corecharge_without_corereturn.csv'
        COPY (
             with core_charge as (
            select
                *
            from
                du_dms_fortellis_model.etl_parts
            where
                "coreSale" :: numeric != 0
                or "coreCost" :: numeric != 0
        ),
        core_return as (
            select
                *
            from
                du_dms_fortellis_model.etl_parts
            where
                "qtySold" :: numeric < 0
                AND "desc" like '%CORE%'
        ),
        exp_charge as(
            select
                *
            From
                core_charge c
            where
                not exists(
                    select
                        *
                    from
                        core_return r
                    where
                        r."roNumber" = c."roNumber"
                        and r."lineCode" = c."lineCode"
                )
        )
        SELECT
            "roNumber"    AS "RO Number",
            "lineCode" AS "Line",
            "number"   AS "Part No" 
        FROM
            exp_charge
        UNION
        ALL
        SELECT
            'Exception Count: ' || (
                SELECT
                    count(*)
                FROM
                    exp_charge
            ),
            'Total RO: ' || (
                SELECT
                    count(DISTINCT "roNumber")
                FROM
                    du_dms_fortellis_model.etl_head
            ),
            'Exception Percentage: ' || Round(
                (
                    (
                        (
                            select
                                count(*)
                            FROM
                                exp_charge
                        ) * 100
                    ) :: numeric / (
                        SELECT
                            count(DISTINCT "roNumber")
                        FROM
                            du_dms_fortellis_model.etl_head
                    ) :: numeric
                ),
                2
            ) || '%'
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'corecharge_corereturn_mismatch.csv'
        COPY (
             WITH partss AS(  
                SELECT
                    "roNumber",
                    "lineCode",
                    "number",
                    CASE 
                    WHEN "qtySold" :: numeric < 0 AND lower("desc") like '%core%'
                        THEN  'Core Return'
                    ELSE 'Part Number' END                                           AS classification,
                    "sale"::numeric * ceil("qtySold"::numeric)::integer        AS sale,
                    "cost"::numeric * ceil("qtySold"::numeric)::integer        AS cost,
                    "coreSale"::numeric * ceil("qtySold"::numeric)::integer    AS core_charge_sale,
                    "coreCost"::numeric * ceil("qtySold"::numeric)::integer    AS core_charge_cost
                FROM du_dms_fortellis_model.etl_parts
            )
            , core_return AS (
                SELECT
                    "roNumber",
                    "lineCode",
                    "number",
                    abs(SUM(sale))                                                        AS core_return_sale,
                    abs(SUM(cost))                                                        AS core_return_cost
                FROM partss
                WHERE classification = 'Core Return'
                GROUP BY 1, 2, 3
            )
            , parts_with_core_charge AS (
                SELECT
                    "roNumber",
                    "lineCode",
                    "number",
                    abs(SUM(sale))                                                        AS sale,
                    abs(SUM(cost))                                                        AS cost,
                    abs(SUM(core_charge_sale))                                            AS core_charge_sale,
                    abs(SUM(core_charge_cost))                                            AS core_charge_cost
                FROM partss
                WHERE classification = 'Part Number'
                    AND core_charge_sale != 0
                    AND core_charge_cost != 0
                GROUP BY 1, 2, 3
            ), final_result AS (SELECT 
                parts_with_core_charge."roNumber",
                "lineCode",
                parts_with_core_charge."number",
                sale,
                cost,
                core_charge_sale,
                core_charge_cost,
                core_return_sale,
                core_return_cost
            FROM parts_with_core_charge
                LEFT JOIN core_return USING("roNumber", "lineCode")
            WHERE 
             core_return."number" LIKE '%' ||parts_with_core_charge."number" ||'%' AND
   			 (abs(core_return_sale) != abs(core_charge_sale)OR abs(core_return_cost) != abs(core_charge_cost)))
   			 
   			 SELECT "roNumber" AS "RO Number",
                "lineCode" AS "Line",
                "number" AS "Part No.",
                sale::text AS "Part Sale",
                cost::text AS "Part Cost",
                core_charge_sale::text AS "C. Charge Sale",
                core_charge_cost::text AS "C. Charge Cost",
                core_return_sale::text AS "C. Return Sale",
                core_return_cost::text AS "C. Return Cost" FROM final_result
   			 UNION ALL 
   			 SELECT 'Exception Count: ' || (SELECT COUNT(*) FROM  final_result ),
                	'Total RO: ' || ( SELECT count(DISTINCT "roNumber") FROM du_dms_fortellis_model.etl_head),
                	'Exception Percentage: ' || Round(((
                            (
                                SELECT
                                    COUNT(*)
                                FROM
                                    FINAL_RESULT
                            ) * 100
                        ) :: numeric / (
                            SELECT
                                COUNT(DISTINCT "roNumber")
                            FROM
                                du_dms_fortellis_model.etl_head
                        ) :: numeric
                    ),
                    2
                ) || '%', '','','','','',''
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'corecharge_with_nosale.csv'
        copy (SELECT "roNumber" AS "RO Number", ep."number" AS "Part No" FROM du_dms_fortellis_model.etl_parts ep LEFT JOIN du_dms_fortellis_model.etl_head AS eth USING("roNumber") WHERE coalesce(nullif(trim("coreSale"), '')::numeric, 0) = 0 AND coalesce(nullif(trim("coreCost"), '')::numeric, 0) > 0 AND eth."closedDate" IS NOT NULL ORDER BY "roNumber") TO stdout WITH (FORMAT csv, HEADER true);

        \o 'invalid_core_cost_sale_mismatch.csv'
        COPY (
             WITH partss AS(  
                SELECT
                    "roNumber",
                    "lineCode",
                    "number",
                    CASE 
                    WHEN "qtySold" :: numeric < 0 AND lower("desc") like '%core%'
                        THEN  'Core Return'
                    ELSE 'Part Number' END                                           AS classification,
                    "sale"::numeric * ceil("qtySold"::numeric)::integer        AS sale,
                    "cost"::numeric * ceil("qtySold"::numeric)::integer        AS cost,
                    "coreSale"::numeric * ceil("qtySold"::numeric)::integer    AS core_charge_sale,
                    "coreCost"::numeric * ceil("qtySold"::numeric)::integer    AS core_charge_cost
                FROM du_dms_fortellis_model.etl_parts
            )
            , core_return AS (
                SELECT
                    "roNumber",
                    "lineCode",
                    "number",
                    abs(SUM(sale))                                                        AS core_return_sale,
                    abs(SUM(cost))                                                        AS core_return_cost
                FROM partss
                WHERE classification = 'Core Return'
                GROUP BY 1, 2, 3
            )
            , parts_with_core_charge AS (
                SELECT
                    "roNumber",
                    "lineCode",
                    "number",
                    abs(SUM(sale))                                                        AS sale,
                    abs(SUM(cost))                                                        AS cost,
                    abs(SUM(core_charge_sale))                                            AS core_charge_sale,
                    abs(SUM(core_charge_cost))                                            AS core_charge_cost
                FROM partss
                WHERE classification = 'Part Number'
                    AND core_charge_sale != 0
                    AND core_charge_cost != 0
                GROUP BY 1, 2, 3
            )
            SELECT 
                "roNumber" AS "RO Number",
                "lineCode" AS "Line",
                parts_with_core_charge."number" AS "Part No.",
                sale AS "Part Sale",
                cost AS "Part Cost",
                core_charge_sale AS "C. Charge Sale",
                core_charge_cost AS "C. Charge Cost",
                core_return_sale AS "C. Return Sale",
                core_return_cost AS "C. Return Cost"
            FROM parts_with_core_charge
                LEFT JOIN core_return USING("roNumber", "lineCode")
            WHERE 
            core_return."number" LIKE '%' ||parts_with_core_charge."number" ||'%' AND
            (core_return_sale != core_return_cost 
                OR core_charge_sale != core_charge_cost)  
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'invalid_core_amount_mismatch.csv'
        COPY (
            WITH partss AS(  
                SELECT
                    "roNumber",
                    "lineCode",
                    "number",
                    CASE 
                    WHEN "qtySold" :: numeric < 0 AND lower("desc") like '%core%'
                        THEN  'Core Return'
                    ELSE 'Part Number' END                                           AS classification,
                    "sale"::numeric * ceil("qtySold"::numeric)::integer        AS sale,
                    "cost"::numeric * ceil("qtySold"::numeric)::integer        AS cost,
                    "coreSale"::numeric * ceil("qtySold"::numeric)::integer    AS core_charge_sale,
                    "coreCost"::numeric * ceil("qtySold"::numeric)::integer    AS core_charge_cost
                FROM du_dms_fortellis_model.etl_parts
            )
            , core_return AS (
                SELECT
                    "roNumber",
                    "lineCode",
                    "number",
                    abs(SUM(sale))                                                        AS core_return_sale,
                    abs(SUM(cost))                                                        AS core_return_cost
                FROM partss
                WHERE classification = 'Core Return'
                GROUP BY 1, 2, 3
            )
            , parts_with_core_charge AS (
                SELECT
                    "roNumber",
                    "lineCode",
                    "number",
                    abs(SUM(sale))                                                        AS sale,
                    abs(SUM(cost))                                                        AS cost,
                    abs(SUM(core_charge_sale))                                            AS core_charge_sale,
                    abs(SUM(core_charge_cost))                                            AS core_charge_cost
                FROM partss
                WHERE classification = 'Part Number'
                    AND core_charge_sale != 0
                    AND core_charge_cost != 0
                GROUP BY 1, 2, 3
            )
            SELECT 
                "roNumber" AS "RO Number",
                "lineCode" AS "Line",
                parts_with_core_charge."number" AS "Part No.",
                sale AS "Part Sale",
                cost AS "Part Cost",
                core_charge_sale AS "C. Charge Sale",
                core_charge_cost AS "C. Charge Cost",
                core_return_sale AS "C. Return Sale",
                core_return_cost AS "C. Return Cost"
            FROM parts_with_core_charge
                LEFT JOIN core_return USING("roNumber", "lineCode")
            WHERE 
            (core_return."number" LIKE '%' ||parts_with_core_charge."number" ||'%') AND
            (NOT COALESCE(core_return_sale = core_charge_sale, TRUE) 
                OR NOT COALESCE(core_return_cost = core_charge_cost, TRUE))
        ) TO stdout WITH (FORMAT csv, HEADER true);

    \o 'part_details_null.csv'
    COPY (
        SELECT "roNumber" FROM etl_parts WHERE NULLIF("number",'') IS NULL GROUP BY "roNumber" 
    ) TO stdout WITH (FORMAT csv, HEADER true);  

  \o 'gl_missing_ro_list.csv'
        COPY ( 
                with proxy_data AS (  SELECT
                           lbr."roNumber"                                           AS ro_number,
                           sum(coalesce(nullif("sale", '') :: numeric, 0)) * 100 AS sale_amount,
                           sum(coalesce(nullif("cost", '') :: numeric, 0)) * 100 AS cost_amount,
                           left(lbr."type", 1)                              AS prt_billing_type
                       FROM etl_labor lbr
                           JOIN etl_job job
                               ON lbr."roNumber" = job."roNumber"
                                  AND lbr."lineCode" = job."lineCode"
                       GROUP BY lbr."roNumber", left(lbr."type", 1)
                       HAVING sum(coalesce(nullif("sale", '') :: numeric, 0)) * 100 != 0
			 OR sum(coalesce(nullif("cost", '') :: numeric, 0)) * 100 != 0
)
    ,part_data AS (  SELECT
                         "roNumber"                                                     AS ro_number,
                         left("laborType", 1)                                        AS prt_billing_type,
                         sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("sale"), '') :: numeric, 0)) * 100 AS sale_amount,
                         sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("cost"), '') :: numeric, 0)) * 100 AS cost_amount
                     FROM etl_parts
                     GROUP BY "roNumber", left("laborType", 1)
                     HAVING  sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("sale"), '') :: numeric, 0)) * 100 != 0
			 OR sum(coalesce(nullif(trim("qtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("cost"), '') :: numeric, 0)) * 100 != 0
)
    ,tax_data AS ( SELECT
                       "roNumber"                       AS ro_number,
                       left("payType", 1)            AS prt_billing_type,
                       sum("roTax" :: numeric) * 100 AS sale_amount
                   FROM etl_total
                   GROUP BY "roNumber", left("payType", 1)
                   HAVING sum("roTax" :: numeric) * 100 != 0
)

	SELECT 
    eh."roNumber", 
    eh."openDate", 
    eh."closedDate", 
    eh."voidedDate", 
    ev."make"
    FROM 
        du_dms_fortellis_model.etl_head AS eh
    LEFT JOIN 
        du_dms_fortellis_model.etl_vehicle AS ev 
        ON eh."roNumber" = ev."roNumber"
    WHERE 
        eh."closedDate" IS NOT NULL 
        AND eh."roNumber" NOT IN (
            SELECT split_part("hostItemId", '.', 3) 
            FROM du_dms_fortellis_model.etl_accounts
        )
        AND (
            eh."roNumber" IN (SELECT ro_number FROM proxy_data)
            OR eh."roNumber" IN (SELECT ro_number FROM part_data)
    )
			-- or "roNumber" in(select ro_number from tax_data))
      ) TO stdout WITH (FORMAT csv, HEADER true);
SQL
        cp 'corereturn_without_corecharge.csv' $CORE_RETURN_EXCEPTION_CSV_FILE_PATH 
        cp 'corecharge_without_corereturn.csv' $CORE_CHARGE_EXCEPTION_CSV_FILE_PATH
        cp 'corecharge_corereturn_mismatch.csv' $CORE_RETURN_NOT_EQUAL_TO_CORE_CHARGE_EXCEPTION_CSV_FILE_PATH
        cp 'corecharge_with_nosale.csv' $CORE_CHARGE_WITH_NO_SALE

        cp 'invalid_core_cost_sale_mismatch.csv' $INVALID_CORE_COST_SALE_MISMATCH
        cp 'invalid_core_amount_mismatch.csv' $INVALID_CORE_AMOUNT_MISMATCH

        cp 'gl_missing_ro_list.csv' $GL_MISSING_RO_LIST
        cp 'part_details_null.csv' $PART_DETAILS_NULL_EXCEPTION_FILEPATH

        --ROCOUNT_DETAILS=$(psql_local -t -c "SELECT count(*) FROM etl_head_detail;")
        --echo "Total Ros Count:-$ROCOUNT_DETAILS"

        d1=0
        d2=1
        corereturn_without_corecharge_line=$(tail -1 'corereturn_without_corecharge.csv')
        corereturn_without_corecharge_extract=$(echo $corereturn_without_corecharge_line| cut -d',' -f 1)
        corereturn_without_corecharge_count=$(echo $corereturn_without_corecharge_extract| cut -d':' -f 2 | sed 's/ *$//g') 
        corereturn_without_corecharge_count_numeric=$(echo "$corereturn_without_corecharge_count - $d1" | bc)
        echo "corereturn_without_corecharge_count_numeric:$corereturn_without_corecharge_count_numeric"
        
        corecharge_without_corereturn_line=$(tail -1 'corecharge_without_corereturn.csv')
        corecharge_without_corereturn_extract=$(echo $corecharge_without_corereturn_line| cut -d',' -f 1)
        corecharge_without_corereturn_count=$(echo $corecharge_without_corereturn_extract| cut -d':' -f 2 | sed 's/ *$//g') 
        corecharge_without_corereturn_count_numeric=$(echo "$corecharge_without_corereturn_count - $d1" | bc)
        echo "corecharge_without_corereturn_count_numeric:$corecharge_without_corereturn_count_numeric"

        corecharge_corereturn_mismatch_line=$(tail -1 'corecharge_corereturn_mismatch.csv')
        corecharge_corereturn_mismatch_extract=$(echo $corecharge_corereturn_mismatch_line| cut -d',' -f 1)
        corecharge_corereturn_mismatch_count=$(echo $corecharge_corereturn_mismatch_extract| cut -d':' -f 2 | sed 's/ *$//g') 
        corecharge_corereturn_mismatch_count_numeric=$(echo "$corecharge_corereturn_mismatch_count - $d1" | bc)
        echo "corecharge_corereturn_mismatch_count_numeric:$corecharge_corereturn_mismatch_count_numeric"

        corecharge_with_nosale_count=$(cat 'corecharge_with_nosale.csv'  | wc -l)
        corecharge_with_nosale_count_numeric=$(echo "$corecharge_with_nosale_count - $d2" | bc)
        echo "corecharge_with_nosale_count_numeric:$corecharge_with_nosale_count_numeric"

        invalid_core_cost_sale_mismatch_count=$(cat 'invalid_core_cost_sale_mismatch.csv'  | wc -l)
        invalid_core_cost_sale_mismatch_count_numeric=$(echo "$invalid_core_cost_sale_mismatch_count - $d2" | bc)
        echo "invalid_core_cost_sale_mismatch_count_numeric:$invalid_core_cost_sale_mismatch_count_numeric"


        invalid_core_amount_mismatch_count=$(cat 'invalid_core_amount_mismatch.csv'  | wc -l)
        invalid_core_amount_mismatch_count_numeric=$(echo "$invalid_core_amount_mismatch_count - $d2" | bc)
        echo "invalid_core_amount_mismatch_count_numeric:$invalid_core_amount_mismatch_count_numeric"


        part_details_null_exception_count=$(cat 'part_details_null.csv'  | wc -l)
        part_details_null_exception_count_numeric=$(echo "$part_details_null_exception_count - $d2" | bc)
        echo "part_details_null_exception_count_numeric:$part_details_null_exception_count_numeric"

        # invalid_core_cost_sale_mismatch_count_numeric=10
        # invalid_core_amount_mismatch_count_numeric=10
        
        HALT_COUNT=$(($corecharge_corereturn_mismatch_count_numeric + $invalid_core_cost_sale_mismatch_count_numeric + $invalid_core_amount_mismatch_count_numeric )) 
       
        # if [[ "$HALT_OVER_RIDE" = 'false' ]]; 
        # then

        #     if [[ $HALT_COUNT -gt 50 ]];
        #     then
        #         die "Process is HALT!"
        #     fi

        # fi

       
       
       
       
       
       if [[ "$HALT_OVER_RIDE" = 'false' ]]; 
       then

           if [[ $corecharge_corereturn_mismatch_count_numeric -gt 0 ]];
           then
               die "Process is HALT!"
           fi

             if [[ $invalid_core_cost_sale_mismatch_count_numeric -gt 0 ]];
           then
               die "Process is HALT!"
           fi

          if [[ $invalid_core_amount_mismatch_count_numeric -gt 0 ]];
           then
               die "Process is HALT!"
          fi

           if [[ $corecharge_with_nosale_count_numeric -gt 0 ]];
            then
               die "Process is HALT!"
            fi

            if [[ $corereturn_without_corecharge_count_numeric -gt 0 ]];
            then
                die "Process is HALT!"
            fi

            if [[ $corecharge_without_corereturn_count_numeric -gt 0 ]];
            then
                die "Process is HALT!"
            fi

        fi
    
    )
}

function generate_report_customer_pay_types() {
    (
        progress "Generating report of all customer paytypes"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;

        \o 'customer-pay-types.csv'
        COPY (
            WITH cte AS(
                SELECT 
                "roNumber" ,
                SUM("duration"::decimal)  AS "Total Punch Duration",
                STRING_AGG (DISTINCT "technicianId", ',') AS "Tech ID"
                FROM du_dms_fortellis_model.etl_tech_punch GROUP BY "roNumber"
            ), paycode AS (
            	SELECT STRING_AGG("code",', ') As paymentcode , "roNumber" As ro_num
				FROM du_dms_fortellis_model.etl_pay GROUP BY "roNumber" ORDER BY "roNumber"
            )
            SELECT
                lbr."roNumber"                                                                       AS "RO Number",
                CASE
                WHEN COUNT(pay."insuranceFlag")  FILTER (WHERE pay."insuranceFlag"::boolean) > 0
                    THEN 'YES'
                ELSE 'NO' END                                                                        AS "Is Insurance",
                lbr."PayType"                                                                        AS "Pay Type",
                vd."make"                                                                            AS "Vehicle Make",
                SUM(pay."paymentAmount"::numeric)  FILTER (WHERE pay."insuranceFlag"::boolean) AS "Total Insurance Amount",
                SUM(pay."paymentAmount"::numeric)                                                 AS "Total Payment Amount",
                cte."Total Punch Duration"                                                           AS "Total Punch Duration",
		        cte."Tech ID"                                                                        AS "Tech ID",
		        paymentcode 																		 AS "Pay Code"
            FROM (            
                SELECT
                    "roNumber",
                    STRING_AGG(DISTINCT "type",', ') AS "PayType"
                FROM du_dms_fortellis_model.etl_labor
                GROUP BY "roNumber"
                ORDER BY  "roNumber") lbr
                LEFT JOIN du_dms_fortellis_model.etl_pay pay USING ("roNumber") 
                LEFT JOIN du_dms_fortellis_model.etl_vehicle vd USING ("roNumber")
                LEFT JOIN  cte  USING ("roNumber")
                LEFT JOIN paycode p ON p.ro_num = lbr."roNumber"
            GROUP BY "roNumber", lbr."PayType", vd."make", cte."Total Punch Duration", cte."Tech ID" ,paymentcode
            ORDER BY "roNumber"
        ) TO stdout WITH (FORMAT csv, HEADER true);
SQL
    )
}

function generate_report_total_discount() {
    (
        progress "Generating report of total discount"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;

        \o 'ro_discount.csv'
        COPY (
		with cte as(SELECT
        			"roNumber"           AS ro_number,
        			"totalDiscount"::numeric AS total_discount,
        			id::numeric AS total_discount_lineitem,
        			"level" as dis_Level,
        			case when ("lineCode"::text)='RO' then ' level' else concat(' item',("lineCode"::text),' ') end  as LineCode,
        			"laborDiscount" as dis_LaborDiscount,
        			"partsDiscount" as dis_PartsDiscount,
        			concat(("level"::text),((case when ("lineCode"::text)='RO' then ' level' else concat(' item ',("lineCode"::text)) end  )::text),' (',(id::text),' - ',("desc"::text),')',(case when ("partsDiscount"::numeric) !=0 then concat(' Parts Discount= ',(round(abs("partsDiscount"::numeric),2)::text),',') end ),(case when ("laborDiscount"::numeric) !=0 then concat(' Labor Discount= ',(round(abs("laborDiscount"::numeric),2)::text)) end)) dis_description
    				FROM du_dms_fortellis_model.etl_discount)
            	SELECT "ro_number" as "RO Number",
	        	round(abs(SUM("total_discount"::numeric)),2) as "Discount",
	        	string_agg(("dis_description"::text),'; ') as "Summary"
		from cte
    		where ("total_discount"::numeric) !=0  
    		group by "ro_number" 
      ) TO stdout WITH (FORMAT csv, HEADER true);
SQL
    )
}

function generate_exception_report() {
    (
        progress "Generating exception Report"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        # psql_local --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/exception-report.psql \
        #            || die "Generating exception Report Failed"

    )
}

function generate_customerpay_report() {
    (
        progress "Generating Customerpay Report"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        # psql_local --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/customerpay-exception-report.psql \
        #           || die "Generating Customerpay Report Failed"

    )
}

function copy_analysis_table() {
    (
        progress "Copy tables"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/create-exception-csv.psql \
                   || die "Copy tables Failed"

    )
}

function generate_gl_report_for_analysis() {
    (
        progress "Generate GL report for Analysis"
        clear_dir "$EXCEPTION_TABLES_DIR"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/create-gl-report-analysis.psql \
                   || die "Generate GL report Failed"

    )
}

function extract_proxy(){
    progress "$(date +'%H:%M:%S') : Ready to Extract Text ROs"
    proxy_path="$1"
    cd ${DU_ETL_HOME}/${DMS_HOME}/src/exception/python/
    python3 ./extract_text_proxy.py "$1"
    progress "$(date +'%H:%M:%S') : Done Extracting Text ROs"
}

function extract_proxy_to_tsv(){
    progress "$(date +'%H:%M:%S') : Ready to Extract Text ROs to TSV"
    proxy_path="$1"
    tsv_path="$2"
    cd ${DU_ETL_HOME}/${DMS_HOME}/src/extract/python
    python3 ./extract-text-proxy-to-tsv.py "$1" "$2"
    progress "$(date +'%H:%M:%S') : Done Extracting Text ROs to TSV"
}

function generate_analysis_report() {
    (
        store_name_full=$(basename "$INPUT_BUNDLE_ZIP")
        STORE_NAME=${store_name_full%-INITIAL*}
        store_id_full=${INPUT_BUNDLE_ZIP#*3PA}
        store_id=${store_id_full%-*}
        STORE_ID="3PA"$store_id
        

        progress "Scheduler Analysis Report"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_scheduler --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/exception-report-analysis.psql \
                    --set=dealer_id="${STORE_ID}" \
                    --set=project_id="${PROJECT_ID}" \
                    --set=sec_project_id="${SEC_PROJECT_ID}" \
                    --set=store_name="${STORE_NAME}" \
                   || die "Scheduler Analysis Report Failed"

    )
}

function generate_core_exception_report() {
   (
   progress "Generating report of all customer paytypes"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;
        \o 'corecharge_with_nosale.csv'
        copy (SELECT 
        ep."roNumber" AS "RO Number", 
        ep."number" AS "Part No"  
    FROM 
        du_dms_fortellis_model.etl_parts AS ep
    LEFT JOIN 
        du_dms_fortellis_model.etl_head AS eth 
        ON ep."roNumber" = eth."roNumber"  -- Explicit join condition
    WHERE 
        COALESCE(NULLIF(TRIM(ep."coreSale"), '')::numeric, 0) = 0 
        AND COALESCE(NULLIF(TRIM(ep."coreCost"), '')::numeric, 0) > 0 
        AND eth."closedDate" IS NOT NULL 
    ORDER BY 
        ep."roNumber") TO stdout WITH (FORMAT csv, HEADER true);
SQL
       cp 'corecharge_with_nosale.csv' $CORE_CHARGE_WITH_NO_SALE
    )
}

function exception_report_analysis(){
    progress "$(date +'%H:%M:%S') : Ready to Generate Exception Analysis Report"
    
    input_dir="$1"
    output_dir="$2"
    destination="/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception_tag/All_exception_details.csv"
    
    cd "${DU_ETL_HOME}/${DMS_HOME}/Processor-Application/src/python" || {
        echo "Failed to cd into python directory"
        return 1
    }

    python3 ./exception_details.py "$input_dir" "$output_dir"
    src_file="${output_dir}/All_exception_details.csv"
    ls $output_dir
    if [ -f "$src_file" ]; then
        cp "$src_file" "$destination"
        ls $destination
        echo "File copied to $destination"
    else
        echo "Source file not found: $src_file"
        return 1
    fi
    progress "$(date +'%H:%M:%S') : Done Generating Exception Analysis Report"
}

function load_exception_analysis_report() {
    (
        progress "$(date +'%H:%M:%S') : Loading Exception Analysis Report"
        total_ro_count_for_report=`psql_local -t -c "SELECT COUNT(*) FROM du_dms_fortellis_model.etl_head"`
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_exception_analysis --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/Processor-Application/src/psql/exception-report-analysis.psql \
                    --set=uuid="${UUID}" \
                    --set=total_ro_count="${total_ro_count_for_report}" \
                    --set=performed_by="${PERFORMED_BY}" \
                    --set=dms="${DMS}" \
                   || die "Exception Analysis Report load Failed"
        progress "$(date +'%H:%M:%S') : Done Loading Exception Analysis Report"

    )
}

function save_scheduler_details() {
    (
        progress "$(date +'%H:%M:%S') : Saving scheduler details"
        total_ro_count_for_report=`psql_local -t -c "SELECT COUNT(*) FROM du_dms_fortellis_model.etl_head"`
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_exception_analysis --quiet --file ${DU_ETL_HOME}/scheduler_halt/save_scheduler_details.psql \
                    --set=uuid="${UUID}" \
                    --set=total_ro_count="${total_ro_count_for_report}" \
                    --set=performed_by="${PERFORMED_BY}" \
                    --set=dms="${DMS}" \
                   || die "Saving scheduler details Failed"
        progress "$(date +'%H:%M:%S') : Done Saving scheduler details"

    )
}


function  pre_import_halt_detection() {

    cd "$ZIP_WORK_DIR"
    deptment_halt=false
    paytype_halt=false
    make_halt=false
    inv_seq_halt=false

    echo "Paytype import halt checking"
    paytype_import_halt=`psql_local -t -c "WITH store_paytype AS ( 
        SELECT distinct \"type\" As paytype_value FROM du_dms_fortellis_model.etl_labor WHERE \"type\" !~ '^I' AND NULLIF(\"type\",'' ) IS NOT NULL
    )
    SELECT count(*) FROM store_paytype WHERE paytype_value NOT IN (SELECT paytype FROM du_dms_fortellis_model.etl_paytype_detail)"`

    if [[ $paytype_import_halt -gt 0 ]];
        then
        paytype_halt=true
    fi

    echo "Department import halt checking"
    # department_import_halt=`psql_local -t -c 'WITH store_department AS ( 
    #     SELECT distinct "department" As department_value FROM du_dms_fortellis_model.etl_head
    # )
    # SELECT count(*) FROM store_department WHERE department_value NOT IN (SELECT department_name FROM du_dms_fortellis_model.etl_department)'`
    department_import_halt=0
    if [[ $department_import_halt -gt 0 ]];
        then
        deptment_halt=true
    fi

    echo "Inv Seq halt checking"
    inv_import_halt=`psql_local -t -c "WITH seq AS ( 
        SELECT
            \"roNumber\" as ro_number,
            \"openDate\" as open_date, 
            \"closedDate\" as close_date,
            '' as void_date,
            ((regexp_matches(\"roNumber\", '^(\\d+)\\D*$')) [1])::integer AS ro_num_int
        FROM du_dms_fortellis_model.etl_head rd 
        ORDER BY 3, 1
    )
    , seq_1 AS (
        SELECT *,
            COALESCE(lead(ro_num_int) OVER seq_range > ro_num_int + 100, true) AS shift_seq,
            COALESCE(lag(ro_num_int) OVER seq_range < ro_num_int - 100, true)  AS new_seq 
        FROM seq
        WINDOW seq_range AS (ORDER BY ro_num_int, ro_number) 
    )
    , start_end_seq AS (
        SELECT *
        FROM seq_1
        WHERE shift_seq OR new_seq
    )
    , wind_seq AS (
        SELECT
            ro_number                                              AS start_ro,
            lead(ro_number) OVER(ORDER BY ro_num_int, ro_number)   AS end_ro,
            ro_num_int                                             AS start_ro_int,
            lead(ro_num_int) OVER(ORDER BY ro_num_int, ro_number)  AS end_ro_int,
            open_date                                              AS start_ro_date,
            lead(open_date) OVER(ORDER BY ro_num_int, ro_number)   AS end_ro_date,
            lead(shift_seq) OVER(ORDER BY ro_num_int, ro_number)   AS end_shift_seq,
            shift_seq,
            new_seq
        FROM start_end_seq
    )
    SELECT count(*) FROM wind_seq WHERE new_seq"`

    if [[ $inv_import_halt -gt 1 ]];
        then
        inv_seq_halt=true
    fi

    echo "Unassigned Make import halt checking"
    make_import_halt=`psql_local -t -c "WITH unassigned_make AS (
    SELECT COALESCE(renamed_name, UPPER(make)) AS store_make
    FROM du_dms_fortellis_model.etl_vehicle
    LEFT JOIN du_dms_fortellis_model.etl_makerenames_detail
        ON UPPER(make) = original_name
    WHERE NULLIF(make, '') IS NOT NULL
    GROUP BY COALESCE(renamed_name, UPPER(make))
    EXCEPT
    SELECT unnest(valid_makes_array::text[]) AS make_name
    FROM du_dms_fortellis_model.etl_all_manufacturer_detail
)
SELECT count(*) 
FROM unassigned_make"`

    if [[ $make_import_halt -gt 0 ]];
        then
        make_halt=true
    fi

    echo "paytype_halt     $paytype_halt"
    echo "inv_seq_halt     $inv_seq_halt"
    echo "make_halt        $make_halt"
    echo "deptment_halt    $deptment_halt"



    if [ "$make_halt" = true ] || [ "$deptment_halt" = true ] || [ "$paytype_halt" = true ] || [ "$inv_seq_halt" = true ] || [ "$SAVE_FILE" = true ]; then
        PRE_IMPORT_HALT=true
        rm -rf "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        touch "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        echo "paytype Halt: $paytype_import_halt" >> "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        echo "Inv Sequence Halt: $inv_import_halt" >> "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        echo "Make Halt: $make_import_halt" >> "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        echo "Department Halt: $department_import_halt" >> "$ZIP_WORK_DIR/processing-result/halt-import.txt"
        store_name_full=$(basename "$INPUT_BUNDLE_ZIP")
        STORE_NAME=${store_name_full%-INITIAL*}
        SUBJECT="Pre Import Halt Detected for $STORE_NAME"
        BODY=$(cat "$ZIP_WORK_DIR/processing-result/halt-import.txt")
        email_halt_summary "$SUBJECT" "$BODY"        
        IFS=',' read -ra ALL_COMPANY_IDS <<< "$COMPANY_IDS"
        for COMPANY_ID in "${ALL_COMPANY_IDS[@]}"; do
            echo "COMPANY_ID: $COMPANY_ID"
            save_import_halt_status $UUID $COMPANY_ID $make_halt $deptment_halt $paytype_halt $inv_seq_halt || die "Loading halt status failed"
        done

    else
        PRE_IMPORT_HALT=false
        echo "No Pre-import halt detected."
    fi

    echo "PRE_IMPORT_HALT: $PRE_IMPORT_HALT"
}


