# source into process-json

function generate_proxies_from_zip() {
    progress "Generating Proxies from Post-Processed Zip File"

    [[ -f "$POST_PROCESSED_ZIP_FILE" ]] || die "Input Zip File Must Exist"
    local local_work_dir="$WORK_DIR"/post-process-proxies
    clear_dir "$local_work_dir"
    unzip "$POST_PROCESSED_ZIP_FILE" -d "$local_work_dir"
    restore_dump_file_to_database "$local_work_dir"/process-json-csv-results.pgdump
    build_proxy_repair_orders "$local_work_dir" "${BRAND}"
    compress_proxy_output "$local_work_dir"
    prompt_continuation "Review and Continue"
}

function restore_dump_file_to_database() {
    function psql_local() {
        psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA},${SRC_SCHEMA}_model'" --set=ON_ERROR_STOP=1 "$@"
        return $?
    }
    psql_local --quiet <<SQL
        SET client_min_messages = 'WARNING';
        DROP SCHEMA IF EXISTS ${SRC_SCHEMA}_model CASCADE;
        CREATE SCHEMA ${SRC_SCHEMA}_model;
SQL
    progress "Restoring $1"
    pg_restore --verbose -d "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}_model'" "$1"
}

function build_proxy_repair_orders() {
    say "Generating Proxy Repair Orders per Request"
    PROXY_SCHEMA="${SRC_SCHEMA}"_proxy
    SRC_PROXY_SCHEMA="${SRC_SCHEMA}"_model
   
    echo "SHOW_ACCOUNTING_IN_PROXY2:${SHOW_ACCOUNTING_IN_PROXY}"

    SHOW_ACCOUNTING_IN_PROXY=$2
    PORSCHE_STORE=$3
    echo "--------------PORSCHE_STORE-----------$SHOW_ACCOUNTING_IN_PROXY-----------"
    echo $PORSCHE_STORE
        echo "--------------PORSCHE_STORE-r---------------------"

    DUAL_PROXY=$4
    BRAND=$5
    STATE=$6
    (
        cd "$DU_ETL_HOME"/DU-ProxyInvoice
        "$DU_ETL_HOME"/DU-ProxyInvoice/initialize-schema "$PROXY_SCHEMA"
    ) || die "Initialization Failed"

    (
        cd "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis
        psql_etl_proxy --quiet --file ./src/parse/fortellis-proxy-schema-alter.psql
        psql_etl_proxy --quiet --file ./src/parse/populate-fortellis-proxy-invoice.psql
    ) || die "Failed to Populate Fortellis Proxy Invoice"

    (
        mkdir -p "$1"/proxy-invoice
        TEMPLATE_BACKGROUND_ARG=
        TEMPLATE_TYPE=
        if [[ -f "$1"/proxy-background.pdf ]]; then
            TEMPLATE_BACKGROUND_ARG="$1"/proxy-background.pdf
            TEMPLATE_TYPE='invoice_mimic'
        else
            TEMPLATE_BACKGROUND_ARG='none'
            TEMPLATE_TYPE='invoice_mimic'
        fi
        cd "$DU_ETL_HOME"/DU-ProxyInvoice
        GENERATE_DUAL_PROXY=false
        # Convert BRAND to lowercase
        BRAND_LOWER=$(echo "$BRAND" | tr '[:upper:]' '[:lower:]')
        # Apply logic
        echo "BRAND_LOWER --------    $BRAND_LOWER" 
                echo "PORSCHE_STORE --------    $PORSCHE_STORE" 

        if [[ "$BRAND_LOWER" != "porsche" && "$PORSCHE_STORE" == "true" ]]; then
        GENERATE_DUAL_PROXY=true
        fi
        IS_PORSCHE_STORE=false
        if [[ "$BRAND_LOWER" == "porsche" ]]; then
        IS_PORSCHE_STORE=true
        fi
        echo "GENERATE_DUAL_PROXY --------    $GENERATE_DUAL_PROXY" 
            "$DU_ETL_HOME"/DU-ProxyInvoice/generate-fixed-width-proxy-invoice-fortellis.bash \
            "$PROXY_SCHEMA" \
            "$1"/proxy-invoice \
            'true' \
            "invoice_mimic" \
            "$TEMPLATE_BACKGROUND_ARG" \
            "$SHOW_ACCOUNTING_IN_PROXY" \
            "$IS_PORSCHE_STORE"\
            "$DUAL_PROXY"\
            "$BRAND" \
    ) || die "Proxy invoice generation Failed"
}

function compress_proxy_output() {
(

    base_output_dir="$1"/proxy-invoice
        progress "compress $base_output_dir"

    txt_output_dir="$base_output_dir"/text
    pdf_output_dir="$base_output_dir"/pdf
    bundle_output_dir="$base_output_dir"/bundle

    cd "$base_output_dir"
echo $base_output_dir
    if [[ -d "$txt_output_dir" ]]; then
        zip -j -qm Proxy-Invoices-TXT.zip "$txt_output_dir"/*
        rmdir "$txt_output_dir"
    fi

    if [[ -d "$pdf_output_dir" ]]; then
        zip -j -qm Proxy-Invoices-PDF.zip "$pdf_output_dir"/*
        rmdir "$pdf_output_dir"
    fi

    if [[ -d "$bundle_output_dir" ]]; then
        zip -j -qm Proxy-Invoices-Bundles.zip "$bundle_output_dir"/*
	 echo "-----COPY FILE TO AUDIT DISPATCH DIRECTORY----------${INPUT_ZIP_FILE_NAME}-----"
        zip -j "/etl/audit-dispatch-bundle/${OUTPUT_FILE_PREFIX}""${INPUT_ZIP_FILE_NAME}" "$1"/processing-result/coa-report.csv "$1"/processing-result/invoice-master.csv Proxy-Invoices-Bundles.zip
        rmdir "$bundle_output_dir"
    fi

)
}
