# To be sourced by process-xml
TRANSFORMS_DIR="$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/runtime/transforms
mkdir -p "$TRANSFORMS_DIR"

JQ_HEAD="$TRANSFORMS_DIR"/head.jq-trans
JQ_DEDUCTIBLE="$TRANSFORMS_DIR"/deductible.jq-trans
JQ_JOB="$TRANSFORMS_DIR"/job.jq-trans
JQ_LABOR="$TRANSFORMS_DIR"/labor.jq-trans
JQ_PARTS="$TRANSFORMS_DIR"/parts.jq-trans
JQ_TOTAL="$TRANSFORMS_DIR"/total.jq-trans
JQ_FEES="$TRANSFORMS_DIR"/fees.jq-trans
JQ_MLS="$TRANSFORMS_DIR"/mls.jq-trans
JQ_TECH_PUNCH="$TRANSFORMS_DIR"/tech_punch.jq-trans
JQ_DISCOUNT="$TRANSFORMS_DIR"/tech.jq-trans
JQ_VEHICLE="$TRANSFORMS_DIR"/vehicle.jq-trans
JQ_VISIT="$TRANSFORMS_DIR"/tech.jq-trans
JQ_WARRANTY="$TRANSFORMS_DIR"/tech.jq-trans
JQ_PAY="$TRANSFORMS_DIR"/pay.jq-trans


RO_COUNT_BATCH_FILE=/tmp/$(date +%s).tmp

function create_bundle_for_etl() {
    [[ -d "$BUNDLE_OUTPUT_DIRECTORY" ]] || die "Bundle output directory must be specified and exist"
    [[ -n "$BUNDLE_IDENTIFIER" ]]       || die "Bundle identifier required"

    if [[ "$PERFORM_ZIP" = 'true' ]]; then
        zip "$BUNDLE_OUTPUT_DIRECTORY"/"$BUNDLE_IDENTIFIER".zip process-xml-csv-results.pgdump
    else
        cp ./process-xml-csv-results.pgdump "$BUNDLE_OUTPUT_DIRECTORY"/
    fi
}

function persist_and_export_data_and_schema() {
    progress "Persisting and Exporting Data and Schema"
    pg_dump -Fc "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}_model'" \
            -t '*' \
            > "${1:?Directory Required}"/process-json-csv-results.pgdump
}

function export_job_data_to_csv(){


    progress  "Creating New Status File!"
    mkdir "$WORK_DIR_IMPORT_FILES_DIR"
    cd "$WORK_DIR_IMPORT_FILES_DIR"
   psql_local --quiet \
               --set=DMS_HOME="$DMS_HOME" \
               --set=DU_ETL_HOME="$DU_ETL_HOME" \
               --set=WORK_DIR_IMPORT_FILES_DIR="$WORK_DIR_IMPORT_FILES_DIR" \
               >/dev/null \
<<'SQL'
SET client_min_messages = 'warning';

SELECT set_config('search_path', 'du_dms_fortellis_model', true);

\i :DU_ETL_HOME/DU-DMS/DMS-Fortellis/src/parse/finalize-job-schema.psql

SQL

 psql_local --quiet \
               --set=DMS_HOME="$DMS_HOME" \
               >/dev/null \
<<'SQL'
SET client_min_messages = 'warning';



SELECT set_config('search_path', 'du_dms_fortellis_import', true);
-- \o 'discount-details.csv'
--    copy (SELECT * FROM du_dms_fortellis_import.ro_discount_details ORDER BY invoicenumber ASC) TO stdout WITH (FORMAT csv, HEADER true);

-- \o 'gl-accounting-details.csv' 
--    copy (SELECT * FROM du_dms_fortellis_import.ro_gl_accounting) TO stdout WITH (FORMAT csv, HEADER true);

\o 'job-details.csv'
   copy (SELECT * FROM du_dms_fortellis_import.ro_job_details ORDER BY invoicenumber ASC) TO stdout WITH (FORMAT csv, HEADER true);

\o 'invoice-master.csv'
  copy (SELECT * FROM du_dms_fortellis_import.ro_details ORDER BY invoicenumber ASC) TO stdout WITH (FORMAT csv, HEADER true);

\o 'part-details.csv'
  copy (SELECT  * FROM du_dms_fortellis_import.ro_partsdetails ORDER BY invoicenumber ASC) TO stdout WITH (FORMAT csv, HEADER true);

\o 'labor-details.csv'
  copy (SELECT * FROM du_dms_fortellis_import.ro_labordetails ORDER BY invoicenumber ASC ) TO stdout WITH (FORMAT csv, HEADER true);

\o 'ro_sequence.csv'
  copy (SELECT * FROM du_dms_fortellis_import.ro_sequence ORDER BY sequence_no ASC ) TO stdout WITH (FORMAT csv, HEADER true);

-- \o 'paytype-details.csv'
--   copy (SELECT  * FROM du_dms_fortellis_import.ro_paytype_details ORDER BY paytype ASC) TO stdout WITH (FORMAT csv, HEADER true);
SQL
}
function isolate_problematic_ros() {
    progress "Detecting Problematic ROs"
    psql_local --quiet <<'SQL'
    BEGIN;
    SET client_min_messages = 'WARNING';

    -- DELETE FROM du_dms_fortellis_model.etl_accounts
    -- WHERE coalesce("Refer" :: json ->> '_text',
    --            split_part("hostItemId", '*', 3)
    --   )
    --   NOT IN (
    --       SELECT "roNumber"
    --       FROM du_dms_fortellis_model.etl_head
    --   );

    INSERT INTO etl_problematic_ros("RONumber", comment)
    SELECT "roNumber", 'Opened Too Long Ago (' || "openDate" || ')'
      FROM etl_head
     WHERE "openDate"::date < DATE_TRUNC('month', CURRENT_DATE - INTERVAL '6 months')::DATE;

    INSERT INTO etl_problematic_ros("RONumber", comment)
    SELECT "roNumber", 'Unexpected RONumber Format'
      FROM etl_head
     WHERE "roNumber" !~ '^\d{1,9}[A-Z]{0,3}$';

    INSERT INTO etl_problematic_ros("RONumber", comment)
    SELECT "roNumber", 'voidedDate'
      FROM etl_head
     WHERE "voidedDate"::date IS NOT NULL;

    INSERT INTO etl_problematic_ros("RONumber", comment)
    SELECT "roNumber", 'Missing OpCode and PayType'
      FROM etl_labor
     WHERE "type" IS NULL
       AND "opCode" IS NULL;
      
SELECT assert_not_null(min("closedDate"::date), 'RO Numbers Likely In Unexpected Format') AS earliest_closed_date
      FROM etl_head
     WHERE "openDate"::date >= '2024-01-01'::date
       AND "voidedDate"::date IS NULL
       AND "roNumber" ~ '^\d{1,7}[A-Z]{0,3}$'
       AND "postedDate" IS NOT NULL -- garbage data doesn't seem to have this and all good data does
     \gset

    INSERT INTO etl_problematic_ros("RONumber", comment)
    SELECT "roNumber", 'Prior Open (' || "openDate" || ' < ' || :'earliest_closed_date' || ')'
      FROM etl_head
     WHERE "openDate"::date < (:'earliest_closed_date')::date;

    -- Mismatched Line Codes - Deductible
    INSERT INTO etl_problematic_ros("RONumber", comment)
    SELECT
        other."roNumber",
        'Unmatched linecode with dedLineCodes: ' || other."lineCodes"
    FROM
        (SELECT
            "roNumber",
            "lineCodes" 
        FROM etl_deductible) other
        LEFT JOIN etl_job job
        ON job."roNumber" = other."roNumber"
            AND job."lineCode" = other."lineCodes"
    WHERE job."lineCode" IS NULL AND lower(other."lineCodes") != 'ro';

    INSERT INTO etl_problematic_ros("RONumber", comment)
    SELECT "roNumber",
           'Split Line w/ Same lbrLaborType'
      FROM etl_labor
     GROUP BY "roNumber", "lineCode", "type", "sequenceNo"
    HAVING count(*) > 1;

    DELETE FROM etl_total
    WHERE "roNumber" IN (SELECT DISTINCT "RONumber" FROM etl_problematic_ros);

    -- DELETE FROM etl_tech
    -- WHERE "roNumber" IN (SELECT DISTINCT "RONumber" FROM etl_problematic_ros);

    DELETE FROM etl_tech_punch
    WHERE "roNumber" IN (SELECT DISTINCT "RONumber" FROM etl_problematic_ros);

    DELETE FROM etl_parts
    WHERE "roNumber" IN (SELECT DISTINCT "RONumber" FROM etl_problematic_ros);

    DELETE FROM etl_labor
    WHERE "roNumber" IN (SELECT DISTINCT "RONumber" FROM etl_problematic_ros);

    DELETE FROM etl_job
    WHERE "roNumber" IN (SELECT DISTINCT "RONumber" FROM etl_problematic_ros);

    -- DELETE FROM proxy_other
    -- WHERE "roNumber" IN (SELECT DISTINCT "RONumber" FROM etl_problematic_ros);

    -- DELETE FROM etl_cust_mail
    -- WHERE "roNumber" IN (SELECT DISTINCT "RONumber" FROM etl_problematic_ros);

    DELETE FROM etl_head
    WHERE "roNumber" IN (SELECT DISTINCT "RONumber" FROM etl_problematic_ros);

    SELECT * FROM etl_problematic_ros ORDER BY "RONumber";

    COMMIT;
SQL

    if [[ $? != 0 ]]; then
        yell "Aborting During Problematic RO Detection"
        return 1
    fi

    # Move the problematic ROs to dead-letter path

    PROBLEMATIC_ROS=$(psql_local --quiet --tuples-only --no-align --command 'SELECT DISTINCT "RONumber" FROM etl_problematic_ros ORDER BY "RONumber";')
    ROs="$(echo "$PROBLEMATIC_ROS" | tr -s "\n" " ")" #Replace \n with space (" ")
    ROs=${ROs::-1} # Remove trailing space (" ")
    if [[ ! -z "$ROs" ]]; then
        for RO in ""$ROs""
        do
            mv "$SINGLE_RO_JSON_DIR"/"$RO".json "$WORK_DIR_DEAD_LETTER"/"$RO".json
            prob_comment=$(psql_local -At --quiet --set=ronum="$RO" <<SQL
            SELECT string_agg(comment, ';' ORDER BY comment) FROM etl_problematic_ros WHERE "RONumber" = :'ronum'
SQL
)
            echo "${RO}: ${prob_comment}" >> "$WORK_DIR_DEAD_LETTER"/../dead-letter-detail.txt
        done
    fi
}


function append_ro_to_script() {
    local json_file="${1:?RO File Required}"
    local script_file="${2:?Script File Required}"
    
    if [[ -z "$1" || -z "$2" ]]; then
        echo "Error: Required parameters json_file and script_file are missing."
        exit 1
    fi
    
    local jq_file=$(dirname "$script_file")/$(basename "$json_file" .json).jq
    
    (
        cat "$json_file" > "$jq_file"

        echo "\set json_file_name '$json_file'" >> "$script_file"
        echo '\set jq_result `cat' "'$jq_file'" '| jq .`' >> "$script_file"

        cat >>"$script_file" <<'EOF'
        INSERT INTO etl_json ("RONumber", json_document) VALUES (:'json_file_name', :'jq_result');
        SELECT * FROM insert_head_data_to_model(:'json_file_name'::text, :'jq_result');
EOF
    )
}

function load_individual_json_files() {
    progress "Loading Individual Closed ROs (can take a while)"
    SCRIPT_WORK_DIR="$WORK_DIR"/json-load-script
    PSQL_SCRIPT_FILE="$WORK_DIR"/json-load-script/script.psql
    clear_dir "$SCRIPT_WORK_DIR"
    loop_count=1
    limit=500
    echo $(date)
    (
        cd "$SINGLE_RO_JSON_DIR" || die "Failed to CD to $SINGLE_RO_JSON_DIR"
        for json_file in ./*; do
            append_ro_to_script "$json_file" "$PSQL_SCRIPT_FILE"
            if [[ $(($loop_count% $limit)) = 0 ]]; then
                    progress "$(date +'%H:%M:%S') : Ready to Load next $limit ROs"
                    psql_local --quiet --file "$PSQL_SCRIPT_FILE" > /dev/null || die "Bulk SQL Load Failed"
                    truncate -s 0 "$PSQL_SCRIPT_FILE"
                    progress "$(date +'%H:%M:%S') : $loop_count ROs Imported"
            fi 
            ((loop_count++))
        done
        
        progress "Ready to Load remaining ROs"
        psql_local --quiet --file "$PSQL_SCRIPT_FILE" > /dev/null || die "Bulk SQL Load Failed"

        echo $(date)
        progress "Individual ROs Imported"
    ) || die "Failed to load split ROs"
}

function generate_jq_transforms() {
    progress "Generating JQ Transforms"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Head');"  > "$JQ_HEAD"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Deductible');"  > "$JQ_DEDUCTIBLE"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Job');"   > "$JQ_JOB"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Labor');" > "$JQ_LABOR"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Parts');" > "$JQ_PARTS"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Total');" > "$JQ_TOTAL"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Fees');" > "$JQ_FEES"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Mls');" > "$JQ_MLS"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Tech_Punch');" > "$JQ_TECH_PUNCH"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Discount');" > "$JQ_DISCOUNT"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Vehicle');" > "$JQ_VEHICLE"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Visit');" > "$JQ_VISIT"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Warranty');" > "$JQ_WARRANTY"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Pay');" > "$JQ_PAY"
}

function create_schema_from_model() {
    progress "Creating Schema from Model"
    psql_local --quiet \
               --set=DU_ETL_HOME="$DU_ETL_HOME" \
               >/dev/null \
<<'SQL'
SET client_min_messages = 'warning';
DROP SCHEMA  IF EXISTS  du_dms_fortellis_model CASCADE;
CREATE SCHEMA du_dms_fortellis_model;
SELECT set_config('search_path', 'du_dms_fortellis_model', true);

\i :DU_ETL_HOME/DU-Transform/client/process/create-client-functions.psql

CREATE TABLE etl_json (
    "RONumber" text NOT NULL PRIMARY KEY,
    json_document json
);

CREATE TABLE etl_model (
    id serial primary key,
    field_name text not null,
    is_array boolean null, --can only know if has_children is true
    has_children boolean not null,
    target_table text null --update nulls after loading
);

-- Table to store exceptional ROs
CREATE TABLE etl_problematic_ros (
    "RONumber" text NOT NULL,
    comment text
);

-- Table to store customer pay type filter
CREATE TABLE etl_pay_type_filter (
    label text,
    paytype text,
    insurance text,
    fromtype text,
    targettype text
);

\cd :DU_ETL_HOME/DU-DMS/DMS-Fortellis/data-dictionary

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-head.csv' with (format csv, header true)
update etl_model set target_table = 'Head' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-deductibles.csv' with (format csv, header true)
update etl_model set target_table = 'Deductible' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-job.csv' with (format csv, header true)
update etl_model set target_table = 'Job' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-labor.csv' with (format csv, header true)
update etl_model set target_table = 'Labor' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-parts.csv' with (format csv, header true)
update etl_model set target_table = 'Parts' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-totals.csv' with (format csv, header true)
update etl_model set target_table = 'Total' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-fees.csv' with (format csv, header true)
update etl_model set target_table = 'Fees' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-mls.csv' with (format csv, header true)
update etl_model set target_table = 'Mls' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-tech-punch.csv' with (format csv, header true)
update etl_model set target_table = 'Tech_Punch' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-discounts.csv' with (format csv, header true)
update etl_model set target_table = 'Discount' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-vehicle.csv' with (format csv, header true)
update etl_model set target_table = 'Vehicle' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-visitInspection.csv' with (format csv, header true)
update etl_model set target_table = 'Visit' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-warrantyList.csv' with (format csv, header true)
update etl_model set target_table = 'Warranty' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-pay.csv' with (format csv, header true)
update etl_model set target_table = 'Pay' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-other.csv' with (format csv, header true)
update etl_model set target_table = 'Other' where target_table IS NULL;

-- Mainly dynamic code builder functions
\i :DU_ETL_HOME/DU-DMS/DMS-Fortellis/src/parse/support-schema.psql

SELECT *, build_etl_table(target_table)
  FROM (SELECT DISTINCT target_table FROM etl_model) src;

SELECT *, build_etl_view(target_table)
  FROM (SELECT DISTINCT target_table FROM etl_model) src;

-- Insert datas to the tables
\i :DU_ETL_HOME/DU-DMS/DMS-Fortellis/src/parse/data-insert-schema.psql


CREATE TABLE IF NOT EXISTS du_dms_fortellis_model.etl_labor_type (
    "hostItemId"   text,
    "debitAcct"   text,
    "debitAcctTargetCo"   text,
    "gridAccountingFlag"   text,
    "gridAccountingName"   text,
    "calculateShopChargesFlag"   text,
    "inventoryDebitAcctFlag"   text,
    "journalSource"   text,
    "journalSourceCo"   text,
    "laborDetails"   text,
    "partsDebitTaxAcct"   text,
    "partsDebitTaxAcctTargetCo"   text,
    "lubeSaleAcct"   text,
    "miscSaleAcct"   text,
    "partPricingCode"   text,
    "partsSaleAcct"   text,
    "partsTaxCode"   text,
    "prompt4DebitAcct"   text,
    "prompt4DebitControl"   text,
    "prompt4JournalSource"   text,
    "prompt4LubeSaleAcct"   text,
    "prompt4MiscSaleAcct"   text,
    "prompt4PartsSaleAcct"   text,
    "prompt4SubletSaleAcct"   text,
    "subletPartsSaleAcct"   text,
    "subletSaleAcct"   text,
    "subletSaleAmountMarkupGridId"   text,
    "subletPartsTaxableFlag"   text
);

CREATE TABLE IF NOT EXISTS du_dms_fortellis_model.etl_account_coa (
    "hostItemId" text,
    "accountDesc" text,
    "accountNumber" text,
    "accountType" text,
    "accountSubType" text,
    "accountUpdate" text,
    "beginDate" text,
    "endDate" text,
    "controlTypeCode" text,
    "control2TypeCode" text,
    "companyId" text,
    "deptId" text,
    "franchiseId" text,
    "bfSchedule" text,
    "dfSchedule" text,
    "incBalReportGrp" text,
    "incBalReportGrpDesc" text,
    "incBalReportSubGrp" text,
    "incBalReportSubGrpDesc" text,
    "productivityTypeCode" text,
    "reportGrp" text,
    "reportGrpDesc" text,
    "uaReportGrp" text,
    "uaReportGrpDesc" text,
    "chain" text,
    "statCountType" text,
    "pattern" text,
    "postingDescFlag" text,
    "technicianFlag" text
);

CREATE TABLE IF NOT EXISTS du_dms_fortellis_model.etl_accounts (
    "hostItemId" text,
    "accountingDate" text,
    "accountNumber" text,
    "companyId" text,
    "controlNo" text,
    "controlNo2" text,
    "controlTypeCode" text,
    "controlType2Code" text,
    "currentMonth" text,
    "detailDescription" text,
    "distillControlTypeCode" text,
    "distillControlType2Code" text,
    "journalId" text,
    "postingAmount" text,
    "postingSequence" text,
    "postingTime" text,
    "productivityNo" text,
    "productivityNo2" text,
    "productivityNoType" text,
    "productivityNoType2" text,
    "referenceNo" text,
    "scheduleNumber" text,
    "statCount" text
);

SQL
}

function move_or_process_duplicate_ro() {
    # When processing a given RO#, whether the Processing script encountered
    # that said RO# previously in its cycle. If it has/does placing the now
    # current duplicate file into a "duplicates" directory while skipping the
    # processing. And perform a diff on the active and previous files to see
    # whether they are indeed the same (which they should be) and add a ".diff"
    # file to the duplicates directory if they are not.

    if [[ -f "$1"/"$2".json ]]; then
        mkdir -p "$JSON_CONVERSION_WORK_DIR"/duplicates
        diff "$3" "$1"/"$2".json > /dev/null
        if [[ $? -ne 0 ]]; then
            diff --suppress-common-lines "$3" "$1"/"$2".json > "$JSON_CONVERSION_WORK_DIR"/duplicates/"$2".json.diff
        fi
        mv "$3" "$JSON_CONVERSION_WORK_DIR"/duplicates/"$2".json
    else
        mv "$3" "$1"/"$2".json
    fi
}

function place_lines_into_their_own_files() {
    if [[ -z "$1" ]]; then
        echo "Error: File name is required."
        return 1
    fi

    input_file_with_path="$(pwd)/${1:?file name required}"
    dos2unix -q "$input_file_with_path"

    DUPLICATE_RO_JSON_DIR="$JSON_CONVERSION_WORK_DIR"/duplicates
    mkdir -p "$DUPLICATE_RO_JSON_DIR"

    export SINGLE_RO_JSON_DIR
    export DUPLICATE_RO_JSON_DIR

PRG_PERFORM_SPLIT=$(cat <<'PERL'
    use warnings;
    use strict;
    my $outdir = $ENV{'SINGLE_RO_JSON_DIR'};
    my $dupdir = $ENV{'DUPLICATE_RO_JSON_DIR'};
    while (<>) {
        if (/\"roNumber\":\s*\"([^\"]+)\"/) {
            my $ronum_from_line = $1;
            print "Extracted roNumber: $ronum_from_line\n"; # Debug statement
            
            if (-f "$outdir/$ronum_from_line.json") {
                open my $ROOUT, '>', "$dupdir/$ronum_from_line.json" or die "Cannot open file '$dupdir/$ronum_from_line.json': $!";
                print $ROOUT $_;
                close $ROOUT;
            } else {
                open my $ROOUT, '>', "$outdir/$ronum_from_line.json" or die "Cannot open file '$outdir/$ronum_from_line.json': $!";
                print $ROOUT $_;
                close $ROOUT;
            }
        } else {
            print "No match found for roNumber in line:\n$_\n"; # Debug statement
        }
    }
    exit;
PERL
)

perl -e "$PRG_PERFORM_SPLIT" "$input_file_with_path" || die "Failed to split"


}
