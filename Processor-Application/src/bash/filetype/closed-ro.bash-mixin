# Sourced by process-json and provides function and constants
# for dealing with the Fortellis Model JSON File

source "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/Processor-Application/src/bash/process-library.bash-mixin

function process_closed_ro_json() {
    if [[ -z "$1" ]]; then
        echo "Error: File name is required."
        return 1
    fi

    progress "Closed RO JSON"
    closed_ro_processing_log="$WORK_DIR_PROCESSING_LOG_DIR"/ClosedRO.proc
    # open a FD in order to minimize overhead since we are going to
    # be writing timestamps for each processed RO into the file
    exec 3>> "$closed_ro_processing_log"

    echo "$(date)"                     >&3
    echo "START_TIME=$(date '+%s.%N')" >&3

    if closed_ro_split_into_singles "$1"; then
        mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/ || die "Failed to move file to archive directory"
    else
        move_to_work_dead_letter "$1"
    fi

    exec 3>&-
}

function process_accounting_details_json() {
    mkdir -p "$JSON_CONVERSION_WORK_DIR"
    cd "$JSON_CONVERSION_WORK_DIR"
    echo "$(date) GL Loading Begin"
    cd "$ZIP_WORK_DIR"
    jq_filter=$(cat "$1" | sed -e s/\'//g -e 's/^[ ^t]*//;s/[ ^]*$//' | jq '.')
    psql_local << EOF
    INSERT INTO etl_accounts SELECT * FROM json_populate_recordset(null::etl_accounts,'$jq_filter');
EOF
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
}

function process_accounting_coa_json() {
    mkdir -p "$JSON_CONVERSION_WORK_DIR"
    cd "$JSON_CONVERSION_WORK_DIR"
    echo "$(date) COA Loading Begin"
    cd "$ZIP_WORK_DIR"
    jq_filter=$(cat "$1" | sed -e s/\'//g -e 's/^[ ^t]*//;s/[ ^]*$//' | jq '.')
    psql_local << EOF
    INSERT INTO etl_account_coa SELECT * FROM json_populate_recordset(null::etl_account_coa,'$jq_filter');
EOF
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
}

function process_labor_type_json() {
    mkdir -p "$JSON_CONVERSION_WORK_DIR"
    cd "$JSON_CONVERSION_WORK_DIR"
    echo "$(date) Labor Type Loading Begin"
    cd "$ZIP_WORK_DIR"
    jq_filter=$(cat "$1" | sed -e s/\'//g -e 's/^[ ^t]*//;s/[ ^]*$//' | jq '.data')
    psql_local << EOF
    INSERT INTO etl_labor_type SELECT * FROM json_populate_recordset(null::etl_labor_type,'$jq_filter');
EOF
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
}


function closed_ro_split_into_singles() {
    filename=${1:?file name required}
    input_file_with_path="$(pwd)/$(basename $filename)"
    echo "input_file_with_path: $input_file_with_path"
    progress "Beginning $PROCESSING_MODE processing in $JSON_CONVERSION_WORK_DIR $1" >&2
    (
         mkdir -p "$SINGLE_RO_JSON_DIR"
        cp "$1" "$JSON_CONVERSION_WORK_DIR"/
        echo "JSON_CONVERSION_WORK_DIR: $(ls $JSON_CONVERSION_WORK_DIR)"

        cd "$JSON_CONVERSION_WORK_DIR"
        RO_COUNT=$(closed_ro_get_repair_order_count $1)
        count_end=$(date +%s.%N)
        echo "$(date) Found # $RO_COUNT"
        {
            echo "SPLIT_END=$count_end"
            echo "SPLIT_COUNT=$RO_COUNT"
        } >&3
        echo "$(date) Transform Begin"
        loop_beg=$(date)

      
        jq -c '.data[]' "$1" > lined_repair_orders.json || die "JQ One-Line-Per-RO Tranformation Failed"
	

        place_lines_into_their_own_files lined_repair_orders.json || die "Failed to place json lines into individual files"

        rm lined_repair_orders.json
        rm $1

        echo "$(date) Transform End"
        loop_end=$(date)

        echo "Loop Beg: $loop_beg" >&3
        echo "Loop End: $loop_end" >&3
    ) || die "Split of Closed ROS into Singles Failed"
    return $?
}

function closed_ro_get_repair_order_count() {
    jq '.data | length' < "$1"
}
