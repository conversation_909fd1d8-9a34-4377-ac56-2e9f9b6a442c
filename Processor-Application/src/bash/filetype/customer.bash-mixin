# Sourced by process-xml and provides function and constants
# for dealing with the Automate Customer Json File

function process_customer_json() {
    progress "Customer JSON"
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
}

function process_customer_timings() {
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
}

function relocate_customer_request_log() {
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
}

function relocate_customer_response_log() {
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
}
