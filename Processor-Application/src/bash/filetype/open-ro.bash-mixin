# Sourced by process-json and provides function and constants
# for dealing with the Fortellis Model JSON File

source "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/Processor-Application/src/bash/process-library.bash-mixin

function process_open_ro_json() {
    progress "Open RO JSON"
    open_ro_processing_log="$WORK_DIR_PROCESSING_LOG_DIR"/OpenRO.proc
    # open a FD in order to minimize overhead since we are going to
    # be writing timestamps for each processed RO into the file
    exec 3>> "$open_ro_processing_log"

    echo "$(date)"                     >&3
    echo "START_TIME=$(date '+%s.%N')" >&3

    if open_ro_split_into_singles "$1"; then
        mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
    else
        move_to_work_dead_letter "$1"
    fi

    exec 3>&-
}

function open_ro_split_into_singles() {
    input_file_with_path="$(pwd)/${1:?file name required}"
    progress "Beginning $PROCESSING_MODE processing in $JSON_CONVERSION_WORK_DIR $1" >&2
    (
         mkdir -p "$SINGLE_RO_JSON_DIR"
        cp "$1" "$JSON_CONVERSION_WORK_DIR"/

        cd "$JSON_CONVERSION_WORK_DIR"

        RO_COUNT=$(open_ro_get_repair_order_count $1)
        count_end=$(date +%s.%N)
        echo "$(date) Found # $RO_COUNT"
        {
            echo "SPLIT_END=$count_end"
            echo "SPLIT_COUNT=$RO_COUNT"
        } >&3
        echo "$(date) Transform Begin"
        loop_beg=$(date)

        jq -c \
           ".[]" \
           $1 \
           > lined_repair_orders.json \
           || die "JQ One-Line-Per-RO Tranformation Failed"
	
	 
	

        place_lines_into_their_own_files lined_repair_orders.json || die "Failed to place json lines into individual files"

        rm lined_repair_orders.json
        rm $1

        echo "$(date) Transform End"
        loop_end=$(date)

        echo "Loop Beg: $loop_beg" >&3
        echo "Loop End: $loop_end" >&3
    ) || die "Split of Open ROS into Singles Failed"
    return $?
}

function open_ro_get_repair_order_count() {
    jq '.ShowRepairOrder.ShowRepairOrderDataArea.RepairOrder | length' < "$1"
}
