# Sourced by process-xml; provides processing implementation functions for iterating
# through the provided zip file.

NAME_JSONCONV=jsonconversions
NAME_EXT_ARC=extraction-archive
NAME_PROC_LOG=processing-log
NAME_EXTR_LOG=extraction-log
NAME_PROC_RST=processing-result
NAME_IMPORT_FILES=import-files

WORK_DIR_EXTRACTION_LOG_DIR="$ZIP_WORK_DIR"/"$NAME_EXTR_LOG"
WORK_DIR_EXTRACTION_ARCHIVE_DIR="$ZIP_WORK_DIR"/"$NAME_EXT_ARC"
WORK_DIR_PROCESSING_LOG_DIR="$ZIP_WORK_DIR"/"$NAME_PROC_LOG"
WORK_DIR_PROCESSING_RESULTS_DIR="$ZIP_WORK_DIR"/"$NAME_PROC_RST"
WORK_DIR_DEAD_LETTER="$ZIP_WORK_DIR"/dead-letter
WORK_DIR_IMPORT_FILES_DIR="$ZIP_WORK_DIR"/"$NAME_IMPORT_FILES"

EXCEPTION_TABLES_DIR='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception_tables'

CORE_RETURN_EXCEPTION_CSV_FILE_PATH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/corereturn_without_corecharge.csv'
CORE_CHARGE_EXCEPTION_CSV_FILE_PATH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/corecharge_without_corereturn.csv'
CORE_RETURN_NOT_EQUAL_TO_CORE_CHARGE_EXCEPTION_CSV_FILE_PATH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/corecharge_corereturn_mismatch.csv'
CORE_CHARGE_WITH_NO_SALE='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/corecharge_with_nosale.csv'
INVALID_CORE_COST_SALE_MISMATCH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/invalid_core_cost_sale_mismatch.csv'
INVALID_CORE_AMOUNT_MISMATCH='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/invalid_core_amount_mismatch.csv'
GL_MISSING_RO_LIST='/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/gl_missing_ro_list.csv'

function process_zip_file() {
    JSON_CONVERSION_WORK_DIR="$ZIP_WORK_DIR"/"$NAME_JSONCONV"
    SINGLE_RO_JSON_DIR="$JSON_CONVERSION_WORK_DIR"/single-ro-json

    echo "HALT_OVER_RIDE:${HALT_OVER_RIDE}"

    [[ -f "$INPUT_BUNDLE_ZIP" ]] || die "Zip file required for zip mode: $INPUT_BUNDLE_ZIP"
    say "Processing Input Zip Archive: $INPUT_BUNDLE_ZIP"
    prepAndUnzipGlobalInputZipFile

    create_schema_from_model                              || die "Could not initialize schema"
    
    generate_jq_transforms                                || die "Failed to create jq transforms"

    processZipFileContents                                || die "Content Processing Failed"

    load_individual_json_files                            || die "Could not load at least one json repair order"

#    isolate_problematic_ros                               || die "Problematic RO isolation failed"
#
#    enumerate_present_ro                                  || die "RO Number Enumeration Failed"
#
#    persist_and_export_data_and_schema "$ZIP_WORK_DIR"    || die "Persistence and exporting failed"
#
#    export_job_data_to_csv                                || die "Exporting job data Failed"
#
#    report_open_or_void_ros                               || die "Open/Void RO report generation failed"
#    generate_report_customer_pay_types                    || die "Customer paytype report generation failed"
#    
#    generate_report_total_discount                        || die "Total discount report generation failed"
#    generate_exception_report                             || die "Exception report generation failed"
#    # generate_core_exception_report                      || die "Exception core report generation failed"
#    check_missing_against_extraction                      || die "Failed to check missing against original extraction"
# #   if [[ "$EXCEPTION_ANALYSIS" = 'true' ]]; then
# #       generate_gl_report_for_analysis                   || die "Analysis GL report generation failed"
# #       generate_customerpay_report                       || die "Customer pay generation failed"
#  #      copy_analysis_table                               || die "analysis table generation failed"
#  #  fi

#    compress_extraction_directory
#    compress_jsonconversion_directory
#    compress_extractionlog_directory
#    compress_processinglog_directory
#    compress_or_remove_deadletter_directory
#    compress_import_files_directory

##    if [[ "$PERFORM_PROXY_RO_BUILD" = 'true' ]]; then
##        build_proxy_repair_orders "$ZIP_WORK_DIR" "$SHOW_ACCOUNTING_IN_PROXY" "$PORSCHE_STORE" "$DUAL_PROXY" "$BRAND"
##        extract_proxy_to_tsv "$ZIP_WORK_DIR"/proxy-invoice/text "$WORK_DIR_PROCESSING_RESULTS_DIR" || echo "Proxy extraction to tsv failed"
##        if [[ "$EXCEPTION_ANALYSIS" = 'true' ]]; then
##            extract_proxy "$ZIP_WORK_DIR"/proxy-invoice/text || echo "Proxy extraction for exception failed"
##        fi
##        compress_proxy_output "$ZIP_WORK_DIR"
##        if [[ "$DUAL_PROXY" = 'true' ]]; then
##            compress_proxy_v2_output "$ZIP_WORK_DIR"
##        fi
##    fi
##
##    if [[ "$EXCEPTION_ANALYSIS" = 'true' ]]; then
##        generate_analysis_report                              || die "analysis report generation failed"
##    fi
  #  distribute_and_clear_work
}
function processZipFileContents() {
    progress "Iterating Over Zip File Contents"
    export DU_ETL_DEBUG_TARGET="$WORK_DIR_PROCESSING_LOG_DIR"/Processing.log
    (
        cd "$ZIP_WORK_DIR"
        mkdir "$WORK_DIR_EXTRACTION_LOG_DIR"
        mkdir "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"
        mkdir "$WORK_DIR_PROCESSING_LOG_DIR"
        mkdir "$WORK_DIR_PROCESSING_RESULTS_DIR"
        mkdir "$WORK_DIR_DEAD_LETTER"

        for filename in ./*; do
            case "$(basename $filename)" in
                extraction-log)     : ;;
                extraction-archive) : ;;
                processing-log)     : ;;
                processing-result)  : ;;
                dead-letter)        : ;;                
                *.json)	            process_closed_ro_json			                   "$filename" ;;
                *.err)             	relocate_to_log_dir_and_log_error_die              "$filename" ;;
                *)                 yell "File $filename Not Recognized"                            ;;
            esac
            proc_result=$?
            if [[ "$proc_result" != 0 ]]; then
                die "File Processing Failed"
            fi
        done
    ) || die "Zip File Processing Failed"
    export DU_ETL_DEBUG_TARGET=/dev/null
    return $?
}

function relocate_to_log_dir() {
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
}

function relocate_to_extraction_dir() {
    mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
}

function relocate_to_log_dir_and_log_error_die(){
    echo "$1 Error content is"
    head -20 $1
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
    die  "Error File Present: $1"
}

function prepAndUnzipGlobalInputZipFile() {
    progress "Unzipping Input to Work"
    mkdir -p "$ZIP_WORK_DIR"
    clear_dir "$ZIP_WORK_DIR"
    # junk the input directories since none are present for the initial import
    # and re-processing the extraction archive is much simpler if the embedded directory
    # that is part of the zip file be handled transparently.
    unzip -j -q "$INPUT_BUNDLE_ZIP" -d "$ZIP_WORK_DIR"
}

function compress_extraction_directory() {
(
    cd "$ZIP_WORK_DIR"
    zip -q -mr "$NAME_EXT_ARC".zip ./"$NAME_EXT_ARC"
)
}

function compress_jsonconversion_directory() {
(
    if [[ -d "$JSON_CONVERSION_WORK_DIR" ]]; then
        cd "$ZIP_WORK_DIR"
        zip -q -mr "$NAME_JSONCONV".zip ./"$NAME_JSONCONV"
    fi
)
}

function compress_processinglog_directory() {
(
    cd "$ZIP_WORK_DIR"
    zip -q -mr "$NAME_PROC_LOG".zip ./"$NAME_PROC_LOG"
)
}

function compress_extractionlog_directory() {
(
    cd "$ZIP_WORK_DIR"
    zip -q -mr "$NAME_EXTR_LOG".zip ./"$NAME_EXTR_LOG"
)
}

function compress_or_remove_deadletter_directory() {
(
    if has_files "$WORK_DIR_DEAD_LETTER"; then
        cd "$ZIP_WORK_DIR"
        zip -q -mr 'dead-letter.zip' ./'dead-letter'
    else
        rmdir "$WORK_DIR_DEAD_LETTER"
    fi
)
}

function compress_import_files_directory() {
(
    cd "$ZIP_WORK_DIR"
    zip -q -jmr "$NAME_IMPORT_FILES".zip ./"$NAME_IMPORT_FILES"
    rmdir ./"$NAME_IMPORT_FILES"
)
}

function distribute_and_clear_work() {
    progress "Moving Work to Bundle Directory"
    if [[ "$PERFORM_ZIP" = 'true' ]]; then
        distribute_as_zip_file
    else
        distribute_as_directory
    fi
    progress "Distribute Function Result: $?"
    clear_dir "$ZIP_WORK_DIR"
}

function distribute_as_zip_file() {
(
    cd "$ZIP_WORK_DIR"
    zip -qr "$WORK_DIR"/"$INPUT_ZIP_FILE_NAME" ./*
    mv "$WORK_DIR"/"$INPUT_ZIP_FILE_NAME" "${BUNDLE_OUTPUT_DIRECTORY:?Specify bundle directory first}"/"${OUTPUT_FILE_PREFIX}""$(basename $INPUT_ZIP_FILE_NAME)"
)
return $?
}

function distribute_as_directory() {
(
    cd "$ZIP_WORK_DIR"/..
    mv ./"$(basename $ZIP_WORK_DIR)" \
       "${BUNDLE_OUTPUT_DIRECTORY:?Specify bundle directory first}"/"$(basename $INPUT_ZIP_FILE_NAME .zip)"
)
return $?
}

function process_session_log() {
    mv "$1" "$WORK_DIR_EXTRACTION_LOG_DIR"/
}

function move_to_work_dead_letter() {
    mkdir -p "$WORK_DIR_DEAD_LETTER"
    mv "$1" "$WORK_DIR_DEAD_LETTER"
}

function enumerate_present_ro() {
    progress "Enumerating ROs"
    (
        cd "$SINGLE_RO_JSON_DIR"
        find . -type f -exec basename {} .json \; | sort > "$WORK_DIR_PROCESSING_RESULTS_DIR"/single-ro-json-present-list.txt
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;
        DROP TABLE IF EXISTS present_repair_order_number;
        DROP TABLE IF EXISTS repair_order_number_summary;
        CREATE TABLE present_repair_order_number (
            invoice_number text primary key
        );
        \copy present_repair_order_number from single-ro-json-present-list.txt with (format csv, header false)
        select count(*) AS count_all, count(*) filter (where invoice_number !~ '^\d+$') AS count_non_numeric
          from present_repair_order_number;

        CREATE UNLOGGED TABLE repair_order_number_summary AS
        WITH
        split_ranges AS (
             SELECT min(invoice_number::int) AS first_invoicenumber, max(invoice_number::int) AS last_invoicenumber
               FROM present_repair_order_number
              WHERE invoice_number ~ '^\d+$'
        ),
        explode_invoice_numbers AS (
              SELECT generate_series(first_invoicenumber, last_invoicenumber, 1) AS invoice_number
                FROM split_ranges
        ),
        present AS (
            SELECT invoice_number::int, true AS is_present
              FROM present_repair_order_number
             WHERE invoice_number ~ '^\d+$'
        ),
        combined AS (
            SELECT *, invoice_number::integer / 100 AS inv_grp FROM explode_invoice_numbers NATURAL LEFT JOIN present
        )
        SELECT
          (sum("Present #") OVER () / sum("Total #") OVER ())::numeric(10,2) AS "Overall %"
        , sum("Present #") OVER () AS "Included #"
        , sum("Total #") OVER () AS "Overall #"
        , ("Present #" / "Total #"::numeric)::numeric(10,2) AS "Tranche %"
        , *
        FROM (
            SELECT
                  sum(CASE WHEN is_present IS TRUE THEN 1 ELSE 0 END) AS "Present #"
                , sum(CASE WHEN is_present IS TRUE THEN 0 ELSE 1 END) AS "Missing #"
                , max(invoice_number) - min(invoice_number) + 1 AS "Total #"
                , min(invoice_number) AS "Starting"
                , max(invoice_number) AS "Ending"
                , array_agg(DISTINCT CASE WHEN is_present IS NOT TRUE
                                          THEN invoice_number
                                          ELSE NULL END) AS "Missing Invoices"
                , inv_grp AS "Inv Grp ID"
            FROM combined
            GROUP BY inv_grp
            ORDER BY inv_grp
        ) overall_calc
        ;

        \o 'repair-order-sequence-non-numeric.txt'
        \C 'Non-Numeric Repair Order Numbers'
        SELECT * FROM present_repair_order_number WHERE invoice_number !~ '^\d+$';

        \o repair-order-sequence-summary.txt
        \C 'Numeric-only Repair Order Number Summary'
        SELECT "Overall %", "Included #", "Overall #",
               "Tranche %", "Present #", "Missing #", "Total #",
               "Starting", "Ending"
          FROM repair_order_number_summary
         WHERE "Present #" > 0;

        \C
        \t
        \pset format unaligned
        \o 'repair-order-sequence-missing-numerics.txt'
        SELECT invoice_number
          FROM (
            SELECT unnest("Missing Invoices") AS invoice_number
              FROM repair_order_number_summary
             WHERE "Present #" > 0
             ORDER BY 1
               ) AS src
         WHERE invoice_number IS NOT NULL;

        \o
SQL
        rm single-ro-json-present-list.txt

        "$DU_ETL_HOME"/invoice-manipulation-common/summarize-ro-list \
            repair-order-sequence-missing-numerics.txt \
            > repair-order-sequence-missing-numerics-summary.txt

    )
}

function check_missing_against_extraction() {
    progress "Checking for Missing ROs in Original Raw Data"
    (
        cd "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"
        grep -o \
             -f "$WORK_DIR_PROCESSING_RESULTS_DIR"/repair-order-sequence-missing-numerics.txt \
             ./* \
             > "$WORK_DIR_PROCESSING_RESULTS_DIR"/missing-lookup-in-extraction.txt
        if [[ $? != 0 ]]; then
            progress "No missing RO#s found in extraction data"
        else
            yell "At least one missing RO# found in extraction data (false positives possible)"
        fi
    )
}

function report_open_or_void_ros() {
    (
        progress "Detecting Open/Void RO data and reporting"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;

        \o 'repair-order-sequence-open-list.txt'
        SELECT "RONumber","OpenDate", "StatusDesc"
          FROM du_dms_fortellis_model.etl_head
         WHERE "ClosedDate" IS NULL
         ORDER BY "RONumber";

        \o 'repair-order-sequence-closed-by-date-then-ro.txt'
        \t
        \pset format unaligned
        SELECT "RONumber", "ClosedDate", "OpenDate", "StatusDesc"
          FROM du_dms_fortellis_model.etl_head
         WHERE "ClosedDate" IS NOT NULL
         ORDER BY "ClosedDate", "RONumber";

        \o 'repair-order-sequence-closed-by-ro-then-date.txt'
        SELECT "RONumber", "ClosedDate", "OpenDate", "StatusDesc"
          FROM etl_head
         WHERE "ClosedDate" IS NOT NULL
         ORDER BY "RONumber";
           
        \o 'invoice-master.csv'
        COPY (
             WITH inv_master AS (SELECT
                        substring("RONumber" FROM '^[0-9]+')::integer    AS number_part,
                        substring("RONumber" FROM '[A-Za-z]+$')          AS code_part
                    FROM etl_head
		)
		, start_end (min_ro, max_ro) AS (
                    SELECT
                        min(number_part) :: integer,
                        max(number_part) :: integer
                    FROM inv_master
		)
		SELECT *
		FROM(
         		SELECT
            		"RONumber",
             		CASE WHEN "ClosedDate" IS NOT NULL
                 		THEN 'Closed'
             		ELSE 'Open'
		             END                           AS "Status",
	             "OpenDate",
        	     "ClosedDate",
	             "Year",
	             "MakeDesc"                    AS "Make",
        	     "ModelDesc"                   AS "Model",
	             "VIN",
        	     "CustNo"                      AS "Customer",
	             "ServiceAdvisor"              AS "Advisor",
        	     COALESCE("StatusDesc", 'WIP') AS "Sub-Status"
	         FROM etl_head
        	 UNION ALL
		         SELECT
		             ro_num :: text              AS "RONumber",
		             'Voided'                    AS "Status",
		             NULL,
        		     NULL,
		             NULL,
		             NULL,
		             NULL,
		             NULL,
		             NULL,
		             NULL,
		             NULL
		         FROM start_end,
                	     generate_series(min_ro, max_ro) gs (ro_num)
		         WHERE NOT EXISTS(SELECT 1
                		          FROM etl_head
		                          WHERE substring(etl_head."RONumber" FROM '^[0-9]+')::integer = ro_num ::integer)
					     ) ua
			ORDER BY substring("RONumber" FROM '^[0-9]+')::integer 
        ) TO stdout WITH (FORMAT csv, HEADER true);
     
        \o 'coa-report.csv'
	COPY (
    		SELECT
        	    TRIM(regexp_replace("AccountNumber" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                         'g')) AS account_number,
        	    TRIM(regexp_replace("accountDesc" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                         'g')) AS account_description
    		FROM du_dms_fortellis_model.etl_account_coa
	) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );
      
      \o 'ro_with_no_sale_cost_in_gl.csv'
	COPY (
        WITH ro AS(
	SELECT DISTINCT
    	REPLACE((d."Refer"::json->'_text')::text,'"','')              AS "RO#"
    FROM du_dms_fortellis_model.etl_accounts d
    	JOIN du_dms_fortellis_model.etl_account_coa c
            ON (d."AccountNumber"::json->'_text')::text = (c."AccountNumber"::json->'_text')::text
    WHERE REPLACE((c."AccountType"::json->'_text')::text, '"','')  IN ('S','C') 
    ORDER BY REPLACE((d."Refer"::json->'_text')::text,'"','')
    )
    , ro_all AS(
    	SELECT DISTINCT
    		REPLACE((d."Refer"::json->'_text')::text,'"','')              AS "RO#"
    	FROM du_dms_fortellis_model.etl_accounts d
    )
    ,ro_without AS(
    	SELECT a."RO#" 
    	FROM ro_all a
    		LEFT JOIN ro r 
    			ON a."RO#"=r."RO#"
    	WHERE r."RO#" IS NULL
    )
    SELECT 
    	ro."RO#",
        string_agg(REPLACE((c."AccountType"::json->'_text')::text, '"',''),', ')   AS "AccountType" ,
    	string_agg(REPLACE((d."PostingAmount"::json->'_text')::text, '"',''),', ') AS "Amount",
        string_agg(REPLACE((d."JournalID"::json->'_text')::text, '"',''),', ')     AS "JournalID"
    FROM ro_without ro 
    	JOIN du_dms_fortellis_model.etl_accounts d
    		ON REPLACE((d."Refer"::json->'_text')::text,'"','') =ro."RO#"
        JOIN du_dms_fortellis_model.etl_account_coa c
            ON (d."AccountNumber"::json->'_text')::text = (c."AccountNumber"::json->'_text')::text
    GROUP BY ro."RO#"
    ORDER BY ro."RO#"
	) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

       \o 'GL-Accounting_report.csv'
    COPY (
	   WITH coa_details AS (SELECT
                         TRIM(regexp_replace("AccountNumber" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                             'g')) AS accountnumber,
                         TRIM(regexp_replace("accountDesc" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                             'g')) AS accountdescription,
                         TRIM(regexp_replace("AccountType" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                             'g')) AS accounttype,
                         TRIM(regexp_replace("incBalReportGrpDesc" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                             'g')) AS incbalgrpdesc,
                         TRIM(regexp_replace("IncBalSubGrp" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                             'g')) AS incbalsubgrp
                     FROM du_dms_fortellis_model.etl_account_coa
	 ),
         gl_details AS (SELECT
                           TRIM(regexp_replace("HostItemID" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS hostitemid,
                           TRIM(regexp_replace("AccountingDate" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS accountingdate,
                           TRIM(regexp_replace("AccountNumber" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS accountnumber,
                           split_part("HostItemID" :: json ->> '_text', '*', 3) AS invoice,
                           TRIM(regexp_replace("CompanyID" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS companyid,
                           TRIM(regexp_replace("Control" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS control,
                           TRIM(regexp_replace("JournalID" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS journalid,
                           TRIM(regexp_replace("PostingAmount" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingamount,
                           TRIM(regexp_replace("PostingSequence" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingsequence,
                           TRIM(regexp_replace("PostingTime" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS postingtime,
                           TRIM(regexp_replace("Refer" :: json ->> '_text', E'[\\n\t\r]+', ' ',
                                               'g'))                            AS refer
                       FROM du_dms_fortellis_model.etl_accounts
    	)
	SELECT
            hostitemid AS "Host Item ID",
    	    invoice AS "#Invoice",
    	    accountingdate AS "Accounting Date",
    	    accountnumber AS "Account Number",
    	    accountdescription AS "Acccount Description",
    	    accounttype AS "Account Type",
    	    incbalgrpdesc AS "incBalReportGrpDesc",
    	    incbalsubgrp AS "IncBalSubGrp",
    	    companyid AS "Company ID",
    	    control AS "Control",
    	    journalid AS "Journal ID",
    	    postingamount AS "Posting Amount",
    	    postingsequence AS "Posting Sequence",
    	    postingtime AS "Posting Time"
        FROM gl_details
    		LEFT JOIN coa_details USING (accountnumber)
 ) TO STDOUT WITH (FORMAT CSV, HEADER TRUE );

 \o 'exception_cust_amount_notmatch_with_gl.csv'
    COPY(
        WITH pay_summary AS (
            SELECT "RONumber"::text, SUM("payPaymentAmount"::numeric)::text AS "payPaymentAmount"
            FROM du_dms_fortellis_model.etl_pay 
            WHERE "payInsuranceFlag" = 'N'
            GROUP BY "RONumber" HAVING SUM("payPaymentAmount"::numeric) > 0
        ), matching_cust_pay_amt AS (
            SELECT
                d."AccountNumber"::json->>'_text'       AS "AccountNumber",
                d."JournalID"::json->>'_text'           AS "JournalID",
                d."PostingAmount"::json->>'_text'       AS "PostingAmount",
                d."PostingTime"::json->>'_text'         AS "PostingTime",
                d."Refer"::json->>'_text'               AS "Refer",
                ps. "payPaymentAmount"
            FROM du_dms_fortellis_model.etl_accounts d JOIN pay_summary ps
            ON ps."RONumber"::text = d."Refer"::json->>'_text'::text AND d."PostingAmount"::json->>'_text' = ps."payPaymentAmount"
        )
        SELECT  "RONumber" AS "RO Number", "payPaymentAmount" AS "Customer Pay Amount" FROM pay_summary WHERE "RONumber" NOT IN (
            SELECT DISTINCT "Refer" FROM matching_cust_pay_amt
        ) ORDER BY "RONumber"
    ) TO STDOUT WITH (FORMAT csv, HEADER true);

  \o 'corereturn_without_corecharge.csv'
    COPY(with core_charge as (
            select
                *
            from
                du_dms_fortellis_model.etl_parts
            where
                "prtCoreSale" :: numeric != 0
                or "prtCoreCost" :: numeric != 0
        ),
        core_return as (
            select
                *
            from
                du_dms_fortellis_model.etl_parts
            where
                "prtQtySold" :: numeric < 0
                 AND "prtDesc" like '%CORE%'
        ),
        exp_return as(
            select
                *
            From
                core_return r
            where
                not exists(
                    select
                        *
                    from
                        core_charge c
                    where
                        c."RONumber" = r."RONumber"
                        and c."prtLineCode" = r."prtLineCode"
                )
        )
        SELECT
            "RONumber"    AS "RO Number",
            "prtLineCode" AS "Line",
            "prtPartNo"   AS "Part No" 
        FROM
            exp_return
        UNION
        ALL
        SELECT
            ' Exception Count: ' || (
                SELECT
                    count(*)
                FROM
                    exp_return
            ),
            ' Total RO: ' || (
                SELECT
                    count(DISTINCT "RONumber")
                FROM
                    du_dms_fortellis_model.etl_head
            ),
            'Exception Percentage: ' || Round(
                (
                    (
                        (
                            select
                                count(*)
                            FROM
                                exp_return
                        ) * 100
                    ) :: numeric / (
                        SELECT
                            count(DISTINCT "RONumber")
                        FROM
                            du_dms_fortellis_model.etl_head
                    ) :: numeric
                ),
                2
            ) || '%'
        ) TO stdout WITH (FORMAT csv, HEADER true);


     \o 'corecharge_without_corereturn.csv'
        COPY (
             with core_charge as (
            select
                *
            from
                du_dms_fortellis_model.etl_parts
            where
                "prtCoreSale" :: numeric != 0
                or "prtCoreCost" :: numeric != 0
        ),
        core_return as (
            select
                *
            from
                du_dms_fortellis_model.etl_parts
            where
                "prtQtySold" :: numeric < 0
                AND "prtDesc" like '%CORE%'
        ),
        exp_charge as(
            select
                *
            From
                core_charge c
            where
                not exists(
                    select
                        *
                    from
                        core_return r
                    where
                        r."RONumber" = c."RONumber"
                        and r."prtLineCode" = c."prtLineCode"
                )
        )
        SELECT
            "RONumber"    AS "RO Number",
            "prtLineCode" AS "Line",
            "prtPartNo"   AS "Part No" 
        FROM
            exp_charge
        UNION
        ALL
        SELECT
            'Exception Count: ' || (
                SELECT
                    count(*)
                FROM
                    exp_charge
            ),
            'Total RO: ' || (
                SELECT
                    count(DISTINCT "RONumber")
                FROM
                    du_dms_fortellis_model.etl_head
            ),
            'Exception Percentage: ' || Round(
                (
                    (
                        (
                            select
                                count(*)
                            FROM
                                exp_charge
                        ) * 100
                    ) :: numeric / (
                        SELECT
                            count(DISTINCT "RONumber")
                        FROM
                            du_dms_fortellis_model.etl_head
                    ) :: numeric
                ),
                2
            ) || '%'
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'corecharge_corereturn_mismatch.csv'
        COPY (
             WITH parts_details AS(  
                SELECT
                    "RONumber",
                    "prtLineCode",
                    "prtPartNo",
                    CASE 
                    WHEN "prtQtySold" :: numeric < 0 AND lower("prtDesc") like '%core%'
                        THEN  'Core Return'
                    ELSE 'Part Number' END                                           AS classification,
                    "prtSale"::numeric * ceil("prtQtySold"::numeric)::integer        AS sale,
                    "prtCost"::numeric * ceil("prtQtySold"::numeric)::integer        AS cost,
                    "prtCoreSale"::numeric * ceil("prtQtySold"::numeric)::integer    AS core_charge_sale,
                    "prtCoreCost"::numeric * ceil("prtQtySold"::numeric)::integer    AS core_charge_cost
                FROM du_dms_fortellis_model.etl_parts
            )
            , core_return AS (
                SELECT
                    "RONumber",
                    "prtLineCode",
                    "prtPartNo",
                    abs(SUM(sale))                                                        AS core_return_sale,
                    abs(SUM(cost))                                                        AS core_return_cost
                FROM parts_details
                WHERE classification = 'Core Return'
                GROUP BY 1, 2, 3
            )
            , parts_with_core_charge AS (
                SELECT
                    "RONumber",
                    "prtLineCode",
                    "prtPartNo",
                    abs(SUM(sale))                                                        AS sale,
                    abs(SUM(cost))                                                        AS cost,
                    abs(SUM(core_charge_sale))                                            AS core_charge_sale,
                    abs(SUM(core_charge_cost))                                            AS core_charge_cost
                FROM parts_details
                WHERE classification = 'Part Number'
                    AND core_charge_sale != 0
                    AND core_charge_cost != 0
                GROUP BY 1, 2, 3
            ), final_result AS (SELECT 
                parts_with_core_charge."RONumber",
                "prtLineCode",
                parts_with_core_charge."prtPartNo",
                sale,
                cost,
                core_charge_sale,
                core_charge_cost,
                core_return_sale,
                core_return_cost
            FROM parts_with_core_charge
                LEFT JOIN core_return USING("RONumber", "prtLineCode")
            WHERE 
             core_return."prtPartNo" LIKE '%' ||parts_with_core_charge."prtPartNo" ||'%' AND
   			 (abs(core_return_sale) != abs(core_charge_sale)OR abs(core_return_cost) != abs(core_charge_cost)))
   			 
   			 SELECT "RONumber" AS "RO Number",
                "prtLineCode" AS "Line",
                "prtPartNo" AS "Part No.",
                sale::text AS "Part Sale",
                cost::text AS "Part Cost",
                core_charge_sale::text AS "C. Charge Sale",
                core_charge_cost::text AS "C. Charge Cost",
                core_return_sale::text AS "C. Return Sale",
                core_return_cost::text AS "C. Return Cost" FROM final_result
   			 UNION ALL 
   			 SELECT 'Exception Count: ' || (SELECT COUNT(*) FROM  final_result ),
                	'Total RO: ' || ( SELECT count(DISTINCT "RONumber") FROM du_dms_fortellis_model.etl_head),
                	'Exception Percentage: ' || Round(((
                            (
                                SELECT
                                    COUNT(*)
                                FROM
                                    FINAL_RESULT
                            ) * 100
                        ) :: numeric / (
                            SELECT
                                COUNT(DISTINCT "RONumber")
                            FROM
                                du_dms_fortellis_model.etl_head
                        ) :: numeric
                    ),
                    2
                ) || '%', '','','','','',''
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'corecharge_with_nosale.csv'
        copy (SELECT "RONumber" AS "RO Number", "prtPartNo" AS "Part No" FROM du_dms_fortellis_model.etl_parts_detail LEFT JOIN du_dms_fortellis_model.etl_head AS eth USING("RONumber") WHERE coalesce(nullif(trim("prtCoreSale"), '')::numeric, 0) = 0 AND coalesce(nullif(trim("prtCoreCost"), '')::numeric, 0) > 0 AND eth."ClosedDate" IS NOT NULL ORDER BY "RONumber") TO stdout WITH (FORMAT csv, HEADER true);

        \o 'invalid_core_cost_sale_mismatch.csv'
        COPY (
             WITH parts_details AS(  
                SELECT
                    "RONumber",
                    "prtLineCode",
                    "prtPartNo",
                    CASE 
                    WHEN "prtQtySold" :: numeric < 0 AND lower("prtDesc") like '%core%'
                        THEN  'Core Return'
                    ELSE 'Part Number' END                                           AS classification,
                    "prtSale"::numeric * ceil("prtQtySold"::numeric)::integer        AS sale,
                    "prtCost"::numeric * ceil("prtQtySold"::numeric)::integer        AS cost,
                    "prtCoreSale"::numeric * ceil("prtQtySold"::numeric)::integer    AS core_charge_sale,
                    "prtCoreCost"::numeric * ceil("prtQtySold"::numeric)::integer    AS core_charge_cost
                FROM du_dms_fortellis_model.etl_parts
            )
            , core_return AS (
                SELECT
                    "RONumber",
                    "prtLineCode",
                    "prtPartNo",
                    abs(SUM(sale))                                                        AS core_return_sale,
                    abs(SUM(cost))                                                        AS core_return_cost
                FROM parts_details
                WHERE classification = 'Core Return'
                GROUP BY 1, 2, 3
            )
            , parts_with_core_charge AS (
                SELECT
                    "RONumber",
                    "prtLineCode",
                    "prtPartNo",
                    abs(SUM(sale))                                                        AS sale,
                    abs(SUM(cost))                                                        AS cost,
                    abs(SUM(core_charge_sale))                                            AS core_charge_sale,
                    abs(SUM(core_charge_cost))                                            AS core_charge_cost
                FROM parts_details
                WHERE classification = 'Part Number'
                    AND core_charge_sale != 0
                    AND core_charge_cost != 0
                GROUP BY 1, 2, 3
            )
            SELECT 
                "RONumber" AS "RO Number",
                "prtLineCode" AS "Line",
                parts_with_core_charge."prtPartNo" AS "Part No.",
                sale AS "Part Sale",
                cost AS "Part Cost",
                core_charge_sale AS "C. Charge Sale",
                core_charge_cost AS "C. Charge Cost",
                core_return_sale AS "C. Return Sale",
                core_return_cost AS "C. Return Cost"
            FROM parts_with_core_charge
                LEFT JOIN core_return USING("RONumber", "prtLineCode")
            WHERE 
            core_return."prtPartNo" LIKE '%' ||parts_with_core_charge."prtPartNo" ||'%' AND
            (core_return_sale != core_return_cost 
                OR core_charge_sale != core_charge_cost)  
        ) TO stdout WITH (FORMAT csv, HEADER true);

        \o 'invalid_core_amount_mismatch.csv'
        COPY (
            WITH parts_details AS(  
                SELECT
                    "RONumber",
                    "prtLineCode",
                    "prtPartNo",
                    CASE 
                    WHEN "prtQtySold" :: numeric < 0 AND lower("prtDesc") like '%core%'
                        THEN  'Core Return'
                    ELSE 'Part Number' END                                           AS classification,
                    "prtSale"::numeric * ceil("prtQtySold"::numeric)::integer        AS sale,
                    "prtCost"::numeric * ceil("prtQtySold"::numeric)::integer        AS cost,
                    "prtCoreSale"::numeric * ceil("prtQtySold"::numeric)::integer    AS core_charge_sale,
                    "prtCoreCost"::numeric * ceil("prtQtySold"::numeric)::integer    AS core_charge_cost
                FROM du_dms_fortellis_model.etl_parts
            )
            , core_return AS (
                SELECT
                    "RONumber",
                    "prtLineCode",
                    "prtPartNo",
                    abs(SUM(sale))                                                        AS core_return_sale,
                    abs(SUM(cost))                                                        AS core_return_cost
                FROM parts_details
                WHERE classification = 'Core Return'
                GROUP BY 1, 2, 3
            )
            , parts_with_core_charge AS (
                SELECT
                    "RONumber",
                    "prtLineCode",
                    "prtPartNo",
                    abs(SUM(sale))                                                        AS sale,
                    abs(SUM(cost))                                                        AS cost,
                    abs(SUM(core_charge_sale))                                            AS core_charge_sale,
                    abs(SUM(core_charge_cost))                                            AS core_charge_cost
                FROM parts_details
                WHERE classification = 'Part Number'
                    AND core_charge_sale != 0
                    AND core_charge_cost != 0
                GROUP BY 1, 2, 3
            )
            SELECT 
                "RONumber" AS "RO Number",
                "prtLineCode" AS "Line",
                parts_with_core_charge."prtPartNo" AS "Part No.",
                sale AS "Part Sale",
                cost AS "Part Cost",
                core_charge_sale AS "C. Charge Sale",
                core_charge_cost AS "C. Charge Cost",
                core_return_sale AS "C. Return Sale",
                core_return_cost AS "C. Return Cost"
            FROM parts_with_core_charge
                LEFT JOIN core_return USING("RONumber", "prtLineCode")
            WHERE 
            (core_return."prtPartNo" LIKE '%' ||parts_with_core_charge."prtPartNo" ||'%') AND
            (NOT COALESCE(core_return_sale = core_charge_sale, TRUE) 
                OR NOT COALESCE(core_return_cost = core_charge_cost, TRUE))
        ) TO stdout WITH (FORMAT csv, HEADER true);


  \o 'gl_missing_ro_list.csv'
        COPY ( 
                with proxy_data AS (  SELECT
                           lbr."RONumber"                                           AS ro_number,
                           sum(coalesce(nullif("lbrSale", '') :: numeric, 0)) * 100 AS sale_amount,
                           sum(coalesce(nullif("lbrCost", '') :: numeric, 0)) * 100 AS cost_amount,
                           left(lbr."lbrLaborType", 1)                              AS prt_billing_type
                       FROM etl_labor lbr
                           JOIN etl_job job
                               ON lbr."RONumber" = job."RONumber"
                                  AND lbr."lbrLineCode" = job."linLineCode"
                       GROUP BY lbr."RONumber", left(lbr."lbrLaborType", 1)
                       HAVING sum(coalesce(nullif("lbrSale", '') :: numeric, 0)) * 100 != 0
			 OR sum(coalesce(nullif("lbrCost", '') :: numeric, 0)) * 100 != 0
)
    ,part_data AS (  SELECT
                         "RONumber"                                                     AS ro_number,
                         left("prtLaborType", 1)                                        AS prt_billing_type,
                         sum(coalesce(nullif(trim("prtQtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("prtSale"), '') :: numeric, 0)) * 100 AS sale_amount,
                         sum(coalesce(nullif(trim("prtQtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("prtCost"), '') :: numeric, 0)) * 100 AS cost_amount
                     FROM etl_parts
                     GROUP BY "RONumber", left("prtLaborType", 1)
                     HAVING  sum(coalesce(nullif(trim("prtQtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("prtSale"), '') :: numeric, 0)) * 100 != 0
			 OR sum(coalesce(nullif(trim("prtQtySold"), '') :: numeric, 0) *
                             coalesce(nullif(trim("prtCost"), '') :: numeric, 0)) * 100 != 0
)
    ,tax_data AS ( SELECT
                       "RONumber"                       AS ro_number,
                       left("totPayType", 1)            AS prt_billing_type,
                       sum("totRoTax" :: numeric) * 100 AS sale_amount
                   FROM etl_total
                   GROUP BY "RONumber", left("totPayType", 1)
                   HAVING sum("totRoTax" :: numeric) * 100 != 0
)

	SELECT "RONumber", "OpenDate", "ClosedDate", "VoidedDate", "Make" from du_dms_fortellis_model.etl_head
		WHERE "ClosedDate" IS NOT NULL AND  "RONumber" not in(select split_part("HostItemID" :: json ->> '_text', '*', 3) 
        FROM du_dms_fortellis_model.etl_accounts)
                               and  ("RONumber" in(select ro_number from proxy_data)
			or "RONumber" in(select ro_number from part_data)
			or "RONumber" in(select ro_number from tax_data))
      ) TO stdout WITH (FORMAT csv, HEADER true);
SQL
        cp 'corereturn_without_corecharge.csv' $CORE_RETURN_EXCEPTION_CSV_FILE_PATH 
        cp 'corecharge_without_corereturn.csv' $CORE_CHARGE_EXCEPTION_CSV_FILE_PATH
        cp 'corecharge_corereturn_mismatch.csv' $CORE_RETURN_NOT_EQUAL_TO_CORE_CHARGE_EXCEPTION_CSV_FILE_PATH
        cp 'corecharge_with_nosale.csv' $CORE_CHARGE_WITH_NO_SALE

        cp 'invalid_core_cost_sale_mismatch.csv' $INVALID_CORE_COST_SALE_MISMATCH
        cp 'invalid_core_amount_mismatch.csv' $INVALID_CORE_AMOUNT_MISMATCH

        cp 'gl_missing_ro_list.csv' $GL_MISSING_RO_LIST

        d1=0
        d2=1
        corereturn_without_corecharge_line=$(tail -1 'corereturn_without_corecharge.csv')
        corereturn_without_corecharge_extract=$(echo $corereturn_without_corecharge_line| cut -d',' -f 1)
        corereturn_without_corecharge_count=$(echo $corereturn_without_corecharge_extract| cut -d':' -f 2 | sed 's/ *$//g') 
        corereturn_without_corecharge_count_numeric=$(echo "$corereturn_without_corecharge_count - $d1" | bc)
        echo "corereturn_without_corecharge_count_numeric:$corereturn_without_corecharge_count_numeric"
        
        corecharge_without_corereturn_line=$(tail -1 'corecharge_without_corereturn.csv')
        corecharge_without_corereturn_extract=$(echo $corecharge_without_corereturn_line| cut -d',' -f 1)
        corecharge_without_corereturn_count=$(echo $corecharge_without_corereturn_extract| cut -d':' -f 2 | sed 's/ *$//g') 
        corecharge_without_corereturn_count_numeric=$(echo "$corecharge_without_corereturn_count - $d1" | bc)
        echo "corecharge_without_corereturn_count_numeric:$corecharge_without_corereturn_count_numeric"

        corecharge_corereturn_mismatch_line=$(tail -1 'corecharge_corereturn_mismatch.csv')
        corecharge_corereturn_mismatch_extract=$(echo $corecharge_corereturn_mismatch_line| cut -d',' -f 1)
        corecharge_corereturn_mismatch_count=$(echo $corecharge_corereturn_mismatch_extract| cut -d':' -f 2 | sed 's/ *$//g') 
        corecharge_corereturn_mismatch_count_numeric=$(echo "$corecharge_corereturn_mismatch_count - $d1" | bc)
        echo "corecharge_corereturn_mismatch_count_numeric:$corecharge_corereturn_mismatch_count_numeric"

        corecharge_with_nosale_count=$(cat 'corecharge_with_nosale.csv'  | wc -l)
        corecharge_with_nosale_count_numeric=$(echo "$corecharge_with_nosale_count - $d2" | bc)
        echo "corecharge_with_nosale_count_numeric:$corecharge_with_nosale_count_numeric"

        invalid_core_cost_sale_mismatch_count=$(cat 'invalid_core_cost_sale_mismatch.csv'  | wc -l)
        invalid_core_cost_sale_mismatch_count_numeric=$(echo "$invalid_core_cost_sale_mismatch_count - $d2" | bc)
        echo "invalid_core_cost_sale_mismatch_count_numeric:$invalid_core_cost_sale_mismatch_count_numeric"


        invalid_core_amount_mismatch_count=$(cat 'invalid_core_amount_mismatch.csv'  | wc -l)
        invalid_core_amount_mismatch_count_numeric=$(echo "$invalid_core_amount_mismatch_count - $d2" | bc)
        echo "invalid_core_amount_mismatch_count_numeric:$invalid_core_amount_mismatch_count_numeric"

        # invalid_core_cost_sale_mismatch_count_numeric=10
        # invalid_core_amount_mismatch_count_numeric=10
        
        HALT_COUNT=$(($corecharge_corereturn_mismatch_count_numeric + $invalid_core_cost_sale_mismatch_count_numeric + $invalid_core_amount_mismatch_count_numeric )) 
       
        if [[ "$HALT_OVER_RIDE" = 'false' ]]; 
        then

            if [[ $HALT_COUNT -gt 50 ]];
            then
                die "Process is HALT!"
            fi

        fi

       
       
       
       
       
       # if [[ "$HALT_OVER_RIDE" = 'false' ]]; 
       # then

       #     if [[ $corecharge_corereturn_mismatch_count_numeric -gt 0 ]];
       #     then
       #         die "Process is HALT!"
       #     fi

       #       if [[ $invalid_core_cost_sale_mismatch_count_numeric -gt 0 ]];
       #     then
       #         die "Process is HALT!"
       #     fi

       #    if [[ $invalid_core_amount_mismatch_count_numeric -gt 0 ]];
       #     then
       #         die "Process is HALT!"
       #    fi

       #     if [[ $corecharge_with_nosale_count_numeric -gt 0 ]];
       #      then
       #         die "Process is HALT!"
       #      fi

            # if [[ $corereturn_without_corecharge_count_numeric -gt 0 ]];
            # then
            #     die "Process is HALT!"
            # fi

            # if [[ $corecharge_without_corereturn_count_numeric -gt 0 ]];
            # then
                # die "Process is HALT!"
            # fi

        #fi
    
    )
}

function generate_report_customer_pay_types() {
    (
        progress "Generating report of all customer paytypes"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;

        \o 'customer-pay-types.csv'
        COPY (
            WITH cte AS(
                SELECT 
                "RONumber" ,
                SUM("punDuration"::decimal)  AS "Total Punch Duration",
                STRING_AGG (DISTINCT "punTechNo", ',') AS "Tech ID"
                FROM du_dms_fortellis_model.etl_tech_punch GROUP BY "RONumber"
            ), paycode AS (
            	SELECT STRING_AGG("payPaymentCode",', ') As paymentcode , "RONumber" As ro_num
				FROM du_dms_fortellis_model.etl_pay GROUP BY "RONumber" ORDER BY "RONumber"
            )
            SELECT
                lbr."RONumber"                                                                       AS "RO Number",
                CASE
                WHEN COUNT(pay."payInsuranceFlag")  FILTER (WHERE pay."payInsuranceFlag"::boolean) > 0
                    THEN 'YES'
                ELSE 'NO' END                                                                        AS "Is Insurance",
                lbr."PayType"                                                                        AS "Pay Type",
                hd."Make"                                                                            AS "Vehicle Make",
                SUM(pay."payPaymentAmount"::numeric)  FILTER (WHERE pay."payInsuranceFlag"::boolean) AS "Total Insurance Amount",
                SUM(pay."payPaymentAmount"::numeric)                                                 AS "Total Payment Amount",
                cte."Total Punch Duration"                                                           AS "Total Punch Duration",
		        cte."Tech ID"                                                                        AS "Tech ID",
		        paymentcode 																		 AS "Pay Code"
            FROM (            
                SELECT
                    "RONumber",
                    STRING_AGG(DISTINCT "lbrLaborType",', ') AS "PayType"
                FROM du_dms_fortellis_model.etl_labor
                GROUP BY "RONumber"
                ORDER BY  "RONumber") lbr
                LEFT JOIN du_dms_fortellis_model.etl_pay pay USING ("RONumber") 
                LEFT JOIN du_dms_fortellis_model.etl_head hd USING ("RONumber")
                LEFT JOIN  cte  USING ("RONumber")
                LEFT JOIN paycode p ON p.ro_num = lbr."RONumber"
            GROUP BY "RONumber", lbr."PayType", hd."Make", cte."Total Punch Duration", cte."Tech ID" ,paymentcode
            ORDER BY "RONumber"
        ) TO stdout WITH (FORMAT csv, HEADER true);
SQL
    )
}

function generate_report_total_discount() {
    (
        progress "Generating report of total discount"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;

        \o 'ro_discount.csv'
        COPY (
		with cte as(SELECT
        			"RONumber"           AS ro_number,
        			dis_total_discount."disTotalDiscount"::numeric AS total_discount,
        			dis_total_discount."disTotalDiscount_idx"::numeric AS total_discount_lineitem,
        			dis_Level."disLevel" as dis_Level,
        			case when (dis_LineCode."disLineCode"::text)='RO' then ' level' else concat(' item',(dis_LineCode."disLineCode"::text),' ') end  as LineCode,
        			dis_LaborDiscount."disLaborDiscount" as dis_LaborDiscount,
        			dis_PartsDiscount."disPartsDiscount" as dis_PartsDiscount,
        			concat((dis_Level."disLevel"::text),((case when (dis_LineCode."disLineCode"::text)='RO' then ' level' else concat(' item ',(dis_LineCode."disLineCode"::text)) end  )::text),' (',(dis_DiscountID."disDiscountID"::text),' - ',(dis_Desc."disDesc"::text),')',(case when (dis_PartsDiscount."disPartsDiscount"::numeric) !=0 then concat(' Parts Discount= ',(round(abs(dis_PartsDiscount."disPartsDiscount"::numeric),2)::text),',') end ),(case when (dis_LaborDiscount."disLaborDiscount"::numeric) !=0 then concat(' Labor Discount= ',(round(abs(dis_LaborDiscount."disLaborDiscount"::numeric),2)::text)) end)) dis_description
    				FROM du_dms_fortellis_model.proxy_other,
        			LATERAL (
        					SELECT
            					jsonb_array_elements(jsonb("disSequenceNo")->'V')->>'_text'              AS "disSequenceNo",
            					jsonb_array_elements(jsonb("disSequenceNo")->'V')->'_attributes'->>'Idx' AS "disSequenceNo_idx") dis_sequence_no
     				LEFT JOIN LATERAL (
                				SELECT
                    				jsonb_array_elements(jsonb("disTotalDiscount")->'V')->>'_text'              AS "disTotalDiscount",
                    				jsonb_array_elements(jsonb("disTotalDiscount")->'V')->'_attributes'->>'Idx' AS "disTotalDiscount_idx") dis_total_discount
               					ON dis_sequence_no."disSequenceNo_idx" = dis_total_discount."disTotalDiscount_idx"
            			LEFT JOIN LATERAL (
                				SELECT
                    				jsonb_array_elements(jsonb("disLevel")->'V')->>'_text'              AS "disLevel",
                    				jsonb_array_elements(jsonb("disLevel")->'V')->'_attributes'->>'Idx' AS "disLevel_idx") dis_Level
               					ON dis_sequence_no."disSequenceNo_idx" = dis_Level."disLevel_idx"
            			LEFT JOIN LATERAL (
                				SELECT
                    				jsonb_array_elements(jsonb("disLineCode")->'V')->>'_text'              AS "disLineCode",
                    				jsonb_array_elements(jsonb("disLineCode")->'V')->'_attributes'->>'Idx' AS "disLineCode_idx") dis_LineCode
               					ON dis_sequence_no."disSequenceNo_idx" = dis_LineCode."disLineCode_idx"
            			LEFT JOIN LATERAL (
                				SELECT
                    				jsonb_array_elements(jsonb("disLaborDiscount")->'V')->>'_text'              AS "disLaborDiscount",
                    				jsonb_array_elements(jsonb("disLaborDiscount")->'V')->'_attributes'->>'Idx' AS "disLaborDiscount_idx") dis_LaborDiscount
               					ON dis_sequence_no."disSequenceNo_idx" = dis_LaborDiscount."disLaborDiscount_idx"
            			LEFT JOIN LATERAL (
                				SELECT
                    				jsonb_array_elements(jsonb("disPartsDiscount")->'V')->>'_text'              AS "disPartsDiscount",
                    				jsonb_array_elements(jsonb("disPartsDiscount")->'V')->'_attributes'->>'Idx' AS "disPartsDiscount_idx") dis_PartsDiscount
               					ON dis_sequence_no."disSequenceNo_idx" = dis_PartsDiscount."disPartsDiscount_idx"
            			LEFT JOIN LATERAL (
                				SELECT
                    				jsonb_array_elements(jsonb("disDiscountID")->'V')->>'_text'              AS "disDiscountID",
                    				jsonb_array_elements(jsonb("disDiscountID")->'V')->'_attributes'->>'Idx' AS "disDiscountID_idx") dis_DiscountID
               					ON dis_sequence_no."disSequenceNo_idx" = dis_DiscountID."disDiscountID_idx"
            			LEFT JOIN LATERAL (
                				SELECT
                    				jsonb_array_elements(jsonb("disDesc")->'V')->>'_text'              AS "disDesc",
                    				jsonb_array_elements(jsonb("disDesc")->'V')->'_attributes'->>'Idx' AS "disDesc_idx") dis_Desc
               					ON dis_sequence_no."disSequenceNo_idx" = dis_Desc."disDesc_idx"
            		)
            	SELECT "ro_number" as "RO Number",
	        	round(abs(SUM("total_discount"::numeric)),2) as "Discount",
	        	string_agg(("dis_description"::text),'; ') as "Summary"
		from cte
    		where ("total_discount"::numeric) !=0  
    		group by "ro_number" 
      ) TO stdout WITH (FORMAT csv, HEADER true);
SQL
    )
}

function generate_exception_report() {
    (
        progress "Generating exception Report"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        # psql_local --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/exception-report.psql \
        #           || die "Generating exception Report Failed"

    )
}

function generate_customerpay_report() {
    (
        progress "Generating Customerpay Report"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        # psql_local --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/customerpay-exception-report.psql \
        #           || die "Generating Customerpay Report Failed"

    )
}

function copy_analysis_table() {
    (
        progress "Copy tables"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/create-exception-csv.psql \
                   || die "Copy tables Failed"

    )
}

function generate_gl_report_for_analysis() {
    (
        progress "Generate GL report for Analysis"
        clear_dir "$EXCEPTION_TABLES_DIR"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/create-gl-report-analysis.psql \
                   || die "Generate GL report Failed"

    )
}

function extract_proxy(){
    progress "$(date +'%H:%M:%S') : Ready to Extract Text ROs"
    proxy_path="$1"
    cd ${DU_ETL_HOME}/${DMS_HOME}/src/exception/python/
    python3 ./extract_text_proxy.py "$1"
    progress "$(date +'%H:%M:%S') : Done Extracting Text ROs"
}

function extract_proxy_to_tsv(){
    progress "$(date +'%H:%M:%S') : Ready to Extract Text ROs to TSV"
    proxy_path="$1"
    tsv_path="$2"
    cd ${DU_ETL_HOME}/${DMS_HOME}/src/extract/python
    python3 ./extract-text-proxy-to-tsv.py "$1" "$2"
    progress "$(date +'%H:%M:%S') : Done Extracting Text ROs to TSV"
}

function generate_analysis_report() {
    (
        store_name_full=$(basename "$INPUT_BUNDLE_ZIP")
        STORE_NAME=${store_name_full%-INITIAL*}
        store_id_full=${INPUT_BUNDLE_ZIP#*3PA}
        store_id=${store_id_full%-*}
        STORE_ID="3PA"$store_id
        

        progress "Scheduler Analysis Report"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_scheduler --quiet --file ${DU_ETL_HOME}/${DMS_HOME}/src/parse/exception-report-analysis.psql \
                    --set=dealer_id="${STORE_ID}" \
                    --set=project_id="${PROJECT_ID}" \
                    --set=sec_project_id="${SEC_PROJECT_ID}" \
                    --set=store_name="${STORE_NAME}" \
                   || die "Scheduler Analysis Report Failed"

    )
}

function generate_core_exception_report() {
   (
   progress "Generating report of all customer paytypes"
        cd "$WORK_DIR_PROCESSING_RESULTS_DIR"
        psql_local --quiet <<SQL
        set client_min_messages = warning;
        \o 'corecharge_with_nosale.csv'
        copy (SELECT "RONumber" AS "RO Number", "prtPartNo" AS "Part No" FROM du_dms_fortellis_model.etl_parts_detail LEFT JOIN du_dms_fortellis_model.etl_head AS eth USING("RONumber") WHERE coalesce(nullif(trim("prtCoreSale"), '')::numeric, 0) = 0 AND coalesce(nullif(trim("prtCoreCost"), '')::numeric, 0) > 0 AND eth."ClosedDate" IS NOT NULL ORDER BY "RONumber") TO stdout WITH (FORMAT csv, HEADER true);
SQL
       cp 'corecharge_with_nosale.csv' $CORE_CHARGE_WITH_NO_SALE
    )
}




