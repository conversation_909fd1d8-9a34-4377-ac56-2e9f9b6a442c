#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/ansi-color-constants.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

shopt -s nullglob

SCRIPT_DIR="$(cd $(dirname $0) && pwd)"

WORK_DIR="$HOME/tmp/du-etl-dms-fortellis-extractor-work/process-json-temp"
ZIP_WORK_DIR="$WORK_DIR"/zip-temp
PROCESSING_MODE='none'
FILE_TO_PROCESS=''
PERFORM_ZIP='false'
PERFORM_PROXY_RO_BUILD='false'
INPUT_BUNDLE_ZIP=''
INPUT_ZIP_FILE_NAME=''
INPUT_BUNDLE_DIR=''
DO_ZAP_INPUT='false'
TIME_ZONE='America/New_York'
HALT_OVER_RIDE='false'
OUTPUT_FILE_PREFIX=''
UUID=''
EXCEPTION_REPORT=''
PERFORMED_BY=''
COMPANY_IDS=''
PRE_IMPORT_HALT='false'
SOLVE_DB='true'
SCHEDULER_DB='true'
EXCEPTION_ANALYSIS='true'
SHOW_ACCOUNTING_IN_PROXY='false'
PROJECT_ID=''
SEC_PROJECT_ID=''
PORSCHE_STORE='false'
BRAND=''
STATE=''
DUAL_PROXY='false'
OUTPUT_FILE=''



TRANSFORMS_DIR="$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/runtime/transforms
REPO_DIR="$DU_ETL_HOME"/DU-DMS/DMS-Fortellis

DEAD_LETTER_DIR=

source "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/Fortellis.env

source "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/Processor-Application/src/bash/process-library.bash-mixin
source "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/Processor-Application/src/bash/process-zip.bash-mixin

source "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/Processor-Application/src/bash/filetype/open-ro.bash-mixin
source "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/Processor-Application/src/bash/filetype/printed-ro.bash-mixin
source "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/Processor-Application/src/bash/filetype/closed-ro.bash-mixin
source "$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/Processor-Application/src/bash/process-proxyinvoice.bash-mixin

function psql_local() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}_model'" \
         --set=DU_ETL_HOME="$DU_ETL_HOME" \
         --set=ON_ERROR_STOP=1 \
         "$@"

    return $?
}

function psql_scheduler() {
    psql "service=$DU_ETL_SCHEDULER_SERVICE options='-c search_path=${SRC_SCHEMA}_model'" \
         --set=DU_ETL_HOME="$DU_ETL_HOME" \
         --set=ON_ERROR_STOP=1 \
         "$@"

    return $?
}

function psql_etl_proxy() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${PROXY_SCHEMA},${SRC_PROXY_SCHEMA}'" \
         --set=TIME_ZONE="$TIME_ZONE" \
         --set=ON_ERROR_STOP=1 "$@"
}

function psql_exception_analysis() {
    psql "service=$DU_ETL_SCHEDULER_SERVICE options='-c search_path=process_data'" \
         --set=DU_ETL_HOME="$DU_ETL_HOME" \
         --set=ON_ERROR_STOP=1 \
         "$@"

    return $?
}

function main() {
    parse_options "$@"
    initialize
    if execute_processing_mode "$PROCESSING_MODE"; then
        cleanup "$PROCESSING_MODE"
    else
        move_input_to_deadletter_and_abort "$PROCESSING_MODE"
    fi
}

function execute_processing_mode() {
    proc_mode="${1:?Processing Mode Specification Required}"
    (
        case "$proc_mode" in
                zip) process_zip_file ;;
                post-processed-proxies) generate_proxies_from_zip ;;
        esac
    )
    return $?
}

function initialize() {
    mkdir -p "$WORK_DIR"
    clear_dir "$WORK_DIR"

    [[ -d "${DEAD_LETTER_DIR:?Please Pass a Dead-Letter Directory Path}" ]] \
        || die "Dead Letter Directory Must Exist"
}

function move_input_to_deadletter_and_abort() {
    proc_mode="${1:?Processing Mode Specification Required}"
    yell "Moving input to dead-letter bin: $DEAD_LETTER_DIR"

    case "$proc_mode" in
        zip)
            cp "$INPUT_BUNDLE_ZIP" "$DEAD_LETTER_DIR"/
            cleanup "$proc_mode"
            ;;
    esac
}

function cleanup() {
    proc_mode="${1:?Processing Mode Specification Required}"

    progress "Clearing working directory and remove input if requested"
    clear_dir "$WORK_DIR"

    if [[ "$DO_ZAP_INPUT" = 'true' ]]; then
        if [[ -f "$INPUT_BUNDLE_ZIP" ]]; then
            remove_input_zip
        else
            yell "Zap input not implemented for non-zip files"
        fi
    fi
}

function remove_input_zip() {
    [[ -f "$INPUT_BUNDLE_ZIP" ]] || die "Input zip must exist to be removed: $INPUT_BUNDLE_ZIP"
    say "Zapping Input Zip File As Requested"
    rm "$INPUT_BUNDLE_ZIP"
}

function get_time_zone () {

	echo "get time zone"

	declare -A states
	states["AL"]="America/Chicago"
    states["AK"]="America/Anchorage"
    states["AZ"]="America/Phoenix"
    states["AR"]="America/Chicago"
    states["CA"]="America/Los_Angeles"
    states["CO"]="America/Denver"
    states["CT"]="America/New_York"
    states["DE"]="America/New_York"
    states["FL"]="America/Chicago"
    states["GA"]="America/New_York"
    states["HI"]="Pacific/Honolulu"
    states["ID"]="America/Boise"
    states["IL"]="America/Chicago"
    states["IN"]="America/Indiana/Indianapolis"
    states["IA"]="America/Chicago"
    states["KS"]="America/Chicago"
    states["KY"]="America/New_York"
    states["LA"]="America/Chicago"
    states["ME"]="America/New_York"
    states["MD"]="America/New_York"
    states["MA"]="America/New_York"
    states["MI"]="America/Menominee"
    states["MN"]="America/Chicago"
    states["MS"]="America/Chicago"
    states["MO"]="America/Chicago"
    states["MT"]="America/Denver"
    states["NE"]="America/Denver"
    states["NV"]="America/Los_Angeles"
    states["NH"]="America/New_York"
    states["NJ"]="America/New_York"
    states["NM"]="America/Denver"
    states["NY"]="America/New_York"
    states["NC"]="America/New_York"
    states["ND"]="America/Chicago"
    states["OH"]="America/New_York"
    states["OK"]="America/Chicago"
    states["OR"]="America/Los_Angeles"
    states["PA"]="America/New_York"
    states["RI"]="America/New_York"
    states["SC"]="America/New_York"
    states["SD"]="America/Denver"
    states["TN"]="America/New_York"
    states["TX"]="America/Denver"
    states["UT"]="America/Denver"
    states["VT"]="America/New_York"
    states["VA"]="America/New_York"
    states["WA"]="America/Los_Angeles"
    states["WV"]="America/New_York"
    states["WI"]="America/Chicago"
    states["WY"]="America/Denver"

	TIME_ZONE_STATE=$1
	TIME_ZONE=${states[${TIME_ZONE_STATE}]}
	if [[ $TIME_ZONE = '' ]]; then
	    TIME_ZONE='America/New_York'
	fi
}

function parse_options() {
    OPT_ENV=$(getopt --options c:w: --long output-prefix:,build-proxies-using:,dead-letter:,no-build-proxies,build-proxies,work-dir:,brand:,bundle-dir:,bundle-id:,no-zip,zip,input-dir:,input-zip:,state:,company_ids:,uuid:,exception-report:,performed-by:,pre-import-halt:,solve-db:,scheduler-db:,exception-analysis:,project-id:,secondary-project-id:,labor_project:,parts_project:,show-accounting-in-proxy,porsche-store,dual-proxy,hide-accounting-in-proxy,halt-over-ride:,zap-input -n 'process-json' -- "$@")
    if [[ ! $? = '0' ]]; then
        die "Option Parsing Failed"
    fi

    eval set -- "$OPT_ENV"
    while true; do
        case "$1" in

            --dead-letter)
                DEAD_LETTER_DIR="$2"
                shift 2
                ;;
            --bundle-dir)
                BUNDLE_OUTPUT_DIRECTORY="$2"
                shift 2
                ;;
            --bundle-id)
                BUNDLE_IDENTIFIER="$2"
                shift 2
                ;;
            --input-dir)
                INPUT_BUNDLE_DIR="$2"
                PROCESSING_MODE='full'
                shift 2
                ;;
            --halt-over-ride)
               HALT_OVER_RIDE=$2
               shift 2
               ;;    
            --input-zip)
                INPUT_BUNDLE_ZIP="$2"
                INPUT_ZIP_FILE_NAME="$(basename $INPUT_BUNDLE_ZIP)"
                PROCESSING_MODE='zip'
                PERFORM_ZIP='true'
                shift 2
                ;;
            --output-prefix)
                OUTPUT_FILE_PREFIX="$2"
                shift 2
                ;;
            --zap-input)
                DO_ZAP_INPUT='true'
                shift
                ;;
            --build-proxies)
                PERFORM_PROXY_RO_BUILD='true'
                shift
                ;;
            --build-proxies-using)
                POST_PROCESSED_ZIP_FILE="$2"
                PROCESSING_MODE="post-processed-proxies"
                shift 2
                ;;
            --no-build-proxies)
                PERFORM_PROXY_RO_BUILD='false'
                shift
                ;;
            --no-zip)
                PERFORM_ZIP='false'
                shift
                ;;
            --zip)
                PERFORM_ZIP='true'
                shift
                ;;
            --work-dir)
                say "Work Dir Change: $2"
                WORK_DIR="$2"
                shift 2
                ;;
            --state)
                STATE="$2"
                shift 2
                ;;
            --brand)
                BRAND="$2"
                shift 2
                ;;
            --uuid)
                UUID="$2"
                echo "UUID: $UUID"
                shift 2
                ;;
            --company_ids)
                COMPANY_IDS="$2"
                shift 2
                ;;
            --dual-proxy)
                DUAL_PROXY='true'
                shift
                ;;
            --exception-report)
                EXCEPTION_REPORT="$2"
                echo "EXCEPTION_REPORT: $EXCEPTION_REPORT"
                shift 2
                ;;
            --performed-by)
                PERFORMED_BY="$2"
                echo "PERFORMED_BY: $PERFORMED_BY"
                shift 2
                ;;
            --pre-import-halt)
                PRE_IMPORT_HALT="$2"
                echo "PRE_IMPORT_HALT: $PRE_IMPORT_HALT"
                shift 2
                ;;   
            --solve-db)
                SOLVE_DB="$2"
                echo "SOLVE_DB: $SOLVE_DB" 
                shift 2
                ;;
            --scheduler-db)
                SCHEDULER_DB="$2"
                echo "SCHEDULER_DB: $SCHEDULER_DB"
                shift 2
                ;;
            --show-accounting-in-proxy)
                SHOW_ACCOUNTING_IN_PROXY='true'
                shift
                ;;
            --hide-accounting-in-proxy)
                SHOW_ACCOUNTING_IN_PROXY='false'
                shift
                ;;
            --project-id)
                PROJECT_ID="$2"
                shift 2
                ;;
            --secondary-project-id)
                SEC_PROJECT_ID="$2"
                shift 2
                ;;
            --labor_project)
                SEC_PROJECT_ID="$2"
                echo "labor project:$SEC_PROJECT_ID"
                shift 2
                ;; 
            --parts_project)
                PROJECT_ID="$2"
                echo "Parts project:$PROJECT_ID"
                shift 2
                ;;  
            --porsche-store)
                PORSCHE_STORE='true'
                shift
                ;;
            --exception-analysis)
                EXCEPTION_ANALYSIS="$2"
                echo "EXCEPTION_ANALYSIS: $2"
                shift 2
                ;; 
            --) shift ; break ;;
            *) die "Unrecognized Argument $1"
        esac
    done
}

main "$@"
