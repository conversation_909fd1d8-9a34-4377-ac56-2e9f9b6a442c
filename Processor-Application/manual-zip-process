#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

ZIP_TO_PROCESS="${1:?Zip File Required First}"
BASE_WORK_DIR="$HOME"/tmp/du-etl-dms-fortellis-extractor-work

mkdir -p "$BASE_WORK_DIR"/manual/dead-letter-processed
mkdir -p "$BASE_WORK_DIR"/manual/bundle
mkdir -p "$BASE_WORK_DIR"/manual/extraction-archive
SCH_ID=$RANDOM$RANDOM$RANDOM$RANDOM

./process-json --input-zip "$ZIP_TO_PROCESS" \
               --bundle-dir "$BASE_WORK_DIR"/manual/bundle \
               --dead-letter "$BASE_WORK_DIR"/manual/dead-letter-processed \
               --output-prefix 'fortellis-' \
               --state 'AL' \
               --build-proxies \
               --brand 'AUDI' \
               --uuid "$SCH_ID" \
               --company_ids '*********' \
               --exception-report 'false' \
               --performed-by ''\
               --halt-over-ride 'false' \
               --pre-import-halt 'false'\
               --solve-db 'true' \
               --scheduler-db 'true' \
              --show-accounting-in-proxy \
              --exception-analysis 'true' 

if [[ $? = 0 ]]; then
    say "Processing Completed; Moving input to Archive"
    mv --force "$ZIP_TO_PROCESS" "$BASE_WORK_DIR"/manual/extraction-archive
    (
        cd ..
        find "$BASE_WORK_DIR"/manual/bundle/ \
             -maxdepth 1 -type f -exec ./send-bundle-live-hpdog {} \;
    )

else
    yell "Processing Failed; Leaving Extraction Zip File In Place"
fi
