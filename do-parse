#!/usr/bin/env bash

shopt -s nocasematch

SCRIPT_BASE="$DU_ETL_HOME"/DU-DMS/DMS-Fortellis/src/parse/parse-scripts 

TMP_DIR=~/tmp/du-etl-dms-fortellis/do-parse
mkdir -p "$TMP_DIR"
touch "$TMP_DIR"/waiting-for-cleanup

STATUS_FILENAME=fortellis-status.env
source "$DU_ETL_HOME"/DU-Transform/client/parse/do-parse-shared.bash

function clean_and_die(){
    local import_status=${1}
    local comment=${2}
    update_scheduler_event "${PROJECT_ID}" "${SECONDARY_PROJECT_ID}" "${UNIQUE_ID}" "${SCHEDULER_ID}" "${IMPORTED_BY}" "${DMS}" "${import_status}" "${comment}"
    die $1
}

if [[ -z "$IMPORT_SCHEMA" ]]; then
    IMPORT_SCHEMA="${SRC_SCHEMA}_model"
    echo "IMPORT_SCHEMA: $IMPORT_SCHEMA"
fi

if [[ $FINALIZE_PARSE = 'true' ]]; then
    (
        echo "UUID: $UNIQUE_ID"
        echo "${IMPORT_SCHEMA}"
        echo "SCHEDULER_ID: $SCHEDULER_ID"
        PROJECT_IDS="{$PROJECT_ID}"

        if [[ -n "$SECONDARY_PROJECT_ID" ]]; then
            echo "Variable has a value: $SECONDARY_PROJECT_ID"
            PROJECT_IDS="{$PROJECT_ID , $SECONDARY_PROJECT_ID}"
        fi
        save_import_status "$SCHEDULER_ID" "$DMS" "$COMPANY_ID" "$STORENAME" "$IMPORTED_BY" "InProgress" "$UNIQUE_ID"
        
        script_base_dir="$PWD"
        cd "$WORK_DIR"
        
        export PATH="$PATH:$DU_ETL_HOME/DU-DMS/DMS-Fortellis"
        psql_local --quiet --set=IMPORT_SCHEMA="${IMPORT_SCHEMA}" --set=SRC_SCHEMA="${SRC_SCHEMA}" --file "$script_base_dir"/src/parse/finalize-schema.psql \
                   || clean_and_die false "Parse Finalize Failed".
                   
        exit 0
    ) || clean_and_die false "Finalization Failed"
    exit 0
fi

function extract_type_name() {
    local fullname=$(basename "${1}")
    if [[ "$fullname" =~ .*pgdump ]]; then
        echo "DumpFile"
        exit $S_OK
    elif [[ "$fullname" = proxy-text.tsv ]]; then
        echo "ProxyTsv"
        exit $S_OK
    elif [[ "$fullname" =~ config_.*.bash ]]; then
        echo "Config"
        exit $S_OK
    else
        echo "N/A"
    fi
    return 0
}

typename=$(extract_type_name "${1}")
case "$typename" in
    'DumpFile')

    grep -q "^CREATE SCHEMA" "${1}"

    if [ $? -eq 0 ]; then
        echo "The dump file contains a CREATE SCHEMA statement."
            psql_local --quiet <<SQL
        SET client_min_messages = 'WARNING';
        DROP SCHEMA IF EXISTS ${IMPORT_SCHEMA} CASCADE;
SQL
    else
        echo "The dump file does not contain a CREATE SCHEMA statement."
        psql_local --quiet <<SQL
        SET client_min_messages = 'WARNING';
        DROP SCHEMA IF EXISTS ${IMPORT_SCHEMA} CASCADE;
        CREATE SCHEMA ${IMPORT_SCHEMA};
SQL
    fi
        pg_restore --exclude-schema=pg_catalog -d "service=$DU_ETL_PG_SERVICE options='-c search_path=${IMPORT_SCHEMA}'" "$1"
        ;;
    'ProxyTsv')
        echo "Proxy Tsv Found"
        echo "WORK_DIR: $WORK_DIR"
        ;;
    'Config')
        echo "Config found"
        ;;
    *)
        exit 2
        ;;
esac

if [[ "$?" != 0 ]]; then
    comment="Parsing file failed"
    import_status=false
    save_failure_import_status "$SCHEDULER_ID" "$DMS" "$COMPANY_ID" "$STORENAME" "$IMPORTED_BY" "$import_status" "$UNIQUE_ID" "$comment"
    exit 10
fi


# read in the context from the status file and use it
# to configure how parsing should proceed.  In particular
# we need to determine how the database schema is going
# to be laid out.

exit 0
