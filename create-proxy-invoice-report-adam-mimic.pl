#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Term::ANSIColor qw( colored );
use Time::Format qw(%time %strftime %manip);
use POSIX qw/ceil/;

no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];
my $is_porsche_store =  $ARGV[1];
# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;

my @header_section;
my @header_subsection;
my @job_section;
my @grand_total_section;
my @end_of_invoice_section;
my @invoice_note_section;

my ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

my ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

my ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

my ($sale_amount, $labor_cost, $parts_cost, $parts_sale,  $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

my $job_hours = 0;
my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;

my ($parts_entries, $misc_entries, $gog_entries, $sublet_entries, $ded_entries) = (0)x5;
my $lbr_type;
my $page_max_height = 53; # Maximum no of lines in page body
my $subpage_max_height = 56; # Maximum no of lines in page body
my $page_header_height = 8; # No of lines in header section
my $invoice_dealer_header = 7; # No of lines in the dealer header section (1st page)
my $total_header_height;
my $extra_tech_line = 0;
my $curr_page_height = 0;
my $invoice_note_lines = 4; # No of lines needed for invoice note
my $pages;
my $curr_page_num;
my $invoice_datetime;
my $cust_inv_head;
my $parts_total_sale;
my $customer_total_amount=0;

my $ro_qry = $conn->prepare("SELECT ro_number
                               FROM repair_order
                              ORDER BY ro_number");

$ro_qry->execute();
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number) = @ro_list;
    my $file = $ro_number . ".txt";
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;

    my $file_inv_head = "inv-bg/".$ro_number . "-inv-bg.txt";
    unless(open FILE, '>'.$file_inv_head) {
        die "\nUnable to create $file_inv_head\n";
    }
    print_ro_inv_bg_head();
    close FILE;

    load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
  my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
  $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
   $ro_doc_insert->finish;
}
  
# Subroutine to prepare HEADER section of the invoice
sub make_header {
    my $ro_header_qry = $conn->prepare("SELECT
                                to_char(ro.creation_date, 'mm/dd/yyyy') AS creation_date,
                                to_char(ro.completion_date, 'mm/dd/yy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                ro.advisor,
                                ro.hat_no,
                                ro.production_date,
                                ro.stock_no,
                                ro.po_num,
                                ro.war_company,
                                roc.customer_number,
                                roc.customer_name,
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip,
                                roc.customer_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                rov.vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in,
                                SUBSTRING(rov.vehicle_color, 1, 9) AS vehicle_color,
                                rov.mileage_out,
                                roc.license_number,
                                ro.tag_no,
                                --to_char(ro.delivery_date, 'mm/dd/yy') AS delivery_date,
                                to_char(ro.delivery_date, 'mm/dd/yyyy') AS delivery_date,
                                to_char(ro.promised_time, 'HH24:MI') || ' ' || to_char(ro.promised_date, 'ddMONyy') AS promised,
                                to_char(ro.booked_time, 'HH24:MI') || ' ' || to_char(ro.booked_date, 'ddMONyy') AS ready,
                                ro.payment_code,
                                ro.comments,
                                ro_ext,
                                status,
                                roc.control_number,
                                to_char(in_service_date, 'mm/dd/yy') AS in_service_date,
                                work_phone,
                                other_phone AS cell_phone,
                                nullif(delivery_miles, 0) AS delivery_miles,
                                roc.other_phone,
                                to_char(ro.promised_date, 'mm/dd/yyyy') AS promised_date,
                                ro.policy,
                                coalesce(nullif(ro.xp_deduct, 0),0) as ded_cost 
                          FROM repair_order ro
                                LEFT JOIN repair_order_customer roc USING (ro_number)
                                LEFT JOIN repair_order_vehicle rov USING (ro_number)
                          WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
    my ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor, $Lot, $prodDate, $stock, $po_num, $ext_warranty_co, $customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $customer_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $vehicle_color, $mileage_out, $license_number, $tag_no, $delivery_date, $promised, $ready, $payment_code, $comments, $ro_ext, $status,
        $control_number, $in_service_date, $work_phone, $cell_phone, $delivery_miles, $other_phone, $promised_date,$policy, $ded_cost, $invoice_dealer_header ) = @header_row;
    $invoice_datetime = $creation_date;
    $cust_inv_head = $ro_ext;

    my $dealer_name = ""  ;  
    my $override = "";
    my $swIntEst = "";
    my $enginehrs = "";
    my $author = "";
    my $adjustor = "";
    my $ded_cost_print = "";
    if($ded_cost != 0) {
      $ded_cost_print = currency_format(substr($ded_cost,0, 19));
    }
    if($is_porsche_store eq 'true'){
        $vehicle_vin=substr($vehicle_vin, 0, 12);
        $work_phone = '';
        $other_phone = '';
        $customer_phone = '';
        $customer_number = '';
        $customer_name ='';
        $customer_address = '';
        $customer_city = '';
        $customer_zip = '';
        $customer_state = '';
        $customer_email = '';
    }

push (@header_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s"."\n", ""));    
 push (@header_section, sprintf(border(">").'~font{DejaVuSansMono10}'.'~color{0 0 .55}'."%92s%-1s".border("|")."\n", "",""));
    push (@header_section, sprintf(border(">").'~font{HelveticaMono3}'."%-100s"."\n", ""));    
    push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%1s %-10s %-28s %-15s %-16s %-14s %-14s %-15s ".'~font{default}'.border("|")."\n", "",substr($customer_number, 0, 10), substr($customer_name,0, 28),substr($license_number,0,15), substr($stock,0, 16), substr($dealer_name,0, 14), substr($creation_date,0, 14), substr($ro_number,0, 15)));
    push (@header_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s"."\n", "")); 
    push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%2s %-39s %-15s %-16s %-14s %-14s %-14s ".'~font{default}'.border("|")."\n", "",substr($customer_name, 0, 39),substr($mileage_in,0,15), substr($mileage_out,0, 16), substr($Lot,0, 14), substr($vehicle_color,0, 14), substr($delivery_date,0, 15)));
    push (@header_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s"."\n", ""));                                   
    push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%2s %-38s %-47s %-14s %-15s ".'~font{default}'.border("|")."\n","",substr($customer_address, 0, 38),substr($vehicle_year." ".$vehicle_make." ".$vehicle_model, 0, 47), "",substr($promised_date,0, 15)));
    push (@header_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s"."\n", ""));
    push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%2s %-38s %-47s %-14s %-15s ".'~font{default}'.border("|")."\n","",substr($customer_city.",".$customer_state." ".$customer_zip, 0, 38),substr($vehicle_vin, 0, 47), substr('',0, 14),substr($po_num,0, 15)));
    push (@header_section, sprintf(border(">").'~font{HelveticaMono2}'."%-100s".border("|")."\n", ""));
    
    push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%2s %-38s %-19s %-27s %-14s %-14s ".'~font{default}'.border("|")."\n","",substr($other_phone, 0, 38),substr($override, 0, 19), substr($advisor,0, 27),substr($swIntEst,0, 14),substr($enginehrs,0, 15)));
    push (@header_section, sprintf(border(">").'~font{HelveticaMono3}'."%-100s".border("|")."\n", ""));
    push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%2s %-38s %-27s".'~color{.60 0 0}'."  %-19s ".'~color{0 0 .55}'." %-14s %-11s ".'~font{default}'.border("|")."\n","",substr($ext_warranty_co, 0, 38),substr($policy, 0, 27), $ded_cost_print,substr($author,0, 14),substr($adjustor,0, 15))); 
    push (@header_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s".border("|")."\n", ""));
    push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%2s %-16s %-21s %-18s %-57s ".'~font{default}'.border("|")."\n","",substr($customer_phone, 0, 14),substr($work_phone, 0, 14), substr($other_phone,0, 18),substr('',0, 59)));
    
    # push (@header_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s".border("|")."\n", ""));


     push (@header_subsection, sprintf(border(">").'~font{HelveticaMono8}'."%-60s"."\n", ""));
    push (@header_subsection, sprintf(border(">").'~font{HelveticaMono8}'."%-60s"."\n", ""));  
    push (@header_subsection, sprintf(border(">").'~font{DejaVuSansMono8}'."%1s %-10s %-28s %-15s %-16s %-14s %-14s %-10s ".'~font{default}'.border("|")."\n", "",substr($customer_number, 0, 10), substr($customer_name,0, 28),substr($license_number,0,15), substr($stock,0, 16), substr($dealer_name,0, 14), substr($creation_date,0, 14), substr($ro_number,0, 15)));
    
    # push (@header_subsection, sprintf(border(">").'~font{HelveticaMono1}'."%-100s"."\n", ""));      
}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {
    my $job_qry = $conn->prepare("SELECT
                                    rl.ro_line :: numeric,
                                    rl.complaint,
                                    rj.ro_job_line,
                                    left(rj.billing_code, 1) AS billing_code,
                                    rj.billing_code          AS billing_labor_type,
                                    rj.op_code,
                                    rj.op_description,
                                    rj.sold_hours,
                                    rj.labor_cost::money,
                                    rj.sale_amount,
                                    rj.cause,
                                    rj.correction,
                                    rj.parts_cost::money,
                                    rj.sublet_amount:: numeric
                                FROM repair_line rl
                                    JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ? ORDER BY ro_line :: numeric");

    $job_qry->execute($ro_number);
    
    my ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours, $cause, $correction, $parts_cost, $sublet_amount );
    while (my @job_row = $job_qry->fetchrow_array) {
        ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours, $labor_cost, $sale_amount, $cause, $correction, $parts_cost, $sublet_amount ) = @job_row;
        if ($billing_code eq 'I' || $billing_code eq 'i'){
            $lbr_type = "I";
        } elsif ($billing_code eq 'C' || $billing_code eq 'c'){
            $lbr_type = "C";
        } elsif ($billing_code eq 'W' || $billing_code eq 'w'){
            $lbr_type = "W";
        }
        my @job_header_section;
        my $techs_qry = $conn->prepare("SELECT
                            tech_id,
                            actual_hours,
                            booked_hours
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
        $techs_qry->execute($ro_number, $ro_line, $ro_job_line);
        my ($job_tech, $act_time, $booked_time);
        my @job_tech_row = $techs_qry->fetchrow_array;
        my $tech_lic = "";
        ($job_tech, $act_time, $booked_time) = @job_tech_row;
         $job_hours = $sold_hours;

            push (@job_header_section, sprintf(border(">").'~font{DejaVuSansMono-Bold9}'.'~color{0 0 .55}'."%1s%-5s".'~font{DejaVuSansMono9}'." %-5s %-43s".'~font{DejaVuSansMono9}'." %-5s %-24s".border("|")."\n","",
                                           " TYPE:".$lbr_type, "JOB#", $ro_line, "Lbr/Pts Cost:" , $labor_cost."/".$parts_cost ));
            push (@job_header_section,border(">").'~font{DejaVuSansMono9}'.'~color{.60 0 0}'."  OP Code:".$op_code.'~color{0 0 .55}'."\n");    
         if($techs_qry->rows > 0){

        	while (@job_tech_row = $techs_qry->fetchrow_array) {
            		($job_tech, $act_time, $booked_time) = @job_tech_row;
            		$job_hours = $job_hours + $act_time;
        	}
	}

         push (@job_section, [@job_header_section]);

        # Job Complaint
        my @job_complaint_section;
        $Text::Wrap::columns = 90;
        my $wrapped_complaint = fill('', '', expand($complaint));
        my @complaint_list = split "\n", $wrapped_complaint;
if($complaint ne ""){
        push (@job_complaint_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%2s".'~color{0 0 0}'."%-11s".'~color{0 0 .55}'."  %-90s".'~font{DejaVuSansMono9}'.border("|")."\n","","Complaint:", $complaint_list[0]));
        foreach my $i (1 .. $#complaint_list) {
            push (@job_complaint_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%3s %10s %-62s".'~font{DejaVuSansMono9}'.border("|")."\n","", "", $complaint_list[$i]));
        }
        push (@job_complaint_section, sprintf(border("#").'~font{DejaVuSansMono9}'."%2s%-100s".'~font{DejaVuSansMono9}'.border("|")."\n","", "-"x100));
        push (@job_section, [@job_complaint_section]);
}
        # Job Cause
        my @job_cause_section;
        my $wrapped_cause = fill('', '', expand($cause));
        my @cause_list = split "\n", $wrapped_cause;
if($cause ne ""){
        push (@job_cause_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%2s".'~color{0 0 0}'."%-11s".'~color{0 0 .55}'." %-90s".'~font{DejaVuSansMono9}'.border("|")."\n","","Cause:", $cause_list[0]));
       foreach my $i (1 .. $#cause_list) {
            push (@job_cause_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%3s %10s %-62s".'~font{DejaVuSansMono9}'.border("|")."\n","", "", $cause_list[$i]));
        }
        push (@job_cause_section, sprintf(border("#").'~font{DejaVuSansMono9}'."%2s%-100s".'~font{DejaVuSansMono9}'.border("|")."\n","", "-"x100));

        push (@job_section, [@job_cause_section]);
}
        # Job Correction
        my @job_correction_section;
        my $wrapped_correction = fill('', '', expand($correction));
        my @correction_list = split "\n", $wrapped_correction;
        push (@job_correction_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%2s".'~color{0 0 0}'."%-11s".'~color{0 0 .55}'." %-90s".'~font{DejaVuSansMono9}'.border("|")."\n","", "Correction:", $correction_list[0]));
        foreach my $i (1 .. $#correction_list) {
           push (@job_correction_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%3s %10s %-62s".'~font{DejaVuSansMono9}'.border("|")."\n","", "", $correction_list[$i]));
       }
        push (@job_correction_section, sprintf(border("#").'~font{DejaVuSansMono9}'."%2s%-100s".'~font{DejaVuSansMono9}'.border("|")."\n","", "-"x100));

        push (@job_section, [@job_correction_section]);

         my @parts_section = make_parts_section($ro_line, $ro_job_line);
         push (@job_section, [@parts_section]);

         my @misc_section = make_misc_section($ro_line);
         push (@job_section, [@misc_section]);

         my @gog_section = make_gog_section($ro_line);
         push (@job_section, [@gog_section]);

         my @sublet_section = make_sublet_section($ro_line);
         push (@job_section, [@sublet_section]);

         my @ded_section = make_deductible_section($ro_line);
         push (@job_section, [@ded_section]);

       # Job Tech
        my @job_tech_section;

       # my $wrapped_tech = fill('', '', expand($job_tech));
        #my @tech_list = split "\n", $wrapped_tech;
        #push (@job_tech_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%2s".'~color{0 0 0}'." %-10s".'~color{0 0 .55}'." %-62s".'~font{DejaVuSansMono9}'.border("|")."\n","", "Tech:", $tech_list[0]));
        #foreach my $i (1 .. $#tech_list) {
         #   push (@job_tech_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%3s %10s %-62s".'~font{DejaVuSansMono9}'.border("|")."\n","", "", $tech_list[$i]));
       #}
       #push (@job_tech_section, sprintf(border("#").'~font{DejaVuSansMono9}'."%3s %-183s".'~font{DejaVuSansMono9}'.border("|")."\n","", "."x180));
       #push (@job_section, [@job_tech_section]);
        my @tech_section = make_tech_section($ro_line, $ro_job_line);
        push (@job_section, [@tech_section]);

         my @tax_section = get_ro_tax_amount($ro_line);
         #push (@job_section, [@tax_section]);

         my @job_total_section = make_job_total_section($ro_line, $sublet_amount);
         push (@job_section, [@job_total_section]);

    }
}

# Subroutine to prepare the TECH section of a JOB
sub make_tech_section{
    my ($ro_line, $ro_job_line, $total_act_time, $total_booked_time) = @_;
    my @tech_section_array;
my $array_size;
    my $tech_details_qry = $conn->prepare("SELECT
                                                ro_job_tech_line,
                                                tech_id,
                                                work_date,
                                                work_start_time,
                                                work_end_time,
                                                work_note,
                                                actual_hours,
                                                booked_hours
                                            FROM repair_job_technician_detail td
                                            WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
    $tech_details_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours);
    if($tech_details_qry->rows > 0){
		#my @newarray=();
        if($tech_details_qry->rows == 1){

		push (@tech_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold9}'."%4s".'~color{0 0 .55}'. "Tech".'~color{0 0 .55}'."%3s"." Flag".'~color{0 0 .55}'."%5s". "Clock Warr".'~color{0 0 .55}'."%5s". "Fail".'~color{0 0 .55}'."%5s"." QC".'~color{0 0 .55}'."%-51s".'~font{default}'.border("|")."\n",
                                                "","","","","",""));
        }
        else{
 
		push (@tech_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold9}'."%4s".'~color{0 0 .55}'. "Tech".'~color{0 0 .55}'."%3s"." Flag".'~color{0 0 .55}'."%5s". "Clock Warr".'~color{0 0 .55}'."%5s". "Fail".'~color{0 0 .55}'."%5s"." QC".'~color{0 0 .55}'."%-3s".'~color{0 0 .55}'. "Tech".'~color{0 0 .55}'."%3s"." Flag".'~color{0 0 .55}'."%5s". "Clock Warr".'~color{0 0 .55}'."%5s". "Fail".'~color{0 0 .55}'."%5s"." QC".'~color{0 0 .55}'."%-3s".'~font{default}'.border("|")."\n",
                                                "","","","","","","","","","",""));
		#push (@tech_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold9}'."%4s".'~color{0 0 .55}'. "Tech".'~color{0 0 .55}'."%3s"." Flag".'~color{0 0 .55}'."%5s". "Clock Warr".'~color{0 0 .55}'."%5s". "Fail".'~color{0 0 .55}'."%5s"." QC".'~color{0 0 .55}'."%-3s".'~font{default}'.border("|")."\n",
                 #                               "","","","","",""));
        }
        while (my @tech_row = $tech_details_qry->fetchrow_array) {
			#push(@newarray,@tech_row);
           ($ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours) = @tech_row;
		$Text::Wrap::columns = 28;
            # push (@tech_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold9}'."%4s".'~color{0 0 .55}'. "Tech".'~color{0 0 .55}'."%3s"." Flag".'~color{0 0 .55}'."%5s". "Clock Warr".'~color{0 0 .55}'."%5s". "Fail".'~color{0 0 .55}'."%5s"." QC".'~color{0 0 .55}'."%-51s".'~font{default}'.border("|")."\n",
             #                                   "","","","","",""));
             if ($ro_job_tech_line % 2 == 0){
			push (@tech_section_array, sprintf('~font{DejaVuSansMono-Bold9}'."%6s".'~color{0 0 .55}'."%2s%2s".'~color{0 0 .55}'."%4s%5s".'~color{0 0 .55}'."%4s%5s".'~color{0 0 .55}'."%5s".'~color{0 0 .55}'."%5s".'~color{0 0 .55}'."%5s".'~color{0 0 .55}'."%-11s".'~font{default}'.border("|")."\n",
                                               "",$ro_job_tech_line.': ',$tech_id,"",$booked_hours,"", $actual_hours,"","","",""));
	               $tech_section_array[@tech_section_array - 2] = $tech_section_array[@tech_section_array - 2] . $tech_section_array[@tech_section_array - 1 ];
				delete($tech_section_array[@tech_section_array - 1 ]);

              }
	     else{
             	push (@tech_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold9}'."%1s".'~color{0 0 .55}'."%2s%2s".'~color{0 0 .55}'."%4s%5s".'~color{0 0 .55}'."%4s%5s".'~color{0 0 .55}'."%5s".'~color{0 0 .55}'."%5s".'~color{0 0 .55}'."%5s".'~color{0 0 .55}'."%-2s".'~font{default}'."",
                                              "",$ro_job_tech_line.': ',$tech_id,"",$booked_hours,"", $actual_hours,"","","",""));
	
		
	    }
        }
	if($tech_details_qry->rows % 2 == 1){
  				#$array_size=@tech_section_array;
				push (@tech_section_array, sprintf('~font{DejaVuSansMono-Bold9}'."%11s".border("#")."\n",""));
		                  $tech_section_array[@tech_section_array - 2] = $tech_section_array[@tech_section_array - 2] . $tech_section_array[@tech_section_array - 1 ];
				delete($tech_section_array[@tech_section_array - 1 ]);
       		}
	}
    return @tech_section_array;
}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line) = @_;
    my @parts_section_array;

    my $parts_qry = $conn->prepare("SELECT
                                        ro_part_line,
                                        --concat(parts_prefix,' ',part_number,' ',parts_suffix) as part_number,
                                        part_description,
                                        quantity_sold,
                                        unit_cost,
                                        unit_sale,
                                        parts_prefix,
                                        part_number,
                                        parts_suffix
                                    FROM repair_part rp                                   
                                    WHERE rp.ro_number = ? AND rp.ro_line = ? AND rp.ro_job_line = ? AND quantity_sold !=0");
    $parts_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_part_line, $part_description, $quantity_sold, $unit_cost, $unit_sale ,$parts_prefix, $part_number, $parts_suffix);
     $parts_sale = 0;
    $parts_cost = 0;
     $parts_total_sale = 0;
    $parts_entries = $parts_qry->rows;
    if($parts_entries > 0){

        while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_description, $quantity_sold, $unit_cost, $unit_sale ,$parts_prefix, $part_number, $parts_suffix) = @parts_row;
            $parts_sale = 0;
            $parts_cost = 0;
            $parts_sale = $parts_sale + ($unit_sale*$quantity_sold);
            $parts_cost = $parts_cost + ($unit_cost*$quantity_sold);
            $parts_total_sale = $parts_total_sale+$parts_sale;
            $Text::Wrap::columns = 25;
            my $wrapped_part_description = fill('', '', expand($part_description));
            my @part_description = split "\n", $wrapped_part_description;
                         
            push (@parts_section_array, sprintf(border(">").'~font{DejaVuSansMono9}'."%1s".'~color{0 0 0}'. "Part:".'~color{0 0 0}'." %7s %-2s %-14s %-7s %-2s %-25s".'~color{0 0 0}'." Qty:".'~color{0 0 0}'."%3d%2s".'~color{0 0 0}'."%5s%-8s%4s%8s".'~font{default}'.border("|")."\n",
 			"", $parts_prefix, "", $part_number, $parts_suffix, "", $part_description[0], $quantity_sold,"", "Cost:",$parts_cost, "", currency_format($parts_sale))); 
                         
 #"", $parts_prefix, $part_number, $parts_suffix, $part_description_list[0], $quantity_sold,"", "Cost:".$parts_cost, "\$".$parts_sale ));
            

            
        }
    }
    return @parts_section_array;
}

# Subroutine to prepare the MISC section of a JOB
sub make_misc_section{
    my ($ro_line) = @_;
    my @misc_section_array;
    my $misc_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       coalesce(other_cost,0)::numeric as other_cost,
                                      coalesce(other_sale,0)::numeric as other_sale
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line =? AND item_type = 'MISC'");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $misc_code, $misc_description, $misc_cost, $misc_sale);
    $misc_job_sale = 0;
    $misc_job_cost = 0;
    my $other_sale=0.00;
    my $other_cost=0.00;
    $misc_entries = $misc_qry->rows;
    if($misc_entries > 0){

        while (my @misc_row = $misc_qry->fetchrow_array) {
            ( $misc_code, $misc_description, $misc_cost, $misc_sale) = @misc_row;
            $Text::Wrap::columns = 28;
            my $wrapped_misc_description = fill('', '', expand($misc_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;
             if ($misc_sale == 0){
                $other_sale=0.00;
            }
            else{
                    $other_sale=$misc_sale;
            }

            if ($misc_cost == 0){
                $other_cost=0.00;
            }
            else{
                    $other_cost=$misc_cost;
            }
            push (@misc_section_array, sprintf(border(">").'~font{DejaVuSansMono9}'."%1s".'~color{0 0 0}'. "Misc:".'~color{0 0 0}'."%-37s %-25s".'~color{0 0 0}'.'~color{0 0 0}'." %22s %5s%5s".'~color{0 0 .55}'.'~font{default}'.border("|")."\n",
                                                "", "", $misc_description_list[0], "",  "" , "\$".$other_cost));
            foreach my $i (1 .. $#misc_description_list) {
                push (@misc_section_array, sprintf(border(">")."%4s%-28s%-36s".border("|")."\n", "", $misc_description_list[$i], ""));
            }
            $misc_job_sale = $misc_job_sale + $misc_sale;
            $misc_job_cost = $misc_job_cost + $misc_cost;
        }
    }
    return @misc_section_array;
}

# Subroutine to prepare the GOG section of a JOB
sub make_gog_section{
    my ($ro_line) = @_;
    my @gog_section_array;
    my $gog_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'GOG'");
    $gog_qry->execute($ro_number, $ro_line);
    my ( $gog_code, $gog_description, $gog_cost, $gog_sale);
    $gog_job_sale = 0;
    $gog_job_cost = 0;
    $gog_entries = $gog_qry->rows;
    if($gog_qry->rows > 0){
        while (my @gog_row = $gog_qry->fetchrow_array) {
            ( $gog_code, $gog_description, $gog_cost, $gog_sale) = @gog_row;
            $Text::Wrap::columns = 28;
            my $wrapped_gog_description = fill('', '', expand($gog_description));
            my @gog_description_list = split "\n", $wrapped_gog_description;

            foreach my $i (1 .. $#gog_description_list) {
            }
            $gog_job_sale = $gog_job_sale + $gog_sale;
            $gog_job_cost = $gog_job_cost + $gog_cost;
        }
    }
    return @gog_section_array;
}

# Subroutine to prepare the SUBLET section of a JOB
sub make_sublet_section{
    my ($ro_line) = @_;
    my @sublet_section_array;
    my $sublet_qry = $conn->prepare("SELECT
                                        other_code,
                                        other_description,
                                        other_cost,
                                        other_sale
                                    FROM repair_other
                                    WHERE ro_number = ? AND ro_line =? AND item_type = 'SUBLET'");
    $sublet_qry->execute($ro_number, $ro_line);
    my ( $sublet_code, $sublet_description, $sublet_cost, $sublet_sale);
    $sublet_job_sale = 0;
    $sublet_job_cost = 0;
    $sublet_entries = $sublet_qry->rows;
    if($sublet_entries > 0){
        while (my @sublet_row = $sublet_qry->fetchrow_array) {
            ( $sublet_code, $sublet_description, $sublet_cost, $sublet_sale) = @sublet_row;
            $Text::Wrap::columns = 28;
            my $wrapped_sublet_description = fill('', '', expand($sublet_description));
            my @sublet_description_list = split "\n", $wrapped_sublet_description;
            foreach my $i (1 .. $#sublet_description_list) {
            }
            $sublet_job_sale = $sublet_job_sale + $sublet_sale;
            $sublet_job_cost = $sublet_job_cost + $sublet_cost;
        }
    }
    return @sublet_section_array;
}

# Subroutine to prepare the DEDUCTIBLE section of a JOB
sub make_deductible_section{
    my ($ro_line) = @_;
    my @ded_section_array;
    my $ded_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'DEDUCTIBLE'");
    $ded_qry->execute($ro_number, $ro_line);
    my ( $ded_code, $ded_description, $ded_cost, $ded_sale);
    $ded_job_sale = 0;

    $ded_job_cost = 0;
    $ded_entries = $ded_qry->rows;
    if($ded_entries > 0){
        while (my @ded_row = $ded_qry->fetchrow_array) {
            ( $ded_code, $ded_description, $ded_cost, $ded_sale) = @ded_row;
            $Text::Wrap::columns = 28;
            my $wrapped_ded_description = fill('', '', expand($ded_description));
            my @ded_description_list = split "\n", $wrapped_ded_description;
            foreach my $i (1 .. $#ded_description_list) {
            }
            $ded_job_sale = $ded_job_sale + $ded_sale;
            $ded_job_cost = $ded_job_cost + $ded_cost;
        }
    }
    return @ded_section_array;
}


# Subroutine to prepare the TOTAL section of a JOB
sub make_job_total_section{
    my ($ro_line, $sublet_amount) = @_;
    my @job_total_section_array;
    my $job_total_amount=0;
   my $tax_line="";
   if($cust_ro_tax==0 || $cust_ro_tax==0.00){
      $tax_line="";
   }
   else{
	 $tax_line="  Tax:".currency_format($cust_ro_tax);
   }
   my $misc_line="";
   if($misc_job_cost==0 || $misc_job_cost==0.00){
       $misc_line="";
   }
   else{
	$misc_line="  Misc:".currency_format($misc_job_cost);
   }

   my $sublet_line="";
   if($sublet_amount == 0 || $sublet_amount == 0.00){
       $sublet_line="";
   }
   else{
	$sublet_line="  Sublet:".currency_format($sublet_amount);
   }

    $job_total_amount=$parts_total_sale+$misc_job_cost+$sublet_amount+$cust_ro_tax+$sale_amount;
    push (@job_total_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold9}'.'~color{0 0 0}'."%-105s".'~color{0 0 0}'.border("|").'~font{DejaVuSansMono9}'."\n", "_"x105));
    push (@job_total_section_array, sprintf(border(">").'~font{DejaVuSansMono-Bold9}' .'~color{0 0 0}'." %-6s".'~color{0 0 .55}'." %-7s %-7s %-7s %-10s %-5s".'~color{0 0 0}'."    %-3s".'~color{0 0 .55}'."%-15s".'~color{0 0 .55}'.'~font{default}'.border("|").'~font{DejaVuSansMono9}'."\n",
                                        "JOB TOTALS"," Labor:".currency_format($sale_amount), "  Parts:".currency_format($parts_total_sale), $tax_line, $misc_line, $sublet_line," Total:",currency_format($job_total_amount)));      
    push (@job_total_section_array, sprintf(border("#").'~font{DejaVuSansMono-Bold9}'.'~color{0 0 0}'."%-105s".'~color{0 0 0}'.border("|").'~font{DejaVuSansMono9}'."\n", "_"x105));                               

    $customer_total_amount= $customer_total_amount+$parts_total_sale+$misc_job_cost+$cust_ro_tax+$sale_amount;
    if ($lbr_type eq "Internal"){
        $intern_lbr_cost = $intern_lbr_cost+$labor_cost;
        $intern_lbr_sale = $intern_lbr_sale+$sale_amount;
        $intern_prt_cost = $intern_prt_cost+$parts_cost;
        $intern_prt_sale = $intern_prt_sale+$parts_sale;
        $intern_misc_cost = $intern_misc_cost+$misc_job_cost;
        $intern_misc_sale = $intern_misc_sale+$misc_job_sale;
        $intern_gog_cost = $intern_gog_cost+$gog_job_cost;
        $intern_gog_sale = $intern_gog_sale+$gog_job_sale;
        $intern_sublet_cost = $intern_sublet_cost+$sublet_job_cost;
        $intern_sublet_sale = $intern_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    } elsif ($lbr_type eq "Customer"){
        $cust_lbr_cost = $cust_lbr_cost+$labor_cost;
        $cust_lbr_sale = $cust_lbr_sale+$sale_amount;
        $cust_prt_cost = $cust_prt_cost+$parts_cost;
        $cust_prt_sale = $cust_prt_sale+$parts_sale;
        $cust_misc_cost = $cust_misc_cost+$misc_job_cost;
        $cust_misc_sale = $cust_misc_sale+$misc_job_sale;
        $cust_gog_cost = $cust_gog_cost+$gog_job_cost;
        $cust_gog_sale = $cust_gog_sale+$gog_job_sale;
        $cust_sublet_cost = $cust_sublet_cost+$sublet_job_cost;
        $cust_sublet_sale = $cust_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    } elsif ($lbr_type eq "Warranty"){
        $warr_lbr_cost = $warr_lbr_cost+$labor_cost;
        $warr_lbr_sale = $warr_lbr_sale+$sale_amount;
        $warr_prt_cost = $warr_prt_cost+$parts_cost;
        $warr_prt_sale = $warr_prt_sale+$parts_sale;
        $warr_misc_cost = $warr_misc_cost+$misc_job_cost;
        $warr_misc_sale = $warr_misc_sale+$misc_job_sale;
        $warr_gog_cost = $warr_gog_cost+$gog_job_cost;
        $warr_gog_sale = $warr_gog_sale+$gog_job_sale;
        $warr_sublet_cost = $warr_sublet_cost+$sublet_job_cost;
        $warr_sublet_sale = $warr_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    }
    return @job_total_section_array;
}

#Subroutine to prepare the FOOTER section of the invoice
sub get_footer {
    my @footer_section;
    #my $ext_warranty_pay = "";
    #my $factory_warranty_pay = "";
    #my $internal_pay = "";

    my ($is_end, $page_number) = @_;
    my $grand_total = $cust_prt_cost+$cust_misc_cost+$cust_ro_tax;
    my $total_details_qry = $conn->prepare("SELECT
                                               cp_total::money AS cp_total,
                                               wp_total::money AS factory_warranty_pay,
                                               xp_total::money AS ext_warranty_pay,
                                               in_total::money AS internal_pay
                                            FROM repair_order ro
                                            WHERE ro_number = ?");
    $total_details_qry->execute($ro_number);
    my ($cp_total,$factory_warranty_pay,$ext_warranty_pay,$internal_pay);
    if($total_details_qry->rows > 0){

           my @total_row = $total_details_qry->fetchrow_array;
            ($cp_total,$factory_warranty_pay,$ext_warranty_pay,$internal_pay) = @total_row;
    }

    push (@footer_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s"."\n", "")); 
    push (@footer_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%-100s"."\n", "")); 
    push (@footer_section, sprintf(border("#").'~font{DejaVuSansMono9}'.'~color{0 0 0}'."%1s %-16s %-17s %-50s %8s".'~font{DejaVuSansMono9}'.'~color{0 0 .55}'.border("|")."\n","",substr($ext_warranty_pay, 0, 10), substr($factory_warranty_pay,0, 28),substr($internal_pay,0,15), substr($cp_total,0, 16)));
    push (@footer_section, sprintf(border(">").'~font{DejaVuSansMono9}'.'~color{0 0 0}'."%1sPage %-80s ".'~font{DejaVuSansMono9}'.'~color{0 0 .55}'.border("|")."\f", "", $curr_page_num));
   
return @footer_section;
}

# Subroutine to get the RO TAX amount
sub get_ro_tax_amount {
    my ($ro_line) = @_;
    $cust_ro_tax = 0;
    $warr_ro_tax = 0;
    $intern_ro_tax =0;
    my $tax_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number=? AND ro_line = ? AND item_type = 'TAX'");
    $tax_qry->execute($ro_number, $ro_line);
    my ( $tax_type, $tax_amount);

    while (my @tax_row = $tax_qry->fetchrow_array) {
        ( $tax_type, $tax_amount) = @tax_row;
        if ($tax_type eq "C"){
            $cust_ro_tax = $tax_amount;

        }elsif ($tax_type eq "W"){
            $warr_ro_tax = $tax_amount;
        }elsif ($tax_type eq "I"){
            $intern_ro_tax = $tax_amount;
        }
    }
}

# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_grand_total_section {
    get_ro_tax_amount();

    # Customer Totals
    #my @total_section_array;
    my $total_details_qry = $conn->prepare("SELECT
                                                coalesce(cp_total,0)::money AS cp_total,
                                                coalesce(cp_labor,0)::money AS cp_labor,
                                                coalesce(cp_taxes,0)::money AS cp_taxes,
                                                coalesce(cp_parts,0)::money AS cp_parts,
                                                coalesce(cp_other,0)::money AS cp_other,
                                                sum(rj.parts_cost)::money as total_parts_cost,
                                                sum(rj.labor_cost)::money as total_labor_cost
                                            FROM repair_order ro
                                                LEFT JOIN du_dms_adam_proxy.repair_job rj USING(ro_number)
                                            WHERE ro_number = ?
                                            GROUP BY ro.ro_number");
    $total_details_qry->execute($ro_number);
    my ($cp_total,$cp_labor,$cp_taxes,$cp_parts,$cp_other,$total_parts_cost,$total_labor_cost);
    if($total_details_qry->rows > 0){

           my @total_row = $total_details_qry->fetchrow_array;
            ($cp_total,$cp_labor,$cp_taxes,$cp_parts,$cp_other,$total_parts_cost,$total_labor_cost) = @total_row;

     push (@grand_total_section, sprintf(border("#").'~font{DejaVuSansMono-Bold9}'."%2s".'~color{0 0 0}'."%8s"."%30s".'~font{DejaVuSansMono-Bold9}'.'~color{0 0 0}'." %12s".'~font{default}'.border("|")."\n",
                                "", "CUSTOMER TOTALS:".$cp_total, "","Lbr/Pts Cost:".$total_labor_cost."/".$total_parts_cost ));
            
    my $total_line;   
    if ($cp_labor ne '$0.00'){
           $total_line = "Labor:".$cp_labor;
    }
    if ($cp_parts ne '$0.00'){
           $total_line = $total_line."		Parts:".$cp_parts
    }
    if ($cp_other ne '$0.00'){
           $total_line = $total_line."		Misc:".$cp_other
    }
    if ($cp_taxes ne '$0.00'){
           $total_line = $total_line."		Tax:".$cp_taxes
    }

push (@grand_total_section, sprintf(border("#").'~color{0 0 0}'." %2s".'~font{DejaVuSansMono-Bold9}'.'~color{0 0 0}'."%8s".'~font{default}'.border("|")."\n",
                                "",$total_line));
   
  }
push (@grand_total_section, sprintf(border("#").'~font{DejaVuSansMono-Bold9}'.'~color{0 0 0}'." %-105s".'~color{0 0 .55}'.'~font{DejaVuSansMono9}'.border("|")."\n","_"x104));
}

# Subroutine to calculate the total no of pages in the invoice
# Update page count calculation section, if any modification is made in the body of the report
sub calculate_page_count {
    my $page_count = 1;
    my $page_height = 0;
    my $total_page_content_height = 0;

    for my $job(@job_section){
        $total_page_content_height = $total_page_content_height + scalar(@$job);
    }
    $total_page_content_height = $total_page_content_height + scalar(@grand_total_section);

    if($total_page_content_height <= ($page_max_height - $page_header_height - $invoice_dealer_header)){
        $page_count = 1;
    } else{
        $page_count = ceil(($total_page_content_height - ($page_max_height - $page_header_height - $invoice_dealer_header)) /
                            ($page_max_height - $page_header_height)) + 1;
    }
   return $page_count;
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")."%95s".border("|")."\n", "");
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
 sub print_header_section {
    my ($page_num) = @_;
    my %hash = (pg_num => $page_num );
if ($page_num== 1){
    foreach (@header_section)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
}
else{

      foreach (@header_subsection)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        
        print FILE $header_line;
    }
}
 }

sub print_dealer_header_section {
    print_blank_line($invoice_dealer_header);
 }

# Subroutine for pagination
sub paginate_and_print_segment {
my  $total_page_content_heightnew;
    my (@section_array) = @_;
    $total_header_height=0;
    my $sub_header_height;
    if($curr_page_num == 1){
        $total_header_height = $page_header_height + $invoice_dealer_header ;
         $sub_header_height = $invoice_dealer_header + 3 ;
    } else{
        $total_header_height = $invoice_dealer_header + 3 ;
    }

     # Print section that fit in the current page
if ($curr_page_num == 1){

    if(scalar(@section_array) < (($page_max_height - $total_header_height) - $curr_page_height) - 3){

        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{

    	for my $sub_section(@section_array){
    		if(($curr_page_height < ($page_max_height - $total_header_height) - 3) && $curr_page_num == 1)
        	{
              		print FILE $sub_section;
              		$curr_page_height = $curr_page_height + 1;
         	}
	 	elsif(($curr_page_height >= ($page_max_height - $total_header_height) - 3) && $curr_page_num == 1)
                    {
                	print FILE get_footer(0, $curr_page_num);
                	$curr_page_num = $curr_page_num + 1;
                	print_dealer_header_section();
                	print_header_section ($curr_page_num);
                	print FILE $sub_section;
                	$curr_page_height = 1;
           	}
                elsif(  $curr_page_num > 1){
		        if($curr_page_height < ($subpage_max_height - $sub_header_height) - 3)
        		{
            			print FILE $sub_section;
              			$curr_page_height = $curr_page_height + 1;
         		}
                        else{
				print FILE get_footer(0, $curr_page_num);
                		$curr_page_num = $curr_page_num + 1;
                 		print_dealer_header_section();
                		print_header_section ($curr_page_num);
                		print FILE $sub_section;
                		$curr_page_height = 1;
           		}
                }
     	}
    }

}
else{

 if(scalar(@section_array) <= (($subpage_max_height - $total_header_height) - $curr_page_height) - 3){

        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{
     for my $sub_section(@section_array){
    	if($curr_page_height < ($subpage_max_height - $total_header_height) - 3)
        {
            
              print FILE $sub_section;
              $curr_page_height = $curr_page_height + 1;
         }
	 else{
               
                print FILE get_footer(0, $curr_page_num);
                $curr_page_num = $curr_page_num + 1;
                 print_dealer_header_section();
                print_header_section ($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
           }
     }
}
}
}

# Subroutine to prepare end of invoice section
sub make_end_of_invoice_section {
   # push (@end_of_invoice_section, sprintf(border("#")." %116s ".border("|")."\n", "")); # Separator Line
    #push (@end_of_invoice_section, sprintf(border("#")." %116s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#").'~font{DejaVuSansMono9}'." %4s".'~color{0 0 0}'."%60s".'~color{0 0 .55}'."%40s".border("|")."\n",
           "",
           "YOU MAY RECEIVE A SURVEY",
           ""));
   push (@end_of_invoice_section, sprintf(border("#").'~font{DejaVuSansMono9}'." %20s".'~color{0 0 0}'."%65s".'~color{0 0 .55}'."%18s ".border("|")."\n",
           "",
           "IF FOR ANY REASON YOU CAN NOT ANSWER COMPLETELY SATISFIED,",
           ""));
   push (@end_of_invoice_section, sprintf(border("#").'~font{DejaVuSansMono9}'." %20s".'~color{0 0 0}'."%65s".'~color{0 0 .55}'."%18s ".border("|")."\n",
           "",
           "PLEASE ADVISE THE SERVICE MANAGER SO WE CAN CORRECT THE",
           ""));
  push (@end_of_invoice_section, sprintf(border("#").'~font{DejaVuSansMono9}'." %20s".'~color{0 0 0}'."%65s".'~color{0 0 .55}'."%18s ".border("|")."\n",
           "",
           "CONCERNS. COMPLETE CUSTOMER SATISFACTION IS OUR GOAL!",
           ""));
}

# Subroutine to prepare invoice note section
sub make_invoice_note_section {
    for (my $i = 0; $i < $invoice_note_lines; $i++) {
        push (@invoice_note_section, sprintf(border("#")."%95s".border("|")."\n", ""));
    }
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
    ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

    ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

    ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

    ($sale_amount, $parts_cost, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

    $job_hours = 0;

    ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;
    undef $cust_ro_tax;
    undef $warr_ro_tax;
    undef $intern_ro_tax;  
    #$curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @header_subsection;
    undef @job_section;
    undef @grand_total_section;
    undef @end_of_invoice_section;
    undef @invoice_note_section;

    ########## Prepare invoice ##########
    make_header();
    $extra_tech_line = 0;
    make_job_section();
    make_grand_total_section();
    make_end_of_invoice_section();
    make_invoice_note_section();
    $pages = calculate_page_count();

    ########## Print invoice ##########
    print_dealer_header_section();
    $curr_page_height = 0;
    print_header_section ($curr_page_num);

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }

    # Pagination of Grand total section
    paginate_and_print_segment(@grand_total_section);

     # Pagination of invoice end section
    paginate_and_print_segment(@end_of_invoice_section);
    if($curr_page_num == 1){
        $total_header_height = $page_header_height + $invoice_dealer_header;
    } else{
        $total_header_height = $invoice_dealer_header + 3;
    }
    # End of invoice footer

 if($curr_page_num == 1){
    if($curr_page_height == (($page_max_height - $total_header_height) - $invoice_dealer_header)){
        print FILE get_footer(1, $curr_page_num);
    } else {

       my $line_count=0;
       my $blank_line_count=0;
       $line_count=$curr_page_height +  $total_header_height + 3;
       $blank_line_count=$page_max_height - $line_count;
       print_blank_line($blank_line_count);
       # print_blank_line(($page_max_height - $total_header_height)  - $curr_page_height + 2);
        print FILE get_footer(1, $curr_page_num);
    }
}
else{
    if($curr_page_height == ($subpage_max_height - $total_header_height)){
        print FILE get_footer(1, $curr_page_num);
    } else {
       my $line_count=0;
       my $blank_line_count=0;
       $line_count=$curr_page_height +  $total_header_height + 3;
       $blank_line_count=$subpage_max_height - $line_count;
       print_blank_line($blank_line_count);

        #print_blank_line(($subpage_max_height - $total_header_height) - $curr_page_height + 1);
        print FILE get_footer(1, $curr_page_num);
    }
}

}

sub print_ro_inv_bg_head {
    printf(FILE $cust_inv_head);
}

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}

sub align_center {
    # Return $str centered in a field of $col $padchars.
    # $padchar defaults to ' ' if not specified.
    # $str is truncated to len $column if too long.

    my ($str, $col, $padchar) = @_;
    $padchar = ' ' unless $padchar;
    my $strlen = length($str);
    $str = substr($str, 0, $col) if ($strlen > $col);
    $strlen = length($str);
    my $fore = int(($col - $strlen) / 2);
    my $aft = $col - ($strlen + $fore);
    $padchar x $fore . $str . $padchar x $aft;
}
sub currency_format{
    use Number::Format qw(:subs);
    my $precision=2;
    my $symbol='';
    my ($number) = @_;
    my $formatted =format_price($number, $precision, $symbol);
    # return  $formatted;
    return  '$'.$formatted;
}
sub numeric_format{
    use Number::Format qw(:subs);
    my $precision=2;
    my ($number) = @_;
    my $formatted =format_price($number, $precision);
    return  $formatted;
}
