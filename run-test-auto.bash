#!/usr/bin/env bash

#
# Optional args:
# $1 - Test directory.  Prompts if not provided
# $2 - Optional "copy" directive to use upon re-run
#      if the validation failure of the prior run
#      is deemed acceptable and wants to be copied
#      to the output directory to replace the existing
#      files.
#
source "${DU_ETL_HOME}"/DU-DMS/DMS-Fortellis/Fortellis.env

source "${DU_ETL_HOME}"/src/test-common/run-integration-test-fixture.bash

run_fixture "$@" # processes the caller's vars
