#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

function main() {
#   prompt_continuation 'Manually Clear tmp tree then continue.'
#   perform_extraction
    perform_processing
    perform_distribute
}

function perform_extraction() {
    ./run-live.bash
}

function perform_processing() {
(
    cd Processor-Application
    find /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/run-live-temp/fortellis-zip-eti/ \
         -maxdepth 1 -type f -exec ./manual-zip-process {} \;
)
}

function perform_distribute() {
    find /home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/manual/bundle/ \
         -maxdepth 1 -type f -exec ./send-bundle-live-hpdog {} \;
}

main
