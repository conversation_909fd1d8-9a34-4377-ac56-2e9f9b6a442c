#!/bin/bash

TARGET_DIR="/s3disk/archive/dropbox-ro-imports"
LOG_FILE="/etl/log/sharepoint-dropbox-files.txt"
DAYS_OLD=185
echo "-----------------------dropbox-ro-imports start---------------------" >> "$LOG_FILE"
echo "Deleted files  on dropbox-ro-imports $(date):" >> "$LOG_FILE"

echo "Deleted files: Automate" >> "$LOG_FILE"
find "$TARGET_DIR/automate" -maxdepth 1 -type f -name "*.zip" -mtime +$DAYS_OLD -print -delete >> "$LOG_FILE"

echo "Deleted files: Reynolds" >> "$LOG_FILE"
find "$TARGET_DIR/reynolds" -maxdepth 1 -type f -name "*.zip" -mtime +$DAYS_OLD -print -delete >> "$LOG_FILE"

echo "Deleted files: UCS" >> "$LOG_FILE"
find "$TARGET_DIR/ucs" -maxdepth 1 -type f -name "*.zip" -mtime +$DAYS_OLD -print -delete >> "$LOG_FILE"

echo "Deleted files: DealerTrack" >> "$LOG_FILE"
find "$TARGET_DIR/ucs" -maxdepth 1 -type f -name "*.zip" -mtime +$DAYS_OLD -print -delete >> "$LOG_FILE"

echo "-----------------------dropbox-ro-imports end---------------------" >> "$LOG_FILE"

