#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

REPO_ROOT="$(cd $(dirname $0) && pwd)"

SCHEMA_NAME="${1:?Schema Name Required}"
TARGET_DIR="${2:?Target Directory Required}"
CONVERT_TO_PDF="${3:-true}"
# Ver generic_proxy:- Generic proxy invoice,
# Ver invoice_mimic:- Mimic invoice merged with Auto/mate template
VERSION="${4:-"generic_proxy"}"

SINGLE_STORE_FLAG="${6:-false}"
CUSTOM_BRANCH_NAME=$7
PORSCHE_STORE="${8:-false}"
BRAND=$9
DUAL_PROXY="${11}"


if [[ "$VERSION" = "generic_proxy" ]]; then
    "$REPO_ROOT"/generate-fixed-width-proxy-invoice.bash \
            "$SCHEMA_NAME" \
            "$TARGET_DIR" \
            "$CONVERT_TO_PDF"
elif [[ "$VERSION" = "invoice_mimic" ]]; then
    TMP_DIR="/tmp/du-etl-reynolds3pa-proxyinvoice-work"
    mkdir -p "$TMP_DIR"
    clear_dir "$TMP_DIR"
    BOOK_DIR="$TMP_DIR"/bookmarking

    if [[ "$DUAL_PROXY" = 'true' ]]; then
        TEXT_OUT=text-porsche
        PDF_OUT=pdf-porsche
        BUNDLE_OUT=bundle-porsche
    else
        TEXT_OUT=text
        PDF_OUT=pdf
        BUNDLE_OUT=bundle
        clear_dir "$TARGET_DIR"
    fi

    [[ -d "$TARGET_DIR" ]] || die "$2 must exist and should be empty"
    
    rm -rf /tmp/input
    mkdir /tmp/input
        
    mkdir "$TARGET_DIR/$TEXT_OUT" || die "Could not prepare text directory"
    mkdir "$TARGET_DIR/$TEXT_OUT/inv-bg" || die "Could not prepare text directory"
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        mkdir "$TARGET_DIR/$PDF_OUT"  || die "Could not prepare pdf directory"
    fi

    (
        cd "$TARGET_DIR"/$TEXT_OUT || die "Failed to set target directory as active"

        if [[ "$VERSION" = "invoice_mimic" ]]; then
            "$REPO_ROOT"/create-proxy-invoice-report-reynoldsrci-mimic.pl "${SCHEMA_NAME}" "${SINGLE_STORE_FLAG}" "${CUSTOM_BRANCH_NAME}" "${PORSCHE_STORE}" "${BRAND}"
        fi

        #Create fixed width proxy invoice PDF report by iterating the proxy invoice txt reports
        loop_count=0
        if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
            progress "Beginning PDF Conversions - Count: $(find . -maxdepth 1 -type f | wc -l)"
            echo "$(date +%H:%M:%S) Starting"

            echo "" > "$TMP_DIR"/"blank.txt";
            enscript -q \
                    --no-header \
                    -r \
                    -L 1 "$TMP_DIR"/"blank.txt" \
                    -o - \
                    | ps2pdf - "$TMP_DIR"/"blank.pdf"

            for pi in ./*.txt; do
            sed -i -Ee 's/\~([A-Z])/\1/g' "${pi}"
                sed -i -Ee 's/\~([[:space:]])/\1/g' "${pi}"
                sed -i -Ee 's/\~([0-9])/\1/g' "${pi}"
                ((loop_count++))
                if [[ $(($loop_count%200)) = 0 ]]; then
                    echo "$(date +'%H:%M:%S') Converted $loop_count"
                fi
                clear_dir "$BOOK_DIR"
                [ -f "$pi" ] || break
                filename=$(basename "$pi")
                filename="${filename%.*}"
                if [[ "$VERSION" = "invoice_mimic" ]]; then
                    enscript -q \
                            -f fCourier-Bold@7 \
                            -e~ \
                            --no-header \
                            -s 1 \
                            --margins=37:2:14:15 \
                            -L 73 "${pi}" \
                            -o - \
                        | sed '/^\/bgs /,/^}/{
                                /x y blskip/s//x   y height .2 mul add   blskip/
                                }' | ps2pdf - "$BOOK_DIR"/"${filename}.pdf"
                fi

                pdftk "$BOOK_DIR"/"${filename}.pdf" dump_data output "${BOOK_DIR}"/temp-pdf-metadata.txt

                                printf 'BookmarkBegin\nBookmarkTitle: %s\nBookmarkLevel: 1\nBookmarkPageNumber: 1' "${filename}" \
                                    > "${BOOK_DIR}"/temp-pdf-new-bookmark.txt

                                cat "${BOOK_DIR}"/temp-pdf-metadata.txt \
                                    "${BOOK_DIR}"/temp-pdf-new-bookmark.txt \
                                    > "${BOOK_DIR}"/temp-pdf-new-metadata.txt

                                pdftk "${BOOK_DIR}"/"${filename}.pdf" \
                                    update_info \
                                    "${BOOK_DIR}"/temp-pdf-new-metadata.txt \
                                    output - \
                                    > ../"${PDF_OUT}/${filename}.pdf" 2>&1

                # enscript -q \
                #         -f fCourier-Bold@7 \
                #         --no-header \
                #         --margins=30:2:12:15 \
                #         -L 73 "./inv-bg/${filename}-inv-bg.txt" \
                #         -r \
                #         -o - \
                #     | ps2pdf - "$BOOK_DIR"/"${filename}-inv-bg.pdf"


                # pdftk "$BOOK_DIR"/"${filename}-inv-bg.pdf" "$TMP_DIR"/"blank.pdf" cat output "$BOOK_DIR"/"${filename}-append-blank.pdf"

                # pdftk "$BOOK_DIR"/"${filename}-append-blank.pdf" cat 1-endsouth output "$BOOK_DIR"/"${filename}-inv-bg-out.pdf"
  
                # qpdf --decrypt "$BOOK_DIR"/"${filename}.pdf" /tmp/input/"${filename}.pdf" 

                # pdftk /tmp/input/"${filename}.pdf" multibackground "$REPO_ROOT"/invoice-template/reynolds3pa_template.pdf output "$BOOK_DIR"/"${filename}-merged.pdf"

                # pdftk "$BOOK_DIR"/"${filename}-merged.pdf" multibackground "$BOOK_DIR"/"${filename}-inv-bg-out.pdf" output "$BOOK_DIR"/"${filename}-merged-inv-bg.pdf"

                # pdftk "$BOOK_DIR"/"${filename}.pdf" dump_data output "${BOOK_DIR}"/temp-pdf-metadata.txt

                # printf 'BookmarkBegin\nBookmarkTitle: %s\nBookmarkLevel: 1\nBookmarkPageNumber: 1' "${filename}" \
                #     > "${BOOK_DIR}"/temp-pdf-new-bookmark.txt


                # cat "${BOOK_DIR}"/temp-pdf-metadata.txt \
                #     "${BOOK_DIR}"/temp-pdf-new-bookmark.txt \
                #     > "${BOOK_DIR}"/temp-pdf-new-metadata.txt

                # pdftk "$BOOK_DIR"/"${filename}-merged-inv-bg.pdf" \
                #     update_info \
                #     "${BOOK_DIR}"/temp-pdf-new-metadata.txt \
                #     output - \
                #     > ../pdf/"${filename}.pdf" 2>&1
            done
            echo "$(date +%H:%M:%S) Ending"
        else
            progress "Skipping PDF Conversion as Requested"
        fi

    ) || die "Failed during Proxy Invoice generation"

    rm -rf "$TARGET_DIR"/"$TEXT_OUT"/inv-bg
 
    cd "$TARGET_DIR"/$TEXT_OUT
    
     # Create one or more PDF bundles from the individual PDF files
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        (
            cd "$TARGET_DIR/${PDF_OUT}"
            mkdir ../"${BUNDLE_OUT}"
            BUNDLE_PREFIXES=$(find . -mindepth 1 -maxdepth 1 -type f -exec basename {} \; | perl -ale 'print $1 if /(\d+)\d{2}.pdf/;' | sort | uniq)
            for prefix in $BUNDLE_PREFIXES; do
                FIRST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $1}')
                LAST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $NF}')
                FIRST=${FIRST_TMP::-4}
                LAST=${LAST_TMP::-4}
                pdftk ./"${prefix}"*.pdf cat output ../"${BUNDLE_OUT}"/bundle-"${FIRST}-${LAST}".pdf
            done
        )
    fi
    say "Done Creating Proxy Invoices"
else
    die "Invalid Proxy Version"
fi
