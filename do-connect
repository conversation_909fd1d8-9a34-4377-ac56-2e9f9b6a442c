#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

function conditional_connect() {
    local DIR_MNT="$1"
    local TRG_SSH="$2"
    local DIR_RMT="$3"

    if [[ -d "$DIR_MNT" ]]; then
        if has_files "$DIR_MNT"; then
            progress "$DIR_MNT Already Connected"
        else
            if sshfs "$TRG_SSH":"$DIR_RMT" "$DIR_MNT"; then
                say "$DIR_MNT Connected"
            else
                yell "Could not connect $DIR_MNT to $TRG_SSH:$DIR_RMT"
            fi
        fi
    fi
}

conditional_connect "/mnt/du-etl" "du-etl" "/etl/etl-vagrant/"
conditional_connect "/mnt/etl-nfs" "du-etl" "/etl"

exit 0
