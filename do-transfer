#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

# Dispatching script for invoice and raw data transferrence
# The original script has been moved to src/transfer/perform-raw-and-invoice-transfers
# If the first argument doesn't begin with a hypen we assume the call is inteded to
# be for the original script otherwise we call the new script with the same arguments.

say "Remote DU-ETL Work Files Located Under: ${DU_ETL_WORK_REMOTE:?Please export DU_ETL_WORK_REMOTE envar with path}"
timeout 3s test -f "$DU_ETL_WORK_REMOTE"/README
dirtest=$?
if [[ "$dirtest" = '124' ]]; then
    die "Failed to see $DU_ETL_WORK_REMOTE/README in three seconds"
elif [[ "$dirtest" = '1' ]]; then
    die "Remote file $DU_ETL_WORK_REMOTE/README does not exist!"
fi

FIRST_ARG="${1:?At least one option must be provided}"

SCRIPT_BASE="$DU_ETL_HOME"/src/transfer/bash
INVOICE_SCRIPT="$SCRIPT_BASE"/prepare-store-for-invoice-import

if [[ "${FIRST_ARG:0:1}" = '-' ]]; then
    # Invoices are now argument driven and channeled through a separate path
    "$INVOICE_SCRIPT" "$@"
else
    # Transfer raw data files from (typically) the FTP server to the
    # ETL machine as well as keep a local copy.  SIS Jobs already are
    # on the ETL machine so just have a way to pull them down locally.
    # The "push-" variants look for the named file locally and push
    # it up to the ETL machine.  Intended for use when there are importing
    # errors that need to be debugged locally.  If data file changes are
    # needed they can be made and then the updated data file can be pushed
    # to the ETL machine.
    if [[ "$FIRST_ARG" = 'job' ]]; then
        cp --verbose "$DU_ETL_WORK_REMOTE"/etl-sis/sis-zip/JOB_"${2}"_* \
                     "$DU_ETL_ETI_DIR_SIS" || exit 1

    elif [[ "$FIRST_ARG" = 'push-job' ]]; then
        cp --verbose "$DU_ETL_ETI_DIR_SIS"/JOB_"${2}"_* \
                     "$DU_ETL_WORK_REMOTE"/etl-sis/sis-zip/ || exit 1

    elif [[ "$FIRST_ARG" = 'rey' ]]; then
        if [[ ! -f "$2" ]]; then
            echo "Cannot locate named file: $2"
            exit 1
        fi

        cp --verbose "${2}" \
                     "$DU_ETL_WORK_REMOTE"/etl-reynolds/reynolds-zip/ || exit 1

        cp --verbose "${2}" \
                     "$DU_ETL_ETI_DIR_Reynolds"/ || exit 1

        rm --verbose "${2}"

    elif [[ "$FIRST_ARG" = 'cdk' ]]; then
        if [[ ! -f "$2" ]]; then
            echo "Cannot locate named file: $2"
            exit 1
        fi

        cp --verbose "${2}" \
                     "$DU_ETL_WORK_REMOTE"/etl-cdk/cdk-zip/ || exit 1

        cp --verbose "${2}" \
                     "$DU_ETL_ETI_DIR_CDK"/ || exit 1

        rm --verbose "${2}"

    else
        die "Unrecognized non-option argument: $FIRST_ARG"
    fi
fi

exit
