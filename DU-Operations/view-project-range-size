#!/usr/bin/env bash

function psql_rds() {
    psql "service=${GGS_PROD_PG_SERVICE}" "$@"
}

function psql_s360() {
    psql "service=solve360" "$@"
}

function psql_local() {
    # using solve360 for local since it is running 9.5
    # and we need the FILTER clause
    psql "service=solve360" "$@"
}

TMP_DIR='/tmp/du-etl/du-operations/'
FILELIST_NAME='view-project-range-size-scenariolist.tsv'
FILECOUNT_NAME='view-project-range-size-invoicecounts.tsv'
mkdir -p "$TMP_DIR"

psql_s360 <<SQL
SELECT set_config('search_path',
                  'middletier_solve360_data, solve360_api, solve360_rules',
                  false);

CREATE TEMP TABLE scenariolist AS
    SELECT DISTINCT scenariokey
      FROM s360_project_base
     WHERE scenariokey IS NOT NULL
           AND factory_deadline IS NOT NULL
           AND project_start::date >= '2016-01-01'::date;
;
\copy scenariolist TO '$TMP_DIR/$FILELIST_NAME'
SQL

if [[ "$1" = '--compute-counts' ]]; then
    echo "Computing Counts - Could Take A While)"
    psql_rds <<SQL
    BEGIN;
    SELECT set_config('work_mem',
                      '1GB',
                      false);
    CREATE TEMP TABLE scenariolist (scenariokey text PRIMARY KEY);
    \copy scenariolist FROM '$TMP_DIR/$FILELIST_NAME'

    CREATE TEMP TABLE scenarios_with_counts AS
    WITH scope_scenarios AS (
        SELECT ms.scenariokey,
               rangestartingro,
               rangeendingro,
               (SELECT invoicesequence
                  FROM mageinvoicesequencemaster ism
                 WHERE ism.scenariokey = ms.scenariokey
                       AND ism.invoicenumber = ms.rangestartingro)  AS firstseq,
               (SELECT invoicesequence
                  FROM mageinvoicesequencemaster ism
                 WHERE ism.scenariokey = ms.scenariokey
                       AND ism.invoicenumber = ms.rangeendingro)    AS lastseq
          FROM magescenario ms
          JOIN (SELECT * FROM scenariolist ORDER BY scenariokey) s USING (scenariokey)
    ),
    full_inv_range AS (
        SELECT ss.scenariokey, mis.invoicenumber
          FROM scope_scenarios ss
          JOIN mageinvoicesequencemaster mis ON (ss.scenariokey = mis.scenariokey
                                                 AND mis.invoicesequence BETWEEN ss.firstseq
                                                                             AND ss.lastseq)
    ), counting AS (
        SELECT scenariokey,
               count(distinct invoicenumber)
          FROM magepartdetail mpd
          JOIN magepartdetailextended mpde USING (scenariokey, partdetailid)
          JOIN full_inv_range              USING (scenariokey, invoicenumber)
         WHERE NOT finalexcluded
         GROUP BY  scenariokey
    )
    SELECT * FROM counting;

    \copy scenarios_with_counts TO '$TMP_DIR/$FILECOUNT_NAME'
SQL

fi

psql_local <<SQL
CREATE TEMP TABLE scenarios_with_counts(
    scenariokey text PRIMARY KEY,
    invoicecount integer NOT NULL
);

\copy scenarios_with_counts FROM  '$TMP_DIR/$FILECOUNT_NAME'

WITH range_bands (range_spec, fixed, variable) AS (
    VALUES ('[0,30)',     90, 0),
           ('[30,50)',   110, 0),
           ('[50,75)',   150, 0),
           ('[75,100)',  200, 0),
           ('[100,125)', 250, 0),
           ('[125,)',      0, 2)
)
SELECT range_spec                                      AS "Range Spec.",
       count(*)                                        AS "Scenario Count",
       round(avg(invoicecount), 0)                     AS "Group Avg.",
       round(count(*)::numeric /
                (sum(count(*)) OVER ()) * 100,
             0) * 2                                    AS "Grp%",
       format('%13s',
       to_char(sum(fixed + (invoicecount * variable)),
               'FM999G999'))                           AS "Model Expense",
       string_agg(DISTINCT invoicecount::text, ' ')
           FILTER (WHERE variable >= 0)                AS "Distinct Counts",
       string_agg(invoicecount::text, ' ')
           FILTER (WHERE variable > 0)                 AS "Individual Counts"
  FROM scenarios_with_counts
  JOIN range_bands ON (invoicecount <@ range_spec::int4range)
 GROUP BY GROUPING SETS ((range_spec), ())
 ORDER BY range_spec::int4range
;

SQL
