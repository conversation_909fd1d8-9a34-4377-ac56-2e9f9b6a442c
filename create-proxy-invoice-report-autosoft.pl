#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Time::Format qw(%time %strftime %manip);
use POSIX qw/ceil/;

no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];
my $is_porsche_store =  $ARGV[1];
my $is_separate = $ARGV[2];

# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;
my $file_name;
my $warr_ro_number;
my $cust_discount = 0;
my @header_section;
my @job_section;
my @ro_disc_section;
my @grand_total_section;
my @end_of_invoice_section;
my @invoice_note_section;

my ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

my ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

my ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

my ($sale_amount, $labor_cost, $parts_cost, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

my $job_hours = 0;
my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax, $warr_deduct_amount_add, $warr_deduct_amount_sub) = (0)x6;

my ($labor_item_ro_line,$op_code)  = (0)x3;

my ($parts_entries, $misc_entries, $gog_entries, $discount_job_entries, $sublet_entries, $ded_entries) = (0)x5;
my $lbr_type;
my $page_max_height = 47; # Maximum no of lines in page body
my $page_header_height = 1; # No of lines in header section
my $invoice_dealer_header = 1; # No of lines in the dealer header section (1st page)
my $total_header_height;
my $curr_page_height = 0;
my $invoice_note_lines = 22; # No of lines needed for invoice note
my $pages;
my $curr_page_num;
my $invoice_datetime;
my $payment_method;
my $cust_inv_head;

my $job;
my $job_number;

my $ro_doc_truncate = $conn->prepare("TRUNCATE TABLE repair_order_print")
    or die "Prepare failed: " . $conn->errstr;

$ro_doc_truncate->execute()
    or die "Execution failed: " . $ro_doc_truncate->errstr;

my $ro_open_void_qry = $conn->prepare(
    "WITH series_gap (max_ser_ro, min_ser_ro) AS (
         SELECT * FROM (SELECT
                            ro_number::integer,
                            lag(ro_number::integer) OVER ranges,
                            ro_number::integer - lag(ro_number::integer) OVER ranges AS diff
                    FROM repair_order
                    WHERE ro_number ~ '^\\d+\$'
                    WINDOW ranges AS (order by ro_number::integer))t
         WHERE diff <= 100)
     SELECT LPAD(ro_number::text, (SELECT LENGTH(MAX(ro_number)) FROM repair_order WHERE ro_number ~ '^\\d+\$'), '0')
     FROM repair_order
     WHERE completion_date IS NULL
     UNION ALL
     SELECT LPAD(generate_series(min_ser_ro+1, max_ser_ro-1)::text, (SELECT LENGTH(MAX(ro_number)) FROM repair_order WHERE ro_number ~ '^\\d+\$'),'0')
     FROM series_gap");

$ro_open_void_qry->execute();
while (my @open_ro_list = $ro_open_void_qry->fetchrow_array) {
    ($ro_number) = @open_ro_list;
    my $file_open = $ro_number . ".txt";
    unless(open FILE, '>'.$file_open) {
        die "\nUnable to create $file_open\n";
    }
    print_open_ro();
    close FILE;
}


my $ro_open_void_series_qry = $conn->prepare(
    "SELECT * FROM (SELECT
						    lag(ro_number::integer) OVER ranges AS start_ro,
                            ro_number::integer AS end_ro,
                            ro_number::integer - lag(ro_number::integer) OVER ranges AS diff
                    FROM du_dms_autosoft_proxy.repair_order  
                    WHERE ro_number ~ '^\\d+\$'     
                    WINDOW ranges AS (order by ro_number::integer))t
         WHERE diff >= 100");

$ro_open_void_series_qry->execute();
my ($start_ro, $end_ro, $diff);
while (my @open_ro_series_list = $ro_open_void_series_qry->fetchrow_array) {
    ($start_ro, $end_ro, $diff) = @open_ro_series_list;
    my $series_start = $start_ro+1;
    my $series_end = $end_ro-1;
    my $bundle_series_start = int($series_start/100);
    my $bundle_series_end = int($series_end/100);
    my @a = ($bundle_series_start..$bundle_series_end);
    my $i;
    for $i (@a){
        my $suffixStart = "00";
        my $suffixEnd = "99";
        my $tempStart = $i.$suffixStart+0;
        my $tempEnd = $i.$suffixEnd+0; 
        my $start;
        my $end;
        if($tempStart >= $series_start && $tempStart <= $series_end){
          $start = $tempStart;
        } else{
          $start = $series_start; 
        }
        if($tempEnd <= $series_end && $tempEnd >= $series_start){
          $end = $tempEnd; 
        } else{
          $end = $series_end;  
        }
        my $ro_range = $start."-".$end;
        my $file_open = $start . ".txt";
        unless(open FILE, '>'.$file_open) {
            die "\nUnable to create $file_open\n";
        }
        print_open_ro_series($ro_range);
        close FILE;
    }
}




my $ro_qry = $conn->prepare("SELECT ro_number, LPAD(ro_number::text, (SELECT LENGTH(MAX(ro_number)) FROM repair_order WHERE ro_number ~ '^\\d+\$'),'0'), job 
                               FROM repair_order
                              ORDER BY ro_number");

$ro_qry->execute();
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number, $file_name, $job) = @ro_list;
    my $file = $file_name . ".txt";
    if(length($job) >0){
      $job_number = $job;
    }
    else{
        $job_number = $ro_number; 
    }
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    # if($is_separate eq 'false'){
    #     print_header_section();
    # }
    print_ro();
    close FILE;

    if($is_separate eq 'true'){
        my $file_inv_head = "../text-inv-header/".$ro_number . "-inv-header.txt";
        unless(open FILE, '>'.$file_inv_head) {
            die "\nUnable to create $file_inv_head\n";
        }
        print_header_section();
        close FILE;
    }

    load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
    my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
    $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
    $ro_doc_insert->finish;
}

# Subroutine to prepare HEADER section of the invoice
sub make_header {
    my $ro_header_qry = $conn->prepare("SELECT
                                to_char(ro.creation_date, 'mm/dd/yy') AS creation_date,
                                to_char(ro.open_time, 'HH24:MI') AS creation_time,
				to_char(ro.completion_date, 'mm/dd/yy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                ro.advisor,
                                ro.license_plate_number as license_plate_num,
                                ro.job,
                                roc.customer_number,
                                roc.customer_name,
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip,
                                '(' || SUBSTRING(roc.customer_phone, 1, 3) || ') ' || SUBSTRING(roc.customer_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.customer_phone,7,4) AS customer_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                trim(rov.vehicle_vin) AS vehicle_vin,   
                                rov.vehicle_year,
                                CASE WHEN rov.mileage_in > 0 THEN rov.mileage_in ELSE NULL END AS mileage_in,
                                SUBSTRING(rov.vehicle_color, 1, 9) AS vehicle_color,
                                CASE WHEN rov.mileage_out > 0 THEN rov.mileage_out ELSE NULL END AS mileage_out,
                                roc.license_number,
                                '(' || SUBSTRING(roc.work_phone, 1, 3) || ') ' || SUBSTRING(roc.work_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.work_phone,7,4) AS work_phone,
                                ro.tag_no,
                                to_char(ro.delivery_date, 'ddMONyy') AS delivery_date,
                                to_char(ro.promised_time, 'HH24:MI') || ' ' || to_char(ro.promised_date, 'ddMONyy') AS promised,
                                to_char(ro.booked_time, 'HH24:MI') || ' ' || to_char(ro.booked_date, 'ddMONyy') AS ready,
                                ro.payment_code,
                                ro.comments,
				ro.ro_number,
                                ro.opens_time,
                                ro.service_date,
                                ro_ext,
                                ro.next_ro_number
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
    my ($creation_date, $creation_time, $completion_date, $department, $branch, $sub_branch, $advisor,$license_plate_num,$job,$customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $customer_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $vehicle_color, $mileage_out, $license_number,$work_phone,$tag_no, $delivery_date, $promised, $ready, $payment_code, $comments, $ro_number, $opens_time, $service_date, $ro_ext, $next_ro_number ) = @header_row;
    
    if($is_porsche_store eq 'true'){
        $vehicle_vin=substr($vehicle_vin, 0, 12);
        $work_phone = '';
        $customer_name ='';
        $customer_address = '';
        $customer_city = '';
        $customer_zip = '';
        $customer_state = '';
        $customer_email = '';
        $customer_phone = '';
    }
    $payment_method = $payment_code;
    $invoice_datetime = $creation_date;   
    $cust_inv_head = $ro_ext;
    $vehicle_vin =~ s/.\K(?=.)/ /sg;
    

    push (@header_section, sprintf(border(">").'~font{CourierNew9}'."%55s %55s".border("|")."\n", "", ""));
    push (@header_section, sprintf(border(">").'~font{default}'."%55s %54s".border("|")."\n", "Repair Order",""));
    push (@header_section, sprintf(border(">").'~font{default}'."%55s %55s".border("|")."\n", "", ""));
    my $customer_name_trim = substr( $customer_name, 0, 33 );
    push (@header_section, sprintf(border(">").'~font{default}'."%-10s %-35s %-48s".'~font{default}'."%-15s".border("|")."\n",
                                   $ro_number, $vehicle_vin, $customer_name_trim, $creation_date));
    push (@header_section, sprintf(border(">").'~font{default}'."%55s %55s".border("|")."\n", "", ""));    
    push (@header_section, sprintf(border(">").'~font{default}'."%-9s %-15s %-12s %-6s %-48s %-15s".border("|")."\n",
                                   $vehicle_year, $vehicle_make, $vehicle_model, $vehicle_color, $customer_address, $opens_time));
    push (@header_section, sprintf(border(">").'~font{default}'."%55s %55s".border("|")."\n", "", ""));
    push (@header_section, sprintf(border(">").'~font{default}'."%-9s %-15s %8s %10s %-44s %-19s".border("|")."\n",
                                   $mileage_in, $mileage_out, $service_date, $license_plate_num,$customer_city." ".$customer_state." ".$customer_zip, $completion_date));
    push (@header_section, sprintf(border(">").'~font{default}'."%55s %55s".border("|")."\n", "", ""));		   
    
    push (@header_section, sprintf(border(">").'~font{default}'."%-9s %-16s %18s %-18s %-29s %-15s".border("|")."\n",
                                   $next_ro_number, " ", " ", $customer_phone,$work_phone,$advisor));
    push (@header_section, sprintf(border(">").'~font{default}'."%55s %55s".border("|")."\n", "", ""));
    return @header_section;
}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {
    my $job_qry = $conn->prepare("SELECT
                                    rl.ro_line,
                                    rl.complaint,
                                    rj.ro_job_line,
                                    left(rj.billing_code, 1) AS billing_code,
                                    rj.billing_code          AS billing_labor_type,
                                    rj.op_code               AS code,
                                    rj.op_description,
                                    rj.sold_hours,
                                    rj.labor_cost,
                                    rj.sale_amount,
                                    rj.cause,
                                    rj.correction,
                                    rj.labor_type,
                                    rj.job_line_item_number
                                FROM repair_line rl
                                    JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ? ORDER BY  split_part(rj.ro_line, '-', 1)::int");
     
    $job_qry->execute($ro_number);
    my ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $code, $op_description , $sold_hours, $cause, $correction, $labor_type, $job_line_item_number );
    my $k=1;
    while (my @job_row = $job_qry->fetchrow_array) {
        ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $code, $op_description , $sold_hours, $labor_cost, $sale_amount, $cause, $correction, $labor_type, $job_line_item_number ) = @job_row;
        if ($billing_code eq 'I' || $billing_code eq 'i'){
            $lbr_type = "Internal";
        } elsif ($billing_code eq 'C' || $billing_code eq 'c'){
            $lbr_type = "Customer";
        } elsif ($billing_code eq 'W' || $billing_code eq 'w'){
            $lbr_type = "Warranty";
        }
        
        if ($billing_labor_type eq 'I' || $billing_labor_type eq 'i'){
            $billing_labor_type = "IR";
        } elsif ($billing_labor_type eq 'C' || $billing_labor_type eq 'c'){
            $billing_labor_type = "CP";
        } elsif ($billing_labor_type eq 'W' || $billing_labor_type eq 'w'){
            $billing_labor_type = "WC";
        }

        my @job_header_section;
        my $techs_qry = $conn->prepare("SELECT
                            tech_id,
                            tech_name,
                            actual_hours,
                            booked_hours,
                            work_note
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ? ORDER BY  split_part(ro_line, '-', 1)::int");
        $techs_qry->execute($ro_number, $ro_line);
        my ($job_tech,$tech_name,$act_time, $booked_time,$work_note);
        my @job_tech_row = $techs_qry->fetchrow_array;
        ($job_tech,$tech_name,$act_time,$booked_time,$work_note) = @job_tech_row;
         $job_hours = $act_time;

        $Text::Wrap::columns = 46;
        my $wrapped_complaint = fill('', '', expand($complaint));
        my @complaint_list = split "\n", $wrapped_complaint;
	    push (@job_header_section, sprintf(border(">")."%-3s %-47s %-58s".border("|")."\n",
                                           "(".$job_line_item_number.")", $complaint_list[0],""));

        foreach my $i (1 .. $#complaint_list) {
	        if($i == scalar(@complaint_list -1 )){
              push (@job_header_section, sprintf(border(">")."%-3s %-47s %-58s".border("|")."\n",
                                           "", $complaint_list[$i],""));
            }else{
	          push (@job_header_section, sprintf(border(">")."%-3s %-47s %-58s".border("|")."\n",
                                           "", $complaint_list[$i],""));
           }
        }
	
        push (@job_section, [@job_header_section]);

        my @parts_section = make_parts_section($ro_line, $ro_job_line, $work_note, $sold_hours, $labor_cost, $sale_amount, $billing_labor_type, $tech_name, $code, $labor_type);
        push (@job_section, [@parts_section]);

	my @job_total_section = make_job_total_section($ro_line);
	push (@job_section, [@job_total_section]);
    $k++; 
    }
}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line, $correction, $sold_hours, $labor_cost, $sale_amount, $billing_labor_type, $tech_name, $code, $labor_type_name) = @_;
    my @job_correction_section;

    my $labor_item_qry = $conn->prepare("SELECT
                                        ro_line AS labor_item_ro_line,
                                        COALESCE( NULLIF(op_code, ''), 'Labor' )::text,
                                        labor_type,
                                        cc_code,
                                        COALESCE( NULLIF(units, ''), '0' )::numeric,
                                        COALESCE( NULLIF(labor_sale, ''), '0' )::numeric AS service_labor_sale,
                                        COALESCE( NULLIF(labor_cost, ''), '0' )::numeric AS service_labor_cost,
                                        trouble_codes,
                                        case when UPPER(rov.vehicle_make)='FORD' then ford_program_code 
                                             when UPPER(rov.vehicle_make)='CHRYSLER' then chrysler_claim_type
                                             when UPPER(rov.vehicle_make)='MITSUBISHI' then mitsubishi_repair_type
                                             when UPPER(rov.vehicle_make)='MAZDA' then mazda_repair_type
                                             when UPPER(rov.vehicle_make)='VW' then vw_repair_type
                                        end as program_code
					FROM repair_job_labor_item 
  						LEFT JOIN repair_order_vehicle rov USING(ro_number)
                                    WHERE ro_number = ? AND ro_line = ?");
    $labor_item_qry->execute($ro_number,$ro_line);
    my $parts_qry = $conn->prepare("SELECT
                                        ro_part_line,
                                        part_number,
                                        part_description,
                                        quantity_sold,
                                        unit_cost,
                                        unit_sale
				    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND quantity_sold <> 0");
    $parts_qry->execute($ro_number, $ro_line);
    my ( $ro_part_line, $part_number, $part_description, $quantity_sold, $unit_cost, $unit_sale);
    $parts_sale = 0;
    $parts_cost = 0;
    $parts_entries = $parts_qry->rows;
    my $sublet_qry = $conn->prepare("SELECT
                                        other_description,
                                        ro_number as ro_number_sublet,
                                        other_cost,
                                        other_sale,
				    other_sublet_codes,
				    other_sublet_labor_ops
                                    FROM repair_other
                                    WHERE ro_number = ? AND ro_line =? AND item_type = 'SUBLET'");
    $sublet_qry->execute($ro_number, $ro_line);
    my ( $sublet_description, $ro_number_sublet, $sublet_cost, $sublet_sale,$other_sublet_codes,$other_sublet_labor_ops);
    $sublet_job_sale = 0;
    $sublet_job_cost = 0;
    $sublet_entries = $sublet_qry->rows;
    my $misc_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       other_cost,
                                       other_sale
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line =? AND item_type = 'MISC'");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $misc_code, $misc_description, $misc_cost, $misc_sale);
    $misc_job_sale = 0;
    $misc_job_cost = 0;
    $misc_entries = $misc_qry->rows;

    my $gog_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'GOG'");
    $gog_qry->execute($ro_number, $ro_line);
    my ( $gog_code, $gog_description, $gog_cost, $gog_sale);
    $gog_job_sale = 0;
    $gog_job_cost = 0;
    $gog_entries = $gog_qry->rows;

my $discount_job_qry = $conn->prepare("SELECT
                                      disc_code,
                                      disc_description,
                                      disc_applied,
                                      labor_discount,
                                      parts_discount,
                                      total_discount
                                  FROM repair_discount
                                  WHERE ro_number = ?
                                  AND ro_line = ?");
    $discount_job_qry->execute($ro_number, $ro_line);
    my ( $discount_job_code, $discount_job_description, $disc_applied, $labor_discount_job, $parts_discount_job, $total_discount_job);
    $parts_discount_job = 0;
    $labor_discount_job = 0;
    $total_discount_job=0;
    $discount_job_entries = $discount_job_qry->rows;    
    
    $Text::Wrap::columns = 46;
    my $wrapped_correction = fill('', '', expand($correction));
    my @correction_list = split "\n", $wrapped_correction;
   
    my $i=1; 
    my($labor_type, $cc_code, $units, $service_labor_sale, $service_labor_cost, $trouble_codes,$program_code);
    my $k = 0;
    my $entry_loop = 0;
    my $labor_item_entries = $labor_item_qry->rows;

    if($labor_item_entries>0){
        while (my @labor_item_row = $labor_item_qry->fetchrow_array) {
            ( $labor_item_ro_line,$op_code, $labor_type, $cc_code, $units, $service_labor_sale, $service_labor_cost, $trouble_codes,$program_code) = @labor_item_row;
            #print $labor_item_ro_line;
            #print $op_code; 
            if(scalar(@labor_item_row)>0){
                if($k == 0){
                  push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-13s %-15s %7s %9.2f %9.2f %2s".border("|")."\n", " ", $correction_list[$entry_loop], $op_code, $trouble_codes, $units , $service_labor_cost, $service_labor_sale,"" ));
                }
                else{
                  push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-13s %-15s %7s %9.2f %9.2f %2s".border("|")."\n", " ", $correction_list[$entry_loop], $op_code, $trouble_codes, $units , $service_labor_cost, $service_labor_sale,"" ));  
                }
                $entry_loop++;
                $k++;
            }
        }
    }        
    else{
        push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-9s%-9s %10s %7s %9.2f %9.2f %3s".border("|")."\n", " ", $correction_list[0], "Labor", "", "", $sold_hours*10, $labor_cost, $sale_amount,"" )); 
        $entry_loop++;
    }
    if($parts_entries > 0){
        while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_number, $part_description, $quantity_sold, $unit_cost, $unit_sale) = @parts_row;
            $part_description =~ s/''/"/g;
            # my $part_description_trim = substr( $part_description, 0, 5 );
            my $part_description_trim = $part_description;
            if(scalar(@correction_list) >= $entry_loop){
                push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-13s %-15s %7.0f %9.2f %9.2f %3s".border("|")."\n", " ", $correction_list[$entry_loop], $part_number,$part_description_trim, $quantity_sold, $unit_cost, $unit_sale, ""));
                $parts_sale = $parts_sale + $unit_sale;
                $entry_loop++;
            }else{
                push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-13s %-15s %7.0f %9.2f %9.2f %3s".border("|")."\n", " ", " ", $part_number,$part_description_trim, $quantity_sold, $unit_cost, $unit_sale, ""));
                $parts_sale = $parts_sale + $unit_sale;
            }
        }
    }
     if($sublet_entries > 0){
        while (my @sublet_row = $sublet_qry->fetchrow_array) {
            ( $sublet_description, $ro_number_sublet, $sublet_cost, $sublet_sale,$other_sublet_codes,$other_sublet_labor_ops) = @sublet_row;
            my $sublet_description_trim = substr( $sublet_description, 0, 10 );
        if(scalar(@correction_list) >= $entry_loop){
                push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-29s %7s %9.2f %9.2f %3s".border("|")."\n", " ", $correction_list[$entry_loop],  $sublet_description_trim." ".$ro_number_sublet,"",$sublet_cost, $sublet_sale, ""));
                $sublet_job_sale = $sublet_job_sale + $sublet_sale;
            }else{
                push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-29s %7s %9.2f %9.2f %3s".border("|")."\n", " ", " ", $sublet_description_trim." ".$ro_number_sublet,"", $sublet_cost, $sublet_sale, ""));
                $sublet_job_sale = $sublet_job_sale + $sublet_sale;
            }
        }
        $entry_loop++;
     }

    if($misc_entries > 0){
        while (my @misc_row = $misc_qry->fetchrow_array) {
            ( $misc_code, $misc_description, $misc_cost, $misc_sale) = @misc_row;
            $misc_job_sale = $misc_job_sale + $misc_sale;
            #my $misc_description_trim = substr( $misc_description, 0, 5 );
            #if(scalar(@correction_list) >= $entry_loop){
                # push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-13s %20s  %9.2f %9.2f %3s".border("|")."\n", " ", $correction_list[$entry_loop], $misc_code, $misc_description_trim, $misc_cost, $misc_sale, ""));
             #   $misc_job_sale = $misc_job_sale + $misc_sale;
            #}else{
                # push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-13s %20s  %9.2f %9.2f %3s".border("|")."\n", " ", " ", $misc_code, $misc_description_trim, $misc_cost, $misc_sale, ""));
             #   $misc_job_sale = $misc_job_sale + $misc_sale;
            #}
      #  $entry_loop++;    
        }
    }

    if($gog_entries > 0){
        while (my @gog_row = $gog_qry->fetchrow_array) {
        ( $gog_code, $gog_description, $gog_cost, $gog_sale ) = @gog_row;
	    my $gog_description_trim = substr( $gog_description, 0, 5 );
            if(scalar(@correction_list) >= $entry_loop){
            #    push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-13s %20s  %9.2f %9.2f %3s".border("|")."\n", " ", $correction_list[$entry_loop], $gog_code, $gog_description_trim, $gog_cost, $gog_sale, ""));
               $gog_job_sale = $gog_job_sale + $gog_sale;
           }else{
            #    push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-13s %20s  %9.2f %9.2f %3s".border("|")."\n", " ", " ", $gog_code, $gog_description_trim, $gog_cost, $gog_sale, ""));
               $gog_job_sale = $gog_job_sale + $gog_sale;
           }
        $entry_loop++;   
        }
    }

    if($discount_job_entries > 0){
    while (my @discount_job_row = $discount_job_qry->fetchrow_array) {
    ( $discount_job_code, $discount_job_description, $disc_applied, $labor_discount_job, $parts_discount_job, $total_discount_job ) = @discount_job_row;
    my $discount_job_description_trim = substr( $discount_job_description, 0, 5 );
       if($total_discount_job !=  0){
		if(scalar(@correction_list) >= $entry_loop){
		   push (@job_correction_section, sprintf(border(">")."%3s %-47s %-47s %9.2f %3s".border("|")."\n", " ", $correction_list[$entry_loop], "Less Special Discount..........................", negative_format($total_discount_job), ""));
		}
		else{
		   push (@job_correction_section, sprintf(border(">")."%3s %-47s %-47s %9.2f %3s".border("|")."\n", " ", " ", "Less Special Discount..........................", negative_format($total_discount_job), ""));
		}
    		$entry_loop++;   
    	}
    }
    }

    #Code to show remaining correction(work note) as line by line
    if($entry_loop < scalar(@correction_list)){
        my $i = $entry_loop;
        foreach $i ($entry_loop .. $#correction_list) {
            push (@job_correction_section, sprintf(border(">")."%-3s %-47s %-7s %-8s %10s %4s %8s %9s %6s".border("|")."\n", " ", $correction_list[$i], " ", " ", " "," ", " ", " ", ""));
            $i++;
        }
    }
 if( $program_code ne '')
 {
   push (@job_correction_section, sprintf(border(">")."%51s %-47s ".border("|")."\n",
                                                " ","Program Code ".$program_code)); 
 }
    my $billing_labor_type_full;
    if (lc($billing_labor_type) eq 'ir'){
        $billing_labor_type_full = 'Internal';
    } elsif (lc($billing_labor_type) eq 'cp'){
        $billing_labor_type_full = 'Customer';
    } elsif (lc($billing_labor_type) eq 'wc'){
        $billing_labor_type_full = 'Warranty';
    }
    if($parts_entries > 0 || $sublet_entries > 0 || $misc_entries > 0 || $gog_entries > 0){
        if(($parts_sale + $sale_amount + $sublet_job_sale + $gog_job_sale)>0){
            push (@job_correction_section, sprintf(border(">")."%3s %-47s %-14s%-9s%24s %9.2f %3s".border("|")."\n",
                                                " ", "(".$tech_name.")", "Total Repair (",$billing_labor_type_full,").......................", ($parts_sale + $sale_amount + $sublet_job_sale + $gog_job_sale + negative_format($total_discount_job)),"")); 
        } 
        else{
             push (@job_correction_section, sprintf(border(">")."%3s %-47s %-43s %9.2s %3s".border("|")."\n",
                                                " ", "(".$tech_name.")", "", "","")); 
        }
    }else{
        if($sale_amount > 0){
           if(($parts_sale + $sale_amount + $sublet_job_sale + $misc_job_sale + $gog_job_sale)>0){ 
                push (@job_correction_section, sprintf(border(">")."%3s %-47s %-14s%-9s%24s %9.2f %3s".border("|")."\n",
                                            " ", "(".$tech_name.")", "Total Repair (",$billing_labor_type_full,").......................", ($parts_sale + $sale_amount + $sublet_job_sale + $misc_job_sale + $gog_job_sale + negative_format($total_discount_job)),""));
           } 
           else{
               push (@job_correction_section, sprintf(border(">")."%3s %-47s %-43s %9.2s %3s".border("|")."\n",
                                            " ", "(".$tech_name.")", "", "",""));
           }                             
        }
        #push (@job_correction_section, sprintf(border("#")."%110s".border("|")."\n", ""));
        push (@job_correction_section, sprintf(border(">")."%-3s%-107s".border("|")."\n", "", "(".$tech_name.")" )); 
     }
     
     push (@job_correction_section, sprintf(border("#")."%110s".border("|")."\n", ""));
     return @job_correction_section;
}

# Subroutine to prepare the TOTAL section of a JOB
sub make_job_total_section{
    my ($ro_line) = @_;
    my @job_total_section_array;

    if ($lbr_type eq "Internal"){
        $intern_lbr_cost = $intern_lbr_cost+$labor_cost;
        $intern_lbr_sale = $intern_lbr_sale+$sale_amount;
        $intern_prt_cost = $intern_prt_cost+$parts_cost;
        $intern_prt_sale = $intern_prt_sale+$parts_sale;
        $intern_misc_cost = $intern_misc_cost+$misc_job_cost;
        $intern_misc_sale = $intern_misc_sale+$misc_job_sale;
        $intern_gog_cost = $intern_gog_cost+$gog_job_cost;
        $intern_gog_sale = $intern_gog_sale+$gog_job_sale;
        $intern_sublet_cost = $intern_sublet_cost+$sublet_job_cost;
        $intern_sublet_sale = $intern_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    } elsif ($lbr_type eq "Customer"){
        $cust_lbr_cost = $cust_lbr_cost+$labor_cost;
        $cust_lbr_sale = $cust_lbr_sale+$sale_amount;
        $cust_prt_cost = $cust_prt_cost+$parts_cost;
        $cust_prt_sale = $cust_prt_sale+$parts_sale;
        $cust_misc_cost = $cust_misc_cost+$misc_job_cost;
        $cust_misc_sale = $cust_misc_sale+$misc_job_sale;
        $cust_gog_cost = $cust_gog_cost+$gog_job_cost;
        $cust_gog_sale = $cust_gog_sale+$gog_job_sale;
        $cust_sublet_cost = $cust_sublet_cost+$sublet_job_cost;
        $cust_sublet_sale = $cust_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    } elsif ($lbr_type eq "Warranty"){
        $warr_lbr_cost = $warr_lbr_cost+$labor_cost;
        $warr_lbr_sale = $warr_lbr_sale+$sale_amount;
        $warr_prt_cost = $warr_prt_cost+$parts_cost;
        $warr_prt_sale = $warr_prt_sale+$parts_sale;
        $warr_misc_cost = $warr_misc_cost+$misc_job_cost;
        $warr_misc_sale = $warr_misc_sale+$misc_job_sale;
        $warr_gog_cost = $warr_gog_cost+$gog_job_cost;
        $warr_gog_sale = $warr_gog_sale+$gog_job_sale;
        $warr_sublet_cost = $warr_sublet_cost+$sublet_job_cost;
        $warr_sublet_sale = $warr_sublet_sale+$sublet_job_sale;
        $cust_ded_sale = $cust_ded_sale+$ded_job_sale;
    }
    return @job_total_section_array;
}

# Subroutine to make the Total(RO) Discount section
sub make_ro_disc_section {
    my $disc_qry = $conn->prepare("SELECT
                                      disc_code,
                                      disc_description,
                                      disc_applied,
                                      labor_discount,
                                      parts_discount,
                                      total_discount
                                  FROM repair_discount
                                  WHERE ro_number = ?
                                  AND ro_line IS NULL");
    $disc_qry->execute($ro_number);
    my ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount );

    while (my @disc_row = $disc_qry->fetchrow_array) {
        ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount ) = @disc_row;

        if ($labor_discount != 0){
            push (@ro_disc_section, sprintf(border(">")."%40s%14s %8.2f ".border("|")."\n",
                                                "", "LABOR CHARGES DISCOUNTED ", $labor_discount));
        }
        if ($parts_discount != 0){
            push (@ro_disc_section, sprintf(border(">")."%40s%14s %8.2f ".border("|")."\n",
                                                "", "PARTS CHARGES DISCOUNTED ",$parts_discount));                                                                        
        }
        $cust_discount = $cust_discount + $total_discount;
    }
    return @ro_disc_section;
}

#Subroutine to prepare the FOOTER section of the invoice
sub get_footer {
    my @footer_section;
    my ($is_end, $page_number) = @_;

    my $ro_footer_qry = $conn->prepare("SELECT
                                INITCAP(LOWER(ro.payment_code)) AS payment_code
                            FROM repair_order ro
                            WHERE ro.ro_number = ?");

    $ro_footer_qry->execute($ro_number);
    my (@footer_row) = $ro_footer_qry->fetchrow_array();
    my ($payment_method_footer) = @footer_row;


    push (@footer_section, sprintf(border(">").'~font{default}'."%-110s".border("|")."\n", ""));
    push (@footer_section, sprintf(border(">").'~font{default}'."%-110s".border("|")."\n", ""));
    push (@footer_section, sprintf(border(">").'~font{default}'."%-110s".border("|")."\n", ""));
    push (@footer_section, sprintf(border(">").'~font{default}'."%-110s".border("|")."\n", ""));
   
    if($is_end) {
       push (@footer_section, sprintf(border("#")."%63s %8.2f %8.2f %-12s %8.2f %6s".border("|")."\n",
                                " ", $warr_lbr_sale, $intern_lbr_sale , "Labor", $cust_lbr_sale,""));
       push (@footer_section, sprintf(border("#")."%63s %8.2f %8.2f %-12s %8.2f %6s".border("|")."\n",
                                " ", $warr_prt_sale, $intern_prt_sale , "Parts", $cust_prt_sale,""));
       push (@footer_section, sprintf(border("#")."%63s %8.2f %8.2f %-12s %8.2f %6s".border("|")."\n",
                                " ", $warr_sublet_sale, $intern_sublet_sale, "Sublet", $cust_sublet_sale,""));
        if($warr_deduct_amount_add!= 0 ){
          #print $warr_ro_number;
            push (@footer_section, sprintf(border("#")."%63s %8.2f %8.2s %-12s %8.2f %6s".border("|")."\n",
                                " ", $warr_deduct_amount_sub, "", "Warr Deduct", $warr_deduct_amount_add,""));
        }
       push (@footer_section, sprintf(border("#")."%63s %8.2f %8.2f %-10s %8.2f %6s".border("|")."\n",
                                " ", $warr_misc_sale, $intern_misc_sale, "Shop Supplie", $cust_misc_sale,""));
       push (@footer_section, sprintf(border("#")."%63s %8.2f %8.2f %-12s %8.2f %6s".border("|")."\n",
                                " ", $warr_gog_sale, $intern_gog_sale, "Oil/Grease", $cust_gog_sale,""));
       if($cust_discount!= 0){
	  push (@footer_section, sprintf(border("#")."%83s %-10s %8.2f %6s".border("|")."\n",
		      "", "Less Disc.", 1 * negative_format($cust_discount),""));
       }
       my $warr_total = $warr_lbr_sale + $warr_prt_sale + $warr_sublet_sale + $warr_misc_sale + $warr_gog_sale - $warr_deduct_amount_add;

       my $intern_total = $intern_lbr_sale + $intern_prt_sale + $intern_sublet_sale + $intern_misc_sale + $intern_gog_sale;
       my $cust_total = $cust_lbr_sale + $cust_prt_sale + $cust_sublet_sale + $cust_misc_sale + $cust_gog_sale + $warr_deduct_amount_add - $cust_discount;
       my $total = $cust_total + $cust_ro_tax;
       push (@footer_section, sprintf(border("#")."%63s %8.2f %8.2f %-12s %8.2f %6s".border("|")."\n",
                                " ", $warr_total, $intern_total, "Total", $cust_total,""));
       push (@footer_section, sprintf(border(">")."%63s %8.2f %8.2f %-12s %8.2f %6s".border("|")."\n",
                                " ", $warr_ro_tax, $intern_ro_tax, "Tax", $cust_ro_tax,""));
   
       push (@footer_section, sprintf(border(">").'~font{default}'. "%3s Page %d         of     %d".'~font{default}'." %53s %-12s %8.2f %6s".border("|")."\n",
                                "", $curr_page_num, $pages," ", "Total (".$payment_method_footer.")", $total,""));
       push (@footer_section, sprintf(border(">").'~font{default}'."%-110s".border("|")."\n", ""));
       push (@footer_section, sprintf(border(">").'~font{default}' ."%3s %-19s %-12s %-10s %-62s".'~font{default}'.border("|")."\n",
                                   "", $ro_number,"Job ".$job_number,"Archive",""));
       push (@footer_section, sprintf(border(">").'~font{default}' ."%3s %-16s".'~font{default}'.border("|")."\n",
                                   "", "[END OF INVOICE]"));
       push (@footer_section, sprintf(border(">").'~font{default}' ."%3s %-17s".'~font{default}'.border("|")."\n\f",
                                   "", "[END OF DOCUMENT]"));                             

    }else{


       push (@footer_section, sprintf(border(">").'~font{default}'."%-90s".border("|")."\n", ""));
       push (@footer_section, sprintf(border(">").'~font{default}'."%-90s".border("|")."\n", ""));
       push (@footer_section, sprintf(border(">").'~font{default}'."%-90s".border("|")."\n", ""));
       push (@footer_section, sprintf(border(">").'~font{default}'."%-90s".border("|")."\n", ""));
       push (@footer_section, sprintf(border(">").'~font{default}'."%-90s".border("|")."\n", ""));
       push (@footer_section, sprintf(border(">").'~font{default}'."%-90s".border("|")."\n", ""));
       push (@footer_section, sprintf(border(">").'~font{default}'."%-90s".border("|")."\n", ""));
      
       push (@footer_section, sprintf(border(">").'~font{default}'. "%5s Page %d of %d".'~font{default}'.border("|")."\n",
                               "", $curr_page_num, $pages));
       push (@footer_section, sprintf(border(">").'~font{default}'."%-90s".border("|")."\n", ""));
       push (@footer_section, sprintf(border(">").'~font{default}' ."%5s %-15s  %-10s %-10s".'~font{default}'.border("|")."\n\f",
                                   "", $ro_number,"Job  ".$job_number, "Archive"));
    }	   
    return @footer_section;
}

# Subroutine to get the RO TAX amount
sub get_ro_tax_amount {
    $intern_ro_tax=0;
    my $tax_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ? AND item_type = 'TAX'");
    $tax_qry->execute($ro_number);
    my ( $tax_type, $tax_amount);

    while (my @tax_row = $tax_qry->fetchrow_array) {
        ( $tax_type, $tax_amount) = @tax_row;
        if ($tax_type eq "C"){
            $cust_ro_tax = $tax_amount;
        }elsif ($tax_type eq "W"){
            $warr_ro_tax = $tax_amount;
        }elsif ($tax_type eq "I"){
            $intern_ro_tax = $tax_amount;
        }
    }
}


# Subroutine to get the RO TAX amount
sub get_ro_warr_deduct_amount {
    $warr_deduct_amount_add =0;
     $warr_deduct_amount_sub=0;
    my $warr_deduct_qry = $conn->prepare("SELECT
                                      other_code,
				      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ? AND item_type = 'WARRANTY_DEDUCT'");
    $warr_deduct_qry->execute($ro_number);
    my ( $warr_deduct_type, $warr_deduct);
    while (my @warr_deduct_row = $warr_deduct_qry->fetchrow_array) {
        ($warr_deduct_type, $warr_deduct) = @warr_deduct_row;
        $warr_deduct_amount_add = $warr_deduct;
        $warr_deduct_amount_sub = -$warr_deduct;
    }

}

# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_grand_total_section {
    get_ro_tax_amount();
    get_ro_warr_deduct_amount();
}

# Subroutine to calculate the total no of pages in the invoice
# Update page count calculation section, if any modification is made in the body of the report
sub calculate_page_count {
    my $page_count = 1;
    my $page_height = 0;
    my $total_page_content_height = 0;

    for my $job(@job_section){
        $total_page_content_height = $total_page_content_height + scalar(@$job);
    }
    $total_page_content_height = $total_page_content_height + scalar(@grand_total_section) +  scalar(@ro_disc_section);

    if($total_page_content_height <= ($page_max_height - $page_header_height - $invoice_dealer_header)){
        $page_count = 1;
    } else{
        $page_count = ceil(($total_page_content_height - ($page_max_height - $page_header_height - $invoice_dealer_header)) /
                            ($page_max_height - $page_header_height)) + 1;
    }
    return $page_count;
}

# Print Open/Void RO
sub print_open_ro {
    printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO# ".$ro_number." * See Open and Void RO Report *");
}

# Print Open/Void RO
sub print_open_ro_series {
    # $num_args = scalar(@_);
    printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO Range# ".$_[0]." * See Open and Void RO Report *");
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")."%110s".border("|")."\n", "");
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
#  sub print_header_section {
#     undef @header_section;
#     make_header();  
#     print FILE @header_section;
#  }
  sub print_header_section {
    my ($page_num) = @_;
    my %hash = (pg_num => $page_num );
    foreach (@header_section)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
 }

sub print_dealer_header_section {
    print_blank_line($invoice_dealer_header);
 }

# Subroutine for pagination
sub paginate_and_print_segment {
    my (@section_array) = @_;

    if($curr_page_num == 1){
        $total_header_height = $page_header_height + $invoice_dealer_header;
    } else{
        $total_header_height = $page_header_height;
    }

    # Print section that fit in the current page
    if(scalar(@section_array) <= (($page_max_height - $total_header_height) - $curr_page_height)){
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{
        for my $sub_section(@section_array){
            if($curr_page_height < ($page_max_height - $total_header_height))
            {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            } else{
                print FILE get_footer(0, $curr_page_num);
                $curr_page_num = $curr_page_num + 1;
                print_header_section ($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
            }
        }
    }
}

# Subroutine to prepare end of invoice section
sub make_end_of_invoice_section {
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %4s%-31s%s%32s ".border("|")."\n",
           "",
           "*"x26,
           "A L L  D E T A I L  I N V O I C E",
           "*"x27));
}

# Subroutine to prepare invoice note section
sub make_invoice_note_section {
    for (my $i = 0; $i < $invoice_note_lines; $i++) {
        push (@invoice_note_section, sprintf(border("#")."%110s".border("|")."\n", ""));
    }
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
    ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

    ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

    ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

    ($sale_amount, $parts_cost, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

    $job_hours = 0;

    ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;
     
    $cust_discount = 0;
    #$curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @job_section;
    undef @ro_disc_section;
    undef @grand_total_section;
    undef @end_of_invoice_section;
    undef @invoice_note_section;
    ########## Prepare invoice ##########
    make_header();
    make_job_section();
    make_ro_disc_section();
    make_grand_total_section();
    $pages = calculate_page_count();

    ########## Print invoice ##########
    print_dealer_header_section();
    $curr_page_height = 0;
    print_header_section ($curr_page_num);

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }

    # Pagination of RO Discount section
    paginate_and_print_segment(@ro_disc_section);

    # Pagination of Grand total section
    paginate_and_print_segment(@grand_total_section);

    if($curr_page_num == 1){
        $total_header_height = $page_header_height + $invoice_dealer_header;
    } else{
        $total_header_height = $page_header_height;
    }

    # End of invoice footer
    if($curr_page_height == ($page_max_height - $total_header_height)){
        print FILE get_footer(1, $curr_page_num);
    } else {
        #print_blank_line(($page_max_height - $total_header_height) - $curr_page_height);
        print FILE get_footer(1, $curr_page_num);
    }
}

sub print_ro_inv_bg_head {
    printf(FILE $cust_inv_head);
}

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}

sub negative_format {

    use Number::Format qw(:subs);
    my $precision = 2;
    my $symbol    = '';
    my ($number)  = @_;
    my $formatted = format_price( $number, $precision, $symbol );
    $formatted =~ s/,//g;
    return '-' . $formatted;
}
