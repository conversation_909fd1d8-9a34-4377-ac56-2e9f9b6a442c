#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect Per<PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Time::Format qw(%time %strftime %manip);

no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];
my $is_porsche_store =  $ARGV[1];
my $dealer_address =  $ARGV[2];
my $current_range = $ARGV[3];
# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;
my $file_name;

my @header_section;
my @comment_section;
my @job_section;
my @ro_fee_section;
my @ro_paint_section;
my @ro_disc_section;
my @ro_tech_punch_section;
my @ro_account_section;

my ($tot_cust_lbr_cost, $tot_cust_lbr_sale, $tot_cust_prt_cost, $tot_cust_prt_sale, $tot_cust_misc_cost, $tot_cust_misc_sale, $tot_cust_gog_cost, $tot_cust_gog_sale,
    $tot_cust_sublet_cost, $tot_cust_sublet_sale, $tot_cust_ded_cost, $tot_cust_ded_sale, $tot_cust_fee_sale )= (0)x13;

my ($tot_service_lbr_cost, $tot_service_lbr_sale, $tot_service_prt_cost, $tot_service_prt_sale, $tot_service_misc_cost, $tot_service_misc_sale, $tot_service_gog_cost, $tot_service_gog_sale,
    $tot_service_sublet_cost, $tot_service_sublet_sale, $tot_service_ded_cost, $tot_service_ded_sale, $tot_service_fee_sale )= (0)x13;

my ($tot_intern_lbr_cost, $tot_intern_lbr_sale, $tot_intern_prt_cost, $tot_intern_prt_sale, $tot_intern_misc_cost, $tot_intern_misc_sale, $tot_intern_gog_cost,
    $tot_intern_gog_sale, $tot_intern_sublet_cost, $tot_intern_sublet_sale, $tot_intern_ded_cost, $tot_intern_ded_sale, $tot_intern_fee_sale )= (0)x13;

my ($tot_warr_lbr_cost, $tot_warr_lbr_sale, $tot_warr_prt_cost, $tot_warr_prt_sale, $tot_warr_misc_cost, $tot_warr_misc_sale, $tot_warr_gog_cost, $tot_warr_gog_sale,
    $tot_warr_sublet_cost, $tot_warr_sublet_sale, $tot_warr_ded_cost, $tot_warr_ded_sale, $tot_warr_fee_sale )= (0)x13;

my ($job_cust_lbr_cost, $job_cust_lbr_sale, $job_cust_prt_cost, $job_cust_prt_sale, $job_cust_misc_cost, $job_cust_misc_sale, $job_cust_gog_cost, $job_cust_gog_sale,
    $job_cust_sublet_cost, $job_cust_sublet_sale, $job_cust_ded_cost, $job_cust_ded_sale, $job_cust_fee_sale  )= (0)x13;

my ($job_service_lbr_cost, $job_service_lbr_sale, $job_service_prt_cost, $job_service_prt_sale, $job_service_misc_cost, $job_service_misc_sale, $job_service_gog_cost, $job_service_gog_sale,
    $job_service_sublet_cost, $job_service_sublet_sale, $job_service_ded_cost, $job_service_ded_sale ,$qlabor_cost , $work_waranty_sublet_amt, $work_internal_sublet_amt, $work_service_sublet_amt, $work_customer_sublet_amt, $work_cost_sublet_amt , $work_job_billing_code, $job_service_fee_sale )= (0)x20;

my ($job_intern_lbr_cost, $job_intern_lbr_sale, $job_intern_prt_cost, $job_intern_prt_sale, $job_intern_misc_cost, $job_intern_misc_sale, $job_intern_gog_cost,
    $job_intern_gog_sale, $job_intern_sublet_cost, $job_intern_sublet_sale, $job_intern_ded_cost, $job_intern_ded_sale, $job_intern_fee_sale )= (0)x13;

my ($job_warr_lbr_cost, $job_warr_lbr_sale, $job_warr_prt_cost, $job_warr_prt_sale, $job_warr_misc_cost, $job_warr_misc_sale, $job_warr_gog_cost, $job_warr_gog_sale,
    $job_warr_sublet_cost, $job_warr_sublet_sale, $job_warr_ded_cost, $job_warr_ded_sale,  $job_warr_fee_sale  )= (0)x13;

my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax, $service_ro_tax, $policy_adjustment, $line_type, $policy_prefix) = (0)x7;
my $cust_discount = 0;
my $job_billing_code;
my ($parts_entries, $misc_entries, $gog_entries, $sublet_entries, $ded_entries, $tech_punch_entries, $account_entries) = (0)x7;
my ($CustomerPaintSale, $ServicePaintSale, $WarrantyPaintSale, $InternalPaintSale , $CustomerPaintCost, $ServicePaintCost, $WarrantyPaintCost, $InternalPaintCost)= (0)x8;

my $page_max_height = 39; # Maximum no of lines in page body
my $curr_page_height = 0;
my $invoice_note_lines = 22; # No of lines needed for invoice note
my $pages;
my $curr_page_num;

my $ar_number_label;

my $final_coupon_discount=0;

my $ro_doc_truncate = $conn->prepare("TRUNCATE TABLE repair_order_print")
    or die "Prepare failed: " . $conn->errstr;

$ro_doc_truncate->execute()
    or die "Execution failed: " . $ro_doc_truncate->errstr;

my $ro_open_void_qry = $conn->prepare(
    "WITH series_gap (max_ser_ro, min_ser_ro) AS (
         SELECT * FROM (SELECT
                            ro_number::integer,
                            lag(ro_number::integer) OVER ranges,
                            ro_number::integer - lag(ro_number::integer) OVER ranges AS diff
                    FROM repair_order
                    WHERE ro_number ~ '^\\d+\$'
                    WINDOW ranges AS (order by ro_number::integer))t
         WHERE diff <= 100)
     SELECT LPAD(ro_number::text, (SELECT LENGTH(MAX(ro_number::numeric)::text) FROM repair_order WHERE ro_number ~ '^\\d+\$'), '0')
     FROM repair_order
     WHERE completion_date IS NULL
     UNION ALL
     SELECT LPAD(generate_series(min_ser_ro+1, max_ser_ro-1)::text, (SELECT LENGTH(MAX(ro_number::numeric)::text) FROM repair_order WHERE ro_number ~ '^\\d+\$'),'0')
     FROM series_gap");

$ro_open_void_qry->execute();
while (my @open_ro_list = $ro_open_void_qry->fetchrow_array) {
    ($ro_number) = @open_ro_list;
    my $file_open = $ro_number . ".txt";
    unless(open FILE, '>'.$file_open) {
        die "\nUnable to create $file_open\n";
    }
    print_open_ro();
    close FILE;
}


my $ro_open_void_series_qry = $conn->prepare(
    "SELECT * FROM (SELECT
						    lag(ro_number::integer) OVER ranges AS start_ro,
                            ro_number::integer AS end_ro,
                            ro_number::integer - lag(ro_number::integer) OVER ranges AS diff
                    FROM repair_order  
                    WHERE ro_number ~ '^\\d+\$'     
                    WINDOW ranges AS (order by ro_number::integer))t
         WHERE diff >= 100");

$ro_open_void_series_qry->execute();
my ($start_ro, $end_ro, $diff);
while (my @open_ro_series_list = $ro_open_void_series_qry->fetchrow_array) {
    ($start_ro, $end_ro, $diff) = @open_ro_series_list;
    my $series_start = $start_ro+1;
    my $series_end = $end_ro-1;
    my $bundle_series_start = int($series_start/100);
    my $bundle_series_end = int($series_end/100);
    my @a = ($bundle_series_start..$bundle_series_end);
    # my $i;
    # for $i (@a){
    #     my $suffixStart = "00";
    #     my $suffixEnd = "99";
    #     my $tempStart = $i.$suffixStart+0;
    #     my $tempEnd = $i.$suffixEnd+0; 
    #     my $start;
    #     my $end;
    #     if($tempStart >= $series_start && $tempStart <= $series_end){
    #       $start = $tempStart;
    #     } else{
    #       $start = $series_start; 
    #     }
    #     if($tempEnd <= $series_end && $tempEnd >= $series_start){
    #       $end = $tempEnd; 
    #     } else{
    #       $end = $series_end;  
    #     }
    #     my $ro_range = $start."-".$end;
    #     my $file_open = $start . ".txt";
    #     unless(open FILE, '>'.$file_open) {
    #         die "\nUnable to create $file_open\n";
    #     }
    #     print_open_ro_series($ro_range);
    #     close FILE;
    # }
    my $str_len = length($series_end);
    $series_start = sprintf("%0${str_len}d", $series_start);
    my $ro_range = $series_start."-".$series_end;
    my $file_open = $series_start . ".txt";
    unless(open FILE, '>'.$file_open) {
        die "\nUnable to create $file_open\n";
     }
    print_open_ro_series($ro_range);
    close FILE;
    
}

my $ro_qry = $conn->prepare("SELECT ro_number, 
                                LPAD(ro_number::text, (SELECT LENGTH(MAX(ro_number::numeric)::text) FROM repair_order WHERE ro_number ~ '^\\d+\$'),'0') 
                               FROM repair_order
                              ORDER BY ro_number LIMIT 200 OFFSET ?");

$ro_qry->execute($current_range);
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number, $file_name) = @ro_list;
    my $file = $file_name . ".txt";
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;
    #load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
    my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
    $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
    $ro_doc_insert->finish;
}

# Subroutine to prepare HEADER section of the invoice
sub make_header {
    my $ro_header_qry = $conn->prepare("SELECT
                                to_char(ro.creation_date, 'mm/dd/yy') AS creation_date,
                                to_char(ro.completion_date, 'mm/dd/yy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                ro.advisor,
                                ro.advisor_emp_number,
                                ro.document_number,
                                ro.service_writer_id,
                                 CASE
                                    WHEN length(roc.other_phone) > 9 THEN SUBSTRING(roc.other_phone, 1, 3) || '-' || SUBSTRING(roc.other_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.other_phone,7,4)
                                    ELSE ' '
                                END AS other_phone,
                                 CASE
                                    WHEN length(roc.other_phone) > 9 THEN SUBSTRING(roc.other_phone, 1, 3) || '-' || SUBSTRING(roc.other_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.other_phone,7,4)
                                    ELSE ''
                                END AS work_phone,
                                trim(roc.customer_name),
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip ||'          '|| roc.zip_code as customer_zip,  
                                CASE
                                    WHEN length(roc.work_phone) > 9 THEN SUBSTRING(roc.work_phone, 1, 3) || '-' || SUBSTRING(roc.work_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.work_phone,7,4)
                                    ELSE ''
                                END AS customer_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                rov.vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in,
                                SUBSTRING(rov.vehicle_color, 1, 10) AS vehicle_color,
                                rov.mileage_out,
                                roc.license_number,
                                roc.ar_number,
                                ro.tag_no,
                                to_char(ro.delivery_date, 'mm/dd/yy') AS delivery_date,
                                to_char(ro.in_service_date, 'mm/dd/yy') AS in_service_date,
                                to_char(ro.promised_time, 'HH24:MI') || ' ' || to_char(ro.promised_date, 'ddMONyy') AS promised,
                                to_char(ro.booked_time, 'HH24:MI') || ' ' || to_char(ro.booked_date, 'ddMONyy') AS ready,
                                ro.payment_code,
                                ro.comments,
                                rov.vehicle_body,
                                ro.coupon_number
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
    my ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor,$advisor_emp_number,$document_number, $service_writer_id ,$cell_phone, $work_phone, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $customer_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $vehicle_color, $mileage_out, $license_number, $ar_number, $tag_no, $delivery_date, $in_service_date, $promised, $ready, $payment_code, $comments, $vehicle_body, $coupon_number ) = @header_row;

    my $ro_header_discount_qry = $conn->prepare("SELECT
                                 description ,
                                 discount_basis,
                                 discount_amount
                            FROM repair_coupon_and_discount
                            WHERE record_key = ?");
    $ro_header_discount_qry->execute($coupon_number);
    my (@header_discount_row) = $ro_header_discount_qry->fetchrow_array();
    my ($coupon_description, $discount_basis, $discount_amount) = @header_discount_row;

    if($is_porsche_store eq 'true'){
        $vehicle_vin=substr($vehicle_vin, 0, 12);
        $cell_phone = '';
        $customer_name ='';
        $customer_address = '';
        $customer_city = '';
        $customer_zip = '';
        $customer_state = '';
        $customer_email = '';
        $customer_phone = '';

    } else{

        if (index($customer_name, 'INVENTORY') != -1) {
            $customer_name = '';
            if($dealer_address) {
                $dealer_address=~ s/,\s*$//;
                $dealer_address=~ s/\|/,/;
                my $index;
                my @spl = split('~', $dealer_address);
                foreach my $i (@spl) 
                {
                    $index++;
                    if($index == 1) {
                        $customer_name.= uc($i)."\n";
                    } else {
                        $customer_name.= "$i\n";
                    }
                }

            }
            
        }
        
    }
    push (@header_section, sprintf(border(">")."%46s %-23s%-11s %-13s ".border("|")."\n",
           "", $cell_phone, $creation_date, $ro_number . '/' . "<--pg_num-->"));
    # push (@header_section, sprintf(border("#")."%78s  ".border("|")."\n", ""));
            $Text::Wrap::columns = 15;
            # my $wrapped_vehicle_body = fill('', '', expand($vehicle_body));
            # $vehicle_body = substr($vehicle_body, 0,10);
            # my @vehicle_body_list = split "\n", $wrapped_vehicle_body;
            $Text::Wrap::columns = 22;

            my $temp;
            my $wrapped_customer_address;
            if($is_porsche_store eq 'true'){
                $temp=''; 
                $wrapped_customer_address = $temp;
            } else {
                if( length($customer_name) > 1){
                    if($customer_address eq 0 || $customer_address eq 'NULL' || $customer_address eq 'null'){
                        $customer_address = '';
                }
                    if($customer_city eq 0 || $customer_city eq 'NULL' || $customer_city eq 'null' || $customer_city eq ''){
                        $customer_city = '';
                }
                    else{
                        $customer_city = $customer_city.',';
                    }
                    if($customer_state eq 0 || $customer_state eq 'NULL' || $customer_state eq 'null'){
                        $customer_state = '';
                }
                    if($customer_zip eq '0          0' || $customer_zip eq 'NULL' || $customer_zip eq 'null'){
                        $customer_zip = '';
                }
                #   $temp= join '', $customer_name, "\n".$customer_address."\n".$customer_city,',' , $customer_state.' ', $customer_zip;
                $temp= join '', $customer_name."\n".$customer_address."\n".$customer_city.$customer_state.' '.$customer_zip;
                #   $wrapped_customer_address = fill('', '', expand($temp));
                $wrapped_customer_address = expand($temp);
                }
                elsif($customer_name eq 'null' || $customer_name eq 'NULL'){
                        $wrapped_customer_address = '';
                }
                else{
                #$temp='*VOIDED REPAIR ORDER*';
                $temp='';  
                $wrapped_customer_address = $temp; 
                }
            }
            
            

            my @address_list = split "\n", $wrapped_customer_address;
            if($address_list[0] eq 'null'){
				$address_list[0] = '';
            }
            push (@header_section, sprintf(border(">")." %-45s".border("|")."\n", $address_list[0]));
            my $entry_loop = 0;


            foreach my $i (1 .. $#address_list) {
                $entry_loop++;
                if($i == 1){
                    push (@header_section, sprintf(border(">")." %-45s %-16s %14s %11s ".border("|")."\n", $address_list[1], $customer_phone, $completion_date, 'Original'));
                }elsif($i == 2){
                    push (@header_section, sprintf(border(">")." %-45s".border("|")."\n", $address_list[2]."   ".$address_list[3]));
                }elsif($i == 3){
                    push (@header_section, sprintf(border(">")." %-45s %-16s %12s %13s ".border("|")."\n", "", substr($vehicle_body, 0,12), $mileage_in, $mileage_out));
                }
            }
        
            if($entry_loop == 0){
                push (@header_section, sprintf(border(">")."% -45s %-16s %14s %11s ".border("|")."\n", "","", $completion_date, 'Original'));
                push (@header_section, sprintf(border(">")."%-45s  ".border("|")."\n", ""));
                push (@header_section, sprintf(border(">")."% -45s %-15s %14s %13s ".border("|")."\n", "", substr($vehicle_body, 0,13), $mileage_in, $mileage_out));
            }elsif($entry_loop == 1){
                push (@header_section, sprintf(border(">")."%-45s  ".border("|")."\n", ""));
                push (@header_section, sprintf(border(">")."% -45s %-15s %14s %13s ".border("|")."\n", "", substr($vehicle_body, 0,13), $mileage_in, $mileage_out));
            }elsif($entry_loop == 2){
                push (@header_section, sprintf(border(">")." %-45s %-15s %14s %13s ".border("|")."\n", "", substr($vehicle_body, 0,13), $mileage_in, $mileage_out));
            }

    push (@header_section, sprintf(border(">")."%-95s".border("|")."\n", ""));  
    push (@header_section, sprintf(border(">")."   %-8s  %-16s  %-18s  %-15s  %-22s%4s".border("|")."\n",
           $vehicle_year, $vehicle_make, substr($vehicle_model, 0,14), $license_number, $advisor, substr($advisor_emp_number, -4)));

    push (@header_section, sprintf(border(">")."%-95s".border("|")."\n", ""));

    push (@header_section, sprintf(border(">")."%20s %20s%20s%15s%14s".border("|")."\n",
           $vehicle_vin, $vehicle_color, "", $delivery_date , $in_service_date,));

    push (@header_section, sprintf(border(">"). " %-95s".border("|")."\n",$document_number));
    push (@header_section, sprintf(border(">")."%-95s".border("|")."\n",""));
    # push (@header_section, sprintf(border(">")."%-95s".border("|")."\n", ""));
    # push (@header_section, sprintf(border(">")."%-95s".border("|")."\n", ""));
    #push (@header_section, sprintf(border(">")."%-95s".border("|")."\n", ""));

    if($ar_number ne ''){
      $ar_number_label = 'AR#: '.$ar_number;  
    }
    $final_coupon_discount=0;
}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {
    my $customer_detail_qry = $conn->prepare("SELECT
                                 /*CASE
                                    WHEN length(roc.work_phone) > 9 THEN SUBSTRING(roc.work_phone, 1, 3) || '-' || SUBSTRING(roc.work_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.work_phone,7,4)
                                    ELSE ''
                                END AS other_phone,*/
                                CASE
                                   WHEN length(roc.customer_phone) > 9 THEN SUBSTRING(roc.customer_phone, 1, 3) || '-' || SUBSTRING(roc.customer_phone, 4,3) ||
                                    '-' || SUBSTRING(roc.customer_phone,7,4)
                                    ELSE ''
                                END AS cell_phone,
                                roc.customer_email
                            FROM repair_order_customer roc
                            WHERE roc.ro_number = ?");
    $customer_detail_qry->execute($ro_number);
    my ( $cell_phone , $cust_email) ;
    my @cust_details_section;
    while (my @cust_detail_row = $customer_detail_qry->fetchrow_array) {
        ($cell_phone, $cust_email) = @cust_detail_row;
        if($cell_phone && !$cust_email) {
            if($ar_number_label ne ''){
                 push (@cust_details_section, sprintf(border(">")."%6s %-8s %-80s".border("|")."\n",
                    " Cell:", $cell_phone, $ar_number_label));
            }
            else{
              push (@cust_details_section, sprintf(border(">")."%6s %-88s".border("|")."\n",
                    " Cell:", $cell_phone));
            }
           
        }
        if(!$cell_phone && $cust_email) {
             if($ar_number_label ne ''){
                push (@cust_details_section, sprintf(border(">")."%6s  %-96s".border("|")."\n",
                    " Email:", $cust_email.' '. $ar_number_label));
            }
            else{
            push (@cust_details_section, sprintf(border(">")."%6s %-88s".border("|")."\n",
                    " Email:", $cust_email));
            }        
        }
        if($cust_email && $cell_phone) {
             if($ar_number_label ne ''){
               push (@cust_details_section, sprintf(border(">")."%6s %-12s %7s %-67s".border("|")."\n",
                " Cell:", $cell_phone, "Email:", $cust_email.' '. $ar_number_label));
            }
            else{
            push (@cust_details_section, sprintf(border(">")."%6s %-12s %7s %-67s".border("|")."\n",
                " Cell:", $cell_phone, "Email:", $cust_email));
            }    
        }

        if(!$cust_email && !$cell_phone && $ar_number_label ne '') {
            push (@cust_details_section, sprintf(border(">")."%-94s".border("|")."\n",
                , $ar_number_label));
        } 

     } 
    push (@cust_details_section, sprintf(border(">")."%-95s".border("|")."\n", ""));
   
    push (@job_section, [@cust_details_section]);

    my $job_qry = $conn->prepare("SELECT DISTINCT ON (rl.ro_line::numeric) ro_line::numeric,
                                    rl.complaint,
                                    rl.add_on_flag,
                                    rj.cause,
                                    rj.correction,
                                    trim(rj.op_description),
                                    rj.billing_code,
                                    rj.servicetype,
                                    COUNT(*) OVER (Partition BY rj.ro_number) AS job_count
                                FROM repair_line rl
                                    LEFT JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ? AND rj.ro_line IS NOT NULL AND rj.linetype = 'A'
                                ORDER BY rl.ro_line::numeric");                            

    $job_qry->execute($ro_number);
    my $job_length = 0;
    my ( $ro_line, $complaint, $add_on_flag, $cause, $correction, $op_description, $billing_code,$servicetype, $job_count);
    while (my @job_row = $job_qry->fetchrow_array) {
        ( $ro_line, $complaint, $add_on_flag, $cause, $correction, $op_description, $billing_code,$servicetype, $job_count) = @job_row;
        my @job_header_section;
        my $opcode;
        my $op_code;
        my $opcode_qry = $conn->prepare("SELECT  op_code FROM repair_job
                              WHERE  ro_number = ? AND ro_line = ? AND op_code!='' AND op_code!='*' ORDER BY linetype ASC LIMIT 1");
        $opcode_qry->execute($ro_number,$ro_line); 
        while (my @opcode_row = $opcode_qry->fetchrow_array)
        {
           ($op_code) = @opcode_row;
           $opcode= $op_code.":"; 
        }
        if(!$op_code || $op_code eq "LABOR"){
           $opcode= "Customer Reports:"; 
        }


        if($servicetype eq ''){
        
          my $temp_servicetype;
          my $service_type_qry = $conn->prepare("SELECT  servicetype FROM repair_job
                              WHERE  ro_number = ? AND ro_line = ? ORDER BY servicetype DESC  LIMIT 1");

            $service_type_qry->execute($ro_number,$ro_line); 
            while (my @servicetype_row = $service_type_qry->fetchrow_array)
            {
                ($temp_servicetype) = @servicetype_row;
                $servicetype = $temp_servicetype;
            }                  
        }

        my $linetype;
        my $ro_job_line;
        my $find_ro_line;
        my $find_ro_job_line;
        my $labor_sale_amount;
        my $coupon_amount;
        my $linetype_qry = $conn->prepare("SELECT  linetype,ro_job_line, sale_amount FROM repair_job
                                WHERE  ro_number = ? AND ro_line = ?");
        $linetype_qry->execute($ro_number,$ro_line);
        while (my @linetype_row = $linetype_qry->fetchrow_array)
        {
           ($linetype,$ro_job_line, $labor_sale_amount) = @linetype_row;
           if($linetype eq 'Q'){
             $find_ro_line = $ro_line;
             $find_ro_job_line = $ro_job_line;
             $coupon_amount = $labor_sale_amount
           }

        }  
        
        my $labor_opcode_find_query = $conn->prepare("SELECT DISTINCT
                                                      FROM repair_line rl
                                                      LEFT JOIN repair_job rj USING (ro_number, ro_line)
                                                      WHERE rl.ro_number = ?
                                                      ORDER BY rl.ro_line");

        # Job Complaint
        my ($op_description_temp, $op_description_spl_1, $op_description_spl_desc_1, $op_description_spl_desc_2, $op_description_spl_2, $complaints);

        if (!$op_description) {
            $op_description = $complaint;
            $op_description =~ s/~/ /g;
            $complaint = '';
            $complaint= join '' , $servicetype."  ".$opcode.'  ',$op_description.' ';
            my $complaint_len = 56;
            my @op_description_spl = grep{s/^\s+//;$_ ne ''}split(/(.{1,$complaint_len})(?=\s)/, $complaint);
            $complaints = join( '~', @op_description_spl);
        }
        else{
             $op_description =~ s/(.{55})/$1~/sg;
            if (!$complaint) {
                $op_description_temp = join '' , $servicetype."  ".$opcode.'  ', $op_description;
            }
            else{
                $op_description_temp = join '' , $servicetype."  ".$opcode.'  ', $op_description.'~', $complaint;
            }

            if ($op_description_temp =~ /(^.*)(\~.*)/) {
                $op_description_spl_1 = $1;
                $op_description_spl_2 = $2;
                $complaints = join( '', $op_description_spl_1, $op_description_spl_2);
            } elsif ($op_description_temp =~ /(^.{55})(.*)/) {
                $op_description_spl_desc_1 = $1;
                $op_description_spl_desc_2 = $2;
                $complaints = join( '', $op_description_spl_desc_1.'~', $op_description_spl_desc_2);
            } else {
                $complaints= join '' ,$op_description_temp.'~', $complaint; 
            }
        }
        my @complaint_list = split "~", $complaints;
        if($linetype ne 'M'){
            push (@job_header_section, sprintf(border(">")."%6s%-74s%2s"."\n","#".$ro_line." - ",$complaint_list[0], $billing_code));
            foreach my $i (1 .. $#complaint_list) {
                push (@job_header_section, sprintf(border(">")."%6s%-67s%9s"."\n", " ", $complaint_list[$i], ""));
            }
        }
        
        push (@job_section, [@job_header_section]);

        # Job Cause
        if ($cause){
            my @job_cause_section;
            # $Text::Wrap::columns = 60;
            # my $wrapped_cause = fill('', '', expand($cause));
            my @cause_list = split "~", $cause;
            push (@job_cause_section, sprintf(border(">")."%10s%-61s%10s"."\n","","Caused by ", ""));
            push (@job_cause_section, sprintf(border(">")."%10s%-61s%10s"."\n", "", $cause_list[0], ""));

            foreach my $i (1 .. $#cause_list) {
                push (@job_cause_section, sprintf(border(">")."%10s%-61s%10s"."\n", "", $cause_list[$i], ""));
            }
            push (@job_section, [@job_cause_section]);
        }

        ($job_cust_lbr_cost, $job_cust_lbr_sale, $job_cust_prt_cost, $job_cust_prt_sale, $job_cust_misc_cost, $job_cust_misc_sale, $job_cust_gog_cost, $job_cust_gog_sale,
         $job_cust_sublet_cost, $job_cust_sublet_sale, $job_cust_ded_cost, $job_cust_ded_sale, $job_cust_fee_sale )= (0)x13;

        ($job_intern_lbr_cost, $job_intern_lbr_sale, $job_intern_prt_cost, $job_intern_prt_sale, $job_intern_misc_cost, $job_intern_misc_sale, $job_intern_gog_cost,
         $job_intern_gog_sale, $job_intern_sublet_cost, $job_intern_sublet_sale, $job_intern_ded_cost, $job_intern_ded_sale, $job_intern_fee_sale )= (0)x13;

        ($job_warr_lbr_cost, $job_warr_lbr_sale, $job_warr_prt_cost, $job_warr_prt_sale, $job_warr_misc_cost, $job_warr_misc_sale, $job_warr_gog_cost, $job_warr_gog_sale,
         $job_warr_sublet_cost, $job_warr_sublet_sale, $job_warr_ded_cost, $job_warr_ded_sale, $job_warr_fee_sale )= (0)x13;
         
        ($job_service_lbr_cost, $job_service_lbr_sale, $job_service_prt_cost, $job_service_prt_sale, $job_service_misc_cost, $job_service_misc_sale, $job_service_gog_cost, $job_service_gog_sale,
         $job_service_sublet_cost, $job_service_sublet_sale, $job_service_ded_cost, $job_service_ded_sale, $job_service_fee_sale)= (0)x13;


        my ($corr_labor_op_code, $corrlabor_opcode_description, $faliure_code,$job_op_code); 
        my $lbr_qry = $conn->prepare("	SELECT DISTINCT ON ( rj.linetype,rj.ro_job_line)
                                        rj.ro_job_line,
                                        left(rj.billing_code, 1) AS billing_code,
                                        rj.billing_code          AS billing_labor_type,
                                        rj.op_code,
                                        rj.op_description,
                                        rj.sold_hours,
                                        rj.actual_hours,
                                        rj.labor_cost,
                                        rj.sale_amount,
                                        rj.transaction_code,
                                        rj.sublet_invoice_number,
                                        rj.sublet_vendor_number,
                                        COUNT(*) OVER (Partition BY rj.ro_number, rj.ro_line,
                                                 rj.ro_job_line) AS lbr_count
                                FROM du_dms_dealertrack_proxy.repair_job rj
                                WHERE  rj.ro_number = ? AND rj.ro_line = ?
                               ORDER BY rj.linetype ASC, rj.ro_job_line  ASC");

        $lbr_qry->execute($ro_number, $ro_line);
        #$ro_job_line,, $op_code
        my ( $billing_labor_type, $op_description , $sold_hours,
             $actual_hours, $labor_cost, $sale_amount, $transaction_code, $sublet_invoice_number, $sublet_vendor_number, $lbr_count );

        my $flag=0;
        my $work_sublet_vendor;
        my $work_transaction_code;
        my $work_transaction_sublet_invoice_number;
        my $work_sublet_invoice_number;
        my $work_fixed_transaction_code = 'IV';
        my $work;
        my $work_sale_amount=0;
        my $work_labor_cost=0;
        my @work_lbr_section;
        my @work_haz_section;
        my $print_once='true';
        
        while (my @work_row = $lbr_qry->fetchrow_array) {
            ( $ro_job_line, $job_billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours,
             $actual_hours, $labor_cost, $sale_amount, $transaction_code, $sublet_invoice_number, $sublet_vendor_number, $lbr_count ) = @work_row;
            if($sublet_invoice_number ne ''){
              $flag=1;  
              if($sublet_vendor_number ne ''){
                $work_sublet_vendor = $sublet_vendor_number;
                $work_sublet_invoice_number = $sublet_invoice_number;
                $work_sale_amount = $sale_amount;
                $work_labor_cost =  $labor_cost;
                $work_job_billing_code = $job_billing_code;
              }
              else{
                 $work_transaction_code = $transaction_code; 
                 $work_transaction_sublet_invoice_number= $sublet_invoice_number;
              }

            }
        } 
        $work= "Work performed by ".$work_sublet_vendor.":".$work_transaction_code."#".$work_transaction_sublet_invoice_number.":". $work_fixed_transaction_code."#".$work_sublet_invoice_number;
        

        $lbr_qry->execute($ro_number, $ro_line);
        while (my @lbr_row = $lbr_qry->fetchrow_array) {
            ( $ro_job_line, $job_billing_code, $billing_labor_type, $op_code, $op_description , $sold_hours,
             $actual_hours, $labor_cost, $sale_amount, $lbr_count ) = @lbr_row;

            my $lbr_billing;
            my @job_labor_section;
            my $lbr_detail_qry = $conn->prepare("WITH 
                                                feedet AS (
                                                    SELECT ro_number, ro_line  FROM du_dms_dealertrack_proxy.repair_other 
                                                    WHERE NULLIF(item_type,'') <> '' GROUP BY ro_number, ro_line
                                                )
                                                SELECT
                                                    left(rj.billing_code, 1) AS billing_code,
                                                    rj.billing_code          AS billing_labor_type,
                                                    CASE WHEN rj.actual_hours != 0 THEN rj.sold_hours END AS sold_hours,
                                                    rj.actual_hours,
                                                    CASE WHEN (rj.actual_hours != 0 OR rj.transaction_code='PM' 
                                                               OR rj.transaction_code='MS'  OR LOWER(left(rj.billing_code, 1)) = 'i') THEN rj.labor_cost END AS labor_cost,
                                                    rj.sale_amount AS sale_amount,
                                                    rj.corr_labor_op_code,
                                                    rj.corrlabor_opcode_description,
                                                    case when rj.faliure_code!= '' then '('||rj.faliure_code||')' else rj.faliure_code end faliure_code,
                                                    rj.transaction_code,
                                                    rj.sublet_invoice_number,
                                                    rj.sublet_vendor_number,
                                                    rjt.tech_id,
                                                    rjt.tech_emp_number,
                                                    rjt.coupon_number,
                                                    rjt.coupon_description,
                                                    case when rj.op_code!= '' then '('||rj.op_code||')' else rj.op_code end,
                                                    linetype
                                                FROM repair_job rj
                                                    LEFT JOIN repair_job_technician_detail rjt
                                                        ON rjt.ro_number = rj.ro_number
                                                            AND rjt.ro_line = rj.ro_line
                                                            AND rjt.ro_job_line = rj.ro_job_line
                                                            --AND rjt.tech_billing_type = rj.billing_code
                                                    LEFT JOIN feedet rfe  ON rfe.ro_number = rj.ro_number  AND rfe.ro_line = rj.ro_line
                                                WHERE (rj.lineseqnumber !='0'  OR rfe.ro_line::numeric <> 0 OR (rj.linetype = 'Q' OR rj.linetype = 'M'  OR rj.linetype = 'S') )  AND rjt.policy_adjustment !='X' AND rjt.tech_id IS NOT NULL AND rj.ro_number = ? AND rj.ro_line = ? AND rj.ro_job_line = ?
                                                ORDER BY rj.ro_job_line DESC, rj.corr_labor_op_code");
            $lbr_detail_qry->execute($ro_number, $ro_line, $ro_job_line);

               while ( my @lbr_detail_row = $lbr_detail_qry->fetchrow_array){
                    my $job_tech_id;
                    my $tech_emp_number;
                    my $job_coupon_number;
                    my $coupon_description;
                    # my @job_sp_section;
                    
                    ( $job_billing_code, $billing_labor_type, $sold_hours,
                    $actual_hours, $labor_cost, $sale_amount, $corr_labor_op_code, $corrlabor_opcode_description, $faliure_code , $transaction_code, $sublet_invoice_number, $sublet_vendor_number,$job_tech_id , $tech_emp_number, $job_coupon_number, $coupon_description,$job_op_code,$line_type) = @lbr_detail_row;
                    my $result = 0;
                      my $policy_adj_qry = $conn->prepare("SELECT
                                                      rj.linetype,
                                                      rj.policy_adjustment
                                                FROM repair_job rj                                                
                                                WHERE rj.ro_number = ? AND rj.ro_line = ? AND rj.linetype ='A' AND coalesce(rj.policy_adjustment,'0') != '0' and rj.policy_adjustment!=''
                                                ORDER BY rj.ro_job_line DESC");

                     $policy_adj_qry->execute($ro_number, $ro_line);
                     #$line_type = 0;
                     $policy_adjustment=0;
                    while ( my @policy_adj_row = $policy_adj_qry->fetchrow_array){
			            ( $line_type,$policy_adjustment) = @policy_adj_row;
                    }
                    if($actual_hours != 0){
                        $result = ($sale_amount / $actual_hours)
                    }
                    if (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                       $policy_prefix="P".$policy_adjustment;
                    }
                    else{
			$policy_prefix ='';
                    }
                    # if($labor_cost > -1 || $sale_amount>0){
                    if(($labor_cost && $transaction_code ne 'PM' && $transaction_code ne 'MS') || $line_type eq 'Q'){                      
                        if($sublet_invoice_number eq ''){                  
                            #This is the labour item that lineType have 'Q' 
                            if($find_ro_line && $find_ro_job_line){            
                               if($coupon_amount < 0) {
                                 if($ro_line==$find_ro_line && $ro_job_line==$find_ro_job_line) {             
                                    $qlabor_cost+=0;
                                    $labor_cost=0;
                                    push (@job_labor_section, sprintf(border(">")."%5s %-45s %6s%7s %13s %7s %8s".border("|")."\n",
                                                        "",$coupon_description," ", "" , $sale_amount, "", "")); 
                                    if($sale_amount < 0){
                                          $final_coupon_discount = $final_coupon_discount + $sale_amount;
                                    }
                                    $sale_amount=0;                     
                                } else{
                                    push (@job_labor_section, sprintf(border(">")."%5s %-41s %12s%7.2f %11s %2s %4s%8.2f".border("|")."\n",
                                                        "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4), $actual_hours." hrs @", $result, $sale_amount, "", "", $labor_cost));
                                } 
                               } else {
                                 if($ro_line==$find_ro_line && $ro_job_line==$find_ro_job_line) {             
                                    $qlabor_cost+=$labor_cost;
                                    $labor_cost=0;
                                    push (@job_labor_section, sprintf(border(">")."%5s %-45s %6s%7s %13s %7s %8s".border("|")."\n",
                                                        "",$coupon_description," ", "" , $sale_amount, "", "")); 
                                    if($sale_amount < 0){
                                          $final_coupon_discount = $final_coupon_discount + $sale_amount;
                                    }
                                    $sale_amount=0;                     
                                } else{
                                    push (@job_labor_section, sprintf(border(">")."%5s %-41s %12s%7.2f %11s %2s %4s%8.2f".border("|")."\n",
                                                        "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4), $actual_hours." hrs @", $result, "Included", "", "", $labor_cost));
                                } 
                               }  
                            }
                            else{
                                if($labor_cost == 0 && $sale_amount>0){
                                     push (@job_labor_section, sprintf(border(">")."%5s %-41s %12s %7s %11s %2s %4s%8.2f".border("|")."\n",
                                                    "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4),$actual_hours." hrs","", $sale_amount, $policy_prefix, "", $labor_cost));
                                }
                                else{
                                    if($actual_hours == 0){
                                        push (@job_labor_section, sprintf(border(">")."%5s %-41s %12s %7s %11s %4s %8s".border("|")."\n",
                                            "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4),$actual_hours." hrs @", "" , "", "", plain_amount_format($labor_cost)));
                                    }
                                    else{
                                        if($sale_amount != 0 && $labor_cost !=0){
                                            push (@job_labor_section, sprintf(border(">")."%5s %-41s %12s%7.2f %11s %2s %4s%8.2f".border("|")."\n",
                                                "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4),$actual_hours." hrs @", $result , $sale_amount,  $policy_prefix, "", $labor_cost));
                                        } else{
                                            if($sale_amount == 0 && $labor_cost > 0 && $actual_hours > 0){
                                            	push (@job_labor_section, sprintf(border(">")."%5s %-41s %12s%7s %11s %2s %4s%8.2f".border("|")."\n",
                                                "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4),$actual_hours." hrs @", "" , "",  $policy_prefix, "", $labor_cost));
                                             } 
					 }
				     }
                                }
                            }

                            if (lc($job_billing_code) eq 'i'){
                                $job_intern_lbr_sale = $job_intern_lbr_sale + $sale_amount;
                                $job_intern_lbr_cost = $job_intern_lbr_cost + $labor_cost;
                            } elsif (lc($linetype) eq 'a' && $policy_adjustment ne '0'){
                                $job_intern_lbr_sale = $job_intern_lbr_sale + $sale_amount;
                                $job_intern_lbr_cost = $job_intern_lbr_cost + $labor_cost;
                            } elsif (lc($job_billing_code) eq 'c'){
                                $job_cust_lbr_sale = $job_cust_lbr_sale + $sale_amount;
                                $job_cust_lbr_cost = $job_cust_lbr_cost + $labor_cost;
                            } elsif (lc($job_billing_code) eq 'w'){
                                $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                                $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                            } elsif (lc($job_billing_code) eq 's'){
                                $job_service_lbr_sale = $job_service_lbr_sale + $sale_amount;
                                $job_service_lbr_cost = $job_service_lbr_cost + $labor_cost;
                            }
                            
                        }
                    }
                    else{
                          if($corr_labor_op_code ne '' && $transaction_code ne 'MS'){
                                # my $corrected_by = "Corrected by"."  ".$corr_labor_op_code.": ".$faliure_code." ".$corrlabor_opcode_description;
                                my $corrected_by = "Corrected by"."  ".$corr_labor_op_code.": ".$job_op_code.$faliure_code." ".$corrlabor_opcode_description;
                                $Text::Wrap::columns = 58;
                                my $wrapped_corrected_by_description = fill('', '', expand($corrected_by));
                                my @correctedby_description_list = split "\n", $wrapped_corrected_by_description;
                                push (@job_labor_section, sprintf(border(">")."%5s %-58s %13s %7s %8s".border("|")."\n",
                                                        "", $correctedby_description_list[0],"", "", ""));    

                                foreach my $i (1 .. $#correctedby_description_list) {
                                        push (@job_labor_section, sprintf(border(">")."%5s %-58s %13s %7s %8s".border("|")."\n",
                                                        "", $correctedby_description_list[$i], "", "", ""));                           
                                }
                                    # if($actual_hours == 0){
                                    #     push (@job_labor_section, sprintf(border(">")."%5s %-42s %11s%7s %11s %2s %4s %8s".border("|")."\n",
                                    #         "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4),$actual_hours." hrs @","", "", "", "", ""));
                                    # }       
                            }

                        if($sale_amount != 0 && $actual_hours == 0){
                            if($transaction_code eq 'PM') {
                                push (@job_labor_section, sprintf(border(">")."%5s %-42s %11s%7s %11.2f %2s %4s %8.2f".border("|")."\n",
                                "", "Paint & Materials ","", "" , $sale_amount, "", "", $labor_cost));
                        
                            } elsif($transaction_code eq 'MS') {
                                push (@job_labor_section, sprintf(border(">")."%5s %-42s %11s%7s %11.2f %2s %4s %8.2f".border("|")."\n",
                                "", "Miscellaneous Charges ","", "" , $sale_amount, "", "", $labor_cost));
                            } else {
                                if (lc($job_billing_code) ne 'i'){
                                    $labor_cost = 0;
                                }
                                #$sale_amount =0;
                                if (lc($line_type) eq "l" || lc($line_type) eq "a"){

                                    push (@job_labor_section, sprintf(border(">")."%5s %-41s %12s%7s %11.2f %2s %4s%8s".border("|")."\n",
                                          "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4),$actual_hours." hrs @", "" , $sale_amount, '', "", plain_amount_format($labor_cost)));
                                } 
                                else {
                                    # push (@job_labor_section, sprintf(border(">")."%5s %-42s %11s%7s %11s %2s %4s %8s".border("|")."\n",
                                    #     "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4),$actual_hours." hrs @", "" ,'', '', "", $line_type));
                                    $sale_amount =0;

                                }
                              
                            }
                           
                       if (lc($job_billing_code) eq 'i'){
                                $job_intern_lbr_sale = $job_intern_lbr_sale + $sale_amount;
                                $job_intern_lbr_cost = $job_intern_lbr_cost + $labor_cost;
                            } elsif (lc($linetype) eq 'a' && $policy_adjustment ne '0'){
                                $job_intern_lbr_sale = $job_intern_lbr_sale + $sale_amount;
                                $job_intern_lbr_cost = $job_intern_lbr_cost + $labor_cost;
                            } elsif (lc($job_billing_code) eq 'c'){
                                $job_cust_lbr_sale = $job_cust_lbr_sale + $sale_amount;
                                $job_cust_lbr_cost = $job_cust_lbr_cost + $labor_cost;
                            } elsif (lc($job_billing_code) eq 'w'){
                                $job_warr_lbr_sale = $job_warr_lbr_sale + $sale_amount;
                                $job_warr_lbr_cost = $job_warr_lbr_cost + $labor_cost;
                            } elsif (lc($job_billing_code) eq 's'){
                                $job_service_lbr_sale = $job_service_lbr_sale + $sale_amount;
                                $job_service_lbr_cost = $job_service_lbr_cost + $labor_cost;
                            }
                        
                        }

                        # if($sale_amount == 0 && $actual_hours == 0 && lc($line_type) eq "l" && (lc($job_billing_code) eq 'c' || lc($job_billing_code) eq 'i')){
                        #       push (@job_labor_section, sprintf(border(">")."%5s %-42s %11s%7s %11s %2s %4s %8s".border("|")."\n",
                        #                 "", "Work by Tech ".$job_tech_id."/".substr($tech_emp_number, -4),$actual_hours." hrs @", "" ,'', '', "", ""));
                        
                        # }
                 
                    
                    }
                    if ($line_type ne 'Q'){
                        if ($print_once eq 'true'){
                            $print_once='false';
                            my $fee_detail_qry = $conn->prepare("SELECT
                                            other_billing_type,
                                            other_description,
                                            other_cost
                                        FROM repair_other
                                        WHERE ro_number = ? AND ro_line = ? AND   item_type='FEE' ");
                            $fee_detail_qry->execute($ro_number,$ro_line);
    
                            my $fee_other_type;
                            my $fee_other_description;
                            my $fee_other_cost;
                            
                            while (my @fee_detail_row = $fee_detail_qry->fetchrow_array) {
                                    ($fee_other_type, $fee_other_description, $fee_other_cost) = @fee_detail_row;
                                    #push (@job_labor_section, sprintf(border(">")."%5s %-61s %11.2f %4s %9.2f".border("|")."\n",
                                    #                       "", $fee_other_description,$fee_other_cost, "", $labor_cost));
                                    push (@job_labor_section, sprintf(border(">")."%5s %-61s %11.2f %4s %9s".border("|")."\n",
                                                            "", $fee_other_description,$fee_other_cost, "", ""));
                                    # push (@job_labor_section, sprintf(border(">")."%5s %-58s %10.2f %13s %8s".border("|")."\n",
                                    #                      "", $fee_other_description, $fee_other_cost, "" ,""));
                                
                                    if (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                                        $job_intern_fee_sale = $job_intern_fee_sale + $fee_other_cost;                              
                                    }elsif (lc($fee_other_type) eq "c"){
                                        $job_cust_fee_sale = $job_cust_fee_sale + $fee_other_cost;
                                    }elsif (lc($fee_other_type) eq "w"){
                                        $job_warr_fee_sale = $job_warr_fee_sale + $fee_other_cost;
                                    }elsif (lc($fee_other_type) eq "i"){
                                        $job_intern_fee_sale = $job_intern_fee_sale + $fee_other_cost;                              
                                    }elsif (lc($fee_other_type) eq "s"){
                                        $job_service_fee_sale = $job_service_fee_sale + $fee_other_cost;
                                    }                     
                            }
                        }
                    }                    
                    #my $haz_detail_qry = $conn->prepare("SELECT
                    #                   other_billing_type,
                    #                   other_description,
                    #                   other_cost
                    #               FROM repair_other
                    #               WHERE ro_number = ? AND ro_line = ?  AND   item_type='HAZMAT' ");
                    #$haz_detail_qry->execute($ro_number,$ro_line);

                    # my $haz_other_type;
                    # my $haz_other_description;
                    # my $haz_other_cost;
                    #while (my @haz_detail_row = $haz_detail_qry->fetchrow_array) {
                    #        ($haz_other_type, $haz_other_description, $haz_other_cost) = @haz_detail_row;
                    #        push (@job_labor_section, sprintf(border(">")."%5s %-61s %11.2f %4s %9s".border("|")."\n",
                    #                                "", $haz_other_description,$haz_other_cost, "", ""));                     
                    #} 

                    # my $ro_job_discount_qry = $conn->prepare("SELECT
                    #         description,
                    #         discount_basis,
                    #         discount_amount
                    #         FROM repair_coupon_and_discount
                    #         WHERE record_key = ?");
                    # $ro_job_discount_qry->execute($job_coupon_number);
                    # while ( my @job_discount_row = $ro_job_discount_qry->fetchrow_array){
                    #     my ($job_coupon_description, $job_discount_basis, $job_discount_amount) = @job_discount_row;
                    #         push (@job_labor_section, sprintf(border(">")."%5s %-42s %11s%7.2f %11s %4s %9s".border("|")."\n",
                    #                                         "", $job_coupon_description, "" ,  $job_discount_amount, "","", ""));
                    # }   
                }
               
             push (@job_section, [@job_labor_section]);
        }
       
        if($flag==1){
            if (lc($work_job_billing_code) eq 'i'){
                $work_internal_sublet_amt = $work_sale_amount;
                # $job_intern_lbr_sale = $job_intern_lbr_sale + $work_sale_amount;
                # $job_intern_lbr_cost = $job_intern_lbr_cost + $work_labor_cost;
            } elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
           	$work_internal_sublet_amt = $work_sale_amount;
            } elsif (lc($work_job_billing_code) eq 'c'){
                $work_customer_sublet_amt = $work_sale_amount;
                # $job_cust_lbr_sale = $job_cust_lbr_sale + $work_sale_amount;
                # $job_cust_lbr_cost = $job_cust_lbr_cost + $work_labor_cost;
            } elsif (lc($work_job_billing_code) eq 'w'){
                $work_waranty_sublet_amt = $work_sale_amount;
                # $job_warr_lbr_sale = $job_warr_lbr_sale + $work_sale_amount;
                # $job_warr_lbr_cost = $job_warr_lbr_cost + $work_labor_cost;
            } elsif (lc($work_job_billing_code) eq 's'){
                $work_service_sublet_amt = $work_sale_amount;
                # $job_service_lbr_sale = $job_service_lbr_sale + $work_sale_amount;
                # $job_service_lbr_cost = $job_service_lbr_cost + $work_labor_cost;
            }
            $work_cost_sublet_amt =  $work_labor_cost;
            push (@work_lbr_section, sprintf(border(">")."%5s %-64s %8.2f %5s %8.2f".border("|")."\n",
                                            "", $work, $work_sale_amount, "", $work_labor_cost));
        }
        
        push (@job_section, [@work_lbr_section]);

         my $haz_detail_qry = $conn->prepare("SELECT
                                      other_billing_type,
                                      other_description,
                                       other_cost
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line = ?  AND   item_type='HAZMAT' ");
                    $haz_detail_qry->execute($ro_number,$ro_line);

                     my $haz_other_type;
                     my $haz_other_description;
                     my $haz_other_cost;
                    while (my @haz_detail_row = $haz_detail_qry->fetchrow_array) {
                            ($haz_other_type, $haz_other_description, $haz_other_cost) = @haz_detail_row;
                            push (@work_haz_section, sprintf(border(">")."%5s %-61s %11.2f %4s %9s".border("|")."\n",
                                                    "", $haz_other_description,$haz_other_cost, "", ""));                     
                    } 
          push (@job_section, [@work_haz_section]);

        my @parts_section = make_parts_section($ro_line,1,$find_ro_line,$find_ro_job_line, $coupon_amount);
        push (@job_section, [@parts_section]);

        my @misc_section = make_misc_section($ro_line);
        push (@job_section, [@misc_section]);

        my @gog_section = make_gog_section($ro_line);
        push (@job_section, [@gog_section]);

        my @sublet_section = make_sublet_section($ro_line, $billing_labor_type);
        push (@job_section, [@sublet_section]);

        my @job_total_section = make_job_total_section($ro_line);
        push (@job_section, [@job_total_section]);

        my @ded_section = make_deductible_section($ro_line);
        push (@job_section, [@ded_section]);

        my $techs_qry = "SELECT
                            string_agg(DISTINCT tech_id, ', ')
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ?";

        my ($job_techs) = $conn->selectrow_array($techs_qry, undef, ($ro_number, $ro_line));

        # Job Correction
        if ($correction){
            my @job_correction_section;
            # $Text::Wrap::columns = 51;
            $correction =~ s/,,/,/g;
        #     my @spl_correction = split(',,', $correction);
        #     foreach my $i (@spl_correction) 
	    # {
            	# my $wrapped_correction = fill('', '', expand($i));
            # my $wrapped_correction = fill('', '', expand($correction));
	        my @correction_list = split "~", $correction;
        	push (@job_correction_section, sprintf(border(">")."%6s%-74s%-4s%10s"."\n", "", $correction_list[0], $policy_prefix,""));
	        foreach my $i (1 .. $#correction_list) {
        	    push (@job_correction_section, sprintf(border(">")."%6s%-78s%10s"."\n", "", $correction_list[$i], ""));
	         }
       	         
            #  }
             push (@job_section, [@job_correction_section]);
        }
        my @job_end_section;

        $job_length++;
        if($job_length != $job_count) {
            push (@job_end_section, sprintf(border("#")."%s".border("|")."\n", "-"x96));
        }
        push (@job_section, [@job_end_section]);
    }

        my $discount_detail_qry = $conn->prepare("SELECT
                                       other_cost,
                                       other_billing_type,
                                       COALESCE(description::text,'Discount') 
                                   FROM repair_other
                                       LEFT JOIN repair_coupon_and_discount ON other_code = record_key
                                   WHERE ro_number = ? AND  item_type='CDISC' ");
        $discount_detail_qry->execute($ro_number);
        my ($coupon_amount, $coupon_type, $coupon_description);
        my  $discount_value;
        my $percentage_type= 'P';
        while (my @discount_detail_row = $discount_detail_qry->fetchrow_array) {
        ( $coupon_amount, $coupon_type, $coupon_description ) = @discount_detail_row;
         $discount_value=  $coupon_amount ;   
        }
       # if (index($coupon_type, $percentage_type) != -1) {
            # if( substr($discount_value, 1)>0){
                my @job_end_section;
                my @discount_section;
                my $coupon_amt=0;
                if( $final_coupon_discount > 0)
                {
                	$coupon_amt= $final_coupon_discount - $discount_value;
		        }
                if( $final_coupon_discount < 0)
                {
                	$coupon_amt= $discount_value - $final_coupon_discount;
		        }
                if( $final_coupon_discount == 0)
                {
                	$coupon_amt= $discount_value;
		        }
                if( $coupon_amt != 0)
                {
                    
                    if($job_length == $job_count) {
                        push (@discount_section, sprintf(border("#")."%s".border("|")."\n", "-"x96));
                    }                    
                    push (@discount_section, sprintf(border(">")."%-56s %22s %1s      %5s ".border("|")."\n",
                                                    $coupon_description, plain_amount_format($coupon_amt), "",""));
                    if($job_length != $job_count) {
                        push (@discount_section, sprintf(border("#")."%s".border("|")."\n", "-"x96));
                    }
                    # push (@discount_section, sprintf(border("#")."%2s%86s ".border("|")."\n", "", "-"x92));
                    push (@job_section, [@discount_section]);
                }
                # push (@job_end_section, sprintf(border("#")."%s ".border("|")."\n", "-"x96));
                # push (@job_section, [@job_end_section]);
            #}
       # }
    return @discount_section;
}
# Subroutine to prepare the TECH section of a JOB
sub make_tech_section{
    my ($ro_line, $ro_job_line) = @_;
    my @tech_section_array;
    my $tech_details_qry = $conn->prepare("SELECT
                                                ro_job_tech_line,
                                                tech_id,
                                                work_date,
                                                work_start_time,
                                                work_end_time,
                                                work_note,
                                                actual_hours,
                                                booked_hours
                                            FROM repair_job_technician_detail td
                                            WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
    $tech_details_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours);
    if($tech_details_qry->rows > 0){
        while (my @tech_row = $tech_details_qry->fetchrow_array) {
            ($ro_job_tech_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_note , $actual_hours, $booked_hours) = @tech_row;
            my $tech_detail = $tech_id. " "." LIC#:"; #need to add technician name and LIC#
            push (@tech_section_array, sprintf(border(">")."%11s%-57s%10s".border("|")."\n",
                                                "", $tech_detail, ""));
        }
    }
    return @tech_section_array;
}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line, $find_ro_line, $find_ro_job_line, $coupon_amount) = @_;
    my @parts_section_array;

    ($job_service_prt_cost, $job_service_prt_sale,$job_cust_prt_cost, $job_cust_prt_sale,$job_warr_prt_cost,$job_warr_prt_sale,$job_intern_prt_cost, $job_intern_prt_sale)= (0)x8;

    my $parts_qry = $conn->prepare("SELECT DISTINCT ON (ro_part_line)
                                        ro_part_line,
                                        CASE
                                            WHEN part_number ~ '[0-9]+'  THEN 
                                            SUBSTRING(part_number, 1, 2)  || '' || SUBSTRING(part_number, 3,2) || ''
                                            || SUBSTRING(part_number,5,1) || '' || SUBSTRING(part_number,6,3) || ''
                                            || SUBSTRING(part_number,9,3) || SUBSTRING(part_number,12,30)
                                        ELSE
                                            part_number
                                        END                                       AS part_number,
                                        part_description,
                                        left(nullif(prt_billing_type, 'NA'), 1)   AS prt_billing_code,
                                        nullif(prt_billing_type, 'NA')            AS prt_billing_type,
                                        CASE WHEN (group_price!='K') THEN quantity_sold END AS quantity_sold,
                                        unit_cost AS unit_cost,
                                        CASE WHEN group_price!='Y' THEN unit_sale END AS unit_sale,
                                        CASE WHEN group_price!='Y' THEN unit_core_cost END AS unit_core_cost,
                                        CASE WHEN group_price!='Y' THEN unit_core_sale END AS unit_core_sale,
                                        special_order_emergency_purchase,
                                        trans_code,
                                        failed_part,
                                        COUNT(*) OVER (Partition BY rp.ro_number, rp.ro_line,
                                                 rp.ro_job_line, rp.ro_part_line) AS prt_count,
                                        group_price,
                                        part_kit_description
                                    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ? AND (quantity_sold != 0 OR trans_code = 'SH')
                                    ORDER BY ro_part_line");
    $parts_qry->execute($ro_number, $ro_line, $ro_job_line);
    my ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
         $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale,$special_order_emergency_purchase,$trans_code, $failed_part,$prt_count,$group_price,$part_kit_description);
    
    $parts_entries = $parts_qry->rows;
    if($parts_entries > 0){
        while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
              $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale,$special_order_emergency_purchase,$trans_code, $failed_part,$prt_count,$group_price,$part_kit_description) = @parts_row;
            $prt_billing_code = ($prt_billing_code?$prt_billing_code:$job_billing_code);
            $part_description = $part_description;
            $Text::Wrap::columns = 32;
            
            my $wrapped_part_description = fill('', '', expand($part_description));
            my @part_description_list = split "\n", $wrapped_part_description;
            my $result = 0;
            my $part_number_with_label='';
            my $kit_part='false';
            if ($failed_part ne '' && $failed_part eq 'X'){
               $part_number_with_label = $part_number.'(FP)'; 
            } 
            else{
                $part_number_with_label = $part_number;
            }

            if ($part_description eq '' && $group_price eq 'K'){
               $kit_part = 'true';
               $part_number_with_label = 'Kit: '.$part_number; 
               $part_description_list[0] = $part_kit_description;
            }
            else{
                $kit_part = 'false';
            }
            if($trans_code eq 'SH') {
                $part_number_with_label = 'Freight: '.$part_number; 
            }

            if($quantity_sold != 0){
                $result = $unit_sale / $quantity_sold ;
                $result = $unit_sale ;
                if($trans_code ne 'OF'){
                  $unit_sale=$unit_sale*$quantity_sold;
                }
                else{
                    $quantity_sold = 0;
                    $result = 0;
                    $unit_cost = 0; 
                }
                $unit_cost=$unit_cost*$quantity_sold;
            }
            if($prt_count == 1){
                #This is the  line number  which   contain one of the  labor item line type 'Q'
                if($find_ro_line && $find_ro_job_line){

                    my $qty_print = '';
                    my $rate_print = '';
                    if($quantity_sold) {
                        $qty_print = $quantity_sold. '@';
                        $rate_print = $result;
                        # $unit_cost=$unit_cost*$quantity_sold;
                    }

                    if($group_price eq 'Y'){
                   		push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7s %11s %5s %9.2f".border("|")."\n",
                                            "", $part_number_with_label . ' : ' .$part_description_list[0],  "", "" , "Included", "", $unit_cost)); 
                	} else {
				if($coupon_amount < 0) {
                        		push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7.2f %11s %5s %9.2f".border("|")."\n",
                        	                    "", $part_number_with_label . ' : ' .$part_description_list[0],  $qty_print, $rate_print , $unit_sale, "", $unit_cost)); 

                    		} else {
                        		push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7.2f %11s %5s %9.2f".border("|")."\n",
                                            "", $part_number_with_label . ' : ' .$part_description_list[0],  $qty_print, $rate_print , "Included", "", $unit_cost)); 
                    		}
                	}
                   
                }
                elsif($group_price eq 'Y'){
                   push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7s %11s %5s %9.2f".border("|")."\n",
                                            "", $part_number_with_label . ' : ' .$part_description_list[0],  "", "" , "Included", "", $unit_cost)); 
                }

                else{
                    #checking  Orderd parts 
                    if($special_order_emergency_purchase ne '' && $special_order_emergency_purchase eq 'S'){
                    push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7.2f %11s %4s %9.2f".border("|")."\n",
                                    "",  $part_number_with_label . ' : ' .$part_description_list[0],  $quantity_sold."@", $result ,"Ordered", "", $unit_cost));
                    }
                    else{
                        my $qty_print_1 = '';
                        my $rate_print_1 = '';
                        if($quantity_sold) {
                            $qty_print_1 = $quantity_sold. '@';
                            $rate_print_1 = $result;
                        }
                        my $parts_full_label = $part_number_with_label;
                        if($part_description_list[0]) {
                            $parts_full_label = $part_number_with_label . ' : ' .$part_description_list[0]
                        }
                        push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7s %11.2f%3s %2s %9.2f".border("|")."\n",
                                    "",  $parts_full_label,  $qty_print_1, $rate_print_1 , $unit_sale, $policy_prefix, "", $unit_cost));
                    }
                    # push (@parts_section_array, sprintf(border(">")."%5s  %-35s %20s%6.2f %6.2f %5s %8.2f".border("|")."\n",
                    #                         "", $part_description_list[0],  $quantity_sold." @ ", $result , $unit_sale, "", $unit_cost));
                }        
                    if($find_ro_line && $find_ro_job_line && $kit_part eq 'false'){
                        if (lc($prt_billing_code) eq 'i'){
                            $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                        }elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                            $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                        } elsif (lc($prt_billing_code) eq 'c'){
                            $job_cust_prt_cost = $job_cust_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                        } elsif (lc($prt_billing_code) eq 'w'){
                            $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                        } elsif (lc($prt_billing_code) eq 's'){
                            $job_service_prt_cost = $job_service_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                        }
                        
                        if($coupon_amount < 0) {
                            if (lc($prt_billing_code) eq 'i'){
                                $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                            } elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                                $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                            } elsif (lc($prt_billing_code) eq 'c'){
                                $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                            } elsif (lc($prt_billing_code) eq 'w'){
                                $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                            } elsif (lc($prt_billing_code) eq 's'){
                                $job_service_prt_sale = $job_service_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                            }

                            }
                    } else {
                        #checking  Orderd parts 
                        if($special_order_emergency_purchase eq '' || $special_order_emergency_purchase ne 'S'){
                            if (lc($prt_billing_code) eq 'i'){
                                if ($kit_part eq 'false'){
                                    $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                }
                                else{
                                    $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                }
                            } elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                                if ($kit_part eq 'false'){
                                    $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                }
                                else{
                                    $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                }
                            } elsif (lc($prt_billing_code) eq 'c'){
                                if ($kit_part eq 'false'){
                                    $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    $job_cust_prt_cost = $job_cust_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                }
                                else{
                                    $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                }
                            } elsif (lc($prt_billing_code) eq 'w'){
                                if ($kit_part eq 'false'){
                                    $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                }
                                else{
                                    $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                }
                            } elsif (lc($prt_billing_code) eq 's'){
                                if ($kit_part eq 'false'){
                                    $job_service_prt_sale = $job_service_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    $job_service_prt_cost = $job_service_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                }
                                else{
                                    $job_service_prt_sale = $job_service_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                }
                            }
                        }
                }
            } else {
                     #This is the  line number  which   contain    one of the  labor item line type 'Q'
                     if($find_ro_line && $find_ro_job_line){
                         if($coupon_amount < 0) {
                            push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7.2f %11s %5s %9.2f".border("|")."\n",
                                            "", $part_description_list[0],  $quantity_sold."@", $result ,$unit_sale, "", $unit_cost));  
                         } else {
                            push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7.2f %11s %5s %9.2f".border("|")."\n",
                                            "", $part_description_list[0],  $quantity_sold."@", $result ,"Included", "", $unit_cost)); 
                            
                         } 
                     }
                     else {
                         #checking  Orderd parts 
                         if($special_order_emergency_purchase   ne '' && $special_order_emergency_purchase eq 'S'){
                            push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7.2f %11s %4s %9.2f".border("|")."\n",
                                            "", $part_description_list[0],  $quantity_sold."@", $result ,"Ordered", "", $unit_cost));
                         }
                         else{
                             push (@parts_section_array, sprintf(border(">")."%5s %-48s %5s%7.2f %11s %4s %9.2f".border("|")."\n",
                                            "", $part_description_list[0],  $quantity_sold."@", $result , $unit_sale, "", $unit_cost));
                         }
                     }
                    foreach my $i (1 .. $#part_description_list) {
                        push (@parts_section_array, sprintf(border(">")."%5s %-49s %5s%7.2f %11s %4s %9.2f".border("|")."\n",
                                                "", $part_description_list[$i],  "" , "", "", "", ""));
                    }
                     my $prt_detail_qry = $conn->prepare("SELECT
                                                        left(prt_billing_type, 1) AS prt_billing_code,
                                                        prt_billing_type,
                                                        CASE WHEN group_price!='Y' THEN quantity_sold END AS quantity_sold,
                                                        CASE WHEN group_price!='Y' THEN unit_cost END AS unit_cost,
                                                        CASE WHEN group_price!='Y' THEN unit_sale END AS unit_sale,
                                                        CASE WHEN group_price!='Y' THEN unit_core_cost END AS unit_core_cost,
                                                        CASE WHEN group_price!='Y' THEN unit_core_sale END AS unit_core_sale
                                                    FROM repair_part rp
                                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ? AND quantity_sold != 0
                                                          AND ro_part_line = ?
                                                    ORDER BY ro_part_line");
                    $prt_detail_qry->execute($ro_number, $ro_line, $ro_job_line, $ro_part_line);
                    while ( my @prt_detail_row = $prt_detail_qry->fetchrow_array){
                        ( $prt_billing_code, $prt_billing_type, $quantity_sold, $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale ) = @prt_detail_row;

                        if($find_ro_line &&  $find_ro_job_line && $kit_part eq 'false'){

                            if (lc($prt_billing_code) eq 'i'){
                                $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                            } elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                                $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                            } elsif (lc($prt_billing_code) eq 'c'){
                                $job_cust_prt_cost = $job_cust_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                            } elsif (lc($prt_billing_code) eq 'w'){
                                $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                            } elsif (lc($prt_billing_code) eq 's'){
                                $job_service_prt_cost = $job_service_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                            }
                            if($coupon_amount < 0) {
                                if (lc($prt_billing_code) eq 'i'){
                                    $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                } elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                                    $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                } elsif (lc($prt_billing_code) eq 'c'){
                                    $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                } elsif (lc($prt_billing_code) eq 'w'){
                                    $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                } elsif (lc($prt_billing_code) eq 's'){
                                    $job_service_prt_sale = $job_service_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                }

                            }

                        }

                        else{
                            #checking  Orderd parts 
                            if($special_order_emergency_purchase  eq '' && $special_order_emergency_purchase ne 'S'){
                                if (lc($prt_billing_code) eq 'i'){
                                    if ($kit_part eq 'false'){
                                        $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                        $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                    }
                                    else{
                                        $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    }
                                } elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                                    if ($kit_part eq 'false'){
                                        $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                        $job_intern_prt_cost = $job_intern_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                    }
                                    else{
                                        $job_intern_prt_sale = $job_intern_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    }
                                } elsif (lc($prt_billing_code) eq 'c'){
                                    if ($kit_part eq 'false'){
                                        $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                        $job_cust_prt_cost = $job_cust_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                    }
                                    else{
                                        $job_cust_prt_sale = $job_cust_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    }
                                } elsif (lc($prt_billing_code) eq 'w'){
                                    if ($kit_part eq 'false'){
                                        $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                        $job_warr_prt_cost = $job_warr_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                    }
                                    else{
                                        $job_warr_prt_sale = $job_warr_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    }
                                } elsif (lc($prt_billing_code) eq 's'){
                                    if ($kit_part eq 'false'){
                                        $job_service_prt_sale = $job_service_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                        $job_service_prt_cost = $job_service_prt_cost + ($unit_cost) + ($unit_core_cost*$quantity_sold);
                                    }
                                    else{
                                        $job_service_prt_sale = $job_service_prt_sale + ($unit_sale) + ($unit_core_sale*$quantity_sold);
                                    }
                                }
                            }
                        } 
                    # }   
                    }
                 }
        }
    }
    return @parts_section_array;
}

# Subroutine to prepare the MISC section of a JOB
sub make_misc_section{

    my ($ro_line) = @_;

    my @misc_section_array;
    my $misc_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       left(other_billing_type, 1) AS other_billing_type,
                                       other_cost,
                                       other_sale
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line =? AND item_type = 'MISC'");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale);
    $misc_entries = $misc_qry->rows;
    if($misc_entries > 0){

        while (my @misc_row = $misc_qry->fetchrow_array) {
            ( $misc_code, $misc_description, $other_billing_type, $misc_cost, $misc_sale) = @misc_row;
            $Text::Wrap::columns = 28;
            $misc_description = $misc_code." ".$misc_description;
            my $wrapped_misc_description = fill('', '', expand($misc_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;

            push (@misc_section_array, sprintf(border(">")."%5s  %-35s %20s%6.2f %6.2f %5s %8.2f"."\n",
                                            "", $misc_description_list[0],  "" , "",$misc_sale, "", $misc_cost));

            foreach my $i (1 .. $#misc_description_list) {
                push (@misc_section_array, sprintf(border(">")."%5s  %-35s %20s %6.2f %5s %8.2f"."\n",
                                            "", $misc_description_list[$i],  "" , "", "","", ""));
            }

            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $job_intern_misc_sale = $job_intern_misc_sale + $misc_sale;
                $job_intern_misc_cost = $job_intern_misc_cost + $misc_cost;
            } elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                $job_intern_misc_sale = $job_intern_misc_sale + $misc_sale;
                $job_intern_misc_cost = $job_intern_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 'c'){
                $job_cust_misc_sale = $job_cust_misc_sale + $misc_sale;
                $job_cust_misc_cost = $job_cust_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $job_warr_misc_sale = $job_warr_misc_sale + $misc_sale;
                $job_warr_misc_cost = $job_warr_misc_cost + $misc_cost;
            } elsif (lc($other_billing_type) eq 's'){
                $job_service_misc_sale = $job_service_misc_sale + $misc_sale;
                $job_service_misc_cost = $job_service_misc_cost + $misc_cost;
            }

        }
    }
    return @misc_section_array;
}

# Subroutine to prepare the GOG section of a JOB
sub make_gog_section{
    my ($ro_line) = @_;
    my @gog_section_array;
    my $gog_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    left(other_billing_type, 1) AS other_billing_type,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'GOG'");
    $gog_qry->execute($ro_number, $ro_line);
    my ( $gog_code, $gog_description, $other_billing_type, $gog_cost, $gog_sale);

    $gog_entries = $gog_qry->rows;
    if($gog_qry->rows > 0){

        while (my @gog_row = $gog_qry->fetchrow_array) {
            ( $gog_code, $gog_description, $other_billing_type, $gog_cost, $gog_sale) = @gog_row;
            $Text::Wrap::columns = 28;
            $gog_description = $gog_code." ".$gog_description;
            my $wrapped_gog_description = fill('', '', expand($gog_description));
            my @gog_description_list = split "\n", $wrapped_gog_description;
                    push (@gog_section_array, sprintf(border(">")."%5s  %-35s %20s%6.2f %6.2f %5s %8.2f".border("|")."\n",
                                            "", $gog_description_list[0],  "" , "", $gog_sale, "", $gog_cost));

            foreach my $i (1 .. $#gog_description_list) {
                push (@gog_section_array, sprintf(border(">")."%5s  %-35s %20s%6.2f %6.2f %5s %8.2f".border("|")."\n", "", $gog_description_list[$i], "", "", "", "", ""));
            }
            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $job_intern_gog_sale = $job_intern_gog_sale + $gog_sale;
                $job_intern_gog_cost = $job_intern_gog_cost + $gog_cost;
            } elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                $job_intern_gog_sale = $job_intern_gog_sale + $gog_sale;
                $job_intern_gog_cost = $job_intern_gog_cost + $gog_cost;
            }elsif (lc($other_billing_type) eq 'c'){
                $job_cust_gog_sale = $job_cust_gog_sale + $gog_sale;
                $job_cust_gog_cost = $job_cust_gog_cost + $gog_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $job_warr_gog_sale = $job_warr_gog_sale + $gog_sale;
                $job_warr_gog_cost = $job_warr_gog_cost + $gog_cost;
            } elsif (lc($other_billing_type) eq 's'){
                $job_service_gog_sale = $job_service_gog_sale + $gog_sale;
                $job_service_gog_cost = $job_service_gog_cost + $gog_cost;
            }
        }
    }
    return @gog_section_array;
}

# Subroutine to prepare the SUBLET section of a JOB
sub make_sublet_section{
    my ($ro_line, $billing_code) = @_;
    my @sublet_section_array;
    my $sublet_qry = $conn->prepare("SELECT
                                        other_code,
                                        other_description,
                                        left(other_billing_type, 1) AS other_billing_type,
                                        other_cost,
                                        coalesce (nullif(other_sale::text, ''), '0')::numeric as other_sale
                                    FROM repair_other
                                    WHERE ro_number = ? AND ro_line =? AND item_type = 'SUBLET'");
    $sublet_qry->execute($ro_number, $ro_line);
    my ( $sublet_code, $sublet_description, $other_billing_type, $sublet_cost, $sublet_sale);

    $sublet_entries = $sublet_qry->rows;
    if($sublet_entries > 0){

        while (my @sublet_row = $sublet_qry->fetchrow_array) {
            ( $sublet_code, $sublet_description, $other_billing_type, $sublet_cost, $sublet_sale) = @sublet_row;
            $Text::Wrap::columns = 60;
            my $wrapped_sublet_description = fill('', '', expand($sublet_description));
            my @sublet_description_list = split "\n", $wrapped_sublet_description;
            if($sublet_sale > 0 || $sublet_cost > 0){
            push (@sublet_section_array, sprintf(border(">")."%4s  %-64s %8.2f %-6s %8.2f".border("|")."\n",
                                            "", $sublet_description_list[0], $sublet_sale, "", $sublet_cost));
            foreach my $i (1 .. $#sublet_description_list) {
                push (@sublet_section_array, sprintf(border(">")."%4s  %-64s %8s %-6s %8s".border("|")."\n",
                                            "", $sublet_description_list[$i], "", "", ""));
            }
            }
            $other_billing_type = ($other_billing_type?$other_billing_type:$job_billing_code);
            if (lc($other_billing_type) eq 'i'){
                $work_internal_sublet_amt = $work_internal_sublet_amt + $sublet_sale;
                $job_intern_sublet_sale = $job_intern_sublet_sale + $sublet_sale;
                $job_intern_sublet_cost = $job_intern_sublet_cost + $sublet_cost;
            } elsif (lc($line_type) eq "a" && $policy_adjustment ne "0"){
                $work_internal_sublet_amt = $work_internal_sublet_amt + $sublet_sale;
                $job_intern_sublet_sale = $job_intern_sublet_sale + $sublet_sale;
                $job_intern_sublet_cost = $job_intern_sublet_cost + $sublet_cost;
            } elsif (lc($other_billing_type) eq 'c'){
                $work_customer_sublet_amt = $work_customer_sublet_amt + $sublet_sale;
                $job_cust_sublet_sale = $job_cust_sublet_sale + $sublet_sale;
                $job_cust_sublet_cost = $job_cust_sublet_cost + $sublet_cost;
            } elsif (lc($other_billing_type) eq 'w'){
                $work_waranty_sublet_amt = $work_waranty_sublet_amt + $sublet_sale;
                $job_warr_sublet_sale = $job_warr_sublet_sale + $sublet_sale;
                $job_warr_sublet_cost = $job_warr_sublet_cost + $sublet_cost;
            } elsif (lc($other_billing_type) eq 's'){
                $work_service_sublet_amt = $work_service_sublet_amt + $sublet_sale;
                $job_service_sublet_sale = $job_service_sublet_sale + $sublet_sale;
                $job_service_sublet_cost = $job_service_sublet_cost + $sublet_cost;
            }
        }
    }
    return @sublet_section_array;
}

# Subroutine to prepare the DEDUCTIBLE section of a JOB
sub make_deductible_section{
    my ($ro_line) = @_;
    my @ded_section_array;
    my $ded_qry = $conn->prepare("SELECT
                                    other_code,
                                    other_description,
                                    other_cost,
                                    other_sale
                                FROM repair_other
                                WHERE ro_number = ? AND ro_line =? AND item_type = 'DEDUCTIBLE'");
    $ded_qry->execute($ro_number, $ro_line);
    my ( $ded_code, $ded_description, $ded_cost, $ded_sale);

    $ded_entries = $ded_qry->rows;
    if($ded_entries > 0){
        while (my @ded_row = $ded_qry->fetchrow_array) {
            ( $ded_code, $ded_description, $ded_cost, $ded_sale) = @ded_row;
            $Text::Wrap::columns = 60;
            my $wrapped_ded_description = fill('', '', expand($ded_description));
            my @ded_description_list = split "\n", $wrapped_ded_description;
            push (@ded_section_array, sprintf(border(">")."%5s  %-35s %20s%6.2f %6.2f %-3s %8.2f".border("|")."\n",
                                            "", $ded_description_list[0],  "" , "", $ded_sale, "", $ded_cost));
            foreach my $i (1 .. $#ded_description_list) {
                push (@ded_section_array, sprintf(border(">")."%5s  %-35s %20s%6.2f %6.2f %-3s %8.2f".border("|")."\n",
                                            "", $ded_description_list[$i],  "" , "", "", "", ""));
            }
            $job_cust_ded_sale = $job_cust_ded_sale + $ded_sale;
            $job_cust_ded_cost = $job_cust_ded_cost + $ded_cost;
        }
    }
    $tot_cust_ded_sale = $tot_cust_ded_sale+$job_cust_ded_sale;
    return @ded_section_array;
}

# Subroutine to prepare the TOTAL section of a JOB
sub make_job_total_section{
    my ($ro_line) = @_;
    my @job_total_section_array;

    $tot_cust_lbr_cost = $tot_cust_lbr_cost+$job_cust_lbr_cost;
    $tot_cust_lbr_sale = $tot_cust_lbr_sale+$job_cust_lbr_sale;
    $tot_cust_prt_cost = $tot_cust_prt_cost+$job_cust_prt_cost;
   

    $tot_cust_prt_sale = $tot_cust_prt_sale+$job_cust_prt_sale;
    $tot_cust_misc_cost = $tot_cust_misc_cost+$job_cust_misc_cost;
    $tot_cust_misc_sale = $tot_cust_misc_sale+$job_cust_misc_sale;
    $tot_cust_gog_cost = $tot_cust_gog_cost+$job_cust_gog_cost;
    $tot_cust_gog_sale = $tot_cust_gog_sale+$job_cust_gog_sale;
    $tot_cust_sublet_cost = $tot_cust_sublet_cost+$job_cust_sublet_cost;
    $tot_cust_sublet_sale = $tot_cust_sublet_sale+$job_cust_sublet_sale;

    $tot_warr_lbr_cost = $tot_warr_lbr_cost+$job_warr_lbr_cost;
    $tot_warr_lbr_sale = $tot_warr_lbr_sale+$job_warr_lbr_sale;
    $tot_warr_prt_cost = $tot_warr_prt_cost+$job_warr_prt_cost;
    $tot_warr_prt_sale = $tot_warr_prt_sale+$job_warr_prt_sale;
    $tot_warr_misc_cost = $tot_warr_misc_cost+$job_warr_misc_cost;
    $tot_warr_misc_sale = $tot_warr_misc_sale+$job_warr_misc_sale;
    $tot_warr_gog_cost = $tot_warr_gog_cost+$job_warr_gog_cost;
    $tot_warr_gog_sale = $tot_warr_gog_sale+$job_warr_gog_sale;
    $tot_warr_sublet_cost = $tot_warr_sublet_cost+$job_warr_sublet_cost;
    $tot_warr_sublet_sale = $tot_warr_sublet_sale+$job_warr_sublet_sale;

    $tot_intern_lbr_cost = $tot_intern_lbr_cost+$job_intern_lbr_cost;
    $tot_intern_lbr_sale = $tot_intern_lbr_sale+$job_intern_lbr_sale;
    $tot_intern_prt_cost = $tot_intern_prt_cost+$job_intern_prt_cost;
    $tot_intern_prt_sale = $tot_intern_prt_sale+$job_intern_prt_sale;
    $tot_intern_misc_cost = $tot_intern_misc_cost+$job_intern_misc_cost;
    $tot_intern_misc_sale = $tot_intern_misc_sale+$job_intern_misc_sale;
    $tot_intern_gog_cost = $tot_intern_gog_cost+$job_intern_gog_cost;
    $tot_intern_gog_sale = $tot_intern_gog_sale+$job_intern_gog_sale;
    $tot_intern_sublet_cost = $tot_intern_sublet_cost+$job_intern_sublet_cost;
    $tot_intern_sublet_sale = $tot_intern_sublet_sale+$job_intern_sublet_sale;

    $tot_service_lbr_cost = $tot_service_lbr_cost+$job_service_lbr_cost;
    $tot_service_lbr_sale = $tot_service_lbr_sale+$job_service_lbr_sale;
    $tot_service_prt_cost = $tot_service_prt_cost+$job_service_prt_cost;
    $tot_service_prt_sale = $tot_service_prt_sale+$job_service_prt_sale;
    $tot_service_misc_cost = $tot_service_misc_cost+$job_service_misc_cost;
    $tot_service_misc_sale = $tot_service_misc_sale+$job_service_misc_sale;
    $tot_service_gog_cost = $tot_service_gog_cost+$job_service_gog_cost;
    $tot_service_gog_sale = $tot_service_gog_sale+$job_service_gog_sale;
    $tot_service_sublet_cost = $tot_service_sublet_cost+$job_service_sublet_cost;
    $tot_service_sublet_sale = $tot_service_sublet_sale+$job_service_sublet_sale;


    $tot_cust_fee_sale = $tot_cust_fee_sale + $job_cust_fee_sale;
    $tot_intern_fee_sale = $tot_intern_fee_sale + $job_intern_fee_sale;
    $tot_warr_fee_sale = $tot_warr_fee_sale + $job_warr_fee_sale;
    $tot_service_fee_sale = $tot_service_fee_sale + $job_service_fee_sale;

    return @job_total_section_array;
}

#Subroutine   to print vale if its greater than 0  otherwise print null
sub printstr
{
    my ($x) = @_;
    if($x>0){
       sprintf("%11.2f", $x); 
    }
    else{
       sprintf("%11.2s", "");
    }
}
sub printspecial
{
    my ($x) = @_;
    if($x<0){
       sprintf("%11.2f", $x); 
    }
    elsif($x>0){
        sprintf("%11.2f", $x);
    }
    else{
       sprintf("%11.2s", "");
    }
}
sub printstr_new
{
    my ($x) = @_;
    if($x!=0){
       sprintf("%11.2f", $x); 
    }
   else{
       sprintf("%11.2s", "");
    }

}
# Subroutine to get the RO TAX amount
sub get_ro_tax_amount {
    my $tax_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ? AND item_type = 'TAX'");
    $tax_qry->execute($ro_number);
    my ( $tax_type, $tax_amount);

    while (my @tax_row = $tax_qry->fetchrow_array) {
        ( $tax_type, $tax_amount) = @tax_row;
        if (lc($line_type) eq "a" && $policy_adjustment ne "0"){
	    $intern_ro_tax = $tax_amount;
        }elsif (lc($tax_type) eq "c"){
            $cust_ro_tax = $tax_amount;
        }elsif (lc($tax_type) eq "w"){
            $warr_ro_tax = $tax_amount;
        }elsif (lc($tax_type) eq "i"){
            $intern_ro_tax = $tax_amount;
        }elsif (lc($tax_type) eq "s"){
            $service_ro_tax = $tax_amount;
        }
    }
}

#Subroutine to prepare the FOOTER section of the invoice
sub get_footer {
    my $other_ded_detail_qry = $conn->prepare("SELECT
                                       service_contract_deduct_paid
                                   FROM repair_other
                                   WHERE ro_number = ? AND service_contract_deduct_paid is NOT NULL");
    $other_ded_detail_qry->execute($ro_number);
    my $service_contract_deduct_paid;
    
    my $other_ded_detail_row = $other_ded_detail_qry->fetchrow_array;
    $service_contract_deduct_paid = $other_ded_detail_row;
    my $neg_service_contract_deduct_paid = -$service_contract_deduct_paid;

    my $other_detail_qry = $conn->prepare("SELECT
                                       item_type,
                                       other_billing_type,
                                       other_cost                                    
                                   FROM repair_other
                                   WHERE ro_number = ? AND ro_line is NULL");
    $other_detail_qry->execute($ro_number);
    my ( $item_type,$other_billing_type,$other_cost,$TotalCouponDiscount,$SpecialOrderDeposits,$CustomerPayHazardousMaterials,$sublet_amt,$internal_sale_tax,$warranty_sale_tax,$customer_sale_tax,$service_sale_tax,
    $CustomerPayShopSup,$WarrantyShopSup,$InternalShopSup,$ServicePayShopSup,$total_internal_sale,$total_service_sale,$total_waranty_sale,$total_customer_sale,$total_,$total_cost);
   
    while (my @other_detail_row = $other_detail_qry->fetchrow_array) {

            ($item_type,$other_billing_type,$other_cost) = @other_detail_row;
            if($item_type eq 'CDISC'){
                $TotalCouponDiscount=$other_cost;

            }
            if($item_type eq 'SPECDEP'){
                $SpecialOrderDeposits=$other_cost;
            }
            if($item_type eq 'HAZMAT'){
                 $CustomerPayHazardousMaterials=$other_cost;
            }
            # if($item_type eq 'SUBLET'){
            #      $sublet_amt=$other_cost;
            # }
            # if($work_sublet_amt>0){
            #      $sublet_amt=$work_sublet_amt;
            # }
            if($item_type eq 'TAX'){
            
               if($other_billing_type eq 'I'){
                    $internal_sale_tax = $other_cost;
               }
               if($other_billing_type eq 'W'){
                    $warranty_sale_tax = $other_cost;
               }
               if($other_billing_type eq 'C'){
                    $customer_sale_tax= $other_cost;
               }
               if($other_billing_type eq 'S'){
                    $service_sale_tax= $other_cost;
               }
               
            }
            if($item_type eq 'PAYSHOP'){
            
               if($other_billing_type eq 'I'){
                    $InternalShopSup = $other_cost;
               }
               if($other_billing_type eq 'W'){
                    $WarrantyShopSup = $other_cost;
               }
               if($other_billing_type eq 'C'){
                   $CustomerPayShopSup = $other_cost;
               }
              if($other_billing_type eq 'S'){
                   $ServicePayShopSup = $other_cost;
               }

            }
    }

    $CustomerPayShopSup = $CustomerPayShopSup + $CustomerPaintSale;
    $ServicePayShopSup = $ServicePayShopSup + $ServicePaintSale;
    $WarrantyShopSup = $WarrantyShopSup + $WarrantyPaintSale;
    $InternalShopSup = $InternalShopSup + $InternalPaintSale;
    
    my $trimmed_total_coupon_discount = substr($TotalCouponDiscount, 1);

    $total_customer_sale  = 0;
    $total_waranty_sale   = 0;
    $total_internal_sale  = 0;
    $total_service_sale   = 0;

    $total_customer_sale = ($tot_cust_lbr_sale+$tot_cust_prt_sale+$work_customer_sublet_amt+$CustomerPayShopSup+$CustomerPayHazardousMaterials+$customer_sale_tax+$SpecialOrderDeposits+$tot_cust_fee_sale)-$trimmed_total_coupon_discount;
    $total_waranty_sale  = ($tot_warr_lbr_sale+$tot_warr_prt_sale+$work_waranty_sublet_amt+$WarrantyShopSup+$warranty_sale_tax+$tot_warr_fee_sale);
    $total_internal_sale = ($tot_intern_lbr_sale+$tot_intern_prt_sale+$work_internal_sublet_amt+$InternalShopSup+$internal_sale_tax+$tot_intern_fee_sale);
    $total_service_sale  = ($tot_service_lbr_sale+$tot_service_prt_sale+$work_service_sublet_amt+$ServicePayShopSup+$service_sale_tax+$tot_service_fee_sale+$neg_service_contract_deduct_paid);

    $total_cost = 0;

    $total_cost =($tot_intern_lbr_cost + $tot_service_lbr_cost + $tot_warr_lbr_cost + $tot_cust_lbr_cost + $work_cost_sublet_amt + $tot_service_sublet_cost + $tot_cust_sublet_cost + $tot_warr_sublet_cost +  $tot_intern_sublet_cost)+($tot_intern_prt_cost + $tot_service_prt_cost + $tot_warr_prt_cost + $tot_cust_prt_cost); 

    my @footer_section;
    my ($is_end, $page_number) = @_;
    get_ro_tax_amount();

    my ($cust_total_lbr_sale,$total_lbr_cost,$cust_total_prt_sale,$total_prt_cost);
    $cust_total_lbr_sale = $tot_cust_lbr_sale;
    $total_lbr_cost = ($tot_intern_lbr_cost + $tot_service_lbr_cost + $tot_warr_lbr_cost + $tot_cust_lbr_cost);
    $cust_total_prt_sale = $tot_cust_prt_sale+$qlabor_cost;
    $total_customer_sale  = $total_customer_sale+$qlabor_cost+$service_contract_deduct_paid;
    $total_prt_cost = ($tot_intern_prt_cost + $tot_service_prt_cost + $tot_warr_prt_cost + $tot_cust_prt_cost);

    if($is_end) {
        # push (@footer_section, sprintf(border(">")."%30s"."\n",""));
        # push (@footer_section, sprintf(border(">")."%30s"."\n",""));
        if($tot_intern_fee_sale > 0 || $tot_service_fee_sale > 0 || $tot_warr_fee_sale > 0 || $tot_cust_fee_sale > 0){
           push (@footer_section, sprintf(border(">")."%-30s%11s %11s %11s %11s %11s"."\n",
              "Total Fee Amounts",printstr($tot_intern_fee_sale),printstr($tot_service_fee_sale),printstr($tot_warr_fee_sale),printstr($tot_cust_fee_sale),""));  
     } else {
        # Blank row if no fee amounts exist
        push(@footer_section, sprintf(border(">")."%-30s%11s %11s %11s %11s %11s\n",
            "", "", "", "", "", ""
        ));
        }

        push (@footer_section, sprintf(border(">")."%95s"."\n",""));
        push (@footer_section, sprintf(border(">")."%95s"."\n",""));
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "",printstr($tot_intern_lbr_sale),printstr($tot_service_lbr_sale),printstr_new($tot_warr_lbr_sale),printstr($cust_total_lbr_sale),printstr($total_lbr_cost)));
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "",printstr($tot_intern_prt_sale),printstr($tot_service_prt_sale),printstr_new($tot_warr_prt_sale),printstr($cust_total_prt_sale),printstr($total_prt_cost)));
        # Deductible
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "","", printspecial($neg_service_contract_deduct_paid), "", printspecial($service_contract_deduct_paid),""));

        #Sublet      
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "", printstr($work_internal_sublet_amt), printstr($work_service_sublet_amt), printstr_new($work_waranty_sublet_amt), printstr($work_customer_sublet_amt), printstr($work_cost_sublet_amt + $tot_service_sublet_cost + $tot_cust_sublet_cost + $tot_warr_sublet_cost +  $tot_intern_sublet_cost)));
                                                     
        #Shop supplies      
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "",printstr($InternalShopSup), printstr($ServicePayShopSup), printstr_new($WarrantyShopSup), printstr($CustomerPayShopSup),""));

        #Hazardous Materials      
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "","", "", "", printstr($CustomerPayHazardousMaterials),""));

        #sale tax       
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "", printstr($internal_sale_tax),printstr($service_sale_tax),printstr_new($warranty_sale_tax),printstr($customer_sale_tax),""));

        #special order deposit      
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "","", "", "", printspecial($SpecialOrderDeposits),""));

        #discounts      
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "","", "", "", substr($TotalCouponDiscount, 1) > 0 ? $TotalCouponDiscount:"", ""));
        #total      
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "",printstr($total_internal_sale),printstr($total_service_sale),printstr_new($total_waranty_sale), printstr($total_customer_sale),printstr($total_cost)));
        push (@footer_section, sprintf(border(">")."%30s%11s %11s %11s %11s %11s"."\n",
              "","", "", "", "", ""));

    }
    push (@footer_section, sprintf(border("#")."%95s"."\f", ""));
    
    return @footer_section;
}

# Subroutine to make the Total(RO) FEE section
sub make_ro_fee_section {
    my $fee_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_description,
                                      other_cost,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ?
                                  AND item_type = 'FEE'
                                  AND ro_line IS NULL");
    $fee_qry->execute($ro_number);
    my ( $fee_type, $fee_description, $fee_cost, $fee_sale);

    while (my @fee_row = $fee_qry->fetchrow_array) {
        ( $fee_type, $fee_description, $fee_cost, $fee_sale) = @fee_row;
        push (@ro_fee_section, sprintf(border(">")."%-30s %7.2f %7.2f  %20s %8.2f ".border("|")."\n",
              $fee_description, $fee_cost, $fee_sale, "",  $fee_sale));
        if (lc($line_type) eq "a" && $policy_adjustment ne "0"){
     	    $tot_intern_misc_sale = $tot_intern_misc_sale + $fee_sale;
            $tot_intern_misc_cost = $tot_intern_misc_cost + $fee_cost;
        }elsif (lc($fee_type) eq "c"){
            $tot_cust_misc_sale = $tot_cust_misc_sale + $fee_sale;
            $tot_cust_misc_cost = $tot_cust_misc_cost + $fee_cost;
        }elsif (lc($fee_type) eq "w"){
            $tot_warr_misc_sale = $tot_warr_misc_sale + $fee_sale;
            $tot_warr_misc_cost = $tot_warr_misc_cost + $fee_cost;
        }elsif (lc($fee_type) eq "i"){
            $tot_intern_misc_sale = $tot_intern_misc_sale + $fee_sale;
            $tot_intern_misc_cost = $tot_intern_misc_cost + $fee_cost;
        }elsif (lc($fee_type) eq "s"){
            $tot_service_misc_sale = $tot_service_misc_sale + $fee_sale;
            $tot_service_misc_cost = $tot_service_misc_cost + $fee_cost;
        }
    }
    return @ro_fee_section;
}

sub make_ro_paint_section {
    my $paint_qry = $conn->prepare("SELECT
                                      other_code,
                                      other_description,
                                      other_cost,
                                      other_sale
                                  FROM repair_other
                                  WHERE ro_number = ?
                                  AND item_type = 'PAYSHOP' AND  other_sale is not NULL ");
    $paint_qry->execute($ro_number);
    my ( $paint_type, $paint_description, $paint_cost, $paint_sale);

    
    while (my @paint_row = $paint_qry->fetchrow_array) {
        push (@ro_paint_section, sprintf(border("#")."%s".border("|")."\n", "-"x96));
        ( $paint_type, $paint_description, $paint_cost, $paint_sale) = @paint_row;
        push (@ro_paint_section, sprintf(border(">")."%5s %-24s %7s %7s  %22s %8.2f ".border("|")."\n", "",
              $paint_description, "", "", "",  $paint_sale));
        if (lc($paint_type) eq "c"){
            $CustomerPaintSale = $CustomerPaintSale + $paint_sale;
            $CustomerPaintCost = $CustomerPaintCost + $paint_cost;
        }elsif (lc($paint_type) eq "w"){
            $WarrantyPaintSale = $WarrantyPaintSale + $paint_sale;
            $WarrantyPaintCost = $WarrantyPaintCost + $paint_cost;
        }elsif (lc($paint_type) eq "i"){
            $InternalPaintSale = $InternalPaintSale + $paint_sale;
            $InternalPaintCost = $InternalPaintCost + $paint_cost;
        }elsif (lc($paint_type) eq "s"){
            $ServicePaintSale = $ServicePaintSale + $paint_sale;
            $ServicePaintCost = $ServicePaintCost + $paint_cost;
        }
    }
    return @ro_paint_section;
}


# Subroutine to make the Total(RO) Discount section
sub make_ro_disc_section {
    my $disc_qry = $conn->prepare("SELECT
                                      disc_code,
                                      disc_description,
                                      disc_applied,
                                      labor_discount,
                                      parts_discount,
                                      total_discount
                                  FROM repair_discount
                                  WHERE ro_number = ?
                                  AND ro_line IS NULL");
    $disc_qry->execute($ro_number);
    my ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount );

    while (my @disc_row = $disc_qry->fetchrow_array) {
        ( $disc_code, $disc_description, $disc_applied, $labor_discount, $parts_discount, $total_discount ) = @disc_row;
        $Text::Wrap::columns = 68;
        $disc_description = $disc_code." ".$disc_description;
        my $wrapped_disc_description = fill('', '', expand($disc_description));
        my @disc_description_list = split "\n", $wrapped_disc_description;

        foreach my $i (0 .. $#disc_description_list) {
            push (@ro_disc_section, sprintf(border(">")."%-68s%10s".border("|")."\n",
                                    $disc_description_list[$i], ""));
        }

        $cust_discount = $cust_discount + $total_discount;
    }
    return @ro_disc_section;
}

# Subroutine to make the Tech-Punch section
sub make_ro_tech_punch_section {
    my $tech_punch_qry = $conn->prepare("SELECT
                                      ro_line,
                                      tech_id,
                                      to_char(work_date, 'mm-dd-yy') AS work_date,
                                      to_char(work_start_time, 'HH24:MI'),
                                      to_char(work_end_time, 'HH24:MI'),
                                      work_type,
                                      work_duration
                                  FROM repair_tech_punch
                                  WHERE ro_number = ?
                                  ORDER BY work_start_time");
    $tech_punch_qry->execute($ro_number);
    my ( $ro_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_type, $work_duration );

    $tech_punch_entries = $tech_punch_qry->rows;
    if($tech_punch_entries > 0){
        push (@ro_tech_punch_section, sprintf(border("#")."%78s".border("|")."\n", ""));
        push (@ro_tech_punch_section, sprintf(border("#")."%4sDATE   START  FINISH  DURATION  TYPE    TECH  LINE(S)  CHG%16s".border("|")."\n", "", ""));
        while ( my @tech_punch_row = $tech_punch_qry->fetchrow_array) {
            ( $ro_line, $tech_id, $work_date, $work_start_time, $work_end_time, $work_type, $work_duration ) = @tech_punch_row;
            push (@ro_tech_punch_section, sprintf(border(">")."%-8s   %5s  %6s  %8.2f  %4s  %6s  %7s %20s".border("|")."\n",
                                            $work_date, $work_start_time, $work_end_time, $work_duration, $work_type, $tech_id, $ro_line, ""));
        }
    }
    return @ro_tech_punch_section;
}

# Subroutine to make the Tech-Punch section
sub make_ro_account_section {
    my $ro_account_qry = $conn->prepare("SELECT
                                      ro_account_no,
                                      account_description,
                                      account_controlnumber,
                                      account_referencenumber,
                                      account_transactionamount
                                  FROM repair_account
                                  WHERE ro_number = ?
                                  ORDER BY ro_account_id");
    $ro_account_qry->execute($ro_number);
    my ( $ro_account_no, $account_description, $account_controlnumber, $account_referencenumber, $account_transactionamount );

    $account_entries = $ro_account_qry->rows;
    if($account_entries > 0){
        #push (@ro_account_section, sprintf(border("#")."%95s".border("|")."\n", ""));
        #push (@ro_account_section, sprintf(border("#")."%2sAccount %7s Description %28s Control    Reference %7s Amount ".border("|")."\n", "", "", "", ""));
        push (@ro_account_section, sprintf(border("#")."%2s%92s ".border("|")."\n", "", "_"x92));
        while ( my @ro_account_row = $ro_account_qry->fetchrow_array) {
            ( $ro_account_no, $account_description, $account_controlnumber, $account_referencenumber, $account_transactionamount ) = @ro_account_row;
            push (@ro_account_section, sprintf(border(">")." %-16s %-40s %-10s %-10s      %8.2f ".border("|")."\n",
                                            $ro_account_no, ($ro_account_no eq '*VOID')? " ":$account_description, $account_controlnumber, $account_referencenumber, $account_transactionamount));
        }
    }
    return @ro_account_section;
}

# Print Open/Void RO
sub print_open_ro {
    printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO# ".$ro_number." * See Open and Void RO Report *");
}

# Print Open/Void RO
sub print_open_ro_series {
    # $num_args = scalar(@_);
    printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO Range# ".$_[0]." * See Open and Void RO Report *");
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")."%95s".border("|")."\n", "");
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
 sub print_header_section {
    my ($page_num) = @_;
    my %hash = (pg_num => $page_num );
    foreach (@header_section)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
 }

# Subroutine for pagination
sub paginate_and_print_segment {
    my (@section_array) = @_;
    # Print section that fit in the current page
    if(scalar(@section_array) <= ($page_max_height - $curr_page_height)){
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{
        for my $sub_section(@section_array){
            if($curr_page_height < $page_max_height)
            {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            } else{
                print FILE get_footer(0, $curr_page_num);
                $curr_page_num = $curr_page_num + 1;
                print_header_section ($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
            }
        }
    }
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
    ($tot_cust_lbr_cost, $tot_cust_lbr_sale, $tot_cust_prt_cost, $tot_cust_prt_sale, $tot_cust_misc_cost, $tot_cust_misc_sale, $tot_cust_gog_cost, $tot_cust_gog_sale,
    $tot_cust_sublet_cost, $tot_cust_sublet_sale, $tot_cust_ded_cost, $tot_cust_ded_sale, $tot_cust_fee_sale )= (0)x13;

    ($tot_intern_lbr_cost, $tot_intern_lbr_sale, $tot_intern_prt_cost, $tot_intern_prt_sale, $tot_intern_misc_cost, $tot_intern_misc_sale, $tot_intern_gog_cost,
    $tot_intern_gog_sale, $tot_intern_sublet_cost, $tot_intern_sublet_sale, $tot_intern_ded_cost, $tot_intern_ded_sale, $tot_intern_fee_sale )= (0)x13;

    ($tot_warr_lbr_cost, $tot_warr_lbr_sale, $tot_warr_prt_cost, $tot_warr_prt_sale, $tot_warr_misc_cost, $tot_warr_misc_sale, $tot_warr_gog_cost, $tot_warr_gog_sale,
    $tot_warr_sublet_cost, $tot_warr_sublet_sale, $tot_warr_ded_cost, $tot_warr_ded_sale, $tot_warr_fee_sale )= (0)x13;

    ($tot_service_lbr_cost, $tot_service_lbr_sale, $tot_service_prt_cost, $tot_service_prt_sale, $tot_service_misc_cost, $tot_service_misc_sale, $tot_service_gog_cost, $tot_service_gog_sale,
    $tot_service_sublet_cost, $tot_service_sublet_sale, $tot_service_ded_cost, $tot_service_ded_sale, $tot_service_fee_sale )= (0)x13;

    ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax, $service_ro_tax) = (0)x4;
    $cust_discount = 0;

    $curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @comment_section;
    undef @job_section;
    undef @ro_fee_section;
    undef @ro_paint_section;
    undef @ro_disc_section;
    undef @ro_tech_punch_section;
    undef @ro_account_section;

    undef $tot_intern_lbr_cost;
    undef $tot_service_lbr_cost;
    undef $tot_warr_lbr_cost;
    undef $tot_cust_lbr_cost;
    undef $tot_intern_prt_cost; 
    undef $tot_service_prt_cost;
    undef $tot_warr_prt_cost;
    undef $tot_cust_prt_cost;
    undef $tot_intern_lbr_sale;
    undef $tot_service_lbr_sale;
    undef $tot_warr_lbr_sale;
    undef $tot_cust_lbr_sale;
    undef $tot_intern_prt_sale;
    undef $tot_service_prt_sale;
    undef $tot_warr_prt_sale;
    undef $tot_cust_prt_sale;
    undef $qlabor_cost;
    undef $work_waranty_sublet_amt;
    undef $work_internal_sublet_amt;
    undef $work_service_sublet_amt;
    undef $work_customer_sublet_amt;
    undef $work_cost_sublet_amt;
    undef $work_job_billing_code;
    undef $ar_number_label;

    undef $tot_cust_fee_sale;
    undef $tot_intern_fee_sale;
    undef $tot_warr_fee_sale;
    undef $tot_service_fee_sale;

    undef $CustomerPaintCost;
    undef $CustomerPaintSale;
    undef $WarrantyPaintCost;
    undef $WarrantyPaintSale;
    undef $InternalPaintCost;
    undef $InternalPaintSale;
    undef $ServicePaintCost;
    undef $ServicePaintSale;

    ##### Prepare invoice ##########
    make_header();
    make_job_section();
    make_ro_fee_section();
    # make_ro_disc_section();
    make_ro_paint_section();
    # make_ro_tech_punch_section();
    make_ro_account_section();

    ########## Print invoice ##########
    print_header_section ($curr_page_num);

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }

    # Pagination of RO fee section
    paginate_and_print_segment(@ro_fee_section);

    # Pagination of RO Discount section
    paginate_and_print_segment(@ro_disc_section);

    # Pagination of RO paint section
    paginate_and_print_segment(@ro_paint_section);

    # Pagination of RO Comments
    paginate_and_print_segment(@comment_section);

    # Pagination of RO Technician Punch details
    # paginate_and_print_segment(@ro_tech_punch_section);

    # Pagination of RO Account details
    paginate_and_print_segment(@ro_account_section);

    # End of invoice footer
    if($curr_page_height == $page_max_height){
        print FILE get_footer(1, $curr_page_num);
    } else {
        print_blank_line($page_max_height - $curr_page_height);
        print FILE get_footer(1, $curr_page_num);
    }
}

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}


sub plain_amount_format{
    use Number::Format qw(:subs);
    my $precision = 2;
    my $symbol    = '';
    my ($plain_amounts) = @_;
    my $plain_amount_formatted;
    if ( $plain_amounts == 0 || $plain_amounts == 0.00){
        $plain_amount_formatted = "";
    }
    else{
        $plain_amount_formatted = format_price( $plain_amounts, $precision, $symbol );
        $plain_amount_formatted =~ s/,//g;
    }
    return  $plain_amount_formatted;
}
