#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Term::ANSIColor qw( colored );
use Time::Format qw(%time %strftime %manip);
use POSIX qw/ceil/;

no warnings 'uninitialized';

# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name        = $ARGV[0];
my $single_store_flag  = $ARGV[1];
my $custom_branch_name = $ARGV[2];
my $is_porsche_store   = $ARGV[3];
my $proxy_brand        = $ARGV[4];

# Connect to postgresql database using psql service
my $conn =
  DBI->connect( "dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 } );

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;

my @header_section;
my @header_subsection;
my @job_section;
my @grand_total_section;
my @end_of_invoice_section;
my @invoice_note_section;

my (
    $cust_lbr_cost,    $cust_lbr_sale,  $cust_prt_cost,
    $cust_prt_sale,    $cust_misc_cost, $cust_misc_sale,
    $cust_gog_cost,    $cust_gog_sale,  $cust_sublet_cost,
    $cust_sublet_sale, $cust_ded_cost,  $cust_ded_sale
) = (0) x 12;

my (
    $intern_lbr_cost,    $intern_lbr_sale,  $intern_prt_cost,
    $intern_prt_sale,    $intern_misc_cost, $intern_misc_sale,
    $intern_gog_cost,    $intern_gog_sale,  $intern_sublet_cost,
    $intern_sublet_sale, $intern_ded_cost,  $intern_ded_sale
) = (0) x 12;

my (
    $warr_lbr_cost,    $warr_lbr_sale,  $warr_prt_cost,
    $warr_prt_sale,    $warr_misc_cost, $warr_misc_sale,
    $warr_gog_cost,    $warr_gog_sale,  $warr_sublet_cost,
    $warr_sublet_sale, $warr_ded_cost,  $warr_ded_sale
) = (0) x 12;

my (
    $sale_amount,     $labor_cost,      $parts_cost,   $parts_sale,
    $misc_job_cost,   $misc_job_sale,   $gog_job_cost, $gog_job_sale,
    $sublet_job_cost, $sublet_job_sale, $ded_job_cost, $ded_job_sale,
    $bill_time_tech
) = (0) x 13;

# my ($store_number) = (0) x 1;
# my ($ro_store_number) = (0) x 1;

my (
    $creation_date,        $completion_date, $department,
    $branch,               $sub_branch,      $advisor,
    $customer_number,      $customer_name,   $customer_address,
    $customer_city,        $customer_state,  $customer_zip,
    $customer_phone,       $other_phone,     $work_phone,
    $customer_email,       $vehicle_make,    $vehicle_model,
    $vehicle_vin,          $vehicle_year,    $mileage_in,
    $vehicle_color,        $mileage_out,     $license_number,
    $tag_no,               $delivery_date,   $promised,
    $ready,                $payment_code,    $comments,
    $stock_no,             $ro_ext,          $ro_date,
    $accounting_make_code, $adv_no,          $carline,
    $tax_customer,         $tax_warranty,    $tax_internal, 
    $delivery_odm,         $engine_no,	 	 $ro_prefix
) = (0) x 36;

my (
    $ro_part_line,     $part_number,      $part_kit,
    $part_description, $prt_billing_code, $prt_billing_type, 
    $quantity_sold,    $unit_cost,        $unit_sale,        
    $unit_core_cost,   $unit_core_sale,   $prt_count
) = (0) x 11;

my $job_hours = 0;
my ( $cust_ro_tax, $warr_ro_tax, $intern_ro_tax ) = (0) x 3;

my (
    $parts_entries,    $misc_entries,           $tax_entries,
    $gog_entries,      $sublet_entries,         $ded_entries,
    $comments_entries, $recommendation_entries, $chg_misc_entries,
    $chg_misc_total_entries, $estimate_entries
) = (0) x 10;
my $lbr_type;
my $page_max_height    = 58;    # Maximum no of lines in page body
my $page_header_height = 8;     # No of lines in header section
my $invoice_dealer_header = 1;    # No of lines in the dealer header section (1st page)
my $total_header_height;
my $extra_tech_line    = 0;
my $curr_page_height   = 0;
my $invoice_note_lines = 4;    # No of lines needed for invoice note
my $pages;
my $curr_page_num;
my $invoice_datetime;
my $cust_inv_head;

#Varibles for warranty
my $total_warranty_job_labor_cost  = 0.00;
my $total_warranty_job_labor_sale  = 0.00;
my $total_warranty_job_parts_cost  = 0.00;
my $total_warranty_job_parts_sale  = 0.00;
my $total_warranty_job_gog_cost    = 0.00;
my $total_warranty_job_gog_sale    = 0.00;
my $total_warranty_job_misc_cost   = 0.00;
my $total_warranty_job_misc_sale   = 0.00;
my $total_warranty_job_misc_disc_cost   = 0.00;
my $total_warranty_job_misc_disc_sale   = 0.00;
my $total_warranty_job_sublet_cost = 0.00;
my $total_warranty_job_sublet_sale = 0.00;
my $total_warranty_job_sale        = 0.00;
my $total_warranty_job_cost        = 0.00;
my $total_warranty_job_total       = 0.00;

my $grand_warranty_labor_cost  = 0.00;
my $grand_warranty_labor_sale  = 0.00;
my $grand_warranty_parts_cost  = 0.00;
my $grand_warranty_parts_sale  = 0.00;
my $grand_warranty_gog_cost    = 0.00;
my $grand_warranty_gog_sale    = 0.00;
my $grand_warranty_misc_cost   = 0.00;
my $grand_warranty_misc_sale   = 0.00;
my $grand_warranty_misc_disc_cost   = 0.00;
my $grand_warranty_misc_disc_sale   = 0.00;
my $grand_warranty_sublet_cost = 0.00;
my $grand_warranty_sublet_sale = 0.00;
my $grand_warranty_cost        = 0.00;
my $grand_warranty_sale        = 0.00;
my $total_joba_cost        = 0.00;
my $total_joba_sale        = 0.00;

#Varibles for internal
my $total_internal_job_labor_cost  = 0.00;
my $total_internal_job_labor_sale  = 0.00;
my $total_internal_job_parts_cost  = 0.00;
my $total_internal_job_parts_sale  = 0.00;
my $total_internal_job_gog_cost    = 0.00;
my $total_internal_job_gog_sale    = 0.00;
my $total_internal_job_misc_cost   = 0.00;
my $total_internal_job_misc_sale   = 0.00;
my $total_internal_job_misc_disc_cost   = 0.00;
my $total_internal_job_misc_disc_sale   = 0.00;
my $total_internal_job_sublet_cost = 0.00;
my $total_internal_job_sublet_sale = 0.00;
my $total_internal_job_sale        = 0.00;
my $total_internal_job_cost        = 0.00;
my $total_internal_job_total       = 0.00;

my $grand_internal_labor_cost  = 0.00;
my $grand_internal_labor_sale  = 0.00;
my $grand_internal_parts_cost  = 0.00;
my $grand_internal_parts_sale  = 0.00;
my $grand_internal_gog_cost    = 0.00;
my $grand_internal_gog_sale    = 0.00;
my $grand_internal_misc_cost   = 0.00;
my $grand_internal_misc_sale   = 0.00;
my $grand_internal_misc_disc_cost   = 0.00;
my $grand_internal_misc_disc_sale   = 0.00;
my $grand_internal_sublet_cost = 0.00;
my $grand_internal_sublet_sale = 0.00;
my $grand_internal_cost        = 0.00;
my $grand_internal_sale        = 0.00;

#Varibles  for customer
my $total_customer_job_labor_cost  = 0.00;
my $total_customer_job_labor_sale  = 0.00;
my $total_customer_job_parts_cost  = 0.00;
my $total_customer_job_parts_sale  = 0.00;
my $total_customer_job_gog_cost    = 0.00;
my $total_customer_job_gog_sale    = 0.00;
my $total_customer_job_misc_cost   = 0.00;
my $total_customer_job_misc_sale   = 0.00;
my $total_customer_job_misc_disc_cost   = 0.00;
my $total_customer_job_misc_disc_sale   = 0.00;
my $total_customer_job_sublet_cost = 0.00;
my $total_customer_job_sublet_sale = 0.00;
my $total_customer_job_sale        = 0.00;
my $total_customer_job_cost        = 0.00;
my $total_customer_job_total       = 0.00;

my $grand_customer_labor_cost  = 0.00;
my $grand_customer_labor_sale  = 0.00;
my $grand_customer_parts_cost  = 0.00;
my $grand_customer_parts_sale  = 0.00;
my $grand_customer_gog_cost    = 0.00;
my $grand_customer_gog_sale    = 0.00;
my $grand_customer_misc_cost   = 0.00;
my $grand_customer_misc_sale   = 0.00;
my $grand_customer_misc_disc_cost   = 0.00;
my $grand_customer_misc_disc_sale   = 0.00;
my $grand_customer_sublet_cost = 0.00;
my $grand_customer_sublet_sale = 0.00;
my $grand_customer_cost        = 0.00;
my $grand_customer_sale        = 0.00;

my $total_job_parts_cost = 0.00;
my $total_job_parts_sale = 0.00;

my $total_gog_cost = 0.00;
my $total_gog_sale = 0.00;

my $total_sublet_cost = 0.00;
my $total_sublet_sale = 0.00;

my $total_misc_cost = 0.00;
my $total_misc_sale = 0.00;

my $customer_total_tax = 0;
my $warranty_total_tax = 0;
my $internal_total_tax = 0;

my $positive_warranty_misc_sale = 0.00;
my $positive_warranty_misc_cost = 0.00;
my $positive_customer_misc_sale = 0.00;
my $positive_customer_misc_cost = 0.00;
my $positive_internal_misc_sale = 0.00;
my $positive_internal_misc_cost = 0.00;
my $positive_total_misc_sale    = 0.00;
my $positive_total_misc_cost    = 0.00;


# my $ro_qry = $conn->prepare(
#     "SELECT ro_number
#                                FROM repair_order WHERE completion_date IS NOT NULL
#                               ORDER BY ro_number"
# );
my $store_number = '';
my $store_number_query = $conn->prepare(
        "SELECT
                area_number::text||'S'
            FROM repair_dealer_details LIMIT 1"
    );

    $store_number_query->execute();
    while (my @store_number_row = $store_number_query->fetchrow_array)
    {
        ($store_number) = @store_number_row;
    }



# my $ro_open_void_qry = $conn->prepare(
#     "WITH series_gap (max_ser_ro, min_ser_ro) AS (
#          SELECT * FROM (SELECT
#                             ro_number::integer,
#                             lag(ro_number::integer) OVER ranges,
#                             ro_number::integer - lag(ro_number::integer) OVER ranges AS diff
#                     FROM repair_order
#                     WHERE ro_number ~ '^\\d+\$'
#                     WINDOW ranges AS (order by ro_number::integer))t
#          WHERE diff < 2)
#      SELECT LPAD(ro_number::text, (SELECT LENGTH(MAX(ro_number::numeric)::text) FROM repair_order WHERE ro_number ~ '^\\d+\$'), '0')
#      FROM repair_order
#      WHERE completion_date IS NULL
#      UNION ALL
#      SELECT LPAD(generate_series(min_ser_ro+1, max_ser_ro-1)::text, (SELECT LENGTH(MAX(ro_number::numeric)::text) FROM repair_order WHERE ro_number ~ '^\\d+\$'),'0')
#      FROM series_gap");

# $ro_open_void_qry->execute();
# while (my @open_ro_list = $ro_open_void_qry->fetchrow_array) {
#     ($ro_number) = @open_ro_list;
#     my $file_open = $ro_number . ".txt";
#     unless(open FILE, '>'.$file_open) {
#         die "\nUnable to create $file_open\n";
#     }
#     print_open_ro();
#     close FILE;
# }

my $ro_doc_truncate = $conn->prepare("TRUNCATE TABLE repair_order_print")
    or die "Prepare failed: " . $conn->errstr;

$ro_doc_truncate->execute()
    or die "Execution failed: " . $ro_doc_truncate->errstr;
    
my $ro_open_void_omitted_qry = $conn->prepare("SELECT store, LPAD(ro_num::text, (SELECT LENGTH(MAX(ro_num::numeric)::text) FROM inv_sequence_master WHERE ro_num ~ '^\\d+\$'), '0') ,inv_type FROM inv_sequence_master ORDER BY ro_num::numeric ASC");
$ro_open_void_omitted_qry->execute();
my ($store_seq, $ro_no_seq, $inv_type);
my $ronum_file='';
my $ro_no_seq_range;
my $check_range;
while (my @open_ro_omitted_list = $ro_open_void_omitted_qry->fetchrow_array) {
    ($store_seq, $ro_no_seq, $inv_type) = @open_ro_omitted_list;

    $ro_no_seq_range = substr($ro_no_seq, -2);
    $check_range = $ro_no_seq_range/99;
     if ($inv_type eq "O") {

        if($ronum_file eq '') {
            $ronum_file=$ro_no_seq;
            my $file_open = $ronum_file. ".txt";
                    unless(open FILE, '>'.$file_open) {
                        die "\nUnable to create $file_open\n";
                    }

        }
        printf( FILE border("#") . " %-100s " . border("|") . "\n",
                      "Omitted Invoice # " . $ro_no_seq ." belongs to other Store " . $store_seq ."\n"
            );
            
        if ($ro_no_seq_range eq 99 || $ro_no_seq_range eq 98){
            $ronum_file='';
            close FILE;
        }
     }
    elsif ($inv_type eq "V") {

        if(!$ronum_file) {
            $ronum_file=$ro_no_seq;
            my $file_open = $ronum_file. ".txt";
                    unless(open FILE, '>'.$file_open) {
                        die "\nUnable to create $file_open\n";
                    }

        }
        printf( FILE border("#") . " %-100s " . border("|") . "\n",
                      "Invoice Source Not Present: RO# " . $ro_no_seq ." * See Open and Void RO Report *\n"
                );

        if ($ro_no_seq_range eq 99 || $ro_no_seq_range eq 98){
            $ronum_file='';
            close FILE;
        }

     }  elsif ($inv_type eq "C") {

        $ronum_file='';
        close FILE;
      }				
}

# my $ro_open_void_series_qry = $conn->prepare(
#     "SELECT * FROM (SELECT
# 						    lag(ro_number::integer) OVER ranges AS start_ro,
#                             ro_number::integer AS end_ro,
#                             ro_number::integer - lag(ro_number::integer) OVER ranges AS diff,
#                             (SELECT LENGTH(MAX(ro_number::numeric)::text) FROM repair_order WHERE ro_number ~ '^\\d+\$') AS maxlen
#                     FROM repair_order
#                     WHERE ro_number ~ '^\\d+\$'
#                     WINDOW ranges AS (order by ro_number::integer))t
#          WHERE diff > 1");

# $ro_open_void_series_qry->execute();
# my ($start_ro, $end_ro, $diff, $maxlen);
# while (my @open_ro_series_list = $ro_open_void_series_qry->fetchrow_array) {
#     ($start_ro, $end_ro, $diff, $maxlen) = @open_ro_series_list;
#     my $series_start = $start_ro+1;
#     my $series_end = $end_ro-1;
#     my $bundle_series_start = int($series_start/2);
#     my $bundle_series_end = int($series_end/2);
#     my @a = ($bundle_series_start..$bundle_series_end);
#     # my $i;
#     # for $i (@a){
#     #     my $suffixStart = "00";
#     #     my $suffixEnd = "99";
#     #     my $tempStart = $i.$suffixStart+0;
#     #     my $tempEnd = $i.$suffixEnd+0;
#     #     my $start;
#     #     my $end;
#     #     if($tempStart >= $series_start && $tempStart <= $series_end){
#     #       $start = $tempStart;
#     #     } else{
#     #       $start = $series_start;
#     #     }
#     #     if($tempEnd <= $series_end && $tempEnd >= $series_start){
#     #       $end = $tempEnd;
#     #     } else{
#     #       $end = $series_end;
#     #     }
#     #     my $ro_range = $start."-".$end;
#     #     my $file_open = sprintf("%0".$maxlen."d", $start) . ".txt";
#     #     unless(open FILE, '>'.$file_open) {
#     #         die "\nUnable to create $file_open\n";
#     #     }
#     #     #print_open_ro_series($ro_range);
#     #      print "\n";
#     #         print $start .'---Start----'.$end;
#     #     print_open_ro_series($start, $end);
#     #     close FILE;
#     # }

#         my $ro_range = $series_start."-".$series_end;
#         my $file_open = sprintf("%0".$maxlen."d", $series_start) . ".txt";
#         unless(open FILE, '>'.$file_open) {
#             die "\nUnable to create $file_open\n";
#         }
#         print_open_ro_series($series_start, $series_end);
#         close FILE;
# }

my $ro_qry = $conn->prepare("SELECT ro_number, LPAD(ro_number::text, (SELECT LENGTH(MAX(ro_number::numeric)::text) FROM repair_order WHERE ro_number ~ '^\\d+\$'),'0') 
                               FROM repair_order WHERE completion_date IS NOT NULL
                              ORDER BY ro_number");

$ro_qry->execute();
my $file_name;
while ( my @ro_list = $ro_qry->fetchrow_array ) {
    ($ro_number, $file_name) = @ro_list;

    my $temp_ro_number = $file_name;
    #$temp_ro_number =~ s/^0+//;
    my $file = $temp_ro_number . ".txt";
    unless ( open FILE, '>' . $file ) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;
    my $file_inv_head = "inv-bg/" . $temp_ro_number . "-inv-bg.txt";
    unless ( open FILE, '>' . $file_inv_head ) {
        die "\nUnable to create $file_inv_head\n";
    }
    print_ro_inv_bg_head();
    close FILE;

    load_file_into_database( $ro_number, $file );
}

$conn->disconnect;

sub load_file_into_database {
    my ( $ro_number, $ro_document_file ) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;

#   my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
#   $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
#    $ro_doc_insert->finish;
}

# Subroutine to prepare HEADER section of the invoice
sub make_header {
    my $ro_header_qry = $conn->prepare(
        "SELECT
                                to_char(ro.open_time, 'HH24:MI') || ' ' || to_char(ro.creation_date, 'ddMONyy') AS creation_date,
                                to_char(ro.completion_date, 'mm/dd/yy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                               -- regexp_replace(ro.advisor,' (.*)','') as advisor_name,
                                ro.advisor,
                                roc.customer_number,
                                trim(roc.customer_name) AS customer_name,
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                CASE
                                    WHEN length(roc.customer_zip) > 7 THEN SUBSTRING(roc.customer_zip, 1, 5) || '-' || SUBSTRING(roc.customer_zip, 6,4)
                                    ELSE roc.customer_zip
                                END AS customer_zip,
                                roc.customer_phone,
				                roc.other_phone,
			                    roc.work_phone,
                                coalesce(roc.customer_email, ''),
                                rov.vehicle_make,
                                rov.vehicle_model,
                                --rov.vehicle_vin,
				                --regexp_replace(rov.vehicle_vin, E'(.)(?!$)', E'\\1 ', 'g') as vehicle_vin,
				                trim(rov.vehicle_vin) AS vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in,
                                rov.vehicle_color,
                                rov.mileage_out,
                                roc.license_number,
                                ro.tag_no,
                                to_char(ro.delivery_date, 'mm/dd/yyyy') AS delivery_date,
                                to_char(ro.promised_time, 'HH24:MI') || ' ' || to_char(ro.promised_date, 'ddMONyy') AS promised,
                                to_char(ro.booked_time, 'HH24:MI') || ' ' || to_char(ro.booked_date, 'ddMONyy') AS ready,
                                ro.payment_code,
                                ro.comments,
                                ro.stock_no,
				                ro_ext,
                                to_char(ro.creation_date, 'mm/dd/yy') AS ro_date,
                                COALESCE(NULLIF(ro.accounting_make_code,''), ro_prefix),
                                ro.advisor_number,
				                rov.carline,
								coalesce(ro.tax_customer::numeric, 0.00),
								coalesce(ro.tax_warranty::numeric, 0.00),
								coalesce(ro.tax_internal::numeric, 0.00),
                                delivery_odm,
                                CASE
                                    WHEN NULLIF(engine_no,'') != ''
                                    THEN 'E# ' || engine_no
                                    ELSE ''
                                END AS engine_no,
                                ro_prefix
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?"
    );

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
    (
        $creation_date,        $completion_date, $department,
        $branch,               $sub_branch,      $advisor,
        $customer_number,      $customer_name,   $customer_address,
        $customer_city,        $customer_state,  $customer_zip,
        $customer_phone,       $other_phone,     $work_phone,
        $customer_email,       $vehicle_make,    $vehicle_model,
        $vehicle_vin,          $vehicle_year,    $mileage_in,
        $vehicle_color,        $mileage_out,     $license_number,
        $tag_no,               $delivery_date,   $promised,
        $ready,                $payment_code,    $comments,
        $stock_no,             $ro_ext,          $ro_date,
        $accounting_make_code, $adv_no,          $carline,
        $tax_customer,         $tax_warranty,    $tax_internal, 
        $delivery_odm,         $engine_no,		 $ro_prefix
    ) = @header_row;

    if($is_porsche_store eq 'true'){
        $vehicle_vin=substr($vehicle_vin, 0, 12);
        $work_phone = '';
        $other_phone = '';
        $customer_phone = '';
        $customer_number = '';
        $customer_name ='';
        $customer_address = '';
        $customer_city = '';
        $customer_zip = '';
        $customer_state = '';
        $customer_email = '';
    }
    # if ( $single_store_flag eq 'true' ) {
    #     $accounting_make_code = $custom_branch_name;
    # }

    $cust_inv_head = $ro_ext;
    $vehicle_vin =~ s/.\K(?=.)/ /sg;

    


    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}'
              . "%-1s%-38s%-20s%-25s%-6s%-10s"
              . '~font{fCourier-Bold-Bold7}' . "%17s"
              . '~font{default}'
              . '~color{0 0 0}'
              . border("|") . "\n",
            "", $customer_name, $advisor, $adv_no, $tag_no, $completion_date,
            $accounting_make_code . "+" . $department . $ro_number
        )
    );
    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}' . "%-1s%-78s%-8s%-15s%-6s"
              . '~font{default}'
              . border("|") . "\n",
            "", $customer_email, "", "", ""
        )
    );
    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}' . "%-1s%-5s%-20s%-6s%73s%10s"
              . '~font{default}'
              . border("|") . "\n",
            "", "MAIL ", substr( $customer_phone, 0, 20 ), " CELL ", "", $stock_no
        )
    );
    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}'
              . "%-1s%-59s%-15s%-14s%-26s"
              . '~font{default}'
              . border("|") . "\n",
            "", substr( $customer_address, 0, 39 ), substr( $mileage_in,    0, 15 ), substr( $vehicle_color, 0, 14 ), ""          
        )
    );
    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}' . "%-1s%-5s%-20s%-5s"
              . '~font{default}'
              . border("|") . "\n",
            "", "CALL ", "", " FAX "
        )
    );
    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}' . "%-1s%-6s%-19s%-9s"
              . '~font{default}'
              . border("|") . "\n",
            "", "EMAIL ", "", " BUS.EXT "
        )
    );
    my $vehicle_details = substr(substr( $vehicle_year, -2 ) . "/" . $vehicle_make . "/" . $carline . "/" . $vehicle_model, 0, 40);
    $vehicle_details =~ s/\/\//\//g;          

    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}'
              . "%-1s%-38s%-36s%-10s%-20s%10s"
              . '~font{default}'
              . border("|") . "\n",
            "", "",
            $vehicle_details,
            $delivery_date,
            "",
            $delivery_odm
        )
    );

    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}' . "%-1s%-9s"
              . '~font{default}'
              . border("|") . "\n",
            "", "OPTOUT"
        )
    );
    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}'
              . "%-1s%-38s%-44s%-14s%-10s%-8s"
              . '~font{default}'
              . border("|") . "\n",
            "",
            substr(
                $customer_city . "," . $customer_state . " " . $customer_zip,
                0, 38
            ),
            substr( $vehicle_vin, 0, 44 ),
            substr( '',           0, 14 ),
            substr( '',           0, 15 ),""
        )
    );

    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}'
              . "%-1s%-8s%-70s %-4s %-10s%20s"
              . '~font{default}'
              . border("|") . "\n",
            "",
            $customer_number,
            substr( $customer_phone, 0, 38 ) . " "
              . substr( $work_phone,  0, 38 ) . " "
              . substr( $other_phone, 0, 38 ),
            substr( $license_number, 0, 15 ),
            $ro_date,""
        )
    );
    push(
        @header_section,
        sprintf(
            border(">")
              . '~font{fCourier-Bold7}'
              . "%22s%-17s%-20s%36s%20s "
              . '~font{default}'
              . border("|") . "\n",
            "", $engine_no,"","", "MO: " . substr( $mileage_out, 0, 16 )
        )
    );
}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {

    #Varibles for warranty
    $total_warranty_job_labor_cost  = 0.00;
    $total_warranty_job_labor_sale  = 0.00;
    $total_warranty_job_parts_cost  = 0.00;
    $total_warranty_job_parts_sale  = 0.00;
    $total_warranty_job_gog_cost    = 0.00;
    $total_warranty_job_gog_sale    = 0.00;
    $total_warranty_job_misc_cost   = 0.00;
    $total_warranty_job_misc_sale   = 0.00;
    $total_warranty_job_misc_disc_cost   = 0.00;
    $total_warranty_job_misc_disc_sale   = 0.00;
    $total_warranty_job_sublet_cost = 0.00;
    $total_warranty_job_sublet_sale = 0.00;
    $total_warranty_job_total       = 0.00;

    #Varibles for internal
    $total_internal_job_labor_cost  = 0.00;
    $total_internal_job_labor_sale  = 0.00;
    $total_internal_job_parts_cost  = 0.00;
    $total_internal_job_parts_sale  = 0.00;
    $total_internal_job_gog_cost    = 0.00;
    $total_internal_job_gog_sale    = 0.00;
    $total_internal_job_misc_cost   = 0.00;
    $total_internal_job_misc_sale   = 0.00;
    $total_internal_job_misc_disc_cost   = 0.00;
    $total_internal_job_misc_disc_sale   = 0.00;
    $total_internal_job_sublet_cost = 0.00;
    $total_internal_job_sublet_sale = 0.00;
    $total_internal_job_total       = 0.00;

    #Varibles for customer
    $total_customer_job_labor_cost  = 0.00;
    $total_customer_job_labor_sale  = 0.00;
    $total_customer_job_parts_cost  = 0.00;
    $total_customer_job_parts_sale  = 0.00;
    $total_customer_job_gog_cost    = 0.00;
    $total_customer_job_gog_sale    = 0.00;
    $total_customer_job_misc_cost   = 0.00;
    $total_customer_job_misc_sale   = 0.00;
    $total_customer_job_misc_disc_cost   = 0.00;
    $total_customer_job_misc_disc_sale   = 0.00;
    $total_customer_job_sublet_cost = 0.00;
    $total_customer_job_sublet_sale = 0.00;
    $total_customer_job_total       = 0.00;

    $total_job_parts_cost = 0.00;
    $total_job_parts_sale = 0.00;
    $total_gog_cost       = 0.00;
    $total_gog_sale       = 0.00;
    $total_sublet_cost    = 0.00;
    $total_sublet_sale    = 0.00;
    $total_misc_cost      = 0.00;
    $total_misc_sale      = 0.00;

    my $customer_total_misc_chg_sale = 0;
    my $customer_total_misc_chg_cost = 0;
    my $customer_total_misc_dis_sale = 0;
    my $customer_total_misc_dis_cost = 0;
    my $warranty_total_misc_chg_sale = 0;
    my $warranty_total_misc_chg_cost = 0;
    my $warranty_total_misc_dis_cost = 0;
    my $warranty_total_misc_dis_sale = 0;
    my $internal_total_misc_chg_sale = 0;
    my $internal_total_misc_chg_cost = 0;
    my $internal_total_misc_dis_cost = 0;
    my $internal_total_misc_dis_sale = 0;

    my $job_qry = $conn->prepare(
        "SELECT
                                    rl.ro_line::numeric,
                                    rl.complaint,
                                    rj.ro_job_line,
                                    left(rj.billing_code, 1) AS billing_code,
                                    rj.billing_code,
                                    rj.billing_code          AS billing_labor_type,
                                    case when up_sell_flag='Y'
	    			    then  '+'||rj.op_code
					else rj.op_code end op_code_job,
                                    rj.op_code,
                                    rj.op_description,
                                    rj.sold_hours,
                                    coalesce(rj.labor_cost::numeric, 0.00),
                                    coalesce(rj.sale_amount::numeric, 0.00),
                                    rj.cause,
                                    rj.correction,
                                    rj.origcause,
                                    rj.origcorrection,
                                    rj.parts_cost,
                                    case when left( rj.billing_code  ,1)='C' then 'CUSTOMER' when left( rj.billing_code  ,1)='W' then 'WARRANTY' when left( rj.billing_code  ,1)='I' then 'INTERNAL' 
                                    	when left( rj.billing_code  ,1)='P' then t.prt_billing_type end as billing_type,
				    rj.up_sell_flag,
                                    rj.billtime_tech,
                                    rj.split_cost,
                                    rj.split_total
                                FROM repair_line rl
                                    JOIN repair_job rj USING (ro_number, ro_line)
				    LEFT JOIN (select ro_number, ro_line,prt_billing_type from du_dms_reynoldsrci_proxy.repair_part  WHERE ro_part_line = 1 group by 1,2,3) t using(ro_number, ro_line)
                                WHERE rl.ro_number = ? ORDER BY ro_line :: numeric"

     );

    $job_qry->execute($ro_number);
    my (
        $ro_line,      $complaint,          $ro_job_line,
        $billing_code, $billing_code_all,   $billing_labor_type, 
        $op_code_job,   $op_code,           $op_description,     
        $sold_hours,
        $cause,        $correction,   $origcause,  $origcorr,   $parts_cost,
        $billing_type, $up_sell_flag,       $billtime_tech,
        $split_cost,   $split_total
    );

    while ( my @job_row = $job_qry->fetchrow_array ) {
        (
            $ro_line,      $complaint,          $ro_job_line,
            $billing_code, $billing_code_all,   $billing_labor_type, $op_code_job,
            $op_code,      $op_description,     $sold_hours,
            $labor_cost,   $sale_amount,        $cause,
            $correction,   $origcause,  $origcorr, $parts_cost,         $billing_type,
            $up_sell_flag, $billtime_tech,      $split_cost,   
            $split_total
        ) = @job_row;

        $total_warranty_job_labor_cost  = 0.00;
        $total_warranty_job_labor_sale  = 0.00;
        $total_warranty_job_parts_cost  = 0.00;
        $total_warranty_job_parts_sale  = 0.00;
        $total_warranty_job_gog_cost    = 0.00;
        $total_warranty_job_gog_sale    = 0.00;
        $total_warranty_job_sublet_cost = 0.00;
        $total_warranty_job_sublet_sale = 0.00;
        $total_warranty_job_misc_cost   = 0.00;
        $total_warranty_job_misc_sale   = 0.00;
        $total_warranty_job_misc_disc_cost   = 0.00;
        $total_warranty_job_misc_disc_sale   = 0.00;
        $total_warranty_job_sale        = 0.00;
        $total_warranty_job_cost        = 0.00;

        $total_customer_job_labor_cost  = 0.00;
        $total_customer_job_labor_sale  = 0.00;
        $total_customer_job_parts_cost  = 0.00;
        $total_customer_job_parts_sale  = 0.00;
        $total_customer_job_gog_cost    = 0.00;
        $total_customer_job_gog_sale    = 0.00;
        $total_customer_job_sublet_cost = 0.00;
        $total_customer_job_sublet_sale = 0.00;
        $total_customer_job_misc_cost   = 0.00;
        $total_customer_job_misc_sale   = 0.00;
        $total_customer_job_misc_disc_cost   = 0.00;
        $total_customer_job_misc_disc_sale   = 0.00;
        $total_customer_job_sale        = 0.00;
        $total_customer_job_cost        = 0.00;

        $total_internal_job_labor_cost  = 0.00;
        $total_internal_job_labor_sale  = 0.00;
        $total_internal_job_parts_cost  = 0.00;
        $total_internal_job_parts_sale  = 0.00;
        $total_internal_job_gog_cost    = 0.00;
        $total_internal_job_gog_sale    = 0.00;
        $total_internal_job_sublet_cost = 0.00;
        $total_internal_job_sublet_sale = 0.00;
        $total_internal_job_misc_cost   = 0.00;
        $total_internal_job_misc_sale   = 0.00;
        $total_internal_job_misc_disc_cost   = 0.00;
        $total_internal_job_misc_disc_sale   = 0.00;
        $total_internal_job_sale        = 0.00;
        $total_internal_job_cost        = 0.00;

        $total_job_parts_cost = 0.00;
        $total_job_parts_sale = 0.00;
        $total_gog_sale       = 0.00; 
        $total_sublet_sale    = 0.00;
        $total_misc_sale      = 0.00;

        if ( $billing_type eq "WARRANTY" or $billing_type eq "Warr" ) {
            $total_warranty_job_labor_cost += $labor_cost;
            $total_warranty_job_labor_sale += $sale_amount;
        }
        elsif ( $billing_type eq "CUSTOMER" or $billing_type eq "Cust" ) {
            $total_customer_job_labor_cost += $labor_cost;
            $total_customer_job_labor_sale += $sale_amount;
        }
        elsif ( $billing_type eq "INTERNAL" or $billing_type eq "Intr" ) {
            $total_internal_job_labor_cost += $labor_cost;
            $total_internal_job_labor_sale += $sale_amount;
        }

        $bill_time_tech = $billtime_tech;
        my @job_header_section;
        my $techs_id_qry = $conn->prepare(
            "SELECT DISTINCT
                            tech_id
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?"
        );
        $techs_id_qry->execute( $ro_number, $ro_line, $ro_job_line );
        my ( $tech_id, $job_tech_id );
        if ( $techs_id_qry->rows > 0 ) {
            while ( my @job_id_tech_row = $techs_id_qry->fetchrow_array ) {
                ($tech_id) = @job_id_tech_row;
                $job_tech_id = $job_tech_id . " " . $tech_id;
            }
        }

        my $techs_tot_hrs = $conn->prepare(
            "SELECT SUM(booked_hours::numeric) AS tot_tech_hrs
                        FROM du_dms_reynoldsrci_proxy.repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?
                        GROUP BY ro_number, ro_line, ro_job_line"
        );
        $techs_tot_hrs->execute( $ro_number, $ro_line, $ro_job_line );
        my ( $tot_tech_time );
        if ( $techs_tot_hrs->rows > 0 ) {
            while ( my @tech_hrs_row = $techs_tot_hrs->fetchrow_array ) {
                ($tot_tech_time) = @tech_hrs_row;
            }
        }

        $Text::Wrap::columns = 34;
        my $wrapped_job_tech_id = fill( '', '', expand($job_tech_id) );
        my @job_tech_id_list = split "\n", $wrapped_job_tech_id;

        my $techs_qry = $conn->prepare(
            "SELECT
                            tech_id,
                            actual_hours,
                            booked_hours,
			                to_char(job_work_date::date, 'mm/dd/yy') AS work_date,
                            EXTRACT(HOUR FROM start_time::interval)::numeric +  ROUND((EXTRACT(MINUTE FROM start_time::interval) / 60)::numeric ,2) AS start_time,
							EXTRACT(HOUR FROM finish_time::interval)::numeric +  ROUND((EXTRACT(MINUTE FROM finish_time::interval) / 60)::numeric ,2) AS finish_time,
                            --booked_hours-actual_hours as time_taken,
                            booked_hours as time_taken,
                            work_note
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?
                        ORDER BY  job_work_date::date , ro_job_tech_line, start_time::time"
        );
        $techs_qry->execute( $ro_number, $ro_line, $ro_job_line );

        # my @job_tech_row = $techs_qry->fetchrow_array;
        my (
            $job_tech,   $act_time, $booked_time, $work_date,
            $start_time, $end_time, $time_taken,  $work_note
        );
        push(
            @job_header_section,
            sprintf(
                border(">") . "%-3s%-15s%-50s" . border("|") . "\n",
                "", "JOB# " . $ro_line . " CHARGES",
                "-" x 81
            )
        );

        push( @job_header_section,
            sprintf( border(">") . "%-16s" . border("|") . "\n", "" ) );
        if ( $billing_type eq "I" or $billing_type eq "Intr" ) {

            $billing_type = "INTERNAL";
        }
        elsif ( $billing_type eq "C" or $billing_type eq "Cust" ) {

            $billing_type = "CUSTOMER";
        }
        elsif ( $billing_type eq "W" or $billing_type eq "Warr" ) {

            $billing_type = "WARRANTY";
        }
        push(
            @job_header_section,
            sprintf(
                border(">") . "%3s%-17s%-75s" . border("|") . "\n",
                "", "LABOR----" . $billing_type,
                "-" x 70
            )
        );
        my $hourstoprint = $sold_hours ? $sold_hours : '';
        if ( $bill_time_tech > 0 ) {
            push(
                @job_header_section,
                sprintf(
                    border(">")
                      . "%3s%-7s%-15s%-25s%-1s%-12s%-1s%-27s%10.2f"
                      . border("|") . "\n",
                    "",
                    "J# " . $ro_line,
                    $op_code_job,
                    substr( $op_description, 0, 29 ),
                    "",
                    "HOURS: " . $hourstoprint,
                    "",
                    "TECH(S):" . substr( $job_tech_id_list[0], 0, 19),
                    $sale_amount
                )
            );
        }
        else {
            push(
                @job_header_section,
                sprintf(
                    border(">")
                      . "%3s%-7s%-15s%-25s%-1s%-12s%-1s%-27s%10.2f"
                      . border("|") . "\n",
                    "",
                    "J# " . $ro_line,
                    $op_code_job,
                    substr( $op_description, 0, 29 ),
                    "",
                    "HOURS: " . $hourstoprint,
                    "",
                    "TECH(S):" . substr( $job_tech_id_list[0], 0, 19),
                    $sale_amount
                )
            );
        }
        my @billing_code_list = split ",", $billing_code_all;
        my $paytype_count = scalar(@billing_code_list);
        if ( $paytype_count > 1 ){
            my @split_amount_list = split ",", $split_cost;
            my @split_total_list  = split ",", $split_total;
            my $billing_val;
            if ( $billing_code_list[0] eq "I" or $billing_code_list[0] eq "Intr" ) {

                $billing_code_list[0] = "INTERNAL";
            }
            elsif ( $billing_code_list[0] eq "C" or $billing_code_list[0] eq "Cust" ) {

                $billing_code_list[0] = "CUSTOMER";
            }
            elsif ( $billing_code_list[0] eq "W" or $billing_code_list[0] eq "Warr" ) {

                $billing_code_list[0] = "WARRANTY";
            }
            if ( $billing_code_list[1] eq "I" or $billing_code_list[1] eq "Intr" ) {

                $billing_code_list[1] = "INTERNAL";
                $billing_val          = "INTL";
                $total_internal_job_labor_cost += $split_amount_list[1];
                $total_internal_job_labor_sale += $split_total_list[1];
            }
            elsif ( $billing_code_list[1] eq "C" or $billing_code_list[1] eq "Cust" ) {

                $billing_code_list[1] = "CUSTOMER";
                $billing_val          = "CUST";
                $total_customer_job_labor_cost += $split_amount_list[1];
                $total_customer_job_labor_sale += $split_total_list[1];
            }
            elsif ( $billing_code_list[1] eq "W" or $billing_code_list[1] eq "Warr" ) {

                $billing_code_list[1] = "WARRANTY";
                $billing_val          = "WARR";
                $total_warranty_job_labor_cost += $split_amount_list[1];
                $total_warranty_job_labor_sale += $split_total_list[1];
            }

            push(
                @job_header_section,
                sprintf(
                    border(">") . "%-7s%-20s%-9s%-9s%-17s%-9s%-10s%20.2f" . border("|") . "\n",
                    "", $billing_val,"SPLIT",$billing_code_list[0],"=       %",$billing_code_list[1],"=       %",$split_total_list[1]
                )
            );
        }
        if ( $up_sell_flag eq "Y" ) {
            push(
                @job_header_section,
                sprintf(
                    border(">") . "%4s%-9s" . border("|") . "\n",
                    "",
                    "Added Operation  ("
                      . $advisor . " @ "
                      . $completion_date . ")"
                )
            );
        }
        if ( $techs_qry->rows > 0 ) {
            push(
                @job_header_section,
                sprintf(
                    border(">")
                      . "%10s%10s%12s%10s%10s%8s%8s%17s"
                      . border("|") . "\n",
                    "", "TECH#", "DATE","START","FINISH", "ACT", "TIME", "DESCRIPTION"
                )
            );
            my $tot_act_time   = 0;
            my $tot_time_taken = 0;
            while ( my @job_tech_row = $techs_qry->fetchrow_array ) {

                # my @job_tech_row = $techs_qry->fetchrow_array;
                my $tech_lic = "";
                (
                    $job_tech,   $act_time, $booked_time, $work_date,
                    $start_time, $end_time, $time_taken,  $work_note
                ) = @job_tech_row;
                $job_hours = $sold_hours;
                push(
                    @job_header_section,
                    sprintf(
                        border(">")
                          . "%10s%10s%14s%8s%10s%8.2f%8.2f%17s"
                          . border("|") . "\n",
                        "", $job_tech, $work_date, $start_time,$end_time, $act_time, $time_taken, ""
                    )
                );
                $tot_act_time   = $tot_act_time + $act_time;
                $tot_time_taken = $tot_time_taken + $time_taken;
            }
            push(
                @job_header_section,
                sprintf(
                    border(">")
                      . "%52s%8.2f%8.2f"
                      . border("|") . "\n",
                    "TOTAL TECH TIME",
                    $tot_act_time, $tot_time_taken
                )
            );
        }

        my $spg_info_qry = $conn->prepare(
            "SELECT
            opcode,
            opcode_desc
            FROM du_dms_reynoldsrci_proxy.repair_spg_details WHERE ro_number = ? AND job_number = ?"
        );
        $spg_info_qry->execute( $ro_number, $ro_line );

        # if ( $spg_info_qry->rows > 0 ) {

        #     push( @job_header_section,
        #         sprintf( border(">") . "%-16s" . border("|") . "\n", "" ) );

            # push(
            #     @job_header_section,
            #     sprintf(
            #         border(">") . "%-10s %-10s %-2s" . border("|") . "\n",
            #         "", "*** Maintenance Minder Symbol 3 ***", ""
            #     )
            # );
            # push(
            #     @job_header_section,
            #     sprintf(
            #         border(">") . "%-12s %-10s %-2s" . border("|") . "\n",
            #         "", "Maintenance Minder Symbol 3", ""
            #     )
            # );

        #     while ( my @spg_info_row = $spg_info_qry->fetchrow_array ) {
        #         my ( $SPGOpCode, $SPGOpCodeDesc ) = @spg_info_row;

        #         push(
        #             @job_header_section,
        #             sprintf(
        #                 border(">")
        #                   . "%-6s%-14s %-32s %-2s"
        #                   . border("|") . "\n",
        #                 "", $SPGOpCode, $SPGOpCodeDesc, ""
        #             )
        #         );
        #     }

        # }

        push( @job_header_section,
            sprintf( border(">") . "%-16s" . border("|") . "\n", "" ) );

        # Job Complaint
        my $complaint_len = length($complaint);
        $Text::Wrap::columns = $complaint_len + 1;
        my $wrapped_complaint = fill( '', '', expand($complaint) );
        my @complaint_list = split "~", $wrapped_complaint;

        push(
            @job_header_section,
            sprintf(
                border(">") . "%-3s%-12s %-10s %-2s" . border("|") . "\n",
                "", "COMPLAINT", $complaint_list[0], ""
            )
        );
        foreach my $i ( 1 .. $#complaint_list ) {
            push(
                @job_header_section,
                sprintf(
                    border(">") . "%-15s %-10s %-2s" . border("|") . "\n",
                    "", $complaint_list[$i], ""
                )
            );
        }
        push( @job_section, [@job_header_section] );

        # Job OrigCause
        if ($origcause) {
            my @job_cause_section;
            my $origcause_len = length($origcause);
            $Text::Wrap::columns = $origcause_len + 1;
            my $wrapped_origcause = fill( '', '', expand($origcause) );
            my @origcause_list = split "~", $wrapped_origcause;
            push(
                @job_cause_section,
                sprintf(
                    border(">") . "%-3s%-12s %-10s %-2s" . border("|") . "\n",
                    "", "ORIG CAUSE", $origcause_list[0], ""
                )
            );
            foreach my $i ( 1 .. $#origcause_list ) {
                push(
                    @job_cause_section,
                    sprintf(
                        border(">") . "%-15s %-10s %-2s" . border("|") . "\n",
                        "", $origcause_list[$i], ""
                    )
                );
            }
            push( @job_section, [@job_cause_section] );
        }

        # Job Cause
        if ($cause) {
            my @job_cause_section;
            my $cause_len = length($cause);
            $Text::Wrap::columns = $cause_len + 1;
            my $wrapped_cause = fill( '', '', expand($cause) );
            my @cause_list = split "~", $wrapped_cause;
            push(
                @job_cause_section,
                sprintf(
                    border(">") . "%-3s%-12s %-10s %-2s" . border("|") . "\n",
                    "", "CAUSE", $cause_list[0], ""
                )
            );
            foreach my $i ( 1 .. $#cause_list ) {
                push(
                    @job_cause_section,
                    sprintf(
                        border(">") . "%-15s %-10s %-2s" . border("|") . "\n",
                        "", $cause_list[$i], ""
                    )
                );
            }
            push( @job_section, [@job_cause_section] );
        }

          # Job Origcorr
        if ($origcorr) {
            my @job_correction_section;
            my $origcorr_len = length($origcorr);
            $Text::Wrap::columns = $origcorr_len + 1;
            my $wrapped_origcorr = fill( '', '', expand($origcorr) );
            my @origcorr_list = split "~", $wrapped_origcorr;
            push(
                @job_correction_section,
                sprintf(
                    border(">") . "%-3s%-12s %-10s %-2s" . border("|") . "\n",
                    "", "ORIG CORR", $origcorr_list[0], ""
                )
            );
            foreach my $i ( 1 .. $#origcorr_list ) {
                push(
                    @job_correction_section,
                    sprintf(
                        border(">") . "%-15s %-10s %-2s" . border("|") . "\n",
                        "", $origcorr_list[$i], ""
                    )
                );
            }
            push( @job_section, [@job_correction_section] );
        }

        # Job Correction
        if ($correction) {
            my @job_correction_section;
            my $correction_len = length($correction);
            $Text::Wrap::columns = $correction_len + 1;
            my $wrapped_correction = fill( '', '', expand($correction) );
            my @correction_list = split "~", $wrapped_correction;
            push(
                @job_correction_section,
                sprintf(
                    border(">") . "%-3s%-12s %-10s %-2s" . border("|") . "\n",
                    "", "CORRECTION", $correction_list[0], ""
                )
            );
            foreach my $i ( 1 .. $#correction_list ) {
                push(
                    @job_correction_section,
                    sprintf(
                        border(">") . "%-15s %-10s %-2s" . border("|") . "\n",
                        "", $correction_list[$i], ""
                    )
                );
            }
            push( @job_correction_section,
                sprintf( border(">") . "%-15s" . border("|") . "\n", "" ) );
            push( @job_section, [@job_correction_section] );
        }

        my @parts_section = make_parts_section( $ro_line, $ro_job_line );
        push( @job_section, [@parts_section] );

        my @total_parts_section;
        if ( $total_job_parts_cost > 0 ) {
            push(
                @total_parts_section,
                sprintf(
                    border(">") . "%-58s%-9s%10.2f" . border("|") . "\n",
                    "", "COST TOTAL", $total_job_parts_cost
                )
            );
        }

	if ( $#parts_section > 0 ) {
            push(
                @total_parts_section,
                sprintf(
                    border(">") . "%-70s  %-18s%11.2f" . border("|") . "\n",
                    "", "TOTAL - PARTS",
                    $total_job_parts_sale
                )
            );
        }
            push( @job_section, [@total_parts_section] );
        
      my @sublet_section = make_sublet_section( $ro_number, $ro_line );
        push( @job_section, [@sublet_section] );
        my @total_sublet_section;
        if ( $#sublet_section > 0 ) {
            push(
                @total_sublet_section,
                sprintf(
                    border(">") . "%-75s  %-9s %10.2f" . border("|") . "\n",
                    "", "TOTAL - SUBLET",
                    $total_sublet_sale
                )
            );
            push( @job_section, [@total_sublet_section] );
        }

        my @gog_section = make_gog_section( $ro_number, $ro_line );
        push( @job_section, [@gog_section] );
        my @total_gog_section;
        if ( $#gog_section > 0 ) {
            push(
                @total_gog_section,
                sprintf(
                    border(">") . "%-75s  %-13s %11.2f" . border("|") . "\n",
                    "", "TOTAL - GOG", $total_gog_sale
                )
            );
            push( @job_section, [@total_gog_section] );
        }

       

        #   $grand_total_sublet = $grand_total_sublet + $total_sublet;

        my @misc_section = make_misc_section( $ro_number, $ro_line );
        push( @job_section, [@misc_section] );
        my @total_misc_section;

        # if ( $total_misc_sale < 0 ) {
        if ( $#misc_section > 0 ) {
            push(
                @total_misc_section,
                sprintf(
                    border(">") . "%-75s  %-10s %11.2f" . border("|") . "\n",
                    "", "TOTAL - MISC", $total_misc_sale
                )
            );
            push( @job_section, [@total_misc_section] );
        }

        $total_warranty_job_cost +=
          $total_warranty_job_labor_cost +
          $total_warranty_job_parts_cost +
          $total_warranty_job_gog_cost +
          $total_warranty_job_sublet_cost +
          $total_warranty_job_misc_cost +
          $total_warranty_job_misc_disc_cost;

        $total_warranty_job_sale +=
          $total_warranty_job_labor_sale +
          $total_warranty_job_parts_sale +
          $total_warranty_job_gog_sale +
          $total_warranty_job_sublet_sale +
          $total_warranty_job_misc_sale +
          $total_warranty_job_misc_disc_sale;

        $grand_warranty_labor_cost  += $total_warranty_job_labor_cost;
        $grand_warranty_labor_sale  += $total_warranty_job_labor_sale;
        $grand_warranty_parts_cost  += $total_warranty_job_parts_cost;
        $grand_warranty_parts_sale  += $total_warranty_job_parts_sale;
        $grand_warranty_gog_cost    += $total_warranty_job_gog_cost;
        $grand_warranty_gog_sale    += $total_warranty_job_gog_sale;
        $grand_warranty_misc_cost   += $total_warranty_job_misc_cost;
        $grand_warranty_misc_sale   += $total_warranty_job_misc_sale;
        $grand_warranty_misc_disc_cost   += $total_warranty_job_misc_disc_cost;
        $grand_warranty_misc_disc_sale   += $total_warranty_job_misc_disc_sale;
        $grand_warranty_sublet_cost += $total_warranty_job_sublet_cost;
        $grand_warranty_sublet_sale += $total_warranty_job_sublet_sale;
        $grand_warranty_cost        += $total_warranty_job_cost;
        $grand_warranty_sale        += $total_warranty_job_sale;

        $total_customer_job_cost +=
          $total_customer_job_labor_cost +
          $total_customer_job_parts_cost +
          $total_customer_job_gog_cost +
          $total_customer_job_sublet_cost +
          $total_customer_job_misc_cost +
          $total_customer_job_misc_disc_cost;

        $total_customer_job_sale +=
          $total_customer_job_labor_sale +
          $total_customer_job_parts_sale +
          $total_customer_job_gog_sale +
          $total_customer_job_sublet_sale +
          $total_customer_job_misc_sale +
          $total_customer_job_misc_disc_sale;

        $grand_customer_labor_cost  += $total_customer_job_labor_cost;
        $grand_customer_labor_sale  += $total_customer_job_labor_sale;
        $grand_customer_parts_cost  += $total_customer_job_parts_cost;
        $grand_customer_parts_sale  += $total_customer_job_parts_sale;
        $grand_customer_gog_cost    += $total_customer_job_gog_cost;
        $grand_customer_gog_sale    += $total_customer_job_gog_sale;
        $grand_customer_misc_cost   += $total_customer_job_misc_cost;
        $grand_customer_misc_sale   += $total_customer_job_misc_sale;
        $grand_customer_misc_disc_cost   += $total_customer_job_misc_disc_cost;
        $grand_customer_misc_disc_sale   += $total_customer_job_misc_disc_sale;
        $grand_customer_sublet_cost += $total_customer_job_sublet_cost;
        $grand_customer_sublet_sale += $total_customer_job_sublet_sale;
        $grand_customer_cost        += $total_customer_job_cost;
        $grand_customer_sale        += $total_customer_job_sale;

        $total_internal_job_cost +=
          $total_internal_job_labor_cost +
          $total_internal_job_parts_cost +
          $total_internal_job_gog_cost +
          $total_internal_job_sublet_cost +
          $total_internal_job_misc_cost +
          $total_internal_job_misc_disc_cost;

        $total_internal_job_sale +=
          $total_internal_job_labor_sale +
          $total_internal_job_parts_sale +
          $total_internal_job_gog_sale +
          $total_internal_job_sublet_sale +
          $total_internal_job_misc_sale +
          $total_internal_job_misc_disc_sale;

        $grand_internal_labor_cost  += $total_internal_job_labor_cost;
        $grand_internal_labor_sale  += $total_internal_job_labor_sale;
        $grand_internal_parts_cost  += $total_internal_job_parts_cost;
        $grand_internal_parts_sale  += $total_internal_job_parts_sale;
        $grand_internal_gog_cost    += $total_internal_job_gog_cost;
        $grand_internal_gog_sale    += $total_internal_job_gog_sale;
        $grand_internal_misc_cost   += $total_internal_job_misc_cost;
        $grand_internal_misc_sale   += $total_internal_job_misc_sale;
        $grand_internal_misc_disc_cost   += $total_internal_job_misc_disc_cost;
        $grand_internal_misc_disc_sale   += $total_internal_job_misc_disc_sale;
        $grand_internal_sublet_cost += $total_internal_job_sublet_cost;
        $grand_internal_sublet_sale += $total_internal_job_sublet_sale;
        $grand_internal_cost        += $total_internal_job_cost;
        $grand_internal_sale        += $total_internal_job_sale;

        my @total_labor_parts_section;

        ## Format amount with or without 0.00

        my ($formatted_total_warranty_job_labor_cost, $formatted_total_warranty_job_labor_sale, $formatted_total_warranty_job_parts_cost,
        $formatted_total_warranty_job_parts_sale,$formatted_total_warranty_job_gog_cost,$formatted_total_warranty_job_gog_sale,
        $formatted_total_warranty_job_misc_cost,$formatted_total_warranty_job_misc_sale,$formatted_total_warranty_job_misc_disc_cost,
        $formatted_total_warranty_job_misc_disc_sale,$formatted_total_warranty_job_sublet_cost,$formatted_total_warranty_job_sublet_sale
        );

        if (   $total_warranty_job_cost > 0
            || $total_warranty_job_sale > 0 )
        {

            $formatted_total_warranty_job_labor_cost = amount_format($total_warranty_job_labor_cost);
            $formatted_total_warranty_job_labor_sale = amount_format($total_warranty_job_labor_sale) ;
            $formatted_total_warranty_job_parts_cost = amount_format($total_warranty_job_parts_cost) ;
            $formatted_total_warranty_job_parts_sale = amount_format($total_warranty_job_parts_sale) ;
            $formatted_total_warranty_job_gog_cost = amount_format($total_warranty_job_gog_cost) ;
            $formatted_total_warranty_job_gog_sale = amount_format($total_warranty_job_gog_sale) ;
            $formatted_total_warranty_job_misc_cost = amount_format($total_warranty_job_misc_cost) ;
            $formatted_total_warranty_job_misc_sale = amount_format($total_warranty_job_misc_sale) ;
            $formatted_total_warranty_job_misc_disc_cost = amount_format($total_warranty_job_misc_disc_cost) ;
            $formatted_total_warranty_job_misc_disc_sale = amount_format($total_warranty_job_misc_disc_sale) ;
            $formatted_total_warranty_job_sublet_cost = amount_format($total_warranty_job_sublet_cost) ;
            $formatted_total_warranty_job_sublet_sale = amount_format($total_warranty_job_sublet_sale) ;

        }

        else
        {

            $formatted_total_warranty_job_labor_cost = plain_amount_format($total_warranty_job_labor_cost);
            $formatted_total_warranty_job_labor_sale = plain_amount_format($total_warranty_job_labor_sale) ;
            $formatted_total_warranty_job_parts_cost = plain_amount_format($total_warranty_job_parts_cost) ;
            $formatted_total_warranty_job_parts_sale = plain_amount_format($total_warranty_job_parts_sale) ;
            $formatted_total_warranty_job_gog_cost = plain_amount_format($total_warranty_job_gog_cost) ;
            $formatted_total_warranty_job_gog_sale = plain_amount_format($total_warranty_job_gog_sale) ;
            $formatted_total_warranty_job_misc_cost = plain_amount_format($total_warranty_job_misc_cost) ;
            $formatted_total_warranty_job_misc_sale = plain_amount_format($total_warranty_job_misc_sale) ;
            $formatted_total_warranty_job_misc_disc_cost = plain_amount_format($total_warranty_job_misc_disc_cost) ;
            $formatted_total_warranty_job_misc_disc_sale = plain_amount_format($total_warranty_job_misc_disc_sale) ;
            $formatted_total_warranty_job_sublet_cost = plain_amount_format($total_warranty_job_sublet_cost) ;
            $formatted_total_warranty_job_sublet_sale = plain_amount_format($total_warranty_job_sublet_sale) ;

        }

        my ($formatted_total_customer_job_labor_cost, $formatted_total_customer_job_labor_sale, $formatted_total_customer_job_parts_cost,
        $formatted_total_customer_job_parts_sale,$formatted_total_customer_job_gog_cost,$formatted_total_customer_job_gog_sale,
        $formatted_total_customer_job_misc_cost,$formatted_total_customer_job_misc_sale,$formatted_total_customer_job_misc_disc_cost,
        $formatted_total_customer_job_misc_disc_sale,$formatted_total_customer_job_sublet_cost,$formatted_total_customer_job_sublet_sale
        );

        if (   $total_customer_job_cost > 0
            || $total_customer_job_sale > 0 )
        {

            $formatted_total_customer_job_labor_cost = amount_format($total_customer_job_labor_cost);
            $formatted_total_customer_job_labor_sale = amount_format($total_customer_job_labor_sale) ;
            $formatted_total_customer_job_parts_cost = amount_format($total_customer_job_parts_cost) ;
            $formatted_total_customer_job_parts_sale = amount_format($total_customer_job_parts_sale) ;
            $formatted_total_customer_job_gog_cost = amount_format($total_customer_job_gog_cost) ;
            $formatted_total_customer_job_gog_sale = amount_format($total_customer_job_gog_sale) ;
            $formatted_total_customer_job_misc_cost = amount_format($total_customer_job_misc_cost) ;
            $formatted_total_customer_job_misc_sale = amount_format($total_customer_job_misc_sale) ;
            $formatted_total_customer_job_misc_disc_cost = amount_format($total_customer_job_misc_disc_cost) ;
            $formatted_total_customer_job_misc_disc_sale = amount_format($total_customer_job_misc_disc_sale) ;
            $formatted_total_customer_job_sublet_cost = amount_format($total_customer_job_sublet_cost) ;
            $formatted_total_customer_job_sublet_sale = amount_format($total_customer_job_sublet_sale) ;

        }

        else
        {

            $formatted_total_customer_job_labor_cost = plain_amount_format($total_customer_job_labor_cost);
            $formatted_total_customer_job_labor_sale = plain_amount_format($total_customer_job_labor_sale) ;
            $formatted_total_customer_job_parts_cost = plain_amount_format($total_customer_job_parts_cost) ;
            $formatted_total_customer_job_parts_sale = plain_amount_format($total_customer_job_parts_sale) ;
            $formatted_total_customer_job_gog_cost = plain_amount_format($total_customer_job_gog_cost) ;
            $formatted_total_customer_job_gog_sale = plain_amount_format($total_customer_job_gog_sale) ;
            $formatted_total_customer_job_misc_cost = plain_amount_format($total_customer_job_misc_cost) ;
            $formatted_total_customer_job_misc_sale = plain_amount_format($total_customer_job_misc_sale) ;
            $formatted_total_customer_job_misc_disc_cost = plain_amount_format($total_customer_job_misc_disc_cost) ;
            $formatted_total_customer_job_misc_disc_sale = plain_amount_format($total_customer_job_misc_disc_sale) ;
            $formatted_total_customer_job_sublet_cost = plain_amount_format($total_customer_job_sublet_cost) ;
            $formatted_total_customer_job_sublet_sale = plain_amount_format($total_customer_job_sublet_sale) ;

        }
        my ($formatted_total_internal_job_labor_cost, $formatted_total_internal_job_labor_sale, $formatted_total_internal_job_parts_cost,
        $formatted_total_internal_job_parts_sale,$formatted_total_internal_job_gog_cost,$formatted_total_internal_job_gog_sale,
        $formatted_total_internal_job_misc_cost,$formatted_total_internal_job_misc_sale,$formatted_total_internal_job_misc_disc_cost,
        $formatted_total_internal_job_misc_disc_sale,$formatted_total_internal_job_sublet_cost,$formatted_total_internal_job_sublet_sale
        );

        if (   $total_internal_job_cost > 0
            || $total_internal_job_sale > 0 )
        {

            $formatted_total_internal_job_labor_cost = amount_format($total_internal_job_labor_cost);
            $formatted_total_internal_job_labor_sale = amount_format($total_internal_job_labor_sale) ;
            $formatted_total_internal_job_parts_cost = amount_format($total_internal_job_parts_cost) ;
            $formatted_total_internal_job_parts_sale = amount_format($total_internal_job_parts_sale) ;
            $formatted_total_internal_job_gog_cost = amount_format($total_internal_job_gog_cost) ;
            $formatted_total_internal_job_gog_sale = amount_format($total_internal_job_gog_sale) ;
            $formatted_total_internal_job_misc_cost = amount_format($total_internal_job_misc_cost) ;
            $formatted_total_internal_job_misc_sale = amount_format($total_internal_job_misc_sale) ;
            $formatted_total_internal_job_misc_disc_cost = amount_format($total_internal_job_misc_disc_cost) ;
            $formatted_total_internal_job_misc_disc_sale = amount_format($total_internal_job_misc_disc_sale) ;
            $formatted_total_internal_job_sublet_cost = amount_format($total_internal_job_sublet_cost) ;
            $formatted_total_internal_job_sublet_sale = amount_format($total_internal_job_sublet_sale) ;

        }

        else
        {

            $formatted_total_internal_job_labor_cost = plain_amount_format($total_internal_job_labor_cost);
            $formatted_total_internal_job_labor_sale = plain_amount_format($total_internal_job_labor_sale) ;
            $formatted_total_internal_job_parts_cost = plain_amount_format($total_internal_job_parts_cost) ;
            $formatted_total_internal_job_parts_sale = plain_amount_format($total_internal_job_parts_sale) ;
            $formatted_total_internal_job_gog_cost = plain_amount_format($total_internal_job_gog_cost) ;
            $formatted_total_internal_job_gog_sale = plain_amount_format($total_internal_job_gog_sale) ;
            $formatted_total_internal_job_misc_cost = plain_amount_format($total_internal_job_misc_cost) ;
            $formatted_total_internal_job_misc_sale = plain_amount_format($total_internal_job_misc_sale) ;
            $formatted_total_internal_job_misc_disc_cost = plain_amount_format($total_internal_job_misc_disc_cost) ;
            $formatted_total_internal_job_misc_disc_sale = plain_amount_format($total_internal_job_misc_disc_sale) ;
            $formatted_total_internal_job_sublet_cost = plain_amount_format($total_internal_job_sublet_cost) ;
            $formatted_total_internal_job_sublet_sale = plain_amount_format($total_internal_job_sublet_sale) ;

        }
        push( @total_labor_parts_section,
            sprintf( border(">") . "%-16s" . border("|") . "\n", "" ) );
        push(
            @total_labor_parts_section,
            sprintf(
                border(">") . "%2s %10s" . border("|") . "\n",
                "",
                "JOB# "
                  . $ro_line
                  . " TOTALS---------CUSTOMER------------------WARRANTY------------------INTERNAL-----------------"
            )
        );
        push(
            @total_labor_parts_section,
            sprintf(
                border(">")
                  . "%-16s %-5s %-2s %-6s %-2s %-6s %-2s %-5s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %6s %2s %6s"
                  . border("|") . "\n",
                "",     "GL", "",     "COST", "",   "SALE", "",     "GL", "",
                "COST", "",   "SALE", "",     "GL", "",     "COST", "",   "SALE"
            )
        );

        if (   $total_warranty_job_labor_sale > 0
            || $total_customer_job_labor_sale > 0
            || $total_internal_job_labor_sale > 0
            || $total_warranty_job_labor_cost > 0
            || $total_customer_job_labor_cost > 0
            || $total_internal_job_labor_cost > 0 )
        {
            push(
                @total_labor_parts_section,
                sprintf(
                    border(">")
                      . "%-4s%-16s%-2s%8s%-2s%8s%-2s%-6s%-3s%8s%-2s%8s%-2s%-6s%-6s%8s%-2s%8s"
                      . border("|") . "\n",
                    "", "LABOR",
                    "", $formatted_total_customer_job_labor_cost,
                    "", $formatted_total_customer_job_labor_sale,
                    "", "",
                    "", $formatted_total_warranty_job_labor_cost,
                    "", $formatted_total_warranty_job_labor_sale,
                    "", "",
                    "", $formatted_total_internal_job_labor_cost,
                    "", $formatted_total_internal_job_labor_sale
                )
            );
        }
        if (   $total_warranty_job_parts_sale > 0
            || $total_customer_job_parts_sale > 0
            || $total_internal_job_parts_sale > 0
            || $total_warranty_job_parts_cost > 0
            || $total_customer_job_parts_cost > 0
            || $total_internal_job_parts_cost > 0 )
        {
            push(
                @total_labor_parts_section,
                sprintf(
                    border(">")
                      . "%-4s%-16s%-2s%8s%-2s%8s%-2s%-6s%-3s%8s%-2s%8s%-2s%-6s%-6s%8s%-2s%8s"
                      . border("|") . "\n",
                    "", "PARTS",
                    "", $formatted_total_customer_job_parts_cost,
                    "", $formatted_total_customer_job_parts_sale,
                    "", "",
                    "", $formatted_total_warranty_job_parts_cost,
                    "", $formatted_total_warranty_job_parts_sale,
                    "", "",
                    "", $formatted_total_internal_job_parts_cost,
                    "", $formatted_total_internal_job_parts_sale
                )
            );
        }
        if (   $total_warranty_job_sublet_sale > 0
            || $total_customer_job_sublet_sale > 0
            || $total_internal_job_sublet_sale > 0
            || $total_warranty_job_sublet_cost > 0
            || $total_customer_job_sublet_cost > 0
            || $total_internal_job_sublet_cost > 0 )
        {
            push(
                @total_labor_parts_section,
                sprintf(
                    border(">")
                      . "%-4s%-16s%-2s%8s%-2s%8s%-2s%-6s%-3s%8s%-2s%8s%-2s%-6s%-6s%8s%-2s%8s"
                      . border("|") . "\n",
                    "", "SUBLET",
                    "", $formatted_total_customer_job_sublet_cost,
                    "", $formatted_total_customer_job_sublet_sale,
                    "", "",
                    "", $formatted_total_warranty_job_sublet_cost,
                    "", $formatted_total_warranty_job_sublet_sale,
                    "", "",
                    "", $formatted_total_internal_job_sublet_cost,
                    "", $formatted_total_internal_job_sublet_sale
                )
            );
        }
        if (   $total_warranty_job_gog_sale > 0
            || $total_customer_job_gog_sale > 0
            || $total_internal_job_gog_sale > 0
            || $total_warranty_job_gog_cost > 0
            || $total_customer_job_gog_cost > 0
            || $total_internal_job_gog_cost > 0 )
        {
            push(
                @total_labor_parts_section,
                sprintf(
                    border(">")
                      . "%-4s%-16s%-2s%8s%-2s%8s%-2s%-6s%-3s%8s%-2s%8s%-2s%-6s%-6s%8s%-2s%8s"
                      . border("|") . "\n",
                    "", "G.O.G.",
                    "", $formatted_total_customer_job_gog_cost,
                    "", $formatted_total_customer_job_gog_sale,
                    "", "",
                    "", $formatted_total_warranty_job_gog_cost,
                    "", $formatted_total_warranty_job_gog_sale,
                    "", "",
                    "", $formatted_total_internal_job_gog_cost,
                    "", $formatted_total_internal_job_gog_sale
                )
            );
        }

# if($total_misc_chg > 0){
# push (@total_labor_parts_section, sprintf(border(">")."%-4s%-19s%12s%-3s%7.2f%-2s%-6s%-5s%6s%-3s%6.2f%-2s%-6s%-8s%6s%-2s%6.2f".border("|")."\n",
#                                             "","MISC CHG","","",$customer_total_misc_chg,"","","","","",$warranty_total_misc_chg,"","","","","",$internal_total_misc_chg));
# }
        if (   $total_warranty_job_misc_sale > 0
            || $total_customer_job_misc_sale > 0
            || $total_internal_job_misc_sale > 0
            || $total_warranty_job_misc_cost > 0
            || $total_customer_job_misc_cost > 0
            || $total_internal_job_misc_cost > 0 )
        {
            push(
                @total_labor_parts_section,
                sprintf(
                    border(">")
                      . "%-4s%-10s%-8s%8s%-2s%8s%-2s%-6s%-3s%8s%-2s%8s%-2s%-6s%-6s%8s%-2s%8s"
                      . border("|") . "\n",
                    "", "MISC CHG",
                    "", $formatted_total_customer_job_misc_cost,
                    "", $formatted_total_customer_job_misc_sale,
                    "", "",
                    "", $formatted_total_warranty_job_misc_cost,
                    "", $formatted_total_warranty_job_misc_sale,
                    "", "",
                    "", $formatted_total_internal_job_misc_cost,
                    "", $formatted_total_internal_job_misc_sale
                )
            );
        }

        if (   $total_warranty_job_misc_disc_sale < 0
            || $total_customer_job_misc_disc_sale < 0
            || $total_internal_job_misc_disc_sale < 0
            || $total_warranty_job_misc_disc_cost < 0
            || $total_customer_job_misc_disc_cost < 0
            || $total_internal_job_misc_disc_cost < 0 )
        {
            push(
                @total_labor_parts_section,
                sprintf(
                    border(">")
                      . "%-4s%-10s%-8s%8s%-2s%8s%-2s%-6s%-3s%8s%-2s%8s%-2s%-6s%-6s%8s%-2s%8s"
                      . border("|") . "\n",
                    "", "MISC DIS",
                    "", $formatted_total_customer_job_misc_disc_cost,
                    "", $formatted_total_customer_job_misc_disc_sale,
                    "", "",
                    "", $formatted_total_warranty_job_misc_disc_cost,
                    "", $formatted_total_warranty_job_misc_disc_sale,
                    "", "",
                    "", $formatted_total_internal_job_misc_disc_cost,
                    "", $formatted_total_internal_job_misc_disc_sale
                )
            );
        }

        push(
            @total_labor_parts_section,
            sprintf(
                border(">")
                  . "%-4s%-16s%-2s%8.2f%-2s%8.2f%-2s%-6s%-3s%8.2f%-2s%8.2f%-2s%-6s%-6s%8.2f%-2s%8.2f"
                  . border("|") . "\n",
                "", "TOTAL",                  "", $total_customer_job_cost,
                "", $total_customer_job_sale, "", "",
                "", $total_warranty_job_cost, "", $total_warranty_job_sale,
                "", "",                       "", $total_internal_job_cost,
                "", $total_internal_job_sale
            )
        );
        push(
            @total_labor_parts_section,
            sprintf(
                border(">")
                  . "%-3s%-5s%-12s%-4s%-25s%-32s%-2s"
                  . border("|") . "\n",
                "",
                "JOB# " . $ro_line . " JOURNAL PREFIX",
                "",
                $accounting_make_code . "C" . $department,
                "",
                $accounting_make_code . "W" . $department,
                $accounting_make_code . "I" . $department
            )
        );
        push( @total_labor_parts_section,
            sprintf( border(">") . "%-16s" . border("|") . "\n", "" ) );
        push( @job_section, [@total_labor_parts_section] );
    }

    #my @misc_section = make_misc_section($ro_number, $ro_line);
    #push (@job_section, [@misc_section]);

#	my @total_misc_section;
#   if($total_misc > 0){
#     push (@total_misc_section, sprintf(border(">")."%-75s  %-10s %11.2f".border("|")."\n", "", "TOTAL - MISC", $total_misc ));
#    push (@job_section, [@total_misc_section]);
#}
# GOG claim section of a JOB

    my $GOG_query = $conn->prepare(
        "SELECT
                dlr_cost
            FROM repair_other_totals 
            WHERE all_to_amt_type = 'All' AND dlr_cost::numeric > 0 AND ro_number = ? AND types = 'GOG' AND pay_type = 'Warr'"
    );
    my (@GOG_row) = $GOG_query->execute($ro_number);
    my ($GOG)     = @GOG_row;

    # $gog_entries = $GOG_query->rows;

    # MISC claim section of a JOB

    my $MISC_query = $conn->prepare(
        "SELECT
                dlr_cost
            FROM repair_other_totals 
            WHERE all_to_amt_type = 'All' AND dlr_cost::numeric > 0 AND ro_number = ? AND types = 'MISC' AND pay_type = 'Warr'"
    );
    my (@MISC_row) = $MISC_query->execute($ro_number);
    my ($MISC)     = @MISC_row;

    # $gog_entries = $GOG_query->rows;

    # sublet_labor_claim section of a JOB

    my $sublet_labor_query = $conn->prepare(
        "SELECT
                dlr_cost
            FROM repair_other_totals 
            WHERE all_to_amt_type = 'Labor' AND dlr_cost::numeric > 0 AND ro_number = ? AND types = 'SUBLET' AND pay_type = 'Warr'"
    );
    my (@sublet_labor_row) = $sublet_labor_query->execute($ro_number);
    my ($sublet_labor)     = @sublet_labor_row;

    # my ($sublet_labor_entries) = $sublet_labor_query->rows;

    # sublet_part_claim section of a JOB

    my $sublet_part_query = $conn->prepare(
        "SELECT
                dlr_cost
            FROM repair_other_totals 
            WHERE all_to_amt_type = 'Parts' AND dlr_cost::numeric > 0 AND ro_number = ? AND types = 'SUBLET' AND pay_type = 'Warr'"
    );
    my (@sublet_part_row) = $sublet_part_query->execute($ro_number);
    my ($sublet_part)     = @sublet_part_row;

    # my ($sublet_part_entries) = $sublet_part_query->rows;

    my @total_section;
    ##############################CLAIM SECTION START#############################

    my (
        $ro_number_claim,  $claim_number_claim, $labor_sale_claim,
        $parts_sale_claim, $sublet_labor_claim, $sublet_parts_claim,
        $gog_claim,        $misc_claim,         $tax_claim,
        $total_claim
    );
    my (
        $total_labor_sale_claim,   $total_parts_sale_claim,
        $total_sublet_labor_claim, $total_sublet_parts_claim,
        $total_gog_claim,          $total_misc_claim,
        $total_tax_claim,          $grand_total_claim
    );

    my $claim_deatil_qry = $conn->prepare(
        "SELECT
                                        ro_number,
                                        claim_number,
                                        coalesce(labor_sale::numeric, 0.00),
                                        coalesce(parts_sale::numeric, 0.00),
                                        coalesce(sublet_labor::numeric, 0.00),
                                        coalesce(sublet_parts::numeric, 0.00),
                                        coalesce(gog::numeric, 0.00),
                                        coalesce(misc::numeric, 0.00),
                                        coalesce(tax::numeric, 0.00),
                                        coalesce(total::numeric, 0.00) 
                                  FROM repair_claim_details
                                  WHERE ro_number = ?"
    );
    $claim_deatil_qry->execute($ro_number);

    if ( $claim_deatil_qry->rows > 0 ) {

        push(
            @total_section,
            sprintf(
                border(">") . '~font{fCourier-Bold3}' . "%-100s" . "\n",
                ""
            )
        );
        push(
            @total_section,
            sprintf(
                border(">")
                  . '~font{fCourier-Bold7}'
                  . "%3s %10s"
                  . border("|") . "\n",
                "",
"WARRANTY CLAIM DETAIL TOTALS---------------------------------------------------------------------"
            )
        );
        push(
            @total_section,
            sprintf(
                border(">") . "%3s %10s" . border("|") . "\n",
                "",
"CLAIM#.......... LABOR.... PARTS.... SUB.LAB.... SUB.PART.... GOG.... MISC.... TAX.... TOTAL....."
            )
        );

        while ( my @claim_deatil_row = $claim_deatil_qry->fetchrow_array ) {
            (
                $ro_number_claim,  $claim_number_claim, $labor_sale_claim,
                $parts_sale_claim, $sublet_labor_claim, $sublet_parts_claim,
                $gog_claim,        $misc_claim,         $tax_claim,
                $total_claim
            ) = @claim_deatil_row;

            push(
                @total_section,
                sprintf(
                    border("#")
                      . '~font{fCourier-Bold7}'
                      . "%4s%-14s%10.2f%10.2f%7.2f%12.2f%13.2f%8.2f%9.2f%12.2f"
                      . '~font{default}'
                      . border("|") . "\n",
                    "",                  $claim_number_claim,
                    $labor_sale_claim,   $parts_sale_claim,
                    $sublet_labor_claim, $sublet_parts_claim,
                    $gog_claim,          $misc_claim,
                    $tax_claim,          $total_claim
                )
            );

            $total_labor_sale_claim   += $labor_sale_claim;
            $total_parts_sale_claim   += $parts_sale_claim;
            $total_sublet_labor_claim += $sublet_labor_claim;
            $total_sublet_parts_claim += $sublet_parts_claim;
            $total_gog_claim          += $gog_claim;
            $total_misc_claim         += $misc_claim;
            $total_tax_claim          += $tax_claim;
            $grand_total_claim        += $total_claim;
        }
        push(
            @total_section,
            sprintf(
                border(">") . "%3s %10s" . border("|") . "\n",
                "",
"---------------- --------- --------- ----------- ------------ ------- -------- ------- ---------"
            )
        );
        push(
            @total_section,
            sprintf(
                border(">")
                  . '~font{fCourier-Bold7}'
                  . "%4s%-14s%10.2f%10.2f%7.2f%12.2f%13.2f%8.2f%9.2f%12.2f"
                  . border("|") . "\n",
                "",                        "CLAIM TOTALS",
                $total_labor_sale_claim,   $total_parts_sale_claim,
                $total_sublet_labor_claim, $total_sublet_parts_claim,
                $total_gog_claim,          $total_misc_claim,
                $total_tax_claim,          $grand_total_claim
            )
        );
# Based on Hunt_beach_chrysler actual RO review commented
        # if ( $grand_warranty_sale <= 0 ) {
        #     $grand_warranty_labor_sale += $total_labor_sale_claim;
        #     $grand_warranty_parts_sale += $total_parts_sale_claim;
        #     $grand_warranty_gog_sale   += $total_gog_claim;
        #     $grand_warranty_sublet_sale +=
        #       $total_sublet_labor_claim + $total_sublet_parts_claim;
        #     $grand_warranty_misc_sale += $total_misc_claim;
        #     $grand_warranty_sale +=
        #       $total_labor_sale_claim +
        #       $total_parts_sale_claim +
        #       $total_gog_claim +
        #       $total_sublet_labor_claim +
        #       $total_sublet_parts_claim +
        #       $total_misc_claim;
        # }
    }

    push( @job_section, [@total_section] );

    ##############################CLAIM SECTION END#############################

    # my @all_misc_section = make_all_misc_section($ro_number);
    # push( @job_section, [@all_misc_section] );
    my @all_misc_job_a_section = make_misc_joba_section($ro_number);
    push( @job_section, [@all_misc_job_a_section] );
    my @total_joba_section;
        if ( $#all_misc_job_a_section > 0 ) {
            push(
                @total_joba_section,
                sprintf(
                    border(">") . "%-77s%-14s%10.2f" . border("|") . "\n",
                    "", "TOTAL - MISC",
                    $total_joba_sale
                )
            );
            push( @job_section, [@total_joba_section] );
    }

    my @estimate_section = make_estimate_section($ro_number);
    push (@job_section, [@estimate_section]);
    my @comments_section = make_comments_section($ro_number);
    push(
        @comments_section,
        sprintf(
            border("#")
              . '~font{fCourier-Bold7}'
              . "%3s%-80s"
              . '~font{default}'
              . border("|") . "\n",
            "", "GRAND TOTALS" . "-" x 83
        )
    );

    push(
        @comments_section,
        sprintf(
            border(">") . "%2s %10s" . border("|") . "\n",
            "",
"--------------CUSTOMER------------------------WARRANTY---------------------------INTERNAL---------"
        )
    );
    push(
        @comments_section,
        sprintf(
            border(">")
              . "%-15s %-5s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %6s %2s %6s"
              . border("|") . "\n",
            "",     "GL", "",     "COST", "",   "SALE", "",     "GL", "",
            "COST", "",   "SALE", "",     "GL", "",     "COST", "",   "SALE"
        )
    );
    if (    $grand_customer_labor_cost > 0 
	 || $grand_customer_labor_sale > 0  
	 || $grand_warranty_labor_cost > 0 
         || $grand_warranty_labor_sale > 0  
         || $grand_internal_labor_cost > 0 
         || $grand_internal_labor_sale > 0 )
    {
    	push(
        	@comments_section,
        	sprintf(
            		border(">")
              			. "%-4s%-15s%-2s%8s%3s%8s%2s%6s%3s%8s%2s%8s%2s%6s%6s%8s%2s%8s"
              			. border("|") . "\n",
            		"", "LABOR",                    "", plain_amount_format($grand_customer_labor_cost),
            		"", plain_amount_format($grand_customer_labor_sale), "", "",
            		"", plain_amount_format($grand_warranty_labor_cost), "", plain_amount_format($grand_warranty_labor_sale),
            		"", "",                         "", plain_amount_format($grand_internal_labor_cost),
            		"", plain_amount_format($grand_internal_labor_sale)
        		)
    	);
    }
    if (    $grand_customer_parts_cost > 0 
	 || $grand_customer_parts_sale > 0  
	 || $grand_warranty_parts_cost > 0 
         || $grand_warranty_parts_sale > 0  
         || $grand_internal_parts_cost > 0 
         || $grand_internal_parts_sale > 0 )
    {
    	push(
        	@comments_section,
	        sprintf(
            		border(">")
              			. "%-4s%-15s%-2s%8s%3s%8s%2s%6s%3s%8s%2s%8s%2s%6s%6s%8s%2s%8s"
              			. border("|") . "\n",
            		"", "PARTS",                    "", plain_amount_format($grand_customer_parts_cost),
            		"", plain_amount_format($grand_customer_parts_sale), "", "",
            		"", plain_amount_format($grand_warranty_parts_cost), "", plain_amount_format($grand_warranty_parts_sale),
            		"", "",                         "", plain_amount_format($grand_internal_parts_cost),
            		"", plain_amount_format($grand_internal_parts_sale)
        	)
    	);
    }
    if (   $grand_customer_sublet_cost > 0
        || $grand_warranty_sublet_cost > 0
        || $grand_internal_sublet_cost > 0
        || $grand_warranty_sublet_sale > 0
        || $grand_customer_sublet_sale > 0
        || $grand_internal_sublet_sale > 0 )
    {
        push(
            @comments_section,
            sprintf(
                border(">")
                  . "%-4s%-8s%-9s%8s%3s%8s%2s%6s%3s%8s%2s%8s%2s%6s%6s%8s%2s%8s"
                  . border("|") . "\n",
                "", "SUBLET", "",
                plain_amount_format($grand_customer_sublet_cost), "", plain_amount_format($grand_customer_sublet_sale),
                "",                          "", "",
                plain_amount_format($grand_warranty_sublet_cost), "", plain_amount_format($grand_warranty_sublet_sale),
                "",                          "", "",
                plain_amount_format($grand_internal_sublet_cost), "", plain_amount_format($grand_internal_sublet_sale)
            )
        );
    }

    if (   $grand_warranty_gog_cost > 0
        || $grand_customer_gog_cost > 0
        || $grand_internal_gog_cost > 0
        || $grand_warranty_gog_sale > 0
        || $grand_customer_gog_sale > 0
        || $grand_internal_gog_sale > 0 )
    {
        push(
            @comments_section,
            sprintf(
                border(">")
                  . "%-4s%-15s%-2s%8s%3s%8s%2s%6s%3s%8s%2s%8s%2s%6s%6s%8s%2s%8s"
                  . border("|") . "\n",
                "", "G.O.G.",                 "", amount_format($grand_customer_gog_cost),
                "", plain_amount_format($grand_customer_gog_sale), "", "",
                "", plain_amount_format($grand_warranty_gog_cost), "", plain_amount_format($grand_warranty_gog_sale),
                "", "",                       "", plain_amount_format($grand_internal_gog_cost),
                "", plain_amount_format($grand_internal_gog_sale)
            )
        );
    }

    if (   $grand_customer_misc_sale > 0
        || $grand_warranty_misc_sale > 0
        || $grand_internal_misc_sale > 0 )
    {
        push(
            @comments_section,
            sprintf(
                border(">")
                  . "%-4s%-18s%7s%3s%8s%2s%6s%6s%6s%1s%8s%2s%6s%8s%6s%2s%8s"
                  . border("|") . "\n",
                "",                        "MISC CHG",
                "",                        "",
                plain_amount_format($grand_customer_misc_sale), "",
                "",                        "",
                "",                        "",
                plain_amount_format($grand_warranty_misc_sale), "",
                "", "",
                "", "",
                plain_amount_format($grand_internal_misc_sale)
            )
        );
    }
    
    if (   $grand_customer_misc_disc_sale < 0
        || $grand_warranty_misc_disc_sale < 0
        || $grand_internal_misc_disc_sale < 0 )
    {
        push(
            @comments_section,
            sprintf(
                border(">")
                  . "%-4s%-18s%7s%3s%8s%2s%6s%6s%5s%2s%8s%2s%6s%8s%6s%2s%8s"
                  . border("|") . "\n",
                "",                        "MISC DIS",
                "",                        "",
                plain_amount_format($grand_customer_misc_disc_sale), "",
                "",                        "",
                "",                        "",
                plain_amount_format($grand_warranty_misc_disc_sale), "",
                "", "",
                "", "",
                plain_amount_format($grand_internal_misc_disc_sale)
            )
        );
    }
    $grand_warranty_sale += $tax_warranty;
    $grand_customer_sale += $tax_customer;
    $grand_internal_sale += $tax_internal;

    if (   $tax_customer > 0
        || $tax_warranty > 0
        || $tax_internal > 0 )
    {
    	push(
        	@comments_section,
	        sprintf(
        	    border(">")
	              . "%-4s%-22s%-7s%7.2f%-2s%-6s%-1s%-6s%-8s%6.2f%-2s%-6s%-4s%-6s%-10s%2.2f"
	              . border("|") . "\n",
	            "", "TAXES", "    ", $tax_customer, "", "", "", "", "",
        	    $tax_warranty, "", "", "", "", "", $tax_internal
	        )
	    );
     }

    push( @job_section, [@comments_section] );

    #  my @tax_section = make_tax_section($ro_number);
    #  push (@job_section, [@tax_section]);
}

# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_grand_total_section {

    @grand_total_section = '';

    # %-4s%-4s%-4s%-18s%6.2f%-4s%-6s%-2s%-6s%-8s%2.2f%-6s%-2s%-6s%-4s%-9s%5.2f
    push(
        @grand_total_section,
        sprintf(
            border(">")
              . "%-22s %-8s %-8s %-7s %-8s %-8s %-13s %-8s %-8s"
              . border("|") . "\n",
            "",          "---------", "---------", "", "---------",
            "---------", "",          "---------", "---------"
        )
    );
    push(
        @grand_total_section,
        sprintf(
            border(">")
              . "%-4s%-15s%-2s%8.2f%3s%8.2f%2s%6s%3s%8.2f%2s%8.2f%2s%6s%6s%8.2f%2s%8.2f"
              . border("|") . "\n",
            "", "TOTAL",              "", $grand_customer_cost,
            "", remove_negative_sign_penny($grand_customer_sale), "", "",
            "", $grand_warranty_cost, "", $grand_warranty_sale,
            "", "",                   "", $grand_internal_cost,
            "", $grand_internal_sale
        )
    );

    push( @grand_total_section,
        sprintf( border(">") . "%-16s" . border("|") . "\n", "" ) );

    push( @grand_total_section,
        sprintf( border(">") . "%-16s" . border("|") . "\n", "" ) );

    push(
        @grand_total_section,
        sprintf(
            border(">") . "%-2s%-60s" . border("|") . "\n",
            "",
"******************************* A L L D E T A I L I N V O I C E ********************************"
        )
    );
}

# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ( $ro_line, $ro_job_line ) = @_;
    my @parts_section_array;
    my $parts_qry = $conn->prepare(
        "SELECT DISTINCT ON ((array_agg(ro_part_line))[1])
                                        (array_agg(ro_part_line))[1],
                                        (array_agg(part_number))[1],
                                        (array_agg(part_kit))[1],
                                        (array_agg(part_description))[1],
                                        string_agg(left(nullif(prt_billing_type, 'NA'), 1), ',')          AS prt_billing_code,
                                        string_agg(case when prt_billing_type = 'Intr' then 'INTL' 
						else UPPER(nullif(prt_billing_type, 'NA')) 
					end, ',') AS prt_billing_type,
                                        (array_agg(quantity_sold))[1],
                                        string_agg(coalesce(unit_cost::text, '0.00'), ','),
                                        string_agg(coalesce(unit_sale::text, '0.00'), ','),
                                        string_agg(unit_core_cost::text, ','),
                                        string_agg(unit_core_sale::text, ',')
                                        ,COUNT(*)  OVER (Partition BY (array_agg(rp.ro_number))[1],
                                        (array_agg(rp.ro_line))[1],
                                                 (array_agg(rp.ro_job_line))[1], (array_agg(rp.ro_part_line))[1]) AS prt_count
                                    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?
                                    GROUP BY seq_no
                                    ORDER BY (array_agg(ro_part_line))[1]"
    );
    $parts_qry->execute( $ro_number, $ro_line, $ro_job_line );

    $parts_entries = $parts_qry->rows;
    if ( $parts_entries > 0 ) {
        push(
            @parts_section_array,
            sprintf(
                border(">") . "%1s %10s" . border("|") . "\n",
                "",
" PARTS-----QTY--FP-NUMBER---------------DESCRIPTION---------U/COST---E/COST----U/PRICE"
            )
        );
        # " PARTS---------QTY----FP-NUMBER----------DESCRIPTION------------U/COST----E/COST---U/PRICE"
        my ($prt_billing_code_all, $prt_billing_type_all, $unit_cost_all, $unit_sale_all, $unit_core_cost_all, $unit_core_sale_all, $part_billing_val_1, $part_billing_val_2);
        while ( my @parts_row = $parts_qry->fetchrow_array ) {
            (
                $ro_part_line,         $part_number,          $part_kit,
                $part_description,     $prt_billing_code_all, $prt_billing_type_all, 
                $quantity_sold,        $unit_cost_all,        $unit_sale_all,        
                $unit_core_cost_all,   $unit_core_sale_all,   $prt_count
            ) = @parts_row;

            my @split_prt_billing_type_list = split ",", $prt_billing_type_all;
            my $prt_billing_type_count = scalar(@split_prt_billing_type_list);
            # if ( ($prt_billing_type_count > 1) or ( > 1) or ( > 1) or ( > 1) or( > 1) ){
            if ( $prt_billing_type_count > 1 ){
                my @part_unit_cost_list      = split ",", $unit_cost_all;
                my @part_unit_sale_list      = split ",", $unit_sale_all;
                my @part_unit_core_cost_list = split ",", $unit_core_cost_all;
                my @part_unit_core_sale_list = split ",", $unit_core_sale_all;
            
                foreach my $type_count ( 0 .. $#split_prt_billing_type_list ) { 
                    $prt_billing_type = $split_prt_billing_type_list[$type_count];
                    $unit_cost        = $part_unit_cost_list[$type_count];
                    $unit_sale        = $part_unit_sale_list[$type_count];
                    $unit_core_cost   = $part_unit_core_cost_list[$type_count];
                    $unit_core_sale   = $part_unit_core_sale_list[$type_count];

                    if ( $prt_billing_type eq "WARR" ) {
                        $total_warranty_job_parts_cost += $unit_cost * $quantity_sold;
                        $total_warranty_job_parts_sale += $unit_sale * $quantity_sold;
                    }
                    elsif ( $prt_billing_type eq "CUST" ) {
                        $total_customer_job_parts_cost += $unit_cost * $quantity_sold;
                        $total_customer_job_parts_sale += $unit_sale * $quantity_sold;
                    }
                    elsif ( $prt_billing_type eq "INTL" ) {
                        $total_internal_job_parts_cost += $unit_cost * $quantity_sold;
                        $total_internal_job_parts_sale += $unit_sale * $quantity_sold;
                    }

                    $total_job_parts_cost =
                    $total_warranty_job_parts_cost +
                    $total_customer_job_parts_cost +
                    $total_internal_job_parts_cost;
                    $total_job_parts_sale =
                    $total_warranty_job_parts_sale +
                    $total_customer_job_parts_sale +
                    $total_internal_job_parts_sale;

                    $Text::Wrap::columns = 18;
                    my $prefix =
        "^AC|^AU|^VW|^BM|^XM|^CH|^BO|^AR|^CH|^FO|^XF|^XL|^XY|^ZL|^GM|^XA|^HP|^HY|^IN|^JA|^KI|^RO|^LE|^XX|^MZ|^MB|^SV|^BM|^MI|^NI|^ZR|^GM|^PO|^SU|^TO";
                    $part_number =~ s/$prefix//;
                    my $wrapped_part_description =
                    fill( '', '', expand($part_description) );
                    my @part_description_list = split "\n", $wrapped_part_description;
                    if ( $type_count == 0 ){
                    push(
                        @parts_section_array,
                        sprintf(
                            border(">")
                            . "%-7s%-9s%-5s%-21s%-20s%7s%9s%10s%13s"
                            . border("|") . "\n",
                            "",
                            $prt_billing_type,
                            $quantity_sold,
                            $part_number,
                            $part_description_list[0],
                            kit_amount_format($unit_cost, $part_kit),
                            kit_amount_format(( $unit_cost * $quantity_sold ), $part_kit),
                            kit_amount_format($unit_sale, $part_kit),
                            kit_amount_format(( $unit_sale * $quantity_sold ), $part_kit)
                        )
                    );

                    foreach my $i ( 1 .. $#part_description_list ) {
                        push(
                            @parts_section_array,
                            sprintf(
                                border(">") . "%6s%-34s%-48s" . border("|") . "\n",
                                "", $part_description_list[$i], ""
                            )
                        );
                    }
                    }
                    else{
                        if ( $split_prt_billing_type_list[0] eq "WARR" ) {
                            $part_billing_val_1 = "WARRANTY";
                        }
                        elsif ( $split_prt_billing_type_list[0] eq "CUST" ) {
                            $part_billing_val_1 = "CUSTOMER";
                        }
                        elsif ( $split_prt_billing_type_list[0] eq "INTL" ) {
                            $part_billing_val_1 = "INTERNAL";
                        } 
                        if ( $split_prt_billing_type_list[1] eq "WARR" ) {
                            $part_billing_val_2 = "WARRANTY";
                        }
                        elsif ( $split_prt_billing_type_list[1] eq "CUST" ) {
                            $part_billing_val_2 = "CUSTOMER";
                        }
                        elsif ( $split_prt_billing_type_list[1] eq "INTL" ) {
                            $part_billing_val_2 = "INTERNAL";
                        }                       
                        push(
                        @parts_section_array,
                            sprintf(
                                border(">") . "%-7s%-12s%-7s%-9s%-10s%-9s%-9s%7s%8s%8s%15s" . border("|") . "\n",
                                "", $prt_billing_type,"SPLIT",$part_billing_val_1,"=      %",$part_billing_val_2,"=      %",$part_unit_cost_list[0], $part_unit_cost_list[1], $part_unit_sale_list[0], $part_unit_sale_list[1]
                            )
                        );
                    }

                    if ( ($prt_billing_type eq "WARR" || $prt_billing_type eq "INTL" || $prt_billing_type eq "CUST") &&  $quantity_sold == 0 ) {
                push(
                            @parts_section_array,
                            sprintf(
                                border(">") . "%13s%-34s%-48s" . border("|") . "\n",
                                "","PART ON SPECIAL ORDER", ""
                            )
                        );
                        push(
                            @parts_section_array,
                            sprintf(
                                border(">") . "%27s%-34s%-48s" . border("|") . "\n",
                                "","** QUANTITY 1 IS SPECIAL ORDERED **", ""
                            )
                        );
                    }
                }
            }
            else {
                    $prt_billing_type = $prt_billing_type_all;
                    $unit_cost        = $unit_cost_all;
                    $unit_sale        = $unit_sale_all;
                    $unit_core_cost   = $unit_core_cost_all;
                    $unit_core_sale   = $unit_core_sale_all;

                    if ( $prt_billing_type eq "WARR" ) {
                        $total_warranty_job_parts_cost += $unit_cost * $quantity_sold;
                        $total_warranty_job_parts_sale += $unit_sale * $quantity_sold;
                    }
                    elsif ( $prt_billing_type eq "CUST" ) {
                        $total_customer_job_parts_cost += $unit_cost * $quantity_sold;
                        $total_customer_job_parts_sale += $unit_sale * $quantity_sold;
                    }
                    elsif ( $prt_billing_type eq "INTL" ) {
                        $total_internal_job_parts_cost += $unit_cost * $quantity_sold;
                        $total_internal_job_parts_sale += $unit_sale * $quantity_sold;
                    }

                    $total_job_parts_cost =
                    $total_warranty_job_parts_cost +
                    $total_customer_job_parts_cost +
                    $total_internal_job_parts_cost;
                    $total_job_parts_sale =
                    $total_warranty_job_parts_sale +
                    $total_customer_job_parts_sale +
                    $total_internal_job_parts_sale;

                    $Text::Wrap::columns = 18;
                    my $prefix =
        "^AC|^AU|^VW|^BM|^XM|^CH|^BO|^AR|^CH|^FO|^XF|^XL|^XY|^ZL|^GM|^XA|^HP|^HY|^IN|^JA|^KI|^RO|^LE|^XX|^MZ|^MB|^SV|^BM|^MI|^NI|^ZR|^GM|^PO|^SU|^TO";
                    $part_number =~ s/$prefix//;
                    my $wrapped_part_description =
                    fill( '', '', expand($part_description) );
                    my @part_description_list = split "\n", $wrapped_part_description;
                    push(
                        @parts_section_array,
                        sprintf(
                            border(">")
                            . "%-7s%-9s%-5s%-21s%-20s%7s%9s%10s%13s"
                            . border("|") . "\n",
                            "",
                            $prt_billing_type,
                            $quantity_sold,
                            $part_number,
                            $part_description_list[0],
                            kit_amount_format($unit_cost, $part_kit),
                            kit_amount_format(( $unit_cost * $quantity_sold ), $part_kit),
                            kit_amount_format($unit_sale, $part_kit),
                            kit_amount_format(( $unit_sale * $quantity_sold ), $part_kit)
                        )
                    );

                    foreach my $i ( 1 .. $#part_description_list ) {
                        push(
                            @parts_section_array,
                            sprintf(
                                border(">") . "%6s%-34s%-48s" . border("|") . "\n",
                                "", $part_description_list[$i], ""
                            )
                        );
                    }
                    if ( ($prt_billing_type eq "WARR" || $prt_billing_type eq "INTL" || $prt_billing_type eq "CUST") &&  $quantity_sold == 0 ) {
                push(
                            @parts_section_array,
                            sprintf(
                                border(">") . "%13s%-34s%-48s" . border("|") . "\n",
                                "","PART ON SPECIAL ORDER", ""
                            )
                        );
                        push(
                            @parts_section_array,
                            sprintf(
                                border(">") . "%27s%-34s%-48s" . border("|") . "\n",
                                "","** QUANTITY 1 IS SPECIAL ORDERED **", ""
                            )
                        );
                    }
            }
        }
    }
    return @parts_section_array;
}

# Subroutine to prepare the GOG section of a JOB
sub make_gog_section {
    my ( $ro_number, $ro_line ) = @_;
    my @gog_section_array;
    $total_gog_sale = 0;
    $total_gog_cost = 0;

    my ($type) = "GOG";
    my $gog_qry = $conn->prepare(
        "SELECT
                case when other_billing_type = 'Intr' then 'INTL' else upper(other_billing_type) end as other_billing_type,
                other_quantity,
                other_code,
                other_description,
                coalesce(other_cost::numeric, 0.00),
                coalesce(other_sale::numeric, 0.00)
            FROM repair_other             
            WHERE other_sale::numeric > 0 AND ro_number = ? AND ro_line = ? AND item_type = ?"
    );
    $gog_qry->execute( $ro_number, $ro_line, $type );
    my (
        $pay_type,        $unit_quantity, $gog_type,
        $gog_description, $dlr_cost,      $cust_price
    );
    $gog_entries = $gog_qry->rows;

    if ( $gog_entries > 0 ) {
        push(
            @gog_section_array,
            sprintf(
                border(">") . "%2s %10s" . border("|") . "\n",
                "",
"G.O.G. & SUPPLIES-------------------------------------------------------------------------"
            )
        );
        while ( my @gog_row = $gog_qry->fetchrow_array ) {
            (
                $pay_type,        $unit_quantity, $gog_type,
                $gog_description, $dlr_cost,      $cust_price
            ) = @gog_row;
           
            if ( $gog_type eq 'F' ) {
                $unit_quantity = 1;
                push(
                    @gog_section_array,
                    sprintf(
                        border(">")
                          . "%-3s%10s%7s%-6s%-26s%20s%-18s%12.2f"
                          . border("|") . "\n",
                        "", $pay_type, "", "", $gog_description, "", "",
                        ( $cust_price * $unit_quantity )
                    )
                );
            }
            else {
                push(
                    @gog_section_array,
                    sprintf(
                        border(">")
                          . "%-3s%10s%7s%-6s%-26s%5s-%10.2f%7s%-15s%12.2f"
                          . border("|") . "\n",
                        "", $pay_type, $unit_quantity, "",
                        $gog_description, '@    ' , $cust_price , '  /UNIT',
                        "", ( $cust_price * $unit_quantity )
                    )
                );

#    push(
#     @gog_section_array,
#     sprintf(
#         border(">") . "%4s%-14s%-15s%-35s%-5s%10.2f%8s%10.2f" . border("|") . "\n",
#         "", $pay_type, $unit_quantity,$gog_description,'@',$cust_price,'/UNIT', $cust_price *  $unit_quantity
#     )
#    );
            }

            $total_gog_cost = $total_gog_cost + ( $dlr_cost * $unit_quantity );
            $total_gog_sale =
              $total_gog_sale + ( $cust_price * $unit_quantity );

            if ( $pay_type eq "WARR" ) {
                $total_warranty_job_gog_cost += ( $dlr_cost * $unit_quantity );
                $total_warranty_job_gog_sale +=
                  ( $cust_price * $unit_quantity );
            }
            elsif ( $pay_type eq "CUST" ) {
                $total_customer_job_gog_cost += ( $dlr_cost * $unit_quantity );
                $total_customer_job_gog_sale +=
                  ( $cust_price * $unit_quantity );
            }
            elsif ( $pay_type eq "INTL" ) {
                $total_internal_job_gog_cost += ( $dlr_cost * $unit_quantity );
                $total_internal_job_gog_sale +=
                  ( $cust_price * $unit_quantity );
            }

            $total_gog_cost =
              $total_warranty_job_gog_cost +
              $total_customer_job_gog_cost +
              $total_internal_job_gog_cost;
            $total_gog_sale =
              $total_warranty_job_gog_sale +
              $total_customer_job_gog_sale +
              $total_internal_job_gog_sale;
        }
    }
    return @gog_section_array;
}

# Subroutine to prepare the SUBLET section of a JOB
sub make_sublet_section {
    my ( $ro_number, $ro_line ) = @_;
    my @sublet_section_array;
    my ($type) = "SUBLET";
    my $sublet_qry = $conn->prepare(
        "SELECT
                case when other_billing_type = 'Intr' then 'INTL' else upper(other_billing_type) end as other_billing_type,
                coalesce(other_cost::numeric, 0.00),
                other_code,
                other_description,
                coalesce(other_sale::numeric, 0.00),
                to_char(other_date :: date, 'mm/dd/yy') AS po_rec_date,
                vendor_inv AS vend_inv
            FROM repair_other 
            WHERE  other_sale::numeric > 0 AND ro_number = ? 
               AND ro_line=? AND item_type=?"
    );
    $sublet_qry->execute( $ro_number, $ro_line, $type );
    my (
        $pay_type,          $sublet_cost, $other_code,
        $other_description, $sublet_sale, $po_rec_date, $vend_inv
    );
    $sublet_entries = $sublet_qry->rows;

    if ( $sublet_entries > 0 ) {
        push(
            @sublet_section_array,
            sprintf(
                border(">") . "%2s %10s" . border("|") . "\n",
                "",
"SUBLET-----PO#--------VEND INV#-INV.DATE-DESCRIPTION---------------------------------------"
            )
        );
        while ( my @sublet_row = $sublet_qry->fetchrow_array ) {
            (
                $pay_type,          $sublet_cost, $other_code,
                $other_description, $sublet_sale, $po_rec_date, $vend_inv
            ) = @sublet_row;

            if ( $pay_type eq "WARR" ) {
                $total_warranty_job_sublet_cost += $sublet_cost;
                $total_warranty_job_sublet_sale += $sublet_sale;
            }
            elsif ( $pay_type eq "CUST" ) {
                $total_customer_job_sublet_cost += $sublet_cost;
                $total_customer_job_sublet_sale += $sublet_sale;
            }
            elsif ( $pay_type eq "INTL" ) {
                $total_internal_job_sublet_cost += $sublet_cost;
                $total_internal_job_sublet_sale += $sublet_sale;
            }

            $total_sublet_cost =
              $total_warranty_job_sublet_cost +
              $total_customer_job_sublet_cost +
              $total_internal_job_sublet_cost;
            $total_sublet_sale =
              $total_warranty_job_sublet_sale +
              $total_customer_job_sublet_sale +
              $total_internal_job_sublet_sale;

            push(
                @sublet_section_array,
                sprintf(
                    border(">")
                      . "%4s%-10s%-21s%-53s%14.2f"
                      . border("|") . "\n",
                    "", $pay_type, $other_code,
                    $vend_inv . " " . $po_rec_date . " " . $other_description, $sublet_sale
                )
            );
        }
    }
    return @sublet_section_array;
}

# Subroutine to prepare the MISC section of a JOB
sub make_misc_section {
    my ( $ro_number, $ro_line ) = @_;
    my @misc_section_array;
    my $misc_qry = $conn->prepare(
        "SELECT
                                       other_code,
                                       other_description,
                                       case when other_billing_type = 'Intr' then 'INTL' else upper(other_billing_type) end as other_billing_type,
                                       coalesce(other_cost::numeric, 0.00),
                                       coalesce(other_sale::numeric, 0.00)
                                  FROM repair_other
                                 WHERE 
                                 other_sale::numeric != 0  AND other_code is not null AND other_description is not null
                                 AND ro_number = ? AND ro_line= ? AND item_type = 'MISC' AND (autocalc='N' OR autocalc is null)"
                                  
    );

    $misc_qry->execute( $ro_number, $ro_line );
    my ( $other_code, $other_description, $other_billing_type, $other_cost,
        $other_sale );

    $misc_entries = $misc_qry->rows;
    if ( $misc_entries > 0 ) {
        push(
            @misc_section_array,
            sprintf(
                border(">") . "%1s %10s" . border("|") . "\n",
                "",
" MISC------CODE--------DESCRIPTION-------------------------------CONTROL NO------------------------"
            )
        );
        while ( my @misc_row = $misc_qry->fetchrow_array ) {
            (
                $other_code, $other_description, $other_billing_type,
                $other_cost, $other_sale
            ) = @misc_row;

            if ( $other_sale > 0 ) {
                if ( $other_billing_type eq "WARR" ) {
                    $total_warranty_job_misc_cost += $other_cost;
                    $total_warranty_job_misc_sale += $other_sale;
                }
                elsif ( $other_billing_type eq "CUST" ) {
                    $total_customer_job_misc_cost += $other_cost;
                    $total_customer_job_misc_sale += $other_sale;
                }
                elsif ( $other_billing_type eq "INTL" ) {
                    $total_internal_job_misc_cost += $other_cost;
                    $total_internal_job_misc_sale += $other_sale;
                }

            }

            elsif ( $other_sale < 0 ) {
                if ( $other_billing_type eq "WARR" ) {
                    $total_warranty_job_misc_disc_cost += $other_cost;
                    $total_warranty_job_misc_disc_sale += $other_sale;
                }
                elsif ( $other_billing_type eq "CUST" ) {
                    $total_customer_job_misc_disc_cost += $other_cost;
                    $total_customer_job_misc_disc_sale += $other_sale;
                }
                elsif ( $other_billing_type eq "INTL" ) {
                    $total_internal_job_misc_disc_cost += $other_cost;
                    $total_internal_job_misc_disc_sale += $other_sale;
                }

            }
            
                $total_misc_cost =
                $total_warranty_job_misc_disc_cost +
                $total_customer_job_misc_disc_cost +
                $total_internal_job_misc_disc_cost;
                $total_misc_sale =
                $total_warranty_job_misc_disc_sale +
                $total_customer_job_misc_disc_sale +
                $total_internal_job_misc_disc_sale;

                $total_misc_cost +=
                $total_warranty_job_misc_cost +
                $total_customer_job_misc_cost +
                $total_internal_job_misc_cost;
                $total_misc_sale +=
                $total_warranty_job_misc_sale +
                $total_customer_job_misc_sale +
                $total_internal_job_misc_sale;
            $Text::Wrap::columns = 40;
            my $wrapped_misc_description =
              fill( '', '', expand($other_description) );
            my @misc_description_list = split "\n", $wrapped_misc_description;
            push(
                @misc_section_array,
                sprintf(
                    border(">")
                      . "%-7s%-8s%-12s%-60s%14.2f"
                      . border("|") . "\n",
                    "",          $other_billing_type,
                    $other_code, $misc_description_list[0],
                    $other_sale
                )
            );
            foreach my $i ( 1 .. $#misc_description_list ) {
                push(
                    @misc_section_array,
                    sprintf(
                        border(">") . "%-27s%-34s%-30s" . border("|") . "\n",
                        "", $misc_description_list[$i], ""
                    )
                );
            }
        }
    }
    return @misc_section_array;
}

# Subroutine to prepare the MISC section of a JOB A
sub make_misc_joba_section {
    my ( $ro_number ) = @_;
    my @misc_a_section_array;
    my $misc_qry = $conn->prepare(
        "SELECT
                                       other_code,
                                       other_description,
                                       case when other_billing_type = 'Intr' then 'INTL' else upper(other_billing_type) end as other_billing_type,
                                       coalesce(other_cost::numeric, 0.00),
                                       coalesce(other_sale::numeric, 0.00)
                                  FROM repair_other
                                 WHERE 
                                 other_sale::numeric != 0  AND other_code is not null AND other_description is not null
                                 AND ro_number = ? AND item_type = 'MISC' AND autocalc='Y'"
    );
    $misc_qry->execute( $ro_number );
    my ( $other_code, $other_description, $other_billing_type, $other_cost,
        $other_sale );

    $misc_entries = $misc_qry->rows;
    if ( $misc_entries > 0 ) {
    	push( @misc_a_section_array,  sprintf( border("#") . " %90s " . border("|") . "\n", "" ) );
        push(
            @misc_a_section_array,
            sprintf(
                border(">") . "%1s %90s" . border("|") . "\n",
                "",
" MISC---------CODE------DESCRIPTION--------------------CONTROL NO-----------------------------------"
            )
        );
        while ( my @misc_row = $misc_qry->fetchrow_array ) {
            (
                $other_code, $other_description, $other_billing_type,
                $other_cost, $other_sale
            ) = @misc_row;

            if ( $other_sale > 0 ) {
                if ( $other_billing_type eq "WARR" ) {
                    $grand_warranty_misc_cost += $other_cost;
                    $grand_warranty_misc_sale += $other_sale;
                    $grand_warranty_cost      += $other_cost;
                    $grand_warranty_sale      += $other_sale;
                }
                elsif ( $other_billing_type eq "CUST" ) {
                    $grand_customer_misc_cost += $other_cost;
                    $grand_customer_misc_sale += $other_sale;
                    $grand_customer_cost      += $other_cost;
                    $grand_customer_sale      += $other_sale;
                }
                elsif ( $other_billing_type eq "INTL" ) {
                    $grand_internal_misc_cost += $other_cost;
                    $grand_internal_misc_sale += $other_sale;
                    $grand_internal_cost      += $other_cost;
                    $grand_internal_sale      += $other_sale;
                }

          
            }

            elsif ( $other_sale < 0 ) {
                if ( $other_billing_type eq "WARR" ) {
                    $grand_warranty_misc_disc_cost += $other_cost;
                    $grand_warranty_misc_disc_sale += $other_sale;
                    $grand_warranty_cost      += $other_cost;
                    $grand_warranty_sale      += $other_sale;
                }
                elsif ( $other_billing_type eq "CUST" ) {
                    $grand_customer_misc_disc_cost += $other_cost;
                    $grand_customer_misc_disc_sale += $other_sale;
                    $grand_customer_cost      += $other_cost;
                    $grand_customer_sale      += $other_sale;
                }
                elsif ( $other_billing_type eq "INTL" ) {
                    $grand_internal_misc_disc_cost += $other_cost;
                    $grand_internal_misc_disc_sale += $other_sale;
                    $grand_internal_cost      += $other_cost;
                    $grand_internal_sale      += $other_sale;
                }

           
            }
            $total_joba_cost += $other_cost;
            $total_joba_sale += $other_sale;              
            $Text::Wrap::columns = 40;
            my $wrapped_misc_description =
              fill( '', '', expand($other_description) );
            my @misc_description_list = split "\n", $wrapped_misc_description;
            push(
                @misc_a_section_array,
                sprintf(
                    border(">")
                      . "%-3s%-14s%-9s%-61s%14.2f"
                      . border("|") . "\n",
                    "",          'JOB # A',
                    $other_code, $misc_description_list[0],
                    $other_sale
                )
            );
            foreach my $i ( 1 .. $#misc_description_list ) {
                push(
                    @misc_a_section_array,
                    sprintf(
                        border(">") . "%-27s%-34s%-30s" . border("|") . "\n",
                        "", $misc_description_list[$i], ""
                    )
                );
            }
        }
        # push( @misc_a_section_array,  sprintf( border("#") . " %90s " . border("|") . "\n", "" ) );
        #paginate_and_print_segment(@misc_a_section_array);

    }
    return @misc_a_section_array;
}


# Subroutine to prepare the MISC section of all JOB
sub make_all_misc_section {
    my ($ro_number) = @_;
    my @chg_misc_section_array;
    my $chg_misc_qry = $conn->prepare(
        "SELECT
                                       other_code,
                                       other_description,
                                       case when other_billing_type = 'Intr' then 'INTL' else upper(other_billing_type) end as other_billing_type,
                                       coalesce(other_cost::numeric, 0.00),
                                       coalesce(other_sale::numeric, 0.00)
                                       ro_line
                                  FROM repair_other
                                 WHERE other_sale::numeric < 0 AND ro_number = ? AND item_type = 'MISC' AND other_description in (SELECT misc_desc FROM du_dms_reynoldsrci_model.etl_misc_job_a_detail)
                                 ORDER BY ro_line"
    );
    $chg_misc_qry->execute($ro_number);
    my ( $chg_other_code, $chg_other_description, $other_billing_type,
        $other_cost, $other_sale, $chg_ro_line )
      = (0) x 6;
    $chg_misc_entries = $chg_misc_qry->rows;

    if ( $chg_misc_entries > 0 ) {
        push(
            @chg_misc_section_array,
            sprintf(
                border(">")
                  . '~font{fCourier-Bold3}'
                  . "%-100s"
                  . '~font{default}'
                  . border("|") . "\n",
                ""
            )
        );
        push(
            @chg_misc_section_array,
            sprintf(
                border(">") . "%1s %10s" . border("|") . "\n",
                "",
" MISC---------CODE------DESCRIPTION--------------------CONTROL NO-----------------------------------"
            )
        );
        my @alph_array = ( "A" .. "Z" );
        while ( my @chg_misc_row = $chg_misc_qry->fetchrow_array ) {
            (
                $chg_other_code, $chg_other_description,
                $other_billing_type, $other_cost, $other_sale, $chg_ro_line
            ) = @chg_misc_row;
            my $ro_char = $alph_array[ $chg_ro_line - 1 ];
            $Text::Wrap::columns = 24;
            my $wrapped_misc_description =
              fill( '', '', expand($chg_other_description) );
            my @misc_description_list = split "\n", $wrapped_misc_description;
            push(
                @chg_misc_section_array,
                sprintf(
                    border(">")
                      . "%-4s%-14s%-9s%-60s%14.2f"
                      . border("|") . "\n",
                    "",              "JOB # " . $ro_char,
                    $chg_other_code, $misc_description_list[0],
                    $other_cost
                )
            );
            foreach my $i ( 1 .. $#misc_description_list ) {
                push(
                    @chg_misc_section_array,
                    sprintf(
                        border(">") . "%-27s%-34s%-30s" . border("|") . "\n",
                        "", $misc_description_list[$i], ""
                    )
                );
            }

            if ( $other_billing_type eq "WARR" ) {
                $positive_warranty_misc_cost += $other_cost;
                $positive_warranty_misc_sale += $other_sale;
            }
            elsif ( $other_billing_type eq "CUST" ) {
                $positive_customer_misc_cost += $other_cost;
                $positive_customer_misc_sale += $other_sale;
            }
            elsif ( $other_billing_type eq "INTL" ) {
                $positive_internal_misc_cost += $other_cost;
                $positive_internal_misc_sale += $other_sale;
            }

        }

        $grand_warranty_cost += $positive_warranty_misc_cost;
        $grand_warranty_sale += $positive_warranty_misc_sale;

        $grand_customer_cost += $positive_customer_misc_cost;
        $grand_customer_sale += $positive_customer_misc_sale;

        $grand_internal_cost += $positive_internal_misc_sale;
        $grand_internal_sale += $positive_internal_misc_cost;

        $positive_total_misc_sale =
          $positive_warranty_misc_sale +
          $positive_customer_misc_sale +
          $positive_internal_misc_sale;
        $positive_total_misc_cost =
          $positive_warranty_misc_cost +
          $positive_customer_misc_cost +
          $positive_internal_misc_cost;
    }
    return @chg_misc_section_array;
}

# Subroutine to prepare the TAX section of a JOB
sub make_tax_section {
    my ($ro_number) = @_;
    my @tax_section_array;

    my $tax_qry = $conn->prepare(
        "SELECT
                                       left(other_billing_type, 1) AS other_billing_type,
                                       other_cost,
                                       other_sale,
									   ro_line
                                   FROM repair_other
                                   WHERE ro_number = ? AND item_type = 'TAX'"
    );
    $tax_qry->execute($ro_number);
    my ( $other_billing_type, $other_cost, $other_sale, $ro_line );
    $tax_entries = $tax_qry->rows;
    if ( $tax_entries > 0 ) {
        while ( my @tax_row = $tax_qry->fetchrow_array ) {
            my ( $other_billing_type, $other_cost, $other_sale, $ro_line ) =
              @tax_row;
            $Text::Wrap::columns = 24;
            if ( $other_billing_type eq "C" ) {
                $customer_total_tax = $customer_total_tax + $other_sale;

            }
            elsif ( $other_billing_type eq "W" ) {
                $warranty_total_tax = $warranty_total_tax + $other_sale;

            }
            elsif ( $other_billing_type eq "I" ) {
                $internal_total_tax = $internal_total_tax + $other_sale;

            }

        }
    }
    if ( $other_sale > 0 ) {
        push(
            @tax_section_array,
            sprintf(
                border(">")
                  . "%-4s%-18s%-7s%7.2f%-2s%-6s%-1s%-6s%-9s%6.2f%-2s%-6s%-4s%-6s%-10s%2.2f"
                  . border("|") . "\n",
                "", "TOTAL TAX",         "", $customer_total_tax,
                "", "",                  "", "",
                "", $warranty_total_tax, "", "",
                "", "",                  "", $internal_total_tax
            )
        );
    }
    return @tax_section_array;
}

sub make_estimate_section {
    my ($ro_number) = @_;
    my @estimate_section_array;

    my $estimate_qry = $conn->prepare(
        "SELECT
            estimate_amount
        FROM repair_order
        WHERE ro_number = ? "
    );
    $estimate_qry->execute($ro_number);
    my ( $estimate_amount);
    $estimate_entries = $estimate_qry->rows;
    if ( $estimate_entries > 0 ) {
        while ( my @estimate_row = $estimate_qry->fetchrow_array ) {
            my ( $estimate_amount ) =
              @estimate_row;
            $Text::Wrap::columns = 24;
            push(
            @estimate_section_array, sprintf(
                        border(">")
                          . '~font{fCourier-Bold7}'
                          . "%2s %-95s"
                          . border("|") . "\n",
                        "",
                    "ESTIMATE--------------------------------------------------------------------------------------"
                    )
                );
            push(
            @estimate_section_array,sprintf(
                        border(">")
                          . '~font{fCourier-Bold7}'
                          . "%2s %-95s"
                          . border("|") . "\n",
                        "",
                    "CUSTOMER HEREBY ACKNOWLEDGES RECEIVING"
                    )
                );
            push(
            @estimate_section_array, sprintf(
                        border(">")
                          . '~font{fCourier-Bold7}'
                          . "%2s %8s %20s %10s %6s %-43s"
                          . border("|") . "\n",
                        "","","ORIGINAL ESTIMATE OF",currency_format($estimate_amount),"(+TAX)",""
                    )
                );

        }
    }

    return @estimate_section_array;
}



# Subroutine to prepare the Comments section of a JOB
sub make_comments_section {
    my ($ro_number) = @_;
    my @comments_section_array;
    my $comments_qry = $conn->prepare(
        "SELECT
                                       comments
                                   FROM repair_order
                                   WHERE ro_number = ? "
    );
    $comments_qry->execute($ro_number);
    my ($comments);

    $comments_entries = $comments_qry->rows;
    if ( $comments_entries > 0 ) {
        while ( my @comments_row = $comments_qry->fetchrow_array ) {
            ($comments) = @comments_row;
            if ( $comments ne '' ) {
                my $comments_len = length($comments);
                $Text::Wrap::columns = $comments_len + 1;
                my $wrapped_comments_description =
                  fill( '', '', expand($comments) );
                my @comments_description_list = split '~', $wrapped_comments_description;
                push(
                    @comments_section_array,
                    sprintf(
                        border(">") . '~font{fCourier-Bold7}' . "%-98s" . "\n",
                        ""
                    )
                );
                push(
                    @comments_section_array,
                    sprintf(
                        border(">")
                          . '~font{fCourier-Bold7}'
                          . "%2s %-95s"
                          . border("|") . "\n",
                        "",
"COMMENTS--------------------------------------------------------------------------------------"
                    )
                );
                push(
                    @comments_section_array,
                    sprintf(
                        border(">")
                          . '~font{fCourier-Bold7}'
                          . "%2s %-95s"
                          . border("|") . "\n",
                        "", $comments_description_list[0]
                    )
                );
                foreach my $i ( 1 .. $#comments_description_list ) {
                    push(
                        @comments_section_array,
                        sprintf(
                            border(">")
                              . '~font{fCourier-Bold7}'
                              . "%2s %-95s"
                              . border("|") . "\n",
                            "", $comments_description_list[$i]
                        )
                    );
                }

# push (@comments_section_array, sprintf(border(">").'~font{fCourier-Bold3}'."%-100s"."\n", ""));
            }
        }
    }

    my $recommended_serv_qry = $conn->prepare(
        "SELECT
                                       rec_serv_code,
                                       rec_serv_desc
                                   FROM repair_recommended_service_detail
                                   WHERE ro_number = ? "
    );
    $recommended_serv_qry->execute($ro_number);
    my ($recommended_serv_entries, $rec_serv_code, $rec_serv_desc);

    $recommended_serv_entries = $recommended_serv_qry->rows;
        if ( $recommended_serv_entries > 0 ) {
            if ( $comments eq '' ) {
                push(
                    @comments_section_array,
                    sprintf(
                        border(">") . '~font{fCourier-Bold7}' . "%-98s" . "\n",
                        ""
                    )
                );
                push(
                    @comments_section_array,
                    sprintf(
                        border(">")
                          . '~font{fCourier-Bold7}'
                          . "%2s %-95s"
                          . border("|") . "\n",
                        "",
"COMMENTS--------------------------------------------------------------------------------------"
                    )
                );
            }
        }
    if ( $recommended_serv_entries > 0 ) {
                push(
                    @comments_section_array,
                    sprintf(
                        border(">")
                          . '~font{fCourier-Bold7}'
                          . "%2s %-95s"
                          . border("|") . "\n",
                        "",
"RECOMMENDED NOT DONE/ DECLINED SERVICES-------------------------------------------------------"
                    )
                );
        while ( my @recommended_serv_row = $recommended_serv_qry->fetchrow_array ) {
            ($rec_serv_code, $rec_serv_desc) = @recommended_serv_row;
            push(@comments_section_array,sprintf(
                                    border(">")
                                    . '~font{fCourier-Bold7}'
                                    . "%3s%-4s%-19s%-42s%-11s%-8s"
                                    . border("|") . "\n",
                                    "","",$rec_serv_code,$rec_serv_desc,"FOLLOW-UP: ","",
                                )
                            );
            push(
                    @comments_section_array,
                    sprintf(
                        border(">") . '~font{fCourier-Bold7}' . "%-98s" . "\n",
                        ""
                    )
                );
# push (@comments_section_array, sprintf(border(">").'~font{fCourier-Bold3}'."%-100s"."\n", ""));
            }
        }

    my $recommendation_qry = $conn->prepare(
        "select 
						recommendations as TechRecommend
					     from repair_order
                                   	     WHERE ro_number = ?"
    );
    $recommendation_qry->execute($ro_number);
    my ($recommendation_list);

    $recommendation_entries = $recommendation_qry->rows;
    if ( $recommendation_entries > 0 ) {
        while ( my @recommendation_row = $recommendation_qry->fetchrow_array ) {
            ($recommendation_list) = @recommendation_row;
            if ( $recommendation_list ne '' ) {
                $Text::Wrap::columns = 62;
                push(
                    @comments_section_array,
                    sprintf(
                        border(">")
                          . '~font{fCourier-Bold7}'
                          . "%3s %-80s"
                          . border("|") . "\n",
                        "",
"RECOMMENDATIONS--------------------------------------------------------------------------------------"
                    )
                );
                my @spl = split( '::', $recommendation_list );
                foreach my $recommendation (@spl) {
                    my $wrapped_recommendation_description =
                      fill( '', '', expand($recommendation) );
                    my @recommendation_description_list = split "\n",
                      $wrapped_recommendation_description;

                    push(
                        @comments_section_array,
                        sprintf(
                            border(">")
                              . '~font{fCourier-Bold7}'
                              . "%3s %-80s"
                              . border("|") . "\n",
                            "", $recommendation_description_list[0]
                        )
                    );
                    foreach my $i ( 1 .. $#recommendation_description_list ) {
                        push(
                            @comments_section_array,
                            sprintf(
                                border(">")
                                  . '~font{fCourier-Bold7}'
                                  . "%6s%-34s%-48s"
                                  . border("|") . "\n",
                                "", $recommendation_description_list[$i], ""
                            )
                        );
                    }

                }
                push(
                    @comments_section_array,
                    sprintf(
                        border(">") . '~font{fCourier-Bold3}' . "%-100s" . "\n",
                        ""
                    )
                );
            }
        }
    }

    return @comments_section_array;
}

# Subroutine to calculate the total no of pages in the invoice
# Update page count calculation section, if any modification is made in the body of the report
sub calculate_page_count {
    my $page_count;

    #my $page_height = 0;
    my $tot_page_lines = 0;

    # Add page count calculation of job section
    foreach my $job_sub_sections (@job_section) {
        $tot_page_lines = $tot_page_lines + scalar(@$job_sub_sections);
    }

    # Add page count calculation of other sections
    $tot_page_lines = $tot_page_lines + scalar(@grand_total_section);
    $tot_page_lines = $tot_page_lines + scalar(@end_of_invoice_section);

    $page_count = ceil( $tot_page_lines / $page_max_height );

    return $page_count;
}

# Print Open/Void RO
sub print_open_ro_series {

    # $num_args = scalar(@_);
    #if($proxy_brand eq 'FCA / Fiat' || $proxy_brand eq 'FCA / Chrysler' || $proxy_brand eq 'Maserati'){
        for ( $a = $_[0] ; $a <= $_[1] ; $a = $a + 1 ) {
            my $ro_store_number = '';

            my $ro_store = $conn->prepare(
                "SELECT dms_store_id::text FROM du_dms_reynoldsrci_model.etl_invoicemaster WHERE invoicenumber  = ?"
            );
            $ro_store->execute($a);
            while (my @ro_store_row = $ro_store->fetchrow_array)
            {
               ($ro_store_number) = @ro_store_row;
            }
            if($ro_store_number eq "" || $ro_store_number eq $store_number){
                printf( FILE border("#") . " %-100s " . border("|") . "\n",
                      "Invoice Source Not Present: RO# " . $a ." * See Open and Void RO Report *\n"
                );
             } else {
                printf( FILE border("#") . " %-100s " . border("|") . "\n",
                      "Omitted Invoice # " . $a ." belongs to other Store " . $ro_store_number ."\n"
                );
             }
 
        }
    # } else {
    #    my $ro_rangeprint = $_[0]."-".$_[1];
    #    if(($_[1]-$_[0]) == 0) {
    #         printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO No# ".$_[1]." * See Open and Void RO Report *");

    #     } else {
    #         printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO Range# ".$ro_rangeprint." * See Open and Void RO Report *");

    #     }
    # }
    
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for ( my $i = 0 ; $i < $blank_lines ; $i++ ) {
        printf( FILE border("#") . "%95s" . border("|") . "\n", "" );
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
sub print_header_section {
    my ($page_num) = @_;
    my %hash = ( pg_num => $page_num );
    foreach (@header_section) {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
}

sub print_dealer_header_section {
    print_blank_line($invoice_dealer_header);
}

# Subroutine to prepare end of invoice section
sub make_end_of_invoice_section {
    push( @end_of_invoice_section,
        sprintf( border("#") . " %100s " . border("|") . "\n", "" ) )
      ;    # Separator Line
    push( @end_of_invoice_section,
        sprintf( border("#") . " %100s " . border("|") . "\n", "" ) )
      ;    # Separator Line
    push(
        @end_of_invoice_section,
        sprintf(
            border("#") . " %4s%-31s%s%32s " . border("|") . "\n",
            "", "*" x 26, "A L L  D E T A I L  I N V O I C E",
            "*" x 27
        )
    );
}

sub paginate_and_print_segment {
    my (@section_array) = @_;

    # Print section that fit in the current page
    if ( scalar(@section_array) <= ( $page_max_height - $curr_page_height ) ) {
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }

#  Print section with size longer than the space allocated ($page_max_height) to a single page
    else {
        for my $sub_section (@section_array) {
            if ( $curr_page_height < $page_max_height ) {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            }
            else {
                print FILE get_footer( 0, $curr_page_num );
                $curr_page_num = $curr_page_num + 1;
                print_dealer_header_section();
                print_header_section($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
            }
        }
    }
}

#Subroutine to prepare the FOOTER section of the invoice

sub get_footer {
    my @footer_section;
    my ( $is_end, $page_number ) = @_;
    push( @footer_section,
        sprintf( border("#") . " %100s " . border("|") . "\n", "" ) );
    if ($is_end) {

# no newline after the form-feed since a last newline is coming from somewhere and
# causing the PDF document to paginate a blank page at the end.
        push(
            @footer_section,
            sprintf(
                border("#")
                  . '~font{fCourier-Bold7}'
                  . '~color{0 0 0}'
                  . "%-2s %-25s %30s%40s%2s "
                  . border("|") . "\f",
                "",
                "PAGE $curr_page_num OF $pages",
                "ACCOUNTING COPY",
                "[  END  OF  INVOICE  ]", ""
            )
        );
    }
    else {
        push(
            @footer_section,
            sprintf(
                border("#")
                  . '~font{fCourier-Bold7}'
                  . '~color{0 0 0}'
                  . "%-2s %-25s %30s%40s%2s "
                  . border("|") . "\f",
                "",
                "PAGE $curr_page_num OF $pages",
                "ACCOUNTING COPY",
                "[  CONTINUED ON NEXT PAGE  ]", ""
            )
        );
    }
    return @footer_section;
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
    (
        $cust_lbr_cost,    $cust_lbr_sale,  $cust_prt_cost,
        $cust_prt_sale,    $cust_misc_cost, $cust_misc_sale,
        $cust_gog_cost,    $cust_gog_sale,  $cust_sublet_cost,
        $cust_sublet_sale, $cust_ded_cost,  $cust_ded_sale
    ) = (0) x 12;

    (
        $intern_lbr_cost,    $intern_lbr_sale,  $intern_prt_cost,
        $intern_prt_sale,    $intern_misc_cost, $intern_misc_sale,
        $intern_gog_cost,    $intern_gog_sale,  $intern_sublet_cost,
        $intern_sublet_sale, $intern_ded_cost,  $intern_ded_sale
    ) = (0) x 12;

    (
        $warr_lbr_cost,    $warr_lbr_sale,  $warr_prt_cost,
        $warr_prt_sale,    $warr_misc_cost, $warr_misc_sale,
        $warr_gog_cost,    $warr_gog_sale,  $warr_sublet_cost,
        $warr_sublet_sale, $warr_ded_cost,  $warr_ded_sale
    ) = (0) x 12;

    (
        $sale_amount,   $parts_cost,      $parts_sale,
        $misc_job_cost, $misc_job_sale,   $gog_job_cost,
        $gog_job_sale,  $sublet_job_cost, $sublet_job_sale,
        $ded_job_cost,  $ded_job_sale
    ) = (0) x 11;

    $job_hours = 0;

    ( $cust_ro_tax, $warr_ro_tax, $intern_ro_tax ) = (0) x 3;

    # $curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @job_section;

    ###########################################################
    undef $grand_customer_labor_cost;
    undef $grand_customer_labor_sale;
    undef $grand_customer_parts_cost;
    undef $grand_customer_parts_sale;
    undef $grand_customer_gog_cost;
    undef $grand_customer_gog_sale;
    undef $grand_customer_misc_cost;
    undef $grand_customer_misc_sale;
    undef $grand_customer_misc_disc_cost;
    undef $grand_customer_misc_disc_sale;
    undef $grand_customer_sublet_cost;
    undef $grand_customer_sublet_sale;
    undef $grand_customer_cost;
    undef $grand_customer_sale;

    undef $grand_warranty_labor_cost;
    undef $grand_warranty_labor_sale;
    undef $grand_warranty_parts_cost;
    undef $grand_warranty_parts_sale;
    undef $grand_warranty_gog_cost;
    undef $grand_warranty_gog_sale;
    undef $grand_warranty_misc_cost;
    undef $grand_warranty_misc_sale;
    undef $grand_warranty_misc_disc_cost;
    undef $grand_warranty_misc_disc_sale;
    undef $grand_warranty_sublet_cost;
    undef $grand_warranty_sublet_sale;
    undef $grand_warranty_cost;
    undef $grand_warranty_sale;
    undef $total_joba_cost;
    undef $total_joba_sale;

    undef $grand_internal_labor_cost;
    undef $grand_internal_labor_sale;
    undef $grand_internal_parts_cost;
    undef $grand_internal_parts_sale;
    undef $grand_internal_gog_cost;
    undef $grand_internal_gog_sale;
    undef $grand_internal_misc_cost;
    undef $grand_internal_misc_sale;
    undef $grand_internal_misc_disc_cost;
    undef $grand_internal_misc_disc_sale;
    undef $grand_internal_sublet_cost;
    undef $grand_internal_sublet_sale;
    undef $grand_internal_cost;
    undef $grand_internal_sale;

    undef $positive_warranty_misc_sale;
    undef $positive_warranty_misc_cost;
    undef $positive_customer_misc_sale;
    undef $positive_customer_misc_cost;
    undef $positive_internal_misc_sale;
    undef $positive_internal_misc_cost;
    undef $positive_total_misc_sale;
    undef $positive_total_misc_cost;

    ########## Prepare invoice ##########
    make_header();
    $extra_tech_line = 0;
    make_job_section();
    make_grand_total_section();
    $pages = calculate_page_count();
    ########## Print invoice ##########
    print_dealer_header_section();
    $curr_page_height = 0;
    print_header_section($curr_page_num);

    # Pagination of Job section
    for my $js (@job_section) {
        paginate_and_print_segment(@$js);
    }

    # Pagination of Grand total section
    paginate_and_print_segment(@grand_total_section);

    if ( $curr_page_height == $page_max_height ) {
        print FILE get_footer( 1, $curr_page_num );
    }
    else {
        print_blank_line( $page_max_height - $curr_page_height );
        print FILE get_footer( 1, $curr_page_num );
    }

}

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if ($debug_mode) {
        return $border_char;
    }
    else {
        return " ";
    }
}

sub print_ro_inv_bg_head {
    printf( FILE $cust_inv_head );
}

sub currency_format {

    #use Number::Format qw(:subs);
    my $precision = 2;
    my $symbol    = '';
    my ($number)  = @_;
    my $formatted = format_price( $number, $precision, $symbol );
    return '$' . $formatted;
}

sub amount_format{
    use Number::Format qw(:subs);
    my $precision = 2;
    my $symbol    = '';
    my ($amounts) = @_;
    my $amount_formatted;
    if ( $amounts == 0 || $amounts == 0.00){
        $amount_formatted = "0.00";
    }
    else{
        $amount_formatted = format_price( $amounts, $precision, $symbol );
        $amount_formatted =~ s/,//g;
    }
    return  $amount_formatted;
}

sub plain_amount_format{
    use Number::Format qw(:subs);
    my $precision = 2;
    my $symbol    = '';
    my ($plain_amounts) = @_;
    my $plain_amount_formatted;
    if ( $plain_amounts == 0 || $plain_amounts == 0.00){
        $plain_amount_formatted = "";
    }
    else{
        $plain_amount_formatted = format_price( $plain_amounts, $precision, $symbol );
        $plain_amount_formatted =~ s/,//g;
    }
    return  $plain_amount_formatted;
}


sub kit_amount_format{
    use Number::Format qw(:subs);
    my $precision = 2;
    my $symbol    = '';
    my ($kit_amounts, $kit_status) = @_;
    my $kit_amount_formatted;
    if ( $kit_status ne "" && ($kit_amounts == 0 || $kit_amounts == 0.00) ){
        $kit_amount_formatted = "****";
    }
    else{
        $kit_amount_formatted = format_price( $kit_amounts, $precision, $symbol );
        $kit_amount_formatted =~ s/,//g;
    }
    return  $kit_amount_formatted;
}

# Print Open/Void RO
sub print_open_ro {
    printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO# ".$ro_number." * See Open and Void RO Report *");
}

sub remove_negative_sign_penny {
	my ($amount) = @_;
    my $amount_formatted = $amount;
    if ( $amount < 0 && $amount > -0.000001){
        $amount_formatted =~ s/^-//g;
    }
    return $amount_formatted;
}
