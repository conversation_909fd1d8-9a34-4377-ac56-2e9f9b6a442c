
-- This function execute in the etl public schema which is used to delete import schema using cron


CREATE OR REPLACE FUNCTION public.drop_old_schemas() RET<PERSON>NS void AS $$
DECLARE
    schema_name text;
BEGIN
    -- Loop through schemas that match the pattern and are older (OID condition)
    FOR schema_name IN 
        SELECT nspname FROM pg_namespace 
        WHERE nspname ~ '^du_dms_[a-zA-Z0-9]{30,}_[0-9]{7,}'
        AND oid::int < (SELECT MAX(oid)::int FROM pg_namespace) - 20
    LOOP
        -- Drop schema only if it exists
        EXECUTE format('DROP SCHEMA IF EXISTS %I CASCADE;', schema_name);
        RAISE NOTICE 'Dropped schema: %', schema_name;
    END LOOP;
END;
$$ LANGUAGE plpgsql;