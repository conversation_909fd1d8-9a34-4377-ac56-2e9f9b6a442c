#!/bin/bash

TARGET_DIR="/etl/audit-dispatch-bundle"
LOG_FILE="/etl/log/audit-dispatch-bundle.txt"
DAYS_OLD=220
echo $(date +'%Y%m%d_%H%M%S') >> "$LOG_FILE"
echo "Deleted files  on $(date +'%Y%m%d_%H%M%S'):" >> "$LOG_FILE"

echo "Deleted files:" >> "$LOG_FILE"
find "$TARGET_DIR" -maxdepth 1 -type f -name "*.zip" -mtime +$DAYS_OLD -print -delete >> "$LOG_FILE"

echo "-----------------------delete files from auctual table---------------------" >> "$LOG_FILE"

psql "service=audit_etl_local" -c "UPDATE invoice_master.actual_ro_import SET is_removed = TRUE WHERE created_on = CURRENT_DATE - INTERVAL '${DAYS_OLD} days';" >> "$LOG_FILE"

echo "-----------------------delete files from proxy table---------------------" >> "$LOG_FILE"

psql "service=audit_etl_local" -c "UPDATE invoice_master.proxy_ro_import SET is_removed = TRUE WHERE created_on = CURRENT_DATE - INTERVAL '${DAYS_OLD} days';" >> "$LOG_FILE"
