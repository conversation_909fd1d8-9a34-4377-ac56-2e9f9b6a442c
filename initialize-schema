#!/usr/bin/env bash

SCHEMA_NAME="${1:?Schema Name Required}"

function psql_proxy() {
    psql "service=$DU_ETL_PG_SERVICE" --quiet --set=ON_ERROR_STOP=1 "$@"
}

psql_proxy --set=schema_name="$SCHEMA_NAME" <<SQL
    SET client_min_messages = warning;
    DROP SCHEMA IF EXISTS :"schema_name" CASCADE;
    CREATE SCHEMA :"schema_name";
SQL

function psql_proxy_script() {
    psql --quiet "service=$DU_ETL_PG_SERVICE options='-c search_path=${SCHEMA_NAME}'" \
         --set=ON_ERROR_STOP=1 "$@"
}

psql_proxy_script --file ./src/main/psql/create-schema.psql

exit
