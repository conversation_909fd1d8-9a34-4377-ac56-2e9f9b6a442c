#!/usr/bin/env bash

set -o errexit
set -o nounset
set -o pipefail
shopt -s failglob

txt_reset='\e[0m'
txt_red='\e[1;31m'
txt_green='\e[1;32m'
txt_yellow='\e[1;33m'

# Installs sub-repos and configurations

# Location for DU_ETL_HOME
REPO_ROOT="$(cd $(dirname $0) && pwd)"

# Location (and link to) DU_ETL_WORK
ETL_WORK_DIR="$HOME"/du-etl-work
OPT_SHARED_WORK_DIR=/mnt/du-etl-work

PGSERVICE_DIRECTORY="$HOME"/pg-service.d
PG_SVC_LOCAL=du_etl_local
PG_DB_NAME=DU-ETL
PG_CFG_LOCAL=du-etl-local.conf
BASHRC_DIRECTORY="$HOME"/.bashrc.d
BASH_CFG=du-etl.bash

function main() {
    link_shared_etl_work_dir                  # vagrant oriented
    ensure_du_etl_psql_environment_exists     # assumes service=postgres and pg_service.d exists
    ensure_du_etl_bash_environment_exists     # assumes .bashrc.d exists; with .bashrc code to iterate over its contents
    export DU_ETL_HOME="$REPO_ROOT"           # view-git-status wants to know
    ./view-git-status --clone                 # centralizes knowledge of in-scope repos
    warn_when_jq_is_not_installed
    warn_when_csvkit_commands_are_not_installed
    warn_when_enscript_is_not_installed
    warn_when_ps2pdf_is_not_installed
    warn_when_pdftk_is_not_installed
    warn_when_pdf2txt_is_not_installed
    warn_when_bats_is_not_installed
    warn_when_perl_trim_is_not_installed
    warn_when_perl_date_manip_is_not_installed
    warn_when_ggs_local_not_present
    warn_when_ggs_prod_not_present
    warn_when_production_etl_load_dirs_not_present
}

function die() {
    echo -e "${txt_red}$@${txt_reset}"
    exit 1
}

function psql_bootstrap() {
    psql "service=postgres" --set=ON_ERROR_STOP=1 "$@"
}

function link_shared_etl_work_dir() {
    if [[ ! -e "$ETL_WORK_DIR" ]]; then
        if [[ -d "$OPT_SHARED_WORK_DIR" ]]; then
            ln -s "$OPT_SHARED_WORK_DIR" "$ETL_WORK_DIR"
        else
             echo "Shared Work Directory Not Mounted"
        fi
    else
        echo "Link Target Already Exists"
    fi
}

function ensure_du_etl_psql_environment_exists() {
    # Remove obsolete file
    if [[ -e "$PGSERVICE_DIRECTORY"/"$PG_CFG_LOCAL" ]]; then
        rm "$PGSERVICE_DIRECTORY"/"$PG_CFG_LOCAL"
    fi

    cat > "$PGSERVICE_DIRECTORY"/"$PG_CFG_LOCAL" \
<<PG
[${PG_SVC_LOCAL}]
dbname=${PG_DB_NAME}
user=postgres
PG
    cat "$PGSERVICE_DIRECTORY"/* > ~/.pg_service.conf
    psql_bootstrap -c "CREATE DATABASE \"${PG_DB_NAME}\";" \
        || echo "Ignoring DB Already Exists..."
    # The database must exist but the various modules
    # take care of creating their own schema.
}

function ensure_du_etl_bash_environment_exists() {
    cat > "$BASHRC_DIRECTORY"/"$BASH_CFG" \
<<BASH
export DU_ETL_HOME="${REPO_ROOT}"
export DU_ETL_WORK="${ETL_WORK_DIR}"
export DU_ETL_PG_SERVICE="${PG_SVC_LOCAL}"
export DU_ETL_SOLVE360_SERVICE="\${DU_ETL_PG_SERVICE}"
source "${REPO_ROOT}/DU-Solve360/bashrc"
source "${REPO_ROOT}/src/install/bashrc-to-source-dms-bashrcs"
BASH
}

function warn_when_csvkit_commands_are_not_installed() {
    if which csvclean >/dev/null; then
        echo -e "${txt_green}found csvclean${txt_reset}"
    else
        echo -e "${txt_yellow}csvclean not found, please install python3-csvkit (18.04 csvkit) package${txt_reset}"
    fi
}

function warn_when_enscript_is_not_installed() {
    if which enscript >/dev/null; then
        echo -e "${txt_green}found enscript${txt_reset}"
    else
        echo -e "${txt_yellow}enscript not found, please install enscript package${txt_reset}"
    fi
}

function warn_when_ps2pdf_is_not_installed() {
    if which ps2pdf >/dev/null; then
        echo -e "${txt_green}found ps2pdf${txt_reset}"
    else
        echo -e "${txt_yellow}ps2pdf not found, please install ps2pdf${txt_reset}"
    fi
}

function warn_when_pdftk_is_not_installed() {
    if which pdftk >/dev/null; then
        echo -e "${txt_green}found pdftk${txt_reset}"
    else
        echo -e "${txt_yellow}pdftk not found, please install (18:04 ppa:malteworld/ppa) pdftk${txt_reset}"
    fi
}

function warn_when_pdf2txt_is_not_installed() {
    if which pdf2txt >/dev/null; then
        echo -e "${txt_green}found pdf2txt${txt_reset}"
    else
        echo -e "${txt_yellow}pdf2txt not found, please install python-pdfminer${txt_reset}"
    fi
}

function warn_when_jq_is_not_installed() {
    if which jq >/dev/null; then
        echo -e "${txt_green}found jq${txt_reset}"
    else
        echo -e "${txt_yellow}jq not found, please install jq package${txt_reset}"
    fi
}

function warn_when_bats_is_not_installed() {
    if which bats >/dev/null; then
        echo -e "${txt_green}found bats${txt_reset}"
    else
        echo -e "${txt_yellow}bats not found, please install bats package${txt_reset}"
    fi
}

function warn_when_perl_trim_is_not_installed() {
    local result=$(perl -MText::Trim -e 'print trim("    hello, world    ");')
    if [[ $? -eq 0 ]]; then
        if [[ "$result" = 'hello, world' ]]; then
            echo -e "${txt_green}found perl Text::Trim${txt_reset}"
        else
            echo -e "${txt_yellow}inconsistent perl Text::Trim output: ${result}${txt_reset}"
        fi
    else
        echo -e "${txt_yellow}perl Text::Trim not found, please install libtext-trim-perl package${txt_reset}"
    fi
}

function warn_when_perl_date_manip_is_not_installed() {
    local result=$(perl -MDate::Manip -e 'use Date::Manip qw(ParseDate UnixDate); print UnixDate(ParseDate("10/3/1981"),"%m/%d/%y");')
    if [[ $? -eq 0 ]]; then
        if [[ "$result" = '10/03/81' ]]; then
            echo -e "${txt_green}found perl Date::Manip${txt_reset}"
        else
            echo -e "${txt_yellow}inconsistent perl Date::Manip output: ${result}${txt_reset}"
        fi
    else
        echo -e "${txt_yellow}perl Date::Manip not found, please install libdate-manip-perl package${txt_reset}"
    fi
}

function warn_when_ggs_local_not_present() {
    if [[ "${GGS_LOCAL_PG_SERVICE:-none}" = 'none' ]]; then
        echo -e "${txt_yellow}GGS_LOCAL_PG_SERVICE not found, please install GGSMigration repo${txt_reset}"
        return 0
    fi
    if psql "service=$GGS_LOCAL_PG_SERVICE" -c 'SELECT 1;' >/dev/null; then
        echo -e "${txt_green}pg_service $GGS_LOCAL_PG_SERVICE found${txt_reset}"
    else
        echo -e "${txt_yellow}pg_service $GGS_LOCAL_PG_SERVICE not found, please check GGSMigration repo installation${txt_reset}"
    fi
}

function warn_when_ggs_prod_not_present() {
    if [[ "${GGS_PROD_PG_SERVICE:-none}" = 'none' ]]; then
        echo -e "${txt_yellow}GGS_PROD_PG_SERVICE not found, please add file to bashrc.d${txt_reset}"
        return 0
    fi
    timeout 3s psql "service=${GGS_PROD_PG_SERVICE}" -c 'SELECT 1;' >/dev/null;
    local exitcode=$?
    if [[ $exitcode = '0' ]]; then
        echo -e "${txt_green}pg_service $GGS_PROD_PG_SERVICE found${txt_reset}"
    elif [[ $exitcode = '124' ]]; then
        echo -e "${txt_yellow}pg_service $GGS_PROD_PG_SERVICE connection attempt timed out after 3s, please fix access${txt_reset}"
    elif [[ $exitcode = '2' ]]; then
        echo -e "${txt_yellow}pg_service $GGS_PROD_PG_SERVICE not found, please add file to pg-service.d${txt_reset}"
    fi
}

function warn_when_production_etl_load_dirs_not_present() {
    if [[ "${DU_ETL_DIST_DIR:-none}" = 'none' ]]; then
        echo -e "${txt_yellow}DU_ETL_DIST_DIR not found, please add file to bashrc.d${txt_reset}"
    else
        if [[ ! -d "$DU_ETL_DIST_DIR" ]]; then
            echo -e "${txt_yellow}$DU_ETL_DIST_DIR not present, production loading will fail${txt_reset}"
        else
            echo -e "${txt_green}Production distribution files will go to $DU_ETL_DIST_DIR${txt_reset}"
        fi
    fi
    if [[ "${DU_MAPPER_ETLMAP_I:-none}" = 'none' ]]; then
        echo -e "${txt_yellow}DU_MAPPER_ETLMAP_I not found, please add file to bashrc.d${txt_reset}"
    else
        if [[ ! -d "$DU_MAPPER_ETLMAP_I" ]]; then
            echo -e "${txt_yellow}$DU_MAPPER_ETLMAP_I not present, etl-<dms> mapper export will fail${txt_reset}"
        else
            echo -e "${txt_green}etl-<dms> mapper files will be place in $DU_MAPPER_ETLMAP_I${txt_reset}"
        fi
    fi
    if [[ ! -d /etl ]]; then
        echo -e "${txt_yellow}Production /etl directory not found, etl-transform and DU-Load/do-load-and-distribute will not work${txt_reset}"
    else
        if [[ ! -d /etl/load-in ]]; then
            echo -e "${txt_yellow}Production /etl/load-in directory not found, etl-transform and DU-Load/do-load-and-distribute will not work${txt_reset}"
        else
            echo -e "${txt_green}Production /etl directories present${txt_reset}"
        fi
    fi
}

main || die "Installation Failed!"
exit 0
