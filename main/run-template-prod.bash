# Production version of run-template.bash

clear

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/etl/bash/etl-command-functions.bash

mkdir -p ~/tmp

# General Setup
SCENARIOKEY=
CONFIG_FILE=
USERFILE=
PERMANENT_ETI='false'

LOAD_IN=/etl/load-in
LOAD_ARCHIVE=/etl/load-archive

MAPPER_DIR=/etl/mapper-in
MAPPER_ARCHIVE=/etl/mapper-archive

export RUN_TEST=false

DIR="$(dirname "$(readlink -f "$0")")"

function usage() {
    yell "$@"
    cat <<EOF
        --use-config        <filepath>  Obtain variables by sourcing in named file
                            Defaults to prompt for config file
EOF
    exit 1
} >&2

function help_select_config_file() {
    pre_help
    display_help <<HELP
SELECT CONFIGURATION FILE
=========================
Configuration files contain the settings for the import process.
They are generated from Solve360.
Select Refresh to refresh the list of files.
Select Quit to quit this program.
Otherwise, select a configuration file from options shown.
HELP
    post_help
}

function select_config_file() {
    local prompt
    local files
    local dms

    while :
    do
        if [[ -e "$DU_ETL_CONFIG_DIR/dms.env" ]]; then
            dms=$(< $DU_ETL_CONFIG_DIR/dms.env)
            prompt="[$dms] Please select a configuration file:"
            refresh_solve360 $dms
        else
            prompt="[?] Please Swtich to a DMS:"
        fi

        PS3="$prompt "
        files=( $(find "$DU_ETL_CONFIG_DIR" -maxdepth 1 -type f ! -name "dms.env" -printf '%f\n' | sort | xargs -0) )

        select opt in "Switch to SIS" "Switch to ADV" "Switch to Reynolds" "Switch to CDK" "Switch to AutoSoft" "Switch to CDKDash" "Switch to AutoNation" "Switch to KenGarff" "Switch to DPC" "Switch to Quorum" "Switch to UCS" "Switch to DealerBuilt" "Switch to PBS" "Switch to CDK3PA" "Switch to DealerTrack" "Switch to Advantage" "Switch to AutoMate" "Switch to MPK" "Switch to Dominion" "Switch to Adam" "Switch to Tekion" "Switch to Reynolds RCI" "Switch to CDK-FLEX" "Switch to DominionVue" "Switch to TekionAPI" "Show Errors" "${files[@]}" "Quit" "?"
        do
            if [[ "$opt" = "Quit" ]]; then
                exit 99
            elif [[ "$opt" = "Switch to SIS" ]]; then
                refresh_solve360 "SIS"
            elif [[ "$opt" = "Switch to ADV" ]]; then
                refresh_solve360 "ADV"
            elif [[ $opt = "Switch to Reynolds" ]]; then
                refresh_solve360 "Reynolds"
            elif [[ $opt = "Switch to CDKDash" ]]; then
                refresh_solve360 "CDKDash"
            elif [[ $opt = "Switch to CDK" ]]; then
                refresh_solve360 "CDK"
            elif [[ $opt = 'Switch to AutoNation' ]]; then
                refresh_solve360 "AutoNation"
            elif [[ $opt = 'Switch to KenGarff' ]]; then
                refresh_solve360 "KenGarff"
            elif [[ "$opt" = "Switch to DPC" ]]; then
                refresh_solve360 "DPC"
            elif [[ "$opt" = "Switch to Quorum" ]]; then
                refresh_solve360 "Quorum"
            elif [[ "$opt" = "Switch to UCS" ]]; then
                refresh_solve360 "UCS"
            elif [[ "$opt" = "Switch to DealerBuilt" ]]; then
                refresh_solve360 "DealerBuilt"
            elif [[ "$opt" = "Switch to PBS" ]]; then
                refresh_solve360 "PBS"
            elif [[ "$opt" = "Switch to AutoSoft" ]]; then
                refresh_solve360 "AutoSoft"
            elif [[ "$opt" = "Switch to CDK3PA" ]]; then
                refresh_solve360 "CDK3PA"
            elif [[ "$opt" = "Switch to DealerTrack" ]]; then
                refresh_solve360 "DealerTrack"
            elif [[ "$opt" = "Switch to Advantage" ]]; then
                refresh_solve360 "Advantage"
            elif [[ "$opt" = "Switch to AutoMate" ]]; then
                refresh_solve360 "AutoMate"
            elif [[ "$opt" = "Switch to MPK" ]]; then
                refresh_solve360 "MPK"
            elif [[ "$opt" = "Switch to Dominion" ]]; then
                refresh_solve360 "Dominion"
            elif [[ "$opt" = "Switch to Adam" ]]; then
                refresh_solve360 "ADAM"
            elif [[ "$opt" = "Switch to Tekion" ]]; then
                refresh_solve360 "Tekion"
            elif [[ "$opt" = "Switch to Reynolds RCI" ]]; then
                refresh_solve360 "ReynoldsRCI"
            elif [[ "$opt" = "Switch to CDK-FLEX" ]]; then
                refresh_solve360 "CDKFLEX"
            elif [[ "$opt" = "Switch to DominionVue" ]]; then
                refresh_solve360 "DominionVue"
            elif [[ "$opt" = "Switch to Tekion API" ]]; then
                refresh_solve360 "TekionAPI"
            elif [[ "$opt" = "Show Errors" ]]; then
                 show_solve360_errors
            elif [[ $opt = "?" ]]; then
                help_select_config_file
            elif [[ $REPLY =~ ^[0-9]+$ ]]; then
                if [[ -e "$DU_ETL_CONFIG_DIR"/"${opt}" ]]; then
                    CONFIG_FILE="${DU_ETL_CONFIG_DIR}/${opt}"
                    "$DU_ETL_HOME"/DU-Solve360/present-config-to-user "$CONFIG_FILE"
                    say "Current selection # ${REPLY}: ${opt}"
                    prompt_retry "Do you wish to change your selection?"
                    if [[ "$RETRY_ANSWER" = true ]]; then
                        CONFIG_FILE=
                    else
                        return
                    fi
                else
                    yell "Invalid Selection [ ${REPLY} := ${opt} ]"
                fi
            else
                yell "Invalid option [ ${REPLY} ]"
          fi
          break
        done
    done
}

function refresh_solve360() {
    local dms="${1}"
    (
        cd "$DU_ETL_HOME/DU-Solve360"
        ./src/main/bash/process.bash --target-dms "${dms}" --target-dir "${DU_ETL_CONFIG_DIR}" || die "DMS Switching Failed"
        echo "${dms}" > "$DU_ETL_CONFIG_DIR/dms.env"
    )
}

function show_solve360_errors() {
    (
        cd "$DU_ETL_HOME/DU-Solve360"
        ./src/main/bash/print-errors.bash
    )
}


while [ $# -gt 0 ]; do
    case "$1" in
    --use-config)
        if [[ -e "${2}" ]]; then
        CONFIG_FILE="${2}"
           die "Configuration File Name Required"
        fi
        shift 2
        ;;
    *)
        usage "Unrecognized option $1; exiting"
        exit 1
        ;;
    esac
done

if [[ ! -f "$CONFIG_FILE" ]]; then
    select_config_file
fi

# Pretend we are doing --distribute-only since as far as the overall process is
# concerned we are.  Without this the "else" branch in the configuration file
# is executed and GROUP_CODE is overridden to become, for instance, SISTEST.
# Since the configuration file computes the SCENARIOKEY used below this is
# annoying.
# This has the unfortunate side-effect of requiring GGS_PROD_PG_SERVICE to be
# defined on the non-Load ETL machine.  However, since only DU-Load makes
# use of this envar in practice the actual location pointed to by GGS_PROD_PG_SERVICE
# can be the local, non-production, version, the same as GGS_LOCAL_PG_SERVICE.
DISTRIBUTE_ONLY='true'
source "$CONFIG_FILE"

if [[ -n "${ETI}" ]]; then
    PERMANENT_ETI='true'
fi
finalize_interactive_configuration
reset_working_directories

cp ${CONFIG_FILE} ${TLI}/config.bash
CONFIG_FILE="${TLI}/config.bash"
export SCENARIOKEY

MACHINE="$(hostname)"
echo -e "\nMACHINE=${MACHINE}" >> ${CONFIG_FILE}

PAYTYPE_MODEL_SCRIPT_OVERRIDE="${DU_ETL_HOME}"/"${DMS_HOME}"/src/template/run-functions.bash
PAYTYPE_MODEL_SCRIPT_CLIENT="${DU_ETL_HOME}"/DU-Transform/client/paytypes/default-rule-population-functions.bash
PAYTYPE_MODEL_SCRIPT=

if [[ -f "$PAYTYPE_MODEL_SCRIPT_OVERRIDE" ]]; then
    PAYTYPE_MODEL_SCRIPT="$PAYTYPE_MODEL_SCRIPT_OVERRIDE"
else
    PAYTYPE_MODEL_SCRIPT="$PAYTYPE_MODEL_SCRIPT_CLIENT"
fi

source "$PAYTYPE_MODEL_SCRIPT"

export DU_ETL_DEBUG_TARGET="${BASE_DIR}"/etl.log
export DU_ETL_DEBUG_MODE=2
export DMS_HOME

debug "Beginning $DMS Processing @ $(date)"

if [[ "${PROMPT_FOR_FILE:='false'}" = 'true' ]]; then
    perform_zip_file_selection_and_parse   || die "Zip File Processing Failed"
else
    type 'stage_data_for_parsing' &>/dev/null && \
        stage_data_for_parsing
    perform_unattended_parse               || die "Parsing Failed"
fi
du_paytype_sub --initialize --done     || die "Paytype Initialization Failed"

populate_parts_rules                   || die "Parts Rule Population Failed" # Sourced In
type 'add_additional_parts_paytype_rules' &>/dev/null && \
    add_additional_parts_paytype_rules

populate_labor_rules                   || die "Labor Rule Population Failed" # Sourced In
type 'add_additional_labor_paytype_rules' &>/dev/null && \
    add_additional_labor_paytype_rules

du_transform --process                  \
             --done                     \
        || die "Processing Failed"

du_transform --status                   --done   || die "Status display failed..."

echo -n -e "${txt_data_table}"
echo "Config File : ${CONFIG_FILE}"
echo "Source File : $(basename $(< ${TW}/meta/source-files.txt))"
echo "**********************************************************************"
echo -n -e "${t_reset}"

ask "Processing complete. Continue with export [Y/n]?" "[nNyY]" "Y"
if [[ "${ANSWER^^}" = "N" ]]; then
    exit 0
fi

du_transform --set-target "${TLI}" \
             --export                   --done   || die "Exporting of Standard Files failed"

if [[ -d "${MAPPER_DIR}" ]]; then
    du_export_mapper --output-dir "${MAPPER_ARCHIVE}" || die "Exporting mapper files failed"
    cp "${MAPPER_ARCHIVE}"/"${SCENARIOKEY}.zip" "${MAPPER_DIR}"/"${SCENARIOKEY}.tmp"
    mv "${MAPPER_DIR}"/"${SCENARIOKEY}.tmp" "${MAPPER_DIR}"/"${SCENARIOKEY}.zip"
fi

(
    cd ${BASE_DIR}
    zip -qr "${LOAD_IN}/${SCENARIOKEY}.tmp" $(basename ${TLI})
    cp "${LOAD_IN}/${SCENARIOKEY}.tmp" "${LOAD_ARCHIVE}/${SCENARIOKEY}.zip"
    mv "${LOAD_IN}/${SCENARIOKEY}.tmp" "${LOAD_IN}/${SCENARIOKEY}.zip"
)

archive_source_file
rm "${CONFIG_FILE}"
exit 0
