# The various run-DMS.bash scripts are all expected to source
# this script once they have setup the environment to their liking.
# Mainly that involves setting DMS and DMS_HOME
# DMS Setups also include implementing any desired
# Hooks that are defined within this template.

# Implementations can rely upon these globals and functions
# being present.
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/etl/bash/etl-command-functions.bash

mkdir -p ~/tmp

# set -o nounset
# Setup known variables - whose values are likely to be
# be established during execution; or via the command-line

# General Setup
SCENARIOKEY=
USERFILE=
USER_CONFIG_FILE=

USE_CONFIG=false
export RUN_TEST=false

# Pertains only to the --test flow and toggles
# whether pre-defined values for various user
# inputs are to be chosen or if the user will
# be presented with the relevant UI to input
# their own data.
PROCESS_MANUAL=false

# Loading can happen via DISTRIBUTE_ONLY or by
# performing a full transform-and-load.  This
# flag indicates that at the end of the transform
# step the choice to Load the data was affirmed.
TEST_LOAD=false

# A bit of a misnomer since it also LOADs the data
# into production before performing the distribution
# of the just-loaded data.
# The point of this is to re-use the immediately
# preceeding TRANSFORM result but place it into
# a different target database.
DISTRIBUTE_ONLY=false

# In addition to distribution it is necessary to
# generate the DU-Mapper zip file.  This flag
# instructs the system to do just that.
EXPORT_MAPPER=false

# We want to detect whether the ETI directory is
# a permanent one - i.e., supplied by the caller -
# or one computed on-the-fly by run-template.
# A permanent directory will not be cleared out
# during initialization.
PERMANENT_ETI='false'

# General Loading

DIR="$(dirname "$(readlink -f "$0")")"

function usage() {
    yell "$@"
    cat <<EOF
    --test                           Source a pre-defined script for a test execution
    --test-file      <filepath>      Obtain variables by sourcing in named file for auto test
    --use-config     <filepath>      Obtain variables by sourcing in named file
    --distribute-only                Assuming one has successfully completed a --use-config
                                     run appending this option will re-use the same Transformation
                                     results but will Load and Distribute them elsewhere.
                                     The config-file is expected to set environment variables
                                     based upon the true/false value of DISTRIBUTE_ONLY
    --export-mapper                  Export the DU-Mapper Zip File for the last processed scenario
    --process-manual                 Test-only toggle which when set causes the various
                                     user-interfaces to be presented during the ETL process.
                                     Without this the testing code is expected to run fully-automated.
EOF
    exit 1
} >&2

if [[ "$#" = '0' ]]; then
    usage "Invalid specification: one of --use-config <filepath> OR --test OR --test-file <filepath> required"
fi


while [ $# -gt 0 ]; do
    case "$1" in
    --use-config)
        USE_CONFIG=true
        # If the next argument is existing file
        # assume it is the location of a config file
        # and store it.
        if [[ -e "${2}" ]]; then
            USER_CONFIG_FILE="${2}"
        else
            die "Configuration File Name Required"
        fi
        shift 2
        ;;
    --test)
        export RUN_TEST=true
        TEST_FILE="$DU_ETL_HOME"/"$DMS_HOME"/src/test/load-test-data-auto.bash
        shift 1
        ;;
    --test-file)
        export RUN_TEST=true
        if [[ -e "${2}" ]]; then
            TEST_FILE="${2}"
        else
            die "Test File Name Required"
        fi
        shift 2
        ;;
    --process-manual)
        PROCESS_MANUAL=true
        shift 1
        ;;
    --distribute-only)
        DISTRIBUTE_ONLY=true
        EXPORT_MAPPER=true
        shift 1
        ;;
    --export-mapper)
        EXPORT_MAPPER=true
        shift 1
        ;;
    --)
        shift 1
        ;;
    *)
        usage "Unrecognized option $1; exiting"
        exit 1
    esac
done

# Either we are in Test mode or we expect to have a SCENARIOKEY
# defined at this point.
if [[ "$USE_CONFIG" = 'true' ]]; then
    if [[ "$EXPORT_MAPPER" = 'true' ]]; then
        # Mapper export to be performed relative to production keys
        actual_distribute_only="$DISTRIBUTE_ONLY"
        DISTRIBUTE_ONLY='true'
        source "$USER_CONFIG_FILE"
        DISTRIBUTE_ONLY="$actual_distribute_only"
    else
        source "$USER_CONFIG_FILE"
    fi

    if [[ -n "${ETI}" ]]; then
        PERMANENT_ETI='true'
    fi
    finalize_interactive_configuration
    if [[ "$DISTRIBUTE_ONLY" = 'false' ]] && [[ "$EXPORT_MAPPER" = 'false' ]]; then
        reset_working_directories
    fi

    PAYTYPE_MODEL_SCRIPT_OVERRIDE="${DU_ETL_HOME}"/"${DMS_HOME}"/src/template/run-functions.bash
    PAYTYPE_MODEL_SCRIPT_CLIENT="${DU_ETL_HOME}"/DU-Transform/client/paytypes/default-rule-population-functions.bash
    PAYTYPE_MODEL_SCRIPT=

    if [[ -f "$PAYTYPE_MODEL_SCRIPT_OVERRIDE" ]]; then
        PAYTYPE_MODEL_SCRIPT="$PAYTYPE_MODEL_SCRIPT_OVERRIDE"
    else
        PAYTYPE_MODEL_SCRIPT="$PAYTYPE_MODEL_SCRIPT_CLIENT"
    fi

    source "$PAYTYPE_MODEL_SCRIPT"

elif [ -n "${TEST_FILE}" ]; then
    source "${TEST_FILE}"
else
    # if --use-config WAS defined this block could not be reached due to the
    # check for SCENARIOKEY post-config load.
    usage "Invalid specification: one of --use-config <filepath> OR --test OR --test-file <filepath> required!"
fi

# Up to now we have been doing configuration and so it is sufficient for all
# logging to end up strictl on screen.  At this point, though, we want to add a log
# file to location specified by BASE_DIR.
export DU_ETL_DEBUG_TARGET="${BASE_DIR}"/etl.log
export DU_ETL_DEBUG_MODE=2
export DMS_HOME

debug "Beginning $DMS Processing @ $(date)"

if [[ "$DISTRIBUTE_ONLY" = false ]] && [[ "$EXPORT_MAPPER" = false ]]; then
    if [[ "${PROMPT_FOR_FILE:='false'}" = 'true' ]]; then
        perform_zip_file_selection_and_parse   || die "Zip File Processing Failed"
    else
        type 'stage_data_for_parsing' &>/dev/null && \
            stage_data_for_parsing
        perform_unattended_parse               || die "Parsing Failed"
    fi
    du_paytype_sub --initialize --done     || die "Paytype Initialization Failed"

    populate_parts_rules                   || die "Parts Rule Population Failed" # Sourced In
    type 'add_additional_parts_paytype_rules' &>/dev/null && \
        add_additional_parts_paytype_rules

    populate_labor_rules                   || die "Labor Rule Population Failed" # Sourced In
    type 'add_additional_labor_paytype_rules' &>/dev/null && \
        add_additional_labor_paytype_rules

    if [[ "$RUN_TEST" = true ]]; then
        perform_test_transform || die "Test Transformation Failed"
    else
        du_transform --process                  \
                     --done                     \
                     || die "Processing Failed"
    fi
    du_transform --status                   --done   || die "Status display failed..."
    du_transform --set-target "${TLI}" \
                 --export                   --done   || die "Exporting of Standard Files failed"

    prompt_production_load #Test mode will always perform a load
    TEST_LOAD=true
fi

if [[ "$DISTRIBUTE_ONLY" = true ]] || [[ "$TEST_LOAD" = true ]]; then
    du_loadcheck  "${TLI}"                  --done   || die "Check of exported files failed!"
    [[ -n "$PARTSKEY" ]]                             || {
        scream "Parts Scenario Key Required At This Point"
        yell "Should be able to fix and re-run with --distribute-only"
        exit 1
    }

    LABORKEY=$(du_loadcreate --key "${PARTSKEY}" \
                  --compute-labor-key)

    # perform_load
    # perform_distribution --zip # File copy errors are non-fatal; all others are

    # type 'perform_post_distribution_cleanup' &>/dev/null && \
    #     perform_post_distribution_cleanup

    AUDIT_LOAD=true
    if  [[ "$AUDIT_LOAD" = true ]]; then
        echo "Audit Load Started"
        perform_audit_load
        perform_auditfile_distribution  --zip 
    else
        echo "Not performed the Audit Load"
    fi
fi

if [[ "$DISTRIBUTE_ONLY" = true ]]; then
    # Record the production info to a file for use in populating Solve360
    # and for inclusion in a notification email.
    {
        echo "$STORENAME"
        echo "$GROUP_CODE"
        echo "$STORE_PART"
        echo "$STATE"
        echo "$PARTSKEY"
        echo "$LABORKEY"
        echo ""
    } > "$BASE_DIR"/last-import.txt
    cat "$BASE_DIR"/last-import.txt >> "$BASE_DIR"/import-log.txt
    attempt_agent_reload
    distribute_via_email "Import: $GROUPNAME - $STORENAME" "$BASE_DIR"/last-import.txt
else
    echo "Non-Distribution Test" > "$BASE_DIR"/last-import.txt
fi

if [[ "$EXPORT_MAPPER" = true ]]; then
    echo "$SRC_SCHEMA"
    echo "$SCENARIOKEY"
    echo "$PARTSKEY"
    echo "$MFG"
    echo "$STATE"
    echo "${DU_MAPPER_ETLMAP_I:?Must define DU_MAPPER_ETLMAP_I}"
    [[ -d "$DU_MAPPER_ETLMAP_I" ]] || die "Mapper Export Directory Doesn't Exist: $DU_MAPPER_ETLMAP_I"

    du_export_mapper --output-dir "${DU_MAPPER_ETLMAP_I}" || die "Exporting mapper files failed"

else
    echo "DU-Mapper Export Skipped"
fi

exit 0
