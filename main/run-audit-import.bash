clear

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/etl/bash/etl-command-functions.bash

mkdir -p ~/tmp

# General Setup
SCENARIOKEY=
CONFIG_FILE=
USERFILE=
PERMANENT_ETI='false'

BASE_DIR=$HOME/tmp
SOURCE_DIR=/etl/audit-import
# WORK_DIR="${ETI}"
LOAD_IN=/etl/load-in-audit
LOAD_ARCHIVE=/etl/load-archive-audit

MAPPER_DIR=/etl/mapper-in-audit
MAPPER_ARCHIVE=/etl/mapper-archive-audit

AUDIT_WORK="/etl/audit-work"
AUDIT_FAIL="/etl/audit-failed"
LOAD_FOLDER="${BASE_DIR}"/audit-load
LOAD_FILE_FOLDER="${LOAD_FOLDER}"/run-du-etl-load-tli


IMPORT_LOG="/etl/log"

mkdir -p ${AUDIT_WORK}
mkdir -p ${AUDIT_FAIL}
mkdir -p ${IMPORT_LOG}
mkdir -p ${LOAD_FOLDER}
mkdir -p ${LOAD_FILE_FOLDER}

PORTAL_API_KEY=${PORTAL_EVENT_API_KEY}
PORTAL_API_URL=${PORTAL_EVENT_API_URL}

ETL_LOG="/etl/log"

SCHEDULER_API=${SCHEDULER_EVENT_API_URL}

function psql_audit_import() {
    psql "service=$DU_ETL_SCHEDULER_SERVICE options='-c search_path=process_data'" --set=ON_ERROR_STOP=1 "$@"
    return $?
}

function update_portal_event() {
    local project_id=${1}
    local status=${2}
    local imported_on="$(date -I)"
    local performedby=${IMPORTED_BY}
    echo "${imported_on}: Import" >> ${IMPORT_LOG}/update-portal-event.log
    echo "${imported_on}: Updating the project ${project_id}" >> ${IMPORT_LOG}/update-portal-event.log
    echo "performedby :${performedby}" >> ${IMPORT_LOG}/update-portal-event.log
    if [[ ! "${PORTAL_API_URL:-}" = '' ]]; then
        curl -X PUT \
            --user "auth-key:${PORTAL_API_KEY}" \
            --header "Content-Type: application/json" \
            --max-time 30 \
            -d "{\"inProjectId\"     : \"${project_id}\",  \
                 \"inSource\"        : \"Import\", \
                 \"inAction\"        : \"${status}\", \
                 \"inPerformedOn\"   : \"${imported_on}\", \
		 \"inPerformedBy\"   : \"${performedby}\", \
		 \"inData\"          : \"{}\", \
                 \"inCreatedBy\"     : \"${performedby}\"}" \
                "${PORTAL_API_URL}" >> ${IMPORT_LOG}/update-portal-event.log 2>&1
    fi  
}

function update_scheduler_event() {
    local project_id=${1}
    local sec_project_id=${2}
    local submissionId="${3}"
    local schedulerId="${4}"
    local imported_by=${5}
    local dms=${6}
    local import_status=${7}
    local comment=${8}
    local imported_on="$(date -I)"
    echo "Updating import status to database for $submissionId" >> ${ETL_LOG}/update-import-event.log 2>&1
    curl -X POST \
            --header "Content-Type: application/json" \
            --max-time 30 \
            -d "{\"inProjectId\"     : \"${project_id}\",  \
                 \"inSecondaryProjectId\"  : \"${sec_project_id}\", \
                 \"inSubmissionId\"  : \"${submissionId}\", \
                 \"inSchedulerId\"   : \"${schedulerId}\", \
                 \"inSource\"        : \"SCHEDULER\", \
                 \"inAction\"        : \"SchedulerImport\", \
                 \"inPerformedOn\"   : \"${imported_on}\", \
		         \"inPerformedBy\"   : \"${imported_by}\", \
                 \"inDms\"           : \"${dms}\", \
                 \"inCreatedBy\"     : \"${imported_by}\", \
                 \"isInputFileGeneratedComment\"     : \"${comment}\", \
                 \"isInputFileGenerated\"     : ${import_status}, \
                 \"inputFileGeneratedOn\"     : \"${imported_on}\" }" \
            "${SCHEDULER_API}" >> ${ETL_LOG}/update-import-event.log 2>&1
}


function save_import_status() {
    uuid=$1
    import_status=$2
    comment=$3

    progress "Updating import status to database"
if [ "$import_status" = true ]; then    

    if [ -n "$SCHEDULER_API" ]; then
        update_scheduler_event "${PROJECT_ID}" "${SECONDARY_PROJECT_ID}" "${uuid}" "${SCHEDULER_ID}" "${IMPORTED_BY}" "${DMS}" "${import_status}" "${comment}"
    fi

else
    if [ -n "$SCHEDULER_API" ]; then
        update_scheduler_event "${PROJECT_ID}" "${SECONDARY_PROJECT_ID}" "${uuid}" "${SCHEDULER_ID}" "${IMPORTED_BY}" "${DMS}" "${import_status}" "${comment}"
    fi
fi
}


function cleanup() {
    rm -rf "${AUDIT_WORK}"/*
    rm -rf "${LOAD_FILE_FOLDER}"/*
}

function clean_and_die() {
    comment=$1
    cleanup
    save_import_status $UNIQUE_ID false $comment || die "Updating import status failed"
    die $1
}

export RUN_TEST=false

DIR="$(dirname "$(readlink -f "$0")")"

function usage() {
    yell "$@"
    cat <<EOF
        --use-config        <filepath>  Obtain variables by sourcing in named file
                            Defaults to prompt for config file
EOF
    exit 1
} >&2

while [ $# -gt 0 ]; do
    case "$1" in
    --use-config)
        if [[ -e "${2}" ]]; then
        CONFIG_FILE="${2}"
           die "Configuration File Name Required"
        fi
        shift 2
        ;;
    *)
        usage "Unrecognized option $1; exiting"
        exit 1
        ;;
    esac
done


DISTRIBUTE_ONLY='true'
# source "$CONFIG_FILE"

if [[ -n "${ETI}" ]]; then
    PERMANENT_ETI='true'
fi
while [[ -n $(find $SOURCE_DIR -maxdepth 1 -type f -name '*.zip') ]];
do
    echo "Import Started at-`date +%Y-%m-%d-%H.%M.%S`"

    export UNIQUE_ID=$(uuidgen)
    reset_working_directories
        echo "TLI contents 0"
        echo "**********************************************************************"
        echo "$(ls ${TLI})"
        echo "**********************************************************************"
    perform_zip_file_selection_and_parse   || clean_and_die "Zip File Processing Failed"
    CONFIG_FILE="${TLI}"/config_$COMPANY_ID.bash
    echo "$(cat ${TLI}/config_$COMPANY_ID.bash)"
    PAYTYPE_MODEL_SCRIPT_OVERRIDE="${DU_ETL_HOME}"/"${DMS_HOME}"/src/template/run-functions.bash
    PAYTYPE_MODEL_SCRIPT_CLIENT="${DU_ETL_HOME}"/DU-Transform/client/paytypes/default-rule-population-functions.bash
    PAYTYPE_MODEL_SCRIPT=

    if [[ -f "$PAYTYPE_MODEL_SCRIPT_OVERRIDE" ]]; then
        PAYTYPE_MODEL_SCRIPT="$PAYTYPE_MODEL_SCRIPT_OVERRIDE"
    else
        PAYTYPE_MODEL_SCRIPT="$PAYTYPE_MODEL_SCRIPT_CLIENT"
    fi

    source "$PAYTYPE_MODEL_SCRIPT"

    export DU_ETL_DEBUG_TARGET="${BASE_DIR}"/etl.log
    export DU_ETL_DEBUG_MODE=2
    export DMS_HOME

        echo "TLI contents 1"
        echo "**********************************************************************"
        echo "$(ls ${TLI})"
        echo "**********************************************************************"
    du_paytype_sub --initialize --done     || clean_and_die "Paytype Initialization Failed"

    populate_parts_rules                   || clean_and_die "Parts Rule Population Failed" # Sourced In
    type 'add_additional_parts_paytype_rules' &>/dev/null && \
        add_additional_parts_paytype_rules

    populate_labor_rules                   || clean_and_die "Labor Rule Population Failed" # Sourced In
    type 'add_additional_labor_paytype_rules' &>/dev/null && \
        add_additional_labor_paytype_rules

    du_transform --process                  \
                --done                     \
            || clean_and_die "Processing Failed"

        echo "TLI contents 1.1"
        echo "**********************************************************************"
        echo "$(ls ${TLI})"
        echo "**********************************************************************"

    du_transform --status                   --done   || clean_and_die "Status display failed..."

    echo -n -e "${txt_data_table}"
    echo "Source File : $(basename $(< ${TW}/meta/source-files.txt))"
    echo "**********************************************************************"
    echo -n -e "${t_reset}"

    # ask "Processing complete. Continue with export [Y/n]?" "[nNyY]" "Y"
    # if [[ "${ANSWER^^}" = "N" ]]; then
    #     exit 0
    # fi
        echo "TLI contents 2"
        echo "**********************************************************************"
        echo "$(ls ${TLI})"
        echo "**********************************************************************"

    du_transform --set-target "${TLI}" \
                --export                   --done   || clean_and_die "Exporting of Standard Files failed"

    if [[ -d "${MAPPER_DIR}" ]]; then
        du_export_mapper --output-dir "${MAPPER_ARCHIVE}" || clean_and_die "Exporting mapper files failed"
        cp "${MAPPER_ARCHIVE}"/"${UNIQUE_ID}-mapper.zip" "${MAPPER_DIR}"/"${UNIQUE_ID}-mapper.tmp"
        mv "${MAPPER_DIR}"/"${UNIQUE_ID}-mapper.tmp" "${MAPPER_DIR}"/"${UNIQUE_ID}-mapper.zip"
    fi

    (
        cd ${LOAD_FOLDER}
        echo "TLI contents 3"
        echo "**********************************************************************"
        echo "$(ls ${TLI})"
        echo "**********************************************************************"

        cp -r ${TLI}/* $(basename ${LOAD_FILE_FOLDER})
        zip -qr "${LOAD_IN}/${UNIQUE_ID}-audit.tmp" $(basename ${LOAD_FILE_FOLDER})
        cp "${LOAD_IN}/${UNIQUE_ID}-audit.tmp" "${LOAD_ARCHIVE}/${UNIQUE_ID}-audit.zip"
        mv "${LOAD_IN}/${UNIQUE_ID}-audit.tmp" "${LOAD_IN}/${UNIQUE_ID}-audit.zip"
    )
    echo "UNIQUE_ID: $UNIQUE_ID"
    save_import_status "$UNIQUE_ID" true "" || clean_and_die "Updating import status failed"
    archive_source_file
    cleanup
    echo "Import Ended at-`date +%Y-%m-%d-%H.%M.%S`"
    done
    exit 0

