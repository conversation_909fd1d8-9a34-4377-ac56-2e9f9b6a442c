#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

REPO_ROOT="$(cd $(dirname $0) && pwd)"

SCHEMA_NAME="${1:?Schema Name Required}"
TARGET_DIR="${2:?Target Directory Required}"
CONVERT_TO_PDF="${3:-true}"
TMP_DIR="/tmp/du-etl-cdk3pa-proxyinvoice-work"
mkdir -p "$TMP_DIR"
clear_dir "$TMP_DIR"
BOOK_DIR="$TMP_DIR"/bookmarking
SHOW_ACCOUNTING_IN_PROXY="${4:-false}"
PORSCHE_STORE="${5:-false}"

[[ -d "$TARGET_DIR" ]] || die "$2 must exist and should be empty"

clear_dir "$TARGET_DIR"
mkdir "$TARGET_DIR"/text || die "Could not prepare text directory"
if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
    mkdir "$TARGET_DIR"/pdf  || die "Could not prepare pdf directory"
fi

(
    cd "$TARGET_DIR"/text || die "Failed to set target directory as active"

    "$REPO_ROOT"/create-proxy-invoice-cdkflex-report.pl "${SCHEMA_NAME}" "${SHOW_ACCOUNTING_IN_PROXY}" "${PORSCHE_STORE}"

    #Create fixed width proxy invoice PDF report by iterating the proxy invoice txt reports
    loop_count=0
    if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
        progress "Beginning PDF Conversions - Count: $(find . -type f | wc -l)"
        echo "$(date +%H:%M:%S) Starting"
        for pi in ./*.txt; do
            ((loop_count++))
            if [[ $(($loop_count%200)) = 0 ]]; then
                echo "$(date +'%H:%M:%S') Converted $loop_count"
            fi
            clear_dir "$BOOK_DIR"
            [ -f "$pi" ] || break
            filename=$(basename "$pi")
            filename="${filename%.*}"
            enscript -q \
                     -fCourier-Bold9 \
                     --no-header \
                     --margins=::15: \
                     -L 73 "${pi}" \
                     -o - \
                | ps2pdf - "$BOOK_DIR"/"${filename}.pdf"

            pdftk "$BOOK_DIR"/"${filename}.pdf" dump_data output "${BOOK_DIR}"/temp-pdf-metadata.txt

            printf 'BookmarkBegin\nBookmarkTitle: %s\nBookmarkLevel: 1\nBookmarkPageNumber: 1' "${filename}" \
                 > "${BOOK_DIR}"/temp-pdf-new-bookmark.txt

            cat "${BOOK_DIR}"/temp-pdf-metadata.txt \
                "${BOOK_DIR}"/temp-pdf-new-bookmark.txt \
                > "${BOOK_DIR}"/temp-pdf-new-metadata.txt

            pdftk "${BOOK_DIR}"/"${filename}.pdf" \
                  update_info \
                  "${BOOK_DIR}"/temp-pdf-new-metadata.txt \
                  output - \
                  > ../pdf/"${filename}.pdf" 2>&1

        done
    else
        progress "Skipping PDF Conversion as Requested"
    fi

) || die "Failed during Proxy Invoice generation"

# Create one or more PDF bundles from the individual PDF files
if [[ "$CONVERT_TO_PDF" = 'true' ]]; then
    (
        cd "$TARGET_DIR"/pdf
        mkdir ../bundle
        BUNDLE_PREFIXES=$(find . -mindepth 1 -maxdepth 1 -type f -exec basename {} \; | perl -ale 'print $1 if /(\w{0,}\d+)\d{2}\w{0,}.pdf/;' | sort | uniq)
        for prefix in $BUNDLE_PREFIXES; do
            FIRST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $1}')
            LAST_TMP=$(echo "${prefix}"*.pdf | sort | uniq | awk '{print $NF}')
            FIRST=${FIRST_TMP::-4}
            LAST=${LAST_TMP::-4}
            pdftk ./"${prefix}"*.pdf cat output ../bundle/bundle-"${FIRST}-${LAST}".pdf
        done
    )
fi

say "Done Creating Proxy Invoices"
