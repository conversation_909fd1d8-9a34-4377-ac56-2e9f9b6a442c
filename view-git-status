#!/usr/bin/env bash
source "${DU_ETL_HOME:?DU_ETL_HOME Not Exported}"/src/shared/bash/globals-index.bash

SSH_REPO_BASE="${SSH_REPO_BASE:-**************:DealerUplift-Org}"

VERBOSE='false'
SHOW_DIFFS='false'
DO_PUSH='false'
DO_COMMIT='false'
DO_BRANCH='false'
DO_CHECKOUT='false'
SET_UPSTREAMS='false'
CHECK_STASH='false'
DO_FETCH='false'
DO_PULL='false'
CLONE_ONLY='false'
RUN_TESTS='false'
COMMIT_MSG=

if [[ $1 = '--show-origin' ]]; then
    VERBOSE='true'
elif [[ $1 = '--show-diffs' ]]; then
    SHOW_DIFFS='true'
elif [[ $1 = '--do-push' ]]; then
    DO_PUSH='true'
elif [[ $1 = '--do-commit-all' ]]; then
    DO_COMMIT='true'
    COMMIT_MSG="${2:?Commit Message Required}"
elif [[ $1 = '--do-branch' ]]; then
    DO_BRANCH='true'
    BRANCH_NAME="${2:?Branch Name Required}"
elif [[ $1 = '--do-checkout' ]]; then
    DO_CHECKOUT='true'
    BRANCH_NAME="${2:?Branch Name Required}"
elif [[ $1 = '--set-upstreams' ]]; then
    SET_UPSTREAMS='true'
    BRANCH_NAME="${2:?Branch Name Required}"
elif [[ $1 = '--check-stash' ]]; then
    CHECK_STASH='true'
elif [[ $1 = '--fetch' ]]; then
    DO_FETCH='true'
elif [[ $1 = '--pull' ]]; then
    DO_PULL='true'
elif [[ $1 = '--clone' ]]; then
    CLONE_ONLY='true'
elif [[ $1 = '--show-branches' ]]; then
    SHOW_BRANCHES='true'
elif [[ $1 = '--test' ]]; then
    RUN_TESTS='true'
fi

function display_status() {
    git status
}

function display_location() {
    echo -e -n "\e[1;33m"
    git rev-parse --show-toplevel
    echo -e -n "\e[0m"
}

function display_origin() {
    [[ $VERBOSE = 'true' ]] && { git remote show origin; }
}

function display_diff() {
    [[ $SHOW_DIFFS = 'true' ]] && { git --no-pager diff; }
}

function perform_push() {
    [[ $DO_PUSH = 'true' ]] && { git push; }
}

function perform_commit() {
    if [[ $DO_COMMIT = 'true' ]]; then
        git add --all
        git commit -m "$COMMIT_MSG"
    fi
}

function perform_branch() {
    if [[ $DO_BRANCH = 'true' ]]; then
        git branch "$BRANCH_NAME"
    fi
}

function perform_checkout() {
    if [[ $DO_CHECKOUT = 'true' ]]; then
        git checkout "$BRANCH_NAME"
    fi
}

function perform_fetch() {
    if [[ $DO_FETCH = 'true' ]]; then
        git fetch
    fi
}

function perform_pull() {
    if [[ $DO_PULL = 'true' ]]; then
        git pull --ff-only
    fi
}


function set_upstreams() {
    if [[ $SET_UPSTREAMS = 'true' ]]; then
        git push --set-upstream origin "$BRANCH_NAME"
    fi
}

function check_stash() {
    [[ $CHECK_STASH = 'true' ]] && { git stash list; }
}

function show_branches() {
    [[ $SHOW_BRANCHES = 'true' ]] && { git branch; }
}

function run_tests() {
    [[ $RUN_TESTS = 'true' ]] && {
        if [[ -x ./run-tests ]]; then
        (
            ./run-tests || die "Failed run-tests in $(pwd)"
        ) || die "Aborting Due To Test Failure"
        fi
    }
}

function display() {
mkdir -p "$DU_ETL_HOME"/DU-DMS
local repo_name
local ssh_repo_path
    if [[ $CLONE_ONLY = 'true' ]]; then
        repo_name=$(basename "$1")
        if [[ ! -d "$1" ]]; then
            ssh_repo_path="${SSH_REPO_BASE}"/"${repo_name}".git
            git clone "$ssh_repo_path" "$1"
        else
            say "Skipping Cloning of Existing Repo $repo_name"
        fi
        return 0
    fi
    if [[ ! -d "$1" ]]; then
        scream "$1 Not Cloned"
        return 1
    fi
    cd $1
    display_location
    show_branches
    perform_commit
    display_status
    display_origin
    display_diff
    perform_push
    perform_branch
    perform_checkout
    perform_fetch
    perform_pull
    set_upstreams
    check_stash
    run_tests
}

if [[ "$CLONE_ONLY" = 'true' ]]; then
    say "Cloning Child Repos"
else
    say "Viewing Git Repo Status"
    clear
fi

display "$DU_ETL_HOME"
display "$DU_ETL_HOME"/DU-Extract
display "$DU_ETL_HOME"/DU-Scheduler
display "$DU_ETL_HOME"/DU-Transform
display "$DU_ETL_HOME"/DU-Load
display "$DU_ETL_HOME"/DU-Solve360
display "$DU_ETL_HOME"/DU-ProxyInvoice
display "$DU_ETL_HOME"/DU-DMS/DMS-PBS
display "$DU_ETL_HOME"/DU-DMS/DMS-AutoNation
display "$DU_ETL_HOME"/DU-DMS/DMS-DealerTrack
display "$DU_ETL_HOME"/DU-DMS/DMS-DealerBuilt
display "$DU_ETL_HOME"/DU-DMS/DMS-SIS
display "$DU_ETL_HOME"/DU-DMS/DMS-AutoMate
display "$DU_ETL_HOME"/DU-DMS/DMS-UCS
display "$DU_ETL_HOME"/DU-DMS/DMS-Reynolds
display "$DU_ETL_HOME"/DU-DMS/DMS-KenGarff
display "$DU_ETL_HOME"/DU-DMS/DMS-Quorum
display "$DU_ETL_HOME"/DU-DMS/DMS-DPC
display "$DU_ETL_HOME"/DU-DMS/DMS-AutoSoft
display "$DU_ETL_HOME"/DU-DMS/DMS-CDKDash
display "$DU_ETL_HOME"/DU-DMS/DMS-Dominion
display "$DU_ETL_HOME"/DU-DMS/DMS-CDK3PA
display "$DU_ETL_HOME"/DU-DMS/DMS-Advantage
display "$DU_ETL_HOME"/DU-DMS/DMS-MPK

exit
