#!/usr/bin/env bash

###############################################################################
# ./etl-share-monitor.bash --help for description and usage
###############################################################################

ETL_WORK="$DU_ETL_WORK"
OUTPUT_FILE="/tmp/etl-share-monitor.txt"

RETAIN_DMS_ARCHIVE_FILES=+30
RETAIN_LOAD_ARCHIVE_FILES=+30
RETAIN_MAPPER_ARCHIVE_FILES=+30
RETAIN_INVOICE_ARCHIVE_FILES=+30

DRY_RUN=false

function usage() {
    if [ "$*" != "" ] ; then
        echo "Error: $*"
    fi

    cat >&2 <<'USAGE'
Usage: sudo -E etl-share-monitor.bash [--options]

Monitor directories and files on the ETL share defined by $DU_ETL_WORK or
by the --etl-work option. 

* Create missing directories as needed
* Report unrecognized directories and files
* Purge archive directories
* Email a report of processing with a summary of files and space used

Options:
--help                      display this usage message and exits
--etl-work                  ETL work directory (default $DU_ETL_WORK)
--send-to                   distribution list
--dry-run                   don't create missing directories or purge files
USAGE
    exit 1
}

while [ $# -gt 0 ]; do
    case "$1" in
        --send-to             ) SEND_TO_ETL_ADMINDEV="${2}";                    shift 2 ;;
        --dry-run             ) DRY_RUN=true;                                   shift 1 ;;
        --etl-work            ) ETL_WORK="${2?:ETL Work Directory Required}";   shift 2 ;;
        --help                ) usage;;
                            * ) usage "Unrecognized option $1";                 exit 1 ;;
    esac
done

ETL_ROOT=$(dirname "$ETL_WORK")

function scan_root() {
    local root_file_list="/tmp/etl-share-root-files.txt"
    local missing unrecognized dir delete

    echo
    echo "Scanning root $ETL_ROOT"
    find "$ETL_ROOT" -maxdepth 1 -mindepth 1 -not -path "*/.*" -printf "%f\n" | sort > $root_file_list

    unrecognized=($(cat $root_file_list | grep -vF "$(get_root_directories)"))
    if [[ "${#unrecognized[@]}" > 0 ]]; then
        IFS=$'\n' echo "Unrecognized file(s): ${unrecognized[@]}"
    fi

    missing=($(get_root_directories | grep -vFf $root_file_list))
    if [[ "${#missing[@]}" > 0 ]]; then
        IFS=$'\n' echo "Missing file(s): ${missing[@]}"

        for dir in "${missing[@]}"; do
            create_directory "${ETL_ROOT}/${dir}"
        done
    fi

    echo "Purging files"
    [[ $DRY_RUN = false ]] && delete="-delete"
    find "${ETL_ROOT}/load-archive" -type f -mtime $RETAIN_LOAD_ARCHIVE_FILES -print $delete | sort
    find "${ETL_ROOT}/mapper-archive" -type f -mtime $RETAIN_MAPPER_ARCHIVE_FILES -print $delete | sort
    find "${ETL_ROOT}/invoices-archive" -type f -mtime $RETAIN_INVOICE_ARCHIVE_FILES -print $delete | sort

    rm "$root_file_list"
}

function get_root_directories() {
    cat <<TXT
config
dist
etl-vagrant
invoices-archive
invoices-err
invoices-in
load-archive
load-in
load-work
log
mapper-archive
mapper-err
mapper-in
mapper-out
tmp
TXT
}

function root_summary() {
    printf "\nETL Root Summary:\n"
    printf "============================== ======== =======\n"
    printf "Directory                       #Files   Space\n"
    printf "============================== ======== =======\n"

    (
        cd "$ETL_ROOT"
        find load* invoices* mapper* -type d | sort | while read dir; do
            summarize_directory "$dir"
        done
        printf "\n"
    )
}

function summarize_directory() {
    local dir="$1"
    local nfiles space

    nfiles=$(ls "$dir" | wc -l)
    space=$(du -sh "$dir" | awk '{print $1}')
    printf "%-30s %7s %7s\n" "$dir" "$nfiles" "$space"
}

function scan_dms() {
    local DMS="${1}"
    local dms="${DMS,,}"
    local etldir="etl-${dms}"
    local unrecognized
    local delete

    echo "$etldir"

    if [[ ! -d "${ETL_WORK}/${etldir}" ]]; then
        create_directory "${ETL_WORK}/${etldir}"
    fi

    (
        cd "${ETL_WORK}/${etldir}"
    
        if [[ ! -d "${dms}-zip" ]]; then
            create_directory "${ETL_WORK}/${etldir}/${dms}-zip"
        fi

        if [[ ! -d "${dms}-zip/archive" ]]; then
            create_directory "${ETL_WORK}/${etldir}/${dms}-zip/archive"
        fi

        unrecognized=($(find . -mindepth 1 -maxdepth 1 \
                        -not -name "inv-${dms}" \
                        -not -name "inv-${dms}-test" \
                        -not -name "invoice-manipulation" \
                        -not -name "${dms}-*" \
                        -not -path "*/.*"  \
                        -printf "%f\n" | sort))

        if [[ "${#unrecognized[@]}" > 0 ]]; then
            IFS=$'\n' echo "Unrecognized file(s): ${unrecognized[@]}"
        fi

        echo "Purging files"
        [[ $DRY_RUN = false ]] && delete="-delete"
        find "${dms}-zip/archive" -mindepth 1 -mtime $RETAIN_DMS_ARCHIVE_FILES -print $delete | sort
    )

    echo
}

function scan_dmses() {
    local cmd="${1:-scan_dms}"
    local dir DMS

    [[ "$cmd" = 'scan_dms' ]] && echo "Scanning DMSes"

    for dir in $(find "$DU_ETL_HOME/DU-DMS" -mindepth 1 -maxdepth 1 -type d -printf "%f\n" | sort); do
        DMS="${dir##DMS-}"
        $cmd "$DMS"
    done
}

function summarize_dms() {
    local DMS="${1}"
    local dms="${DMS,,}"
    local etldir="etl-${dms}"
    local nfiles_zip nfiles_archive nfiles_other
    local du_zip du_archive du_other

    nfiles_zip=$(ls "${ETL_WORK}/${etldir}/${dms}-zip" | wc -l)
    nfiles_archive=$(ls "${ETL_WORK}/${etldir}/${dms}-zip/archive" | wc -l)
    nfiles_other=$(find "${ETL_WORK}/${etldir}" -not -name "${dms}-zip" | wc -l)
    du_zip=$(du -sh "${ETL_WORK}/${etldir}/${dms}-zip" | awk '{print $1}')
    du_archive=$(du -sh "${ETL_WORK}/${etldir}/${dms}-zip/archive" | awk '{print $1}')
    du_other=$(du -sh "${ETL_WORK}/${etldir}" --exclude="${dms}-zip" | awk '{print $1}')

    printf "%-15s %5s %5s %6s %6s %6s %6s\n" "$DMS" "$nfiles_zip" "$du_zip" "$nfiles_archive" "$du_archive" "$nfiles_other" "$du_other"
}
 
function dms_summary() {
    printf "DMS Summary:\n"
    printf "=============== =========== ============= =============\n"
    printf "DMS               Zip Files Archive Files  Other Files\n"
    printf "=============== =========== ============= =============\n"

    scan_dmses summarize_dms
}

function email() {
    if [[ ! "${SEND_TO_ETL_ADMINDEV_NOTIFY:-}" = '' ]]; then
        if type send-email >/dev/null 2>&1; then
            if send-email --to "${SEND_TO_ETL_ADMINDEV_NOTIFY}" \
                          --subject "ETL Share Monitor" \
                          --body "See attached" \
                          --attach "${OUTPUT_FILE}" \
                          --send > /dev/null; then
                return
            fi
        fi
    fi
}

function create_directory() {
    local dir="$1"

    if [[ $DRY_RUN = false ]]; then
        mkdir "$dir"
        chown :etluser "$dir"
        chmod g+rwxs "$dir"
    fi

    echo "Created directory $dir"
}

function main() {
    echo "ETL Share Monitor @ $(date)"
    echo "ETL Work:                     $ETL_WORK"
    echo "Dry Run:                      $DRY_RUN"
    echo "Retain Load Archive Files:    $RETAIN_LOAD_ARCHIVE_FILES"
    echo "Retain Mapper Archive Files:  $RETAIN_MAPPER_ARCHIVE_FILES"
    echo "Retain Invoice Archive Files: $RETAIN_INVOICE_ARCHIVE_FILES"
    echo "Retain DMS Archive Files:     $RETAIN_DMS_ARCHIVE_FILES"
    echo "Send To:                      $SEND_TO"

    scan_root
    root_summary

    scan_dmses
    dms_summary

} > "$OUTPUT_FILE"

main
email
