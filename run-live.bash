#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

BUNDLE_TYPE='initial' #initial or refresh

START_DATE='09/01/2018' # usually today
END_DATE='11/30/2018'   # usually start of 6 or so months ago

BASE_WORK_DIR="$HOME"/tmp/du-etl-dms-fortellis-extractor-work

WORK_DIR="$HOME"/tmp/du-etl-dms-fortellis-extractor-work
BUNDLE_DIR="$WORK_DIR"/run-live-temp/fortellis-zip-eti
DEADLETTER_DIR="$WORK_DIR"/run-live-temp/dead-letter-extracted
OUTPUT_DIR="$WORK_DIR"/run-live-temp/fortellis-zip-eti/1312

mkdir -p "$WORK_DIR" "$BUNDLE_DIR" "$DEADLETTER_DIR"

function do_pull() {
PARTY_ID="$1"
DEALER_ID="$2"
GROUP_CD="$3"
STORE_CD="$4"
STATE_CD="$5"


fortellis-requestor pull     --partyID "$PARTY_ID" \
                            --dealerID "$DEALER_ID" \
                            --startDate "$START_DATE" \
                            --endDate "$END_DATE" \
                            --bundle "$BUNDLE_TYPE" \
                            --zipPath "$BUNDLE_DIR" \
                            --deadLetter "$DEADLETTER_DIR" \
                            --mageGroupCode "$GROUP_CD" \
                            --mageStoreCode "$STORE_CD" \
                            --stateCode "$STATE_CD" \

}

echo "Start: $START_DATE"
echo "End  : $END_DATE"

prompt_continuation 'Confirm dates and continue.'

do_pull "P100022" "SVD999401" "GROUP" "STORE" "STATE"
