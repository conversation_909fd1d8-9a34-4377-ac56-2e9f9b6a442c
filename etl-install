
#############################################################################
# ETL onstallation/configuration script
# ./etl-install --help for usage
#############################################################################

export DU_ETL_HOME="$(cd $(dirname $0) && pwd)"
source "${DU_ETL_HOME}"/src/shared/bash/globals-index.bash

set -o pipefail
shopt -s failglob

DU_ETL_WORK="/etl/etl-vagrant"
DU_ETL_DATABASE="etldb"
DU_ETL_PG_SERVICE=etldb
LOGNAME=$(logname)

function main() {
    local rc=0

    if [[ $LOGNAME != 'etl' ]]; then
        yell "Please run this script as user etl, not $LOGNAME"
        ((rc |= 1))
    fi

    if [[ $EUID != "0" ]]; then
        yell "This script requires sudo"
        ((rc |= 1))
    fi

    if [[ "${rc}" != "0" ]]; then
        return 1
    fi
    
    clone_repos                         || ((rc |= 1))
    create_bash_environment
    install_programs                    || ((rc |= 1))
    create_pg_services                  || ((rc |= 1))

    if [[ "${rc}" = "0" ]]; then
        init_solve360_database          || ((rc |= 1))
    fi

    create_directories                  || ((rc |= 1))

    if [[ "${rc}" = "0" ]]; then
        run_tests                       || ((rc |= 1))
    fi

    return $rc
}

function clone_repos() {
    "${DU_ETL_HOME}"/view-git-status --clone
}

function create_bash_environment() {
   cat > /etc/profile.d/etl.sh \
<<BASH
export DU_ETL_HOME="${DU_ETL_HOME}"
export DU_ETL_WORK="${DU_ETL_WORK}"
export DU_ETL_CONFIG_DIR="${DU_ETL_WORK}/config"
export DU_ETL_DIST_DIR=/etl/dist

export DU_ETL_PG_SERVICE=${DU_ETL_PG_SERVICE}
export DU_ETL_DATABASE=${DU_ETL_DATABASE}
export GGS_LOCAL_PG_SERVICE=ggs_local
export GGS_PROD_PG_SERVICE=not_defined
export PGSERVICEFILE=$HOME/.pg_service.conf

export DU_ETL_SOLVE360_SERVICE=${DU_ETL_PG_SERVICE}
export SOLVE360_API_EMAIL=<EMAIL>
export SOLVE360_API_TOKEN=n6xfv5ccH6n9I0le97G245T1lctfgd45I2dcD9n0
source "${DU_ETL_HOME}/DU-Solve360/bashrc"

export SWAKS_HOME=$HOME/.swaksrc
export PATH=$DU_ETL_HOME:$DU_ETL_HOME/DU-Load:$PATH
source "${DU_ETL_HOME}/src/install/bashrc-to-source-dms-bashrcs"
BASH
    source /etc/profile.d/etl.sh
}

function init_solve360_database() {
    say "Initializing Solve360 database"
    (
        cd "${DU_ETL_HOME}/DU-Solve360"
        ./src/main/bash/initdb.bash
    )
}

function install_programs() {
    local rc=0

    check_jq_is_installed                   || ((rc |= 1))
    check_csvkit_commands_are_installed     || ((rc |= 1))
    check_enscript_is_installed             || ((rc |= 1))
    check_ps2pdf_is_installed               || ((rc |= 1))
    check_pdf2txt_is_installed              || ((rc |= 1))
    check_pdftk_is_installed                || ((rc |= 1))
    check_bats_is_installed                 || ((rc |= 1))
    check_perl_trim_is_installed            || ((rc |= 1))
    check_perl_date_manip_is_installed      || ((rc |= 1))
    check_dos2unix_is_installed             || ((rc |= 1))

    return $rc
}

function check_csvkit_commands_are_installed() {
    if which csvclean >/dev/null; then
        say "found csvclean"
    else
        yell "csvclean not found, installing"
        pip install csvkit
    fi
}

function check_enscript_is_installed() {
    if which enscript >/dev/null; then
        say "found enscript"
    else
        yell "enscript not found, installing..."
        apt-get install enscript
    fi
}

function check_ps2pdf_is_installed() {
    if which ps2pdf >/dev/null; then
        say "found ps2pdf"
    else
        yell "ps2pdf not found, please install ps2pdf"
        return 1
    fi
}

function check_jq_is_installed() {
    if which jq >/dev/null; then
        say "found jq"
    else
        yell "jq not found, installing..."
        apt-get install jq
    fi
}

function check_bats_is_installed() {
    if which bats >/dev/null; then
        say "found bats"
    else
        yell "bats not found, installing..."
        apt-get install bats
    fi
}

function check_perl_trim_is_installed() {
    local result=$(perl -MText::Trim -e 'print trim("    hello, world    ");')
    if [[ $? -eq 0 ]]; then
        if [[ "$result" = 'hello, world' ]]; then
            say "found perl Text::Trim"
        else
            yell "inconsistent perl Text::Trim output: ${result}"
            return 1
        fi
    else
        yell "perl Text::Trim not found, installing..."
        apt-get install "libtext-trim-perl"
    fi
}

function check_perl_date_manip_is_installed() {
    local result=$(perl -MDate::Manip -e 'use Date::Manip qw(ParseDate UnixDate); print UnixDate(ParseDate("10/3/1981"),"%m/%d/%y");')
    if [[ $? -eq 0 ]]; then
        if [[ "$result" = '10/03/81' ]]; then
            say "found perl Date::Manip"
        else
            yell "inconsistent perl Date::Manip output: ${result}"
            return 1
        fi
    else
        yell "perl Date::Manip not found, installing..."
        if ! which cpan; then
            cpan App::cpanminus
        fi
        cpanm Date::Manip
    fi
}

function check_pdf2txt_is_installed() {
    if which pdf2txt >/dev/null; then
        say  "found pdf2txt"
    else
        yell "pdf2txt not found, installing..."
        apt-get -f install
        apt-get install python-pdfminer
    fi
}

function check_pdftk_is_installed() {
    if which pdftk >/dev/null; then
        say  "found pdftk"
    else
        yell "pdftk not found, installing..."
        apt-get install pdftk
    fi
}

function check_dos2unix_is_installed() {
    if which dos2unix >/dev/null; then
        say  "found dos2unix"
    else
        yell "dos2unix not found, installing..."
        apt-get install dos2unix
    fi
}
function create_directories() {
    say "Creating directories"
    if [[ "${TEST}" = "true" ]]; then
         if mountpoint &> /dev/null /etl; then
            umount /etl
        fi
    else
        if ! grep -q '/etl' /etc/fstab; then
             echo 'us-east-1c.fs-ca3bfb83.efs.us-east-1.amazonaws.com:/  /etl nfs4 nfsvers=4.1 0 0' >> /etc/fstab
        fi
        if ! mountpoint &> /dev/null /etl; then
            mount /etl
        fi
    fi
    
    mkdir -p /etl/{load-in,load-archive,load-work}
    mkdir -p /etl/{mapper-in,mapper-out,mapper-err,mapper-archive}
    mkdir -p /etl/{invoices-in,invoices-err,invoices-archive}
    mkdir -p "${DU_ETL_WORK}"
    mkdir -p "${DU_ETL_CONFIG_DIR}" 
    mkdir -p "${DU_ETL_DIST_DIR}"
    mkdir -p "${DU_ETL_WORK}/mage-s360"

    for dms in $(find "${DU_ETL_HOME}"/DU-DMS/* -maxdepth 0  -type d); do
        dms=$(basename $dms)
        dms=${dms,,}  # lowercase
        dms=${dms:4}  # remove dms-
        mkdir -p "${DU_ETL_WORK}/etl-${dms}/${dms}-config"
        mkdir -p "${DU_ETL_WORK}/etl-${dms}/${dms}-zip"
        mkdir -p "${DU_ETL_WORK}/etl-${dms}/${dms}-zip/archive"
        mkdir -p "${DU_ETL_WORK}/etl-${dms}/inv-${dms}"
    done

    addgroup --gid 1009 etluser 2> /dev/null
    usermod -a -G etluser $LOGNAME
    chown --recursive etl:etluser /etl
    chmod --recursive g+rwxs /etl

    if [[ ! -e "${DU_ETL_HOME}"/DU-Load/log-last-run.txt ]]; then
        touch "${DU_ETL_HOME}"/DU-Load/log-last-run.txt
    fi
    chown etl:etluser "${DU_ETL_HOME}"/DU-Load/log-last-run.txt
    chmod g+w "${DU_ETL_HOME}"/DU-Load/log-last-run.txt
 }

function psql_bootstrap() {
    psql "service=postgres" --set=ON_ERROR_STOP=1 "$@"
}

# Set up .pg_service.conf file
# Passwords are stored in .pg_service.conf
# instead of .pgpass, since.pgpass requires
# no group or world access and thus can't be shared
# where as .pg_service.conf doesn't have this restriction
function create_pg_services() {
    local rc=0

    if [[ ! -e $HOME/.pg_service.conf ]]; then
        cat > $HOME/.pg_service.conf \
<<PG
[postgres]
user=postgres
dbname=postgres
password=changeme

[${DU_ETL_PG_SERVICE}]
user=postgres
dbname=${DU_ETL_DATABASE}
password=changeme

[${GGS_LOCAL_PG_SERVICE}]
user=postgres
dbname=ggs
password=changeme
PG
        chown $LOGNAME:$LOGNAME $HOME/.pg_service.conf
        yell "Change database passwords in $HOME/.pg_service.conf"
        return 1
    fi

    check_pg_service "postgres"                   || ((rc |= 1))
    check_pg_service "${DU_ETL_PG_SERVICE}"       || ((rc |= 1))
    check_pg_service "${GGS_LOCAL_PG_SERVICE}"    || ((rc |= 1))
    
    if [[ "$rc" = "0" ]]; then
        psql_bootstrap -c "create database $DU_ETL_DATABASE;" > /dev/null 2> /tmp/createdb.txt
        if [[ -s /tmp/createdb.txt ]] && ! grep -q "already exists" /tmp/createdb.txt; then
            yell "Error creating database $DU_ETL_DATABASE: $(< /tmp/createdb.txt)"
            ((rc |= 1))
        fi
    fi

    return $rc
}

function check_pg_service() {
    local service=$1
    say "Checking service $service"
    timeout 3s psql "service=${service}" -w -c 'SELECT 1;' >/dev/null;
    local exitcode=$?
    if [[ $exitcode = '0' ]]; then
        say "pg_service $service ok"
        return 0
    elif [[ $exitcode = '124' ]]; then
        yell "pg_service $service connection attempt timed out after 3s, please fix access"
        return 1
    elif [[ $exitcode = '2' ]]; then
        if ! grep -q ${service} ${PGSERVICEFILE}; then
            yell "pg_service $service not found in $PGSERVICEFILE,"
        fi
        return 1 # Authentication error
    fi
}

function run_tests() {
    local rc=0
    (
        if ! $DU_ETL_HOME/run-tests; then
            ((rc |= 1))
        fi
    )
    return $rc
}

function install_user() {
    if ! id -u $INSTALL_USER > /dev/null; then        
        adduser --shell /bin/bash $INSTALL_USER
        usermod -a -G etluser $INSTALL_USER
    fi
    mkdir -p /home/<USER>/tmp
}

function usage() {
    if [ "$*" != "" ] ; then
        scream "Error: $*"
    fi

    cat >&2 << EOF
Usage: ./etl-install [--options]

Install/configure ETL environment

Options:
--help                      displays this usage message and exits
--test-env                  configure test environment, defaults to prod
                            (uses local directories instead of shared /etl mount)
--user <name>               Configures a user to use ETL. Adds user if user does not exist

Prereqisities:
* DU-ETL repository is cloned (needed for this script, other repos cloned automatically)
* etl user is current user
* Script is run as sudo
EOF
    exit 1
}

INSTALL_USER=
TEST=false

while [ $# -gt 0 ]; do
    case "$1" in
        --help                ) usage;                                            shift 1;;
        --test-env            ) TEST=true;                                        shift 1 ;;
        --user                ) INSTALL_USER="${2:?Username required}";           shift 2 ;;
                            * ) usage "Unrecognized option $1; exiting";          exit 1 ;;
    esac
done

if [[ -n $INSTALL_USER ]]; then
    install_user || die "Installation Failed!"
else
    if main; then
        say "Installation successful"
    else
        die "Installation Failed!"
    fi
fi
exit 0
