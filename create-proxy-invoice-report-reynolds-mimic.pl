#!/usr/bin/env perl
# Generate Fixed width Proxy invoice report
# This script requires the installation of DBI, DBD:ODBC, DBD:Pg interface/connector to connect <PERSON><PERSON> with database.

use strict;
use warnings;
use DBI;
use DBD::Pg;
use Text::Wrap;
use Text::Tabs;
use Term::ANSIColor qw( colored );
use Time::Format qw(%time %strftime %manip);
use POSIX qw/ceil/;

no warnings 'uninitialized';


# Environment variable 'DU_ETL_PG_SERVICE' - psql connection service
my $db_conn = $ENV{'DU_ETL_PG_SERVICE'};

# Argument to create Proxy invoice - Proxy invoice schema name
my $schema_name = $ARGV[0];
my $single_store_flag = $ARGV[1]; 
my $custom_branch_name = $ARGV[2];

# Connect to postgresql database using psql service
my $conn = DBI->connect("dbi:Pg:service=$db_conn", undef, undef, { RaiseError => 1 });

$conn->do("SET search_path to $schema_name") or die;

# Border will be printed with Debug mode
my $debug_mode = $ENV{'DU_ETL_PROXYINVOICE_DEBUG'};
$debug_mode = defined $debug_mode ? $debug_mode : 0;

my $ro_number;

my @header_section;
my @header_subsection;
my @job_section;
my @grand_total_section;
my @end_of_invoice_section;
my @invoice_note_section;

my ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

my ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

my ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

my ($sale_amount, $labor_cost, $parts_cost, $parts_sale,  $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale, $bill_time_tech) = (0)x13;

my ($store_number)= (0)x1;

my ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor, $customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $customer_phone, $other_phone, $work_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $vehicle_color, $mileage_out, $license_number, $tag_no, $delivery_date, $promised, $ready, $payment_code,
     $comments, $stock_no, $ro_ext, $ro_date, $accounting_make_code, $adv_no, $carline, $tax_customer, $tax_warranty, $tax_internal )= (0)x36;

my ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
         $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale, $prt_count)= (0)x11;

my $job_hours = 0;
my ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;

my ($parts_entries, $misc_entries, $tax_entries, $gog_entries, $sublet_entries, $ded_entries, $comments_entries, $recommendation_entries, $chg_misc_entries, $chg_misc_total_entries) = (0)x10;
my $lbr_type;
my $page_max_height = 44; # Maximum no of lines in page body
my $page_header_height = 8; # No of lines in header section
my $invoice_dealer_header = 1; # No of lines in the dealer header section (1st page)
my $total_header_height;
my $extra_tech_line = 0;
my $curr_page_height = 0;
my $invoice_note_lines = 4; # No of lines needed for invoice note
my $pages;
my $curr_page_num;
my $invoice_datetime;
my $cust_inv_head;
my $parts_total_sale;
my $customer_total_amount=0;
my $labor_total = 0;
my $parts_total = 0;
my $gog_total = 0;
my $sub_labor_total = 0;
my $sub_parts_total = 0;
my $misc_total = 0;
my $in_total = 0;
my $total_parts = 0;
my $total_cost = 0;
my $total_misc_chg = 0;
my $total_misc_dis_sale = 0;
my $total_misc_dis_cost = 0;
my $total_gog = 0;
my $total_sublet = 0;
my $total_sublet_cost = 0;
my $total_sale_amount=0;
my $total_parts_amount=0;
my $customer_total_tax=0;
my $warranty_total_tax=0;
my $internal_total_tax=0;
my $customer_labor_cost_grand_total = 0;
my $internal_labor_cost_grand_total = 0;
my $warranty_labor_cost_grand_total = 0;
my $customer_parts_cost_grand_total = 0;
my $internal_parts_cost_grand_total = 0;
my $warranty_parts_cost_grand_total = 0;
my $customer_labor_sale_grand_total = 0;
my $internal_labor_sale_grand_total = 0;
my $warranty_labor_sale_grand_total = 0;
my $customer_parts_sale_grand_total = 0;
my $internal_parts_sale_grand_total = 0;
my $warranty_parts_sale_grand_total = 0;
my  $customer_total_misc_chg_sale=0;
my  $customer_total_misc_chg_cost=0;
my  $customer_total_misc_dis_sale=0;
my  $customer_total_misc_dis_cost=0;
my  $warranty_total_misc_chg_sale=0;
my  $warranty_total_misc_chg_cost=0;
my  $warranty_total_misc_dis_cost=0;
my  $warranty_total_misc_dis_sale=0;
my  $internal_total_misc_chg_sale=0;
my  $internal_total_misc_chg_cost=0;
my  $internal_total_misc_dis_cost=0;
my  $internal_total_misc_dis_sale=0;
my  $customer_total_gog=0;
my  $warranty_total_gog=0;
my  $internal_total_gog=0;
my  $customer_total_sublet=0;
my  $warranty_total_sublet=0;
my  $internal_total_sublet=0;
my  $customer_total_sublet_cost=0;
my  $warranty_total_sublet_cost=0;
my  $internal_total_sublet_cost=0;
my  $grand_total_sublet=0;
my  $customer_grandtotal_misc_dis = 0;
my  $internal_grandtotal_misc_dis = 0;
my  $warranty_grandtotal_misc_dis = 0;
my  $customer_grandtotal_sublet = 0;
my  $internal_grandtotal_sublet = 0;
my  $warranty_grandtotal_sublet = 0;
my  $customer_grandtotal_sublet_cost = 0;
my  $internal_grandtotal_sublet_cost = 0;
my  $warranty_grandtotal_sublet_cost = 0;


my $ro_open_void_qry = $conn->prepare(
    "WITH inv_master AS (SELECT
                        substring(ro_number FROM '^[0-9]+')    AS number_part,
                        substring(ro_number FROM '[A-Za-z]+') AS code_part
                    FROM repair_order
       )
      ,series_gap (max_ser_ro, min_ser_ro) AS (
         SELECT * FROM (SELECT
                            number_part::integer,
                            lag(number_part::integer) OVER ranges,
                            number_part::integer - lag(number_part::integer) OVER ranges AS diff,
                            code_part
                    FROM inv_master
                    WINDOW ranges AS (order by number_part::integer))t
         WHERE diff <= 100)
     SELECT ro_number
     FROM repair_order
     WHERE completion_date IS NULL
     UNION ALL
     SELECT generate_series(min_ser_ro+1, max_ser_ro-1)::text||code_part
     FROM series_gap");
     

$ro_open_void_qry->execute();
while (my @open_ro_list = $ro_open_void_qry->fetchrow_array) {
    ($ro_number) = @open_ro_list;
    my $file_open = $ro_number . ".txt";
    unless(open FILE, '>'.$file_open) {
        die "\nUnable to create $file_open\n";
    }
    print_open_ro();
    close FILE;
}

# my $ro_open_void_series_qry = $conn->prepare(
#     "SELECT * FROM (SELECT
# 						    lag(ro_number::integer) OVER ranges AS start_ro,
#                             ro_number::integer AS end_ro,
#                             ro_number::integer - lag(ro_number::integer) OVER ranges AS diff
#                     FROM du_dms_reynolds_proxy.repair_order  
#                     WHERE ro_number ~ '^\\d+\$'     
#                     WINDOW ranges AS (order by ro_number::integer))t
#          WHERE diff >= 2");

# $ro_open_void_series_qry->execute();
# my ($start_ro, $end_ro, $diff);
# while (my @open_ro_series_list = $ro_open_void_series_qry->fetchrow_array) {
#     ($start_ro, $end_ro, $diff) = @open_ro_series_list;
#     my $series_start = $start_ro+1;
#     my $series_end = $end_ro-1;
#     my $bundle_series_start = int($series_start/2);
#     my $bundle_series_end = int($series_end/2);
#     my @a = ($bundle_series_start..$bundle_series_end);
#     my $i;
#     for $i (@a){
#         my $suffixStart = "00";
#         my $suffixEnd = "99";
#         my $tempStart = $i.$suffixStart+0;
#         my $tempEnd = $i.$suffixEnd+0; 
#         my $start;
#         my $end;
#         if($tempStart >= $series_start && $tempStart <= $series_end){
#           $start = $tempStart;
#         } else{
#           $start = $series_start; 
#         }
#         if($tempEnd <= $series_end && $tempEnd >= $series_start){
#           $end = $tempEnd; 
#         } else{
#           $end = $series_end;  
#         }
#         my $ro_range = $start."-".$end;
#         my $file_open = $start . ".txt";
#         unless(open FILE, '>'.$file_open) {
#             die "\nUnable to create $file_open\n";
#         }
#         print_open_ro_series($start, $end);
#         close FILE;
#     }
# }

my $ro_qry = $conn->prepare("SELECT ro_number
                               FROM repair_order
                              ORDER BY ro_number");

$ro_qry->execute();
while (my @ro_list = $ro_qry->fetchrow_array) {
    ($ro_number) = @ro_list;
    my $temp_ro_number = $ro_number;
    $temp_ro_number =~ s/^0+//;
    my $file = $temp_ro_number . ".txt";
    unless(open FILE, '>'.$file) {
        die "\nUnable to create $file\n";
    }
    print_ro();
    close FILE;
    my $file_inv_head = "inv-bg/".$temp_ro_number . "-inv-bg.txt";
    unless(open FILE, '>'.$file_inv_head) {
        die "\nUnable to create $file_inv_head\n";
    }
    print_ro_inv_bg_head();
    close FILE;

    load_file_into_database($ro_number, $file);
}

$conn->disconnect;

sub load_file_into_database {
    my ($ro_number, $ro_document_file) = @_;
    open ROFILE, $ro_document_file || print "Cannot open file";
    my $ro_document;
    while (<ROFILE>) {
        $ro_document .= $_;
    }
    close ROFILE;
#   my $ro_doc_insert = $conn->prepare("INSERT INTO repair_order_print VALUES (?, ?)") || print "Doc Insert Preparse Failed";
#   $ro_doc_insert->execute($ro_number, $ro_document) || print "Doc Insert Failed";
#    $ro_doc_insert->finish;
}
  
# Subroutine to prepare HEADER section of the invoice
sub make_header {
     my $ro_header_qry = $conn->prepare("SELECT
                                to_char(ro.open_time, 'HH24:MI') || ' ' || to_char(ro.creation_date, 'ddMONyy') AS creation_date,
                                to_char(ro.completion_date, 'mm/dd/yyyy') AS completion_date,
                                ro.department,
                                ro.branch,
                                ro.sub_branch,
                                ro.advisor,
                                roc.customer_number,
                                roc.customer_name,
                                roc.customer_address,
                                roc.customer_city,
                                roc.customer_state,
                                roc.customer_zip,
                                roc.customer_phone,
				                roc.other_phone,
			                    roc.work_phone,
                                roc.customer_email,
                                rov.vehicle_make,
                                rov.vehicle_model,
                                --rov.vehicle_vin,
				                --regexp_replace(rov.vehicle_vin, E'(.)(?!$)', E'\\1 ', 'g') as vehicle_vin,
				                trim(rov.vehicle_vin) AS vehicle_vin,
                                rov.vehicle_year,
                                rov.mileage_in,
                                rov.vehicle_color,
                                rov.mileage_out,
                                roc.license_number,
                                ro.tag_no,
                                to_char(ro.delivery_date, 'mm/dd/yyyy') AS delivery_date,
                                to_char(ro.promised_time, 'HH24:MI') || ' ' || to_char(ro.promised_date, 'ddMONyy') AS promised,
                                to_char(ro.booked_time, 'HH24:MI') || ' ' || to_char(ro.booked_date, 'ddMONyy') AS ready,
                                ro.payment_code,
                                ro.comments,
                                ro.stock_no,
				                ro_ext,
                                to_char(ro.creation_date, 'mm/dd/yy') AS ro_date,
                                ro.accounting_make_code,
                                ro.advisor_number,
				                rov.carline,
								ro.tax_customer,
								ro.tax_warranty,
								ro.tax_internal
                            FROM repair_order ro
                                JOIN repair_order_customer roc USING (ro_number)
                                JOIN repair_order_vehicle rov USING (ro_number)
                            WHERE ro.ro_number = ?");

    $ro_header_qry->execute($ro_number);
    my (@header_row) = $ro_header_qry->fetchrow_array();
     ($creation_date, $completion_date, $department, $branch, $sub_branch, $advisor, $customer_number, $customer_name, $customer_address, $customer_city,
        $customer_state, $customer_zip, $customer_phone, $other_phone, $work_phone, $customer_email, $vehicle_make, $vehicle_model, $vehicle_vin , $vehicle_year,
        $mileage_in, $vehicle_color, $mileage_out, $license_number, $tag_no, $delivery_date, $promised, $ready, $payment_code, $comments, $stock_no, $ro_ext,
        $ro_date, $accounting_make_code, $adv_no, $carline,  $tax_customer, $tax_warranty, $tax_internal ) = @header_row;

if($single_store_flag eq 'true'){
    $accounting_make_code = $custom_branch_name;
}

$cust_inv_head = $ro_ext;
$vehicle_vin =~ s/.\K(?=.)/ /sg;
$grand_total_sublet=0;
$customer_total_misc_chg_sale = 0;
$warranty_total_misc_chg_sale = 0;
$internal_total_misc_chg_sale = 0;
$customer_total_misc_chg_cost = 0;
$warranty_total_misc_chg_cost = 0;
$internal_total_misc_chg_cost = 0;

$customer_grandtotal_misc_dis = 0;
$warranty_grandtotal_misc_dis = 0;
$internal_grandtotal_misc_dis = 0;

$customer_total_misc_chg_sale = 0;
$warranty_total_misc_chg_sale = 0;
$internal_total_misc_chg_sale = 0;
$customer_total_misc_chg_cost = 0;
$warranty_total_misc_chg_cost = 0;
$internal_total_misc_chg_cost = 0;

$total_misc_chg = 0;

    my $store_number_query = $conn->prepare("SELECT
                store_number::text
            FROM repair_dealer_details LIMIT 1");
    my (@store_number_row) = $store_number_query->execute();
    ($store_number)= @store_number_row;

	push (@header_section, sprintf(border(">").'~font{HelveticaMono2}'."%-100s"."\n", ""));    
 	push (@header_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%2s%-20s%-20s%-5s%-6s%-10s".'~font{DejaVuSansMono-Bold9}'." %41s".'~font{default}'.'~color{0 0 0}'.border("|")."\n",
           "", $customer_name, $advisor, $adv_no , $tag_no,  $completion_date, $accounting_make_code."+".$department.$ro_number));    
	push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%2s%-80s%-8s%-15s%-6s".'~font{default}'.border("|")."\n", "",$customer_email,"","",""));
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%2s%-39s%-10s%-16s%-12s%-3s%-8s%-15s%-6s".'~font{default}'.border("|")."\n", "",substr($customer_address, 0, 39),"", substr($mileage_in,0,15), substr($vehicle_color,0, 14)."","","","",""));
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%-82s%-8s%-15s%-6s".'~font{default}'.border("|")."\n", "","","",""));
        push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%1s%-33s%-38s%-10s%-20s".'~font{default}'.border("|")."\n","","",substr(substr($vehicle_year, -2)."/".$vehicle_make."/".$carline."/".$vehicle_model, 0, 36),$delivery_date,""));
    push (@header_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s"."\n", ""));
    push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%2s%-33s%-47s %-14s %-10s ".'~font{default}'.border("|")."\n","",substr($customer_city.",".$customer_state." ".$customer_zip, 0, 38),substr($vehicle_vin, 0, 47), substr('',0, 14),substr('',0, 15)));
    
push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%1s%-8s%-78s %-4s %-19s".'~font{default}'.border("|")."\n","",$customer_number,substr($customer_phone, 0, 38)." ".substr($work_phone, 0, 38)." ".substr($other_phone, 0, 38),substr($license_number,0,15),$ro_date));
    push (@header_section, sprintf(border(">").'~font{HelveticaMono3}'."%-100s".border("|")."\n", ""));
   push (@header_section, sprintf(border(">").'~font{DejaVuSansMono8}'."%93s %-7s ".'~font{default}'.border("|")."\n","","MO: ".substr($mileage_out,0, 16)));
    push (@header_section, sprintf(border(">").'~font{HelveticaMono1}'."%-100s".'~font{default}'.border("|")."\n", ""));
     
}

# Subroutine to prepare the JOB section of an RO
sub make_job_section {
    my $job_qry = $conn->prepare("SELECT
                                    rl.ro_line::numeric,
                                    rl.complaint,
                                    rj.ro_job_line,
                                    left(rj.billing_code, 1) AS billing_code,
                                    rj.billing_code          AS billing_labor_type,
                                    case when up_sell_flag='Y'
	    			    then  '+'||rj.op_code
					else rj.op_code end op_code_job,
                                    rj.op_code,
                                    rj.op_description,
                                    rj.sold_hours,
                                    rj.labor_cost,
                                    rj.sale_amount,
                                    rj.cause,
                                    rj.correction,
                                    rj.parts_cost,
                                    case when left( rj.billing_code  ,1)='C' then 'CUSTOMER' when left( rj.billing_code  ,1)='W' then 'WARRANTY' when left( rj.billing_code  ,1)='I' then 'INTERNAL' end as billing_type,
				    rj.up_sell_flag,
                                    rj.billtime_tech
                                FROM repair_line rl
                                    JOIN repair_job rj USING (ro_number, ro_line)
                                WHERE rl.ro_number = ? ORDER BY ro_line :: numeric");

    $job_qry->execute($ro_number);
      $customer_labor_cost_grand_total = 0;
      $internal_labor_cost_grand_total = 0;
      $warranty_labor_cost_grand_total = 0;
      $customer_parts_cost_grand_total = 0;
      $internal_parts_cost_grand_total = 0;
      $warranty_parts_cost_grand_total = 0;
      $customer_labor_sale_grand_total = 0;
      $internal_labor_sale_grand_total = 0;
      $warranty_labor_sale_grand_total = 0;
      $customer_parts_sale_grand_total = 0;
      $internal_parts_sale_grand_total = 0;
      $warranty_parts_sale_grand_total = 0;
      
        $customer_total_gog=0;
        $warranty_total_gog=0;
        $internal_total_gog=0;
        $customer_total_sublet=0;
        $warranty_total_sublet=0;
        $internal_total_sublet=0;
        $bill_time_tech=0;
        my ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code_job, $op_code, $op_description , $sold_hours, $cause, $correction, $parts_cost,$billing_type,$up_sell_flag,$billtime_tech );
    while (my @job_row = $job_qry->fetchrow_array) {
        ( $ro_line, $complaint, $ro_job_line, $billing_code, $billing_labor_type, $op_code_job, $op_code, $op_description , $sold_hours, $labor_cost, $sale_amount, $cause, $correction, $parts_cost,$billing_type,$up_sell_flag,$billtime_tech  ) = @job_row;
         $bill_time_tech=$billtime_tech;       
        my @job_header_section;
                my $techs_id_qry = $conn->prepare("SELECT DISTINCT
                            tech_id
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
        $techs_id_qry->execute($ro_number, $ro_line, $ro_job_line);
        my ($tech_id, $job_tech_id);
        if($techs_id_qry->rows > 0){
            while (my @job_id_tech_row = $techs_id_qry->fetchrow_array) {
                ($tech_id)= @job_id_tech_row;
                $job_tech_id = $job_tech_id ." ". $tech_id;
            }
        }
        $Text::Wrap::columns = 34;
        my $wrapped_job_tech_id = fill('', '', expand($job_tech_id));
        my @job_tech_id_list = split "\n", $wrapped_job_tech_id;

                my $techs_qry = $conn->prepare("SELECT
                            tech_id,
                            actual_hours,
                            booked_hours,
			    to_char(work_date, 'mm/dd/yy') AS work_date,
                            work_start_time,
                            work_end_time,
                            --booked_hours-actual_hours as time_taken,
                            booked_hours as time_taken,
                            work_note
                        FROM repair_job_technician_detail td
                        WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ?");
        $techs_qry->execute($ro_number, $ro_line, $ro_job_line);
        # my @job_tech_row = $techs_qry->fetchrow_array;
        my ($job_tech, $act_time, $booked_time,$work_date,$start_time,$end_time,$time_taken,$work_note);
         push (@job_header_section, sprintf(border(">")."%3s%-5s%-50s".border("|")."\n",
                                           "","JOB#".$ro_line."	CHARGES","-"x82));
          
        push (@job_header_section, sprintf(border(">")."%-16s".border("|")."\n", ""));

            push (@job_header_section, sprintf(border(">")."%4s%-5s%-75s".border("|")."\n",
                                           "","LABOR----".$billing_type,"-"x70));
            if($bill_time_tech > 0){
            	push (@job_header_section, sprintf(border(">")."%4s%-9s%-15s%-29s%-9s%-2s%-14s%10.2f".border("|")."\n",
                                           "","J# ".$ro_line, $op_code_job, substr($op_description, 0, 38),"UNITS:".$bill_time_tech,"","TECH(S):".$job_tech_id_list[0], $sale_amount));  
            }
            else{
		push (@job_header_section, sprintf(border(">")."%4s%-9s%-15s%-29s%-9s%-2s%-14s%10.2f".border("|")."\n",
                                           "","J# ".$ro_line, $op_code_job, substr($op_description, 0, 38),"UNITS:","","TECH(S):".$job_tech_id_list[0], $sale_amount));  
            }	        
 	   if($up_sell_flag eq "Y"){
		push (@job_header_section, sprintf(border(">")."%4s%-9s".border("|")."\n",
                                           "","Added Operation  (".$advisor." @ ".$completion_date.")"));
            }
        if($techs_qry->rows > 0){
                push (@job_header_section, sprintf(border(">")."%8s%-10s%-12s%-10s%-10s%-10s%10s".border("|")."\n",
                                           "","TECH#",""," ACT","TIME","",""));
                my $tot_act_time=0;
                my $tot_time_taken=0;
        	while (my @job_tech_row = $techs_qry->fetchrow_array) {
            		
        		# my @job_tech_row = $techs_qry->fetchrow_array;
        		my $tech_lic = "";
        		($job_tech, $act_time, $booked_time,$work_date,$start_time,$end_time,$time_taken,$work_note) = @job_tech_row;
        		$job_hours = $sold_hours;
            		push (@job_header_section, sprintf(border(">")."%8s%-19s%7.2f%10.2f%10s".border("|")."\n",
                                           "",$job_tech,$act_time, $time_taken,""));  
                        $tot_act_time = $tot_act_time + $act_time;
                        $tot_time_taken = $tot_time_taken + $time_taken;
		}  
                push (@job_header_section, sprintf(border(">")."%10s%-3s%-5s%4.2f%10.2f".border("|")."\n",
                                           "","TOTAL TECH TIME","", $tot_act_time,$tot_time_taken));
        } 
        push (@job_header_section, sprintf(border(">")."%-16s".border("|")."\n", ""));

            	   # Job Complaint
        $Text::Wrap::columns = 67;
        my $wrapped_complaint = fill('', '', expand($complaint));
        my @complaint_list = split "\n", $wrapped_complaint;

        push (@job_header_section, sprintf(border(">")."%-4s%-12s %-10s %-2s".border("|")."\n","","COMPLAINT",$complaint_list[0],""));
        foreach my $i (1 .. $#complaint_list) {
            push (@job_header_section, sprintf(border(">")."%-16s %-10s %-2s".border("|")."\n", "", $complaint_list[$i],""));
        }
        push (@job_section, [@job_header_section]);

	# Job Cause
        if ($cause){
            my @job_cause_section;
            $Text::Wrap::columns = 61;
            my $wrapped_cause = fill('', '', expand($cause));
            my @cause_list = split "\n", $wrapped_cause;
            push (@job_cause_section, sprintf(border(">")."%-4s%-12s %-10s %-2s".border("|")."\n", "","CAUSE", $cause_list[0], ""));
            foreach my $i (1 .. $#cause_list) {
                push (@job_cause_section, sprintf(border(">")."%-16s %-10s %-2s".border("|")."\n", "", $cause_list[$i], ""));
            }
            push (@job_section, [@job_cause_section]);
        }
        
         # Job Correction
        if ($correction){
            my @job_correction_section;
            $Text::Wrap::columns = 67;
            my $wrapped_correction = fill('', '', expand($correction));
            my @correction_list = split "\n", $wrapped_correction;
            push (@job_correction_section, sprintf(border(">")."%-4s%-12s %-10s %-2s".border("|")."\n", "","CORRECTION", $correction_list[0], ""));
            foreach my $i (1 .. $#correction_list) {
                push (@job_correction_section, sprintf(border(">")."%-16s %-10s %-2s".border("|")."\n", "", $correction_list[$i], ""));
            }
            push (@job_correction_section, sprintf(border(">")."%-16s".border("|")."\n", ""));
            push (@job_section, [@job_correction_section]);
        }

	my @parts_section = make_parts_section($ro_line, $ro_job_line);
        push (@job_section, [@parts_section]);

        my @total_parts_section;
        if(($total_parts > 0) or ($total_cost > 0)){
            push (@total_parts_section, sprintf(border(">")."%-62s%-9s%10.2f".border("|")."\n", "", "COST TOTAL",$total_cost ));
            push (@total_parts_section, sprintf(border(">")."%-75s  %-10s %11.2f".border("|")."\n", "", "TOTAL - PARTS", $total_parts ));
            push (@job_section, [@total_parts_section]);
        }

	my @gog_section = make_gog_section($ro_number, $ro_line);
        push (@job_section, [@gog_section]);
        my @total_gog_section;
        if($total_gog > 0){
            push (@total_gog_section, sprintf(border(">")."%-75s  %-13s %11.2f".border("|")."\n", "", "TOTAL - GOG", $total_gog ));
            push (@job_section, [@total_gog_section]);
        }

	my @sublet_section = make_sublet_section($ro_number, $ro_line);
        push (@job_section, [@sublet_section]);
        my @total_sublet_section;
        if($total_sublet > 0){
            push (@total_sublet_section, sprintf(border(">")."%-75s  %-9s %10.2f".border("|")."\n", "", "TOTAL - SUBLET", $total_sublet ));
            push (@job_section, [@total_sublet_section]);
        }

     #   $grand_total_sublet = $grand_total_sublet + $total_sublet;

        my @misc_section = make_misc_section($ro_number, $ro_line);
        push (@job_section, [@misc_section]);
        my @total_misc_section;
    	if( $total_misc_dis_sale < 0){
       		push (@total_misc_section, sprintf(border(">")."%-75s  %-10s %11.2f".border("|")."\n", "", "TOTAL - MISC",  $total_misc_dis_sale ));
       		push (@job_section, [@total_misc_section]);
    	}

	my @total_labor_parts_section ;
        my $total_labor_parts= 0;
        $total_labor_parts = $total_parts + $sale_amount;
         
        my $total_labor_parts_cost=0;        
        my $total_labor_parts_sale=0;

        $total_labor_parts_cost = $labor_cost + $total_cost;
        $total_labor_parts_sale = $sale_amount + $total_parts;

        my $customer_labor_cost=0;
	my $warranty_labor_cost=0;
	my $internal_labor_cost=0;
	my $customer_sale_amount=0;
	my $warranty_sale_amount=0;
	my $internal_sale_amount=0;
	my $customer_total_parts = 0;
        my $customer_total_cost = 0;
        my $warranty_total_parts = 0;
        my $warranty_total_cost = 0;
	my $internal_total_parts = 0;
        my $internal_total_cost = 0;
	my $customer_total_labor_parts_cost =0;
	my $customer_total_labor_parts_sale = 0;
	my $warranty_total_labor_parts_cost = 0;
	my $warranty_total_labor_parts_sale = 0;
	my $internal_total_labor_parts_cost = 0;
	my $internal_total_labor_parts_sale = 0;
       
       

        if($billing_type eq "CUSTOMER")
        {
                if($labor_cost == 0 || $labor_cost eq '0.00'){
			$customer_labor_cost = "0.00";
		}
		else{
			$customer_labor_cost = $labor_cost;
		}
		if($sale_amount == 0 || $sale_amount eq '0.00'){
			$customer_sale_amount= "0.00";		
		}
		else{
			$customer_sale_amount = $sale_amount;
		}
		if($total_parts == 0 || $total_parts eq '0.00'){
			$customer_total_parts = "0.00";	
		}
		else{
			$customer_total_parts = $total_parts;
		}
                if($total_cost == 0 || $total_cost eq '0.00'){
			$customer_total_cost = "0.00";	
		}
		else{
			$customer_total_cost = $total_cost;
		}
		$warranty_labor_cost="0.00";
		$internal_labor_cost="0.00";
		$warranty_sale_amount="0.00";
		$internal_sale_amount="0.00";		
                $warranty_total_parts = "0.00";
		$internal_total_parts = "0.00"; 
                $warranty_total_cost = "0.00";
		$internal_total_cost = "0.00";                
		$customer_total_misc_dis_sale= $total_misc_dis_sale;
		$customer_total_misc_dis_cost= $total_misc_dis_cost;
                $customer_total_gog=$total_gog;
                $customer_total_sublet=$total_sublet;
                $customer_total_sublet_cost=$total_sublet_cost;
				$internal_total_sublet=0;
                $internal_total_sublet_cost=0;
				$warranty_total_sublet=0;
                $warranty_total_sublet_cost=0;
		$customer_total_labor_parts_cost = $total_labor_parts_cost + $total_misc_chg + $customer_total_misc_dis_cost + $total_gog + $total_sublet;
		$customer_total_labor_parts_sale = $total_labor_parts_sale + $customer_total_misc_dis_sale + $customer_total_gog + $customer_total_sublet;
		$warranty_total_labor_parts_cost = 0;
		$warranty_total_labor_parts_sale = 0;
		$internal_total_labor_parts_cost = 0;
		$internal_total_labor_parts_sale = 0;
                $customer_labor_sale_grand_total = $customer_labor_sale_grand_total + $sale_amount;
                $customer_labor_cost_grand_total = $customer_labor_cost_grand_total + $labor_cost;
                $customer_parts_sale_grand_total = $customer_parts_sale_grand_total + $total_parts;
                $customer_parts_cost_grand_total = $customer_parts_cost_grand_total + $total_cost;
		$customer_grandtotal_misc_dis= $customer_grandtotal_misc_dis+ $total_misc_dis_sale;
		$customer_grandtotal_sublet=$customer_grandtotal_sublet + $total_sublet;
                $customer_grandtotal_sublet_cost=$customer_grandtotal_sublet_cost + $total_sublet_cost;
	}
        elsif($billing_type eq "WARRANTY")
        {
		if($labor_cost == 0 || $labor_cost eq '0.00'){
			$warranty_labor_cost = "0.00";
		}
		else{
			$warranty_labor_cost = $labor_cost;
		}
		if($sale_amount == 0  || $sale_amount eq '0.00'){
			$warranty_sale_amount= "0.00";		
		}
		else{
			$warranty_sale_amount = $sale_amount;
		}
		if($total_parts == 0  ||  $total_parts eq '0.00'){
			$warranty_total_parts = "0.00";	
		}
		else{
			$warranty_total_parts = $total_parts;
		}
                if($total_cost == 0 || $total_cost eq '0.00'){
			$warranty_total_cost = "0.00";	
		}
		else{
			$warranty_total_cost = $total_cost;
		}
		$customer_labor_cost ="0.00";
		$internal_labor_cost="0.00";
		$customer_sale_amount= "0.00";
		$internal_sale_amount="0.00";
		$customer_total_parts = "0.00";
		$internal_total_parts = "0.00";
                $customer_total_cost = "0.00";
		$internal_total_cost = "0.00";
		$customer_total_labor_parts_cost = 0;
		$customer_total_labor_parts_sale = 0;                
                $warranty_total_gog=$total_gog;
                $warranty_total_sublet=$total_sublet;
                $warranty_total_sublet_cost=$total_sublet_cost;
				$customer_total_sublet=0;
                $customer_total_sublet_cost=0;
				$internal_total_sublet=0;
                $internal_total_sublet_cost=0;
		$warranty_total_misc_dis_sale = $total_misc_dis_sale;
		$warranty_total_misc_dis_cost = $total_misc_dis_cost;
		$warranty_total_labor_parts_cost = $total_labor_parts_cost + $total_misc_chg + $warranty_total_misc_dis_cost + $total_gog + $total_sublet;
		$warranty_total_labor_parts_sale = $total_labor_parts_sale +  $warranty_total_misc_dis_sale + $warranty_total_gog + $warranty_total_sublet;
		$internal_total_labor_parts_cost = 0;
		$internal_total_labor_parts_sale = 0;
                $warranty_labor_sale_grand_total = $warranty_labor_sale_grand_total + $sale_amount;
                $warranty_labor_cost_grand_total = $warranty_labor_cost_grand_total + $labor_cost;
                $warranty_parts_sale_grand_total = $warranty_parts_sale_grand_total + $total_parts;
                $warranty_parts_cost_grand_total = $warranty_parts_cost_grand_total + $total_cost;
              # $warranty_total_misc_chg_sale= $warranty_total_misc_chg_sale + $total_misc_chg_sale ;
		$warranty_grandtotal_misc_dis = $warranty_grandtotal_misc_dis + $total_misc_dis_sale;
		$warranty_grandtotal_sublet=$warranty_grandtotal_sublet + $total_sublet;
                $warranty_grandtotal_sublet_cost=$warranty_grandtotal_sublet_cost + $total_sublet_cost;
	}
	elsif($billing_type eq "INTERNAL")
        {
                if($labor_cost == 0 || $labor_cost eq '0.00'){
			$internal_labor_cost = "0.00";
		}
		else{
			$internal_labor_cost = $labor_cost;
		}
		if($sale_amount == 0 || $sale_amount eq '0.00'){
			$internal_sale_amount= "0.00";		
		}
		else{
			$internal_sale_amount = $sale_amount;
		}
		if($total_parts == 0 || $total_parts eq '0.00'){
			$internal_total_parts = "0.00";	
		}
		else{
			$internal_total_parts = $total_parts;
		}
                if($total_cost == 0 || $total_cost eq '0.00'){
			$internal_total_cost = "0.00";	
		}
		else{
			$internal_total_cost = $total_cost;
		}
		$customer_labor_cost ="0.00";
		$warranty_labor_cost="0.00";
		$customer_sale_amount= "0.00";
		$warranty_sale_amount="0.00";
		$customer_total_parts = "0.00";
                $warranty_total_parts = "0.00";
                $customer_total_cost = "0.00";
                $warranty_total_cost = "0.00";
		$customer_total_labor_parts_cost = 0;
		$customer_total_labor_parts_sale = 0;
		$warranty_total_labor_parts_cost = 0;
		$warranty_total_labor_parts_sale = 0;                
		$internal_total_misc_dis_sale = $total_misc_dis_sale;  
        $internal_total_misc_dis_cost = $total_misc_dis_cost;     		
                $internal_total_gog=$total_gog;
                $internal_total_sublet=$total_sublet;
                $internal_total_sublet_cost=$total_sublet_cost;
				$warranty_total_sublet=0;
                $warranty_total_sublet_cost=0;
				$customer_total_sublet=0;
                $customer_total_sublet_cost=0;
		$internal_total_labor_parts_cost = $total_labor_parts_cost + $total_misc_chg + $internal_total_misc_dis_cost + $total_gog + $total_sublet;
		$internal_total_labor_parts_sale = $total_labor_parts_sale + $internal_total_misc_dis_sale + $internal_total_gog + $internal_total_sublet;
                $internal_labor_sale_grand_total = $internal_labor_sale_grand_total + $sale_amount;
                $internal_labor_cost_grand_total = $internal_labor_cost_grand_total + $labor_cost;
                $internal_parts_sale_grand_total = $internal_parts_sale_grand_total + $total_parts;
                $internal_parts_sale_grand_total = $internal_parts_sale_grand_total + $total_parts;
                $internal_parts_cost_grand_total = $internal_parts_cost_grand_total + $total_cost;
               # $internal_total_misc_chg=$internal_total_misc_chg + $total_misc_chg ;
		$internal_grandtotal_misc_dis = $internal_grandtotal_misc_dis + $total_misc_dis_sale;
		$internal_grandtotal_sublet=$internal_grandtotal_sublet + $total_sublet;
                $internal_grandtotal_sublet_cost=$internal_grandtotal_sublet_cost + $total_sublet_cost;
	}
        push (@total_labor_parts_section, sprintf(border(">")."%2s %10s".border("|")."\n",
                                                    "","JOB#".$ro_line." TOTALS---------CUSTOMER------------------WARRANTY------------------INTERNAL------------------"));
        push (@total_labor_parts_section, sprintf(border(">")."%-16s %-5s %-2s %-6s %-2s %-6s %-2s %-5s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %6s %2s %6s".border("|")."\n",
                                                    "","GL","","COST","","SALE","","GL","","COST","","SALE","","GL","","COST","","SALE"));
	if($customer_sale_amount  > 0 || $warranty_sale_amount  > 0 || $internal_sale_amount  > 0 || $customer_labor_cost  > 0 || $warranty_labor_cost  > 0 || $internal_labor_cost  > 0){          
		push (@total_labor_parts_section, sprintf(border(">")."%-4s%-16s%-2s%8.2f%-2s%8.2f%-2s%-6s%-3s%8.2f%-2s%8.2f%-2s%-6s%-6s%8.2f%-2s%8.2f".border("|")."\n",
                                                    "","LABOR","",$customer_labor_cost,"",$customer_sale_amount,"","","",$warranty_labor_cost,"",$warranty_sale_amount,"","","",$internal_labor_cost,"",$internal_sale_amount));
	}
	if($customer_total_parts  > 0 || $warranty_total_parts  > 0 || $internal_total_parts  > 0 || $customer_total_cost  > 0 || $warranty_total_cost  > 0 || $internal_total_cost  > 0){      
		push (@total_labor_parts_section, sprintf(border(">")."%-4s%-16s%-2s%8.2f%-2s%8.2f%-2s%-6s%-3s%8.2f%-2s%8.2f%-2s%-6s%-6s%8.2f%-2s%8.2f".border("|")."\n",
                                                    "","PARTS","",$customer_total_cost,"",$customer_total_parts,"","","",$warranty_total_cost,"",$warranty_total_parts,"","","",$internal_total_cost,"",$internal_total_parts));
	}
        if($total_sublet > 0 || $total_sublet_cost > 0){
        push (@total_labor_parts_section, sprintf(border(">")."%-4s%-16s%-2s%8.2f%-2s%8.2f%-2s%-6s%-3s%8.2f%-2s%8.2f%-2s%-6s%-6s%8.2f%-2s%8.2f".border("|")."\n",
                                                    "","SUBLET","",$customer_total_sublet_cost,"",$customer_total_sublet,"","","",$warranty_total_sublet_cost,"",$warranty_total_sublet,"","","",$internal_total_sublet_cost,"",$internal_total_sublet));
        }
        if($total_gog > 0){
        push (@total_labor_parts_section, sprintf(border(">")."%-4s%-19s%7s%-2s%8.2f%-2s%-6s%-5s%6s%-2s%8.2f%-2s%-6s%-8s%6s%-2s%8.2f".border("|")."\n",
                                                    "","G.O.G.","","",$customer_total_gog,"","","","","",$warranty_total_gog,"","","","","",$internal_total_gog));
        }

       # if($total_misc_chg > 0){
       # push (@total_labor_parts_section, sprintf(border(">")."%-4s%-19s%12s%-3s%7.2f%-2s%-6s%-5s%6s%-3s%6.2f%-2s%-6s%-8s%6s%-2s%6.2f".border("|")."\n",
       #                                             "","MISC CHG","","",$customer_total_misc_chg,"","","","","",$warranty_total_misc_chg,"","","","","",$internal_total_misc_chg));
       # }
        if($total_misc_dis_sale < 0 || $total_misc_dis_cost < 0){
        push (@total_labor_parts_section, sprintf(border(">")."%-4s%-10s%-8s%8.2f%-2s%8.2f%-2s%-6s%-3s%8.2f%-2s%8.2f%-2s%-6s%-6s%8.2f%-2s%8.2f".border("|")."\n",
                                                    "","MISC DIS","",$customer_total_misc_dis_cost,"",$customer_total_misc_dis_sale,"","","",$warranty_total_misc_dis_cost,"",$warranty_total_misc_dis_sale,"","","",$internal_total_misc_dis_cost,"",$internal_total_misc_dis_sale));
        }
        push (@total_labor_parts_section, sprintf(border(">")."%-4s%-16s%-2s%8.2f%-2s%8.2f%-2s%-6s%-3s%8.2f%-2s%8.2f%-2s%-6s%-6s%8.2f%-2s%8.2f".border("|")."\n",
                                                    "","TOTAL","",$customer_total_labor_parts_cost,"",$customer_total_labor_parts_sale,"","","",$warranty_total_labor_parts_cost,"",$warranty_total_labor_parts_sale,"","","",$internal_total_labor_parts_cost,"",$internal_total_labor_parts_sale));
        push (@total_labor_parts_section, sprintf(border(">")."%-3s%-5s%-8s%-4s%-26s%-32s%-2s".border("|")."\n",
                                                    "","JOB#".$ro_line." JOURNAL PREFIX","",$accounting_make_code."C".$department,"",$accounting_make_code."W".$department,$accounting_make_code."I".$department));
        push (@total_labor_parts_section, sprintf(border(">")."%-16s".border("|")."\n", ""));
        push (@job_section, [@total_labor_parts_section]);
	 
         $total_sale_amount = $total_sale_amount +  $sale_amount;
         $total_parts_amount = $total_parts_amount + $total_parts;
       }

	#my @misc_section = make_misc_section($ro_number, $ro_line);
        #push (@job_section, [@misc_section]);

#	my @total_misc_section;
 #   if($total_misc > 0){
  #     push (@total_misc_section, sprintf(border(">")."%-75s  %-10s %11.2f".border("|")."\n", "", "TOTAL - MISC", $total_misc ));
   #    push (@job_section, [@total_misc_section]);
    #}
# GOG claim section of a JOB

    my $GOG_query = $conn->prepare("SELECT
                dlr_cost
            FROM repair_other_totals 
            WHERE all_to_amt_type = 'All' AND dlr_cost::numeric > 0 AND ro_number = ? AND types = 'GOG' AND pay_type = 'Warr'");
    my (@GOG_row) = $GOG_query->execute($ro_number);
    my ($GOG)= @GOG_row;
    # $gog_entries = $GOG_query->rows;

# MISC claim section of a JOB

    my $MISC_query = $conn->prepare("SELECT
                dlr_cost
            FROM repair_other_totals 
            WHERE all_to_amt_type = 'All' AND dlr_cost::numeric > 0 AND ro_number = ? AND types = 'MISC' AND pay_type = 'Warr'");
    my (@MISC_row) = $MISC_query->execute($ro_number);
    my ($MISC)= @MISC_row;
    # $gog_entries = $GOG_query->rows;


# sublet_labor_claim section of a JOB

    my $sublet_labor_query = $conn->prepare("SELECT
                dlr_cost
            FROM repair_other_totals 
            WHERE all_to_amt_type = 'Labor' AND dlr_cost::numeric > 0 AND ro_number = ? AND types = 'SUBLET' AND pay_type = 'Warr'");
    my (@sublet_labor_row) = $sublet_labor_query->execute($ro_number);
    my ($sublet_labor)= @sublet_labor_row;
    # my ($sublet_labor_entries) = $sublet_labor_query->rows;

# sublet_part_claim section of a JOB

    my $sublet_part_query = $conn->prepare("SELECT
                dlr_cost
            FROM repair_other_totals 
            WHERE all_to_amt_type = 'Parts' AND dlr_cost::numeric > 0 AND ro_number = ? AND types = 'SUBLET' AND pay_type = 'Warr'");
    my (@sublet_part_row) = $sublet_part_query->execute($ro_number);
    my ($sublet_part)= @sublet_part_row;
    # my ($sublet_part_entries) = $sublet_part_query->rows;

	my @total_section;
	my $total_invoice=0;
    $labor_total = $labor_total + $total_sale_amount;
    $parts_total = $parts_total + $total_parts_amount;
    $gog_total = $gog_total + $GOG;
    $sub_labor_total = $sub_labor_total + $sublet_labor;
    $sub_parts_total = $sub_parts_total + $sublet_part;
    $misc_total = $misc_total + $total_misc_chg + $total_misc_dis_sale;
    $total_invoice = $total_sale_amount + $total_parts_amount + $total_misc_chg + $total_misc_dis_sale;
    $in_total = $in_total + $total_invoice;

    ##############################CLAIM SECTION START#############################

    my ( $ro_number_claim, $claim_number_claim, $labor_sale_claim, $parts_sale_claim, $sublet_labor_claim, $sublet_parts_claim, $gog_claim, $misc_claim, $tax_claim, $total_claim );
    my ( $total_labor_sale_claim, $total_parts_sale_claim, $total_sublet_labor_claim, $total_sublet_parts_claim, $total_gog_claim, $total_misc_claim, $total_tax_claim, $grand_total_claim );

    my $claim_deatil_qry = $conn->prepare("SELECT
                                        ro_number,
                                        claim_number,
                                        labor_sale,
                                        parts_sale,
                                        sublet_labor,
                                        sublet_parts,
                                        gog,
                                        misc,
                                        tax,
                                        total
                                  FROM repair_claim_details
                                  WHERE ro_number = ?");
    $claim_deatil_qry->execute($ro_number);
    
    if($claim_deatil_qry->rows > 0){
       
        push (@total_section, sprintf(border(">").'~font{HelveticaMono3}'."%-100s"."\n", ""));  
        push (@total_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%3s %10s".border("|")."\n",
                                                    "","WARRANTY CLAIM DETAIL TOTALS---------------------------------------------------------------------")); 
        push (@total_section, sprintf(border(">")."%3s %10s".border("|")."\n",
                                                    "","CLAIM#---------- LABOR---- PARTS---- SUB.LAB---- SUB.PART---- GOG---- MISC---- TAX---- TOTAL-----")); 

        while(my @claim_deatil_row = $claim_deatil_qry->fetchrow_array) {
        	( $ro_number_claim, $claim_number_claim, $labor_sale_claim, $parts_sale_claim, $sublet_labor_claim, $sublet_parts_claim, $gog_claim, $misc_claim, $tax_claim, $total_claim ) = @claim_deatil_row;

            push (@total_section, sprintf(border("#").'~font{DejaVuSansMono9}'."%4s%-17s%-10.2f%-10.2f%-12.2f%-13.2f%-8.2f%-9.2f%-8.2f%-10.2f".'~font{default}'.border("|")
                                ."\n","",$claim_number_claim, $labor_sale_claim, $parts_sale_claim, $sublet_labor_claim, $sublet_parts_claim, $gog_claim, $misc_claim, $tax_claim, $total_claim));
       
            $total_labor_sale_claim += $labor_sale_claim;
            $total_parts_sale_claim += $parts_sale_claim;
            $total_sublet_labor_claim += $sublet_labor_claim;
            $total_sublet_parts_claim += $sublet_parts_claim;
            $total_gog_claim += $gog_claim;
            $total_misc_claim +=  $misc_claim;
            $total_tax_claim += $tax_claim;
            $grand_total_claim +=  $total_claim;
        }
        push (@total_section, sprintf(border(">")."%3s %10s".border("|")."\n",
                                                   "","---------------- --------- --------- ----------- ------------ ------- -------- ------- ---------"));
        push (@total_section, sprintf(border(">").'~font{DejaVuSansMono9}'."%3s %-16s %-10.2f%-10.2f%-12.2f%-13.2f%-8.2f%-9.2f%-8.2f%-10.2f".border("|")."\n",
                                                    "","CLAIM TOTALS",$total_labor_sale_claim, $total_parts_sale_claim, $total_sublet_labor_claim, $total_sublet_parts_claim, $total_gog_claim, $total_misc_claim, $total_tax_claim, $grand_total_claim));                                              
    }

    push (@job_section, [@total_section]);

    ##############################CLAIM SECTION END#############################

	my @all_misc_section = make_all_misc_section($ro_number);
        push (@job_section, [@all_misc_section]);
        my @total_chg_misc_section;
    	if($total_misc_chg > 0 ){
       		push (@total_chg_misc_section, sprintf(border(">")."%-75s  %-10s %11.2f".border("|")."\n", "", "TOTAL - MISC", $total_misc_chg   ));
       		push (@job_section, [@total_chg_misc_section]);
    	}    
        my @comments_section = make_comments_section($ro_number);


        push (@comments_section, sprintf(border("#").'~font{DejaVuSansMono9}'."%4s%-100s".'~font{default}'.border("|")."\n","","GRAND TOTALS"."-"x84));
        

        push (@comments_section, sprintf(border(">")."%2s %10s".border("|")."\n",
                                                    "","--------------CUSTOMER------------------------WARRANTY---------------------------INTERNAL---------"));
        push (@comments_section, sprintf(border(">")."%-15s %-5s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %-6s %-2s %6s %2s %6s".border("|")."\n",
                                                    "","GL","","COST","","SALE","","GL","","COST","","SALE","","GL","","COST","","SALE"));
        push (@comments_section, sprintf(border(">")."%-4s%-15s%-2s%8.2f%3s%8.2f%2s%6s%3s%8.2f%2s%8.2f%2s%6s%6s%8.2f%2s%8.2f".border("|")."\n",
                                                    "","LABOR","",$customer_labor_cost_grand_total,"",$customer_labor_sale_grand_total,"","","",$warranty_labor_cost_grand_total,"",$warranty_labor_sale_grand_total,"","","",$internal_labor_cost_grand_total,"",$internal_labor_sale_grand_total));
        push (@comments_section, sprintf(border(">")."%-4s%-15s%-2s%8.2f%3s%8.2f%2s%6s%3s%8.2f%2s%8.2f%2s%6s%6s%8.2f%2s%8.2f".border("|")."\n",
                                                    "","PARTS","",$customer_parts_cost_grand_total,"",$customer_parts_sale_grand_total,"","","",$warranty_parts_cost_grand_total,"",$warranty_parts_sale_grand_total,"","","",$internal_parts_cost_grand_total,"",$internal_parts_sale_grand_total));    
    	if($customer_grandtotal_sublet > 0 || $warranty_grandtotal_sublet > 0 || $internal_grandtotal_sublet > 0 || $customer_grandtotal_sublet_cost > 0 || $warranty_grandtotal_sublet_cost > 0 || $internal_grandtotal_sublet_cost > 0){
        	push (@comments_section, sprintf(border(">")."%-4s%-8s%-9s%8.2f%3s%8.2f%2s%6s%3s%8.2f%2s%8.2f%2s%6s%6s%8.2f%2s%8.2f".border("|")."\n",
                                                    "","SUBLET","",$customer_grandtotal_sublet_cost,"",$customer_grandtotal_sublet,"","","",$warranty_grandtotal_sublet_cost,"",$warranty_grandtotal_sublet,"","","",$internal_grandtotal_sublet_cost,"",$internal_grandtotal_sublet));    
        }

    	if($total_gog > 0){
        	push (@comments_section, sprintf(border(">")."%-4s%-18s%7s%3s%8.2f%2s%6s%6s%6s%2s%8.2f%2s%6s%8s%6s%2s%8.2f".border("|")."\n",
                                                    "","G.O.G.","","",$total_gog,"","","","","","0.00","","","","","","0.00"));    
        }

        if($customer_total_misc_chg_sale > 0 || $warranty_total_misc_chg_sale > 0 || $internal_total_misc_chg_sale > 0){
        	push (@comments_section, sprintf(border(">")."%-4s%-18s%7s%3s%8.2f%2s%6s%6s%6s%1s%8.2f%2s%6s%8s%6s%2s%8.2f".border("|")."\n",
                                                    "","MISC CHG","","",$customer_total_misc_chg_sale,"","","","","",substr($warranty_total_misc_chg_sale,0,4),"","","","","",$internal_total_misc_chg_sale));
        }
        if($customer_grandtotal_misc_dis < 0 || $warranty_grandtotal_misc_dis < 0 || $internal_grandtotal_misc_dis < 0){
        	push (@comments_section, sprintf(border(">")."%-4s%-18s%7s%3s%8.2f%2s%6s%6s%6s%2s%8.2f%2s%6s%8s%6s%2s%8.2f".border("|")."\n",
                                                    "","MISC DIS","","",$customer_grandtotal_misc_dis,"","","","","",substr($warranty_grandtotal_misc_dis,0,4),"","","","","",$internal_grandtotal_misc_dis));
        }

		push (@comments_section, sprintf(border(">")."%-4s%-22s%-7s%7.2f%-2s%-6s%-1s%-6s%-8s%6.2f%-2s%-6s%-4s%-6s%-10s%2.2f".border("|")."\n",
                                                    "","TAXES","    ",$tax_customer,"","","","","",$tax_warranty,"","","","","",$tax_internal));

        push (@job_section, [@comments_section]);

		
      #  my @tax_section = make_tax_section($ro_number);
      #  push (@job_section, [@tax_section]);
}

# Subroutine to prepare the GRAND TOTAL section of an RO
sub make_grand_total_section {
    
        my $customer_cost_total=0;
        my $customer_sale_total=0;
	my $warranty_cost_total=0;
        my $warranty_sale_total=0;
        my $internal_cost_total=0;
        my $internal_sale_total=0;

        $customer_cost_total = $customer_labor_cost_grand_total + $customer_parts_cost_grand_total + $customer_total_misc_chg_cost + $customer_total_misc_dis_cost + $customer_total_gog + $customer_grandtotal_sublet_cost;
	$warranty_cost_total = $warranty_labor_cost_grand_total + $warranty_parts_cost_grand_total + $warranty_total_misc_chg_cost+ $warranty_total_misc_dis_cost + $warranty_total_gog + $warranty_grandtotal_sublet_cost;
	$internal_cost_total = $internal_labor_cost_grand_total + $internal_parts_cost_grand_total + $internal_total_gog + $internal_grandtotal_sublet_cost + $internal_total_misc_chg_cost;
    $customer_sale_total = $customer_labor_sale_grand_total + $customer_parts_sale_grand_total + $customer_total_misc_chg_sale + $customer_grandtotal_misc_dis + $customer_total_gog + $customer_grandtotal_sublet + $tax_customer;
	$warranty_sale_total = $warranty_labor_sale_grand_total + $warranty_parts_sale_grand_total + $warranty_total_misc_chg_sale + $warranty_grandtotal_misc_dis + $warranty_total_gog + $warranty_grandtotal_sublet + $tax_warranty;
	$internal_sale_total = $internal_labor_sale_grand_total + $internal_parts_sale_grand_total + $internal_total_misc_chg_sale + $internal_grandtotal_misc_dis + $internal_total_gog + $internal_grandtotal_sublet + $tax_internal;


        @grand_total_section='';
        # %-4s%-4s%-4s%-18s%6.2f%-4s%-6s%-2s%-6s%-8s%2.2f%-6s%-2s%-6s%-4s%-9s%5.2f
        push (@grand_total_section, sprintf(border(">")."%-22s %-8s %-8s %-10s %-8s %-8s %-10s %-8s %-8s".border("|")."\n",
                                                    "", "---------", "---------", "", "---------",  "---------",  "",  "---------", "---------"));
        push (@grand_total_section, sprintf(border(">")."%-4s%-15s%-2s%8.2f%3s%8.2f%2s%6s%3s%8.2f%2s%8.2f%2s%6s%6s%8.2f%2s%8.2f".border("|")."\n",
                                                    "","TOTAL","",$customer_cost_total,"",$customer_sale_total,"","","",$warranty_cost_total,"",$warranty_sale_total,"","","",$internal_cost_total,"",$internal_sale_total));

        push (@grand_total_section, sprintf(border(">")."%-16s".border("|")."\n", ""));
        
        push (@grand_total_section, sprintf(border(">")."%-16s".border("|")."\n", ""));

        push (@grand_total_section, sprintf(border(">")."%-2s%-60s".border("|")."\n",
                                                    "","******************************* A L L D E T A I L I N V O I C E ********************************")); 
}
# Subroutine to prepare the PARTS section of a JOB
sub make_parts_section {
    my ($ro_line, $ro_job_line) = @_;
    my @parts_section_array;
    $total_parts=0;
    $total_cost=0;

    my $parts_qry = $conn->prepare("SELECT DISTINCT ON (ro_part_line)
                                        ro_part_line,
                                        part_number,
                                        part_description,
                                        left(nullif(prt_billing_type, 'NA'), 1)          AS prt_billing_code,
                                        case when prt_billing_type = 'Intr' then 'INTL' 
						else UPPER(nullif(prt_billing_type, 'NA')) 
					end AS prt_billing_type,
                                        quantity_sold,
                                        unit_cost,
                                        unit_sale,
                                        unit_core_cost,
                                        unit_core_sale,
                                        COUNT(*) OVER (Partition BY rp.ro_number, rp.ro_line,
                                                 rp.ro_job_line, rp.ro_part_line) AS prt_count
                                    FROM repair_part rp
                                    WHERE ro_number = ? AND ro_line = ? AND ro_job_line = ? AND quantity_sold != 0
                                    ORDER BY ro_part_line");
    $parts_qry->execute($ro_number, $ro_line, $ro_job_line);
    

    $parts_entries = $parts_qry->rows;
    if($parts_entries > 0){
    push (@parts_section_array, sprintf(border(">")."%2s %10s".border("|")."\n",
                                                    "", " PARTS---------QTY----FP-NUMBER----------DESCRIPTION------------U/COST---E/COST---U/PRICE"));
        while (my @parts_row = $parts_qry->fetchrow_array) {
            ( $ro_part_line, $part_number, $part_description, $prt_billing_code, $prt_billing_type, $quantity_sold,
              $unit_cost, $unit_sale, $unit_core_cost, $unit_core_sale, $prt_count) = @parts_row;
            $Text::Wrap::columns = 24;
            my $wrapped_part_description = fill('', '', expand($part_description));
            my @part_description_list = split "\n", $wrapped_part_description;
            push (@parts_section_array, sprintf(border(">")."%-3s%10s%7s%-6s%-18s%-19s%9.2f%10.2f%-3s%5.2f%12.2f".border("|")."\n",
                                                    "",$prt_billing_type,$quantity_sold,"",$part_number,$part_description_list[0],$unit_cost,($unit_cost*$quantity_sold),"",$unit_sale,($unit_sale*$quantity_sold)));
                foreach my $i (1 .. $#part_description_list) {
                   push (@parts_section_array, sprintf(border(">")."%6s%-34s%-48s".border("|")."\n", "", $part_description_list[$i], ""));
                }  
             $total_parts = $total_parts +  ($unit_sale*$quantity_sold);
             $total_cost = $total_cost +  ($unit_cost*$quantity_sold);
            }
    }
    return @parts_section_array;
}

# Subroutine to prepare the GOG section of a JOB
sub make_gog_section {
    my ($ro_number, $ro_line) = @_;
    my @gog_section_array;
    $total_gog = 0;
    my ($type) = "GOG";
    my $gog_qry = $conn->prepare("SELECT
                distinct on(ro_other_total_id)
                UPPER(pay_type),
                UPPER(all_to_amt_type),
                txbl_amt
            FROM repair_other_totals             
                JOIN repair_other roth USING (ro_number)
                JOIN repair_order ro USING (ro_number)
            WHERE all_to_amt_type != 'All' AND txbl_amt::numeric > 0 AND ro_number = ? AND ro_line = ? AND types = ?");
    $gog_qry->execute($ro_number, $ro_line, $type);
    my ($pay_type, $all_to_amt_type, $dlr_cost);
    $gog_entries = $gog_qry->rows;
    if($gog_entries > 0){
    push (@gog_section_array, sprintf(border(">")."%2s %10s".border("|")."\n","", "G.O.G. & SUPPLIES-------------------------------------------------------------------------"));
    while (my @gog_row = $gog_qry->fetchrow_array) {
    ($pay_type, $all_to_amt_type, $dlr_cost)= @gog_row;
    $total_gog = $total_gog + $dlr_cost;
    push (@gog_section_array, sprintf(border(">")."%4s%-14s%-70s%14.2f".border("|")."\n",
                                           "",$pay_type, $all_to_amt_type, $dlr_cost));
    }
    }
    return @gog_section_array;
    }

# Subroutine to prepare the SUBLET section of a JOB
sub make_sublet_section {
    my ($ro_number,$ro_line) = @_;
    my @sublet_section_array;
    $total_sublet = 0;
    $total_sublet_cost = 0;
    my ($type) = "SUBLET";
    my $sublet_qry = $conn->prepare("SELECT
                UPPER(pay_type),
                ntxble_amt,
                roth.other_code,
                roth.other_description,
                to_char(ro.creation_date, 'mm/dd/yy') AS ro_date,
                dlr_cost
            FROM repair_other_totals 
            JOIN repair_other roth USING (ro_number)
            JOIN repair_order ro USING (ro_number) 
            JOIN repair_job USING(ro_number, ro_line)
            WHERE all_to_amt_type != 'All' AND ntxble_amt::numeric > 0 AND ro_number = ? 
               AND ro_line=? AND types = ? AND item_type=? AND billing_code = pay_type");
    $sublet_qry->execute($ro_number, $ro_line, $type, $type);
    my ($pay_type, $dlr_cost, $other_code, $other_description, $ro_date, $sublet_cost);
    $sublet_entries = $sublet_qry->rows;
    if($sublet_entries > 0){
    push (@sublet_section_array, sprintf(border(">")."%2s %10s".border("|")."\n","", "SUBLET-----PO#--------VEND INV#-INV.DATE-DESCRIPTION---------------------------------------"));
    while (my @sublet_row = $sublet_qry->fetchrow_array) {
    ($pay_type, $dlr_cost, $other_code, $other_description, $ro_date, $sublet_cost)= @sublet_row;
    $total_sublet = $total_sublet + $dlr_cost;
    $total_sublet_cost = $total_sublet_cost + $sublet_cost;
    push (@sublet_section_array, sprintf(border(">")."%4s%-10s%-21s%-53s%14.2f".border("|")."\n",
                                            "",$pay_type, $other_code, $ro_date." ".$other_description, $dlr_cost));
    }
    }
    return @sublet_section_array;
}


# Subroutine to prepare the MISC section of a JOB
sub make_misc_section {
    my ($ro_number, $ro_line) = @_;
    my @misc_section_array;
    $total_misc_dis_sale=0;
    $total_misc_dis_cost=0;
	
    my $misc_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       case when other_billing_type = 'Intr' then 'INTL' else upper(other_billing_type) end as other_billing_type,
                                       other_cost,
                                       other_sale
                                  FROM repair_other
                                 WHERE other_sale::numeric < 0 AND ro_number = ? AND ro_line= ? AND item_type = 'MISC'");
    $misc_qry->execute($ro_number, $ro_line);
    my ( $other_code, $other_description,$other_billing_type, $other_cost, $other_sale);

    $misc_entries = $misc_qry->rows;
    if($misc_entries > 0){
    push (@misc_section_array, sprintf(border(">")."%2s %10s".border("|")."\n",
                                                    "", " MISC---------CODE------DESCRIPTION--------------------CONTROL NO-----------------------------------"));
        while (my @misc_row = $misc_qry->fetchrow_array) {
    (  $other_code, $other_description,$other_billing_type, $other_cost, $other_sale) = @misc_row;
            $Text::Wrap::columns = 24;
            my $wrapped_misc_description = fill('', '', expand($other_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;
            push (@misc_section_array, sprintf(border(">")."%-4s%-14s%-9s%-60s%14.2f".border("|")."\n",
                                                    "",$other_billing_type,$other_code,$misc_description_list[0],$other_sale));
                foreach my $i (1 .. $#misc_description_list) {
                   push (@misc_section_array, sprintf(border(">")."%-27s%-34s%-30s".border("|")."\n", "", $misc_description_list[$i], ""));
                } 
              if($other_sale < 0){
					$total_misc_dis_sale = $total_misc_dis_sale +  $other_sale;
					$total_misc_dis_cost = $total_misc_dis_cost +  $other_cost;
              }

            }
    }
    return @misc_section_array;
}

# Subroutine to prepare the MISC section of all JOB
sub make_all_misc_section {
    my ($ro_number) = @_;
    my @chg_misc_section_array;
    $total_misc_chg=0;  
    my $chg_misc_qry = $conn->prepare("SELECT
                                       other_code,
                                       other_description,
                                       other_billing_type,
                                       other_cost,
                                       other_sale,
                                       ro_line
                                  FROM repair_other
                                 WHERE other_sale::numeric > 0 AND ro_number = ? AND item_type = 'MISC'
                                 ORDER BY ro_line");
    $chg_misc_qry->execute($ro_number);
    my ( $chg_other_code, $chg_other_description,$chg_other_billing_type, $chg_other_cost, $chg_other_sale, $chg_ro_line) = (0)x6;

    $chg_misc_entries = $chg_misc_qry->rows;
    if($chg_misc_entries > 0){
    push (@chg_misc_section_array, sprintf(border(">").'~font{HelveticaMono3}'."%-100s".'~font{default}'.border("|")."\n", ""));
    push (@chg_misc_section_array, sprintf(border(">")."%2s %10s".border("|")."\n",
                                                    "", " MISC---------CODE------DESCRIPTION--------------------CONTROL NO-----------------------------------"));
    my @alph_array = ("A".."Z");
        while (my @chg_misc_row = $chg_misc_qry->fetchrow_array) {
    ( $chg_other_code, $chg_other_description,$chg_other_billing_type, $chg_other_cost, $chg_other_sale, $chg_ro_line) = @chg_misc_row;
            my $ro_char = $alph_array[$chg_ro_line-1] ;
            $Text::Wrap::columns = 24;
            my $wrapped_misc_description = fill('', '', expand($chg_other_description));
            my @misc_description_list = split "\n", $wrapped_misc_description;
            push (@chg_misc_section_array, sprintf(border(">")."%-4s%-14s%-9s%-60s%14.2f".border("|")."\n",
                                                    "","JOB # ".$ro_char,$chg_other_code,$misc_description_list[0],$chg_other_sale));
                foreach my $i (1 .. $#misc_description_list) {
                   push (@chg_misc_section_array, sprintf(border(">")."%-27s%-34s%-30s".border("|")."\n", "", $misc_description_list[$i], ""));
                }  
              if ($chg_other_sale > 0){
             	$total_misc_chg = $total_misc_chg +  $chg_other_sale;
              }
              
            }
    }
    
  my $chg_misc_total_qry = $conn->prepare("SELECT
                                       sum(other_sale) as other_sale,
                                       left(other_billing_type,1) as other_billing_type,
                                       sum(other_cost) as other_cost
                                  FROM repair_other
                                 WHERE other_sale::numeric > 0 AND ro_number = ? AND item_type = 'MISC'
                                 GROUP BY ro_number, other_billing_type");
    $chg_misc_total_qry->execute($ro_number);
     
my ( $chg_misc_total_sale,$chg_misc_type,$chg_misc_total_cost);
    $chg_misc_total_entries = $chg_misc_qry->rows;
    if($chg_misc_total_entries > 0){
         while (my @chg_misc_total_row = $chg_misc_total_qry->fetchrow_array) {
    ( $chg_misc_total_sale,$chg_misc_type,$chg_misc_total_cost) = @chg_misc_total_row;
        if($chg_misc_type eq "C"){
		$customer_total_misc_chg_sale = $chg_misc_total_sale;
                $customer_total_misc_chg_cost = $chg_misc_total_cost;
        }
        elsif($chg_misc_type eq "I"){
		$internal_total_misc_chg_sale = $chg_misc_total_sale;
                $internal_total_misc_chg_cost = $chg_misc_total_cost;
        }
        elsif($chg_misc_type eq "W"){
		$warranty_total_misc_chg_sale = $chg_misc_total_sale;
                $warranty_total_misc_chg_cost = $chg_misc_total_cost;
        }
      }
     }
    return @chg_misc_section_array;
}

# Subroutine to prepare the TAX section of a JOB
sub make_tax_section {
    my ($ro_number) = @_;
    my @tax_section_array; 
      
    my $tax_qry = $conn->prepare("SELECT
                                       left(other_billing_type, 1) AS other_billing_type,
                                       other_cost,
                                       other_sale,
									   ro_line
                                   FROM repair_other
                                   WHERE ro_number = ? AND item_type = 'TAX'");
    $tax_qry->execute($ro_number);
    my (  $other_billing_type,$other_cost, $other_sale,$ro_line);
    $tax_entries = $tax_qry->rows;
    if($tax_entries > 0){
        while (my @tax_row = $tax_qry->fetchrow_array) {
            my ( $other_billing_type,$other_cost, $other_sale,$ro_line) = @tax_row;
            $Text::Wrap::columns = 24;
            if ($other_billing_type eq   "C"){
                    $customer_total_tax=$customer_total_tax+$other_sale;

            }
            elsif($other_billing_type eq   "W"){
                     $warranty_total_tax=$warranty_total_tax+$other_sale;


            }
            elsif($other_billing_type eq   "I"){
                      $internal_total_tax=$internal_total_tax+$other_sale;

            }
			
 
                           }
    }
   if($other_sale > 0) {
   	push (@tax_section_array, sprintf(border(">")."%-4s%-18s%-7s%7.2f%-2s%-6s%-1s%-6s%-9s%6.2f%-2s%-6s%-4s%-6s%-10s%2.2f".border("|")."\n",
                                                    "","TOTAL TAX","",$customer_total_tax,"","","","","",$warranty_total_tax,"","","","","",$internal_total_tax));
   }
    return @tax_section_array;
}

# Subroutine to prepare the Comments section of a JOB
sub make_comments_section {
    my ($ro_number) = @_;
    my @comments_section_array; 
    my $comments_qry = $conn->prepare("SELECT
                                       comments
                                   FROM repair_order
                                   WHERE ro_number = ? ");
    $comments_qry->execute($ro_number);
    my ( $comments);

    $comments_entries = $comments_qry->rows;
    if($comments_entries > 0){
        while (my @comments_row = $comments_qry->fetchrow_array) {
           ( $comments ) = @comments_row;
        if($comments ne ''){
            $Text::Wrap::columns = 34;
            my $wrapped_comments_description = fill('', '', expand($comments));
            my @comments_description_list = split "\n", $wrapped_comments_description;
            push (@comments_section_array, sprintf(border(">").'~font{HelveticaMono3}'."%-100s"."\n", "")); 
            push (@comments_section_array, sprintf(border(">").'~font{DejaVuSansMono9}'."%3s %-80s".border("|")."\n",
                                                    "","COMMENTS--------------------------------------------------------------------------------------"));
            push (@comments_section_array, sprintf(border(">").'~font{DejaVuSansMono9}'."%6s%-34s%-48s".border("|")."\n", "", $comments_description_list[0], ""));
                foreach my $i (1 .. $#comments_description_list) {
                   push (@comments_section_array, sprintf(border(">").'~font{DejaVuSansMono9}'."%6s%-34s%-48s".border("|")."\n", "", $comments_description_list[$i], ""));
                } 
           # push (@comments_section_array, sprintf(border(">").'~font{HelveticaMono3}'."%-100s"."\n", ""));  
           }
       }
    }
    my $recommendation_qry = $conn->prepare("select 
						recommendations as TechRecommend
					     from repair_order
                                   	     WHERE ro_number = ?");
    $recommendation_qry->execute($ro_number);
    my ( $recommendation_list);

    $recommendation_entries = $recommendation_qry->rows;
    if($recommendation_entries > 0){
        while (my @recommendation_row = $recommendation_qry->fetchrow_array) {
           ( $recommendation_list ) = @recommendation_row;
         if($recommendation_list ne ''){
            $Text::Wrap::columns = 62;
            push (@comments_section_array, sprintf(border(">").'~font{DejaVuSansMono9}'."%3s %-80s".border("|")."\n",
                                                    "","RECOMMENDATIONS--------------------------------------------------------------------------------------"));
            my @spl = split('::', $recommendation_list);
            foreach my $recommendation (@spl) 
	    {
                my $wrapped_recommendation_description = fill('', '', expand($recommendation));
                my @recommendation_description_list = split "\n", $wrapped_recommendation_description;
                
                push (@comments_section_array, sprintf(border(">").'~font{DejaVuSansMono9}'."%3s %-80s".border("|")."\n",
                                                    "",$recommendation_description_list[0]));
                foreach my $i (1 .. $#recommendation_description_list) {
                   push (@comments_section_array, sprintf(border(">").'~font{DejaVuSansMono9}'."%6s%-34s%-48s".border("|")."\n", "", $recommendation_description_list[$i], ""));
                } 
                
             }
             push (@comments_section_array, sprintf(border(">").'~font{HelveticaMono3}'."%-100s"."\n", ""));  
           }
      }
    }

    return @comments_section_array;
}

# Subroutine to calculate the total no of pages in the invoice
# Update page count calculation section, if any modification is made in the body of the report
sub calculate_page_count {
    my $page_count;
    #my $page_height = 0;
    my $tot_page_lines = 0;
    # Add page count calculation of job section
    foreach my $job_sub_sections (@job_section){
        $tot_page_lines = $tot_page_lines + scalar(@$job_sub_sections);
    }

    # Add page count calculation of other sections
    $tot_page_lines = $tot_page_lines + scalar(@grand_total_section);
    $tot_page_lines = $tot_page_lines + scalar(@end_of_invoice_section);

    $page_count = ceil($tot_page_lines / $page_max_height);

    return $page_count;
}


# Print Open/Void RO
sub print_open_ro_series {
    # $num_args = scalar(@_);
    for( $a = $_[0]; $a <= $_[1]; $a = $a + 1 ) {
        printf(FILE border("#")." %-100s ".border("|")."\n", "Invoice Source Not Present: RO# ".$a." * See Open and Void RO Report *");
    }
}

# Subroutine to print blank lines
sub print_blank_line {
    my ($blank_lines) = @_;
    for (my $i = 0; $i < $blank_lines; $i++) {
        printf(FILE border("#")."%95s".border("|")."\n", "");
    }
}

# Subroutine to replace the Page number placeholder in header and to print header into FILE
 sub print_header_section {
    my ($page_num) = @_;
    my %hash = (pg_num => $page_num );
    foreach (@header_section)
    {
        my $header_line = $_;
        $header_line =~ s/<--(.*?)-->/$hash{"\L$1"}/g;
        print FILE $header_line;
    }
 }
sub print_dealer_header_section {
    print_blank_line($invoice_dealer_header);
 }

# Subroutine to prepare end of invoice section
sub make_end_of_invoice_section {
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %100s ".border("|")."\n", "")); # Separator Line
    push (@end_of_invoice_section, sprintf(border("#")." %4s%-31s%s%32s ".border("|")."\n",
           "",
           "*"x26,
           "A L L  D E T A I L  I N V O I C E",
           "*"x27));
}

sub paginate_and_print_segment {
    my (@section_array) = @_;
    # Print section that fit in the current page
    if(scalar(@section_array) <= ($page_max_height - $curr_page_height)){
        print FILE @section_array;
        $curr_page_height = $curr_page_height + scalar(@section_array);
    }
    #  Print section with size longer than the space allocated ($page_max_height) to a single page
    else{
        for my $sub_section(@section_array){
            if($curr_page_height < $page_max_height)
            {
                print FILE $sub_section;
                $curr_page_height = $curr_page_height + 1;
            } else{
                print FILE get_footer(0, $curr_page_num);
                $curr_page_num = $curr_page_num + 1;
		print_dealer_header_section();
                print_header_section ($curr_page_num);
                print FILE $sub_section;
                $curr_page_height = 1;
            }
        }
    }
}

#Subroutine to prepare the FOOTER section of the invoice

sub get_footer {
    my @footer_section;
    my ($is_end, $page_number) = @_;
    push (@footer_section, sprintf(border("#")." %100s ".border("|")."\n", ""));
    if($is_end) {
        # no newline after the form-feed since a last newline is coming from somewhere and
        # causing the PDF document to paginate a blank page at the end.
        push (@footer_section, sprintf(border("#").'~font{DejaVuSansMono9}'.'~color{0 0 0}'."%-3s %-25s %30s%40s%2s ".border("|")."\f",
               "","PAGE $curr_page_num OF $pages",
               "ACCOUNTING COPY",
               "[  END  OF  INVOICE  ]",
               ""));
    } else {
        push (@footer_section, sprintf(border("#").'~font{DejaVuSansMono9}'.'~color{0 0 0}'."%-3s %-25s %30s%40s%2s ".border("|")."\f",
               "","PAGE $curr_page_num OF $pages",
               "ACCOUNTING COPY",
               "[  CONTINUED ON NEXT PAGE  ]",
               ""));
    }
    return @footer_section;
}

# Subroutine to prepare proxy invoice report for an RO
sub print_ro {
   ($cust_lbr_cost, $cust_lbr_sale, $cust_prt_cost, $cust_prt_sale, $cust_misc_cost, $cust_misc_sale, $cust_gog_cost, $cust_gog_sale,
    $cust_sublet_cost, $cust_sublet_sale, $cust_ded_cost, $cust_ded_sale )= (0)x12;

    ($intern_lbr_cost, $intern_lbr_sale, $intern_prt_cost, $intern_prt_sale, $intern_misc_cost, $intern_misc_sale, $intern_gog_cost,
    $intern_gog_sale, $intern_sublet_cost, $intern_sublet_sale, $intern_ded_cost, $intern_ded_sale )= (0)x12;

    ($warr_lbr_cost, $warr_lbr_sale, $warr_prt_cost, $warr_prt_sale, $warr_misc_cost, $warr_misc_sale, $warr_gog_cost, $warr_gog_sale,
    $warr_sublet_cost, $warr_sublet_sale, $warr_ded_cost, $warr_ded_sale )= (0)x12;

    ($sale_amount, $parts_cost, $parts_sale, $misc_job_cost, $misc_job_sale, $gog_job_cost, $gog_job_sale, $sublet_job_cost,
    $sublet_job_sale, $ded_job_cost, $ded_job_sale) = (0)x11;

    $job_hours = 0;

    ($cust_ro_tax, $warr_ro_tax, $intern_ro_tax) = (0)x3;
   # $curr_page_height = 0;
    $curr_page_num = 1;
    undef @header_section;
    undef @job_section;
    ########## Prepare invoice ##########
    make_header();
    $extra_tech_line = 0;
    make_job_section();
    make_grand_total_section();
    $pages = calculate_page_count();
    ########## Print invoice ##########
    print_dealer_header_section();
    $curr_page_height = 0;
    print_header_section ($curr_page_num);

    # Pagination of Job section
    for my $js(@job_section){
        paginate_and_print_segment(@$js);
    }
    
    # Pagination of Grand total section
    paginate_and_print_segment(@grand_total_section);

    if($curr_page_height == $page_max_height){
        print FILE get_footer(1, $curr_page_num);
    } else {
        print_blank_line($page_max_height - $curr_page_height);
        print FILE get_footer(1, $curr_page_num);
    }

   }

# Subroutine to return the border character based on debug mode
sub border {
    my ($border_char) = @_;
    if($debug_mode){
        return $border_char;
    } else {
        return " ";
    }
}


sub print_ro_inv_bg_head {
    printf(FILE $cust_inv_head);
}


sub currency_format{
#use Number::Format qw(:subs);
my $precision=2;
my $symbol='';
my ($number) = @_;
my $formatted =format_price($number, $precision, $symbol);
return '$'.$formatted;
}
