#!/usr/bin/env bash

# Display the detail for an RO in production

if [[ "$#" = "0" ]]; then
    echo "Requires Key and RO#"
fi

SCNKEY="${1:?ScenarioKey Required}"
RONUM="${2:?RO Required}"

function psql_rds() {
    psql "service=${GGS_PROD_PG_SERVICE}" \
         -v scnkey="$SCNKEY" \
         -v ronum="$RONUM" \
         "$@"
}

psql_rds <<SQL
   SELECT  invoicenumber AS ronum,
           lineindex AS "L#",
           jobid AS "J#",
           partnumber,
           partdescription,
           (SELECT max(scenariopartindex) FROM magepartdetail WHERE scenariokey = :'scnkey') AS maxid,
           vehicle_make,
           opendate,
           closedate,
           paytype,
           quantity,
           basesale,
           basecost,
           get_markup(basesale, basecost) AS basemarkup,
           finalexcluded AS fe,
           CASE WHEN hasoverride THEN '*' ELSE '' END || finalreasoncode AS finalreason
      FROM magepartdetail
      JOIN magepartdetailextended USING (partdetailid, scenariokey, partid)
-- LEFT JOIN (SELECT scenariokey, invoicenumber, mageinvoicesequencemaster USING (scenariokey, invoicenumber)
     WHERE scenariokey = :'scnkey'
           AND invoicenumber = :'ronum'
     ORDER BY lineindex ASC;

\x

  SELECT scenariokey,
         dms_id,
         statecode
    FROM magescenario
    JOIN store USING (s_id)
    JOIN storegroup USING (sg_id)
   WHERE scenariokey = :'scnkey';

SQL

exit
