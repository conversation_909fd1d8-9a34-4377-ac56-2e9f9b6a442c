#!/usr/bin/env bash

# This shared interface provides access to both the production runtime template
# and the more fine-grained (and chatty) development template.  The later requires
# specific options to be specified (namely, --dms) which switches the execution path.
# Within development one can choose to simply parse the raw data and stop OR to
# generate a mock configuration and perform the full execution process.
# This mode is enabled by providing a --brand
# Development mode non-mock full execution is enabled by the --execute option
# This file and it support scripts comprise the development template runner while
# the production runner is a separate top-level command in its own right

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

source "$DU_ETL_HOME"/src/etl-dms/transform-functions.bash

PROD_TEMPLATE_COMMAND="$DU_ETL_HOME"/etl-transform

SCRIPT_WORK_DIR="$HOME"/tmp/etl-dms-workdir
MOCK_CFG="$SCRIPT_WORK_DIR"/mock-config.bash

EXECUTION_MODE='prod'
DMS_TYPE='Prompt'
DEVELOPMENT_MODE=
MOCK_BRAND=
MOCK_STATE='NA'
ETI_DIR=
DIST_ONLY='false'
DEV_ARGS=(--)

AUDIT_WORK="/etl/audit-work"
AUDIT_FAIL="/etl/audit-failed"
BASE_DIR=$HOME/tmp
FILE_DIR="${BASE_DIR}/run-du-etl-tw"
LOAD_FOLDER="${BASE_DIR}"/audit-load
LOAD_FILE_FOLDER="${LOAD_FOLDER}"/run-du-etl-load-tli

mkdir -p ${AUDIT_WORK}
mkdir -p ${AUDIT_FAIL}
mkdir -p ${LOAD_FOLDER}
mkdir -p ${LOAD_FILE_FOLDER}

function main() {
    # parse_options "$@"
    # if [[ "$DMS_TYPE" = 'Prompt' ]]; then
    echo "Audit-Import"
    clear
    reset_script_work_dir
    execut_audit_import
        # execute_production_template
    # else
    #     determine_development_mode
    #     configue_for_dev_mode
    #     configure_eti_env
    #     ensure_eti_dir_exists
    #     clear
    #     execute_development_mode
    # fi
}

function cleanup() {
    rm -rf "${AUDIT_WORK}"/*
    rm -rf "${LOAD_FILE_FOLDER}"/*
    import_file=$(cat "$FILE_DIR/meta/source-files.txt")
    mv $import_file "${AUDIT_FAIL}"
}

function clean_and_die(){
cleanup
die $1
}

function parse_options() {
    OPT_ENV=$(getopt --options d:b:es: --long dms:,brand:,execute,state:,debug,distribute-only -n 'etl-dms.bash' -- "$@")
    if [[ ! $? = '0' ]]; then
        clean_and_die "Option Parsing Failed"
    fi

    eval set -- "$OPT_ENV"
    while true; do
        case "$1" in
            -d|--dms)
                DMS_TYPE="$2"
                shift 2
                ;;
            -e|--execute)
                DEVELOPMENT_MODE="execute"
                shift 1
                ;;
            --distribute-only)
                DIST_ONLY='true'
                DEV_ARGS=("--distribute-only" "${DEV_ARGS[@]}")
                shift 1
                ;;
            -b|--brand)
                MOCK_BRAND="$2"
                shift 2
                ;;
            -s|--state)
                MOCK_STATE="$2"
                shift 2
                ;;
            --debug)
                enable_debug_mode
                shift 1
                ;;
            --) shift ; break ;;
            *) clean_and_die "Unrecognized Argument $1"
        esac
    done
}

function execute_production_template() {
    echo "Production"
    "$PROD_TEMPLATE_COMMAND" || clean_and_die "Production Template Exection Failed"
    return
}
function execut_audit_import(){
    "$DU_ETL_HOME"/main/run-audit-import.bash || clean_and_die "Audit Import Failed!"
    return
}
function execute_development_mode() {
    if [[ "$DEVELOPMENT_MODE" = 'execute' ]]; then
        # Do NOT reset_script_work_dir here since the end
        # of a development load can be immediately followed
        # by a --distribute-only call that actually pushes
        # the data and files to production; a normal
        # end-of-script execution targets the local database
        config_file=$(determine_config_file)
        "$DU_ETL_HOME"/DU-Solve360/present-config-to-user "$config_file"
        prompt_continuation "Proceed with importing the above data?"
        "$DU_ETL_HOME"/main/run-audit-import.bash --use-config "$config_file" \
                                 "${DEV_ARGS[@]}" \
                                 || clean_and_die "Main Template Run Failed!"
        cleanup_after_dev_run "$config_file"
    elif [[ "$DEVELOPMENT_MODE" = 'test' ]]; then
        reset_script_work_dir
        perform_parse_only
    elif [[ "$DEVELOPMENT_MODE" = 'mock' ]]; then
        reset_script_work_dir
        generate_mock_config_template > "$MOCK_CFG"
        cat "$MOCK_CFG"
        prompt_continuation "Proceed with importing the above data?"
        "$DU_ETL_HOME"/main/run-template.bash --use-config "$MOCK_CFG" || clean_and_die "Mock Run Failed!"
    fi
    return
}

function determine_development_mode() {
    if [[ -n "$DEVELOPMENT_MODE" ]]; then
        return
    fi
    if [[ -n "$MOCK_BRAND" ]]; then
        DEVELOPMENT_MODE='mock'
        return
    fi
    if [[ -n "$DMS_TYPE" ]]; then
        DEVELOPMENT_MODE='test'
        return
    fi
    clean_and_die "Expecting one of the development mode options to be set."
}

function configure_eti_env() {
    ETI_DIR_VAR="DU_ETL_ETI_DIR_${DMS_TYPE}"
    ETI_DIR=$(eval echo "\${${ETI_DIR_VAR}}")
}

function ensure_eti_dir_exists() {
    if [[ ! -d "${ETI_DIR}" ]]; then
        clean_and_die "Cannot import if $ETI_DIR does not even exist"
    fi
}

function configue_for_dev_mode() {
    CONFIG_DIR_VAR="DU_ETL_CONFIG_DIR_${DMS_TYPE}"
    CONFIG_DIR=$(eval echo "\${${CONFIG_DIR_VAR}}")
    TW='/tmp/etl-dms-devmode-tw'
    clear_dir "$TW"
}

function enable_debug_mode() {
    export DU_ETL_DEBUG_MODE=2
    export DU_ETL_DEBUG_TARGET=/dev/stderr
}

function reset_script_work_dir() {
    clear_dir "$SCRIPT_WORK_DIR"
}

main "$@"
