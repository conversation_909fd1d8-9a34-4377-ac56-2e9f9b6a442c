#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

# Enable debugging symbols in printed output
export DU_ETL_PROXYINVOICE_DEBUG=1

SRC_SCHEMA='du_proxy_invoice_test'
DUMP_FILE=$DU_ETL_HOME/DU-ProxyInvoice/src/test/data/bundle-1/adv-test-bundle-1.sql
EXPECTED_OUT_DIR=$DU_ETL_HOME/DU-ProxyInvoice/src/test/data/bundle-1/expected/
TEMP_TEST_OUTPUT_DIR=/tmp/du-etl-proxyinvoice-test
TEMP_TEST_LOG_DIR=/tmp/du-etl-proxyinvoice-test/log
TEMP_TEST_INV_DIR=/tmp/du-etl-proxyinvoice-test/invoices

clear_dir "$TEMP_TEST_OUTPUT_DIR"
mkdir "$TEMP_TEST_LOG_DIR"
mkdir "$TEMP_TEST_INV_DIR"

function psql_proxy() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}'" \
         --set=ON_ERROR_STOP=1 "$@"
}

psql_proxy -c "DROP SCHEMA IF EXISTS $SRC_SCHEMA CASCADE" > "$TEMP_TEST_LOG_DIR"/clear-and-restore.log 2>&1 || die 'Failed to drop schema'
psql_proxy --file "$DUMP_FILE" >> "$TEMP_TEST_LOG_DIR"/clear-and-restore.log || die 'Failed to restore test fixture'

psql_proxy -c 'SELECT * FROM dealership;' > "$TEMP_TEST_LOG_DIR"/status-check-result.log || die 'Could not query dealership and confirm restoration'

progress "Loading of text fixture bundle-1 complete"

(
    cd "$DU_ETL_HOME"/DU-ProxyInvoice
    "$DU_ETL_HOME"/DU-ProxyInvoice/generate-fixed-width-proxy-invoice.bash \
        "$SRC_SCHEMA" \
        "$TEMP_TEST_INV_DIR" \
        "true"

) || die "Proxy invoice generation Failed"

find "$TEMP_TEST_INV_DIR" | sort

say "Done Generating Proxy Invoices"

if [[ "${1:-copy}" = 'copy' ]]; then
    say "Copying Results to Expected Output Directory"
    cp "$TEMP_TEST_INV_DIR"/text/*.txt "$EXPECTED_OUT_DIR"
fi
