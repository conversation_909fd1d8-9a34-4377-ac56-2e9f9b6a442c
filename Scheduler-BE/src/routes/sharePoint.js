var fs = require("fs");
var path = require("path");
// var sp = require("@pnp/sp").sp;
const sp = import('@pnp/sp');

// import("sp").then((sp) => {});
// import { sp } from "@pnp/sp";
// console.log("sp>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",sp);
// import("@pnp/nodejs").then((module) => {
// var SPFetchClient = module.SPFetchClient;});
// import { sp } from '"@pnp/nodejs"';
// const sp = require('@pnp/sp');
// const spPromise = import('@pnp/sp');

var ProgressBar = require("progress");
const appConstants = require("../common/constants");
// const folderUrl = 'Shared Documents'; // Web relative target folder
const folderUrl = "Documents/production/Scheduler"; // Web relative target folder
const rp = require("request-promise");
const segment = require("../controllers/SEGMENT/segmentManager");
const shConstants = require("../common/constants");
const Cryptr = require("cryptr");
var mailSender = require("./mailSender");
var solve360Update = require("./solve360Update");
const { exec } = require('child_process');
const SetProcessJobStatus = require("../model/setProcessJobStatus");

var mailTemplateReplacementValues = {
 status: "Failed",
};

var mailBody = {
 fromAddress: appConstants.NOTIFICATION.FROMADDRESS,
 toAddress: appConstants.NOTIFICATION.TOADDRESS,
 ccAddress: appConstants.NOTIFICATION.CCADDRESS,
};
async function initSharePoint(
 filePath,
 dmsType,
 rerunFlag,
 updateSolve360Data,
 warningObj,
 retryCount,
 jobId
) {
 let hasFailed = false;
 console.log("initSharePoint-----------------------------", updateSolve360Data);
 segment.saveSegment(
    `updateSolve360Data&&&&&&&&&&&&&&&&& ${JSON.stringify(updateSolve360Data)}`
   );
 console.log("Started file upload:", retryCount);
 segment.saveSegment(`Started SharePoint file upload functionality `);
  segment.saveSegment(`Started SharePoint file upload functionality ${filePath}.${dmsType}`);





const oldFilePath = filePath;
const newFileName = updateSolve360Data["processFileName"];
const newFilePath = path.join(path.dirname(oldFilePath), newFileName);

try {
  fs.renameSync(oldFilePath, newFilePath);
  console.log('File renamed successfully to', newFileName);
   segment.saveSegment(`File renamed successfully ',  ${filePath}to${newFilePath}`);

} catch (err) {
  console.error('Error renaming file:', err);
    segment.saveSegment(`Error renaming file ',  ${JSON.stringify(err)}`);
}


  
 try {
  const cryptr = new Cryptr(
   appConstants.conf.SHAREPOINT_API_PRIVATE_SECRET_KEY
  );
  const encrypted = cryptr.encrypt(
   appConstants.conf.SHAREPOINT_API_PUBLIC_SECRET_KEY
  );

  var fileName = path.parse(filePath).name + path.parse(filePath).ext;
  console.log("ENCRYPTED KEY :::::::::::::::::: ", encrypted);
   segment.saveSegment(`Finish file renaming&&&&&&&&&&&&&&&&&&&`);
  var bodyParams = {
   fileName: newFileName,
   filePath: newFilePath,
   secretKey: encrypted,
   dmsType: dmsType,
   isRerun: rerunFlag,
   projectId: updateSolve360Data["projectId"],
   secondProjectId: updateSolve360Data["secondProjectId"],
   solve360Update: updateSolve360Data["solve360Update"],
   userName: updateSolve360Data["userName"],
   warningObj: warningObj,
   thirdPartyUsername: updateSolve360Data["thirdPartyUsername"],
   storeCode: updateSolve360Data["storeCode"],
   groupCode: updateSolve360Data["groupCode"],
   dmsType: updateSolve360Data["dmsType"],
   resumeUser: updateSolve360Data["resumeUser"],
   projectIds: updateSolve360Data["projectIds"],
   companyObj:updateSolve360Data["companyObj"],
   processFileName:updateSolve360Data["processFileName"],
   secondProjectIdList: updateSolve360Data["secondProjectIdList"],
   totalRoCount: updateSolve360Data["totalRoCount"],
   exceptionTypeCounts:updateSolve360Data["exceptionTypeCounts"],
   exceptions: updateSolve360Data["exceptions"]
    ? updateSolve360Data["exceptions"]
    : "",
   inSchedulerId: updateSolve360Data["uniqueId"]
    ? updateSolve360Data["uniqueId"]
    : "",
   testData: updateSolve360Data["testData"]
    ? updateSolve360Data["testData"]
    : "",
  };

  console.log("body params>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",bodyParams);

  const options = {
   method: "POST",
   uri: shConstants.SHARE_POINT_API_URL,
   headers: {
    "Content-Type": "application/json",
   },
   body: JSON.stringify(bodyParams),
  };
  console.log("body params>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>options",options);

  await rp(options)
   .then(async (parsedBody) => {
    hasFailed = false;
    await SetProcessJobStatus.setProcessJobUploadStatus(jobId,true);
    segment.saveSegment(
     `File uploaded successfully to SharePoint {${fileName}}`
    );
   })
   .catch(async (error) => {
    hasFailed = true;
    await SetProcessJobStatus.setProcessJobUploadStatus(jobId,false);
    segment.saveSegment(
     `File uploaded failed to SharePoint {${fileName}},error:${error}`
    );
    console.log("File Upload Failed!!!!!!!!!!!!!!!!!!!!!!", error.toString());

    await retrySharePointUpload(
     filePath,
     dmsType,
     rerunFlag,
     updateSolve360Data,
     warningObj,
     retryCount,
     error
    );
    var fileName = filePath ? filePath.split("/").pop(-1) : "";
    // mailTemplateReplacementValues.fileName = fileName;
    // mailTemplateReplacementValues.filePath = '';
    // mailTemplateReplacementValues.dmsType = dmsType;
    // mailTemplateReplacementValues.isRerun = rerunFlag;

    // mailTemplateReplacementValues.warningObj = warningObj;
    // mailTemplateReplacementValues.storeCode = updateSolve360Data['storeCode'];
    // mailTemplateReplacementValues.groupCode = updateSolve360Data['groupCode'];
    // mailTemplateReplacementValues.dmsType = updateSolve360Data['dmsType'];

    // mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
    // var statusObj = await mailSender.sendMail(mailBody, appConstants.NOTIFICATION_TYPES.SHARE_POINT);
    // if (statusObj.status == 'success') {
    //     console.log('Notification mail send successfully', JSON.stringify(mailTemplateReplacementValues));
    //     segment.saveSegment(`Notification mail send successfully ${JSON.stringify(mailTemplateReplacementValues)}`);
    // } else {
    //     console.log('Failed to send Notification mail', JSON.stringify(mailTemplateReplacementValues));
    //     segment.saveSegment(`Failed to send Notification mail ${JSON.stringify(mailTemplateReplacementValues)}`);
    //     console.error(statusObj);
    // }
    segment.saveSegment(
     `Failed to upload file to SharePoint {${fileName}} ${error.toString()}`
    );
   });
 } catch (error) {
  var fileName = filePath ? filePath.split("/").pop(-1) : "";
  await SetProcessJobStatus.setProcessJobUploadStatus(jobId,false);
  hasFailed = true;
  segment.saveSegment(
   `File uploaded failed to SharePoint {${filePath}},error:${error}`
  );
  console.log("File Upload Failed!!!!!!!!!!!!!!!!!!!!!!", error.toString());
  await retrySharePointUpload(
   filePath,
   dmsType,
   rerunFlag,
   updateSolve360Data,
   warningObj,
   retryCount,
   error
  );
  // var fileName = filePath ? filePath.split("/").pop(-1) : '';
  // mailTemplateReplacementValues.fileName = fileName;
  // mailTemplateReplacementValues.filePath = '';
  // mailTemplateReplacementValues.dmsType = dmsType;
  // mailTemplateReplacementValues.isRerun = rerunFlag;
  // mailTemplateReplacementValues.errorwarningMessage = errorwarningMessage;
  // mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
  // var statusObj = await mailSender.sendMail(mailBody, appConstants.NOTIFICATION_TYPES.SHARE_POINT);
  // if (statusObj.status == 'success') {
  //     console.log('Notification mail send successfully', JSON.stringify(mailTemplateReplacementValues));
  //     segment.saveSegment(`Notification mail send successfully ${JSON.stringify(mailTemplateReplacementValues)}`);
  // } else {
  //     console.log('Failed to send Notification mail', JSON.stringify(mailTemplateReplacementValues));
  //     segment.saveSegment(`Failed to send Notification mail ${JSON.stringify(mailTemplateReplacementValues)}`);
  //     console.error(statusObj);
  // }
  await SetProcessJobStatus.setProcessJobUploadStatus(jobId,false);
  hasFailed = true;
  segment.saveSegment(
   `Failed to upload file to SharePoint {${fileName}} ${error.toString()}`
  );
 } finally {
    if (hasFailed && retryCount >= 3) {
        segment.saveSegment(
            `hasFailed && retryCount >= 3 {${fileName}} ${hasFailed}`
           );
           let inSchedulerId = updateSolve360Data["uniqueId"] ? updateSolve360Data["uniqueId"]: "";
           const schedulerQuery = getImportHaltStatus(inSchedulerId);
           const schedulerOptions = getOptions(appConstants.conf.GRAPHQL_SCHEDULER_URI, schedulerQuery);
           var importHaltStatus;
   
           try {
           const response = await rp(schedulerOptions);
           console.log('Response:', response);
           importHaltStatus = response;
           } catch (err) {
           segment.saveSegment(`ImportHaltStatusErr: ${err}`);
           }
        // Call completePayloadProcess only if all retries failed
        await completePayloadProcess({
            filePath,
            isRerun: rerunFlag,
            projectId: updateSolve360Data['projectId'],
            secondProjectId: updateSolve360Data['secondProjectId'],
            solve360Update: updateSolve360Data['solve360Update'],
            userName: updateSolve360Data['userName'],
            thirdPartyUsername: updateSolve360Data['thirdPartyUsername'],
            storeCode: updateSolve360Data['storeCode'],
            groupCode: updateSolve360Data['groupCode'],
            dmsType: updateSolve360Data['dmsType'],
            resumeUser: updateSolve360Data['resumeUser'],
            exceptions: updateSolve360Data['exceptions'],
            projectIds: updateSolve360Data['projectIds'],
            secondProjectIdList: updateSolve360Data['secondProjectIdList'],
            inSchedulerId: updateSolve360Data['uniqueId'],
            testData: updateSolve360Data['testData'],
            warningObj,
            importHaltStatus
        });         
    }
}
}
async function completePayloadProcess(req) {   
    console.log("req.body....", req);
    const {             
        isRerun,
        projectId,
        secondProjectId,
        solve360Update: updateRetreiveROinSolve,
        userName, 
        dmsType,
        exceptions,
        projectIds,
        secondProjectIdList,
        inSchedulerId,
        testData,
        companyObj,
        importHaltStatus
    } = req;

    let importFileExist = false;
    let importHaltFilePath ='';

    if(dmsType.toLowerCase()=='cdk3pa'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/exception/halt-import.txt';
        importFileExist =  await isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='automate'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='dealerbuilt'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-dealerbuilt-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='dealertrack'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='reynoldsrci'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
        
    }else if(dmsType.toLowerCase()=='adam'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-adam-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='dominion'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-dominion-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='dominion'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-autosoft-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='tekion'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-tekion-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='quorum'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-quorum-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='pbs'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-pbs-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='tekionapi'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-tekionapi-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }else if(dmsType.toLowerCase()=='fortellis'){
        importHaltFilePath = '/home/<USER>/tmp/du-etl-dms-fortellis-extractor-work/exception/halt-import.txt';
        importFileExist =  isHaltFileExist(importHaltFilePath);
    }
            

    if (projectId && !testData) {
        if (process.env.UPDATE_SOLVE360) {
            console.log("Begin solve360 update");
            try {
                const status = await solve360Update.updateDate(projectId, userName, updateRetreiveROinSolve, isRerun);
                console.log("Solve 360 Update status:", status);
            } catch (err) {
                console.error("Solve 360 Update error:", err);
            }
        } else {
            console.log("UPDATE_SOLVE360 is false in env");
        }

    
         if (projectIds.length > 0 && !testData ) {
            for (const id of projectIds) {
                if (id) {
                    try {
                        console.log(`Inside portal update complete with project id:${id}`);
                        if (exceptions) {
                            await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, exceptions, inSchedulerId);

                            if (importHaltStatus.length > 0 && importFileExist==true ) {
                                await segment.sleep(2000);
                                await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, exceptions, inSchedulerId, true);
                            }
                        } else {
                            await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, null, inSchedulerId);

                            if (importHaltStatus.length > 0 && importFileExist==true ) {
                                await segment.sleep(2000);
                                await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, exceptions, inSchedulerId, true);
                            }
                        }
                    } catch (err) {
                        console.error("doPayloadActionComplete error:", err);
                    }
                }
            }
        }
    } else {
        console.log("ProjectId not found or testData is true");
    }
    console.log("second project id list@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@",secondProjectIdList);
    console.log("testData!!!!!!!!!!!!!!!!!!!!!!!!!!!!!",testData);
    if (secondProjectId && !testData) {
        console.log("test322222222222222222222222222222222222222222222222222222");
        if (process.env.UPDATE_SOLVE360) {
            console.log("Begin solve360 update for second projectId");
            try {
                const status = await solve360Update.updateDate(secondProjectId, userName, updateRetreiveROinSolve, isRerun);
                console.log("Solve 360 Update status for second projectId:", status);
            } catch (err) {
                console.error("Solve 360 Update error for second projectId:", err);
            }
        } else {
            console.log("UPDATE_SOLVE360 is false in env");
        }
        
        segment.saveSegment(`dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@${dmsType}`);

     
        if(secondProjectIdList.length>0){
            console.log("test44444444444444444444444444444444444444444444444444444444444444");
            for (const id of secondProjectIdList) {
                console.log("test555555555555555555555555555555555555555",id);
                if (id && id !== undefined) {
                    try {
                        console.log(`Inside portal update complete with second project id:${id}`);
                        await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, null, inSchedulerId);
                        await segment.sleep(2000);
                    } catch (err) {
                        console.error("doPayloadActionComplete error for second projectIdList:", err);
                        segment.saveSegment(`PORTAL CALL WITH SECOND PROJECT ID HAS ERROR${err}`);
                    }
                    console.log(`importHaltStatus.length > ${importHaltStatus.length}`);
                    console.log(`importFileExist> ${importFileExist}`);
                    segment.saveSegment(`importHaltStatus.length > ${importHaltStatus.length}`);
                    segment.saveSegment(`importFileExist> ${importFileExist}`);
                    if (importHaltStatus.length > 0 && importFileExist==true) {
                        console.log(`test2@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@`);
                        segment.saveSegment(`test2@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@`)
                        try {
                            console.log(`Inside portal update complete with second project id:${id}`);
                            await solve360Update.doPayloadActionComplete(id, userName, updateRetreiveROinSolve, null, inSchedulerId,true);
                        } catch (err) {
                            console.error("doPayloadActionComplete error for second projectIdList:", err);
                            segment.saveSegment(`IMPORT HALT WITH SECOND PROJECT ID HAS ERROR${err}`);
                        }
                    }
                }

              console.log("second project id has error$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");


            }
        }
      
    } else {
        console.log("Second ProjectId not found");
    }


    try {
       

        if (companyObj) {
            for (const item of companyObj) {                
                const inCompanyId = item.companyId;
                const projectidList = [item.projectId, item.secondProjectId].filter(id => id);
                const mutation = 
                    `mutation {
                        updateScheduleDetails(input: {
                            inSchedulerId: "${inSchedulerId}"
                            inCompanyId: "${inCompanyId}"
                            inProjectIdList: ["${projectidList.join('", "')}"]
                            inCreatedBy: "${userName}"
                            inDms: "${dmsType}"
                            inStatus: "complete"
                        }) {
                            json
                        }
                    }`
                ;

                const options = {
                    method: 'POST',
                    uri: appConstants.conf.GRAPHQL_SCHEDULER_URI,
                    body: { query: mutation },
                    json: true
                };

                console.log("GraphQL mutation options:", options);

                try {
                    const response = await rp(options);
                    console.log('GraphQL response:', response);
                    segment.saveSegment(`ImportHaltStatus:${response}`);
                } catch (err) {
                    console.error('GraphQL error:', err);
                    segment.saveSegment(`ImportHaltStatusErr:${err}`);
                }
            }
        }
    } catch (err) {
        console.error("General error:", err);
        segment.saveSegment(`Solve 360 Update or portal Update have error:${JSON.stringify(err)}`);
    }    
    
}

 function isHaltFileExist(filePath){
    return new Promise((resolve,reject)=>{
        if (fs.existsSync(filePath)) {
            console.log('File exists.');
             unLinkFile(filePath)
            resolve(true);
          } else {
            reject(false);
          }
    })
  
}

function unLinkFile(filePath){
    return new Promise((resolve,reject)=>{
        fs.unlink(filePath, (err) => {
            if (err) {
              console.error('Error deleting the file:', err);
              segment.saveSegment(`Error deleting the file::${err}`);
              reject(false)
            } else {
              console.log('File deleted successfully!');
              resolve(true);
            }
          });
    })
}

function getOptions(URI, query) {
    return {
        method: 'POST',
        uri: URI,
        body: JSON.stringify({ query: query }),
        headers: {
            'Content-Type': 'application/json',
        },
    };
}
function getImportHaltStatus(schedulerId) {
    return `query {
        getSchedulerPreImportStatusBySchedulerId(inSchedulerId: "${schedulerId}")
    }`;
}
function uploadFile(filePath, sharedFolderPath) {
  console.log("filePath============================>",filePath,sharedFolderPath)
 segment.saveSegment(`Inside share point Upload function `);
 const SPAUTH_SITEURL = appConstants.conf.SPAUTH_SITEURL;
 const CLIENT_ID = appConstants.conf.SHAREPOINT_CLIENT_ID;
 const CLIENT_SECRET = appConstants.conf.SHAREPOINT_CLIENT_SECRET;

 console.log("SPAUTH_SITEURL>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>.",SPAUTH_SITEURL);
 console.log("CLIENT_ID>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>.",CLIENT_ID);
 console.log("CLIENT_SECRET>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>.",CLIENT_SECRET);
 console.log("sp>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>.",sp);

  sp.setup({
   sp: {
    fetchClientFactory: () => {
     return new SPFetchClient(SPAUTH_SITEURL, CLIENT_ID, CLIENT_SECRET);
    },
   },
  });
 const folderRelativeUrl = `/${SPAUTH_SITEURL.split("/")

  .slice(3)
  .join("/")}${folderUrl}/${sharedFolderPath}/`;
  console.log("filePath============================>folderRelativeUrl",folderRelativeUrl)

 let progress = null;
 return addChunked(folderRelativeUrl, filePath, (data) => {
  console.log("chunked ============================>data",data)
  if (!progress) {
   progress = new ProgressBar(
    `Uploading ${filePath} [:bar] ${data.fileSize} KB`,
    {
     total: data.totalBlocks,
     width: 20,
    }
   );
  }
  progress.tick();
 });
}

function sharePointFileUploadDir(jobType) {
 var sharedDir = "test";
 if (jobType === "CDK3PA") {
  sharedDir = "cdk3pa";
 } else if (jobType === "AUTOMATE") {
  sharedDir = "automate";
 } else if (jobType === "DEALERBUILT") {
  sharedDir = "dealerbuilt";
 } else if (jobType === "DEALERTRACK") {
  sharedDir = "dealertrack";
 } else if (jobType === "ADAM") {
  sharedDir = "adam";
 } else if (jobType === "REYNOLDSRCI") {
  sharedDir = "reynoldsrci";
 } else if (jobType === "UCS") {
  sharedDir = "ucs";
 } else if (jobType === "DOMINION") {
  sharedDir = "dominion";
 } else if (jobType === "CDKFLEX") {
  sharedDir = "cdkflex";
 } else if (jobType === "AUTOSOFT") {
  sharedDir = "autosoft";
 }else if (jobType === "TEKION") {
  sharedDir = "tekion";
 }else if (jobType === "QUORUM") {
    sharedDir = "quorum";
 }else if (jobType === "PBS") {
    sharedDir = "pbs";
 }else if (jobType === "TEKIONAPI") {
    sharedDir = "tekionapi";
 }else if (jobType === "FORTELLIS") {
    sharedDir = "fortellis";
 }else if (jobType === "FOPC") {  
     sharedDir = "fopc"; 
} else {
  sharedDir = "test";
 }

 if (process.env.SERVER == "sandbox") {
  sharedDir = "test";
 }
 return sharedDir;
}

// function addChunked(folderRelativeUrl, filePath, progress) {
//   import("sp").then((sp) => {
//     console.log("Add chunked********************************");
//  const fileName = path.parse(filePath).name + path.parse(filePath).ext;
//  console.log("filename>>>>>>>>>>>>>>>>>>>>>>>>>>>>:",fileName);
//  return readFile(filePath).then((content) => {
//   console.log("Content******************************",content);
//   return sp.web
//    .getFolderByServerRelativeUrl(folderRelativeUrl)
//    .files.addChunked(fileName, content, progress, true);
//  });
//   });
  
// }

// function addChunked(folderRelativeUrl, filePath, progress) {
//   import("@pnp/sp").then((sp) => {
//     console.log("Add chunked********************************");
//     const fileName = path.parse(filePath).name + path.parse(filePath).ext;
//     console.log("filename>>>>>>>>>>>>>>>>>>>>>>>>>>>>:", fileName);
//     return readFile(filePath).then((content) => {
//       console.log("Content******************************", content);
//       return sp.web
//         .getFolderByServerRelativeUrl(folderRelativeUrl)
//         .files.addChunked(fileName, content, progress, true);
//     });
//   });
// }

function addChunked(folderRelativeUrl, filePath, progress) {
    const fileName = path.parse(filePath).name + path.parse(filePath).ext;
    console.log("sp>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>.",sp);

    return readFile(filePath)
        .then(content => {
            return sp.web.getFolderByServerRelativeUrl(folderRelativeUrl)
                .files.addChunked(fileName, content, progress, true);
        });
}

function readFile(filePath) {
 return new Promise((resolve, reject) => {
  fs.stat(filePath, (statErr, stats) => {
   if (statErr) {
    return reject(statErr);
   }
   fs.readFile(filePath, (readErr, buffer) => {
    if (readErr) {
     return reject(readErr);
    }
    // This is required to tream Buffer the same way as Blob
    buffer.size = stats.size;
    resolve(buffer);
   });
  });
 });
}

async function retrySharePointUpload(
 filePath,
 dmsType,
 rerunFlag,
 updateSolve360Data,
 warningObj,
 retryCount,
 error
) {
 let fileName = filePath ? filePath.split("/").pop(-1) : "";
 segment.saveSegment(
  `Share point file upload retry attempt : ${retryCount}, DMS : ${dmsType}, file name:${fileName} ,error:${JSON.stringify(
   error
  )}`
 );
 console.log(
  `Share point file upload retry attempt : ${retryCount}, DMS : ${dmsType}, file name:${fileName} ,error:${JSON.stringify(
   error
  )}`
 );
 if (retryCount < 3) {
  retryCount++;
  initSharePoint(
   filePath,
   dmsType,
   rerunFlag,
   updateSolve360Data,
   warningObj,
   retryCount
  );
 } else {
  segment.saveSegment(
   `File uploaded failed to SharePoint {${fileName}},error:${error}`
  );
  console.log("File Upload Failed!!!!!!!!!!!!!!!!!!!!!!", error.toString());
  mailTemplateReplacementValues.fileName = fileName;
  mailTemplateReplacementValues.filePath = "";
  mailTemplateReplacementValues.dmsType = dmsType;
  mailTemplateReplacementValues.isRerun = rerunFlag;
  mailTemplateReplacementValues.storeCode;
  mailTemplateReplacementValues.groupCode;
  mailTemplateReplacementValues.warningObj = warningObj;
  mailTemplateReplacementValues.storeCode = updateSolve360Data['storeCode'] ? updateSolve360Data['storeCode'] : "" ;
  mailTemplateReplacementValues.groupCode = updateSolve360Data['groupCode'] ? updateSolve360Data['groupCode'] : "";
  mailTemplateReplacementValues.dmsType = updateSolve360Data["dmsType"];
  mailTemplateReplacementValues.resumeUser = updateSolve360Data["resumeUser"];

  mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
    //const newEmailAddress = "<EMAIL>";
    //toAddress = appConstants.NOTIFICATION.TOADDRESS;
    //toAddress.push(newEmailAddress);
    //mailBody.toAddress = toAddress;
  var statusObj = await mailSender.sendMail(
   mailBody,
   appConstants.NOTIFICATION_TYPES.SHARE_POINT
  );
  if (appConstants.CREATE_JIRA_TICKET_ON_UPLOAD_FAIL) {
    await sendEmailForCreateJiraTicketOnUploadFailure(mailBody.mailTemplate);
  }
  if (statusObj.status == "success") {
   console.log(
    "Notification mail send successfully",
    JSON.stringify(mailTemplateReplacementValues)
   );
   segment.saveSegment(
    `Notification mail send successfully ${JSON.stringify(
     mailTemplateReplacementValues
    )}`
   );
  } else {
   console.log(
    "Failed to send Notification mail",
    JSON.stringify(mailTemplateReplacementValues)
   );
   segment.saveSegment(
    `Failed to send Notification mail ${JSON.stringify(
     mailTemplateReplacementValues
    )}`
   );
   console.error(statusObj);
  }
 }
}

function uploadFileUsingRclone(filePath, sharedFolderPath) {
  return new Promise((resolve, reject) => {
      console.log("File Size is less than 100 MB");
      segment.saveSegment(`sharedFolderPath`,sharedFolderPath);
      segment.saveSegment(`filePath`,filePath);
      const destinationPath = `sharepoint:/${sharedFolderPath}`;

      const command = `rclone copy --ignore-times --ignore-size --verbose "${filePath}" "${destinationPath}"`;

      exec(command, (error, stdout, stderr) => {
          if (error) {
              console.error(`Error: ${error.message}`);
              reject(error);
              return;
          }
          if (stderr) {
              if (stderr.split("Transferred").reverse()[0].includes("100%")) {
                  console.log("TEST@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
                  resolve(true);
              }
          } else {
              resolve(false);
          }
      });
  });
}
// Function to prepare and send the email for Jira ticket creation
async function sendEmailForCreateJiraTicketOnUploadFailure(mailTemplate) {
    const secondMailBody = {
     fromAddress: appConstants.NOTIFICATION.FROMADDRESS,
     toAddress: appConstants.NOTIFICATION_TO_JIRA.TOADDRESS,
     ccAddress: appConstants.NOTIFICATION_TO_JIRA.CCADDRESS,
     mailTemplate: mailTemplate,
    };
    const secondStatusObj =  await mailSender.sendMail(
     secondMailBody,
     appConstants.NOTIFICATION_TYPES.SHARE_POINT,false
    );
    const logMessage =
     secondStatusObj.status === "success"
      ? "Second email sent successfully for Jira ticket creation"
      : "Failed to send second email for Jira ticket creation";
    console.log(
     logMessage,
     typeof mailTemplate === "object" ? JSON.stringify(mailTemplate) : mailTemplate
    );
    segment.saveSegment(
     `${logMessage} ${
      typeof mailTemplate === "object"
       ? JSON.stringify(mailTemplate)
       : mailTemplate
     }`
    );
    if (secondStatusObj.status !== "success") {
     console.error(secondStatusObj);
    }
}
module.exports = {
 uploadFile: uploadFile,
 initSharePoint: initSharePoint,
 sharePointFileUploadDir: sharePointFileUploadDir,
 uploadFileUsingRclone:uploadFileUsingRclone
};
