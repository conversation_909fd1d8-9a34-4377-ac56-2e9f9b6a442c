const path = require('path')
var fs = require('fs');
var handlebars = require('handlebars');
const appConstants = require('../common/constants');
const segment = require("../controllers/SEGMENT/segmentManager");
var notifyObj = {};
let preImportHaltFilePath;

function isPreImportHaltExist(dms){
    return new Promise((resolve,reject)=>{
      preImportHaltFilePath = `/home/<USER>/tmp/du-etl-dms-${dms}-extractor-work/pre-import-halt/halt-import.txt`;
        if (fs.existsSync(preImportHaltFilePath)) {
          fs.readFile(preImportHaltFilePath, "utf8", (err, data) => {
            if (err) {
              console.error("Error reading file:", err);
              return;
            }
            const regex = /paytype Halt:\s+(\d+)|Inv Sequence Halt:\s+(\d+)|Make Halt:\s+(\d+)|Department Halt:\s+(\d+)/g;
            let match;
            const result = {};
          
            while ((match = regex.exec(data)) !== null) {
              if (match[1] !== undefined) result["paytype Halt"] = parseInt(match[1], 10);
              if (match[2] !== undefined) result["Inv Sequence Halt"] = parseInt(match[2], 10);
              if (match[3] !== undefined) result["Make Halt"] = parseInt(match[3], 10);
              if (match[4] !== undefined) result["Department Halt"] = parseInt(match[4], 10);
            }
          
            console.log("Extracted values:", result['paytype Halt']);
            console.log("Extracted values:", result['Inv Sequence Halt']);
            console.log("Extracted values:", result['Make Halt']);
            resolve(result);
            
          });

          
        } else {
          resolve(false);
          console.log("File does not exist:", preImportHaltFilePath);
        }
    })
}




var sendMail =  function (reqBdy, type, uploadStatus = true) {
 
 segment.saveSegment(`Inside send mail method`);
  console.log(`Inside send mail method`);
  return new Promise(async(resolve, reject) => {
    var API_KEY = process.env.MAILGUN_API_KEY;
    var DOMAIN = process.env.MAILGUN_DOMAIN;
    if (API_KEY && DOMAIN) {
      var mailgun = require('mailgun-js')({ apiKey: API_KEY, domain: DOMAIN, retry: 3 });
      var parsedBody = JSON.parse(reqBdy.mailTemplate);
      console.log("Mail body *********************************",parsedBody);
      var html = '';
      var notifyTemplate = '';
      var sendNotification = true;
      var mailSubject = '';
      var storeCode;
      var groupCode;
      var isPreImportExist;
      var paytypeHalt;
      var invSequenceHalt;
      var makeHalt;
      var departmentHalt;

      if(parsedBody.hasOwnProperty('storeCode')){
        storeCode = parsedBody.storeCode;
      }
      if(parsedBody.hasOwnProperty('groupCode')){
        groupCode = parsedBody.groupCode;
      }
    
      if (type === appConstants.NOTIFICATION_TYPES.SHARE_POINT) {
        notifyTemplate = '/emailTemplate/sharePointUploadEmailTemplate.html';
      } else {
        notifyTemplate = '/emailTemplate/emailTemplateForExtractionAndProcessXml.html';
      }
      //DMS type set to share point upload mail subject
      var dmsType;
      if(parsedBody.dmsType.toLowerCase() == 'cdk3pa'){
        dmsType = 'CDK3PA';
      } else if(parsedBody.dmsType.toLowerCase() == 'cdkflex'){
        dmsType = 'CDKFLEX';
      } else if(parsedBody.dmsType.toLowerCase() == 'automate'){
        dmsType = 'AutoMate';
      } else if(parsedBody.dmsType.toLowerCase() == 'dealertrack'){
        dmsType = 'DealerTrack';
      } else if(parsedBody.dmsType.toLowerCase() == 'reynoldsrci'){
        dmsType = 'ReynoldsRCI';
      } else if(parsedBody.dmsType.toLowerCase() == 'domininion'){
        dmsType = 'Dominion';
      }else if(parsedBody.dmsType.toLowerCase() == 'autosoft'){
        dmsType = 'Autosoft';
      }else if(parsedBody.dmsType.toLowerCase() == 'pbs'){
        dmsType = 'Pbs';
      }else if(parsedBody.dmsType.toLowerCase() == 'quorum'){
        dmsType = 'Quorum';
      }else if(parsedBody.dmsType.toLowerCase() == 'mpk'){
        dmsType = 'Mpk';
      }else if(parsedBody.dmsType.toLowerCase() == 'tekion'){
        dmsType = 'Tekion';
      }else if(parsedBody.dmsType.toLowerCase() == 'tekionapi'){
        dmsType = 'TekionApi';
      }else if(parsedBody.dmsType.toLowerCase() == 'fortellis'){
        dmsType = 'Fortellis';
      }else if(parsedBody.dmsType.toLowerCase() == 'ucs'){
        dmsType = 'Ucs';
      }
      else if (parsedBody.dmsType.toLowerCase() == 'fopc') {
        dmsType = 'Fopc';
      }
      else {
        dmsType = parsedBody.dmsType.charAt(0).toUpperCase() + parsedBody.dmsType.toLowerCase().slice(1);
      }
      html = fs.readFileSync(path.join(__dirname, notifyTemplate), { encoding: 'utf-8' });
      var template = handlebars.compile(html);
      if (type === appConstants.NOTIFICATION_TYPES.SHARE_POINT) {
        if(parsedBody.dmsType.toLowerCase() == 'dominion'){
          isPreImportExist = await isPreImportHaltExist('dominionvue');
        }else{
          isPreImportExist = await isPreImportHaltExist(parsedBody.dmsType.toLowerCase());
        }
       

     if(isPreImportExist){
          paytypeHalt = isPreImportExist['paytype Halt'];
          invSequenceHalt = isPreImportExist['Inv Sequence Halt'];
          makeHalt = isPreImportExist['Make Halt'];
          departmentHalt = isPreImportExist['Department Halt'];
        }
        console.log(`paytypeHalt${paytypeHalt} invSequenceHalt${invSequenceHalt} makeHalt${makeHalt} ${departmentHalt} `);
        segment.saveSegment(`paytypeHalt${paytypeHalt} invSequenceHalt${invSequenceHalt} makeHalt${makeHalt} ${departmentHalt} `);
        segment.saveSegment(`isPreImportExist : ${isPreImportExist}`);
  
        segment.saveSegment(`Inside sharepoint file upload completion mail`);
        console.log(`Inside sharepoint file upload completion mail`);
        var resArray = parsedBody.fileName.split("-");
        var storeName = (resArray && resArray.length) ? resArray[2] : parsedBody.fileName;
        segment.saveSegment(`Send Mail: Sharepoint: Filename : ${storeName}`);
        console.log(`Send Mail: Sharepoint: Filename : ${storeName}`);
        if (!notifyObj.hasOwnProperty(storeName)) {
          notifyObj[storeName] = {};
          notifyObj[storeName]['PROXY'] = false;
          console.log(`Setup notifyObj !notifyObj && !notifyObj.storeName : ${notifyObj.hasOwnProperty(storeName) ? JSON.stringify(notifyObj) : 'Empty Object'}`);
          segment.saveSegment(`Setup notifyObj !notifyObj && !notifyObj.storeName : ${notifyObj.hasOwnProperty(storeName) ? JSON.stringify(notifyObj) : 'Empty Object'}`);
        }
        segment.saveSegment(`Send Mail: Sharepoint: notifyObj : ${notifyObj[storeName] ? JSON.stringify(notifyObj[storeName]) : 'Empty Object'}`);
        console.log(`Send Mail: Sharepoint: notifyObj : ${notifyObj[storeName] ? JSON.stringify(notifyObj[storeName]) : 'Empty Object'}`);

        segment.saveSegment(`Send Mail: Sharepoint: parsedBody.fileName : ${parsedBody.fileName}`);
        console.log(`Send Mail: Sharepoint: parsedBody.fileName : ${parsedBody.fileName}`);
        var folderUrl = 'Documents/production/Scheduler'; // Web relative target folder
        var sharePointFilePath = `${appConstants.conf.SPAUTH_SITEURL}${folderUrl}`;
        notifyObj[storeName]['PROXY'] = true;
        notifyObj[storeName]['proxyUploadStatus'] = parsedBody.status;
        notifyObj[storeName]['proxyFileName'] = parsedBody.fileName;
        if (parsedBody.status != 'Failed') {
          notifyObj[storeName]['proxyFilePath'] = parsedBody.filePath ? `${sharePointFilePath}/${parsedBody.dmsType}/${parsedBody.fileName}` : '';
        } else {
          notifyObj[storeName]['proxyFilePath'] = '';
        }
    
        notifyObj[storeName]['proxyMailSubject'] = parsedBody.status === 'Success' ? `${dmsType} Proxy Zip Success` : `${dmsType} Proxy Zip Failed`;
        segment.saveSegment(`Setup notifyObj inside condition else part: ${notifyObj[storeName] ? JSON.stringify(notifyObj[storeName]) : 'Empty Object'}`);

        segment.saveSegment(`Send Mail: Sharepoint: notifyObj after checking: ${notifyObj[storeName] ? JSON.stringify(notifyObj[storeName]) : 'Empty Object'}`);
        console.log(`Send Mail: Sharepoint: notifyObj after checking: ${notifyObj[storeName] ? JSON.stringify(notifyObj[storeName]).toString() : 'Empty Object'}`);
        var proxyStatus = false;
        proxyStatus = notifyObj && notifyObj[storeName] ? notifyObj[storeName]['PROXY'] : false;
        segment.saveSegment(`Send Mail: Sharepoint: Proxy status: ${proxyStatus}`);
        console.log(`Send Mail: Sharepoint: Proxy status: ${proxyStatus}`);
       
 
        if (proxyStatus) {
          mailSubject = `- ${notifyObj[storeName]['proxyMailSubject']}`;
          var eftFilePath = '';
          eftFilePath = notifyObj[storeName]['proxyFileName'] ? notifyObj[storeName]['proxyFileName'].replace(appConstants.REPLACE_STRING.FROM, appConstants.REPLACE_STRING.TO) : '';
          var efsPath = `/etl/etl-vagrant/etl-${parsedBody.dmsType}/${parsedBody.dmsType}-zip/${eftFilePath}`;
          var isRerunFlag = (parsedBody.isRerun == 'RERUN') ? false : true; 
          var replacements = {
            proxy_file_upload_status: (notifyObj[storeName] && notifyObj[storeName]['proxyUploadStatus']) ? notifyObj[storeName]['proxyUploadStatus'] : '',
            proxy_file_name: notifyObj[storeName]['proxyFileName'],
            proxy_url: notifyObj[storeName]['proxyFilePath'],
            efs_path: efsPath,
            isRerun:isRerunFlag,
            warning_message: parsedBody.warningObj.hasOwnProperty("errorwarningMessage") ?  parsedBody.warningObj.errorwarningMessage : '',
            closed_rodetail_warning_message: parsedBody.warningObj.hasOwnProperty("closedRODetailErrorwarningMessage") ? parsedBody.warningObj.closedRODetailErrorwarningMessage : '',
            vehicle_warning_message: parsedBody.warningObj.hasOwnProperty("vehicleErrorwarningMessage") ? parsedBody.warningObj.vehicleErrorwarningMessage : '',
            customer_warning_message: parsedBody.warningObj.hasOwnProperty("customerErrorwarningMessage") ? parsedBody.warningObj.customerErrorwarningMessage : '',
            gldetail_warning_message: parsedBody.warningObj.hasOwnProperty("glDeatilErrorwarningMessage") ? parsedBody.warningObj.glDeatilErrorwarningMessage : '',
            coupon_and_discount_warning_message: parsedBody.warningObj.hasOwnProperty("couponAndDiscountWarningMessage") ? parsedBody.warningObj.couponAndDiscountWarningMessage : '',
            ro_account_description_warning_message: parsedBody.warningObj.hasOwnProperty("roAccountDescriptionWarningMessage") ? parsedBody.warningObj.roAccountDescriptionWarningMessage : '',
            // coupon_and_discount_file_not_uploaded_warning_message: parsedBody.warningObj.hasOwnProperty("couponAndDiscountFileNotUploadedWarningMessage") ? parsedBody.warningObj.couponAndDiscountFileNotUploadedWarningMessage : '',
            skip_error_count: parsedBody.warningObj.hasOwnProperty("skipErrorCount") ? parsedBody.warningObj.skipErrorCount : '',
            core_return_exception_count: parsedBody.warningObj.hasOwnProperty("coreReturnExceptionCount") ? parsedBody.warningObj.coreReturnExceptionCount : '',
            core_charge_exception_count: parsedBody.warningObj.hasOwnProperty("coreChargeExceptionCount") ? parsedBody.warningObj.coreChargeExceptionCount : '',
            core_return_not_equal_core_charge_exception_count: parsedBody.warningObj.hasOwnProperty("coreReturnNotEqualCoreChargeExceptionCount") ? parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionCount : '',
            core_charge_with_no_sale_count: parsedBody.warningObj.hasOwnProperty("coreChargeWithNoSaleCount") ? parsedBody.warningObj.coreChargeWithNoSaleCount : '',
            resume_user_message: parsedBody.hasOwnProperty('resumeUser') ?  parsedBody.resumeUser : '',
            dealer_not_subscribed_message: parsedBody.warningObj.hasOwnProperty("dealerNotSubscribedMessage") ?  parsedBody.warningObj.dealerNotSubscribedMessage : '',
            invalid_core_cost_sale_mismatch_count: parsedBody.warningObj.hasOwnProperty("invalidCoreCostSaleMismatchCount") ? parsedBody.warningObj.invalidCoreCostSaleMismatchCount : '',
            invalid_misc_paytype_count: parsedBody.warningObj.hasOwnProperty("invalidmiscpaytypeCount") ? parsedBody.warningObj.invalidmiscpaytypeCount : '',
            punch_time_missing_count: parsedBody.warningObj.hasOwnProperty("punchTimeMissingCount") ? parsedBody.warningObj.punchTimeMissingCount : '',
            inventory_ro_count: parsedBody.warningObj.hasOwnProperty("inventory_ro_count") ? parsedBody.warningObj.inventory_ro_count : '',
            invalid_core_amount_mismatch_count: parsedBody.warningObj.hasOwnProperty("invalidCoreAmountMismatchCount") ? parsedBody.warningObj.invalidCoreAmountMismatchCount : '',
            coa_exception_warning_message: parsedBody.warningObj.hasOwnProperty("coaExceptionWarningMessage") ? parsedBody.warningObj.coaExceptionWarningMessage : '',
            customer_exception_warning_message: parsedBody.warningObj.hasOwnProperty("customerExceptionWarningMessage") ? parsedBody.warningObj.customerExceptionWarningMessage : '',
            misc_exception_count: parsedBody.warningObj.hasOwnProperty("miscExceptionCount") ? parsedBody.warningObj.miscExceptionCount : '',
            gl_ro_not_found_count: parsedBody.warningObj.hasOwnProperty("gl_missing_ro_count") ? parsedBody.warningObj.gl_missing_ro_count : '',
            invoice_missing_count: parsedBody.warningObj.hasOwnProperty("invoice_missing_count") ? parsedBody.warningObj.invoice_missing_count : '',
            suffixed_invoices_count: parsedBody.warningObj.hasOwnProperty("suffixedInvoicesCount") ? parsedBody.warningObj.suffixedInvoicesCount : '',
            company_no_not_matching_count: parsedBody.warningObj.hasOwnProperty("company_no_not_matching_count") ? parsedBody.warningObj.company_no_not_matching_count : '',
            grouped_team_work_count: parsedBody.warningObj.hasOwnProperty("grouped_team_work_count") ? parsedBody.warningObj.grouped_team_work_count : '',
            dealerAddress: parsedBody.warningObj.hasOwnProperty("dealerAddress") ? parsedBody.warningObj.dealerAddress.replace(/~/g,', ') : '',
            split_job_exception_count: parsedBody.warningObj.hasOwnProperty("splitJobExceptionCount") ? parsedBody.warningObj.splitJobExceptionCount : '',
            new_line_type_count: parsedBody.warningObj.hasOwnProperty("new_line_type_count") ? parsedBody.warningObj.new_line_type_count : '',
            chart_of_accounts_file_path: parsedBody.warningObj.hasOwnProperty("chart_of_accounts_file_path") ? parsedBody.warningObj.chart_of_accounts_file_path: '',
            exception_closed_invoices_count: parsedBody.warningObj.hasOwnProperty("exceptionClosedInvoicesCount") ? parsedBody.warningObj.exceptionClosedInvoicesCount : '',
            extra_ro_in_xml_exception_count: parsedBody.warningObj.hasOwnProperty("extraRoInXmlExceptionCount") ? parsedBody.warningObj.extraRoInXmlExceptionCount : '',
            im_Opened_Closed_Rci_Ros_Count: parsedBody.warningObj.hasOwnProperty("imOpenedClosedRciRosCount") ? parsedBody.warningObj.imOpenedClosedRciRosCount : '',
            Sale_Zero_Cost_NonZero_Parts_Count: parsedBody.warningObj.hasOwnProperty("SaleZeroCostNonZeroPartsCount") ? parsedBody.warningObj.SaleZeroCostNonZeroPartsCount : '',
            gl_Deatil_Extraction_Error_Count: parsedBody.warningObj.hasOwnProperty("glDeatilExtractionErrorCount") ? parsedBody.warningObj.glDeatilExtractionErrorCount : '', 
            less_special_discount_count: parsedBody.warningObj.hasOwnProperty("lessSpecialDiscountCount") ? parsedBody.warningObj.lessSpecialDiscountCount : '',
            negative_coupon_count: parsedBody.warningObj.hasOwnProperty("negative_coupon_count") ? parsedBody.warningObj.negative_coupon_count : '',
            labor_with_zero_sale_nonzero_cost_count: parsedBody.warningObj.hasOwnProperty("labor_with_zero_sale_nonzero_cost_count") ? parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_count : '',
            gl_missing_ros_count: parsedBody.warningObj.hasOwnProperty("gl_missing_ros_count") ? parsedBody.warningObj.gl_missing_ros_count : '',
            part_description_exception_count: parsedBody.warningObj.hasOwnProperty("partDescriptionExceptionCount") ? parsedBody.warningObj.partDescriptionExceptionCount : '',
            coupon_discount_basis_amount_mismatch_exception_count: parsedBody.warningObj.hasOwnProperty("coupon_discount_basis_amount_mismatch_exception_count") ? parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_count : '',
            labor_with_no_paytype_exception_count: parsedBody.warningObj.hasOwnProperty("labor_with_no_paytype_exception_count") ? parsedBody.warningObj.labor_with_no_paytype_exception_count : '',   
            parts_excluded_from_history_exception_count:parsedBody.warningObj.hasOwnProperty("parts_excluded_from_history_exception_count") ? parsedBody.warningObj.parts_excluded_from_history_exception_count : '',
            lost_sale_parts_exception_count:parsedBody.warningObj.hasOwnProperty("lost_sale_parts_exception_count") ? parsedBody.warningObj.lost_sale_parts_exception_count : '',
            part_details_null_exception_count: parsedBody.warningObj.hasOwnProperty("partDetailsNullExceptionCount") ? parsedBody.warningObj.partDetailsNullExceptionCount : '',
            multiLaborExceptionCount: parsedBody.warningObj.hasOwnProperty("multiLaborExceptionCount") ? parsedBody.warningObj.multiLaborExceptionCount : '',
            scheduled_by:parsedBody.warningObj.hasOwnProperty("scheduled_by") ? parsedBody.warningObj.scheduled_by : '',
            testData:parsedBody.warningObj.hasOwnProperty("testData") ? parsedBody.warningObj.testData : false,
            paytype_halt:paytypeHalt,
            inv_sequence_halt:invSequenceHalt,
            make_halt:makeHalt,
            department_halt:departmentHalt,
            pre_import_halt_exist:isPreImportExist?true:false
          };

          if(parsedBody.hasOwnProperty('resumeUser')){
             if(parsedBody.resumeUser){
              replacements.resume_user_message_exist_flag = parsedBody.resumeUser.length > 0 ? true :false;
             }
          } 

          if(paytypeHalt>0){
            replacements.paytypeHalt_exist_flag = true;
          }

          if(invSequenceHalt>0){
            replacements.invSequenceHalt_exist_flag = true;
          }

          if(makeHalt>0){
            replacements.makeHalt_exist_flag = true;
          }

         
          if(departmentHalt>0){
            replacements.departmentHalt_exist_flag = true;
          }


          if(isPreImportExist){
            replacements.pre_import_halt_exist = true;
          }

  
          if(parsedBody.warningObj.hasOwnProperty("errorwarningMessage")){
            if(parsedBody.warningObj.errorwarningMessage){
              replacements.warning_message_exist_flag =parsedBody.warningObj.errorwarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("dealerNotSubscribedMessage")){
            if(parsedBody.warningObj.dealerNotSubscribedMessage){
              replacements.dealer_not_subscribed_message_exist_flag =parsedBody.warningObj.dealerNotSubscribedMessage.length > 0 ? true :false;
            }
          } else{
            replacements.dealer_not_subscribed_message_exist_flag = false;
          }
          
          if(parsedBody.warningObj.hasOwnProperty("closedRODetailErrorwarningMessage")){
            if(parsedBody.warningObj.closedRODetailErrorwarningMessage){
              replacements.closed_rodetail_warning_message_exist_flag = parsedBody.warningObj.closedRODetailErrorwarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.closed_rodetail_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("vehicleErrorwarningMessage")){
            if( parsedBody.warningObj.vehicleErrorwarningMessage){
              replacements.vehicle_warning_message_exist_flag =  parsedBody.warningObj.vehicleErrorwarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.vehicle_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("customerErrorwarningMessage")){
            if( parsedBody.warningObj.customerErrorwarningMessage){
              replacements.customer_warning_message_exist_flag =  parsedBody.warningObj.customerErrorwarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.customer_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("glDeatilErrorwarningMessage")){
            if( parsedBody.warningObj.glDeatilErrorwarningMessage){
              replacements.gldetail_warning_message_exist_flag =  parsedBody.warningObj.glDeatilErrorwarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.gldetail_warning_message_exist_flag = false;
          }


          if(parsedBody.warningObj.hasOwnProperty("couponAndDiscountWarningMessage")){
            if( parsedBody.warningObj.couponAndDiscountWarningMessage){
              replacements.coupon_and_discount_message_exist_flag =  parsedBody.warningObj.couponAndDiscountWarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.coupon_and_discount_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("roAccountDescriptionWarningMessage")){
            if( parsedBody.warningObj.roAccountDescriptionWarningMessage){
              replacements.ro_account_description_message_exist_flag =  parsedBody.warningObj.roAccountDescriptionWarningMessage > 0 ? true :false;
            }
          } else{
            replacements.ro_account_description_message_exist_flag = false;
          }
          if(parsedBody.warningObj.hasOwnProperty("chart_of_accounts_file_path")){
            if( parsedBody.warningObj.chart_of_accounts_file_path){
              replacements.chart_of_accounts_file_path_exist_flag =  true
            }
          } else{
            replacements.chart_of_accounts_file_path_exist_flag = false;
          }


          if(parsedBody.warningObj.hasOwnProperty("coaExceptionWarningMessage")){
            if( parsedBody.warningObj.coaExceptionWarningMessage){
              replacements. coa_exception_warning_message_exist_flag =  parsedBody.warningObj.coaExceptionWarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.coa_exception_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("customerExceptionWarningMessage")){
            if( parsedBody.warningObj.customerExceptionWarningMessage){
              replacements. customer_exception_warning_message_exist_flag =  parsedBody.warningObj.customerExceptionWarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.customer_exception_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("skipErrorCount")){
            if( parsedBody.warningObj.skipErrorCount){
              replacements.skip_error_count_message_exist_flag =  parsedBody.warningObj.skipErrorCount.length > 0 ? true :false;
            }
          } else{
            replacements.skip_error_count_message_exist_flag = false;
          }

          /////////////////////////
          if(parsedBody.warningObj.hasOwnProperty("coreReturnExceptionCount")){
            if(parsedBody.warningObj.coreReturnExceptionCount){
              replacements.core_return_exception_exist_flag = parsedBody.warningObj.coreReturnExceptionCount ? true :false;
            }
          } else{
            replacements.core_return_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("coreChargeExceptionCount")){
            if(parsedBody.warningObj.coreChargeExceptionCount){
              replacements.core_charge_exception_exist_flag = parsedBody.warningObj.coreChargeExceptionCount  ? true :false;
            }
          } else{
            replacements.core_charge_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("coreReturnNotEqualCoreChargeExceptionCount")){
            if(parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionCount){
              replacements.core_return_not_equal_to_core_charge_exception_exist_flag =  parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionCount ? true :false;
            }
          } else{
            replacements.core_return_not_equal_to_core_charge_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("coreChargeWithNoSaleCount")){
            if(parsedBody.warningObj.coreChargeWithNoSaleCount){
              replacements.core_charge_with_no_sale_exception_exist_flag =  parsedBody.warningObj.coreChargeWithNoSaleCount ? true :false;
            }
          } else{
            replacements.core_charge_with_no_sale_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("gl_missing_ro_count")){
            if(parsedBody.warningObj.gl_missing_ro_count){
              replacements.gl_ro_not_found_exception_exist_flag =  parsedBody.warningObj.gl_missing_ro_count ? true :false;
            }
          } else{
            replacements.gl_ro_not_found_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("invoice_missing_count")){
            if(parsedBody.warningObj.invoice_missing_count){
              replacements.invoice_missing_exception_exist_flag =  parsedBody.warningObj.invoice_missing_count ? true :false;
            }
          } else{
            replacements.invoice_missing_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("invalidCoreCostSaleMismatchCount")){
            if(parsedBody.warningObj.invalidCoreCostSaleMismatchCount){
              replacements.invalid_core_cost_sale_mismatch_exist_flag =  parsedBody.warningObj.invalidCoreCostSaleMismatchCount ? true :false;
            }
          } else{
            replacements.invalid_core_cost_sale_mismatch_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("invalidmiscpaytypeCount")){
            if(parsedBody.warningObj.invalidmiscpaytypeCount){
              replacements.invalid_misc_paytype_flag =  parsedBody.warningObj.invalidmiscpaytypeCount ? true :false;
            }
          } else{
            replacements.invalid_misc_paytype_flag = false;
          }
          
          if(parsedBody.warningObj.hasOwnProperty("exceptionClosedInvoicesCount")){
            if(parsedBody.warningObj.exceptionClosedInvoicesCount){
              replacements.exception_closed_invoices_flag =  parsedBody.warningObj.exceptionClosedInvoicesCount ? true :false;
            }
          } else{
            replacements.exception_closed_invoices_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("extraRoInXmlExceptionCount")){
            if(parsedBody.warningObj.extraRoInXmlExceptionCount){
              replacements.extra_ro_in_xml_exception_flag =  parsedBody.warningObj.extraRoInXmlExceptionCount ? true :false;
            }
          } else{
            replacements.extra_ro_in_xml_exception_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("imOpenedClosedRciRosCount")){
            if(parsedBody.warningObj.imOpenedClosedRciRosCount){
              replacements.im_opened_closed_rci_ros_flag =  parsedBody.warningObj.imOpenedClosedRciRosCount ? true :false;
            }
          } else{
            replacements.im_opened_closed_rci_ros_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("SaleZeroCostNonZeroPartsCount")){
            if(parsedBody.warningObj.SaleZeroCostNonZeroPartsCount){
              replacements.Sale_Zero_Cost_NonZero_Parts_flag =  parsedBody.warningObj.SaleZeroCostNonZeroPartsCount ? true :false;
            }
          } else{
            replacements.Sale_Zero_Cost_NonZero_Parts_flag = false;
          }


          if(parsedBody.warningObj.hasOwnProperty("glDeatilExtractionErrorCount")){
            if(parsedBody.warningObj.glDeatilExtractionErrorCount){
              replacements.gl_Deatil_Extraction_Error_Count_flag =  parsedBody.warningObj.glDeatilExtractionErrorCount ? true :false;
            }
          } else{
            replacements.gl_Deatil_Extraction_Error_Count_flag = false;
          }

          

         if(parsedBody.warningObj.hasOwnProperty("splitJobExceptionCount")){
            if(parsedBody.warningObj.splitJobExceptionCount){
              replacements.split_job_exception_flag =  parsedBody.warningObj.splitJobExceptionCount ? true :false;
            }
          } else{
            replacements.split_job_exception_flag = false;
          }

         if(parsedBody.warningObj.hasOwnProperty("lessSpecialDiscountCount")){
          if(parsedBody.warningObj.lessSpecialDiscountCount){
            replacements.less_special_discount_exception_flag =  parsedBody.warningObj.lessSpecialDiscountCount ? true :false;
          }
        } else{
          replacements.less_special_discount_exception_flag  = false;
        }

        if(parsedBody.warningObj.hasOwnProperty("negative_coupon_count")){
          if(parsedBody.warningObj.negative_coupon_count){
            replacements.negative_coupon_exception_flag =  parsedBody.warningObj.negative_coupon_count ? true :false;
          }
        } else{
          replacements.negative_coupon_exception_flag  = false;
        }
         
        if(parsedBody.warningObj.hasOwnProperty("gl_missing_ros_count")){
          if(parsedBody.warningObj.gl_missing_ros_count){
            replacements.gl_missing_ros_exception_flag =  parsedBody.warningObj.gl_missing_ros_count ? true :false;
            }
           else{
              replacements.gl_missing_ros_exception_flag  = false;
            }
        }

        if(parsedBody.warningObj.hasOwnProperty("partDescriptionExceptionCount")){
          if(parsedBody.warningObj.partDescriptionExceptionCount >0){
            replacements.part_description_exception_flag =  parsedBody.warningObj.partDescriptionExceptionCount ? true :false;
            }
           else{
              replacements.part_description_exception_flag  = false;
            }
        }


        if(parsedBody.warningObj.hasOwnProperty("coupon_discount_basis_amount_mismatch_exception_count")){
          if(parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_count >0){
            replacements.coupon_discount_basis_amount_mismatch_exception_flag =  parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_count ? true :false;
            }
           else{
              replacements.coupon_discount_basis_amount_mismatch_exception_flag  = false;
            }
        }


        if(parsedBody.warningObj.hasOwnProperty("labor_with_no_paytype_exception_count")){
          if(parsedBody.warningObj.labor_with_no_paytype_exception_count >0){
            replacements.labor_with_no_paytype_exception_flag =  parsedBody.warningObj.labor_with_no_paytype_exception_count ? true :false;
            }
           else{
              replacements.labor_with_no_paytype_exception_flag  = false;
            }
        }

        
        if(parsedBody.warningObj.hasOwnProperty("parts_excluded_from_history_exception_count")){
          if(parsedBody.warningObj.parts_excluded_from_history_exception_count >0){
            replacements.parts_excluded_from_history_exception_flag =  parsedBody.warningObj.parts_excluded_from_history_exception_count ? true :false;
            }
           else{
              replacements.parts_excluded_from_history_exception_flag  = false;
            }
        }

        if(parsedBody.warningObj.hasOwnProperty("lost_sale_parts_exception_count")){
          if(parsedBody.warningObj.lost_sale_parts_exception_count >0){
            replacements.lost_sale_parts_exception_flag =  parsedBody.warningObj.lost_sale_parts_exception_count ? true :false;
            }
           else{
              replacements.lost_sale_parts_exception_flag  = false;
            }
        }
        
        if(parsedBody.warningObj.hasOwnProperty("partDetailsNullExceptionCount")){
          if(parsedBody.warningObj.partDetailsNullExceptionCount >0){
            replacements.part_details_null_exception_flag =  parsedBody.warningObj.partDetailsNullExceptionCount ? true :false;
            }
           else{
              replacements.part_details_null_exception_flag  = false;
            }
        }
        
        if(parsedBody.warningObj.hasOwnProperty("labor_with_zero_sale_nonzero_cost_count")){
          if(parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_count){
            replacements.labor_with_zero_sale_nonzero_cost_exception_flag =  parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_count ? true :false;
          }
        } else{
          replacements.labor_with_zero_sale_nonzero_cost_exception_flag  = false;
        }


        
          
          if(parsedBody.warningObj.hasOwnProperty("punchTimeMissingCount")){
            if(parsedBody.warningObj.punchTimeMissingCount >40){
              replacements.punch_time_missing_flag =  parsedBody.warningObj.punchTimeMissingCount ? true :false;
            }
          } else{
            replacements.punch_time_missing_flag = false;
          }


          if(parsedBody.warningObj.hasOwnProperty("suffixedInvoicesCount")){
            if(parsedBody.warningObj.suffixedInvoicesCount >40){
              replacements.suffixed_invoices_flag =  parsedBody.warningObj.suffixedInvoicesCount ? true :false;
            }
          } else{
            replacements.suffixed_invoices_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("company_no_not_matching_count")){
            if(parsedBody.warningObj.company_no_not_matching_count >0){
              replacements.company_no_not_matching_flag =  parsedBody.warningObj.company_no_not_matching_count ? true :false;
            }
          } else{
            replacements.company_no_not_matching_flag = false;
          }


          if(parsedBody.warningObj.hasOwnProperty("grouped_team_work_count")){
            if(parsedBody.warningObj.grouped_team_work_count >0){
              replacements.grouped_team_work_flag =  parsedBody.warningObj.grouped_team_work_count ? true :false;
            }
          } else{
            replacements.grouped_team_work_flag= false;
          }




          if(parsedBody.warningObj.hasOwnProperty("inventory_ro_count")){
            if(parsedBody.warningObj.inventory_ro_count >0){
              replacements.inventory_ro_flag =  parsedBody.warningObj.inventory_ro_count ? true :false;
            }
          } else{
            replacements.inventory_ro_flag = false;
          }



          if(parsedBody.warningObj.hasOwnProperty("invalidCoreAmountMismatchCount")){
            if(parsedBody.warningObj.invalidCoreAmountMismatchCount){
              replacements.invalid_core_amount_mismatch_exist_flag =  parsedBody.warningObj.invalidCoreAmountMismatchCount ? true :false;
            }
          } else{
            replacements.invalid_core_amount_mismatch_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("miscExceptionCount")){
            if(parsedBody.warningObj.miscExceptionCount){
              replacements.misc_exception_exist_flag = parsedBody.warningObj.miscExceptionCount ? true :false;
            }
          } else{
            replacements.misc_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("multiLaborExceptionCount")){
            if(parsedBody.warningObj.multiLaborExceptionCount){
              replacements.multi_labor_exception_exist_flag = parsedBody.warningObj.multiLaborExceptionCount ? true :false;
            }
          } else{
            replacements.multi_labor_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("scheduled_by")){
            if(parsedBody.warningObj.scheduled_by){
              replacements.scheduled_by_exist_flag = parsedBody.warningObj.scheduled_by ? true :false;
            }
          } else{
            replacements.scheduled_by_exist_flag = false;

          }

          if(parsedBody.warningObj.hasOwnProperty("testData")){
            if(parsedBody.warningObj.testData){
              replacements.test_data_flag = parsedBody.warningObj.testData ? true :false;
            }
          } else{
            replacements.test_data_flag = false;
          }


          if(parsedBody.warningObj.hasOwnProperty("new_line_type_count")){
            if(parsedBody.warningObj.new_line_type_count){
              replacements.new_line_type_exception_exist_flag = parsedBody.warningObj.new_line_type_count ? true :false;
            }
          } else{
            replacements.new_line_type_exception_exist_flag = false;
          }

          if(parsedBody.subject){
             
            if (parsedBody.subject.toLowerCase().includes("failed")) {
              replacements.isFailed = true;
            }else{
              replacements.isFailed = false;
            }

            
          }

          if(parsedBody.hasOwnProperty("dmsType")){
            if(parsedBody.dmsType){
              if(parsedBody.dmsType == 'DEALERTRACK'){
                replacements.dealertrack_identify_flag = true;
              } else{
                replacements.dealertrack_identify_flag = false;
              }
            } else{
              replacements.dealertrack_identify_flag = false;
            }
          } else{
            replacements.dealertrack_identify_flag = false;
          }
          
          

          segment.saveSegment(`Send Mail: Sharepoint: replacement obj: ${JSON.stringify(replacements)}`);
          console.log(`Send Mail: Sharepoint: replacement obj: ${JSON.stringify(replacements)}`);

          sendNotification = true;
        } else {
          sendNotification = false;
        }
        segment.saveSegment(`Send Mail: Sharepoint: notification status: ${sendNotification}`);
        console.log(`Send Mail: Sharepoint: notification status: ${sendNotification}`);

      } else {
        sendNotification = true;
        var replacements = {
          dms_type: parsedBody.dmsType,
          process_types: parsedBody.processTypes,
          display_message: parsedBody.message,
          warning_message: parsedBody.warningObj.hasOwnProperty("errorwarningMessage") ?  parsedBody.warningObj.errorwarningMessage : '',
          closed_rodetail_warning_message: parsedBody.warningObj.hasOwnProperty("closedRODetailErrorwarningMessage") ? parsedBody.warningObj.closedRODetailErrorwarningMessage : '',
          vehicle_warning_message: parsedBody.warningObj.hasOwnProperty("vehicleErrorwarningMessage") ? parsedBody.warningObj.vehicleErrorwarningMessage : '',
          customer_warning_message: parsedBody.warningObj.hasOwnProperty("customerErrorwarningMessage") ? parsedBody.warningObj.customerErrorwarningMessage : '',
          gldetail_warning_message: parsedBody.warningObj.hasOwnProperty("glDeatilErrorwarningMessage") ? parsedBody.warningObj.glDeatilErrorwarningMessage : '',
          coupon_and_discount_warning_message: parsedBody.warningObj.hasOwnProperty("couponAndDiscountWarningMessage") ? parsedBody.warningObj.couponAndDiscountWarningMessage : '',
          ro_account_description_warning_message: parsedBody.warningObj.hasOwnProperty("roAccountDescriptionWarningMessage") ? parsedBody.warningObj.roAccountDescriptionWarningMessage : '',
          // coupon_and_discount_file_not_uploaded_warning_message: parsedBody.warningObj.hasOwnProperty("couponAndDiscountFileNotUploadedWarningMessage") ? parsedBody.warningObj.couponAndDiscountFileNotUploadedWarningMessage : '',
          skip_error_count: parsedBody.warningObj.hasOwnProperty("skipErrorCount") ? parsedBody.warningObj.skipErrorCount : '',
          core_return_exception_count: parsedBody.warningObj.hasOwnProperty("coreReturnExceptionCount") ? parsedBody.warningObj.coreReturnExceptionCount : '',
          core_charge_exception_count: parsedBody.warningObj.hasOwnProperty("coreChargeExceptionCount") ? parsedBody.warningObj.coreChargeExceptionCount : '',
          core_return_not_equal_core_charge_exception_count: parsedBody.warningObj.hasOwnProperty("coreReturnNotEqualCoreChargeExceptionCount") ? parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionCount : '',
          core_charge_with_no_sale_count: parsedBody.warningObj.hasOwnProperty("coreChargeWithNoSaleCount") ? parsedBody.warningObj.coreChargeWithNoSaleCount : '',
          resume_user_message: parsedBody.hasOwnProperty('resumeUser') ?  parsedBody.resumeUser : '',
          dealer_not_subscribed_message: parsedBody.warningObj.hasOwnProperty("dealerNotSubscribedMessage") ?  parsedBody.warningObj.dealerNotSubscribedMessage : '',
          invalid_core_cost_sale_mismatch_count: parsedBody.warningObj.hasOwnProperty("invalidCoreCostSaleMismatchCount") ? parsedBody.warningObj.invalidCoreCostSaleMismatchCount : '',
          invalid_misc_paytype_count: parsedBody.warningObj.hasOwnProperty("invalidmiscpaytypeCount") ? parsedBody.warningObj.invalidmiscpaytypeCount : '',
          punch_time_missing_count: parsedBody.warningObj.hasOwnProperty("punchTimeMissingCount") ? parsedBody.warningObj.punchTimeMissingCount : '',
          inventory_ro_count: parsedBody.warningObj.hasOwnProperty("inventory_ro_count") ? parsedBody.warningObj.inventory_ro_count : '',
          invalid_core_amount_mismatch_count: parsedBody.warningObj.hasOwnProperty("invalidCoreAmountMismatchCount") ? parsedBody.warningObj.invalidCoreAmountMismatchCount : '',
          coa_exception_warning_message: parsedBody.warningObj.hasOwnProperty("coaExceptionWarningMessage") ? parsedBody.warningObj.coaExceptionWarningMessage : '',
          customer_exception_warning_message: parsedBody.warningObj.hasOwnProperty("customerExceptionWarningMessage") ? parsedBody.warningObj.customerExceptionWarningMessage : '',
          misc_exception_count: parsedBody.warningObj.hasOwnProperty("miscExceptionCount") ? parsedBody.warningObj.miscExceptionCount : '',
          gl_ro_not_found_count: parsedBody.warningObj.hasOwnProperty("gl_missing_ro_count") ? parsedBody.warningObj.gl_missing_ro_count : '',
          invoice_missing_count: parsedBody.warningObj.hasOwnProperty("invoice_missing_count") ? parsedBody.warningObj.invoice_missing_count : '',
          suffixed_invoices_count: parsedBody.warningObj.hasOwnProperty("suffixedInvoicesCount") ? parsedBody.warningObj.suffixedInvoicesCount : '',
          company_no_not_matching_count: parsedBody.warningObj.hasOwnProperty("company_no_not_matching_count") ? parsedBody.warningObj.company_no_not_matching_count : '',
          grouped_team_work_count: parsedBody.warningObj.hasOwnProperty("grouped_team_work_count") ? parsedBody.warningObj.grouped_team_work_count : '',
          dealerAddress: parsedBody.warningObj.hasOwnProperty("dealerAddress") ? parsedBody.warningObj.dealerAddress.replace(/~/g,', ') : '',
          split_job_exception_count: parsedBody.warningObj.hasOwnProperty("splitJobExceptionCount") ? parsedBody.warningObj.splitJobExceptionCount : '',
          new_line_type_count: parsedBody.warningObj.hasOwnProperty("new_line_type_count") ? parsedBody.warningObj.new_line_type_count : '',
          chart_of_accounts_file_path: parsedBody.warningObj.hasOwnProperty("chart_of_accounts_file_path") ? parsedBody.warningObj.chart_of_accounts_file_path: '',
          exception_closed_invoices_count: parsedBody.warningObj.hasOwnProperty("exceptionClosedInvoicesCount") ? parsedBody.warningObj.exceptionClosedInvoicesCount : '',
          extra_ro_in_xml_exception_count: parsedBody.warningObj.hasOwnProperty("extraRoInXmlExceptionCount") ? parsedBody.warningObj.extraRoInXmlExceptionCount : '',
          im_Opened_Closed_Rci_Ros_Count: parsedBody.warningObj.hasOwnProperty("imOpenedClosedRciRosCount") ? parsedBody.warningObj.imOpenedClosedRciRosCount : '',
          Sale_Zero_Cost_NonZero_Parts_Count: parsedBody.warningObj.hasOwnProperty("SaleZeroCostNonZeroPartsCount") ? parsedBody.warningObj.SaleZeroCostNonZeroPartsCount : '',
          gl_Deatil_Extraction_Error_Count: parsedBody.warningObj.hasOwnProperty("glDeatilExtractionErrorCount") ? parsedBody.warningObj.glDeatilExtractionErrorCount : '',
          less_special_discount_count: parsedBody.warningObj.hasOwnProperty("lessSpecialDiscountCount") ? parsedBody.warningObj.lessSpecialDiscountCount : '', 
          negative_coupon_count: parsedBody.warningObj.hasOwnProperty("negative_coupon_count") ? parsedBody.warningObj.negative_coupon_count : '',
          labor_with_zero_sale_nonzero_cost_count: parsedBody.warningObj.hasOwnProperty("labor_with_zero_sale_nonzero_cost_count") ? parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_count : '',
          gl_missing_ros_count: parsedBody.warningObj.hasOwnProperty("gl_missing_ros_count") ? parsedBody.warningObj.gl_missing_ros_count : '',
          part_description_exception_count: parsedBody.warningObj.hasOwnProperty("partDescriptionExceptionCount") ? parsedBody.warningObj.partDescriptionExceptionCount : '',
          coupon_discount_basis_amount_mismatch_exception_count: parsedBody.warningObj.hasOwnProperty("coupon_discount_basis_amount_mismatch_exception_count") ? parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_count : '',
          labor_with_no_paytype_exception_count: parsedBody.warningObj.hasOwnProperty("labor_with_no_paytype_exception_count") ? parsedBody.warningObj.labor_with_no_paytype_exception_count : '', 
          parts_excluded_from_history_exception_count:parsedBody.warningObj.hasOwnProperty("parts_excluded_from_history_exception_count") ? parsedBody.warningObj.parts_excluded_from_history_exception_count : '',
          lost_sale_parts_exception_count:parsedBody.warningObj.hasOwnProperty("lost_sale_parts_exception_count") ? parsedBody.warningObj.lost_sale_parts_exception_count : '',
          part_details_null_exception_count: parsedBody.warningObj.hasOwnProperty("partDetailsNullExceptionCount") ? parsedBody.warningObj.partDetailsNullExceptionCount : '',
          multiLaborExceptionCount: parsedBody.warningObj.hasOwnProperty("multiLaborExceptionCount") ? parsedBody.warningObj.multiLaborExceptionCount : '',
          scheduled_by:parsedBody.warningObj.hasOwnProperty("scheduled_by") ? parsedBody.warningObj.scheduled_by : '',
        };
          if(parsedBody.hasOwnProperty('resumeUser')){
            if(parsedBody.resumeUser){
             replacements.resume_user_message_exist_flag = parsedBody.resumeUser.length > 0 ? true :false;
            }
          }

          if(parsedBody.warningObj.hasOwnProperty("errorwarningMessage")){
            if(parsedBody.warningObj.errorwarningMessage){
              replacements.warning_message_exist_flag =parsedBody.warningObj.errorwarningMessage.length >0 ? true :false;
            }
          } else{
            replacements.warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("dealerNotSubscribedMessage")){
            if(parsedBody.warningObj.dealerNotSubscribedMessage){
              replacements.dealer_not_subscribed_message_exist_flag =parsedBody.warningObj.dealerNotSubscribedMessage.length > 0 ? true :false;
            }
          } else{
            replacements.dealer_not_subscribed_message_exist_flag = false;
          }
          
          if(parsedBody.warningObj.hasOwnProperty("closedRODetailErrorwarningMessage")){
            if(parsedBody.warningObj.closedRODetailErrorwarningMessage){
              replacements.closed_rodetail_warning_message_exist_flag = parsedBody.warningObj.closedRODetailErrorwarningMessage.length >0 ? true :false;
            }
          } else{
            replacements.closed_rodetail_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("vehicleErrorwarningMessage")){
            if( parsedBody.warningObj.vehicleErrorwarningMessage){
              replacements.vehicle_warning_message_exist_flag =  parsedBody.warningObj.vehicleErrorwarningMessage.length >0 ? true :false;
            }
          } else{
            replacements.vehicle_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("customerErrorwarningMessage")){
            if( parsedBody.warningObj.customerErrorwarningMessage){
              replacements.customer_warning_message_exist_flag =  parsedBody.warningObj.customerErrorwarningMessage.length >0 ? true :false;
            }
          } else{
            replacements.customer_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("glDeatilErrorwarningMessage")){
            if( parsedBody.warningObj.glDeatilErrorwarningMessage){
              replacements.gldetail_warning_message_exist_flag =  parsedBody.warningObj.glDeatilErrorwarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.gldetail_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("couponAndDiscountWarningMessage")){
            if( parsedBody.warningObj.couponAndDiscountWarningMessage){
              replacements.coupon_and_discount_message_exist_flag =  parsedBody.warningObj.couponAndDiscountWarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.coupon_and_discount_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("roAccountDescriptionWarningMessage")){
            if( parsedBody.warningObj.roAccountDescriptionWarningMessage){
              replacements.ro_account_description_message_exist_flag =  parsedBody.warningObj.roAccountDescriptionWarningMessage > 0 ? true :false;
            }
          } else{
            replacements.ro_account_description_message_exist_flag = false;
          }
  
          if(parsedBody.warningObj.hasOwnProperty("coaExceptionWarningMessage")){
            if( parsedBody.warningObj.coaExceptionWarningMessage){
              replacements. coa_exception_warning_message_exist_flag =  parsedBody.warningObj.coaExceptionWarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.coa_exception_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("customerExceptionWarningMessage")){
            if( parsedBody.warningObj.customerExceptionWarningMessage){
              replacements. customer_exception_warning_message_exist_flag =  parsedBody.warningObj.customerExceptionWarningMessage.length > 0 ? true :false;
            }
          } else{
            replacements.customer_exception_warning_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("skipErrorCount")){
            if( parsedBody.warningObj.skipErrorCount){
              replacements.skip_error_count_message_exist_flag =  parsedBody.warningObj.skipErrorCount.length > 0 ? true :false;
            }
          } else{
            replacements.skip_error_count_message_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("coreReturnExceptionCount")){
            if(parsedBody.warningObj.coreReturnExceptionCount){
              replacements.core_return_exception_exist_flag = parsedBody.warningObj.coreReturnExceptionCount ? true :false;
            }
          } else{
            replacements.core_return_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("coreChargeExceptionCount")){
            if(parsedBody.warningObj.coreChargeExceptionCount){
              replacements.core_charge_exception_exist_flag = parsedBody.warningObj.coreChargeExceptionCount  ? true :false;
            }
          } else{
            replacements.core_charge_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("coreReturnNotEqualCoreChargeExceptionCount")){
            if(parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionCount){
              replacements.core_return_not_equal_to_core_charge_exception_exist_flag =  parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionCount ? true :false;
            }
          } else{
            replacements.core_return_not_equal_to_core_charge_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("coreChargeWithNoSaleCount")){
            if(parsedBody.warningObj.coreChargeWithNoSaleCount){
              replacements.core_charge_with_no_sale_exception_exist_flag =  parsedBody.warningObj.coreChargeWithNoSaleCount ? true :false;
            }
          } else{
            replacements.core_charge_with_no_sale_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("gl_missing_ro_count")){
            if(parsedBody.warningObj.gl_missing_ro_count){
              replacements.gl_ro_not_found_exception_exist_flag =  parsedBody.warningObj.gl_missing_ro_count ? true :false;
            }
          } else{
            replacements.gl_ro_not_found_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("invoice_missing_count")){
            if(parsedBody.warningObj.invoice_missing_count){
              replacements.invoice_missing_exception_exist_flag =  parsedBody.warningObj.invoice_missing_count ? true :false;
            }
          } else{
            replacements.invoice_missing_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("invalidCoreCostSaleMismatchCount")){
            if(parsedBody.warningObj.invalidCoreCostSaleMismatchCount){
              replacements.invalid_core_cost_sale_mismatch_exist_flag =  parsedBody.warningObj.invalidCoreCostSaleMismatchCount ? true :false;
            }
          } else{
            replacements.invalid_core_cost_sale_mismatch_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("invalidmiscpaytypeCount")){
            if(parsedBody.warningObj.invalidmiscpaytypeCount){
              replacements.invalid_misc_paytype_flag =  parsedBody.warningObj.invalidmiscpaytypeCount ? true :false;
            }
          } else{
            replacements.invalid_misc_paytype_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("exceptionClosedInvoicesCount")){
            if(parsedBody.warningObj.exceptionClosedInvoicesCount){
              replacements.exception_closed_invoices_flag =  parsedBody.warningObj.exceptionClosedInvoicesCount ? true :false;
            }
          } else{
            replacements.exception_closed_invoices_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("extraRoInXmlExceptionCount")){
            if(parsedBody.warningObj.extraRoInXmlExceptionCount){
              replacements.extra_ro_in_xml_exception_flag =  parsedBody.warningObj.extraRoInXmlExceptionCount ? true :false;
            }
          } else{
            replacements.extra_ro_in_xml_exception_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("imOpenedClosedRciRosCount")){
            if(parsedBody.warningObj.imOpenedClosedRciRosCount){
              replacements.im_opened_closed_rci_ros_flag =  parsedBody.warningObj.imOpenedClosedRciRosCount ? true :false;
            }
          } else{
            replacements.im_opened_closed_rci_ros_flag = false;
          }


          if(parsedBody.warningObj.hasOwnProperty("SaleZeroCostNonZeroPartsCount")){
            if(parsedBody.warningObj.SaleZeroCostNonZeroPartsCount){
              replacements.Sale_Zero_Cost_NonZero_Parts_flag =  parsedBody.warningObj.SaleZeroCostNonZeroPartsCount ? true :false;
            }
          } else{
            replacements.Sale_Zero_Cost_NonZero_Parts_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("glDeatilExtractionErrorCount")){
            if(parsedBody.warningObj.glDeatilExtractionErrorCount){
              replacements.gl_Deatil_Extraction_Error_Count_flag =  parsedBody.warningObj.glDeatilExtractionErrorCount ? true :false;
            }
          } else{
            replacements.gl_Deatil_Extraction_Error_Count_flag = false;
          }




          if(parsedBody.warningObj.hasOwnProperty("splitJobExceptionCount")){
            if(parsedBody.warningObj.splitJobExceptionCount){
              replacements.split_job_exception_flag =  parsedBody.warningObj.splitJobExceptionCount ? true :false;
            }
          } else{
            replacements.split_job_exception_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("lessSpecialDiscountCount")){
            if(parsedBody.warningObj.lessSpecialDiscountCount){
              replacements.less_special_discount_exception_flag =  parsedBody.warningObj.lessSpecialDiscountCount ? true :false;
            }
          } else{
            replacements.less_special_discount_exception_flag  = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("negative_coupon_count")){
            if(parsedBody.warningObj.negative_coupon_count){
              replacements.negative_coupon_exception_flag =  parsedBody.warningObj.negative_coupon_count ? true :false;
            }
          } else{
            replacements.negative_coupon_exception_flag  = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("gl_missing_ros_count")){
            if(parsedBody.warningObj.gl_missing_ros_count){
              replacements.gl_missing_ros_exception_flag =  parsedBody.warningObj.gl_missing_ros_count ? true :false;
              }
             else{
                replacements.gl_missing_ros_exception_flag  = false;
              }
          }

          if(parsedBody.warningObj.hasOwnProperty("partDescriptionExceptionCount")){
            if(parsedBody.warningObj.partDescriptionExceptionCount){
              replacements.part_description_exception_flag =  parsedBody.warningObj.partDescriptionExceptionCount ? true :false;
              }
             else{
                replacements.part_description_exception_flag  = false;
              }
          }



        if(parsedBody.warningObj.hasOwnProperty("coupon_discount_basis_amount_mismatch_exception_count")){
          if(parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_count >0){
            replacements.coupon_discount_basis_amount_mismatch_exception_flag =  parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_count ? true :false;
            }
           else{
              replacements.coupon_discount_basis_amount_mismatch_exception_flag  = false;
            }
        }

        if(parsedBody.warningObj.hasOwnProperty("labor_with_no_paytype_exception_count")){
          if(parsedBody.warningObj.labor_with_no_paytype_exception_count >0){
            replacements.labor_with_no_paytype_exception_flag =  parsedBody.warningObj.labor_with_no_paytype_exception_count ? true :false;
            }
           else{
              replacements.labor_with_no_paytype_exception_flag  = false;
            }
        }

        if(parsedBody.warningObj.hasOwnProperty("parts_excluded_from_history_exception_count")){
          if(parsedBody.warningObj.parts_excluded_from_history_exception_count >0){
            replacements.parts_excluded_from_history_exception_flag =  parsedBody.warningObj.parts_excluded_from_history_exception_count ? true :false;
            }
           else{
              replacements.parts_excluded_from_history_exception_flag  = false;
            }
        }

        if(parsedBody.warningObj.hasOwnProperty("lost_sale_parts_exception_count")){
          if(parsedBody.warningObj.lost_sale_parts_exception_count >0){
            replacements.lost_sale_parts_exception_flag =  parsedBody.warningObj.lost_sale_parts_exception_count ? true :false;
            }
           else{
              replacements.lost_sale_parts_exception_flag  = false;
            }
        }


             
        if(parsedBody.warningObj.hasOwnProperty("partDetailsNullExceptionCount")){
          if(parsedBody.warningObj.partDetailsNullExceptionCount >0){
            replacements.part_details_null_exception_flag =  parsedBody.warningObj.partDetailsNullExceptionCount ? true :false;
            }
           else{
              replacements.part_details_null_exception_flag  = false;
            }
        }

              
        if(parsedBody.warningObj.hasOwnProperty("partDetailsNullExceptionCount")){
          if(parsedBody.warningObj.partDetailsNullExceptionCount >0){
            replacements.part_details_null_exception_flag =  parsedBody.warningObj.partDetailsNullExceptionCount ? true :false;
            }
           else{
              replacements.part_details_null_exception_flag  = false;
            }
        }
    
               
          if(parsedBody.warningObj.hasOwnProperty("labor_with_zero_sale_nonzero_cost_count")){
            if(parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_count){
            replacements.labor_with_zero_sale_nonzero_cost_exception_flag =  parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_count ? true :false;
            }
          } else{
          replacements.labor_with_zero_sale_nonzero_cost_exception_flag  = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("punchTimeMissingCount")){
            if(parsedBody.warningObj.punchTimeMissingCount>40){
              replacements.punch_time_missing_flag =  parsedBody.warningObj.punchTimeMissingCount ? true :false;
            }
          } else{
            replacements.punch_time_missing_flag = false;
          }

          

          if(parsedBody.warningObj.hasOwnProperty("suffixedInvoicesCount")){
            if(parsedBody.warningObj.suffixedInvoicesCount>40){
              replacements.suffixed_invoices_flag =  parsedBody.warningObj.suffixedInvoicesCount ? true :false;
            }
          } else{
            replacements.suffixed_invoices_flag = false;
          }


         
          if(parsedBody.warningObj.hasOwnProperty("company_no_not_matching_count")){
            if(parsedBody.warningObj.company_no_not_matching_count >0){
              replacements.company_no_not_matching_flag =  parsedBody.warningObj.company_no_not_matching_count ? true :false;
            }
          } else{
            replacements.company_no_not_matching_flag = false;
          }


            
          if(parsedBody.warningObj.hasOwnProperty("grouped_team_work_count")){
            if(parsedBody.warningObj.grouped_team_work_count >0){
              replacements.grouped_team_work_flag=  parsedBody.warningObj.grouped_team_work_count? true :false;
            }
          } else{
            replacements.grouped_team_work_flag = false;
          }

          
          if(parsedBody.warningObj.hasOwnProperty("chart_of_accounts_file_path")){
            if( parsedBody.warningObj.chart_of_accounts_file_path){
              replacements.chart_of_accounts_file_path_exist_flag =  true
            }
          } else{
            replacements.chart_of_accounts_file_path_exist_flag = false;
          }





          if(parsedBody.warningObj.hasOwnProperty("inventory_ro_count")){
            if(parsedBody.warningObj.inventory_ro_count>0){
              replacements.inventory_ro_flag=  parsedBody.warningObj.inventory_ro_count ? true :false;
            }
          } else{
            replacements.inventory_ro_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("invalidCoreAmountMismatchCount")){
            if(parsedBody.warningObj.invalidCoreAmountMismatchCount){
              replacements.invalid_core_amount_mismatch_exist_flag =  parsedBody.warningObj.invalidCoreAmountMismatchCount ? true :false;
            }
          } else{
            replacements.invalid_core_amount_mismatch_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("miscExceptionCount")){
            if(parsedBody.warningObj.miscExceptionCount){
              replacements.misc_exception_exist_flag = parsedBody.warningObj.miscExceptionCount ? true :false;
            }
          } else{
            replacements.misc_exception_exist_flag = false;
          }
          
          if(parsedBody.warningObj.hasOwnProperty("multiLaborExceptionCount")){
            if(parsedBody.warningObj.multiLaborExceptionCount){
              replacements.multi_labor_exception_exist_flag = parsedBody.warningObj.multiLaborExceptionCount ? true :false;
            }
          } else{
            replacements.multi_labor_exception_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("scheduled_by")){
            if(parsedBody.warningObj.scheduled_by){
              replacements.scheduled_by_exist_flag = parsedBody.warningObj.scheduled_by ? true :false;
            }
          } else{
            replacements.scheduled_by_exist_flag = false;
          }

          if(parsedBody.warningObj.hasOwnProperty("testData")){
            if(parsedBody.warningObj.testData){
              replacements.test_data_flag = parsedBody.warningObj.testData ? true :false;
            }
          } else{
            replacements.test_data_flag = false;
          }
          

          if(parsedBody.warningObj.hasOwnProperty("new_line_type_count")){
            if(parsedBody.warningObj.new_line_type_count){
              replacements.new_line_type_exception_exist_flag = parsedBody.warningObj.new_line_type_count ? true :false;
            }
          } else{
            replacements.new_line_type_exception_exist_flag = false;
          }

          if (parsedBody.subject.toLowerCase().includes("failed")) {
            replacements.isFailed = true;
          }else{
            replacements.isFailed = false;
          }

          console.log('replacements.core_return_exception_exist_flag:', replacements.core_return_exception_exist_flag);
          console.log('replacements.core_charge_exception_exist_flag:',  replacements.core_charge_exception_exist_flag);
          console.log('replacements.core_return_not_equal_to_core_charge_exception_exist_flag:',  replacements.core_return_not_equal_to_core_charge_exception_exist_flag);
          console.log('replacements.core_charge_with_no_sale_exception_exist_flag:',  replacements.core_charge_with_no_sale_exception_exist_flag);
          console.log('replacements.invalid_core_cost_sale_mismatch_exist_flag:',  replacements.invalid_core_cost_sale_mismatch_exist_flag);
          console.log('replacements.invalid_core_amount_mismatch_exist_flag:',  replacements.invalid_core_amount_mismatch_exist_flag);
          console.log('replacements.misc_exception_exist_flag:',  replacements.misc_exception_exist_flag);
          console.log('replacements.isFailed:',  replacements.isFailed);
          console.log('paytypeHalt_exist_flag:',  replacements.paytypeHalt_exist_flag);
          console.log('invSequenceHalt_exist_flag:',  replacements.invSequenceHalt_exist_flag);
          console.log('makeHalt_exist_flag:',  replacements.makeHalt_exist_flag);
          console.log('departmentHalt_exist_flag:',  replacements.departmentHalt_exist_flag);
          if(parsedBody.hasOwnProperty("dmsType")){
            if(parsedBody.dmsType){
              if(parsedBody.dmsType == 'DEALERTRACK'){
                replacements.dealertrack_identify_flag = true;
              } else{
                replacements.dealertrack_identify_flag = false;
              }
            } else{
              replacements.dealertrack_identify_flag = false;
            }
          } else{
            replacements.dealertrack_identify_flag = false;
          }

      }
      var htmlToSend = template(replacements);
      var subject = (type === appConstants.NOTIFICATION_TYPES.SHARE_POINT) ? `Sharepoint file upload status for ${groupCode} - ${storeCode} ${mailSubject}` : parsedBody.subject ? dmsType+' '+parsedBody.subject : '';
      var logAttachedFilePath = reqBdy.attachedfailurelogFile;
      if (uploadStatus == false) {
        subject = "Test Support : " + subject;
      }
 const data = {
        from: reqBdy.fromAddress,
        to: reqBdy.toAddress.join(),
        subject: subject,
        html: htmlToSend,
        attachment: ''
      };
      let coreReturnExceptionCsvFile,
      coreChargeExceptionCsvFile,
      coreReturnNotEqualCoreChargeExceptionFile,
      coreChargeWithNoSaleCsvFile,
      roAccountingDescCsvFile,
      invalidCoreCostSaleMismatchCsvFile,
      invalidCoreAmountMismatchCsvFile,
      invalidCoreCostSaleMismatchCsvFile1,
      miscExceptionCsvFile,
      missingInvoiceFilePath;
      let attachmentFileList = [];

      try {
        if (data.hasOwnProperty("subject")) {
          if (data.subject.toLowerCase().includes("failed")) {
            if (logAttachedFilePath) {
              let attached_file = fs.readFileSync(logAttachedFilePath);
              if (fs.existsSync(logAttachedFilePath)) {
                var n = logAttachedFilePath.lastIndexOf("/");
                var log_file = logAttachedFilePath.substring(n + 1);
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: attached_file,
                    contentType: "text",
                    filename: log_file,
                  })
                );
               
              }
            }
         if (
              parsedBody.warningObj.hasOwnProperty("exceptionClosedInvoicesFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.exceptionClosedInvoicesFilePath)
              ) {
                exceptionClosedInvoicesFile= fs.readFileSync(
                  parsedBody.warningObj.exceptionClosedInvoicesFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: exceptionClosedInvoicesFile,
                    contentType: "csv",
                    filename: "exception-closed-invoices.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("extraRoInXmlExceptionFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.extraRoInXmlExceptionFilePath)
              ) {
                extraRoInXmlExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.extraRoInXmlExceptionFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: extraRoInXmlExceptionFile,
                    contentType: "csv",
                    filename: "extraros_in_xml.csv",
                  })
                );
              }
            }


            if (
              parsedBody.warningObj.hasOwnProperty("imOpenedClosedRciRosFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.imOpenedClosedRciRosFilePath)
              ) {
                imOpenedClosedRciRosFile= fs.readFileSync(
                  parsedBody.warningObj.imOpenedClosedRciRosFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: imOpenedClosedRciRosFile,
                    contentType: "csv",
                    filename: "im_opened_closed_rci_ros.csv",
                  })
                );
              }
            }
            
            if (
              parsedBody.warningObj.hasOwnProperty("coupon_and_discount_exception_filepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.coupon_and_discount_exception_filepath)
              ) {
                couponAndDiscountExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.coupon_and_discount_exception_filepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: couponAndDiscountExceptionFile,
                    contentType: "csv",
                    filename: "coupon_and_discount_exception.csv",
                  })
                );
              }
            }


            if (
              parsedBody.warningObj.hasOwnProperty("deletedRosFilepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.deletedRosFilepath)
              ) {
                deletedRosFile= fs.readFileSync(
                  parsedBody.warningObj.deletedRosFilepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: deletedRosFile,
                    contentType: "csv",
                    filename: "removed_ros.csv",
                  })
                );
              }
            }

            data.attachment = attachmentFileList;



            

          } else if (data.subject.toLowerCase().includes("halted") || data.subject.toLowerCase().includes("held")) {
            if (
              parsedBody.warningObj.hasOwnProperty("roAccountingDescCsvFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.roAccountingDescCsvFilePath)
              ) {
                  roAccountingDescCsvFile = fs.readFileSync(
                  parsedBody.warningObj.roAccountingDescCsvFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: roAccountingDescCsvFile,
                    contentType: "csv",
                    filename: "ro_accounting_desc_exception.csv",
                  })
                );
              }
            }
            
            if (
              parsedBody.warningObj.hasOwnProperty("coreReturnExceptionCsvFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.coreReturnExceptionCsvFilePath)
              ) {
                coreReturnExceptionCsvFile = fs.readFileSync(
                  parsedBody.warningObj.coreReturnExceptionCsvFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: coreReturnExceptionCsvFile,
                    contentType: "csv",
                    filename: "corereturn_without_corecharge.csv",
                  })
                );
              }
            }



            if (
              parsedBody.warningObj.hasOwnProperty("new_line_type_file_path")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.new_line_type_file_path)
              ) {
                newLineTypeExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.new_line_type_file_path
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: newLineTypeExceptionFile,
                    contentType: "csv",
                    filename: "new-line-type.csv",
                  })
                );
              }
            }


            if (
              parsedBody.warningObj.hasOwnProperty("splitJobExceptionFilepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.splitJobExceptionFilepath)
              ) {
                splitJobExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.splitJobExceptionFilepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: splitJobExceptionFile,
                    contentType: "csv",
                    filename: "split_job_exception.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("lessSpecialDiscountFilepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.lessSpecialDiscountFilepath)
              ) {
                lessSpecialDiscountFile= fs.readFileSync(
                  parsedBody.warningObj.lessSpecialDiscountFilepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: lessSpecialDiscountFile,
                    contentType: "csv",
                    filename: "less_special_discount_data.csv",
                  })
                );
              }
            }



            if (
              parsedBody.warningObj.hasOwnProperty("exceptionClosedInvoicesFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.exceptionClosedInvoicesFilePath)
              ) {
                exceptionClosedInvoicesFile= fs.readFileSync(
                  parsedBody.warningObj.exceptionClosedInvoicesFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: exceptionClosedInvoicesFile,
                    contentType: "csv",
                    filename: "exception-closed-invoices.csv",
                  })
                );
              }
            }


            if (
              parsedBody.warningObj.hasOwnProperty("extraRoInXmlExceptionFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.extraRoInXmlExceptionFilePath)
              ) {
                extraRoInXmlExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.extraRoInXmlExceptionFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: extraRoInXmlExceptionFile,
                    contentType: "csv",
                    filename: "extraros_in_xml.csv",
                  })
                );
              }
            }



            if (
              parsedBody.warningObj.hasOwnProperty("gl_missing_ros_exception_filepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.gl_missing_ros_exception_filepath)
              ) {
                glMissingExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.gl_missing_ros_exception_filepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: glMissingExceptionFile,
                    contentType: "csv",
                    filename: "gl_missing_ros.csv",
                  })
                );
              }
            }


            if (
              parsedBody.warningObj.hasOwnProperty("partDescriptionExceptionFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.partDescriptionExceptionFilePath)
              ) {
                partDescriptionExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.partDescriptionExceptionFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: partDescriptionExceptionFile,
                    contentType: "csv",
                    filename: "part_description_exception.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("lost_sale_parts_exception_filepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.lost_sale_parts_exception_filepath)
              ) {
                lostSalePartsExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.lost_sale_parts_exception_filepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: lostSalePartsExceptionFile,
                    contentType: "csv",
                    filename: "lost_sale_parts.csv",
                  })
                );
              }
            }
      
            if (
              parsedBody.warningObj.hasOwnProperty("coreChargeExceptionCsvFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.coreChargeExceptionCsvFilePath)
              ) {
                coreChargeExceptionCsvFile = fs.readFileSync(
                  parsedBody.warningObj.coreChargeExceptionCsvFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: coreChargeExceptionCsvFile,
                    contentType: "csv",
                    filename: "corecharge_without_corereturn.csv",
                  })
                );
              }
            }
      
            if (
              parsedBody.warningObj.hasOwnProperty(
                "coreReturnNotEqualCoreChargeExceptionFilePath"
              )
            ) {
              if (
                fs.existsSync(
                  parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionFilePath
                )
              ) {
                coreReturnNotEqualCoreChargeExceptionFile = fs.readFileSync(
                  parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: coreReturnNotEqualCoreChargeExceptionFile,
                    contentType: "csv",
                    filename: "corecharge_corereturn_mismatch.csv",
                  })
                );
              }
            }
      
            if (
              parsedBody.warningObj.hasOwnProperty("coreChargeWithNoSaleCsvFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.coreChargeWithNoSaleCsvFilePath)
              ) {
                coreChargeWithNoSaleCsvFile = fs.readFileSync(
                  parsedBody.warningObj.coreChargeWithNoSaleCsvFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: coreChargeWithNoSaleCsvFile,
                    contentType: "csv",
                    filename: "corecharge_with_nosale.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("SaleZeroCostNonZeroPartsFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.SaleZeroCostNonZeroPartsFilePath)
              ) {
                SaleZeroCostNonZeroPartsFile = fs.readFileSync(
                  parsedBody.warningObj.SaleZeroCostNonZeroPartsFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: SaleZeroCostNonZeroPartsFile,
                    contentType: "csv",
                    filename: "sale_zero_cost_nonzero_parts.csv",
                  })
                );
              }
            }


            if (
              parsedBody.warningObj.hasOwnProperty("invalidCoreCostSaleMismatchFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.invalidCoreCostSaleMismatchFilePath)
              ) {
                invalidCoreCostSaleMismatchCsvFile = fs.readFileSync(
                  parsedBody.warningObj.invalidCoreCostSaleMismatchFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: invalidCoreCostSaleMismatchCsvFile,
                    contentType: "csv",
                    filename: "invalid_core_cost_sale_mismatch.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("invalidCoreAmountMismatchFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.invalidCoreAmountMismatchFilePath)
              ) {
                invalidCoreAmountMismatchCsvFile = fs.readFileSync(
                  parsedBody.warningObj.invalidCoreAmountMismatchFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: invalidCoreAmountMismatchCsvFile,
                    contentType: "csv",
                    filename: "invalid_core_amount_mismatch.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("company_no_not_matching_file_path")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.company_no_not_matching_file_path)
              ) {
                company_no_not_matching_file= fs.readFileSync(
                  parsedBody.warningObj.company_no_not_matching_file_path
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: company_no_not_matching_file,
                    contentType: "csv",
                    filename: "company_no_not_matching_count.csv",
                  })
                );
              }
            }

        if (
              parsedBody.warningObj.hasOwnProperty("grouped_team_work_file_path")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.grouped_team_work_file_path)
              ) {
                grouped_team_work_file= fs.readFileSync(
                  parsedBody.warningObj.grouped_team_work_file_path
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: grouped_team_work_file,
                    contentType: "csv",
                    filename: "grouped-team-work.csv",
                  })
                );
              }
            }
        
          if (
              parsedBody.warningObj.hasOwnProperty("PUNCH_TIME_MISSING_FILEPATH")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.PUNCH_TIME_MISSING_FILEPATH)
              ) {
                punch_time_missing= fs.readFileSync(
                  parsedBody.warningObj.PUNCH_TIME_MISSING_FILEPATH
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: punch_time_missing,
                    contentType: "csv",
                    filename: "punch_time_missing.csv",
                  })
                );
              }
            }

           if (
              parsedBody.warningObj.hasOwnProperty("suffixedInvoicesFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.suffixedInvoicesFilePath)
              ) {
                suffixedInvoicesFile= fs.readFileSync(
                  parsedBody.warningObj.suffixedInvoicesFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: suffixedInvoicesFile,
                    contentType: "csv",
                    filename: "suffixed-invoices.csv",
                  })
                );
              }
            }


         try{
              if (
                parsedBody.warningObj.hasOwnProperty("miscExceptionCsvFilePath")
              ) {
                if (
                  fs.existsSync(parsedBody.warningObj.miscExceptionCsvFilePath)
                ) {
                  miscExceptionCsvFile = fs.readFileSync(
                    parsedBody.warningObj.miscExceptionCsvFilePath
                  );
                  attachmentFileList.push(
                    new mailgun.Attachment({
                      data: miscExceptionCsvFile,
                      contentType: "csv",
                      filename: "misc-exception.csv",
                    })
                  );
                }
              }

              if (
                parsedBody.warningObj.hasOwnProperty("coupon_discount_basis_amount_mismatch_exception_filepath")
              ) {
                if (
                  fs.existsSync(parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_filepath)
                ) {
                  couponDiscountBasisAmountMismatchExceptionFile = fs.readFileSync(
                    parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_filepath
                  );
                  attachmentFileList.push(
                    new mailgun.Attachment({
                      data: couponDiscountBasisAmountMismatchExceptionFile,
                      contentType: "csv",
                      filename: "coupon_discount_basis_amount_mismatch_exception_filepath.csv",
                    })
                  );
                }
              }


              
            if (
              parsedBody.warningObj.hasOwnProperty("imOpenedClosedRciRosFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.imOpenedClosedRciRosFilePath)
              ) {
                imOpenedClosedRciRosFile= fs.readFileSync(
                  parsedBody.warningObj.imOpenedClosedRciRosFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: imOpenedClosedRciRosFile,
                    contentType: "csv",
                    filename: "im_opened_closed_rci_ros.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("labor_with_zero_sale_nonzero_cost_exception_filepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_exception_filepath)
              ) {
                laborWithZeroSaleNonZeroCostExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_exception_filepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: laborWithZeroSaleNonZeroCostExceptionFile,
                    contentType: "csv",
                    filename: "labor_with_zero_sale_nonzero_cost.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("deletedRosFilepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.deletedRosFilepath)
              ) {
                deletedRosFile= fs.readFileSync(
                  parsedBody.warningObj.deletedRosFilepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: deletedRosFile,
                    contentType: "csv",
                    filename: "removed_ros.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("coupon_and_discount_exception_filepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.coupon_and_discount_exception_filepath)
              ) {
                couponAndDiscountExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.coupon_and_discount_exception_filepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: couponAndDiscountExceptionFile,
                    contentType: "csv",
                    filename: "coupon_and_discount_exception.csv",
                  })
                );
              }
            }

            

            if (
              parsedBody.warningObj.hasOwnProperty("labor_with_no_paytype_exception_filepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.labor_with_no_paytype_exception_filepath)
              ) {
                laborWithNoPaytypeExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.labor_with_no_paytype_exception_filepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: laborWithNoPaytypeExceptionFile,
                    contentType: "csv",
                    filename: "labor_with_no_paytype.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("parts_excluded_from_history_exception_filepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.parts_excluded_from_history_exception_filepath)
              ) {
                partsExcludedFromHistoryExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.parts_excluded_from_history_exception_filepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: partsExcludedFromHistoryExceptionFile,
                    contentType: "csv",
                    filename: "parts_excluded_from_history.csv",
                  })
                );
              }
            }
        
            
            
            if (
              parsedBody.warningObj.hasOwnProperty("negative_coupon_exception_filepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.negative_coupon_exception_filepath)
              ) {
                negativeCouponExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.negative_coupon_exception_filepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: negativeCouponExceptionFile,
                    contentType: "csv",
                    filename: "negative_coupon.csv",
                  })
                );
              }
            }

               
            if (
              parsedBody.warningObj.hasOwnProperty("partDetailsNullExceptionFilepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.partDetailsNullExceptionFilepath)
              ) {
                partDetailsNullExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.partDetailsNullExceptionFilepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: partDetailsNullExceptionFile,
                    contentType: "csv",
                    filename: "part_details_null.csv",
                  })
                );
              }
            }

            } catch(err){
              console.log(err);
            }

            data.attachment = attachmentFileList;
            
          } 
          else if (data.subject.toLowerCase().includes("completed")){

            
           if (
              parsedBody.warningObj.hasOwnProperty("missingInvoiceFilePath")
            ) {
              
              if (
                fs.existsSync(parsedBody.warningObj.missingInvoiceFilePath)
              ) {
                
                missingInvoiceFilePath = fs.readFileSync(
                  parsedBody.warningObj.missingInvoiceFilePath
                );
               
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: missingInvoiceFilePath,
                    contentType: "csv",
                    filename: "missing-invoices.csv",
                  })
                );
              
              }
            }


            if (
              parsedBody.warningObj.hasOwnProperty("exceptionClosedInvoicesFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.exceptionClosedInvoicesFilePath)
              ) {
                exceptionClosedInvoicesFile= fs.readFileSync(
                  parsedBody.warningObj.exceptionClosedInvoicesFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: exceptionClosedInvoicesFile,
                    contentType: "csv",
                    filename: "exception-closed-invoices.csv",
                  })
                );
              }
            }

            data.attachment = attachmentFileList;

           if (parsedBody.warningObj.hasOwnProperty("inventory_ro_FilePath")) {
              
              if (fs.existsSync(parsedBody.warningObj.inventory_ro_FilePath)) {
                
                inventory_ro_FilePath = fs.readFileSync(
                  parsedBody.warningObj.inventory_ro_FilePath
                );
               
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: inventory_ro_FilePath,
                    contentType: "csv",
                    filename: "inventory_ro.csv",
                  })
                );
              
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("extraRoInXmlExceptionFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.extraRoInXmlExceptionFilePath)
              ) {
                extraRoInXmlExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.extraRoInXmlExceptionFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: extraRoInXmlExceptionFile,
                    contentType: "csv",
                    filename: "extraros_in_xml.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("deletedRosFilepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.deletedRosFilepath)
              ) {
                deletedRosFile= fs.readFileSync(
                  parsedBody.warningObj.deletedRosFilepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: deletedRosFile,
                    contentType: "csv",
                    filename: "removed_ros.csv",
                  })
                );
              }
            }

            if (
              parsedBody.warningObj.hasOwnProperty("coupon_and_discount_exception_filepath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.coupon_and_discount_exception_filepath)
              ) {
                couponAndDiscountExceptionFile= fs.readFileSync(
                  parsedBody.warningObj.coupon_and_discount_exception_filepath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: couponAndDiscountExceptionFile,
                    contentType: "csv",
                    filename: "coupon_and_discount_exception.csv",
                  })
                );
              }
            }


            if (
              parsedBody.warningObj.hasOwnProperty("imOpenedClosedRciRosFilePath")
            ) {
              if (
                fs.existsSync(parsedBody.warningObj.imOpenedClosedRciRosFilePath)
              ) {
                imOpenedClosedRciRosFile= fs.readFileSync(
                  parsedBody.warningObj.imOpenedClosedRciRosFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: imOpenedClosedRciRosFile,
                    contentType: "csv",
                    filename: "im_opened_closed_rci_ros.csv",
                  })
                );
              }
            }
            data.attachment = attachmentFileList;

          }else if(data.subject.toLowerCase().includes("sharepoint")){
              if (
                fs.existsSync(preImportHaltFilePath)
              ) {
                preImportHaltFile= fs.readFileSync(
                  preImportHaltFilePath
                );
                attachmentFileList.push(
                  new mailgun.Attachment({
                    data: preImportHaltFile,
                    contentType: "txt",
                    filename: "halt-import.txt",
                  })
                );
              }
       

            data.attachment = attachmentFileList;


          }
          else {
            data.attachment = "";
          }
        }
      } catch (error) {
        console.log(error);
      }

      if (reqBdy.ccAddress) {
        data.cc = reqBdy.ccAddress.join();
      }
      data['h:Reply-To'] = reqBdy.fromAddress;
      console.log("Notification Status > ", sendNotification);
      segment.saveSegment(`Send Mail: notification status > ${sendNotification}`);
      console.log(`Send Mail: notification status > ${sendNotification}`);

      if (sendNotification) {
        try {
          mailgun.messages().send(data, (error, body) => {
            if (error) {
              console.log(error);
              reject(error);
            }
            else {
              try{
                if (logAttachedFilePath && fs.existsSync(logAttachedFilePath)) {
                  try {
                    fs.unlink(logAttachedFilePath, function (err) {
                      if (err){
                        reject(err);
                      } 
                    });
                  } catch (error) {
                    console.log(error);
                    reject(error);
                  }
                }
                
                if (parsedBody.warningObj.hasOwnProperty("roAccountingDescCsvFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.roAccountingDescCsvFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.roAccountingDescCsvFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `DealerTrack : Delete operation  of ro_accounting_desc_exception.csv have error ${err}`
                      );
                    }
                  }
                }
                
                if (parsedBody.warningObj.hasOwnProperty("coreReturnExceptionCsvFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.coreReturnExceptionCsvFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.coreReturnExceptionCsvFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `CDK3PA : Delete operation  of  core_return_exception.csv have error ${err}`
                      );
                    }
                  }
                }

                   
                if (parsedBody.warningObj.hasOwnProperty("labor_with_no_paytype_exception_filepath")) {
                  if (fs.existsSync(parsedBody.warningObj.labor_with_no_paytype_exception_filepath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.labor_with_no_paytype_exception_filepath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `DEALERTRACK : Delete operation  of  Labor With No Paytype Exception File Have error ${err}`
                      );
                    }
                  }
                }

               if (parsedBody.warningObj.hasOwnProperty("invalidmiscpaytypeFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.invalidmiscpaytypeFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.invalidmiscpaytypeFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `REYNOLDSRCI : Delete operation  of  core_return_exception.csv have error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("coreChargeExceptionCsvFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.coreChargeExceptionCsvFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.coreChargeExceptionCsvFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `CDK3PA : Delete operation  of  core_charge_exception.csv have error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("gl_missing_ros_exception_filepath")) {
                  if (fs.existsSync(parsedBody.warningObj.gl_missing_ros_exception_filepath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.gl_missing_ros_exception_filepath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `DEALERTRACK : Delete operation  of  gl missing ros exception file has error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("partDescriptionExceptionFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.partDescriptionExceptionFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.partDescriptionExceptionFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `REYNOLDS : Delete operation  of  part Description Exception file has error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("lost_sale_parts_exception_filepath")) {
                  if (fs.existsSync(parsedBody.warningObj.lost_sale_parts_exception_filepath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.lost_sale_parts_exception_filepath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `DEALERTRACK : Delete operation  of Lost Sale Parts  Exception file has error ${err}`
                      );
                    }
                  }
                }

                

                if (parsedBody.warningObj.hasOwnProperty("parts_excluded_from_history_exception_filepath")) {
                  if (fs.existsSync(parsedBody.warningObj.parts_excluded_from_history_exception_filepath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.parts_excluded_from_history_exception_filepath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `REYNOLDS : Delete operation  of  Parts Excluded From History Exception file has error ${err}`
                      );
                    }
                  }
                }

               if (
                  parsedBody.warningObj.hasOwnProperty(
                    "coreReturnNotEqualCoreChargeExceptionFilePath"
                  )
                ) {
                  if (
                    fs.existsSync(
                      parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionFilePath
                    )
                  ) {
                    try {
                      fs.unlinkSync(
                        parsedBody.warningObj.coreReturnNotEqualCoreChargeExceptionFilePath
                      );
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `CDK3PA : Delete operation  of Exception_core_return_not_equal_to_core_charge.csv have error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("coreChargeWithNoSaleCsvFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.coreChargeWithNoSaleCsvFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.coreChargeWithNoSaleCsvFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `CDK3PA : Delete operation  of core charge with_no sale.csv have error ${err}`
                      );
                    }
                  }
                }


                if (parsedBody.warningObj.hasOwnProperty("SaleZeroCostNonZeroPartsFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.SaleZeroCostNonZeroPartsFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.SaleZeroCostNonZeroPartsFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `DOMINION : Delete operation  of core charge with_no sale.csv have error ${err}`
                      );
                    }
                  }
                }


                
              
                if (parsedBody.warningObj.hasOwnProperty("glRoMissingFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.glRoMissingFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.glRoMissingFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `CDK3PA : Delete operation  of glRoMissingFilePath have error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("exceptionClosedInvoicesFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.exceptionClosedInvoicesFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.exceptionClosedInvoicesFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `Reynolds : Delete operation  of exceptionClosedInvoicesFilePath have error ${err}`
                      );
                    }
                  }
                }



                if (parsedBody.warningObj.hasOwnProperty("extraRoInXmlExceptionFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.extraRoInXmlExceptionFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.extraRoInXmlExceptionFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `Reynolds : Delete operation  of extraRoInXmlExceptionFilePath have error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("imOpenedClosedRciRosFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.imOpenedClosedRciRosFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.imOpenedClosedRciRosFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `Reynolds : Delete operation  of imOpenedClosedRciRosFilePath have error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("coupon_discount_basis_amount_mismatch_exception_filepath")) {
                  if (fs.existsSync(parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_filepath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.coupon_discount_basis_amount_mismatch_exception_filepath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `DEALERTRACK : Delete operation  of coupon_discount_basis_amount_mismatch_exception_filepath have error ${err}`
                      );
                    }
                  }
                }


                

                if (parsedBody.warningObj.hasOwnProperty("negative_coupon_exception_filepath")) {
                  if (fs.existsSync(parsedBody.warningObj.negative_coupon_exception_filepath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.negative_coupon_exception_filepath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `Dealertrack : Delete operation  of negative coupon exception filepath have error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("partDetailsNullExceptionFilepath")) {
                  if (fs.existsSync(parsedBody.warningObj.partDetailsNullExceptionFilepath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.partDetailsNullExceptionFilepath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `CDK3PA : Delete operation  of part Details Null Exception File have error ${err}`
                      );
                    }
                  }
                }

                


                
               if (parsedBody.warningObj.hasOwnProperty("invalidCoreCostSaleMismatchFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.invalidCoreCostSaleMismatchFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.invalidCoreCostSaleMismatchFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `CDK3PA : Delete operation  of invalid_core_cost_sale_mismatch.csv have error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("invalidCoreAmountMismatchFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.invalidCoreAmountMismatchFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.invalidCoreAmountMismatchFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `CDK3PA : Delete operation  of core invalid_core_amount_mismatch.csv have error ${err}`
                      );
                    }
                  }
                }
                 
               
               
               
                if (parsedBody.warningObj.hasOwnProperty("company_no_not_matching_file_path")) {
                  if (fs.existsSync(parsedBody.warningObj.company_no_not_matching_file_path)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.company_no_not_matching_file_path);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `DEALERTRACK : Delete operation  of company_no_not_matching.csv have error ${err}`
                      );
                    }
                  }
                }

                
                if (parsedBody.warningObj.hasOwnProperty("grouped_team_work_file_path")) {
                  if (fs.existsSync(parsedBody.warningObj.grouped_team_work_file_path)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.grouped_team_work_file_path);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `DEALERTRACK : Delete operation  of grouped-team-work.csv have error ${err}`
                      );
                    }
                  }
                }

               
               
               
                if (parsedBody.warningObj.hasOwnProperty("suffixedInvoicesFilePath")) {
                  if (fs.existsSync(parsedBody.warningObj.suffixedInvoicesFilePath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.suffixedInvoicesFilePath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `REYNOLDS : Delete operation  of suffixed-invoices.csv have error ${err}`
                      );
                    }
                  }
                }

                if (parsedBody.warningObj.hasOwnProperty("labor_with_zero_sale_nonzero_cost_exception_filepath")) {
                  if (fs.existsSync(parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_exception_filepath)) {
                    try {
                      fs.unlinkSync(parsedBody.warningObj.labor_with_zero_sale_nonzero_cost_exception_filepath);
                    } catch (err) {
                      console.error(err);
                      segment.saveSegment(
                        `Dealertrack  : Delete operation  of csv file have error ${err}`
                      );
                    }
                  }
                }

              try{
                  if (parsedBody.warningObj.hasOwnProperty("miscExceptionCsvFilePath")) {
                    if (fs.existsSync(parsedBody.warningObj.miscExceptionCsvFilePath)) {
                      try {
                        fs.unlinkSync(parsedBody.warningObj.miscExceptionCsvFilePath);
                      } catch (err) {
                        console.error(err);
                        segment.saveSegment(
                          `AutoMate : Delete operation of misc-exception.csv have error ${err}`
                        );
                      }
                    }
                  }
                } catch(err){
                  console.log(err);
                }
                
                ////////////////////////////// End code for delete exception csv reports ////////////////////////////////////////////////
                
                resolve({ status: 'success', message: '' });
              } catch(error){
                reject(error);
              }
            }
          });
        }
        catch (error) {
          console.log(error);
          reject(error);
        }
        notifyObj = {};
      }
    } else {
      segment.saveSegment(`Send Mail: failed > Api key and Domain not found`);
      console.log(`Send Mail: failed > Api key and Domain not found`);
      reject({ status: 'failed', message: 'Api key and Domain not found' });
    }
  });
}


module.exports = { sendMail };
