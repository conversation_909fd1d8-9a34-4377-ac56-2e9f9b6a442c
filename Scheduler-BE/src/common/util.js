"use strict";
const constants = require("./constants");
const constantsCommon = require('./constants');

const fs = require("fs");
const path = require("path");
const unZipper = require("unzipper");
const zipFolder = require("zip-folder");
const segment = require("../controllers/SEGMENT/segmentManager");
const appConstants = require("../common/constants");
const request = require('request');
const AdmZip = require('adm-zip');
const fileTempPath = process.env.PAY_TYPE_TEMP_PATH;
const fileETLCopyTempPath = process.env.COPY_ETL_TEMP_PATH; // Temporary folder for copy ETL file(pg_dump) from dist to cdk-zip-eti

var unzipFile = (filePath) => {
  return new Promise((resolve, reject) => {
    try {
      fs.createReadStream(filePath)
        .pipe(unZipper.Extract({ path: fileTempPath }))
        .on("close", function () {
          console.log("Unzip completed");
          resolve("success");
        });
    } catch (error) {
      reject(error);
    }
  });
};

/*
function for copy ETL file (pg_dump file) to cdk-zip-eti  directory for proxy rerun
*/
var copyETLFile = (filePath, destFileName) => {
  return new Promise((resolve, reject) => {
    try {
      fs.createReadStream(filePath)
        .pipe(unZipper.Extract({ path: fileETLCopyTempPath }))
        .on("close", function () {
          let etlSrcFile = fileETLCopyTempPath + "process-json-csv-results.pgdump";
          var etlTmpDir = fileETLCopyTempPath + "etl-file/";
          if (!fs.existsSync(etlTmpDir)) {
            try{
              fs.mkdirSync(etlTmpDir);
            } catch(err){
              console.log(err);
              reject(err);
            }
          }
          var etlSrcFileTmp = etlTmpDir + "process-json-csv-results.pgdump";
          try {
            fs.copyFile(etlSrcFile , etlSrcFileTmp, (err) => {
              if (err) {
                console.log(err);
                reject(err);
              } else {
                let newDestFileName = destFileName + ".zip";
                zipFolder(etlTmpDir, newDestFileName, function (err) {
                  if (err) {
                    console.log(err);
                    removeDirForce(fileETLCopyTempPath);
                    reject(err);
                  } else {
                    removeDirForce(fileETLCopyTempPath);
                    resolve("success");
                  }
                });
              }
            });
          } catch (err) {
            console.log(err);
            reject(error);
          }
        });
    } catch (error) {
      console.log(err);
      reject(error);
    }
  });
};

/**
 * 
 * @param {*} arr 
 * @param {*} key 
 * Function used for unique the array elements using es6
 * Ref: https://stackoverflow.com/questions/2218999/remove-duplicates-from-an-array-of-objects-in-javascript 6 th answer
 */

function _unique(arr, key) {
    return [...new Map(arr.map(item => [item[key], item])).values()]
}
const sendPayloadRequest = async (payload, DMS_type = "Atomate") => {
  try {
    const authKey = Buffer.from(appConstants.conf.PAYLOAD_ENC_KEY).toString('base64');

    const options = {
      headers: {
        'authKey': authKey
      },
      url: appConstants.conf.SALES_TAG_URI,
      json: true,
      body: payload
    };

    // Adding DMS_type to the log
    segment.saveSegment(`ADD SALES TAG ${DMS_type}: Sending request with options: ${JSON.stringify(options)}`);

    const response = await new Promise((resolve, reject) => {
      request.post(options, (err, res, body) => {
        if (err) {
          console.log(err);
          // Log the error with DMS_type
          segment.saveSegment(`ADD SALES TAG ${DMS_type}: Error: ${JSON.stringify(err)}`);
          reject({
            status: false,
            message: `ADD SALES TAG ${DMS_type}: Error: ${JSON.stringify(err)}`
          });
        } else {
          try {
            // Log the status and body with DMS_type
            segment.saveSegment(`ADD SALES TAG ${DMS_type}: Response Status: ${res.statusCode}`);
            segment.saveSegment(`ADD SALES TAG ${DMS_type}: Response Body: ${JSON.stringify(body)}`);
            resolve({
              status: true,
              message: body.data ? body.data.message : "Success"
            });
          } catch (error) {
            // Log the error processing response with DMS_type
            segment.saveSegment(`ADD SALES TAG ${DMS_type}: Error processing response: ${JSON.stringify(error)}`);
            reject({
              status: false,
              message: "Error processing the response"
            });
          }
        }
      });
    });

    return response;
  } catch (error) {
    // Log the error with DMS_type
    segment.saveSegment(`ADD SALES TAG ${DMS_type}: Error in sendPayloadRequest: ${JSON.stringify(error)}`);
    throw new Error(error.message || "Unknown error");
  }
};

async function sendNotificationCall(UserInput, DMS_type = "Atomate") {
  // Log the type of DMS extraction being processed
  segment.saveSegment(`ADD SALES TAG ${DMS_type}: Processing started`);

  let responseData = {};
  try {
    const {
      inPartsProjectId = "",
      inPartsProjectType = "",
      inLaborProjectId = "",
      inLaborProjectType = "",
      inIsInSales = "",
      inSalesComment = "",
      inUpdatedBy = "",
    } = UserInput;

    // Log the received UserInput with DMS_type
    segment.saveSegment(`ADD SALES TAG ${DMS_type}: Received UserInput: ${JSON.stringify(UserInput)}`);
    
    // Create and send Parts payload if inPartsProjectId exists
    if (inPartsProjectId) {
      const inPartsPayload = {
        "inProjectId": inPartsProjectId,
        "inProjectType": inPartsProjectType,
        "inIsInSales": inIsInSales,
        "inSalesComment": inSalesComment,
        "inUpdatedBy": inUpdatedBy
      };

      // Log before sending Parts Payload
      segment.saveSegment(`ADD SALES TAG ${DMS_type}: Sending Parts Payload`);
      const SalesTagPartsResponse = await sendPayloadRequest(inPartsPayload, DMS_type);
      if (SalesTagPartsResponse) {
        responseData.SalesTagPartsResponse = SalesTagPartsResponse;
      }
    }

    // Create and send Labor payload if inLaborProjectId exists
    if (inLaborProjectId) {
      const inLaborPayload = {
        "inProjectId": inLaborProjectId,
        "inProjectType": inLaborProjectType,
        "inIsInSales": inIsInSales,
        "inSalesComment": inSalesComment,
        "inUpdatedBy": inUpdatedBy
      };

      // Log before sending Labor Payload
      segment.saveSegment(`ADD SALES TAG ${DMS_type}: Sending Labor Payload`);
      const SalesTagLaborResponse = await sendPayloadRequest(inLaborPayload, DMS_type);
      if (SalesTagLaborResponse) {
        responseData.SalesTagLaborResponse = SalesTagLaborResponse;
      }
    }

    // Log the collected response data with DMS_type
    segment.saveSegment(`ADD SALES TAG ${DMS_type}: Response Data: ${JSON.stringify(responseData)}`);
    return responseData;
  } catch (error) {
    console.log("Error in sendNotificationCall:", error);
    // Log the error with DMS_type
    segment.saveSegment(`ADD SALES TAG ${DMS_type}: Error in processing request: ${JSON.stringify(error)}`);
    throw new Error(error.message || "Unknown error");
  }
}
// Utility function to create config files for company data
async function processCompanyData(job, userName, processFileName, filePath,JobType, groupCodeConfig = '') {

segment.saveSegment(`processCompanyData ..333.  job:${job}userName:${userName}processFileName:${processFileName}filePath:${filePath}JOB_TYPE:${JobType}`);
  
  const storeDataArray = job.attrs.data.storeDataArray?.[0] || {};
  const companyIds = storeDataArray.companyIds || '';

  if (companyIds.trim() === '') return;

  try {
      const companyIdArray = companyIds.split('*').filter(Boolean);      
      

      for (const companyId of companyIdArray) {
          let companyObj = storeDataArray.companyObj || [];
          if (typeof companyObj === 'string') {
              companyObj = JSON.parse(companyObj);
          }

          const matchingCompany = companyObj.find(item => item.companyId === companyId);
          const projectId = matchingCompany?.projectId || '';
          const secondProjectId = matchingCompany?.secondProjectId || '';
          const projectName = matchingCompany?.projectName || '';
          const secondProjectName = matchingCompany?.secondaryProjectName || '';
          const secondaryProjectType= matchingCompany?.secondaryProjectType || storeDataArray.secondaryProjectType || '';
          const projectType= matchingCompany?.projectType || storeDataArray.projectType || '';
          const storeName = storeDataArray.mageStoreName ? storeDataArray.mageStoreName : JSON.parse(storeDataArray.metaData || '{}')?.mageStoreName;
          const groupCode = storeDataArray.groupCode ? storeDataArray.groupCode : JSON.parse(storeDataArray.metaData || '{}')?.groupCode;
          const company_name =  matchingCompany?.companyName || '';
          const third_party_username = storeDataArray.thirdPartyUsername || JSON.parse(storeDataArray.metaData || '{}')?.thirdPartyUsername || storeDataArray.dealerId;
          const assignedtoCn = storeDataArray.assignedtoCn || JSON.parse(storeDataArray.metaData || '{}')?.assignedtoCn;
          const errors =  storeDataArray.errors || JSON.parse(storeDataArray.metaData || '{}')?.errors;  
          
          const inputData = {
              dms: JobType,
              mageGrpData: {
                  state: storeDataArray.stateCode || '',
                  mageGroupCode: groupCode || '',
                  mageGroupName: job.attrs.data.groupName,
                  mageStoreName: storeName || '',
                  mageStoreCode: storeDataArray.mageStoreCode || '',
                  mageProjectId: projectId,
                  mageSecondaryId: secondProjectId,
                  mageManufacturer: storeDataArray.mageManufacturer || '',
                  mageProjectName: projectName || '',
                  mageProjectType: projectType || '',
                  secondaryProjectType: secondaryProjectType || '',
                  secondaryProjectName: secondProjectName || '',
                  companyId: companyId,
                  userName: userName || '',
                  schedulerId: storeDataArray.uniqueId || job.attrs.data.att.uniqueId,
                  sourceFile: processFileName || ''
              },
          };
          const inputJobData = {
            company_id: companyId,
            mage_manufacturer: storeDataArray.mageManufacturer || '',
            state: storeDataArray.stateCode || '',
            company_name: company_name || '',
            mage_group_code: groupCode || '',
            mage_group_name: job.attrs.data.groupName,
            mage_store_code: storeDataArray.mageStoreCode || '',
            mage_store_name: storeName || '',
            third_party_username: third_party_username || '',
            project_id:projectId || '',
            project_name:projectName || '', 
            project_type:projectType || '',
            assignedto_cn: assignedtoCn || '',
            dms_code: JobType,
            etl_dms: JobType,
            errors: errors || '',
            secondary_project_id: secondProjectId,
            secondary_project_type: secondaryProjectType || '',
            secondary_project_name: secondProjectName || ''
          };    

          segment.saveSegment(`createConfigFile inputData: ${JSON.stringify(inputData)}`);
          segment.saveSegment(`createConfigFile filePath: ${filePath}`);

          const configFileName = `config_${companyId}.bash`;
          await createConfigFile(inputData, filePath, configFileName);
          await getJobs(inputJobData,filePath, 'job.txt');   
        };

      
  } catch (error) {
      segment.saveSegment(`createConfigFile error: ${error}`);
  }
}

async function getJobs(inputJobData, outputFilePath, jobFilename = 'job.txt') {
    return new Promise((resolve, reject) => {
        segment.saveSegment("Generating job.txt file");

        // Format job data into a pipe-separated string
        const formattedRow = [
            inputJobData.company_id,
            inputJobData.mage_manufacturer,
            inputJobData.state,
            inputJobData.company_name,
            inputJobData.mage_group_code,
            inputJobData.mage_group_name.replace(/\//g, ' '), // Replacing '/' with space
            inputJobData.mage_store_code,
            inputJobData.mage_store_name,
            inputJobData.third_party_username,
            inputJobData.project_id,
            inputJobData.project_name,
            inputJobData.project_type,
            inputJobData.assignedto_cn,
            inputJobData.dms_code,
            inputJobData.etl_dms,
            inputJobData.errors,
            inputJobData.secondary_project_id,
            inputJobData.secondary_project_type,
            inputJobData.secondary_project_name
        ].join('|') + '\n'; // Joining values with '|' and adding newline

        // Define the temporary job file path
        const tempJobFilePath = path.join('/tmp', jobFilename);

        // Write job data to the file
        fs.writeFile(tempJobFilePath, formattedRow, (err) => {
            if (err) {
                segment.saveSegment(`Error writing job.txt file: ${err.message}`);
                return reject(err);
            }

            // Ensure the output zip file exists
            if (!fs.existsSync(outputFilePath)) {
                segment.saveSegment(`Error: Zip file does not exist - ${outputFilePath}`);
                return reject(`Error: Zip file does not exist - ${outputFilePath}`);
            }

            try {
                const zip = new AdmZip(outputFilePath);
                const tempExtractionPath = path.join("/tmp", `unzipped_${Date.now()}`);

                // Extract existing zip content to a temp directory
                zip.extractAllTo(tempExtractionPath, true);

                // Add job file to the extracted folder
                fs.copyFileSync(tempJobFilePath, path.join(tempExtractionPath, jobFilename));

                // Create a new zip with the updated content
                const newZip = new AdmZip();
                newZip.addLocalFolder(tempExtractionPath);
                newZip.writeZip(outputFilePath);

                // Clean up temporary files
                fs.unlinkSync(tempJobFilePath);
                fs.rmSync(tempExtractionPath, { recursive: true, force: true });

                segment.saveSegment(`Job file successfully added to zip: ${outputFilePath}`);
                resolve(`Job file successfully added to zip: ${outputFilePath}`);
            } catch (zipError) {
                segment.saveSegment(`Error zipping file: ${zipError.message}`);
                reject(`Error zipping file: ${zipError.message}`);
            }
        });
    });
}




function createConfigFile(inputData, outputFilename) {
  return new Promise((resolve, reject) => {
    const {
      dms,
      mageGrpData: {
        state,
        mageGroupCode,
        mageGroupName,
        mageStoreName,
        mageStoreCode,
        mageProjectId,
        mageSecondaryId,
        mageManufacturer,
        mageProjectName,
        mageProjectType,
        secondaryProjectType,
        secondaryProjectName,
        companyId,
        userName,
        schedulerId,
        sourceFile
      },
    } = inputData;

    segment.saveSegment(`createConfigFile: inputData - ${inputData}`);

    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');

    const dmsMapping = dms === "Dominion / VUE" || dms === "Dominion / ACS" ? "DominionVue" :
      dms === "Auto/Mate" ? "AutoMate" : dms;

    const configContent = `# Bash Source Test Config File

export DMS_BASE='${dms}'
source "$DU_ETL_HOME/DU-DMS/DMS-${dms}/${dms}.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="\${DU_ETL_ETI_DIR_${dms}}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ${dms}
#Base DMS: ${dms} (optional)

DMS="${dms}"
DATE_PART='${year}${month}'

GROUP_CODE="${mageGroupCode}"
GROUPNAME="${mageGroupName}"
STORENAME="${mageStoreName}"
STORE_PART="${mageStoreCode}"
MFG="${mageManufacturer}"
STATE='${state}'

if [[ "\$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="\$GROUP_CODE"
    SERVICE_NAME=\${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=\${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="\${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=\${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=\${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="\${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[\$GROUP_CODE]\${STORE_PART}+\${DATE_PART}_\${MFG}"

COMPANY_ID="${companyId}"
SOURCE_COMPANY_ID="${companyId}"
PROJECT_ID="${mageProjectId}"
PROJECT_TYPE="${mageProjectType}"
PROJECT_NAME="${mageProjectName}"
SECONDARY_PROJECT_ID="${mageSecondaryId}"
SECONDARY_PROJECT_TYPE="${secondaryProjectType}"
SECONDARY_PROJECT_NAME="${secondaryProjectName}"
IMPORTED_BY='${userName}'
SCHEDULER_ID="${schedulerId}"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="${sourceFile}"`;

    segment.saveSegment(`createConfigFile: configContent - ${configContent}`);

    const zipPath = outputFilename;

    if (!fs.existsSync(zipPath)) {
      segment.saveSegment(`Error: Zip file does not exist - ${zipPath}`);
      reject(`Error: Zip file does not exist - ${zipPath}`);
      return;
    }

    // Create temporary file for config content
    const tempConfigFilePath = path.join('/tmp', `config_${companyId}.bash`);
    fs.writeFile(tempConfigFilePath, configContent, (err) => {
      if (err) {
        segment.saveSegment(`Error writing to temporary config file: ${err.message}`);
        reject(`Error writing to temporary config file: ${err.message}`);
        return;
      }

      try {
        const zip = new AdmZip(outputFilename);
        const tempExtractionPath = path.join("/tmp", `unzipped_${Date.now()}`);

        // Extract existing zip content to a temp directory
        zip.extractAllTo(tempExtractionPath, true);

        // Add the config file to the directory
        const configFileName = path.basename(tempConfigFilePath);
        fs.copyFileSync(tempConfigFilePath, path.join(tempExtractionPath, configFileName));

        const newZip = new AdmZip();
        newZip.addLocalFolder(tempExtractionPath);
        newZip.writeZip(outputFilename);

        // Clean up temporary files
        fs.unlinkSync(tempConfigFilePath);
        fs.rmSync(tempExtractionPath, { recursive: true, force: true });
        segment.saveSegment(`Config file successfully added to zip: ${zipPath}`);        

        resolve(`Config file successfully added to zip: ${zipPath}`);
      } catch (zipError) {
        segment.saveSegment(`Error zipping file: ${zipError.message}`);
        reject(`Error zipping file: ${zipError.message}`);
      }
    });
  });
}




function removeDirForce(dirPath) {
    try { var files = fs.readdirSync(dirPath); }
    catch (e) { return; }
    if (files.length > 0)
        for (var i = 0; i < files.length; i++) {
            var filePath = dirPath + '/' + files[i];
            if (fs.statSync(filePath).isFile())
                fs.unlinkSync(filePath);
            else
                removeDirForce(filePath);
        }
    fs.rmdirSync(dirPath);
    console.log('Directory cleared');
}

function  getinpObjFordoPayloadAction(projectID, attPayload, todayDate,  actionLabel){
  let inpObj;
  try{
    inpObj = {
      inProjectId : projectID,
      inSource : constantsCommon.PULLED_VIA,
      inAction : actionLabel,
      inAssignee  : attPayload.userName,
      inPerformedOn : todayDate,
      inPerformedBy : attPayload.userName,
      inData  : JSON.stringify(attPayload),
      inCreatedBy : attPayload.userName
    };
  } catch (err){
    console.log(err);
  }
 
  return inpObj;
}
function getTimestampForAuditFile() {
  const now = new Date();
  const pad = (n) => n.toString().padStart(2, '0');

  const year = now.getFullYear().toString().slice(2); 
  const month = pad(now.getMonth() + 1);           
  const day = pad(now.getDate());
  const hour = pad(now.getHours());
  const minute = pad(now.getMinutes());
  // const second = pad(now.getSeconds());

  return `${year}${month}${day}${hour}${minute}`;
}

module.exports = {
    removeDirForce: removeDirForce,
    unzipFile: unzipFile,
    _unique: _unique,
    copyETLFile: copyETLFile,
    getinpObjFordoPayloadAction: getinpObjFordoPayloadAction,
    sendNotificationCall:sendNotificationCall,
    processCompanyData:processCompanyData,
    getTimestampForAuditFile:getTimestampForAuditFile
}
