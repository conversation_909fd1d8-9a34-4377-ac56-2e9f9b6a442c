var agenda = require("./controllers/agenda");

/**
  * Add AgendaManager for each DMSs here
  */
var cdkAgendaJobManager = require("./controllers/CDK/AgendaJobManager");
var cdkFlexAgendaJobManager = require("./controllers/CDKFLEX/AgendaJobManager");

var autoMateAgendaJobManager = require("./controllers/AutoMate/AgendaJobManager");
var dealerBuiltAgendaJobManager = require("./controllers/DealerBuilt/AgendaJobManager");
var dealerTrackAgendaJobManager = require("./controllers/DealerTrack/AgendaJobManager");
var adamAgendaJobManager = require("./controllers/Adam/AgendaJobManager");
var reynoldsAgendaJobManager = require("./controllers/Reynolds/AgendaJobManager");
var dominionAgendaJobManager = require("./controllers/Dominion/AgendaJobManager");
var autosoftAgendaJobManager = require("./controllers/AutoSoft/AgendaJobManager");
var pbsAgendaJobManager = require("./controllers/Pbs/AgendaJobManager");
var quorumAgendaJobManager = require("./controllers/Quorum/AgendaJobManager");
var ucsAgendaJobManager = require("./controllers/Ucs/AgendaJobManager");
var tekionAgendaJobManager = require("./controllers/Tekion/AgendaJobManager");
var mpkAgendaJobManager = require("./controllers/Mpk/AgendaJobManager");
var tekionApiAgendaJobManager = require("./controllers/TekionApi/AgendaJobManager");
var fortellisAgendaJobManager = require("./controllers/Fortellis/AgendaJobManager");
var fopcAgendaJobManager = require("./controllers/fopc/AgendaJobManager");

async function graceful() {
  await agenda.stop();
  process.exit(0);
}

process.on('SIGTERM', graceful);
process.on('SIGINT', graceful);


(async function () {
  await agenda.start();
  // Clear all previous Process XML/JSON jobs with operation = recheck or operation = start
  await agenda.cancel({
    $or: [
      { "data.operation": "start" },
      { "data.operation": "recheck" }
    ]
  }), (err, numRemoved) => {
    if (err)
    console.log(err);
    console.log("All previous process XML/JSON jobs cleared");
  };
  await cdkAgendaJobManager(agenda);
  await cdkFlexAgendaJobManager(agenda);
  await autoMateAgendaJobManager(agenda);
  await fortellisAgendaJobManager(agenda);
  await dealerBuiltAgendaJobManager(agenda);
  await dealerTrackAgendaJobManager(agenda);
  await adamAgendaJobManager(agenda);
  await reynoldsAgendaJobManager(agenda);
  await dominionAgendaJobManager(agenda);
  await autosoftAgendaJobManager(agenda);
  await pbsAgendaJobManager(agenda);
  await quorumAgendaJobManager(agenda);
  await mpkAgendaJobManager(agenda);
  await tekionAgendaJobManager(agenda);
  await tekionApiAgendaJobManager(agenda);
  await quorumAgendaJobManager(agenda);
  await fopcAgendaJobManager(agenda)
})();

