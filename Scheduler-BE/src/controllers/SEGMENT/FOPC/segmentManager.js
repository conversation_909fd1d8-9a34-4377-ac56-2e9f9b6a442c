let fs = require("fs");
const moment = require("moment-timezone");
/**
 * Function to Save segment details to file
 * @param {*} segmentData
 */
async function saveSegment(segmentData) {
    let respMessage = "";
    try {
        let currentPath = process.cwd() + "/logs/FOPC/segment-Fopc-" + moment().format("YYYY-MM-DD") + ".log";
        let infoStream = fs.createWriteStream(currentPath, { flags: "a+" });
        let message = moment().toString() + " : " + segmentData + "\n";
        respMessage = "Success";
        try {
            infoStream.write(message);
        } catch (error) {
            respMessage = "Failure";
        }
        return { status: true, message: respMessage };
    } catch (error) {
        respMessage = "Failure";
        return { status: false, message: respMessage };
    }
}

async function saveSegmentFailure(segmentData,storeCode) {
    let respMessage = "";
    let failureDirectory = process.cwd() + "/logs/FOPC/failure/";
    try {
        if (!fs.existsSync(failureDirectory)){
          fs.mkdirSync(failureDirectory);
        }
      } catch (err) {
        console.error(err);
      }
    try {
        let currentPath = failureDirectory + storeCode + ".log";
       
        let infoStream = fs.createWriteStream(currentPath, { flags: "a+" });
        let message = moment().toString() + " : " + segmentData + "\n";
        respMessage = "Success";
        try {
            infoStream.write(message);
        } catch (error) {
            respMessage = "Failure";
        }
        return { status: true, message: respMessage };
    } catch (error) {
        respMessage = "Failure";
        return { status: false, message: respMessage };
    }  
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

module.exports = {
    saveSegment,
    saveSegmentFailure,
    sleep
};
