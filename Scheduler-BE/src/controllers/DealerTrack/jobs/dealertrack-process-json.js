"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const { spawn } = require("child_process");
const path = require('path');
const fs = require("fs");
const moment = require("moment-timezone");
const segment = require("../../SEGMENT/DealerTrack/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const stripAnsi = require('strip-ansi');
var Agenda = require("../../agenda");
const extractionError = require('../../../../src/common/extractionError');
const csv=require('csvtojson');
const csvParser = require('csv-parser');
const csv1 = require('csv-parser');
const SetProcessJobStatus = require('../../../model/setProcessJobStatus');
const appUtil = require('../../../common/util');
/**
 * Function to perform processing of XML file downloaded through DealerTrack-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {

    var distributeFile = async function (fileName, rerunFlag , updateSolve360Data, warningObj, jobId) {
        var stdErrorArray;
        var distDir = path.join(process.env.DEALERTRACK_DIST_DIR, fileName);
        var etlDir = path.join(process.env.DEALERTRACK_ETL_DIR, fileName);
        etlDir = etlDir.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
        var filePath = path.join(process.env.DEALERTRACK_BUNDLE_DIR, fileName);
        const distributeFile = spawn("bash",
            [
                'send-bundle-live-hpdog', filePath,rerunFlag
            ], {
                cwd: constants.PROCESS_JSON.DEALERTRACK_DISTRIBUTE_CMD_PATH,
                env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
            }).on('error', function (err) {
                console.log("error :", err);
                segment.saveSegment(`error: ${err}`);
            });
        console.log(`DealerTrack: Start processing of distribution`);
        segment.saveSegment(`DealerTrack:  processing distribution`);
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", async (data) => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
        });

        distributeFile.stderr.on("data", async (data) => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + ' ';
            segment.saveSegment(`stderr: ${data}`);
        });

        distributeFile.on("close", async (code) => {
            var message = "n/a";
            var status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
                // await SetProcessJobStatus.setProcessJobStatusForDealerTrack(enterpriseCode,mageStoreCode,message);
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
              // await SetProcessJobStatus.setProcessJobStatusForDealerTrack(enterpriseCode,mageStoreCode,'Failed');
                message = "Distribution failed, general death";
                // await SetProcessJobStatus.setProcessJobStatusForDealerTrack(enterpriseCode,mageStoreCode,'Failed');
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
                // await SetProcessJobStatus.setProcessJobStatusForDealerTrack(enterpriseCode,mageStoreCode,'Failed');
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                segment.saveSegment(message);
            }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, constants.JOB_TYPE, rerunFlag , updateSolve360Data, warningObj, 0, jobId);//Upload dist directory zip file to sharepoint
            }
            await segment.sleep(2000);
            segment.saveSegment(`Completed distributeFile, call  doNextProcess()`);
            await doNextProcess();
        });
    }

    var doNextProcess = async function () {
        segment.saveSegment(`inside doNextProcess`);
        await segment.sleep(5000);
        const extractZip = await util.findOldestZipFile(constants.DEALERTRACK_SCHEDULER_ETI_DIR);
        if (fs.existsSync(`/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/scheduler-temp/dealertrack-zip-eti/${extractZip}`)) {
          console.log(`file Name ${extractZip} exists!`);
        } else {
          console.log(`file Name ${extractZip} does not exists`);
        }
        if (extractZip && fs.existsSync(`/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/scheduler-temp/dealertrack-zip-eti/${extractZip}`) ) {
            console.log(`DealerTrack : Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`DealerTrack : Found one Store extraction > ${extractZip} to process now`);
            try {
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                await agenda.now(constants.PROCESS_JSON.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    operation: "json-processing"
                });
                console.log(`DealerTrack : Process JSON schedule started with file > ${extractZip}`);
                segment.saveSegment(`DealerTrack : Process JSON schedule started with file > ${extractZip}`);
            } catch (error) {
                segment.saveSegment(`inside doNextProcess have error: ${JSON.stringify(error)}`);
                console.error(error);
            }

        } else {
            segment.saveSegment(`DealerTrack : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            console.log(`DealerTrack : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            //segment.saveSegment(`DealerTrack : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_JSON.TIME_GAP}`, constants.PROCESS_JSON.JOB_NAME, { operation: "recheck" });
                console.log(`DealerTrack : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
                segment.saveSegment(`DealerTrack : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
            } catch (error) {
                segment.saveSegment(`DealerTrack : Process JSON schedule have error: ${JSON.stringify(error)}`);
                console.error(error);
            }
        }
    }

    console.log(
        `DealerTrack : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`
    );
    segment.saveSegment(`DealerTrack : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`);
    agenda.define(constants.PROCESS_JSON.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.PROCESS_JSON.CONCURRENCY},
        async (job, done) => {
            const touch = setInterval(() => job.touch(), 1 * 60 * 1000);

            segment.saveSegment(`job data is  : ${JSON.stringify(job)}`);
            const att = job.attrs.data;
            segment.saveSegment(`att data is  : ${JSON.stringify(att)}`);
            var extractZip = null;
            var stdErrorArray = [];
            var stdOutArray = [];
            var uniqueFailLogName;
            let haltIdentifier = false;
            segment.saveSegment(`att.inputFile is  : ${att.inputFile}`);
            var inpFile = att.inputFile ? path.join(constants.DEALERTRACK_SCHEDULER_ETI_DIR, att.inputFile) : '';
            if (att.inputFile && fs.existsSync(inpFile)) {
                    segment.saveSegment(`Input file found : ${att.inputFile}`);
                    if (!fs.existsSync(constants.DEALERTRACK_DEADLETTER_DIR_PREFIX + '-processed')) {
                        fs.mkdirSync(constants.DEALERTRACK_DEADLETTER_DIR_PREFIX + '-processed');
                    }
                    extractZip = att.inputFile;
                    let basename = path.basename(extractZip);
                    var doProxyFromPgDump = false;
                    let enterpriseCode;
                    job.attrs.data.storeID = basename.split("-").reverse()[1];
                    let storeName = job.attrs.data.storeID;
                    var storeCode = storeName;
                    if (basename.includes(constants.PROCESS_JSON.REPLACE_STRING.DO_PROXY_FROM)) {
                        doProxyFromPgDump = true;
                        enterpriseCode= basename.split("-").reverse()[2];
                    } else {
                        enterpriseCode= basename.split("-").reverse()[1];
                        doProxyFromPgDump = false;
                    }
                    job.attrs.data.storeID = !doProxyFromPgDump ? basename.split("-").reverse()[1] : basename.split("-").reverse()[2];
                    // let enterpriseCode = basename.split("-").reverse()[1];
                    let mageGroupCode = basename.split("-")[0];
                    let mageStoreCode =  basename.split("-")[1];

                    uniqueFailLogName = enterpriseCode + '-' + mageStoreCode;
                    
                    console.log('Groupname:', mageGroupCode);
                    segment.saveSegment(`Groupname : ${mageGroupCode}`);
                    console.log('Enterprisecode:', enterpriseCode);
                    segment.saveSegment(`Enterprisecode : ${enterpriseCode}`);
                    console.log('storeName:',mageStoreCode);
                    segment.saveSegment(`storeName : ${mageStoreCode}`);

                    var jobsTmp = await Agenda.jobs( {
                        $and: [
                            { "data.storeDataArray.enterpriseCode": enterpriseCode },
                            { "data.storeDataArray.mageStoreCode": mageStoreCode},
                            { "name": constants.DEALERTRACK.JOB_NAME } 
                        ]
                    });
                    
                    var projectId = '';
                    var secondProjectId = '';
                    var userName = '';
                    var skipErrorCount;
                    var solve360Update = '';
                    var updateSolve360Data; 
                    var buildProxies;
                    var extractionId;
                    let coupon_and_discountCSVFilePath; 
                    let chart_of_accounts_file_path;
                    
                    let agendaObject;
                    let extractedFileTimeStamp;
                    let extractedFileCreationDate;
                    let extractedObjectIndex;
                    let mageManufacturer;
                    let isPorscheStore = false;

                    let haltOverRide = false;
                    let resumeUser;
                    let dealerAddress ='';
                    let companyNumber ='';
                    let projectIds;
                    let secondProjectIdList;
                    let uniqueId;
                    let companyIds;
                    let companyObj;
                    let testData;
                    let totalRoCount;
                    let brands;
                    let exceptionTypeCounts = {};
                    let parentName;
                    let modifiedFileName="";
                    let stateCode;
              
		    
                    try{
                        extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                        segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                        extractedFileCreationDate =  moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                        segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                    } catch(err){
                        console.log(err);
                        segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                    }
                    
                    segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                    segment.saveSegment(`jobsTmp[jobsTmp.length-1] : ${JSON.stringify(jobsTmp[jobsTmp.length-1])}`);
                    segment.saveSegment(`Enterprise Code : ${enterpriseCode}`);
                    console.log("JobsTmp[length - 1]>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",jobsTmp[jobsTmp.length-1]);

                    if(jobsTmp[jobsTmp.length-1]){
                        if(jobsTmp[jobsTmp.length-1].hasOwnProperty("attrs")){
                            extractionId = jobsTmp[jobsTmp.length-1].attrs._id;
                            try{
                                segment.saveSegment(`jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray)}`);
                                agendaObject = jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray;
                                // && el.mageGroupCode == mageGroupCode
                                agendaObject = agendaObject.filter(function (el) {
                                    return el.enterpriseCode == enterpriseCode && el.mageStoreCode == mageStoreCode;
                                });
                                segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);
                                extractedObjectIndex = 0;
                                if(agendaObject.length > 0){ 
                                    agendaObject =  agendaObject.sort((a,b) => b.endTime > a.endTime);
                                    extractedObjectIndex = agendaObject.findIndex(
                                        obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                    );
                                }

                                if(extractedObjectIndex < 0){
                                    extractedObjectIndex = 0;
                                }

                                segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                                segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                                segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);


                                if(agendaObject[extractedObjectIndex].hasOwnProperty("projectId")){
                                    projectId = agendaObject[extractedObjectIndex].projectId;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectId")){
                                    secondProjectId = agendaObject[extractedObjectIndex].secondProjectId;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("solve360Update")){
                                    solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("buildProxies")){
                                    buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("includeMetaData")){
                                    includeMetaData = agendaObject[extractedObjectIndex].includeMetaData;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("userName")){
                                    userName = agendaObject[extractedObjectIndex].userName;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("skipErrorCount")){
                                    skipErrorCount = agendaObject[extractedObjectIndex].skipErrorCount;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("coupon_and_discountCSVFilePath")){
                                    coupon_and_discountCSVFilePath = agendaObject[extractedObjectIndex].coupon_and_discountCSVFilePath;
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("mageManufacturer")){
                                    mageManufacturer = agendaObject[extractedObjectIndex].mageManufacturer;
                  
                  
                                  }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("haltOverRide")){
                                    haltOverRide = agendaObject[extractedObjectIndex].haltOverRide;
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("chart_of_accounts_file_path")){
                                    chart_of_accounts_file_path = agendaObject[extractedObjectIndex].chart_of_accounts_file_path;
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("companyObj")){
                                  companyObj = JSON.parse(agendaObject[extractedObjectIndex].companyObj);
                                  console.log("companyObj?????????????????????????????????????????????????????????",companyObj);
                              }

                              if(agendaObject[extractedObjectIndex].hasOwnProperty("brands")){
                                    console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                    brands = agendaObject[extractedObjectIndex].brands.split("*");

                                    console.log("brands exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",brands);
                                }

                                if(brands.length>1){
                                     const hasPorche = brands.some(item => item.toLowerCase() === 'porche');
                                    if (hasPorche && brands.length > 1) {
                                         mageManufacturer = brands.find(item => item.toLowerCase() !== 'porche');
                                         isPorscheStore = true;
                                       }
                                }else{
                                    if(mageManufacturer == constants.PROCESS_JSON.PORSCHE_STORE_LABEL){
                                         isPorscheStore = true;
                                   } else{
                                    isPorscheStore = false; 
                                 }
                                }


                                if(agendaObject[extractedObjectIndex].hasOwnProperty("dealerAddress")){
                                    dealerAddress = agendaObject[extractedObjectIndex].dealerAddress;
                                    dealerAddress = dealerAddress?dealerAddress.replace(/\n/g, "~"):'';
                                    // dealerAddress = dealerAddress.replace(/\|/g, ",");


                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("companyNumber")){
                                    companyNumber = agendaObject[extractedObjectIndex].companyNumber;
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("projectIds")){
                                     projectIds = agendaObject[extractedObjectIndex].projectIds;
                                     projectIds =  projectIds.split("*");

                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectIdList")){
                                    secondProjectIdList = agendaObject[extractedObjectIndex].secondProjectIdList;
                                    secondProjectIdList = secondProjectIdList.split("*");
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("uniqueId")){
                                  uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                  uniqueId+='-'+Date.now();
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("companyIds")){
                                  console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                  companyIds = agendaObject[extractedObjectIndex].companyIds;
                                  companyIds =  companyIds.replace(new RegExp('\\*', 'g'), ',')
                                  console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyIds);
                                  // companyIds = companyIds.replace(/,\s*$/, "");
                              }
                              if(agendaObject[extractedObjectIndex].hasOwnProperty("testData")){
                                testData = agendaObject[extractedObjectIndex].testData;
                              }  
                                if (agendaObject[extractedObjectIndex].hasOwnProperty("parentName")) {
                               parentName = agendaObject[extractedObjectIndex].parentName;
                                parentName = parentName.replace(/\s*\[.*?\]/, '')  
                                .replace(/\s+/g, '').trim();
                              console.log("Cleaned parentName:", parentName);
                          }

                             if(agendaObject[extractedObjectIndex].hasOwnProperty("stateCode")){
                                    stateCode =agendaObject[extractedObjectIndex].stateCode;
                              }                            


                            } catch(err){
                                console.log(err);
                                segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                            }
                        }
                    }

                    if(skipErrorCount === undefined || skipErrorCount == "" || skipErrorCount == null || !skipErrorCount){
                    skipErrorCount = 0; 
                    }

                    // if(mageManufacturer == constants.PROCESS_JSON.PORSCHE_STORE_LABEL){
                    //     isPorscheStore = true;
                    // } else{
                    //     isPorscheStore = false; 
                    // }
                    modifiedFileName = `${constants.PROCESS_JSON.OUTPUT_PREFIX_VAL}${parentName}-${stateCode}-${companyNumber}-${appUtil.getTimestampForAuditFile()}.zip`

                    updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:enterpriseCode, storeCode: mageStoreCode, dmsType: constants.JOB_TYPE, groupCode:mageGroupCode,projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId,companyObj:companyObj,processFileName:modifiedFileName};
                    console.log(updateSolve360Data);
                    segment.saveSegment(`updateSolve360Data : ${updateSolve360Data}`);

                    console.log('projectId:', projectId);
                    segment.saveSegment(`projectId : ${projectId}`);

                    console.log('secondProjectId:', secondProjectId);
                    segment.saveSegment(`secondProjectId : ${secondProjectId}`);

                    console.log('userName:', userName);
                    segment.saveSegment(`userName : ${userName}`);

                    console.log('solve360Update:', solve360Update);
                    segment.saveSegment(`solve360Update : ${solve360Update}`);

                    console.log('buildProxies:', buildProxies);
                    segment.saveSegment(`buildProxies : ${buildProxies}`);
                    
                    console.log('extractionId:', extractionId);
                    segment.saveSegment(`extractionId : ${extractionId}`);

                    console.log('skipErrorCount:', skipErrorCount);
                    segment.saveSegment(`skipErrorCount : ${skipErrorCount}`);

                    console.log('coupon_and_discountCSVFilePath:', coupon_and_discountCSVFilePath);
                    segment.saveSegment(`coupon_and_discountCSVFilePath : ${coupon_and_discountCSVFilePath}`);

                    console.log('haltOverRide:', haltOverRide);
                    segment.saveSegment(`haltOverRide : ${haltOverRide}`);

                    console.log('chart_of_accounts_file_path:', chart_of_accounts_file_path);
                    segment.saveSegment(`chart_of_accounts_file_path : ${chart_of_accounts_file_path}`);

                    console.log('isPorscheStore:', isPorscheStore);
                    segment.saveSegment(`isPorscheStore : ${isPorscheStore}`);

                    console.log('dealerAddress:', dealerAddress);
                    segment.saveSegment(`dealerAddress : ${dealerAddress}`);

                    console.log('companyNumber:', companyNumber);
                    segment.saveSegment(`companyNumber : ${companyNumber}`);

                    console.log('projectIds:', projectIds);
                    segment.saveSegment(`projectIds : ${projectIds}`);

                    console.log('secondProjectIdList:', secondProjectIdList);
                    segment.saveSegment(`secondProjectIdList : ${secondProjectIdList}`);

                    console.log('uniqueId:', uniqueId);
                    segment.saveSegment(`uniqueId : ${uniqueId}`);
		    
		    console.log('testData:', testData);
                    segment.saveSegment(`testData : ${testData}`);

                    console.log('companyIds:', companyIds);
                    segment.saveSegment(`companyIds : ${companyIds}`);

                    console.log('companyObj:', companyObj);
                    segment.saveSegment(`companyObj : ${companyObj}`);

                    console.log('parentName:', parentName);
                    segment.saveSegment(`parentName : ${parentName}`);
  
                    console.log('modifiedFileName:', modifiedFileName);
                    segment.saveSegment(`modifiedFileName : ${modifiedFileName}`);

                    if(haltOverRide){
                        resumeUser = `${userName}`;
                        console.log('resumeUser:', resumeUser);
                        segment.saveSegment(`resumeUser : ${resumeUser}`);
                        job.attrs.data.isResumed = "true";
                    }

                    updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:enterpriseCode, storeCode: mageStoreCode, dmsType: constants.JOB_TYPE, groupCode:mageGroupCode, resumeUser: resumeUser ? resumeUser : '',projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId,testData:testData,companyObj:companyObj,processFileName:modifiedFileName};
                    console.log(updateSolve360Data);
                    segment.saveSegment(`updateSolve360Data : ${JSON.stringify(updateSolve360Data)}`);

                    let buildProxiesDecider;
                    if(buildProxies){
                        buildProxiesDecider = constants.PROCESS_JSON.OPT_BUILD_PROXY_RO; 
                    } else{
                        buildProxiesDecider = constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO;
                    }

                    await job.save();
                    
                    if (doProxyFromPgDump) {
                        console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>INSIDE do proxy job');
                       spawnInputArray = [
                            constants.PROCESS_JSON.PROCESS_CMD,
                            constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.DEALERTRACK_BUNDLE_DIR,
                            constants.PROCESS_JSON.OPT_BUILD_PROXY_USING, path.join(constants.DEALERTRACK_SCHEDULER_ETI_DIR, extractZip),
                            constants.PROCESS_JSON.OPT_ZAP_INPUT,
                            constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX, constants.DEALERTRACK_DEADLETTER_DIR_PREFIX + '-processed',
                            constants.PROCESS_JSON.OUTPUT_PREFIX, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,
                            constants.PROCESS_JSON.DEALER_ADDRESS,dealerAddress.replace(/\|/g, ","),
                            constants.PROCESS_JSON.BRAND_NAME,mageManufacturer,
                            buildProxiesDecider,
                            isPorscheStore ? constants.PROCESS_JSON.OPT_PORSCHE_STORE : '',
                            constants.PROCESS_JSON.COMPANY_NUMBER,companyNumber,
                            "--uuid",uniqueId,
                            "--performed-by",userName,
                            "--exception-report",true,
                            "--company_ids",companyIds,
                             "--output-file",modifiedFileName
                         ];
                    }

                   else {
                   if(haltOverRide){
                        // Portal update for process xml Resume
                        let todayDate;
                        let attPayload = {};
                        let projectID;
                        let secondProjectID;
                        let inpObjProject;
                        let inpObjSecondProject;
                        let projectIdList;
                        let secondProjectIdList;
                        try{
                        todayDate = new Date().toISOString().slice(0, 10);
                        attPayload = agendaObject[extractedObjectIndex];
                        projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                        projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                        secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                        attPayload['inProjectId'] =  projectID;

                        secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                        attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                        attPayload.in_retrive_ro_request_on = todayDate;
                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                        console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                        if(secondProjectIdList.length>0){
                            inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                            console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                        }
                        } catch(err){
                        console.log(JSON.stringify(err));
                        segment.saveSegment(`CDK : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                        }
                        segment.saveSegment(`CDK : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                        segment.saveSegment(`CDK : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                        try {
                            segment.saveSegment(`CDK : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                            let parsedData = JSON.parse(inpObjProject.inData);
                            let  projectIdList =  parsedData.projectIds.split("*");
                                if(projectIdList){
                                     for(const id of projectIdList){
                                        if(id!=undefined && id!=''){
                                            inpObjProject.inProjectId = id;
                                            portalUpdate.doPayloadAction(inpObjProject);
                                            console.log(`Delertrack Schedule portal call with Project Id RESUME${id}`);
                                            segment.saveSegment(`Dealertrack Schedule portal call with Project Id RESUME${id}`);
                    
                                        }
                                     }
                                } 
                           
                            if(secondProjectIdList.length>0){
                            segment.saveSegment(`CDK : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                            let parsedData = JSON.parse(inpObjSecondProject.inData);
                            let  secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                            if(secondProjectIdList){
                                for(const id of secondProjectIdList){
                                   if(id!=undefined && id!=''){
                                       inpObjSecondProject.inProjectId = id;
                                       portalUpdate.doPayloadAction(inpObjSecondProject);
                                       console.log(`Dealertrack Schedule portal call with Second Project Id Resume${id}`);
                                       segment.saveSegment(`Dealertrack Schedule portal call with Second Project Id RESUME${id}`);
               
                                   }
                                }
                           } 
                            
                            }
                        } catch(error) {
                            console.log("Error:", error);
                            segment.saveSegment(`DEALERTRACK  : doPayloadAction Error - ${JSON.stringify(error)}`); 
                        }
                        //code end for portal update for process xml Resume
                    }

                    var spawnInputArray = [
                            constants.PROCESS_JSON.PROCESS_CMD,
                            constants.PROCESS_JSON.OPT_INPUT_ZIP, path.join(constants.DEALERTRACK_SCHEDULER_ETI_DIR, extractZip),
                            constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.DEALERTRACK_BUNDLE_DIR,
                            constants.PROCESS_JSON.OPT_ZAP_INPUT,
                            constants.PROCESS_JSON.OPT_PERFORM_ZIP,
                            constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX,
                            constants.DEALERTRACK_DEADLETTER_DIR_PREFIX + '-processed',
                            constants.PROCESS_JSON.SKIP_ERROR, skipErrorCount,
                            // constants.PROCESS_JSON.OPT_BUILD_PROXY_RO,
                            buildProxiesDecider,
                            isPorscheStore ? constants.PROCESS_JSON.OPT_PORSCHE_STORE : '',
                            constants.PROCESS_JSON.OUTPUT_PREFIX,
                            constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,
                            constants.PROCESS_JSON.COUPON_AND_DISCOUNT_FILE,
                            coupon_and_discountCSVFilePath,
                            constants.PROCESS_JSON.CHART_OF_ACCOUNTS_FILE,
                            chart_of_accounts_file_path,
                            constants.PROCESS_JSON.HALT_OVER_RIDE,
                            haltOverRide ,
                            constants.PROCESS_JSON.DEALER_ADDRESS,dealerAddress.replace(/\|/g, ","),
                            constants.PROCESS_JSON.COMPANY_NUMBER,companyNumber,
                            constants.PROCESS_JSON.BRAND_NAME,mageManufacturer,
                            "--uuid",uniqueId,
                            "--performed-by",userName,
                            "--exception-report",true,
                            "--company_ids",companyIds,
                            "--output-file",modifiedFileName
                     ] 
                    }

                    const processJson = spawn("bash",
                    spawnInputArray, {
                    cwd: constants.PROCESS_JSON.PROCESS_CMD_PATH,
                    env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
                 }).on('error', function (err) {
                    console.log("error ::", err);
                    segment.saveSegment(`error: ${err}`);
                });
                    console.log(`DealerTrack : Start processing of extraction > ${basename}`);
                    segment.saveSegment(`DealerTrack : Start processing of extraction > ${basename}`);
                    segment.saveSegmentFailure(`DealerTrack : Start processing of extraction > ${basename}`, uniqueFailLogName);
                    process.stdin.pipe(processJson.stdin);

                    processJson.stdout.on("data", async (data) => {
                        console.log(`stdout: ${data}`);
                        stdOutArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                        await job.touch();

                     data = data.toString('utf8');

                     
                                                if(data.includes('Total Ros Count:-')){
                                                 segment.saveSegment(`Total Ros Count55555555555,${data}`);
                                                 console.log('file generated',data.split(':')[1]);
                                                 segment.saveSegment(`Total Ro90999999999,${data.split(':')[1]}`);
                                                 totalRoCount = data.split(':-')[1];
                                                 segment.saveSegment(`totalRoCount666666,${totalRoCount}`);
                     
                                                 
                                               }else{
                                               console.log("failed to generate1212 file")
                                              } 
                     




                    let processorStatus;
                    if(data.includes('Processor status')){
                         segment.saveSegment('Processor statu for UI',data);
                         console.log('file generated',data.split(':')[1]);
                         processorStatus = data.split(':')[1];
                         segment.saveSegment('processorStatus',processorStatus);
                         await SetProcessJobStatus.setProcessJobStatusForRunningJob(basename,processorStatus);
                       }else{
                       console.log("failed to generate file")
                      }

                        segment.saveSegment(`stdout: ${data}`);
                        segment.saveSegmentFailure(`stdout: ${data}`, uniqueFailLogName);
                    });

                    processJson.stderr.on("data", async (data) => {
                        console.log(`stderr: ${data}`);
                        stdErrorArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                        await job.touch();
                        segment.saveSegment(`stderr: ${data}`);
                        segment.saveSegmentFailure(`stderr: ${data}`, uniqueFailLogName);
                    });

                    processJson.on("close", async (code) => {

                   const filePath = '/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception_tag/All_exception_details.csv';
                    if (fs.existsSync(filePath)) {
                      fs.createReadStream(filePath)
                      .pipe(csv1())
                      .on('data', (row) => {
                      const type = row['Type'];
                       if (type) {
                          exceptionTypeCounts[type] = (exceptionTypeCounts[type] || 0) + 1;
                        }
                    })
                     .on('end', () => {
                     console.log("exceptionTypeCounts", exceptionTypeCounts);
                     segment.saveSegment(`exceptionTypeCounts: ${JSON.stringify(exceptionTypeCounts)}`);
                  });
               } else {
                   console.error(`File not found: ${filePath}`);
                   segment.saveSegment(`Error: File not found at path ${filePath}`);
                }

                        var message = "n/a";
                        var status = false;
                
                        if (code == constants.STATUS_CODE.SUCCESS) {
                            status = true;
                            message = "Success";
                        } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                            message = "Extraction failed, general death";
                            job.fail(new Error(`Error: ${message}`));
                        } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                            message = "Extraction failed, moved to dead-letter path";
                            job.fail(new Error(`Error: ${message}`));
                        }
                        if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                            message = "Extraction failed, Zip File Processing Failed,  ";
                            status = false;
                        }

                        

                        var deadLetterPath = `${process.env.DEALERTRACK_WORK_DIR}/dead-letter-processed`;
                        var errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                        if (stdOutArray && stdOutArray.includes(errResp)) {
                            message += errResp
                            status = false;
                        }

                        if(stdErrorArray && !haltOverRide){

                          stdErrorArray.forEach(async(v,i) =>{
                                if(v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)){
                                    message = "Halt";
                                    await SetProcessJobStatus.setProcessJobStatusForDealerTrack(enterpriseCode,mageStoreCode,message);
                                    haltIdentifier = true;
                                    status = false;
                                }
                            })
                            try{

                                segment.saveSegment(`!!!!!!!!!!!!!!!!!!!!!!!!!!!!Before halt file moving code!!!!!!!!!!!!!!!!!!!!!!!!!`);
                                if (stdOutArray && stdOutArray.includes(errResp) && message == 'Halt' && !haltOverRide) {
                                    segment.saveSegment(`!!!!!!!!!!!!!!!!!!!!!!!!!!!!Inside halt file moving code!!!!!!!!!!!!!!!!!!!!!!!!!`);
                                    let deadLetterFilePath = deadLetterPath + '/' + basename;
                                    let haltFilePath = '/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/scheduler-temp/halt/'+ basename;
                                    if(fs.existsSync(deadLetterFilePath)){
                                        // File destination.txt will be created or overwritten by default.
                                        segment.saveSegment(`Inside deadletter path`);
                                        fs.copyFile(deadLetterFilePath, haltFilePath, (err) => {
                                            // if(err) throw err;
                                            // console.log(`${deadLetterFilePath} was copied to ${haltFilePath}`);
                                            if(err)
                                            {
                                                console.log('error',err);
                                                segment.saveSegment(`Error while copying halt  file ${err}`);
                                            }
                                            else
                                            {
                                             console.log(`${deadLetterFilePath} was copied to ${haltFilePath}`);
                                             segment.saveSegment(`File moved to halt path${haltFilePath}`);
                                            }
                                        });
                                    } else{
                                        console.log(`${deadLetterFilePath} not exist!`);
                                        segment.saveSegment(`${deadLetterFilePath} not exist!`);
                                    }
                            }
                            } catch(err){
                              console.log(err);
                            }
                        }

                        var inventory_ro_count;
                        var inventory_ro_Array;
                        var inventory_ro_FilePath ="/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/inventory_ro.csv";




                        
                        if (fs.existsSync(inventory_ro_FilePath)) {
                            console.log(
                              `The Inventory Ro File exists: ${inventory_ro_FilePath}`
                            );
                            segment.saveSegment(
                              `The Inventory RO File exists: ${inventory_ro_FilePath}`
                            );
                            segment.saveSegmentFailure(
                                `The Inventory RO File exists: ${inventory_ro_FilePath}`,
                              storeCode
                            );
                
                            inventory_ro_Array = await csv().fromFile(
                              inventory_ro_FilePath
                            );
                
                            if (inventory_ro_Array) {
                              if (inventory_ro_Array.length) {
                                console.log(
                                  `inventory_ro_Array.length): ${inventory_ro_Array.length}`
                                );
                                segment.saveSegment(
                                  `inventory_ro_Array.length: ${inventory_ro_Array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `inventory_ro_Array.length: ${inventory_ro_Array.length}`,
                                  storeCode
                                );
                
                                inventory_ro_count = inventory_ro_Array.length;
                                console.log('Inventory RO Count',inventory_ro_count);
                                // warningObj.inventory_ro_count = inventory_ro_count;
                                // if (company_no_not_matching_count)
                                //   warningObj.invalidmiscpaytypeFilePath =
                                //     invalidmiscpaytypeFilePath;
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`Inventory RO Count: ${inventory_ro_count}`);
                            segment.saveSegment(
                              `inventory_ro_count: ${inventory_ro_count}`
                            );
                            segment.saveSegmentFailure(
                              `inventory_ro_count: ${inventory_ro_count}`,
                              storeCode
                            );
                          }
                       console.log(`DealerTrack : JSON processing job for Store ${storeName} exited with code ${code}`);
                        segment.saveSegment(`DealerTrack : JSON processing job for Store ${storeName} exited with code ${code}`);
                        segment.saveSegmentFailure(`DealerTrack : JSON processing job for Store ${storeName} exited with code ${code}`, uniqueFailLogName)

                        let failureDirectory = process.cwd() + '/logs/DealerTrack/failure/';
                        let failurelogFile = failureDirectory + uniqueFailLogName + '.log';

                        var extractionErrorResponse;
                        var errorwarningMessage;
                        var warningObj = {};
                        warningObj.scheduled_by = userName;
                        if(testData){
                          warningObj.testData = true;
                          warningObj.userName = userName;
                      }
                        warningObj.skipErrorCount = skipErrorCount;

                        var closedRODetailExtractionErrorResponse;
                        var closedRODetailErrorwarningMessage;

                        var vehicleExtractionErrorResponse;
                        var vehicleErrorwarningMessage;

                        var customerExtractionErrorResponse;
                        var customerErrorwarningMessage;

                        var glDeatilExtractionErrorResponse;
                        var glDeatilErrorwarningMessage;


                        var closedRODetailExtractionCount = 0;
                        var vehicleExtractionErrorCount = 0;
                        var customerExtractionErrorCount = 0;
                        var glDeatilExtractionErrorCount = 0;
                        let couponAndDiscountWarningMessage;
                        if(extractionId){
                            extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(extractionId, 'DealerTrack');
                            if(extractionErrorResponse.status){
                                let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                                let tmpDescritionArray = [];
                                resp.forEach(e => {
                                    tmpDescritionArray.push(e.description + ':- ' + e.errorMessage + '\n');
                    
                                });
                                errorwarningMessage = tmpDescritionArray;
                                
                            }

                            closedRODetailExtractionErrorResponse = await extractionError.displayDealerTrackGeneralExtractionError(extractionId, 'closedrodetail');
                            if(closedRODetailExtractionErrorResponse.status){
                                let resp = JSON.parse(JSON.stringify(closedRODetailExtractionErrorResponse.response))
                                let tmpDescritionArray = [];
                                resp.forEach(e => {
                                    tmpDescritionArray.push(e.description + ':- ' + e.errorMessage + '\n');
                                });
                                closedRODetailErrorwarningMessage = tmpDescritionArray;
                                if(closedRODetailErrorwarningMessage){
                                    closedRODetailExtractionCount = closedRODetailErrorwarningMessage.length; 
                                }
                            }

                            //warning message for vehicle detail { error other than validation error, skip error}
                            vehicleExtractionErrorResponse = await extractionError.displayDealerTrackGeneralExtractionError(extractionId, 'vehicledetail');
                            // console.log('vehicleExtractionErrorResponse:',vehicleExtractionErrorResponse);
                            if(vehicleExtractionErrorResponse.status){
                                let resp = JSON.parse(JSON.stringify(vehicleExtractionErrorResponse.response))
                                let tmpDescritionArray = [];
                                resp.forEach(e => {
                                    tmpDescritionArray.push(e.description + ':- ' + e.errorMessage + '\n');
                                });
                                vehicleErrorwarningMessage = tmpDescritionArray;
                                if(vehicleErrorwarningMessage){
                                    vehicleExtractionErrorCount = vehicleErrorwarningMessage.length;
                                }
                            }

                            //warning message for customer detail { error other than validation error, skip error}
                            customerExtractionErrorResponse = await extractionError.displayDealerTrackGeneralExtractionError(extractionId, 'customerdetail');
                            // console.log('customerExtractionErrorResponse:',customerExtractionErrorResponse);
                            if(customerExtractionErrorResponse.status){
                                let resp = JSON.parse(JSON.stringify(customerExtractionErrorResponse.response))
                                let tmpDescritionArray = [];
                                resp.forEach(e => {
                                    tmpDescritionArray.push(e.description + ':- ' + e.errorMessage + '\n');
                                });
                                customerErrorwarningMessage = tmpDescritionArray;
                                if(customerErrorwarningMessage){
                                    customerExtractionErrorCount = customerErrorwarningMessage.length;
                                }
                            }

                            //warning message for GLDetail detail { error other than validation error, skip error}
                            glDeatilExtractionErrorResponse = await extractionError.displayDealerTrackGeneralExtractionError(extractionId, 'gldetail');
                            // console.log('glDeatilExtractionErrorResponse:',glDeatilExtractionErrorResponse);
                            if(glDeatilExtractionErrorResponse.status){
                                let resp = JSON.parse(JSON.stringify(glDeatilExtractionErrorResponse.response))
                                let tmpDescritionArray = [];
                                resp.forEach(e => {
                                    tmpDescritionArray.push(e.description + ':- ' + e.errorMessage + '\n');
                                });
                                glDeatilErrorwarningMessage = tmpDescritionArray;
                                if(glDeatilErrorwarningMessage){
                                    glDeatilExtractionErrorCount = glDeatilErrorwarningMessage.length;
                                }
                            }

                        } 
                         
                        try{
                            let csvFilePath = constants.COUPON_AND_DISCOUNT_EXCEPTION_CSV_FILE_PATH;
                            let jsonArray;
                            let coupon_number_array = [];
                            var coupon_numbers;
                            let unique_coupon_numbers;
                            if (fs.existsSync(csvFilePath)) {
                                console.log("The csv file file exists");
                                jsonArray = await csv().fromFile(csvFilePath);
                                // console.log(jsonArray);
                                jsonArray.forEach(element => {
                                   if(element.CouponNumber != '0' && element.CouponNumber !='99999' ){
                                    coupon_number_array.push(element.CouponNumber);
                                   }
                                });
                                unique_coupon_numbers = [...new Set(coupon_number_array)];
                                coupon_numbers = unique_coupon_numbers.join(', ');
                                try {
                                    fs.unlinkSync(csvFilePath);
                                  } catch(err) {
                                    console.error(err)
                                    segment.saveSegment(`DealerTrack : Delete operation  of  coupon_and_discount_exception.csv have error ${err}`);
                                    segment.saveSegmentFailure(`DealerTrack : Delete operation  of  coupon_and_discount_exception.csv have error ${err}`, uniqueFailLogName);
                                  }
                            }
                           
                        } catch(err){
                            segment.saveSegment(`DealerTrack : Read operation  of  coupon_and_discount_exception.csv have error ${err}`);
                            segment.saveSegmentFailure(`DealerTrack : Read operation  of  coupon_and_discount_exception.csv have error ${err}`, uniqueFailLogName);
                        }

                        let roAccountDescExceptionCount;
                        try{
                            roAccountDescExceptionCount = await getRoAccountingDescExceptionCount();
                        } catch(err){
                            console.log(err);
                            segment.saveSegment(
                                `DealerTrack : Read operation  of  coupon_and_discount_exception.csv have error ${err}`
                              );
                              segment.saveSegmentFailure(
                                `DealerTrack : Read operation  of  coupon_and_discount_exception.csv have error ${err}`,
                                uniqueFailLogName
                            );
                        }

                        let coa_total_count, coaExceptionWarningMessage;
                        try{
                         
                            if(fs.existsSync(constants.COA_TOTAL_COUNT_FILE_PATH)) {
                                console.log('File exists');
                                coa_total_count = fs.readFileSync(constants.COA_TOTAL_COUNT_FILE_PATH);
                                console.log('coa_total_count:', coa_total_count);
                                fs.unlinkSync(constants.COA_TOTAL_COUNT_FILE_PATH);
                                segment.saveSegment(
                                    `DealerTrack : coa_total_count: ${coa_total_count}`
                                  );
                                  segment.saveSegmentFailure(
                                    `DealerTrack : coa_total_count: ${coa_total_count}`,
                                    uniqueFailLogName
                                );
                              } else {
                                console.error('File does not exist');
                              }

                        } catch(err){
                            console.log(err);
                            segment.saveSegment(
                                `DealerTrack : Read/Delete operation  of coa total count from file have error ${err}`
                              );
                              segment.saveSegmentFailure(
                                `DealerTrack : Read/Delete operation of coa total count from file have error ${err}`,
                                uniqueFailLogName
                            );
                        }


                        let customer_total_count, customerExceptionWarningMessage;
                        try{

                            if (fs.existsSync(constants.CUSTOMER_TOTAL_COUNT_FILE_PATH)) {
                                console.log('File exists');

                            customer_total_count = fs.readFileSync(constants.CUSTOMER_TOTAL_COUNT_FILE_PATH);
                            console.log('customer_total_count:', customer_total_count);
                            fs.unlinkSync(constants.CUSTOMER_TOTAL_COUNT_FILE_PATH);
                            segment.saveSegment(
                                `DealerTrack : customer_total_count: ${customer_total_count}`
                              );
                              segment.saveSegmentFailure(
                                `DealerTrack : customer_total_count: ${customer_total_count}`,
                                uniqueFailLogName
                            );
                                
                              } else {
                                console.error('File does not exist');
                              }
                              





                        } catch(err){
                            console.log(err);
                            segment.saveSegment(
                                `DealerTrack : Read/Delete operation  of customer total count from file have error ${err}`
                              );
                              segment.saveSegmentFailure(
                                `DealerTrack : Read/Delete operation of customer total count from file have error ${err}`,
                                uniqueFailLogName
                            );
                        }


                        // try{
                        //     if(coupon_and_discountCSVFilePath){
                        //         if (fs.existsSync(coupon_and_discountCSVFilePath)) {
                        //             fs.unlinkSync(coupon_and_discountCSVFilePath);  
                        //         }
                        //     }
                        // } catch(error){
                        //     segment.saveSegment(`DealerTrack : Delete operation  of  coupon_and_discount.csv have error ${err}`);

                        // }
                    
                        console.log('errorwarningMessage:',errorwarningMessage);
                        // console.log('closedRODetailErrorwarningMessage:',closedRODetailErrorwarningMessage);
                        // console.log('vehicleErrorwarningMessage:',vehicleErrorwarningMessage);

                        // console.log('customerErrorwarningMessage:',customerErrorwarningMessage);
                        // console.log('glDeatilErrorwarningMessage:',glDeatilErrorwarningMessage);

                        // coa_total_count = 0;
                        // customer_total_count = 0;

                        console.log('coupon_numbers', coupon_numbers);
                        console.log('roAccountDescExceptionCount:', roAccountDescExceptionCount);

                        if(coa_total_count == 0){
                            coaExceptionWarningMessage = constants.DEALERTRACK.COA_EXCEPTION_MESSAGE;
                            console.log('coaExceptionWarningMessage:', coaExceptionWarningMessage);
                        }

                        if(customer_total_count > 0){
                            customerExceptionWarningMessage = constants.DEALERTRACK.CUSTOMER_EXCEPTION_MESSAGE + ': ' + customer_total_count;
                            console.log('customerExceptionWarningMessage:', customerExceptionWarningMessage);
                        }
                   
                        // console.log('couponAndDiscountFileNotUploadedWarningMessage:', couponAndDiscountFileNotUploadedWarningMessage);
                        
                        if(errorwarningMessage){
                            if(errorwarningMessage.length > 0){
                                warningObj.errorwarningMessage = errorwarningMessage;
                            }
                        }
                        
                        if(closedRODetailExtractionCount <= skipErrorCount){
                            warningObj.closedRODetailErrorwarningMessage = closedRODetailErrorwarningMessage;
                        }
                        
                        if(vehicleExtractionErrorCount <= skipErrorCount){
                            warningObj.vehicleErrorwarningMessage = vehicleErrorwarningMessage;
                        }

                        if(customerExtractionErrorCount <= skipErrorCount){
                            warningObj.customerErrorwarningMessage = customerErrorwarningMessage;
                        }

                        if(glDeatilExtractionErrorCount <= skipErrorCount){
                            warningObj.glDeatilErrorwarningMessage = glDeatilErrorwarningMessage;
                            warningObj.glDeatilExtractionErrorCount = glDeatilExtractionErrorCount;
                        }
                           
                        if(glDeatilExtractionErrorCount >= skipErrorCount){
                            // warningObj.glDeatilErrorwarningMessage = glDeatilErrorwarningMessage;
                            warningObj.glDeatilExtractionErrorCount = glDeatilExtractionErrorCount;
                        }
 
 
                        
                        if(coupon_numbers){
                            // coupon_numbers = coupon_numbers.replace("0", '');
                            // coupon_numbers = coupon_numbers.replace("99999", '');
                            coupon_numbers =  coupon_numbers.trim();
                            coupon_numbers = coupon_numbers.replace(/^,|,$/g, '');
                            coupon_numbers = coupon_numbers.replace(/,/g, ';');
                            // coupon_numbers = coupon_numbers.replace(/,\s*$/, "");
                            if(coupon_numbers.trim().length > 0){
                                warningObj.couponAndDiscountWarningMessage = coupon_numbers;
                            }
                        }

                        if(roAccountDescExceptionCount){
                            if(roAccountDescExceptionCount > 0){
                                warningObj.roAccountDescriptionWarningMessage = roAccountDescExceptionCount; 
                                warningObj.roAccountingDescCsvFilePath = constants.RO_ACCOUNTING_DESCRIPTION_EXCEPTION_CSV_FILE_PATH;
                            }
                        }

                        // if(couponAndDiscountFileNotUploadedWarningMessage){
                        //     if(couponAndDiscountFileNotUploadedWarningMessage.length > 0){
                        //         warningObj.couponAndDiscountFileNotUploadedWarningMessage = couponAndDiscountFileNotUploadedWarningMessage;
                        //     }
                        // }

                        if(coaExceptionWarningMessage){
                            if(coaExceptionWarningMessage.length > 0){
                                warningObj.coaExceptionWarningMessage = coaExceptionWarningMessage;
                            }
                        }

                        if(customerExceptionWarningMessage){
                            if(customerExceptionWarningMessage.length > 0){
                                warningObj.customerExceptionWarningMessage = customerExceptionWarningMessage;
                            }
                        }
                           
                        var company_no_not_matching_count;
                        var company_no_not_matching_array;
                        var company_no_not_matching_file_path=
                        "/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/company_no_not_matching_count.csv";

                        
                        // var company_no_not_matching_file_path=
                        // "/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/inventory_ro.csv";
                       


                        if (fs.existsSync(company_no_not_matching_file_path)) {
                            console.log(
                              `The company number not matching  Csv File exists: ${company_no_not_matching_file_path}`
                            );
                            segment.saveSegment(
                              `The company number not matching Csv File exists: ${company_no_not_matching_file_path}`
                            );
                            segment.saveSegmentFailure(
                              `The company number not matching Csv File exists: ${company_no_not_matching_file_path}`,
                              storeCode
                            );
                
                            company_no_not_matching_array = await csv().fromFile(
                              company_no_not_matching_file_path
                            );
                
                            if (company_no_not_matching_array) {
                              if (company_no_not_matching_array.length) {
                                console.log(
                                  `company_no_not_matching_array.length: ${company_no_not_matching_array.length}`
                                );
                                segment.saveSegment(
                                  `company_no_not_matching_array.length: ${company_no_not_matching_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `company_no_not_matching_array.length: ${company_no_not_matching_array.length}`,
                                  storeCode
                                );
                
                                company_no_not_matching_count = company_no_not_matching_array.length;
                                console.log('company_no_not_matching_count',company_no_not_matching_count);
                                warningObj.company_no_not_matching_count = company_no_not_matching_count;
                                if (company_no_not_matching_count){
                                    warningObj.company_no_not_matching_file_path =
                                    company_no_not_matching_file_path;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`company_no_not_matching_count: ${company_no_not_matching_count}`);
                            segment.saveSegment(
                              `company_no_not_matching_count: ${company_no_not_matching_count}`
                            );
                            segment.saveSegmentFailure(
                              `company_no_not_matching_count: ${company_no_not_matching_count}`,
                              storeCode
                            );
                          }
    

                          var grouped_team_work_count;
                          var grouped_team_work_array;
                          var grouped_team_work_file_path=
                          "/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/grouped-team-work.csv";


                          if (fs.existsSync(grouped_team_work_file_path)) {
                            console.log(
                              `The grouped team work Csv File exists: ${grouped_team_work_file_path}`
                            );
                            segment.saveSegment(
                              `The grouped team work Csv File exists: ${grouped_team_work_file_path}`
                            );
                            segment.saveSegmentFailure(
                              `The grouped team Csv File exists: ${grouped_team_work_file_path}`,
                              storeCode
                            );
                
                             grouped_team_work_array= await csv().fromFile(
                              grouped_team_work_file_path
                            );
                
                            if (grouped_team_work_array) {
                              if (grouped_team_work_array.length) {
                                console.log(
                                  `grouped_team_work_array.length: ${grouped_team_work_array.length}`
                                );
                                segment.saveSegment(
                                  `grouped_team_work_array.length: ${grouped_team_work_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `grouped_team_work_array.length: ${grouped_team_work_array.length}`,
                                  storeCode
                                );
                
                                grouped_team_work_count = grouped_team_work_array.length;
                                console.log('grouped_team_work_count',grouped_team_work_count);
                                warningObj.grouped_team_work_count = grouped_team_work_count;
                                if (grouped_team_work_count){
                                    warningObj.grouped_team_work_file_path =
                                    grouped_team_work_file_path;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`grouped_team_work_count: ${grouped_team_work_count}`);
                            segment.saveSegment(
                              `grouped_team_work_count: ${grouped_team_work_count}`
                            );
                            segment.saveSegmentFailure(
                              `grouped_team_work_count: ${grouped_team_work_count}`,
                              storeCode
                            );
                          }
    
                         
                          var new_line_type_count;
                          var new_line_type_array;
                          var new_line_type_file_path="/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/new-line-type.csv";


                          if (fs.existsSync(new_line_type_file_path)) {
                            console.log(
                              `The new_line_type_file_path: ${new_line_type_file_path}`
                            );
                            segment.saveSegment(
                              `new_line_type_file_path exists: ${new_line_type_file_path}`
                            );
                            segment.saveSegmentFailure(
                              `new_line_type_file_path exists: ${new_line_type_file_path}`,
                              storeCode
                            );
                
                            new_line_type_array= await csv().fromFile(
                                new_line_type_file_path
                            );
                
                            if (new_line_type_array) {
                              if (new_line_type_array.length) {
                                console.log(
                                  `new_line_type_array.length: ${new_line_type_array.length}`
                                );
                                segment.saveSegment(
                                  `new_line_type_array.length: ${new_line_type_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `new_line_type_array.length: ${new_line_type_array.length}`,
                                  storeCode
                                );
                
                                new_line_type_count = new_line_type_array.length;
                                console.log('new_line_type_count',new_line_type_count);
                                warningObj.new_line_type_count = new_line_type_count;
                                if (new_line_type_count){
                                    warningObj.new_line_type_file_path =
                                    new_line_type_file_path;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`new_line_type_count: ${new_line_type_count}`);
                            segment.saveSegment(
                              `new_line_type_count: ${new_line_type_count}`
                            );
                            segment.saveSegmentFailure(
                              `new_line_type_count: ${new_line_type_count}`,
                              storeCode
                            );
                          }

                          var negative_coupon_count;
                          var negative_coupon_array;
                          var negative_coupon_exception_filepath='/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/negative_coupon.csv'


                          if (fs.existsSync(negative_coupon_exception_filepath)) {
                            console.log(
                              `The negative_coupon_exception_filepath: ${negative_coupon_exception_filepath}`
                            );
                            segment.saveSegment(
                              `negative_coupon_exception_filepath: ${negative_coupon_exception_filepath}`
                            );
                            segment.saveSegmentFailure(
                              `negative_coupon_exception_filepath: ${negative_coupon_exception_filepath}`,
                              storeCode
                            );
                
                            negative_coupon_array= await csv().fromFile(
                              negative_coupon_exception_filepath
                            );
                
                            if (negative_coupon_array) {
                              if (negative_coupon_array.length) {
                                console.log(
                                  `negative_coupon_array.length: ${negative_coupon_array.length}`
                                );
                                segment.saveSegment(
                                  `negative_coupon_array.length: ${negative_coupon_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `negative_coupon_array.length: ${negative_coupon_array.length}`,
                                  storeCode
                                );
                
                                negative_coupon_count = negative_coupon_array.length;
                                console.log('negative_coupon_count',negative_coupon_count);
                                warningObj.negative_coupon_count = negative_coupon_count;
                                if (negative_coupon_count){
                                    warningObj.negative_coupon_exception_filepath =
                                    negative_coupon_exception_filepath;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`negative_coupon_count: ${negative_coupon_count}`);
                            segment.saveSegment(
                              `negative_coupon_count: ${negative_coupon_count}`
                            );
                            segment.saveSegmentFailure(
                              `negative_coupon_count: ${negative_coupon_count}`,
                              storeCode
                            );
                          }
                          

                          var labor_with_zero_sale_nonzero_cost_count;
                          var labor_with_zero_sale_nonzero_cost_array;
                          var labor_with_zero_sale_nonzero_cost_exception_filepath='/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/labor_with_zero_sale_nonzero_cost.csv'


                          if (fs.existsSync(labor_with_zero_sale_nonzero_cost_exception_filepath)) {
                            console.log(
                              `The labor_with_zero_sale_nonzero_cost_exception_filepath: ${labor_with_zero_sale_nonzero_cost_exception_filepath}`
                            );
                            segment.saveSegment(
                              `labor_with_zero_sale_nonzero_cost_exception_filepath: ${labor_with_zero_sale_nonzero_cost_exception_filepath}`
                            );
                            segment.saveSegmentFailure(
                              `labor_with_zero_sale_nonzero_cost_exception_filepath: ${labor_with_zero_sale_nonzero_cost_exception_filepath}`,
                              storeCode
                            );
                
                            labor_with_zero_sale_nonzero_cost_array= await csv().fromFile(
                              labor_with_zero_sale_nonzero_cost_exception_filepath
                            );
                
                            if (labor_with_zero_sale_nonzero_cost_array) {
                              if (labor_with_zero_sale_nonzero_cost_array.length) {
                                console.log(
                                  `labor_with_zero_sale_nonzero_cost_array.length: ${labor_with_zero_sale_nonzero_cost_array.length}`
                                );
                                segment.saveSegment(
                                  `labor_with_zero_sale_nonzero_cost_array.length: ${labor_with_zero_sale_nonzero_cost_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `labor_with_zero_sale_nonzero_cost_array.length: ${labor_with_zero_sale_nonzero_cost_array.length}`,
                                  storeCode
                                );
                
                                labor_with_zero_sale_nonzero_cost_count = labor_with_zero_sale_nonzero_cost_array.length;
                                console.log('labor_with_zero_sale_nonzero_cost_count',labor_with_zero_sale_nonzero_cost_count);
                                warningObj.labor_with_zero_sale_nonzero_cost_count = labor_with_zero_sale_nonzero_cost_count;
                                if (labor_with_zero_sale_nonzero_cost_count){
                                    warningObj.labor_with_zero_sale_nonzero_cost_exception_filepath =
                                    labor_with_zero_sale_nonzero_cost_exception_filepath;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`labor_with_zero_sale_nonzero_cost_count: ${labor_with_zero_sale_nonzero_cost_count}`);
                            segment.saveSegment(
                              `labor_with_zero_sale_nonzero_cost_count: ${labor_with_zero_sale_nonzero_cost_count}`
                            );
                            segment.saveSegmentFailure(
                              `labor_with_zero_sale_nonzero_cost_count: ${labor_with_zero_sale_nonzero_cost_count}`,
                              storeCode
                            );
                          }
                         
              
                    
                          var gl_missing_ros_count;
                          var gl_missing_ros_array;
                          var gl_missing_ros_exception_filepath='/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/gl_missing_ros.csv'


                          if (fs.existsSync(gl_missing_ros_exception_filepath)) {
                            console.log(
                              `The gl_missing_ros_exception_filepath: ${gl_missing_ros_exception_filepath}`
                            );
                            segment.saveSegment(
                              `gl_missing_ros_exception_filepath: ${gl_missing_ros_exception_filepath}`
                            );
                            segment.saveSegmentFailure(
                              `gl_missing_ros_exception_filepath: ${gl_missing_ros_exception_filepath}`,
                              storeCode
                            );
                
                            gl_missing_ros_array= await csv().fromFile(
                              gl_missing_ros_exception_filepath
                            );
                
                            if (gl_missing_ros_array) {
                              if (gl_missing_ros_array.length) {
                                console.log(
                                  `gl_missing_ros_array.length: ${gl_missing_ros_array.length}`
                                );
                                segment.saveSegment(
                                  `gl_missing_ros_array.length: ${gl_missing_ros_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `gl_missing_ros_array.length: ${gl_missing_ros_array.length}`,
                                  storeCode
                                );
                
                                gl_missing_ros_count = gl_missing_ros_array.length;
                                console.log('gl_missing_ros_count',gl_missing_ros_count);
                                warningObj.gl_missing_ros_count = gl_missing_ros_count;
                                if (gl_missing_ros_count){
                                    warningObj.gl_missing_ros_exception_filepath =
                                    gl_missing_ros_exception_filepath;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`gl_missing_ros_count: ${gl_missing_ros_count}`);
                            segment.saveSegment(
                              `gl_missing_ros_count: ${gl_missing_ros_count}`
                            );
                            segment.saveSegmentFailure(
                              `gl_missing_ros_count: ${gl_missing_ros_count}`,
                              storeCode
                            );
                          }


                          var coupon_discount_basis_amount_mismatch_exception_count;
                          var coupon_discount_basis_amount_mismatch_exception_array;
                          var coupon_discount_basis_amount_mismatch_exception_filepath='/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/coupon_discount_basis_amount_mismatch_exception_filepath.csv'


                          if (fs.existsSync(coupon_discount_basis_amount_mismatch_exception_filepath)) {
                            console.log(
                              `The coupon_discount_basis_amount_mismatch_exception_filepath: ${coupon_discount_basis_amount_mismatch_exception_filepath}`
                            );
                            segment.saveSegment(
                              `coupon_discount_basis_amount_mismatch_exception_filepath: ${coupon_discount_basis_amount_mismatch_exception_filepath}`
                            );
                            segment.saveSegmentFailure(
                              `coupon_discount_basis_amount_mismatch_exception_filepath: ${coupon_discount_basis_amount_mismatch_exception_filepath}`,
                              storeCode
                            );
                
                            coupon_discount_basis_amount_mismatch_exception_array= await csv().fromFile(
                              coupon_discount_basis_amount_mismatch_exception_filepath
                            );
                
                            if (coupon_discount_basis_amount_mismatch_exception_array) {
                              if (coupon_discount_basis_amount_mismatch_exception_array.length) {
                                console.log(
                                  `coupon_discount_basis_amount_mismatch_exception_array.length: ${coupon_discount_basis_amount_mismatch_exception_array.length}`
                                );
                                segment.saveSegment(
                                  `coupon_discount_basis_amount_mismatch_exception_array.length: ${coupon_discount_basis_amount_mismatch_exception_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `coupon_discount_basis_amount_mismatch_exception_array.length: ${coupon_discount_basis_amount_mismatch_exception_array.length}`,
                                  storeCode
                                );
                
                                coupon_discount_basis_amount_mismatch_exception_count = coupon_discount_basis_amount_mismatch_exception_array.length;
                                console.log('coupon_discount_basis_amount_mismatch_exception_count',coupon_discount_basis_amount_mismatch_exception_count);
                                warningObj.coupon_discount_basis_amount_mismatch_exception_count = coupon_discount_basis_amount_mismatch_exception_count;
                                if (coupon_discount_basis_amount_mismatch_exception_count){
                                    warningObj.coupon_discount_basis_amount_mismatch_exception_filepath =
                                    coupon_discount_basis_amount_mismatch_exception_filepath;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`coupon_discount_basis_amount_mismatch_exception_count: ${coupon_discount_basis_amount_mismatch_exception_count}`);
                            segment.saveSegment(
                              `coupon_discount_basis_amount_mismatch_exception_count: ${coupon_discount_basis_amount_mismatch_exception_count}`
                            );
                            segment.saveSegmentFailure(
                              `coupon_discount_basis_amount_mismatch_exception_count: ${coupon_discount_basis_amount_mismatch_exception_count}`,
                              storeCode
                            );
                          }

                          var labor_with_no_paytype_exception_count;
                          var labor_with_no_paytype_exception_count_array;
                          var labor_with_no_paytype_exception_filepath='/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/labor_with_no_paytype.csv'

                          if (fs.existsSync(labor_with_no_paytype_exception_filepath)) {
                            console.log(
                              `The Labor With No Paytype Exception Filepath: ${labor_with_no_paytype_exception_filepath}`
                            );
                            segment.saveSegment(
                              `The Labor With No Paytype Exception Filepath: ${labor_with_no_paytype_exception_filepath}`
                            );
                            segment.saveSegmentFailure(
                              `The Labor With No Paytype Exception Filepath: ${labor_with_no_paytype_exception_filepath}`,
                              storeCode
                            );
                
                              labor_with_no_paytype_exception_count_array= await csv().fromFile(
                                labor_with_no_paytype_exception_filepath
                            );
                
                            if (labor_with_no_paytype_exception_count_array) {
                              if (labor_with_no_paytype_exception_count_array.length) {
                                console.log(
                                  `The Labor With No Paytype Exception array length: ${labor_with_no_paytype_exception_count_array.length}`
                                );
                                segment.saveSegment(
                                  `The Labor With No Paytype Exception array length: ${labor_with_no_paytype_exception_count_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `The Labor With No Paytype Exception array length: ${labor_with_no_paytype_exception_count_array.length}`,
                                  storeCode
                                );
                
                                labor_with_no_paytype_exception_count = labor_with_no_paytype_exception_count_array.length;
                                console.log('The Labor With No Paytype Exception count:',labor_with_no_paytype_exception_count);
                                warningObj.labor_with_no_paytype_exception_count = labor_with_no_paytype_exception_count;
                                if (labor_with_no_paytype_exception_count){
                                    warningObj.labor_with_no_paytype_exception_filepath =
                                    labor_with_no_paytype_exception_filepath;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`labor_with_no_paytype_exception_count: ${labor_with_no_paytype_exception_count}`);
                            segment.saveSegment(
                              `labor_with_no_paytype_exception_count: ${labor_with_no_paytype_exception_count}`
                            );
                            segment.saveSegmentFailure(
                              `labor_with_no_paytype_exception_count: ${labor_with_no_paytype_exception_count}`,
                              storeCode
                            );
                          }

                       
                          var parts_excluded_from_history_exception_count;
                          var parts_excluded_from_history_exception_count_array;
                          var parts_excluded_from_history_exception_filepath='/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/parts_excluded_from_history.csv'

                          if (fs.existsSync(parts_excluded_from_history_exception_filepath)) {
                            console.log(
                              `parts_excluded_from_history_exception_filepath: ${parts_excluded_from_history_exception_filepath}`
                            );
                            segment.saveSegment(
                              `parts_excluded_from_history_exception_filepath: ${parts_excluded_from_history_exception_filepath}`
                            );
                            segment.saveSegmentFailure(
                              `parts_excluded_from_history_exception_filepath: ${parts_excluded_from_history_exception_filepath}`,
                              storeCode
                            );
                
                            parts_excluded_from_history_exception_count_array= await csv().fromFile(
                              parts_excluded_from_history_exception_filepath
                            );
                
                            if (parts_excluded_from_history_exception_count_array) {
                              if (parts_excluded_from_history_exception_count_array.length) {
                                console.log(
                                  `parts_excluded_from_history_exception_count_array.length: ${parts_excluded_from_history_exception_count_array.length}`
                                );
                                segment.saveSegment(
                                  `parts_excluded_from_history_exception_count_array.length: ${parts_excluded_from_history_exception_count_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `parts_excluded_from_history_exception_count_array.length: ${parts_excluded_from_history_exception_count_array.length}`,
                                  storeCode
                                );
                
                                parts_excluded_from_history_exception_count = parts_excluded_from_history_exception_count_array.length;
                                console.log('parts_excluded_from_history_exception_count:',parts_excluded_from_history_exception_count);
                                warningObj.parts_excluded_from_history_exception_count = parts_excluded_from_history_exception_count;
                                if (parts_excluded_from_history_exception_count){
                                    warningObj.parts_excluded_from_history_exception_filepath =
                                    parts_excluded_from_history_exception_filepath;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                            console.log(`parts_excluded_from_history_exception_count: ${parts_excluded_from_history_exception_count}`);
                            segment.saveSegment(
                              `parts_excluded_from_history_exception_count: ${parts_excluded_from_history_exception_count}`
                            );
                            segment.saveSegmentFailure(
                              `parts_excluded_from_history_exception_count: ${parts_excluded_from_history_exception_count}`,
                              storeCode
                            );
                          }


                          var coupon_and_discount_exception_count;
                          var coupon_and_discount_exception_count_array;
                          var coupon_and_discount_exception_filepath='/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/coupon_and_discount/exception/coupon_and_discount_exception.csv'

                          if (fs.existsSync(coupon_and_discount_exception_filepath)) {
                            console.log(
                              `coupon_and_discount_exception_filepath: ${coupon_and_discount_exception_filepath}`
                            );
                            segment.saveSegment(
                              `coupon_and_discount_exception_filepath: ${coupon_and_discount_exception_filepath}`
                            );
                            segment.saveSegmentFailure(
                              `coupon_and_discount_exception_filepath: ${coupon_and_discount_exception_filepath}`,
                              storeCode
                            );
                
                            coupon_and_discount_exception_count_array= await csv().fromFile(
                              coupon_and_discount_exception_filepath
                            );
                
                            if (coupon_and_discount_exception_count_array) {
                              if (coupon_and_discount_exception_count_array.length) {
                                console.log(
                                  `coupon_and_discount_exception_count_array.length: ${coupon_and_discount_exception_count_array.length}`
                                );
                                segment.saveSegment(
                                  `coupon_and_discount_exception_count_array.length: ${coupon_and_discount_exception_count_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `coupon_and_discount_exception_count_array.length: ${coupon_and_discount_exception_count_array.length}`,
                                  storeCode
                                );
                
                                coupon_and_discount_exception_count = coupon_and_discount_exception_count_array.length;
                                console.log('coupon_and_discount_exception_count:',coupon_and_discount_exception_count);
                                warningObj.coupon_and_discount_exception_count = coupon_and_discount_exception_count;
                                if (coupon_and_discount_exception_count){
                                    warningObj.coupon_and_discount_exception_filepath =
                                    coupon_and_discount_exception_filepath;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                        
                          }



                          var lost_sale_parts_exception_count;
                          var lost_sale_parts_exception_count_array;
                          var lost_sale_parts_exception_filepath='/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/lost_sale_parts.csv'
                          
                          if (fs.existsSync(lost_sale_parts_exception_filepath)) {
                            console.log(
                              `lost_sale_parts_exception_filepath: ${lost_sale_parts_exception_filepath}`
                            );
                            segment.saveSegment(
                              `lost_sale_parts_exception_filepath: ${lost_sale_parts_exception_filepath}`
                            );
                            segment.saveSegmentFailure(
                              `lost_sale_parts_exception_filepath: ${lost_sale_parts_exception_filepath}`,
                              storeCode
                            );
                
                            lost_sale_parts_exception_count_array= await csv().fromFile(
                              lost_sale_parts_exception_filepath
                            );
                
                            if (lost_sale_parts_exception_count_array) {
                              if (lost_sale_parts_exception_count_array.length) {
                                console.log(
                                  `lost_sale_parts_exception_count_array.length: ${lost_sale_parts_exception_count_array.length}`
                                );
                                segment.saveSegment(
                                  `lost_sale_parts_exception_count_array.length: ${lost_sale_parts_exception_count_array.length}`
                                );
                                segment.saveSegmentFailure(
                                  `lost_sale_parts_exception_count_array.length: ${lost_sale_parts_exception_count_array.length}`,
                                  storeCode
                                );
                
                                lost_sale_parts_exception_count = lost_sale_parts_exception_count_array.length;
                                console.log('lost_sale_parts_exception_count:',lost_sale_parts_exception_count);
                                warningObj.lost_sale_parts_exception_count = lost_sale_parts_exception_count;
                                if (lost_sale_parts_exception_count){
                                    warningObj.lost_sale_parts_exception_filepath =
                                    lost_sale_parts_exception_filepath;

                                }
                                 
                                // warningObj.invalidCoreCostSaleMismatchCount = invalidCoreCostSaleMismatchArray.length;
                              }
                            }
                
                        
                          }



                                 
                    var groupedSuffixInvoiceFilePath = '/home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/exception/new-line-type_ui.csv';

                    var suffixedInvoicesCsvData='';
                    if (fs.existsSync(groupedSuffixInvoiceFilePath)) {
                          fs.createReadStream(groupedSuffixInvoiceFilePath)
                          .pipe(csvParser())
                          .on('data', (row) => {
                          suffixedInvoicesCsvData+=`${row.LineType}:${row.Count}*`
                          })
                          .on('end', () => {
                            // CSV parsing is complete
                            console.log('CSV parsing finished.');
                            console.log("suffixedInvoicesCsvData",suffixedInvoicesCsvData);
                          })
                          .on('error', (error) => {
                            // Handle any error that occurs during parsing
                            console.error('Error occurred while parsing CSV:', error);
                          });
                        


                 }


                         await segment.sleep(3000);

                        var fetchGroupAndStoreName = (job.attrs.data.inputFile).split('-');
                        var groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : '';
                        var storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : '';

                        var mailTemplateReplacementValues = {
                            dmsType: constants.JOB_TYPE,
                            processTypes: constants.PROCESS_JSON.JOB_NAME,
                            subject: `Process JSON for ${groupName} - ${storeName} Completed`,
                            warningObj: warningObj,
                            thirdPartyUsername: enterpriseCode,
                            storeCode: mageStoreCode,
                            groupCode: mageGroupCode
                        };
                        var mailBody = {
                            fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                            toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                            ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                            attachedfailurelogFile:failurelogFile
                        }

                        job.attrs.data.processorUniqueId = '';
                     
                        if(uniqueId && uniqueId!=undefined){
                          job.attrs.data.processorUniqueId = uniqueId;
                         }

                        if (status) {
                          clearInterval(touch);
                            var opDataFileEtl = path.join(constants.PROCESS_JSON.DEALERTRACK_ETL_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                            var opDataFileDist = path.join(constants.PROCESS_JSON.DEALERTRACK_DIST_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                            opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
                            var outputFile = opDataFileDist + ' & ' + opDataFileEtl;
                            job.attrs.data.outputFile = outputFile;
                            job.attrs.data.status = status;
                            job.attrs.data.message = message;
                            job.attrs.data.address = dealerAddress;
                            // console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>Warning obj',warningObj);
                             if(inventory_ro_count > 0){
                                warningObj.inventory_ro_count = inventory_ro_count;
                                warningObj.inventory_ro_FilePath = inventory_ro_FilePath;
                                warningObj.dealerAddress = dealerAddress;
                             }
                             
                             if(chart_of_accounts_file_path){
                                warningObj.chart_of_accounts_file_path = chart_of_accounts_file_path;
                             }
                            job.attrs.data.warningMessage = warningObj;
                            console.log('job.attrs.data.warningMessage',job.attrs.data.warningMessage);
                            job.attrs.data.roAccountDescExceptionCount =  roAccountDescExceptionCount;
                            job.attrs.data.storeName = mageStoreCode;
                            // if(inventory_ro_count > 0){
                            //     job.attrs.data.inventory_ro_count = inventory_ro_count
                            //     job.attrs.data.inventory_ro_FilePath = inventory_ro_FilePath
                            //  }
                            job.attrs.data.company_no_not_matching_count = company_no_not_matching_count;
                            job.attrs.data.grouped_team_work_count = grouped_team_work_count;
                            console.log("suffixedInvoicesCsvData ??????????????//",suffixedInvoicesCsvData);
                            job.attrs.data.suffixedInvoicesCsvData = suffixedInvoicesCsvData;
                            job.attrs.data.new_line_type_count = new_line_type_count;
                            job.attrs.data.negative_coupon_count = negative_coupon_count;
                            job.attrs.data.labor_with_zero_sale_nonzero_cost_count = labor_with_zero_sale_nonzero_cost_count;
                            job.attrs.data.gl_missing_ros_count = gl_missing_ros_count;
                            job.attrs.data.coupon_discount_basis_amount_mismatch_exception_count = coupon_discount_basis_amount_mismatch_exception_count;
                            job.attrs.data.labor_with_no_paytype_exception_count = labor_with_no_paytype_exception_count;
                            job.attrs.data.parts_excluded_from_history_exception_count = parts_excluded_from_history_exception_count;
                            job.attrs.data.lost_sale_parts_exception_count = lost_sale_parts_exception_count;
                            
                            segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                            segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, uniqueFailLogName);
                            await job.save();
                            done();
                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            // Send notification after process json job completed
                            mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                        } else {
                          clearInterval(touch);
                            // Portal update for process json failed
                            const directoryPath = constants.DEALERTRACK_SCHEDULER_ETI_DIR; 
                            const fileName = basename;                                 
                            const filePath = path.join(directoryPath, fileName);
                            segment.saveSegment(`DEALERTRACK : filePath inpObj Error - ${fileName}`);
                                if (fs.existsSync(filePath)) {
                                    fs.unlink(filePath, (err) => {
                                       if(err) {
                                            segment.saveSegment(`DEALERTRACK : Error deleting file - ${err}`);
                                            console.error('Error deleting file:', err);
                                        } else {
                                        segment.saveSegment(`DEALERTRACK : File deleted successfully - ${filePath}`);
                                        console.log('File deleted successfully:', filePath);
                                        }
                                    });
                                } else {
                                 console.log('File does not exist:', filePath);
                              }

                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            let projectIdList;
                            let secondProjectIdList;
                            try{
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = agendaObject[extractedObjectIndex];
                            projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                            projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                            secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                            attPayload['inProjectId'] =  projectID;

                            secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                            attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";
                            
                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                            attPayload.in_retrive_ro_request_on = todayDate;
                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                            if(secondProjectIdList.length>0){
                                inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                            }
                            } catch(err){
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`DealerTrack : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            segment.saveSegment(`DealerTrack : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                            segment.saveSegment(`DealerTrack : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                            try {
                                segment.saveSegment(`DealerTrack : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                                // let parsedData = JSON.parse(inpObjProject.inData);
                                // let  projectIdList =  parsedData.projectIds.split("*");
                                if(projectIdList){
                                     for(const id of projectIdList){
                                        if(id!=undefined&& id!=''){
                                          inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                            portalUpdate.doPayloadAction(inpObjProject);
                                            console.log(`Dealertrack Schedule portal call with Project Id RESUME${id}`);
                                            segment.saveSegment(`Dealertrack Schedule portal call with Project Id failure${id}`);
                    
                                        }
                                     }
                                } 
                                
                                if(secondProjectIdList.length>0){
                                segment.saveSegment(`DealerTrack : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                                if(secondProjectIdList){
                                  // let parsedData = JSON.parse(inpObjSecondProject.inData);
                                  // let  secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                                  for(const id of secondProjectIdList){
                                     if(id!=undefined && id!=''){
                                      inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT : constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                         portalUpdate.doPayloadAction(inpObjSecondProject);
                                         console.log(`DEALERTRACK Schedule portal call with Second Project Id Failure${id}`);
                                         segment.saveSegment(`Dealertrack Schedule portal call with Second Project Id failure${id}`);
                 
                                     }
                                  }
                             } 
                                
                                }
                            } catch(error) {
                                console.log("Error:", error);
                                segment.saveSegment(`DealerTrack : doPayloadAction Error - ${JSON.stringify(error)}`); 
                            }
                            //code end for portal update for process json failed
                            job.attrs.data.roAccountDescExceptionCount =  roAccountDescExceptionCount;
                            job.attrs.data.warningMessage = warningObj;
                            job.attrs.data.storeName = mageStoreCode;
                            job.attrs.data.company_no_not_matching_count = company_no_not_matching_count;
                            job.attrs.data.grouped_team_work_count = grouped_team_work_count;
                            job.attrs.data.address = dealerAddress
                            console.log("suffixedInvoicesCsvData fail??????????????//",suffixedInvoicesCsvData);
                            job.attrs.data.suffixedInvoicesCsvData = suffixedInvoicesCsvData;
                            job.attrs.data.new_line_type_count = new_line_type_count;
                            job.attrs.data.chart_of_accounts_file_path = chart_of_accounts_file_path;
                            job.attrs.data.negative_coupon_count = negative_coupon_count;
                            job.attrs.data.labor_with_zero_sale_nonzero_cost_count = labor_with_zero_sale_nonzero_cost_count;
                            job.attrs.data.gl_missing_ros_count = gl_missing_ros_count;
                            job.attrs.data.coupon_discount_basis_amount_mismatch_exception_count = coupon_discount_basis_amount_mismatch_exception_count;
                            job.attrs.data.labor_with_no_paytype_exception_count = labor_with_no_paytype_exception_count;
                            job.attrs.data.parts_excluded_from_history_exception_count = parts_excluded_from_history_exception_count;
                            job.attrs.data.lost_sale_parts_exception_count = lost_sale_parts_exception_count;

                            await job.fail(new Error(`Error: ${message}`));
                            done();
                            let displayMessage;
                            mailTemplateReplacementValues.message = displayMessage;
                            if(haltIdentifier){
                                displayMessage = `Halted ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.status = 'Halted';
                                mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Halted`;
                            } else{
                                if(haltOverRide){
                                    mailTemplateReplacementValues.resumeUser = resumeUser ? resumeUser : '';
                                }
                                displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.status = 'Failed';
                                mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Failed`;
                            }
                           
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            // Send notification for failed process xml job
                            mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                            mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                            segment.saveSegmentFailure(displayMessage, uniqueFailLogName);
                            // Send notification for failed process json job
                            await segment.sleep(2000);
                            mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                        }
                        var basenameCheck = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        var distFile = path.join(constants.PROCESS_JSON.DEALERTRACK_BUNDLE_DIR, basenameCheck);
                       
                        let exceptions = "";

                        if (company_no_not_matching_count !== undefined && company_no_not_matching_count !== null) {
                          exceptions += `Company Number Not Matching  Count: ${company_no_not_matching_count}, `;
                        }

                        if (grouped_team_work_count !== undefined && grouped_team_work_count !== null) {
                          exceptions += `Team Work Count: ${grouped_team_work_count}, `;
                        }
                        
                        
                        if (new_line_type_count !== undefined && new_line_type_count !== null) {
                          exceptions += `New Line type Exception Count: ${new_line_type_count}`;
                        }
                        
                        exceptions = `roAccountDescExceptionCount:${roAccountDescExceptionCount}, company_no_not_matching_count:${company_no_not_matching_count}, grouped_team_work_count:${grouped_team_work_count}, new_line_type_count:${new_line_type_count}`;
                        console.log('exceptions dealertrack _________________________',exceptions);
                        updateSolve360Data = {projectIds:projectIds,secondProjectIdList:secondProjectIdList,projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:enterpriseCode, storeCode: mageStoreCode, dmsType: constants.JOB_TYPE, groupCode:mageGroupCode, resumeUser: resumeUser ? resumeUser : '',uniqueId:uniqueId,companyObj:companyObj,processFileName:modifiedFileName};


                        if (status && fs.existsSync(distFile)) {
                            basename = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                            segment.saveSegment(`Start distribute file process`);
                            let rerunFlag;
                            if(doProxyFromPgDump){
                                rerunFlag='RERUN'; 
                            }
                            console.log('Job saved with ID:', job.attrs._id);
                              if(totalRoCount && totalRoCount!=undefined){
                                updateSolve360Data.totalRoCount = totalRoCount;
                               }else{
                                 updateSolve360Data.totalRoCount = 0;
                              }
                          if(exceptionTypeCounts){
                                updateSolve360Data.exceptionTypeCounts = exceptionTypeCounts;
                             }else{
                                 updateSolve360Data.exceptionTypeCounts = null;
                         }   

                            await distributeFile(basename, rerunFlag, updateSolve360Data, warningObj, job.attrs._id);
                        } else {
                            // Process JSON Job Fail ....
                            segment.saveSegment(`Call for next job selection`);
                            await doNextProcess();
                        }
                    });  
            } else {
                /**
                * Remove the Initial/recheck schedules
                */
                 if(job.attrs.data.operation=="recheck") {
                    job.remove(err => {
                    segment.saveSegment(`Inside Job remove function`);
                    segment.saveSegment(`job: ${JSON.stringify(job)}`);
                    if (!err) {
                        segment.saveSegment(`Job removed successfully`);
                        console.log("Initial/recheck schedule for DealerTrack Process JSON job successfully removed");
                    } else{
                        segment.saveSegment(`Job removal process have error : ${JSON.stringify(err)}`);
                    }
                  });
              }
                done();
                segment.saveSegment(`Input file not found, call  doNextProcess()`);
                await doNextProcess();
            }
        });

    return agenda;
}

const getRoAccountingDescExceptionCount = () => {
    return new Promise((resolve, reject) => {
      try {
        let roAccountingDescCsvFilePath =
          constants.RO_ACCOUNTING_DESCRIPTION_EXCEPTION_CSV_FILE_PATH;
        let ro_accounting_description_exception_number = 0;
        let i;
        let count = 0;
        if (fs.existsSync(roAccountingDescCsvFilePath)) {
          console.log("The ro accounting desc csv file path file exists");
          try {
            require("fs")
              .createReadStream(roAccountingDescCsvFilePath)
              .on("data", function (chunk) {
                for (i = 0; i < chunk.length; ++i){
                    if (chunk[i] == 10){
                        count++;
                    } 
                } 
              })
              .on("end", function () {
                console.log("count:", count);
                ro_accounting_description_exception_number = count - 2;
                if(ro_accounting_description_exception_number < 0){
                  ro_accounting_description_exception_number = 0;  
                }
                resolve(ro_accounting_description_exception_number);
              });
          } catch (err) {
            segment.saveSegment(
              `DealerTrack : get Ro Accounting Desc Exception Count have error ${err}`
            );
            segment.saveSegmentFailure(
              `DealerTrack : get Ro Accounting Desc Exception Count have have error ${err}`,
              uniqueFailLogName
            );
            resolve(ro_accounting_description_exception_number);
          }
        } else{
          resolve(ro_accounting_description_exception_number);  
        }
      } catch (err) {
        segment.saveSegment(
          `DealerTrack : Read operation  of  coupon_and_discount_exception.csv have error ${err}`
        );
        segment.saveSegmentFailure(
          `DealerTrack : Read operation  of  coupon_and_discount_exception.csv have error ${err}`,
          uniqueFailLogName
        );
        resolve(ro_accounting_description_exception_number);
      }
    });
  };
  
