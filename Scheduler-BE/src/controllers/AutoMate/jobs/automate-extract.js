"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const AutoMateJobManager = require("../AutoMateJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const fs = require("fs");
const segment = require("../../SEGMENT/AutoMate/segmentManager");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const { v4: uuidv4 } = require('uuid');
const manageScheduleField = require('../../../../src/common/util');


/**
 * Function to find the unique stores in an array of stores
 */
Array.prototype.unique = function () {
    var a = this.concat();
    for (var i = 0; i < a.length; ++i) {
        for (var j = i + 1; j < a.length; ++j) {
            if (a[i].dealerId === a[j].dealerId)
                a.splice(j--, 1);
        }
    }
    return a;
};

/**
 * Function to perform AUTOMATE-Extract
 */
module.exports = function AutoMateExtractJOB(agenda) {
    console.log(
        `AutoMate-Extract job started: JobName: ${constants.AUTOMATE.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.AUTOMATE.CONCURRENCY}`
    );
    let extractionId;
    segment.saveSegment(`AutoMate-Extract job started: JobName: ${constants.AUTOMATE.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.AUTOMATE.CONCURRENCY}`);
    agenda.define(constants.AUTOMATE.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.AUTOMATE.CONCURRENCY },
        async (job, done) => {
            extractionId = job.attrs._id;
            console.log('Extraction Object:', JSON.stringify(job));
            const att = job.attrs.data;
            const storeDataArray = att.storeDataArray.reverse();
            var i = 0;
            var storeCode;
            var dealerId;
            var projectId, solve360Update, buildProxies, projectType, secondaryProjectType, inLaborProjectType, inLaborProjectId, inPartsProjectType, inPartsProjectId;
            var warningObj = {};
            let userName;
            let processFileName;
            let secondaryProjectId;
            async function extract(att, job) {
                var errorWarnningMessage = '';
                dealerId = att.dealerId;
                storeCode = att.mageStoreCode;
                projectId = att.projectId;
                projectType = att.projectType || null;
                secondaryProjectType = att.secondaryProjectType || null;
                secondaryProjectId = att.secondProjectId;
                if (projectType && projectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = projectType;
                    inLaborProjectId = projectId;
                } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("labor")) {
                    inLaborProjectType = secondaryProjectType;
                    inLaborProjectId = secondaryProjectId;
                }

                // Check if projectType or secondaryProjectType starts with "parts"
                if (projectType && projectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = projectType;
                    inPartsProjectId = projectId;
                } else if (secondaryProjectType && secondaryProjectType.toLowerCase().startsWith("parts")) {
                    inPartsProjectType = secondaryProjectType;
                    inPartsProjectId = secondaryProjectId;
                }
                solve360Update = att.solve360Update;
                buildProxies = att.buildProxies;
                userName = att.userName;
                warningObj.scheduled_by = userName;
                console.log('projectId:', projectId);
                console.log('solve360Update:', solve360Update);
                console.log('buildProxies:', buildProxies);
                console.log('extractionId:', extractionId);
                segment.saveSegment(`Extraction Job Started: ${JSON.stringify(att)}`);
                segment.saveSegmentFailure(`Extraction Job Started: ${JSON.stringify(att)}`, storeCode);
                if (att.dealerId && att.startDate && att.endDate) {
                    if (!fs.existsSync(constants.AUTOMATE_DEADLETTER_DIR_PREFIX + '-extracted')) {
                        fs.mkdirSync(constants.AUTOMATE_DEADLETTER_DIR_PREFIX + '-extracted');
                    }
                    var options = [
                        constants.AUTOMATE.PULL_OP,
                        constants.AUTOMATE.OPT_DEALER_ID, att.dealerId,
                        constants.AUTOMATE.OPT_START_DATE, att.startDate,
                        constants.AUTOMATE.OPT_END_DATE, att.endDate,
                        constants.AUTOMATE.OPT_DEADLETTER_DIR_PREFIX,
                        constants.AUTOMATE_DEADLETTER_DIR_PREFIX + '-extracted',
                        constants.AUTOMATE.OPT_MAGE_GROUP_CODE, att.mageGroupCode,
                        constants.AUTOMATE.OPT_MAGE_STORE_CODE, att.mageStoreCode,
                        '--stateCode', att.stateCode,
                        '--extractionID', extractionId
                    ];
                    // The use of --stateCode here is intentional; redirecting via a constant named arguments
                    // to external functions is more annoying than helpful; mainly due to verbosity
                    // repeating "constants" just is and having to namespace the variable ends up being
                    // redundant since the command invocation itself provides the necessary namespacing; once

                    options.push(constants.AUTOMATE.OPT_ZIP_PATH, att.zipPath ? att.zipPath : constants.AUTOMATE_SCHEDULER_ETI_DIR);
                    options.push(constants.AUTOMATE.OPT_ZAP_AFTER_ZIP);

                    // initial
                    (att.jobType == constants.AUTOMATE.JOB_TYPE_INITIAL) ? options.push(constants.AUTOMATE.OPT_CLOSED) : null;
                    ((att.jobType == constants.AUTOMATE.JOB_TYPE_INITIAL) ?
                        options.push(att.closedROOption ? att.closedROOption : constants.AUTOMATE.OPT_CLOSED_CRITERIA_ALL) : null);

                    // ondemand
                    (att.jobType == constants.AUTOMATE.JOB_TYPE_ON_DEMAND)
                        ? options.push(constants.AUTOMATE.OPT_CLOSED) : null;
                    (att.jobType == constants.AUTOMATE.JOB_TYPE_ON_DEMAND)
                        ? options.push(constants.AUTOMATE.OPT_CLOSED_CRITERIA_CURRENT) : null; // For now there is not other option

                    options.push(constants.AUTOMATE.OPT_BUNDLE);
                    options.push(att.jobType);
                    segment.saveSegment(`Extraction Job Started: ${JSON.stringify(att)}`);
                    segment.saveSegmentFailure(`Extraction Job Started: ${JSON.stringify(att)}`, storeCode);
                    //Mock Server
                    let isMockServer = constants.AUTOMATE.ENV_MOCKSERVER;
                    if (isMockServer == "true") {
                        let groupCode = att.mageGroupCode;
                        let sourceFolder = constants.AUTOMATE.MOCKSERVER_SOURCE_FOLDER_PATH + 'automate/';
                        let destinationFolder = constants.AUTOMATE.MOCKSERVER_DESTINATION_FOLDER_PATH_AUTOMATE;
                        //SullivansNWHills-NWHILL_GM-CT-INITIAL-786-20231120102853.zip
                        const mageGroupCodeDIR = groupCode.replace(/ +/g, "");
                        const mageStorecodeDIR = storeCode.replace(/ +/g, "");
                        const stateCodeDIR = att.stateCode;
                        const jobTypeDIR = att.jobType;
                        const dealerIdDir = dealerId;
                        const zipFileName = mageGroupCodeDIR + '-' + mageStorecodeDIR + '-' + stateCodeDIR + '-' + jobTypeDIR.toUpperCase(); +'-' + dealerIdDir + '-';
                        console.log('-------zipFileName-------', zipFileName);

                        fs.readdir(sourceFolder, function (err, files) {
                            if (err) {
                                console.log('AUTOMATE : Unable to scan directory');
                                segment.saveSegment(`AUTOMATE : Unable to scan directory`, err);
                            }
                            const matchedResults = [];
                            let searchResult;
                            files.forEach(function (file) {
                                if (file.includes(zipFileName)) {
                                    matchedResults.push(file);
                                }
                            });
                            console.log('AUTOMATE : files', matchedResults);
                            if (matchedResults.length > 0) {
                                searchResult = (matchedResults.sort().reverse())[0];
                                att.startTime = new Date(moment().utc());
                                fs.copyFile(sourceFolder + searchResult, destinationFolder + '/' + searchResult, (err) => {
                                    if (err) {
                                        console.log('AUTOMATE : Unable to copy file');
                                        segment.saveSegment(`AUTOMATE : Unable to copy file`, err);
                                    } else {
                                        att.endTime = new Date(moment().utc());
                                        att.uniqueId = util.generateUniqueId();
                                        att.status = true;
                                        att.mockServer = true;
                                        att.message = "Success";
                                        let oldStoreArray = job.attrs.data.storeDataArray;
                                        let newStoreArray = [att];
                                        oldStoreArray.map(data => {
                                            if (data.enterpriseCode === newStoreArray[0].enterpriseCode) {
                                                data = newStoreArray;
                                            }
                                        });
                                        var _storeArray = oldStoreArray;
                                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                                        job.attrs.data.storeDataArray = _storeArray;
                                        job.save();
                                        done();
                                    }
                                });
                            } else {
                                let warningObj = {};
                                segment.saveSegment(`AUTOMATE : Test job failed`);
                                console.log('AUTOMATE : Test job failed');
                                att.startTime = new Date(moment().utc());
                                att.endTime = new Date(moment().utc());
                                att.uniqueId = util.generateUniqueId();
                                att.status = false;
                                att.mockServer = true;
                                att.message = "Failed";
                                job.fail(new Error(`AUTOMATE :Test job failed`));
                                job.save()

                                let failureDirectory = process.cwd() + '/logs/AutoMate/failure/';
                                let failurelogFile = failureDirectory + storeCode + '.log';
                                let mailTemplateReplacementValues = {
                                    dmsType: constants.JOB_TYPE,
                                    processTypes: constants.PROCESS_JSON.JOB_NAME,
                                    subject: `Test Extraction Job for ${groupCode} - ${storeCode} Failed`,
                                    warningObj: warningObj,
                                    thirdPartyUsername: att.dealerId,
                                    storeCode: storeCode,
                                    groupCode: groupCode
                                };
                                let mailBody = {
                                    fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                                    toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                                    ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                                    attachedfailurelogFile: failurelogFile
                                }
                                var displayMessage = `Test Failed ${constants.JOB_TYPE} ${constants.AUTOMATE.JOB_NAME} job for group ${groupCode} and store ${storeCode}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Failed';
                                mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                                segment.saveSegment(`Send notification: ${displayMessage}`);
                                segment.saveSegmentFailure('Extraction status: Extraction Failed', storeCode);
                                // Send notification after  cdk extraction job completed
                                mailSender.sendMail(mailBody, constants.AUTOMATE.JOB_NAME);
                            }
                        });
                    } else {
                        segment.saveSegment(`Extraction Job Started6:options ${JSON.stringify(options)}`);    
                        const child = spawn(constants.AUTOMATE.EXTRACT_CMD, options);
                        var startTime = new Date(moment().utc());
                        att.startTime = startTime;
                        var oldStoreArray = job.attrs.data.storeDataArray;
                        var newStoreArray = [att];
                        oldStoreArray.map(data => {
                            if (data.dealerId === newStoreArray[0].dealerId) {
                                data = newStoreArray;
                            }
                        });
                        var _storeArray = oldStoreArray;
                        // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                        job.attrs.data.storeDataArray = _storeArray;
                        segment.saveSegment(`Extraction Job Data: ${JSON.stringify(_storeArray)}`);
                        segment.saveSegmentFailure(`Extraction Job Data: ${JSON.stringify(_storeArray)}`, storeCode);
                        att.uniqueId = util.generateUniqueId();
                        await job.save();
                        process.stdin.pipe(child.stdin)
                        var compareString = "";
                        var status = true;
                        var message = "n/a";
                        let unsubscribedArray = [];
                        let inSalesComment = "No DMS Access";
                        child.stdout.on("data", async (data) => {
                            await job.touch();
                            compareString = data.toString('utf8');
                            if (compareString.search("error:") != -1) {
                                message = data.toString('utf8')
                            }


                            data = data.toString('utf8');
                            segment.saveSegment(`response from request extraction: ${data}`);
                            segment.saveSegment(`response from request extraction: ${JSON.stringify(data)}`);
                            if (data.includes("You do not have permission to this resource")) {
                                if (data.includes("History")) {
                                    unsubscribedArray.push('History');
                                } else if (data.includes("Customer")) {
                                    unsubscribedArray.push('Customer');
                                } else {
                                    unsubscribedArray.push('ro bulk')
                                }
                            }
                            if (data.includes("Output zip file successfully generated @path")) {

                                console.log("data", data);
                                const pathMatch = data.match(/@path: (\/.*?\.zip)/);
                                console.log("pathMatch", pathMatch);
                                segment.saveSegment(`pathMatch: ${pathMatch}`);
                                if (pathMatch && pathMatch[1]) {
                                    const filePath = pathMatch[1];
                                    const match = filePath.match(/[^/]+\.zip$/);
                                    if (match) {
                                        processFileName = match[0];
                                        if (processFileName) {
                                            processFileName = processFileName.trim();
                                            att.processFileName = processFileName;
                                            await manageScheduleField.processCompanyData(job, userName, processFileName, filePath, constants.DMS);                                            
                                            await job.save();
                                        } else {
                                            processFileName = null;
                                        }

                                        console.log("Extracted filename:", processFileName);

                                    } else {

                                        console.log("Failed to extract filename from path:", filePath);

                                    }

                                } else {

                                    console.log("Failed to find the file path in the log data");

                                }
                            } else {
                                console.log("Failed to generate file");
                            }
                            console.log(`stdout: ${data}`);
                            segment.saveSegment(`stdout: ${data}`);
                            segment.saveSegmentFailure(`stdout: ${data}`, storeCode);
                        });


                        child.stderr.on("data", async (data) => {
                            await job.touch();
                            compareString = data.toString('utf8');
                            if (compareString.search("error:") != -1) {
                                message = data.toString('utf8')
                            }
                            console.log(`stderr: ${data}`);
                            segment.saveSegment(`stderr: ${data}`);
                            segment.saveSegmentFailure(`stderr: ${data}`, storeCode);
                        });

                        child.on("close", async (code) => {

                            if (code == constants.STATUS_CODE.SUCCESS) {
                                status = true;
                                message = "Success";
                            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                                status = false;
                                message = "Extraction failed, general death";
                            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                                status = false;
                                message = "Extraction failed, moved to dead-letter path";
                            }
                            segment.saveSegment(`Job close: ${message}`);
                            segment.saveSegmentFailure(`Job close: ${message}`, storeCode);

                            let failureDirectory = process.cwd() + '/logs/AutoMate/failure/';
                            let failurelogFile = failureDirectory + storeCode + '.log';

                            var groupName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageGroupCode : '';
                            var storeName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageStoreCode : '';

                            var mailTemplateReplacementValues = {
                                dmsType: constants.JOB_TYPE,
                                processTypes: constants.PROCESS_JSON.JOB_NAME,
                                subject: `Extraction Job for ${groupName} - ${storeName} Completed`,
                                warningObj: warningObj
                            };
                            var mailBody = {
                                fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                                toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                                ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                                attachedfailurelogFile: failurelogFile,
                                thirdPartyUsername: dealerId,
                                storeCode: storeName,
                                groupCode: groupName
                            }

                            if (status) {
                                // Send notification
                                var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.AUTOMATE.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.status = 'Success';
                                mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                                segment.saveSegment(`Send notification: ${displayMessage}`);
                                segment.saveSegmentFailure('Extraction status: Extraction completed', storeCode);
                                // Send notification after  Automate extraction job completed
                                mailSender.sendMail(mailBody, constants.AUTOMATE.JOB_NAME);
                            } else {
                                mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                                mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                                // Send notification
                                var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.AUTOMATE.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                                mailTemplateReplacementValues.message = displayMessage;
                                mailTemplateReplacementValues.subject = `Extraction Job for ${groupName} - ${storeName} Failed`;
                                mailTemplateReplacementValues.warnningMessage = errorWarnningMessage;
                                mailTemplateReplacementValues.status = 'Failed';
                                mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                                if (unsubscribedArray.length > 0) {
                                    const UserInput = {
                                        inLaborProjectId: inLaborProjectId,
                                        inLaborProjectType: inLaborProjectType,
                                        inPartsProjectType: inPartsProjectType,
                                        inPartsProjectId: inPartsProjectId,
                                        inIsInSales: true,
                                        inSalesComment: appConstants.SALES_TAG_COMMENT,
                                        inUpdatedBy: userName
                                    }
                                    segment.saveSegment(`Send notification - sales tag creation: ${JSON.stringify(UserInput)}`);
                                    try {
                                        const SendSalesTagDetails = await manageScheduleField.sendNotificationCall(UserInput, constants.AUTOMATE.JOB_EXTRACT_NAME);
                                        segment.saveSegment(`Automate Extraction - sales tag creation: ${JSON.stringify(SendSalesTagDetails)}`);
                                    } catch (salesError) {
                                        segment.saveSegment(`Send notification - sales tag creation Error: ${salesError}`);
                                    }
                                }
                                segment.saveSegment(`Send notification: ${displayMessage}`);
                                segment.saveSegmentFailure('Extraction status: Extraction failed', storeCode);

                                // Portal update for extraction failed
                                let todayDate;
                                let attPayload = {};
                                let projectID;
                                let secondProjectID;
                                let inpObjProject;
                                let inpObjSecondProject;
                                let secondProjectIdList;
                                let projectIdList;
                                try {
                                    todayDate = new Date().toISOString().slice(0, 10);
                                    attPayload = att;
                                    projectID = attPayload.hasOwnProperty('projectId') ? attPayload.projectId : "";
                                    projectIdList = attPayload.hasOwnProperty('projectIds') ? attPayload.projectIds.split("*") : "";
                                    secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ? attPayload.secondProjectIdList.split("*") : "";
                                    segment.saveSegment(`CDK EXTRACT : projectIdList - ${projectIdList}`);
                                    segment.saveSegment(`CDK EXTRACT : secondProjectIdList - ${secondProjectIdList}`);
                                    attPayload['inProjectId'] = projectID;

                                    secondProjectID = attPayload.hasOwnProperty('secondProjectId') ? attPayload.secondProjectId : "";
                                    attPayload.in_is_update_retrieve_ro = attPayload.hasOwnProperty('solve360Update') ? attPayload.solve360Update : "";

                                    attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                                    attPayload.in_retrive_ro_request_on = todayDate;
                                    inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                    console.log(inpObjProject, "******** INP OBJJJJJ ***********");
                                    if (secondProjectIdList.length > 0) {
                                        inpObjSecondProject = commonUtil.getinpObjFordoPayloadAction(secondProjectID, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                        console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********");
                                    }


                                    if (secondProjectIdList.length > 0) {
                                        for (let i = 0; i < secondProjectIdList.length; i++) {
                                            if (secondProjectIdList[i] != undefined && secondProjectIdList[i] != '') {
                                                inpObjSecondProject = commonUtil.getinpObjFordoPayloadAction(secondProjectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********");
                                                portalUpdate.doPayloadAction(inpObjSecondProject);
                                            }

                                        }

                                    }

                                    if (projectIdList.length > 0) {
                                        for (let i = 0; i < projectIdList.length; i++) {
                                            if (projectIdList[i] != undefined && projectIdList[i] != '') {

                                                inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIdList[i], attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                                segment.saveSegment(`CDK3PA : doPayloadAction - ${JSON.stringify(inpObjProject)}`);
                                                portalUpdate.doPayloadAction(inpObjProject);
                                            }

                                        }

                                    }
                                } catch (err) {
                                    console.log(JSON.stringify(err));
                                    segment.saveSegment(`AutoMate : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                                }
                                segment.saveSegment(`AutoMate : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                                segment.saveSegment(`AutoMate : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);
                                await segment.sleep(2000);
                                mailSender.sendMail(mailBody, constants.AUTOMATE.JOB_NAME);
                            }
                            att.endTime = new Date(moment().utc());
                            att.uniqueId ? att.uniqueId : util.generateUniqueId();
                            att.status = status;
                            att.message = message;
                            var oldStoreArray = job.attrs.data.storeDataArray;
                            var newStoreArray = [att];
                            oldStoreArray.map(data => {
                                if (data.dealerId === newStoreArray[0].dealerId) {
                                    data = newStoreArray;
                                }
                            });
                            var _storeArray = oldStoreArray;
                            // var _storeArray = newStoreArray.concat(oldStoreArray).unique();
                            job.attrs.data.storeDataArray = _storeArray;
                            if (processFileName) {
                                att.processFileName = processFileName.trim();
                            } else {
                                processFileName = null;
                            }
                            await job.save();
                            console.log(`AutoMate : Extraction process for store ${att.dealerId} exited code ${code}`);
                            segment.saveSegment(`AutoMate : Extraction process for store ${att.dealerId} exited code ${code}`);
                            i++;
                            if (i < storeDataArray.length) {
                                // Check time frame
                                if (att.runNow) {
                                    await extract(storeDataArray[i], job);
                                    segment.saveSegment(`Automate : runNow`);
                                } else if (util.checkExtractTimeFrame()) {
                                    segment.saveSegment(`Automate : Check time frame and start extraction ${JSON.stringify(storeDataArray[i])}`);
                                    await extract(storeDataArray[i], job);
                                } else {
                                    const newDataArray = storeDataArray;
                                    try {
                                        AutoMateJobManager.scheduleAutoMateExtractJob(AutoMateJobManager.createScheduleObject(job, newDataArray.slice(i)), true);
                                        job.attrs.data.storeDataArray = storeDataArray.slice(0, i);
                                        job.fail(new Error(`AutoMate : Time exceeded, remaining stores scheduled to next day.`));
                                        segment.saveSegment(`AutoMate : Time exceeded, remaining stores scheduled to next day.`);
                                        await job.save();
                                        //done();
                                    } catch (error) {
                                        console.error(error);
                                        segment.saveSegment(`Error : ${error.toString()}`);
                                    }
                                }
                            } else {
                                done();
                            }
                        });
                    }
                } else {
                    console.error("AutoMate : Store data Extraction attributes not defined");
                    segment.saveSegment("AutoMate : Store data Extraction attributes not defined");
                }
            }
            if (att.runNow) { // Check whether it is for run now or not, if yes, no need to check time frame
                segment.saveSegment(`Automate : runNow : Check whether it is for run now or not, if yes, no need to check time frame ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else if (util.checkExtractTimeFrame()) {
                segment.saveSegment(`Automate : Check time frame and start extraction ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else { // Auto schedule full Group wise schedule for tomorrow
                segment.saveSegment(`Automate : Auto schedule full Group wise schedule for tomorrow`);
                AutoMateJobManager.scheduleAutoMateExtractJob(AutoMateJobManager.createScheduleObject(job), true);
                job.remove();
            }
        });

    agenda.on("start", job => {
        console.log(`AutoMate : Job ${job.attrs.name}_${job.attrs._id} starting`);
    });

    agenda.on("complete", job => {
        console.log(`AutoMate : Job ${job.attrs.name}_${job.attrs._id} finished`);
    });

    agenda.on("fail", (err, job) => {
        console.log(`AutoMate : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `);
    });
    return agenda;
}
