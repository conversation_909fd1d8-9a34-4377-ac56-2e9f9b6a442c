"use strict";

const constants = require("../constants");
const util = require("../util");

const commonUtil = require("../../../common/util");
const portalUpdate = require("../../../routes/solve360Update");
const constantsCommon = require("../../../common/constants");

const { spawn } = require("child_process");
const path = require('path');
const fs = require("fs");
const moment = require("moment-timezone");
const segment = require("../../SEGMENT/Dominion/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
var mailSender = require('../../../routes/mailSender');
const appConstants = require('../../../common/constants');
const stripAnsi = require('strip-ansi');
var Agenda = require("../../agenda");
const extractionError = require('../../../../src/common/extractionError');
const csvParser = require('csv-parser');
const csv=require('csvtojson');
const csv1 = require('csv-parser');
const SetProcessJobStatus = require('../../../model/setProcessJobStatus');
const appUtil = require('../../../common/util');
/**
 * Function to perform processing of XML file downloaded through Dominion-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {
    let dealerID;

    var distributeFile = async function (fileName, rerunFlag , updateSolve360Data, warningObj, jobId) {
        var stdErrorArray;
        var distDir = path.join(process.env.DOMINION_DIST_DIR, fileName);
        var etlDir = path.join(process.env.DOMINION_ETL_DIR, fileName);
        etlDir = etlDir.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
        var filePath = path.join(process.env.DOMINION_BUNDLE_DIR, fileName);
        const distributeFile = spawn("bash",
            [
                'send-bundle-live-hpdog', filePath
            ], {
                cwd: constants.PROCESS_JSON.DOMINION_DISTRIBUTE_CMD_PATH,
                env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
            }).on('error', function (err) {
                console.log("error :", err);
                segment.saveSegment(`error: ${err}`);
            });
        console.log(`Dominion: Start processing of distribution`);
        segment.saveSegment(`Dominion:  processing distribution`);
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", async (data) => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
        });

        distributeFile.stderr.on("data", async (data) => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + ' ';
            segment.saveSegment(`stderr: ${data}`);
        });

        distributeFile.on("close", async (code) => {
            var message = "n/a";
            var status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
                // await SetProcessJobStatus.setProcessJobStatusForDominion(dealerID,mageStoreCode,message);
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                message = "Distribution failed, general death";
                // await SetProcessJobStatus.setProcessJobStatusForDominion(dealerID,mageStoreCode,'Failed');
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
                // await SetProcessJobStatus.setProcessJobStatusForDominion(dealerID,mageStoreCode,'Failed');
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                // await SetProcessJobStatus.setProcessJobStatusForDominion(dealerID,mageStoreCode,'Failed');
                segment.saveSegment(message);
            }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, constants.JOB_TYPE, rerunFlag , updateSolve360Data, warningObj, 0, jobId);//Upload dist directory zip file to sharepoint
            }
            await segment.sleep(2000);
            segment.saveSegment(`Completed distributeFile, call  doNextProcess()`);
            await doNextProcess();
        });
    }

    var doNextProcess = async function () {
        await segment.sleep(5000);
        segment.saveSegment(`inside doNextProcess`);
        const extractZip = await util.findOldestZipFile(constants.DOMINION_SCHEDULER_ETI_DIR);
        if (fs.existsSync(`/home/<USER>/tmp/du-etl-dms-dominionvue-extractor-work/scheduler-temp/dominionvue-zip-eti/${extractZip}`)) {
            console.log(`file Name ${extractZip} exists!`);
        } else {
            console.log(`file Name ${extractZip} does not exists`);
        }
        if (extractZip && fs.existsSync(`/home/<USER>/tmp/du-etl-dms-dominionvue-extractor-work/scheduler-temp/dominionvue-zip-eti/${extractZip}`)) {
            console.log(`Dominion : Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`Dominion : Found one Store extraction > ${extractZip} to process now`);
            try {
                var createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                await agenda.now(constants.PROCESS_JSON.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    operation: "json-processing"
                });
                console.log(`Dominion : Process JSON schedule started with file > ${extractZip}`);
                segment.saveSegment(`Dominion : Process JSON schedule started with file > ${extractZip}`);
            } catch (error) {
                segment.saveSegment(`inside doNextProcess have error: ${JSON.stringify(error)}`);
                console.error(error);
            }

        } else {
            segment.saveSegment(`Dominion : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            console.log(`Dominion : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            //segment.saveSegment(`Dominion : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_JSON.TIME_GAP}`, constants.PROCESS_JSON.JOB_NAME, { operation: "recheck" });
                console.log(`Dominion : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
                segment.saveSegment(`Dominion : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
            } catch (error) {
                segment.saveSegment(`Dominion : Process JSON schedule have error: ${JSON.stringify(error)}`);
                console.error(error);
            }
        }
    }

    console.log(
        `Dominion : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`
    );
    segment.saveSegment(`Dominion : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`);
    agenda.define(constants.PROCESS_JSON.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.PROCESS_JSON.CONCURRENCY },
        async (job, done) => {
            const touch = setInterval(() => job.touch(), 1 * 60 * 1000);
            segment.saveSegment(`job data is  : ${JSON.stringify(job)}`);
            const att = job.attrs.data;
            segment.saveSegment(`att data is  : ${JSON.stringify(att)}`);
            var extractZip = null;
            var stdErrorArray = [];
            var stdOutArray = [];
            var uniqueFailLogName;
            segment.saveSegment(`att.inputFile is  : ${att.inputFile}`);
            var inpFile = att.inputFile ? path.join(constants.DOMINION_SCHEDULER_ETI_DIR, att.inputFile) : '';
            if (att.inputFile && fs.existsSync(inpFile)) {
                    segment.saveSegment(`Input file found : ${att.inputFile}`);
                    if (!fs.existsSync(constants.DOMINION_DEADLETTER_DIR_PREFIX + '-processed')) {
                        fs.mkdirSync(constants.DOMINION_DEADLETTER_DIR_PREFIX + '-processed');
                    }
                    extractZip = att.inputFile;
                    let basename = path.basename(extractZip);
                    job.attrs.data.storeID = basename.split("-").reverse()[1];
                    let storeName = job.attrs.data.storeID;
                    var storeCode = storeName;

                    let dealerID = basename.split("-").reverse()[1];
                    let mageGroupCode = basename.split("-")[0];
                    let mageStoreCode =  basename.split("-")[1];

                    uniqueFailLogName = dealerID + '-' + mageStoreCode;
                    
                    console.log('Groupname:', mageGroupCode);
                    segment.saveSegment(`Groupname : ${mageGroupCode}`);
                    console.log('DealerID:', dealerID);
                    segment.saveSegment(`DealerID : ${dealerID}`);
                    console.log('storeName:',mageStoreCode);
                    segment.saveSegment(`storeName : ${mageStoreCode}`);

                    var jobsTmp = await Agenda.jobs( {
                        $and: [
                            { "data.storeDataArray.dealerID": dealerID },
                            { "data.storeDataArray.mageStoreCode": mageStoreCode},
                            { "name": constants.DOMINION.JOB_NAME } 
                        ]
                    });
                    
                    var projectId = '';
                    var secondProjectId = '';
                    var userName = '';
                  
                    var solve360Update = '';
                    var updateSolve360Data; 
                    var buildProxies;
                    var extractionId;
                    
                    let agendaObject;
                    let extractedFileTimeStamp;
                    let extractedFileCreationDate;
                    let extractedObjectIndex;

                    let stateCode;
                    let haltIdentifier = false;
                    let haltOverRide = false;
                    let resumeUser;
                    let projectIds;
                    let secondProjectIdList;
                    let uniqueId;
                    let companyIds;
                    let companyObj;
		            let testData;
                    let totalRoCount;
                    let exceptionTypeCounts = {};
                    let parentName;
                    let modifiedFileName="";
                   


                    try{
                        extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                        segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                        extractedFileCreationDate =  moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                        segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                    } catch(err){
                        console.log(err);
                        segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                    }
                    
                    segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                    segment.saveSegment(`jobsTmp[jobsTmp.length-1] : ${JSON.stringify(jobsTmp[jobsTmp.length-1])}`);
                    segment.saveSegment(`Enterprise Code : ${dealerID}`);

                    if(jobsTmp[jobsTmp.length-1]){
                        if(jobsTmp[jobsTmp.length-1].hasOwnProperty("attrs")){
                            extractionId = jobsTmp[jobsTmp.length-1].attrs._id;
                            try{
                                segment.saveSegment(`jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray)}`);
                                agendaObject = jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray;
                                // && el.mageGroupCode == mageGroupCode
                                agendaObject = agendaObject.filter(function (el) {
                                    return el.dealerID == dealerID && el.mageStoreCode == mageStoreCode;
                                });
                                segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);
                                extractedObjectIndex = 0;
                                if(agendaObject.length > 0){ 
                                    agendaObject =  agendaObject.sort((a,b) => b.endTime > a.endTime);
                                    extractedObjectIndex = agendaObject.findIndex(
                                        obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                    );
                                }

                                if(extractedObjectIndex < 0){
                                    extractedObjectIndex = 0;
                                }

                                segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                                segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                                segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);


                                if(agendaObject[extractedObjectIndex].hasOwnProperty("projectId")){
                                    projectId = agendaObject[extractedObjectIndex].projectId;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectId")){
                                    secondProjectId = agendaObject[extractedObjectIndex].secondProjectId;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("solve360Update")){
                                    solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("buildProxies")){
                                    buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("includeMetaData")){
                                    includeMetaData = agendaObject[extractedObjectIndex].includeMetaData;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("userName")){
                                    userName = agendaObject[extractedObjectIndex].userName;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("stateCode")){
                                    stateCode = agendaObject[extractedObjectIndex].stateCode;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("haltOverRide")){
                                    haltOverRide = agendaObject[extractedObjectIndex].haltOverRide;
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("projectIds")){
                                    projectIds = agendaObject[extractedObjectIndex].projectIds;
                                    projectIds =  projectIds.split("*");

                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("secondProjectIdList")){
                                    secondProjectIdList = agendaObject[extractedObjectIndex].secondProjectIdList;
                                    secondProjectIdList = secondProjectIdList.split("*");
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("companyIds")){
                                    console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
                                    companyIds = agendaObject[extractedObjectIndex].companyIds;
                                    companyIds =  companyIds.replace(new RegExp('\\*', 'g'), ',')
                                    console.log("Company ids exist>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",companyIds);
                                    // companyIds = companyIds.replace(/,\s*$/, "");
                                }

                                if(agendaObject[extractedObjectIndex].hasOwnProperty("uniqueId")){
                                    uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                    uniqueId+='-'+Date.now();
                               
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("companyObj")){
                                    companyObj = JSON.parse(agendaObject[extractedObjectIndex].companyObj);
                                    console.log("companyObj?????????????????????????????????????????????????????????",companyObj);
                                }
                                if(agendaObject[extractedObjectIndex].hasOwnProperty("testData")){
                                    testData = agendaObject[extractedObjectIndex].testData;
                                }
                                   if (agendaObject[extractedObjectIndex].hasOwnProperty("parentName")) {
                                    parentName = agendaObject[extractedObjectIndex].parentName;
                                    parentName = parentName.replace(/\s*\[.*?\]/, '')  
                                .replace(/\s+/g, '').trim();
                              console.log("Cleaned parentName:", parentName);
                          }

                             if(agendaObject[extractedObjectIndex].hasOwnProperty("stateCode")){
                                    stateCode =agendaObject[extractedObjectIndex].stateCode;
                              }           
                               
                            } catch(err){
                                console.log(err);
                                segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                            }
                        }
                    }
                    modifiedFileName = `${constants.PROCESS_JSON.OUTPUT_PREFIX_VAL}${parentName}-${stateCode}-${dealerID}-${appUtil.getTimestampForAuditFile()}.zip`

                    updateSolve360Data = {projectId:projectId, secondProjectId:secondProjectId, userName:userName, solve360Update:solve360Update, thirdPartyUsername:dealerID, storeCode: mageStoreCode, dmsType: constants.JOB_TYPE, groupCode:mageGroupCode,projectIds:projectIds,secondProjectIdList:secondProjectIdList,uniqueId:uniqueId,testData:testData,companyObj:companyObj,companyObj:companyObj,processFileName:modifiedFileName};
                    console.log(updateSolve360Data);
                    segment.saveSegment(`updateSolve360Data : ${updateSolve360Data}`);

                    console.log('projectId:', projectId);
                    segment.saveSegment(`projectId : ${projectId}`);

                    console.log('secondProjectId:', secondProjectId);
                    segment.saveSegment(`secondProjectId : ${secondProjectId}`);

                    console.log('userName:', userName);
                    segment.saveSegment(`userName : ${userName}`);

                    console.log('solve360Update:', solve360Update);
                    segment.saveSegment(`solve360Update : ${solve360Update}`);

                    console.log('buildProxies:', buildProxies);
                    segment.saveSegment(`buildProxies : ${buildProxies}`);
                    
                    console.log('extractionId:', extractionId);
                    segment.saveSegment(`extractionId : ${extractionId}`);

                    console.log('stateCode:', stateCode);
                    segment.saveSegment(`stateCode: ${stateCode}`);

                    console.log('haltOverRide:', haltOverRide);
                    segment.saveSegment(`haltOverRide : ${haltOverRide}`);
                    
                    console.log('projectIds:', projectIds);
                    segment.saveSegment(`projectIds : ${projectIds}`);

                    console.log('secondProjectIdList:', secondProjectIdList);
                    segment.saveSegment(`secondProjectIdList : ${secondProjectIdList}`);

                    console.log('uniqueId:', uniqueId);
                    segment.saveSegment(`uniqueId : ${uniqueId}`);

                    console.log('companyIds:', companyIds);
                    segment.saveSegment(`companyIds : ${companyIds}`);


                    console.log('companyObj:', companyObj);
                    segment.saveSegment(`companyObj : ${companyObj}`);
                    console.log('parentName:', parentName);
                    segment.saveSegment(`parentName : ${parentName}`);
  
                    console.log('modifiedFileName:', modifiedFileName);
                    segment.saveSegment(`modifiedFileName : ${modifiedFileName}`);
                    
                    buildProxies = false; 
                    if(haltOverRide){
                        resumeUser = `${userName}`;
                        console.log('resumeUser:', resumeUser);
                        segment.saveSegment(`resumeUser : ${resumeUser}`);
                    }

                    let buildProxiesDecider;
                    // if(buildProxies){
                    //     buildProxiesDecider = constants.PROCESS_JSON.OPT_BUILD_PROXY_RO; 
                    // } else{
                    //     buildProxiesDecider = constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO;
                    // }

                    buildProxiesDecider = constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO;

                    await job.save();
                    if(haltOverRide){
                        // Portal update for process xml Resume
                        let todayDate;
                        let attPayload = {};    
                        let projectID;
                        let secondProjectID;
                        let inpObjProject;
                        let inpObjSecondProject;
                        let projectIdList;
                        let secondProjectIdList;
                        try{
                        todayDate = new Date().toISOString().slice(0, 10);
                        attPayload = agendaObject[extractedObjectIndex];
                        projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                        projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                       secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                        attPayload['inProjectId'] =  projectID;
    
                        secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                        attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";
    
                        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                        attPayload.in_retrive_ro_request_on = todayDate;
                        inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                        console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                        if(secondProjectIdList.length>0){
                            inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                            console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                        }
                        } catch(err){
                        console.log(JSON.stringify(err));
                        segment.saveSegment(`AUTOMATE : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                        }
                        segment.saveSegment(`AUTOMATE : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                        segment.saveSegment(`AUTOMATE : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);
    
                        try {
                            // segment.saveSegment(`AUTOMATE : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                            // let parsedData = JSON.parse(inpObjProject.inData);
                            // let  projectIdList =  parsedData.projectIds.split("*");
                                if(projectIdList){
                                     for(const id of projectIdList){
                                        if(id!=undefined && id!=''){
                                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                                            portalUpdate.doPayloadAction(inpObjProject);
                                            console.log(`Delertrack Schedule portal call with Project Id RESUME${id}`);
                                            segment.saveSegment(`Dealertrack Schedule portal call with Project Id RESUME${id}`);
                    
                                        }
                                     }
                                } 
                           
                            if(secondProjectIdList.length>0){
                            // segment.saveSegment(`DOMINION : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                            // let parsedData = JSON.parse(inpObjSecondProject.inData);
                            // let  secondProjectIdList =  parsedData.secondProjectIdList.split("*");
                            if(secondProjectIdList){
                                for(const id of secondProjectIdList){
                                   if(id!=undefined && id!=''){
                                    inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate, constantsCommon.PAYLOAD_IN_ACTION.RESUME);
                                       portalUpdate.doPayloadAction(inpObjSecondProject);
                                       console.log(`Dealertrack Schedule portal call with Second Project Id Resume${id}`);
                                       segment.saveSegment(`Dealertrack Schedule portal call with Second Project Id RESUME${id}`);
               
                                   }
                                }
                           } 
                            
                            }
                        } catch(error) {
                            console.log("Error:", error);
                            segment.saveSegment(`DEALERTRACK  : doPayloadAction Error - ${JSON.stringify(error)}`); 
                        }
                        //code end for portal update for process xml Resume
                    }
                    const processJson = spawn("bash",
                        [
                            constants.PROCESS_JSON.PROCESS_CMD,
                            constants.PROCESS_JSON.OPT_INPUT_ZIP, path.join(constants.DOMINION_SCHEDULER_ETI_DIR, extractZip),
                            constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.DOMINION_BUNDLE_DIR,
                            constants.PROCESS_JSON.OPT_ZAP_INPUT,
                            constants.PROCESS_JSON.OPT_PERFORM_ZIP,
                            constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX,
                            constants.DOMINION_DEADLETTER_DIR_PREFIX + '-processed',
                            // constants.PROCESS_JSON.OPT_BUILD_PROXY_RO,
                            buildProxiesDecider,
                            constants.PROCESS_JSON.OUTPUT_PREFIX,
                            constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,
                            constants.PROCESS_JSON.OPT_STATE,stateCode,
                            constants.PROCESS_JSON.HALT_OVER_RIDE,
                            haltOverRide,
                            "--uuid",uniqueId,
                            "--performed-by",userName,
                            "--exception-report",true,
                            "--company_ids",companyIds,
                            "--output-file",modifiedFileName
                        ], {
                            cwd: constants.PROCESS_JSON.PROCESS_CMD_PATH,
                            env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
                        }).on('error', function (err) {
                            console.log("error ::", err);
                            segment.saveSegment(`error: ${err}`);
                        });
                    console.log(`Dominion : Start processing of extraction > ${basename}`);
                    segment.saveSegment(`Dominion : Start processing of extraction > ${basename}`);
                    segment.saveSegmentFailure(`Dominion : Start processing of extraction > ${basename}`, uniqueFailLogName);
                    process.stdin.pipe(processJson.stdin);

                    processJson.stdout.on("data", async (data) => {
                        console.log(`stdout: ${data}`);
                        stdOutArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                        await job.touch();

                     data = data.toString('utf8');
                     let processorStatus;
                        if(data.includes('Total Ros Count:-')){
                                                segment.saveSegment(`Total Ros Count55555555555,${data}`);
                                                console.log('file generated',data.split(':')[1]);
                                                segment.saveSegment(`Total Ro90999999999,${data.split(':')[1]}`);
                                                totalRoCount = data.split(':-')[1];
                                                segment.saveSegment(`totalRoCount666666,${totalRoCount}`);
                                         
                                           }else{
                                                console.log("failed to generate1212 file")
                                          } 

                    if(data.includes('Processor status')){
                         segment.saveSegment('Processor statu for UI',data);
                         console.log('file generated',data.split(':')[1]);
                         processorStatus = data.split(':')[1];
                         segment.saveSegment('processorStatus',processorStatus);
                         await SetProcessJobStatus.setProcessJobStatusForRunningJob(basename,processorStatus);
                       }else{
                       console.log("failed to generate file")
                      }

                        segment.saveSegment(`stdout: ${data}`);
                        segment.saveSegmentFailure(`stdout: ${data}`, uniqueFailLogName);
                    });

                    processJson.stderr.on("data", async (data) => {
                        console.log(`stderr: ${data}`);
                        stdErrorArray.push(stripAnsi(data.toString('utf8').replace(/\n$/, '')));
                        await job.touch();
                        segment.saveSegment(`stderr: ${data}`);
                        segment.saveSegmentFailure(`stderr: ${data}`, uniqueFailLogName);

                    try{
                        if(!data.includes('Beginning zip processing in') && data){
                            if (fs.existsSync(path.join(constants.DOMINION_SCHEDULER_ETI_DIR, extractZip))) {
                                fs.copyFile(path.join(constants.DOMINION_SCHEDULER_ETI_DIR, extractZip), deadLetterPath+ "/" + basename, (err) => {
                                    if (err) {
                                        console.log(err);
                                        segment.saveSegment(`Error in input file to dead letter: ${err}`);
                                        segment.saveSegmentFailure(`Error in input file to dead letter: ${err}`, storeCode);
                                    }
                                    console.log(`${ path.join(constants.DOMINION_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                    segment.saveSegment(`${ path.join(constants.DOMINION_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`);
                                    segment.saveSegmentFailure(`${ path.join(constants.DOMINION_SCHEDULER_ETI_DIR, extractZip)} was copied to ${deadLetterPath}`, storeCode);
                                });
                        
                                // fs.unlink(path.join(constants.REYNOLDS_SCHEDULER_ETI_DIR, extractZip), function (err) {
                                //     if (err){
                                //         console.log(err);
                                //         segment.saveSegment(`Error in delete input file : ${err}`);
                                //         segment.saveSegmentFailure(`Error in delete input file : ${err}`, storeCode);
                                //     } 
                                // });
                            }
                        }
                    } catch(err){
                        console.log(err)
                    }
                    });

                    processJson.on("close", async (code) => {
                     const filePath = '/home/<USER>/tmp/du-etl-dms-dominion-extractor-work/exception_tag/All_exception_details.csv';
                    if (fs.existsSync(filePath)) {
                      fs.createReadStream(filePath)
                      .pipe(csv1())
                      .on('data', (row) => {
                      const type = row['Type'];
                       if (type) {
                          exceptionTypeCounts[type] = (exceptionTypeCounts[type] || 0) + 1;
                        }
                    })
                     .on('end', () => {
                     console.log("exceptionTypeCounts", exceptionTypeCounts);
                     segment.saveSegment(`exceptionTypeCounts: ${JSON.stringify(exceptionTypeCounts)}`);
                  });
               } else {
                   console.error(`File not found: ${filePath}`);
                   segment.saveSegment(`Error: File not found at path ${filePath}`);
                }

                        var message = "n/a";
                        var status = false;
                        if (code == constants.STATUS_CODE.SUCCESS) {
                            status = true;
                            message = "Success";
                        } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                            message = "Extraction failed, general death";
                            job.fail(new Error(`Error: ${message}`));
                        } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                            message = "Extraction failed, moved to dead-letter path";
                            job.fail(new Error(`Error: ${message}`));
                        }
                        if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                            message = "Extraction failed, Zip File Processing Failed,  ";
                            status = false;
                        }
                        var deadLetterPath = `${process.env.DOMINION_WORK_DIR}/dead-letter-processed`;
                        var errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                        if (stdOutArray && stdOutArray.includes(errResp)) {
                            message += errResp
                            status = false;
                        }
                        stdErrorArray.forEach((v,i) =>{
                            if(v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)){
                            }
                        })

                        var warningObj = {};
                        warningObj.scheduled_by = userName;
                        if(testData){
                            warningObj.testData = true;
                            warningObj.userName = userName;
                        }
                          var SaleZeroCostNonZeroPartsCount;
                          var SaleZeroCostNonZeroPartsArray;
                          var SaleZeroCostNonZeroPartsFilePath = "/home/<USER>/tmp/du-etl-dms-dominionvue-extractor-work/exception/sale_zero_cost_nonzero_parts.csv";


  
                          if (fs.existsSync(SaleZeroCostNonZeroPartsFilePath)) {
                              console.log(
                                `The Sale Zero Cost Non Zero Parts Csv File exists: ${SaleZeroCostNonZeroPartsFilePath}`
                              );
                              segment.saveSegment(
                                `The Sale Zero Cost Non Zero Parts Csv File exists: ${SaleZeroCostNonZeroPartsFilePath}`
                              );
                              segment.saveSegmentFailure(
                                `The Sale Zero Cost Non Zero Parts Csv File exists: ${SaleZeroCostNonZeroPartsFilePath}`,
                                storeCode
                              );
                  
                              SaleZeroCostNonZeroPartsArray = await csv().fromFile(
                                  SaleZeroCostNonZeroPartsFilePath
                              );
                  
                              if (SaleZeroCostNonZeroPartsArray) {
                                if (SaleZeroCostNonZeroPartsArray.length) {
                                  console.log(
                                    `SaleZeroCostNonZeroPartsArray.length: ${SaleZeroCostNonZeroPartsArray.length}`
                                  );
                                  segment.saveSegment(
                                    `SaleZeroCostNonZeroPartsArray.length: ${SaleZeroCostNonZeroPartsArray.length}`
                                  );
                                  segment.saveSegmentFailure(
                                    `SaleZeroCostNonZeroPartsArray.length: ${SaleZeroCostNonZeroPartsArray.length}`,
                                    storeCode
                                  );
                  
                                  SaleZeroCostNonZeroPartsCount = SaleZeroCostNonZeroPartsArray.length;
                                  console.log('SaleZeroCostNonZeroPartsCount',SaleZeroCostNonZeroPartsCount);
                                  warningObj.SaleZeroCostNonZeroPartsCount = SaleZeroCostNonZeroPartsCount;
                                  if (SaleZeroCostNonZeroPartsCount)
                                    warningObj.SaleZeroCostNonZeroPartsFilePath =SaleZeroCostNonZeroPartsFilePath;
                                  warningObj.SaleZeroCostNonZeroPartsCount = SaleZeroCostNonZeroPartsCount
                                }
                              }
                  
                              console.log(`SaleZeroCostNonZeroPartsCount: ${SaleZeroCostNonZeroPartsCount}`);
                              segment.saveSegment(
                                `SaleZeroCostNonZeroPartsCount: ${SaleZeroCostNonZeroPartsCount}`
                              );
                              segment.saveSegmentFailure(
                                `SaleZeroCostNonZeroPartsCount: ${SaleZeroCostNonZeroPartsCount}`,
                                storeCode
                              );
                            }

                            var partDescriptionExceptionCount;
                            var partDescriptionExceptionCountArray;
                            var partDescriptionExceptionFilePath = "/home/<USER>/tmp/du-etl-dms-dominionvue-extractor-work/exception/part_description_exception.csv";
    
                            if (fs.existsSync(partDescriptionExceptionFilePath)) {
                                console.log(
                                  `The part Description Exception File exists: ${partDescriptionExceptionFilePath}`
                                );
                                segment.saveSegment(
                                  `The part Description Exception File exists: ${partDescriptionExceptionFilePath}`
                                );
                                segment.saveSegmentFailure(
                                  `The part Description Exception File exists: ${partDescriptionExceptionFilePath}`,
                                  storeCode
                                );
                    
                                partDescriptionExceptionCountArray = await csv().fromFile(
                                    partDescriptionExceptionFilePath
                                );
                    
                                if (partDescriptionExceptionCountArray) {
                                  if (partDescriptionExceptionCountArray.length) {
                                    console.log(
                                      `partDescriptionExceptionCountArray.length: ${partDescriptionExceptionCountArray.length}`
                                    );
                                    segment.saveSegment(
                                      `partDescriptionExceptionCountArray.length: ${partDescriptionExceptionCountArray.length}`
                                    );
                                    segment.saveSegmentFailure(
                                      `partDescriptionExceptionCountArray.length: ${partDescriptionExceptionCountArray.length}`,
                                      storeCode
                                    );
                    
                                    partDescriptionExceptionCount = partDescriptionExceptionCountArray.length;
                                    console.log('partDescriptionExceptionCount',partDescriptionExceptionCount);
                                    warningObj.partDescriptionExceptionCount = partDescriptionExceptionCount;
                                    if (partDescriptionExceptionCount > 2 )
                                      warningObj.partDescriptionExceptionFilePath =partDescriptionExceptionFilePath;
                                    warningObj.partDescriptionExceptionCount = partDescriptionExceptionCount
                                  }
                                }
                    
                                console.log(`partDescriptionExceptionCount: ${partDescriptionExceptionCount}`);
                                segment.saveSegment(
                                  `partDescriptionExceptionCount: ${partDescriptionExceptionCount}`
                                );
                                segment.saveSegmentFailure(
                                  `partDescriptionExceptionCount: ${partDescriptionExceptionCount}`,
                                  storeCode
                                );
                              }




                        console.log(`Dominion : JSON processing job for Store ${storeName} exited with code ${code}`);
                        segment.saveSegment(`Dominion : JSON processing job for Store ${storeName} exited with code ${code}`);
                        segment.saveSegmentFailure(`Dominion : JSON processing job for Store ${storeName} exited with code ${code}`, uniqueFailLogName)

                        let failureDirectory = process.cwd() + '/logs/Dominion/failure/';
                        let failurelogFile = failureDirectory + uniqueFailLogName + '.log';

                        var extractionErrorResponse;
                        var errorwarningMessage;
                        // var warningObj = {};

                        console.log(stdErrorArray);
                        if (stdErrorArray && !haltOverRide) {
    
                            console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT);
                            console.log(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD);
    
                            stdErrorArray.forEach(async(v, i) => {
                                if (
                                    v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_HALT)
                                ) {
                                    message = "Halt";
                                    haltIdentifier = true;
                                    status = false;
                                    await SetProcessJobStatus.setProcessJobStatusForDominion(dealerID,mageStoreCode,message);
                                }
    
                                if (
                                    v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD)
                                ) {
                                    message = "Dead";
                                    haltIdentifier = true;
                                    status = false;
                                    await SetProcessJobStatus.setProcessJobStatusForDominion(dealerID,mageStoreCode,'Failed');
                                }
    
    
                            });
    
                            try {
                                if (
                                    stdOutArray &&
                                    stdOutArray.includes(errResp) &&
                                    message == "Halt" &&
                                    !haltOverRide
                                ) {
                                    let deadLetterFilePath = deadLetterPath + "/" + basename;
                                    let haltFilePath =
                                        "/home/<USER>/tmp/du-etl-dms-dominionvue-extractor-work/scheduler-temp/halt/" +
                                        basename;
                                    if (fs.existsSync(deadLetterFilePath)) {
                                        fs.copyFile(deadLetterFilePath, haltFilePath, (err) => {
                                            if (err)
                                            {
                                                console.log('err',err);
                                            }
                                            else{
                                            console.log(`${deadLetterFilePath} was copied to ${haltFilePath}`)
                                            }
                                        });
                                    } else {
                                        console.log(`${deadLetterFilePath} not exist!`);
                                    }
                                } else{
                                    console.log('Not a Halt process')
                                }
                            } catch (err) {
                                console.log(err);
                            }
                        }
                       
                        if(extractionId){
                            extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(extractionId, 'Dominion');
                            console.log('extractionErrorResponse:',extractionErrorResponse);
                            if(extractionErrorResponse.status){
                                let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response))
                                let tmpDescritionArray = [];
                                resp.forEach(e => { 
                                    tmpDescritionArray.push(e.description + ':- ' + e.errorMessage + '\n');
                                });
                                errorwarningMessage = tmpDescritionArray;
                            }
                        } 

                        console.log('errorwarningMessage:',errorwarningMessage);
                      
                        if(errorwarningMessage){
                            if(errorwarningMessage.length > 0){
                                warningObj.errorwarningMessage = errorwarningMessage;
                            }
                        }
                        
                        var fetchGroupAndStoreName = (job.attrs.data.inputFile).split('-');
                        var groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : '';
                        var storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : '';

                        var mailTemplateReplacementValues = {
                            dmsType: constants.JOB_TYPE,
                            processTypes: constants.PROCESS_JSON.JOB_NAME,
                            subject: `Process JSON for ${groupName} - ${storeName} Completed`,
                            warningObj: warningObj,
                            thirdPartyUsername: dealerID,
                            storeCode: mageStoreCode,
                            groupCode: mageGroupCode
                        };
                        var mailBody = {
                            fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                            toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                            ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                            attachedfailurelogFile:failurelogFile
                        }

                        job.attrs.data.processorUniqueId = '';
                     
                        if(uniqueId && uniqueId!=undefined){
                          job.attrs.data.processorUniqueId = uniqueId;
                         }
                    
                        if (status) {
                            clearInterval(touch);
                            var opDataFileEtl = path.join(constants.PROCESS_JSON.DOMINION_ETL_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                            var opDataFileDist = path.join(constants.PROCESS_JSON.DOMINION_DIST_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename)
                            opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
                            var outputFile = opDataFileDist + ' & ' + opDataFileEtl;
                            job.attrs.data.outputFile = outputFile;
                            job.attrs.data.status = status;
                            job.attrs.data.message = message;
                            job.attrs.data.warningMessage = warningObj;
                            job.attrs.data.storeName = mageStoreCode;
                            job.attrs.data.SaleZeroCostNonZeroPartsCount = SaleZeroCostNonZeroPartsCount;
                            job.attrs.data.partDescriptionExceptionCount = partDescriptionExceptionCount;
                            
                            
                            segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                            segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, uniqueFailLogName);
                            await job.save();
                            done();
                            var displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Success';
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            // Send notification after process json job completed
                            mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                        } else {
                            clearInterval(touch);
                            // Portal update for process json failed
                            const directoryPath = constants.DOMINION_SCHEDULER_ETI_DIR; 
                            const fileName = basename;                                 
                            const filePath = path.join(directoryPath, fileName);
                            segment.saveSegment(`DEALERTRACK : filePath inpObj Error - ${fileName}`);
                            if (fs.existsSync(filePath)) {
                                fs.unlink(filePath, (err) => {
                                 if(err) {
                                    segment.saveSegment(`DOMINION : Error deleting file - ${err}`);
                                    console.error('Error deleting file:', err);
                                  } else {
                                  segment.saveSegment(`DOMINION : File deleted successfully - ${filePath}`);
                                  console.log('File deleted successfully:', filePath);
                                  }
                                });
                            } else {
                              console.log('File does not exist:', filePath);
                            }
                            
            
                            let todayDate;
                            let attPayload = {};
                            let projectID;
                            let secondProjectID;
                            let inpObjProject;
                            let inpObjSecondProject;
                            let projectIdList;
                            let secondProjectIdList;
                            try{
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = agendaObject[extractedObjectIndex];
                            projectID = attPayload.hasOwnProperty('projectId') ?  attPayload.projectId : ""; 
                            projectIdList = attPayload.hasOwnProperty('projectIds') ?  attPayload.projectIds.split("*") : ""; 
                            secondProjectIdList = attPayload.hasOwnProperty('secondProjectIdList') ?  attPayload.secondProjectIdList.split("*") : ""; 
                            attPayload['inProjectId'] =  projectID;

                            secondProjectID = attPayload.hasOwnProperty('secondProjectId') ?  attPayload.secondProjectId : ""; 
                            attPayload.in_is_update_retrieve_ro =attPayload.hasOwnProperty('solve360Update') ?  attPayload.solve360Update : "";

                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;;
                            attPayload.in_retrive_ro_request_on = todayDate;
                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(projectIds, attPayload, todayDate,haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT :  constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                            console.log(inpObjProject, "******** INP OBJJJJJ ***********"); 
                            if(secondProjectIdList.length>0){
                                inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(secondProjectIdList, attPayload, todayDate,haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT :  constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                console.log(inpObjSecondProject, "******** INP OBJJJJJ ***********"); 
                            }
                            } catch(err){
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`Dominion : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            segment.saveSegment(`Dominion : doPayloadAction inpObjProject - ${JSON.stringify(inpObjProject)}`);
                            segment.saveSegment(`Dominion : doPayloadAction inpObjSecondProject - ${JSON.stringify(inpObjSecondProject)}`);

                            // try {
                            //     segment.saveSegment(`Dominion : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   

                            //     let  projectIdList =  inpObjProject.inProjectId.split("*");
                            //     if(projectIdList){
                            //          for(const id of projectIdList){
                            //             if(id){
                            //                 inpObjProject.inProjectId = id;
                            //                 portalUpdate.doPayloadAction(inpObjProject);
                            //                 console.log(`Dominion Schedule portal call with Project Id FAILURE${id}`);
                    
                            //             }
                            //          }
                            //     } 

                            //     if(secondProjectID){
                            //     segment.saveSegment(`Dominion : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                            //     let  secondProjectIdList =  inpObjSecondProject.inProjectId.split("*");
                            //     if(secondProjectIdList){
                            //         for(const id of secondProjectIdList){
                            //            if(id!=undefined){
                            //                inpObjSecondProject.inProjectId = id;
                            //                portalUpdate.doPayloadAction(inpObjSecondProject);
                            //                console.log(`AutoMate Schedule portal call with Second Project Id FAILURE${id}`);
                   
                            //            }
                            //         }
                            //    } 
                            // }
                            
                            // } catch(error) {
                            //     console.log("Error:", error);
                            //     segment.saveSegment(`Dominion : doPayloadAction Error - ${JSON.stringify(error)}`); 
                            // }
                            //code end for portal update for process json failed
                             
                            try {
                                // segment.saveSegment(`DOMINION : doPayloadAction - ${JSON.stringify(inpObjProject)}`);   
                                
                                // let parsedData = JSON.parse(inpObjProject.inData);
                                // let projectIdList =  parsedData.projectIds.split("*");
                                if(projectIdList  && testData==false ){
                                     for(const id of projectIdList){
                                        if(id!=undefined && id!=''){
                                            inpObjProject = commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate,haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT :  constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                            portalUpdate.doPayloadAction(inpObjProject);
                                            console.log(`DOMINION Schedule portal call with Project Id RESUME${id}`);
                                            segment.saveSegment(`DOMINION Schedule portal call with Project Id RESUME${id}`);
                    
                                        }
                                     }
                                } 
                                if(secondProjectIdList.length>0  && testData==false ){
                                  
                                    // let parsedData = JSON.parse(inpObjSecondProject.inData);
                                    // let secondProjectIdList =  parsedData.secondProjectIdList.split("*");

                                    //  segment.saveSegment(`DOMINION : doPayloadAction for secondProjectID - ${JSON.stringify(inpObjSecondProject)}`);
                   
                                   if(secondProjectIdList){
                                       for(const id of secondProjectIdList){
                                          if(id!=undefined && id!=''){
                                            //  inpObjSecondProject.inProjectId = id;
                                            inpObjSecondProject =  commonUtil.getinpObjFordoPayloadAction(id, attPayload, todayDate,haltIdentifier == true ? constantsCommon.PAYLOAD_IN_ACTION.HALT :  constantsCommon.PAYLOAD_IN_ACTION.FAIL);
                                             portalUpdate.doPayloadAction(inpObjSecondProject);
                                             console.log(`DOMINION Schedule portal call with Second Project Id Resume${id}`);
                                             segment.saveSegment(`DOMINION Schedule portal call with Second Project Id RESUME${id}`);
                   
                                          }
                                      }
                                    } 
                                }
                            } catch(error) {
                                console.log("Error:", error);
                                segment.saveSegment(`DOMINION : doPayloadAction Error - ${JSON.stringify(error)}`); 
                            }

                            job.attrs.data.warningMessage = warningObj;
                            job.attrs.data.storeName = mageStoreCode;
                            job.attrs.data.SaleZeroCostNonZeroPartsCount = SaleZeroCostNonZeroPartsCount;
                            job.attrs.data.partDescriptionExceptionCount = partDescriptionExceptionCount;
                            await job.fail(new Error(`Error: ${message}`));
                            done();
                            if(haltIdentifier){
                            
                                if(message == 'Halt'){
                                    displayMessage = `Halted ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                                    mailTemplateReplacementValues.message = displayMessage;
                                    mailTemplateReplacementValues.status = 'Halted';
                                    mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Halted`;
                                }
                            } else{
                                   if(haltOverRide){
                                        mailTemplateReplacementValues.resumeUser = resumeUser ? resumeUser : '';
                                   } 
                            var displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = 'Failed';
                            mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Failed`;
                                }
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            // Send notification for failed process xml job
                            mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                            mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                            segment.saveSegmentFailure(displayMessage, uniqueFailLogName);
                            // Send notification for failed process json job
                            await segment.sleep(2000);
                            mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                        }
                        var basenameCheck = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        var distFile = path.join(constants.PROCESS_JSON.DOMINION_BUNDLE_DIR, basenameCheck)
                        if (status && fs.existsSync(distFile)) {
                            basename = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                            segment.saveSegment(`Start distribute file process`);
                             if(totalRoCount && totalRoCount!=undefined){
                                updateSolve360Data.totalRoCount = totalRoCount;
                               }else{
                                 updateSolve360Data.totalRoCount = 0;
                              }
                              if(exceptionTypeCounts){
                                updateSolve360Data.exceptionTypeCounts = exceptionTypeCounts;
                             }else{
                                 updateSolve360Data.exceptionTypeCounts = null;
                         }   

                            await distributeFile(basename, null, updateSolve360Data, warningObj, job.attrs._id);
                        } else {
                            // Process JSON Job Fail ....
                            segment.saveSegment(`Call for next job selection`);
                            await doNextProcess();
                        }
                    });  
            } else {
                /**
                * Remove the Initial/recheck schedules
                */
                job.remove(err => {
                    segment.saveSegment(`Inside Job remove function`);
                    segment.saveSegment(`job: ${JSON.stringify(job)}`);
                    if (!err) {
                        segment.saveSegment(`Job removed successfully`);
                        console.log("Initial/recheck schedule for Dominion Process JSON job successfully removed");
                    } else{
                        segment.saveSegment(`Job removal process have error : ${JSON.stringify(err)}`);
                    }
                });
                done();
                segment.saveSegment(`Input file not found, call  doNextProcess()`);
                await doNextProcess();
            }
        });

    return agenda;
}
