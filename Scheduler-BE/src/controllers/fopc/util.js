"use strict";

const moment = require("moment-timezone");
const constants = require("./constants");

const fs = require("fs");
const path = require("path");
const util = require("util");
const mv = require("mv");
const unZipper = require("unzipper");
const readdirAsync = util.promisify(fs.readdir);
const statAsync = util.promisify(fs.stat);
const agendaDbModel = require("../../model/agendaDb");

function setStartTimeForUser(inTime) {
    let retTime = inTime.hour(constants.FOPC.START_TIME.split(":")[0]);
    retTime = retTime.minute(constants.FOPC.START_TIME.split(":")[1]);
    retTime = retTime.second(0);
    retTime = retTime.millisecond(0);
    retTime = retTime.utc();
    return retTime;
}

function getZonedTime(inTime) {
    let retTime = moment().utc();
    retTime = retTime.tz(constants.FOPC.TIME_ZONE)
        .hour(inTime.split(":")[0])
        .minute(inTime.split(":")[1])
        .second(0)
        .millisecond(0)
        .utc();
    return retTime;
}

function getTimeFrame() {
    let startTime = getZonedTime(constants.FOPC.START_TIME);
    let endTime = getZonedTime(constants.FOPC.END_TIME);
    if (startTime.isAfter(endTime)) {
    /**
     * This is main condition for checking the time-frame.
     * If the time-frame start time is greater than the
     * end time, then it should be yesterday's night time.
     * So we should subtract one day from the start time.
     */
    if (moment().tz(constants.FOPC.TIME_ZONE).format("A") == "AM") {
    startTime = startTime.subtract(1, "days");
    } else if (moment().tz(constants.FOPC.TIME_ZONE).format("A") == "PM") {
    endTime = endTime.add(1, "days");
    }
    }
    return {
        startTime: startTime,
        endTime: endTime
    };
}

/**
 * Function to check the time frame for data extraction which is set in the env file
 */
function checkExtractTimeFrame() {
    let timeFrame = getTimeFrame();
    let startTime = timeFrame.startTime;
    let endTime = timeFrame.endTime;
    let currentUTCDate = moment().utc();
    return currentUTCDate.isAfter(startTime) && currentUTCDate.isBefore(endTime);
}

/**
 * Function to convert GraphQLDate string to common date format
 * @param {string} dateStr GraphQL GraphQLDateTime string
 */
function toDate(dateStr) {
    let inDate = moment(dateStr).format("YYYY-MM-DDTHH:mm:ssZZ");
    let timeFrame = getTimeFrame();
    let startTime = timeFrame.startTime;
    let endTime = timeFrame.endTime;
    let userTimeInput = moment(inDate).utc().tz(constants.FOPC.TIME_ZONE);
    userTimeInput = userTimeInput.utc();
    let currentUTCDate = moment().utc().tz(constants.FOPC.TIME_ZONE);
    currentUTCDate = currentUTCDate.utc(); // Convert user input to UTC

    let ret = "";
    if (userTimeInput.isSame(currentUTCDate, "year") &&
        userTimeInput.isSame(currentUTCDate, "month") &&
        userTimeInput.isSame(currentUTCDate, "day")) { // back date entry not permitted
        if (userTimeInput.isBetween(startTime, endTime)) {
            /**
             * Add POOL_END_CHECKING minutes to user input time and check if same or before
             * with timeFrame end time value. If yes, add POOL_ADD minutes to user input time
             * otherwise reschedule to next day.
             */
            if (moment(userTimeInput).add(constants.FOPC.POOL_END_CHECKING, "minutes").isSameOrBefore(endTime)) {
            ret = userTimeInput.add(constants.FOPC.POOL_ADD, "seconds");
            } else {
            ret = startTime.add(1, "days");
            }

        } else if (userTimeInput.isBefore(startTime)) {

            ret = startTime;
        } else if (userTimeInput.isAfter(endTime)) {

            ret = startTime.add(1, "days");

        }
        ret = userTimeInput.add(constants.FOPC.POOL_ADD, "seconds");
    } else if (userTimeInput.isAfter(currentUTCDate)) {
        ret = setStartTimeForUser(userTimeInput);
    } else {
        throw ("Back date entry");
    }
    return ret;

}

// moment usage samples for reference
// console.log(`Asia: ${moment.tz(moment(), "Asia/Karachi").format("DD/MM/YYYY HH:MM")}`);
// console.log(`America/Chicago: ${moment.tz(moment(), "America/Chicago").format("DD/MM/YYYY HH:MM")}`);


/**
 * Function to get schedule for tomorrow in case of auto scheduling
 */
function getScheduleForTomorrow() {
    let nextScheduleOfToday = getTimeFrame().startTime;
    let nextScheduleOfTomorrow = nextScheduleOfToday.add(1, "days");
    return nextScheduleOfTomorrow;
}

/**
 * Find the oldest zip file (FIFO order of store data extracted) from the zipPath,
 * extract the zip and return the path to ROs.json file.
 */

async function getOldestZipFileAndReturnROFolder(zipPath) {
  const basePath = path.join(zipPath);
  const files = await readdirAsync(basePath);
  const zipFileList = files.filter(e => path.extname(e).toLowerCase() === ".zip");
  
  if (zipFileList.length === 0) {
    return null;
  }

  const stats = await Promise.all(
    zipFileList.map(async (filename) => {
      const stat = await statAsync(path.join(basePath, filename));
      return { filename, stat };
    })
  );

  const sortedZipFiles = stats
    .sort((a, b) => a.stat.mtime.getTime() - b.stat.mtime.getTime())
    .map(stat => stat.filename);

  const fileNameToProcess = sortedZipFiles[0];
  const zipToProcess = path.join(basePath, fileNameToProcess);
  const extractFolder = path.join(basePath, fileNameToProcess.slice(0, -4)); // Remove '.zip'

  console.log(`Extracting store details from zip file ${zipToProcess} to ${extractFolder}`);

  // Return a new promise that resolves when unzip finishes
  return new Promise((resolve, reject) => {
    fs.createReadStream(zipToProcess)
      .pipe(unZipper.Extract({ path: extractFolder }))
      .on("close", () => {
        // Move zip to processed location
        const processedDir = path.join(basePath, "processed");
        if (!fs.existsSync(processedDir)) {
          fs.mkdirSync(processedDir);
        }
        const zipPathToMove = path.join(processedDir, fileNameToProcess);
        mv(zipToProcess, zipPathToMove, { clobber: true }, function (err) {
          if (err) {
            console.error(err);
            reject(err);  // reject on error
            return;
          }
          console.log(`${zipToProcess} moved to ${zipPathToMove}`);
          resolve(extractFolder);
        });
      })
      .on("error", reject);  // reject if unzip errors
  });
}

async function findOldestZipFile(zipPath) {
  const basePath = path.join(zipPath);
  const files = await readdirAsync(basePath);
  const zipFileList = files.filter(e => path.extname(e).toLowerCase() === ".zip");

  if (zipFileList.length === 0) {
    return null;
  }

  const stats = await Promise.all(
    zipFileList.map(async (filename) => {
      const stat = await statAsync(path.join(basePath, filename));
      return { filename, stat };
    })
  );

  const sortedZipFiles = stats
    .sort((a, b) => a.stat.mtime.getTime() - b.stat.mtime.getTime())
    .map(stat => stat.filename);

  let newSortedFiles = [];

  // Fetch current queue once, outside the loop
  const queueList = await agendaDbModel.fetchJobFromProcessorQueue("fopc_queue");
  const queueResponse = queueList.response;

  // Build a Set of filenames already in the queue for quick lookup
  const queueFileNames = new Set(queueResponse.map(item => item.fileName));

  // Filter out files already in the queue
  for (const fileName of sortedZipFiles) {
    if (!queueFileNames.has(fileName)) {
      newSortedFiles.push(fileName);
    }
  }

  // Add new files to the processor queue
  const addToQueuePromises = newSortedFiles.map(async (fileNameToProcess) => {
    const uniqueExtractionId = Date.now();
    const parentNameResponse = await agendaDbModel.fetchParentName(fileNameToProcess.trim());
    const parentName = parentNameResponse.response;
    const queueItem = {
      extractionId: uniqueExtractionId,
      fileName: fileNameToProcess,
      priority: 7,
      parentName: parentName,
    };

    // Return a promise for addToProcessorQueue; assuming it supports Promise or callback
    return new Promise((resolve, reject) => {
      agendaDbModel.addToProcessorQueue(queueItem, "fopc_queue", (result) => {
        // You can handle result if needed
        resolve(result);
      });
    });
  });

  try {
    await Promise.all(addToQueuePromises);
  } catch (error) {
    console.error("Error adding items to queue:", error);
    throw error;  // Throw so the caller knows
  }

  // Get updated queue list and delete the oldest file from queue
  const updatedQueueList = await agendaDbModel.fetchJobFromProcessorQueue("fopc_queue");
  const updatedQueueResponse = updatedQueueList.response;
  const nextFile = updatedQueueResponse.length > 0 ? updatedQueueResponse[0].fileName : null;

  if (nextFile) {
    await agendaDbModel.deleteJobFromProcessorQueue(nextFile, "fopc_queue");
    return nextFile;
  } else {
    return null;
  }
}

async function updateProcessorQueue(zipPath, currentRunningFile) {
  const basePath = path.join(zipPath);
  const files = await readdirAsync(basePath);
  const zipFileList = files.filter(e => path.extname(e).toLowerCase() === ".zip");

  if (zipFileList.length === 0) {
    return true;
  }

  const stats = await Promise.all(
    zipFileList.map(async (filename) => {
      const stat = await statAsync(path.join(basePath, filename));
      return { filename, stat };
    })
  );

  const sortedZipFiles = stats
    .sort((a, b) => a.stat.mtime.getTime() - b.stat.mtime.getTime())
    .map(stat => stat.filename);

  let newSortedFiles = [];

  if (sortedZipFiles.length > 0) {
    const queueList1 = await agendaDbModel.fetchJobFromProcessorQueue("fopc_queue");
    const queueResponse1 = queueList1.response || [];

    const queuedFileNames = new Set(queueResponse1.map(item => item.fileName));
    queuedFileNames.add(currentRunningFile);

    for (const fileNameToProcess of sortedZipFiles) {
      if (!queuedFileNames.has(fileNameToProcess)) {
        newSortedFiles.push(fileNameToProcess);
      }
    }
  }

  const addToQueuePromises = newSortedFiles.map(async (fileNameToProcess) => {
    const uniqueExtractionId = Date.now();
    const parentName = (await agendaDbModel.fetchParentName(fileNameToProcess.trim())).response;
    const queueItem = {
      extractionId: uniqueExtractionId,
      fileName: fileNameToProcess,
      priority: 7,
      parentName,
    };

    return new Promise((resolve, reject) => {
      agendaDbModel.addToProcessorQueue(queueItem, "fopc_queue", (err, result) => {
        if (err) return reject(err);
        resolve(result);
      });
    });
  });

  await Promise.all(addToQueuePromises);

  const runningFilePath = `/home/<USER>/tmp/du-etl-dms-fopc-extractor-work/scheduler-temp/fopc-zip-eti/${currentRunningFile}`;
  if (fs.existsSync(runningFilePath)) {
    await agendaDbModel.deleteJobFromProcessorQueue(currentRunningFile, "fopc_queue");
  }

  return true;
}




/**
 * Find the names of the zip files to extract and return with store names (slice from zip name).
 */

async function getQueuedStoresAndZipNames(zipPath) {
  const basePath = path.join(zipPath);
  const files = await readdirAsync(basePath);
  const zipFileList = files.filter(e => path.extname(e).toLowerCase() === ".zip");

  if (zipFileList.length === 0) {
    return [];
  }

  const stats = await Promise.all(
    zipFileList.map(async (filename) => {
      const stat = await statAsync(path.join(basePath, filename));
      return { filename, stat };
    })
  );

  const sortedZipFiles = stats
    .sort((a, b) => a.stat.mtime.getTime() - b.stat.mtime.getTime())
    .map(stat => stat.filename);

  const retArray = sortedZipFiles.map(sortedZipFile => {
    const zipToProcess = path.join(basePath, sortedZipFile);
    // Extract storeID from the filename (second last part after splitting by '-')
    const parts = sortedZipFile.split("-");
    const storeID = parts.length >= 2 ? parts[parts.length - 2] : "";
    return { storeID, fileToProcess: zipToProcess };
  });

  return retArray;
}


function generateUniqueId() {
    const now = new Date();

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hour = String(now.getHours()).padStart(2, "0");
    const minute = String(now.getMinutes()).padStart(2, "0");
    const second = String(now.getSeconds()).padStart(2, "0");
    const millisecond = String(now.getMilliseconds()).padStart(3, "0");

    const randomNumber = String(Math.floor(Math.random() * 1000)).padStart(3, "0");

    const uniqueId = `ta${year}${month}${day}${hour}${minute}${second}${millisecond}${randomNumber}`;

    return uniqueId;
}

module.exports = {
    toDate: toDate,
    checkExtractTimeFrame: checkExtractTimeFrame,
    getScheduleForTomorrow: getScheduleForTomorrow,
    getOldestZipFileAndReturnROFolder: getOldestZipFileAndReturnROFolder,
    findOldestZipFile: findOldestZipFile,
    getTimeFrame: getTimeFrame,
    getQueuedStoresAndZipNames: getQueuedStoresAndZipNames,
    updateProcessorQueue:updateProcessorQueue,
    generateUniqueId:generateUniqueId
};
