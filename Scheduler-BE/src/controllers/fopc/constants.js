// Read all env values
const fs = require("fs");
const path = require("path");

/**
 * Configuration file (.env) path
 */
const ENV_PATH = path.join(process.env.HOME + "/.fopc/.env"); // Hidden folder in user's HOME path

if (!fs.existsSync(ENV_PATH)) {
    console.error(`Configuration file ${ENV_PATH} not accessible or exist; Please run  ../install script`);
    process.exit(1);
}
const result = require("dotenv").config({ path: ENV_PATH });
if (result.error) {
    throw result.error;
}
const TEMP_PATH = path.join(process.env.FOPC_WORK_DIR + "/scheduler-temp");
if (!fs.existsSync(TEMP_PATH)) {
    fs.mkdirSync(TEMP_PATH);
}
if (!fs.existsSync(TEMP_PATH + "/extract")) {
    fs.mkdirSync(TEMP_PATH + "/extract");
}
if (!fs.existsSync(path.join(TEMP_PATH, "fopc-zip-eti"))) {
    fs.mkdirSync(path.join(TEMP_PATH, "fopc-zip-eti"));
}

const env = process.env;

module.exports = {
    JOB_PRIORITY: {
        HIGHEST: "highest",
        HIGH: "high",
        LOW: "low",
        LOWEST: "lowest",
        NORMAL: "normal"
    },

    FOPC_SCHEDULER_ETI_DIR: path.join(TEMP_PATH, "fopc-zip-eti"),
    FOPC_DEADLETTER_DIR_PREFIX: env.FOPC_DEADLETTER_DIR_PREFIX,
    JOB_TYPE: "FOPC",
    DMS:"FOPC",
    MISC_EXCEPTION_CSV_FILE_PATH: "/home/<USER>/tmp/du-etl-dms-fopc-extractor-work/exception/misc-exception.csv",
    /**
    * FOPC job related parameters
    */
    FOPC: {
        JOB_NAME: "FOPC",
        JOB_EXTRACT_NAME: "FOPC_EXTRACT",
        JOB_TYPE_INITIAL: "initial",
        JOB_TYPE_REFRESH: "refresh",
        JOB_TYPE_ON_DEMAND: "ondemand",
        CONCURRENCY: 20, // Can run upto CONCURRENCY no of stores in parallel
        EXTRACT_CMD: "fopc-requestor",
        PULL_OP: "pull",
        OPT_DEALER_ID: "--dealerID",
        OPT_START_DATE: "--startDate",
        OPT_END_DATE: "--endDate",
        OPT_OUTPUT_PATH: "--outputDir",
        OPT_ZIP_PATH: "--zipPath",
        OPT_OPEN: "--open",
        OPT_CLOSED: "--closed",
        OPT_CUSTOMER: "--customer",
        OPT_DELTA_DATE: "--deltaDate",
        OPT_RO_WIP: "--rowip",
        PORSCHE_STORE_LABEL:"PORSCHE",
        OPT_CLOSED_CRITERIA_MONTHLY: "monthly",
        OPT_CLOSED_CRITERIA_WEEKLY: "weekly",
        OPT_CLOSED_CRITERIA_ALL: "all",
        OPT_CLOSED_CRITERIA_CURRENT: "current",
        OPT_MODEL: "--model",
        OPT_EMPLOYEE: "--employee",
        OPT_ZAP_AFTER_ZIP: "--zapAfterDist",
        OPT_DEADLETTER_DIR_PREFIX: "--deadLetter",
        OPT_MAGE_GROUP_CODE: "--mageGroupCode",
        OPT_MAGE_STORE_CODE: "--mageStoreCode",
        OPT_BUNDLE: "--bundle",
        OPT_BUILD_PROXY_USING: "--build-proxies-using",
        HCUST_BULK: "HCUST_Bulk",
        HCUST_DELTA: "HCUST_Delta",
        START_TIME: env.FOPC_EXTRACT_START_TIME ? env.FOPC_EXTRACT_START_TIME : "22:00",
        END_TIME: env.FOPC_EXTRACT_END_TIME ? env.FOPC_EXTRACT_END_TIME : "00:00",
        TIME_ZONE: env.FOPC_EXTRACT_ZONE ? env.FOPC_EXTRACT_ZONE : "America/Chicago",
        EXTRACT_FILE_PATH: TEMP_PATH + "/extract",
        ZIP_PATH: env.EXTRACT_ZIP_PATH,
        POOL_END_CHECKING: 30, // 30 minutes
        POOL_ADD: 30, // 5 minutes
        //For MOCK SERVER ,
        MOCKSERVER_SOURCE_FOLDER_PATH: "/home/<USER>/mock-server/",
        MOCKSERVER_DESTINATION_FOLDER_PATH_FOPC: "/home/<USER>/tmp/du-etl-dms-fopc-extractor-work/scheduler-temp/fopc-zip-eti/",
        ENV_MOCKSERVER: env.ENV_MOCKSERVER
    },
    SCHEDULE: {
        SUCCESS: "Job schedule saved successfully",
        OVERRIDE_SUCCESS: "Job schedule updated successfully",
        CANCEL_EXISTING: "Already existing schedule canceled and new ",
        STARTED: "Job started",
        CANCELED: "Job canceled",
        NO_MATCHING: "No matching job found"
    },
    PROCESS_JSON: {
        JOB_NAME: "FOPC-PROCESS-JSON",
        CONCURRENCY: 1, // Can run upto CONCURRENCY no of Process JSON jobs parallel,
        PROCESS_CMD: "process-json",
        PROCESS_CMD_PATH: process.env.DU_ETL_HOME + "/DU-DMS/DMS-FOPC/Processor-Application",
        FOPC_DISTRIBUTE_CMD_PATH: process.env.DU_ETL_HOME + "/DU-DMS/DMS-FOPC",
        OPT_CLOSED: "--closed",
        OPT_INPUT_DIR: "--input-dir",
        OPT_INPUT_ZIP: "--input-zip",
        OPT_BUNDLE_DIR: "--bundle-dir",
        OPT_BUNDLE_ID: "--bundle-id",
        OPT_PERFORM_ZIP: "--zip",
        OPT_PERFORM_NO_ZIP: "--no-zip",
        OPT_ZAP_INPUT: "--zap-input",
        OPT_BUILD_PROXY_RO: "--build-proxies",
        OPT_NO_BUILD_PROXY_RO: "--no-build-proxies",
        OPT_BUILD_PROXY_USING: "--build-proxies-using",
        TIME_GAP: "after 60 seconds",
        OPT_PORSCHE_STORE:"--porsche-store",
        FOPC_BUNDLE_DIR: process.env.FOPC_BUNDLE_DIR,
        OPT_DEADLETTER_DIR_PREFIX: "--dead-letter",
        OUTPUT_PREFIX:"--output-prefix",
        OUTPUT_PREFIX_VAL: process.env.FOPC_PROCESS_JSON_OUTPUT_DIR_PREFIX,
        FOPC_DIST_DIR: process.env.FOPC_DIST_DIR,
        FOPC_ETL_DIR: process.env.FOPC_ETL_DIR,
        DEALER_ADDRESS:"--dealerAddress",
        REPLACE_STRING: {
            FROM:".zip",
            TO:"-ETL.zip",
            DIR_PREFIX:"PROC-",
            DO_PROXY_FROM:"-RERUN",
            DO_PROXY_TO:"-DO-PROXY-ETL",
        },
        ERROR_CHECKING_LABELS: {
            ZIP_FILE_EXIST_CHECK: "Zip File Must Exist",
            ZIP_FILE_PROCESSING_FAILED: "Zip File Processing Failed",            
            PROCESSOR_DEAD:"Process is DEAD!",
        }
    },
    DO_PROXY_MSG:{
        COPY_FILE: "Error while copying the file!",
        FILE_MISSING:"Source file missing",
        FAILED_TO_CREATE_JOB:"Error while do the Process XML Job!",
        JOB_CREATION_SUCCESS:"Job Created Successfully"
    },
    STATUS_CODE: {
        SUCCESS: 0,
        GENERAL_DEATH: 1,
        DEADLETTER_PATH: 2
    }
};
