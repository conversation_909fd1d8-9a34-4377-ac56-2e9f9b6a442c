const fopcJobManager = require("../fopcJobManager");
const validator = require("validator");

const resolvers = {
  Query: {

    /**
    * Query to get all fopc-Extract schedules
    * @param {object} _ GraphQL root object
    * @param {object} args User arguments
    */
    async getAllFopcExtractJobs(_, args) {
      return fopcJobManager.getAllFopcExtractJobs();
    },

    /**
     * Query to get all fopc Process-JSON Jobs
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    async getAllFopcProcessJSONJobs(_, args) {
      return fopcJobManager.getAllFopcProcessJSONJobs();
    }
  },
  Mutation: {

    /**
     * Mutation to schedule fopc-Extract job of different stores under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    scheduleFopcExtractJob(_, args) {
      return fopcJobManager.scheduleFopcExtractJob(args.input);
    },

    /**
     * Mutation to run a fopc-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    runNowFopcExtractJobByStore(_, args) {
      return fopcJobManager.runNowFopcExtractJobByStore(args.input);
    },

    /**
     * Mutation to cancel a fopc-Extract job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */
    cancelFopcExtractJobByStore(_, args) {
      return fopcJobManager.cancelFopcExtractJobByStore(args.input);
    },
    
        /**
     * Mutation to create a Process XML job job of a store under a Group
     * @param {object} _ GraphQL root object
     * @param {object} args User arguments
     */


    createProxyWithSqlDump(_, args) {
      return fopcJobManager.createProxyWithSqlDump(args.input);
    },
  },


  Date: {
    __serialize(value) {

      return value;
    },

    __parseValue(value) {
      let parts = value.split("-");
      return [parts[0], parts[1], parts[2]].join("/");
    },
    __parseLiteral(ast) {
      let dateStr = JSON.parse(JSON.stringify(ast)).value;
      let parts = dateStr.split("-");
      return [parts[1], parts[0], parts[2]].join("/");
    }
  },

  DateTime: {
    __serialize(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseValue(value) {
      if (validator.isISO8601(value)) {
        return value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    },
    __parseLiteral(ast) {
      if (validator.isISO8601(ast.value)) {
        return ast.value;
      }
      throw new Error("DateTime cannot represent an invalid ISO-8601 Date string");
    }
  }
};
module.exports = resolvers;
