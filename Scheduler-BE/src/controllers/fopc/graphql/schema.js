const gt = require("graphql-tools");
const resolvers = require("./resolver");

const typeDefs = `

# This type specifies the entry points into our API

#Scalar types
scalar Date
scalar DateTime
scalar _bsontype

# Type of fopc extract Job
enum JobType {
  initial
  refresh
  ondemand
}

# close RO options
enum ClosedROOption {
  all
  monthly
  weekly
  current
}

input SingleStoreJobData{
  groupName: String!
  storeData: ExtractData!
}

type Data{
  groupName: String # Optional
  storeDataArray: [StoreData]
}

type QueueData{
  storeID: String,
  fileToProcess: String,
  priority:String
}

type JSONProcessData {
  operation: String # Avoid operation = recheck or operation = start
  inputFile: String
  storeID: String
  outputFile: String
  status: Boolean
  message: String
  createdAt: String
  multiLaborExceptionCount: Int
  address:String
  processorUniqueId:String
}

type StoreData {
  dealerId: String!
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  startTime: String
  endTime:String
  status: Boolean
  message: String
  jobType: JobType
  mageGroupCode: String
  mageStoreCode: String
  stateCode: String
  dealerAddress: String
  companyIds:String
  testData:Boolean
  companyObj:String
  processFileName:String
  parentName:String
  brands:String
}

type ExtractJob{
  timeFrameZone: String
  timeFrameStartTime: String
  timeFrameEndTime:String
  poolTime:Int
  jobArray: [Job]
}

type Job {
  name: String
  data:Data
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
}

type ProcessJSONJob {
  name: String
  data:JSONProcessData
  type: String
  priority: String
  nextRunAt: String
  _id: _bsontype
  lastModifiedBy: String
  lockedAt: String
  lastRunAt: String
  lastFinishedAt: String
  running: Boolean
  scheduled: Boolean
  queued: Boolean
  completed: Boolean
  failed: Boolean
  repeating: Boolean
  failReason: String
  uploadStatus: Boolean
}

# Input to pass custom JSON data to a schedule
input JobData{
  groupName: String!
  storeDataArray: [ExtractData]!
}

input ExtractData {
  dealerId: String!
  mageManufacturer: String
  solve360Update: Boolean
  buildProxies: Boolean
  userName: String
  startDate: Date!
  endDate: Date!
  zipPath: String
  closedROOption: ClosedROOption
  jobType: JobType
  mageGroupCode: String!
  mageStoreCode: String!
  stateCode: String!
  dealerAddress: String
  companyIds:String
  testData:Boolean
  companyObj:String
  parentName:String
  groupCode:String
  mageStoreName:String
  errors: String
  thirdPartyUsername: String
  assignedtoCn: String
  brands:String
}

input fopcScheduleInput{
  jobSchedule: DateTime!
  jobData: JobData!
}

input CancelFopcScheduleInput{
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}

#Input to run a particular fopc Store's  extraction
input RunNowFopcScheduleInput {
  jobSchedule: DateTime!
  jobData: SingleStoreJobData!
}
input ProxyGenerationUsingPgDump{
  zipPath: String!

}

input CreateProxyInput{
  proxyJobData: ProxyGenerationUsingPgDump!
}

# Represents the Status of each Mutations
type Status {
  status: Boolean!
  message: String!
  job: Job
}

type fopcProcessJSONInfo{
  processJSONJobsQueue: [QueueData],
  processJSONJobs: [ProcessJSONJob]
}

type Query {
  getAllFopcProcessJSONJobs: fopcProcessJSONInfo
  getAllFopcExtractJobs: ExtractJob
}

# Mutations
type Mutation {
  # Mutation to schedule a fopc extraction job
  scheduleFopcExtractJob (input: fopcScheduleInput!): Status,
  cancelFopcExtractJobByStore (input: CancelFopcScheduleInput!): Status,
  runNowFopcExtractJobByStore (input: RunNowFopcScheduleInput!): Status,
  createProxyWithSqlDump (input: CreateProxyInput!): Status
}

schema {
  query: Query,
  mutation: Mutation
}
`;
let schema = gt.makeExecutableSchema({ typeDefs, resolvers });
module.exports = schema;
