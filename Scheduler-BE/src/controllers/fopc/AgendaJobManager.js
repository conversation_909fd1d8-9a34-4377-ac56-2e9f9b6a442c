const constants = require("./constants");
const segment = require("../SEGMENT/segmentManager");

module.exports = async function loadJobs(agenda) {
  const jobTypes = process.env.JOB_TYPES ? process.env.JOB_TYPES.split(",") : [];
  let initializefopcProcessJSON = false;

  jobTypes.forEach(type => {
    if (type == "fopc-process-json") {
      initializefopcProcessJSON = true;
    }
    if (type.split("-")[0] == "fopc") {
      require("./jobs/" + type)(agenda);
    }
  });

  // Initial startup schedule to immediately start and run the fopc-process-json job
  // and the rest of the operations will be done by the Job itself.
  if (initializefopcProcessJSON) {
    try {
      // Initialize fopc Process JSON job
      await agenda.now(constants.PROCESS_JSON.JOB_NAME, { operation: "start" });
      console.log("FOPC :  Process JSON schedule started");
      segment.saveSegment("FOPC :  Process JSON schedule started");
    } catch (error) {
      console.error(error);
    }
  } else {
    console.log("fopc Extraction Processing Not Enabled - Pass Job Type fopc-process-json To Enable");
    segment.saveSegment("fopc Extraction Processing Not Enabled - Pass Job Type fopc-process-json To Enable");
  }
  return true;
};
