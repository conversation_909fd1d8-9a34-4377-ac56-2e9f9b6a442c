const constants = require("./constants");
const constantsCommon = require("../../common/constants");
const ObjectID = require("mongodb").ObjectID;
const AgendaJobManager = require("../agenda");
const util = require("./util");
const segment = require("../SEGMENT/segmentManager");
const util_rerun = require("../../common/util");


const fs = require("fs");
const path = require("path");
const moment = require("moment-timezone");
const agendaDbModel = require("../../model/agendaDb");
/**
 * Function to find the unique stores in an array of stores
 */
Array.prototype.unique = function () {
    let a = this.concat();
    for (let i = 0; i < a.length; ++i) {
        for (let j = i + 1; j < a.length; ++j) {
            if (a[i].dealerId === a[j].dealerId)
                a.splice(j--, 1);
        }
    }
    return a;
};

function getReturnObject(status, message, object, error) {
    if (status) {
        console.log(message, object ? JSON.stringify(object) : "");
    } else {
        console.error(error);
    }
    return {
        status: status,
        message: message,
        job: object
    };
}

/**
 * Returns all the fopc-Extract schedules
 */
async function getAllFopcExtractJobs() {
    return (await getAgendaJobs(AgendaJobManager, constants.FOPC.JOB_NAME));
}

/**
 * Returns all the fopc Process-JSON schedules
 */
async function getAllFopcProcessJSONJobs() {
    return (await getAgendaJobs(AgendaJobManager, constants.PROCESS_JSON.JOB_NAME));
}


/**
 * Function to schedule fopc-Extract job of different stores under a Group
 * @param {*} UserInput
 */
async function scheduleFopcExtractJob(UserInput, innerCall) {
    segment.saveSegment(`FOPC :  Schedule FOPC Extract Job - ${JSON.stringify(UserInput)}`);
    
    //Portal update on Schedule a job configuration
    console.log(JSON.stringify(UserInput));
    let todayDate;
    let attPayload = {};
    let companyObj;
    try{
        todayDate = new Date().toISOString().slice(0, 10);
        attPayload = UserInput.jobData.storeDataArray[0];
        attPayload.in_is_update_retrieve_ro = Object.prototype.hasOwnProperty.call(UserInput.jobData.storeDataArray[0], "solve360Update") ?  UserInput.jobData.storeDataArray[0].solve360Update : "";
        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;
        attPayload.in_retrive_ro_request_on = todayDate;
        companyObj = Object.prototype.hasOwnProperty.call(UserInput.jobData.storeDataArray[0], "companyObj") ?  UserInput.jobData.storeDataArray[0].companyObj : "";
        attPayload.companyObj = companyObj;
    } catch(err){
        console.log(JSON.stringify(err));
        segment.saveSegment(`FOPC : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
    }
    let scheduleDateZ = innerCall ? UserInput.jobSchedule : util.toDate(UserInput.jobSchedule);
    /**
     * Find jobs with same Group name and date from Agenda
     * matching with user supplied Group name and schedule date
     */
    let jobs = null;
    jobs = await AgendaJobManager.jobs(
        {
            $and: [
                { "data.groupName": UserInput.jobData.groupName },
                {"data.storeDataArray.dealerId": UserInput.jobData.dealerId},
                { lastFinishedAt: null } // Jobs that not finished; Only scheduled
            ]
        });
    segment.saveSegment(`FOPC : jobs - ${JSON.stringify(jobs)}`);  
    if (jobs[0]) { // Same group and same date job found
        let oldStoreArray = jobs[0].attrs.data.storeDataArray;
        let newStoreArray = UserInput.jobData.storeDataArray;
        // Merges both arrays and gets unique items
        let concatArrayFlag = false;
        oldStoreArray.map(data => {
            if (data.dealerId === newStoreArray[0].dealerId && data.mageStoreCode === newStoreArray[0].mageStoreCode) {
                concatArrayFlag = true;
                data = newStoreArray;
            }
        });
        let _storeArray = oldStoreArray;
        if (!concatArrayFlag) {
            _storeArray = newStoreArray.concat(oldStoreArray);
        }
        jobs[0].attrs.data.storeDataArray = _storeArray;   
        jobs[0].save();
        segment.saveSegment(`FOPC 1 : jobs[0] - ${JSON.stringify(jobs[0].attrs)}`);  
        return getReturnObject(true, constants.SCHEDULE.OVERRIDE_SUCCESS, jobs[0].attrs, null);
    } else {
        try {            
            let job = await AgendaJobManager.schedule(
                new Date(scheduleDateZ), constants.FOPC.JOB_NAME, UserInput.jobData
            );
            return getReturnObject(true, constants.SCHEDULE.SUCCESS, job.attrs, null);
        } catch (error) {
            return getReturnObject(false, error.message, null, error);
        }
        segment.saveSegment(`FOPC 2 : job - ${JSON.stringify(job)}`);
    }
}

/**
 * Function to cancel a fopc-Extract job job of a store under a Group
 * @param {*} UserInput
 */
async function cancelFopcExtractJobByStore(UserInput) {
    segment.saveSegment(`FOPC :  cancel fopc Extract Job By Store ${JSON.stringify(UserInput)}`);
    let scheduleDateZ = UserInput.jobSchedule;

    //Portal update on Schedule a job configuration
    let todayDate;
    let attPayload = {};
    try{
        todayDate = new Date().toISOString().slice(0, 10);
        attPayload = UserInput.jobData.storeData;

        attPayload.in_is_update_retrieve_ro = Object.prototype.hasOwnProperty.call(UserInput.jobData.storeData, "solve360Update") ?  UserInput.jobData.storeData.solve360Update : "";

        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;
        attPayload.in_retrive_ro_request_on = todayDate;
    } catch(err){
        console.log(JSON.stringify(err));
        segment.saveSegment(`FOPC : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
    }
    /**
     * Find jobs with same Group name and date from Agenda
     * matching with user supplied Group name and schedule date
     */
    let jobs = await AgendaJobManager.jobs({
        $and: [
            { "data.groupName": UserInput.jobData.groupName },
            { nextRunAt: new Date(scheduleDateZ) }
        ]
    });
    let toCancelStoreData = UserInput.jobData.storeData;
    if (jobs[0]) { // Same group and same date job found

        let oldStoreArray = jobs[0].attrs.data.storeDataArray;
        /**
         * Check and remove the store if it exist in any of the schedule
         * with same Group name and date
         */
        let storesToUpdate = oldStoreArray.filter((store) => {
            return store.dealerId != toCancelStoreData.dealerId;
        });
        if (storesToUpdate.length > 0) { // Check whether any store remains in the existing schedule and save if yes
            jobs[0].attrs.data.storeDataArray = storesToUpdate;
            jobs[0].save();
            return getReturnObject(true, constants.SCHEDULE.CANCELED, jobs[0].attrs, null);
        } else { // cancel the job with it's id if the storeDataArray is empty
            try {
                let status = await AgendaJobManager.cancel({ _id: ObjectID(jobs[0].attrs._id) });
                if (status)
                    return getReturnObject(true, constants.SCHEDULE.CANCELED, null, null);
            } catch (error) {
                return getReturnObject(false, error.message, null, error);
            }
        }
    } else {
        return getReturnObject(true, constants.SCHEDULE.NO_MATCHING, null, null);
    }
}
async function createProxyWithSqlDump(UserInput) {
  try {
      if (fs.existsSync(UserInput.proxyJobData.zipPath)) {
        let destinationFolderPath = "scheduler-temp/fopc-zip-eti/";
          let destFolder = path.join(process.env.FOPC_WORK_DIR, destinationFolderPath);
        let fileName = UserInput.proxyJobData.zipPath.split("/").reverse()[0];
        let timeStamp = fileName.split("-").reverse()[0];
        let currentTimeStamp = moment().format("YYYYMMDDhhmmss");
        let destFileName = fileName;
        destFileName = destFileName.replace(timeStamp, currentTimeStamp+"-RERUN");
        destFileName = destFileName.replace(constants.PROCESS_JSON.REPLACE_STRING.DIR_PREFIX, "");
        destFileName = destFolder + destFileName;
        let statusFlag =  await util_rerun.copyETLFile(UserInput.proxyJobData.zipPath, destFileName);
        
        if(statusFlag == "success"){
          return getReturnObject(true, `${constants.DO_PROXY_MSG.JOB_CREATION_SUCCESS}`, null, null);
        } else{
          return getReturnObject(false, `${constants.DO_PROXY_MSG.FAILED_TO_CREATE_JOB} ${statusFlag.toString()}`, null, null);
        }
        
      } else {
        return getReturnObject(false, constants.DO_PROXY_MSG.FILE_MISSING, null, null);
      }
    } catch (err) {
      return getReturnObject(false, `${constants.DO_PROXY_MSG.FAILED_TO_CREATE_JOB} ${err.toString()}`, null, null);
    }
  }

/**
 * Function to return the status of each Agenda job
 * @param {Agenda} Agenda Agenda object
 * @param {string} jobName Job name
 */
async function getAgendaJobs(Agenda, jobName) {
    return new Promise((resolve, reject) => {
        const collection = Agenda._collection.collection || Agenda._collection;//all data
        collection.aggregate([
            { $match: jobName ? { "name": jobName } : {} },
            {
                $project: {
                    _id: 0,
                    job: "$$ROOT",
                    nextRunAt: { $ifNull: ["$nextRunAt", 0] },
                    lockedAt: { $ifNull: ["$lockedAt", 0] },
                    lastRunAt: { $ifNull: ["$lastRunAt", 0] },
                    lastFinishedAt: { $ifNull: ["$lastFinishedAt", 0] },
                    failedAt: { $ifNull: ["$failedAt", 0] },
                    repeatInterval: { $ifNull: ["$repeatInterval", 0] },
                    uploadStatus: { $ifNull: ["$uploadStatus", null] } 
                }
            },
            {
                $project: {
                    job: "$job",
                    _id: "$job._id",
                    name: "$job.name",
                    type: "$job.type",
                    priority: "$job.priority",
                    nextRunAt: "$job.nextRunAt",
                    lastModifiedBy: "$job.lastModifiedBy",
                    lockedAt: "$job.lockedAt",
                    lastRunAt: "$job.lastRunAt",
                    lastFinishedAt: "$job.lastFinishedAt",
                    uploadStatus: "$job.uploadStatus",
                    data: "$job.data",
                    failReason: "$job.failReason",
                    running: {
                        $and: [
                            "$lastRunAt",
                            { $gt: ["$lastRunAt", "$lastFinishedAt"] }
                        ]
                    },
                    scheduled: {
                        $and: [
                            "$nextRunAt",
                            { $gte: ["$nextRunAt", new Date()] }
                        ]
                    },
                    queued: {
                        $and: [
                            "$nextRunAt",
                            { $gte: [new Date(), "$nextRunAt"] },
                            { $gte: ["$nextRunAt", "$lastFinishedAt"] }
                        ]
                    },
                    completed: {
                        $and: [
                            "$lastFinishedAt",
                            { $gt: ["$lastFinishedAt", "$failedAt"] }
                        ]
                    },
                    failed: {
                        $and: [
                            "$lastFinishedAt",
                            "$failedAt",
                            { $eq: ["$lastFinishedAt", "$failedAt"] }
                        ]
                    },
                    repeating: {
                        $and: [
                            "$repeatInterval",
                            { $ne: ["$repeatInterval", null] }
                        ]
                    }
                }
            }
        ]).toArray(async (err, results) => {
            results.forEach(obj => {
                if (obj.job && obj.job.lastRunAt instanceof Date) {
                    obj.job.lastRunAt = obj.job.lastRunAt.getTime();
                }
                if (obj.job && obj.job.lastFinishedAt instanceof Date) {
                    obj.job.lastFinishedAt = obj.job.lastFinishedAt.getTime();
                }
                if (obj.job && obj.job.nextRunAt instanceof Date) {
                    obj.job.nextRunAt = obj.job.nextRunAt.getTime();
                }
                if (obj.job && obj.job.startTime instanceof Date) {
                    obj.job.startTime = obj.job.startTime.getTime();
                }
                if (obj.job && obj.job.endTime instanceof Date) {
                    obj.job.endTime = obj.job.endTime.getTime();
                }
                if (obj.job && obj.job.lockedAt instanceof Date) {
                    obj.job.lockedAt = obj.job.lockedAt.getTime();
                }
                if (obj.lastRunAt instanceof Date) {
                    obj.lastRunAt = obj.lastRunAt.getTime();
                }
                if (obj.lastFinishedAt instanceof Date) {
                    obj.lastFinishedAt = obj.lastFinishedAt.getTime();
                }
                if (obj.nextRunAt instanceof Date) {
                    obj.nextRunAt = obj.nextRunAt.getTime();
                }
                if (obj.startTime instanceof Date) {
                    obj.startTime = obj.startTime.getTime();
                }
                if (obj.endTime instanceof Date) {
                    obj.endTime = obj.endTime.getTime();
                }
                if (obj.lockedAt instanceof Date) {
                    obj.lockedAt = obj.lockedAt.getTime();
                }
              });
            let currRunningFile;
            for(let i=0;i<results.length;i++){
                if(results[i].running){
                  console.log("running",results[i].data.inputFile);
                  currRunningFile = results[i].data.inputFile;
                }
            }             

            if (err) {
                reject(err);
            }
            if (jobName === constants.FOPC.JOB_NAME) {

                let timeFrame = util.getTimeFrame();
                let ret = {
                    timeFrameZone: constants.FOPC.TIME_ZONE,
                    timeFrameStartTime: timeFrame.startTime.utc(),
                    timeFrameEndTime: timeFrame.endTime.utc(),
                    poolTime: constants.FOPC.POOL_ADD,
                    jobArray: results
                };
                resolve(ret);
            } else if (jobName === constants.PROCESS_JSON.JOB_NAME) {
                console.log("constants.FOPC_SCHEDULER_ETI_DIR$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",constants.FOPC_SCHEDULER_ETI_DIR);
                let queueUpdationStatus = await util.updateProcessorQueue(constants.FOPC_SCHEDULER_ETI_DIR,currRunningFile);
                console.log("Queue updation status============================",queueUpdationStatus);
                let  storesToProcess = [];
                let queueList = await agendaDbModel.fetchJobFromProcessorQueue("fopc_queue");
                let queueResponse =queueList.response;



                for (let i = 0; i < queueResponse.length; i++) {
                   let currentItem = queueResponse[i];
                   let storeId = currentItem.fileName.split("-")[1];
                   let filePath = `/home/<USER>/tmp/du-etl-dms-fopc-extractor-work/scheduler-temp/fopc-zip-eti/${currentItem.fileName}`;
                   if (fs.existsSync(filePath)) {
                   let sourceFilePath = filePath;
                        let fileName = currentItem.fileName;
                        let destinationFilePath = `/home/<USER>/tmp/du-etl-dms-fopc-extractor-work/requeue/${fileName}`;
                        fs.copyFile(sourceFilePath, destinationFilePath, (err) => {
                            if (err) {
                                console.error("Error copying file:", err.message);
                            } else {
                                console.log(`File copied successfully from ${sourceFilePath} to ${destinationFilePath}`);
                            }
                        });
                   let obj = {storeID:storeId,fileToProcess:filePath,priority:currentItem.priority};
                   storesToProcess.push(obj);
                   }else {
                    console.log(`File not found: ${filePath}`);  
                  }
            
                 }
                 storesToProcess = storesToProcess.reverse();
                results.forEach(() => {
                    storesToProcess = storesToProcess.filter(store => {
                           const storeFileName = store.fileToProcess.split("/").pop();
                           console.log(`Store file anme: ${storeFileName} currRunningFile = ${currRunningFile}`);
                           return storeFileName !== currRunningFile;
                      });
                        
                });
                let ret = {
                    processJSONJobsQueue: storesToProcess,
                    processJSONJobs: results
                };
                resolve(ret);
            } else {
                resolve(results);
            }

        });
    });
}

/**
 * Function to create object for reschedule
 * @param {object} job Agenda job object
 * @param {*} storeDataArray Optional Array of store objects
 */
function createScheduleObject(job, storeDataArray) {
    let retObj = {};
    let jobData = {};
    jobData.groupName = job.attrs.data.groupName;
    jobData.storeDataArray = storeDataArray ? storeDataArray : job.attrs.data.storeDataArray;
    retObj.jobData = jobData;
    retObj.jobSchedule = util.getScheduleForTomorrow();
    return retObj;
}

module.exports = {
    getAllFopcExtractJobs,
    getAllFopcProcessJSONJobs,
    scheduleFopcExtractJob,
    cancelFopcExtractJobByStore,
    createScheduleObject,
    createProxyWithSqlDump
};
