"use strict";

const constants = require("../constants");
const util = require("../util");
const constantsCommon = require("../../../common/constants");
const fopcJobManager = require("../fopcJobManager");
const { spawn } = require("child_process");
const moment = require("moment-timezone");
const fs = require("fs");
const segment = require("../../SEGMENT/FOPC/segmentManager");
const mailSender = require("../../../routes/mailSender");
const appConstants = require("../../../common/constants");
const manageScheduleField = require("../../../common/util");


/**
 * Function to find the unique stores in an array of stores
 */
Array.prototype.unique = function () {
    let a = this.concat();
    for (let i = 0; i < a.length; ++i) {
        for (let j = i + 1; j < a.length; ++j) {
            if (a[i].dealerId === a[j].dealerId)
                a.splice(j--, 1);
        }
    }
    return a;
};

/**
 * Function to perform fopc-Extract
 */
module.exports = function fopcExtractJOB(agenda) {
    console.log(
        `fopc-Extract job started: JobName: ${constants.FOPC.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.FOPC.CONCURRENCY}`
    );
    let extractionId;
    segment.saveSegment(`fopc-Extract job started: JobName: ${constants.FOPC.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.FOPC.CONCURRENCY}`);
    agenda.define(constants.FOPC.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: 1,lockLimit: 1,lockLifetime: 30 * 60 * 1000 },
        async (job, done) => {
            extractionId = job.attrs._id;
            console.log("Extraction Object:",JSON.stringify(job));
            const att = job.attrs.data;
            const storeDataArray = att.storeDataArray.reverse();
            let i = 0;
            let storeCode;
            let dealerId;
            let solve360Update, buildProxies;
            let warningObj = {};
            let userName;
            let processFileName;
            async function extract(att, job) {
                let errorWarnningMessage = "";
                dealerId = att.dealerId;
                storeCode = att.mageStoreCode;
                solve360Update = att.solve360Update;
                buildProxies = att.buildProxies;
                userName = att.userName;
                console.log("solve360Update:", solve360Update);
                console.log("buildProxies:", buildProxies);
                console.log("extractionId:", extractionId);
                segment.saveSegment(`Extraction Job Started1: ${JSON.stringify(att)}`);
                segment.saveSegment(`Extraction Job Started2: ${JSON.stringify(att)}`, storeCode);
                if (att.dealerId && att.startDate && att.endDate) {
                    if (!fs.existsSync(constants.FOPC_DEADLETTER_DIR_PREFIX + "-extracted")) {
                        fs.mkdirSync(constants.FOPC_DEADLETTER_DIR_PREFIX + "-extracted");
                    }
                    let options = [
                        constants.FOPC.PULL_OP,
                        constants.FOPC.OPT_DEALER_ID, att.dealerId,
                        constants.FOPC.OPT_START_DATE, att.startDate,
                        constants.FOPC.OPT_END_DATE, att.endDate,
                        constants.FOPC.OPT_DEADLETTER_DIR_PREFIX,
                        constants.FOPC_DEADLETTER_DIR_PREFIX + "-extracted",
                        constants.FOPC.OPT_MAGE_GROUP_CODE, att.mageGroupCode,
                        constants.FOPC.OPT_MAGE_STORE_CODE, att.mageStoreCode,
                        "--stateCode", att.stateCode,
                        "--extractionID",extractionId
                    ];
                    // The use of --stateCode here is intentional; redirecting via a constant named arguments
                    // to external functions is more annoying than helpful; mainly due to verbosity
                    // repeating "constants" just is and having to namespace the variable ends up being
                    // redundant since the command invocation itself provides the necessary namespacing; once
                    options.push(
                    constants.FOPC.OPT_ZIP_PATH,
                    att.zipPath ?? constants.FOPC_SCHEDULER_ETI_DIR,
                    constants.FOPC.OPT_ZAP_AFTER_ZIP
                    );

                    // Map jobType to corresponding options arrays
                    const jobTypeOptions = {
                    [constants.FOPC.JOB_TYPE_INITIAL]: [
                        constants.FOPC.OPT_CLOSED,
                        att.closedROOption ?? constants.FOPC.OPT_CLOSED_CRITERIA_ALL,
                    ],
                    [constants.FOPC.JOB_TYPE_ON_DEMAND]: [
                        constants.FOPC.OPT_CLOSED,
                        constants.FOPC.OPT_CLOSED_CRITERIA_CURRENT,
                    ],
                    };
                    // Push jobType-specific options if any
                    const opts = jobTypeOptions[att.jobType];
                    if (opts) {
                    options.push(...opts);
                    }

                    options.push(constants.FOPC.OPT_BUNDLE);
                    options.push(att.jobType);
                    segment.saveSegment(`Extraction Job Started3: ${JSON.stringify(att)}`);
                    segment.saveSegment(`Extraction Job Started4: ${JSON.stringify(att)}`, storeCode);
                    segment.saveSegment(`Extraction Job Started5:  options : ${JSON.stringify(options)}`, options);
                    //Mock Server
                    let isMockServer = constants.FOPC.ENV_MOCKSERVER; 
                    if(isMockServer == "true"){
                        segment.saveSegment("isMockServer is true");
                        let groupCode = att.mageGroupCode;
                        let sourceFolder = constants.FOPC.MOCKSERVER_SOURCE_FOLDER_PATH +"fopc/"; 
                        let destinationFolder = constants.FOPC.MOCKSERVER_DESTINATION_FOLDER_PATH_fopc;
                        //SullivansNWHills-NWHILL_GM-CT-INITIAL-786-20231120102853.zip
                        const mageGroupCodeDIR =  groupCode.replace(/ +/g, "");
                        const mageStorecodeDIR =  storeCode.replace(/ +/g, "");
                        const stateCodeDIR = att.stateCode;
                        const jobTypeDIR = att.jobType ;
                        const dealerIdDir = dealerId;
                        const zipFileName = `${mageGroupCodeDIR}-${mageStorecodeDIR}-${stateCodeDIR}-${jobTypeDIR.toUpperCase()}-${dealerIdDir}-`;
                        console.log("-------zipFileName-------",zipFileName);                        
                        fs.readdir(sourceFolder, function (err, files) {
                            if (err) {
                                console.log("FOPC : Unable to scan directory");
                                segment.saveSegment("FOPC : Unable to scan directory",err);
                            } 
                            const matchedResults = [];
                            let searchResult;
                            files.forEach(function (file) {
                                if(file.includes(zipFileName)) {
                                    matchedResults.push(file);
                                }
                            });
                            console.log("FOPC : files",matchedResults);
                            if(matchedResults.length > 0) {
                                console.log("FOPC : matchedResults", matchedResults);
                                // Custom sort logic: replace this with your actual sorting criteria
                                matchedResults.sort((a, b) => {
                                    // example: descending by string length
                                    return b.length - a.length;
                                });
                                searchResult = matchedResults[0];
                                att.startTime = new Date(moment().utc());
                                fs.copyFile(sourceFolder + searchResult, destinationFolder + "/" + searchResult, (err) => {
                                    if (err) {
                                    console.log("FOPC : Unable to copy file");
                                    segment.saveSegment("FOPC : Unable to copy file", err);
                                    } else {
                                    att.endTime = new Date(moment().utc());
                                    att.uniqueId = util.generateUniqueId();
                                    att.status = true;
                                    att.mockServer = true;
                                    att.message = "Success";
                                    let oldStoreArray = job.attrs.data.storeDataArray;
                                    let newStoreArray = [att];
                                    oldStoreArray.forEach((data, idx) => {
                                        if (data.enterpriseCode === newStoreArray[0].enterpriseCode) {
                                        oldStoreArray[idx] = newStoreArray[0]; // correctly update the array element
                                        }
                                    });
                                    job.attrs.data.storeDataArray = oldStoreArray;
                                    job.save();
                                    done();
                                    }
                                });
                            }
                            else{
                                console.log("FOPC : No matching files found");
                                segment.saveSegment("FOPC : No matching files found");
                            let warningObj={};
                            segment.saveSegment("FOPC : Test job failed");
                            console.log("FOPC : Test job failed");
                            att.startTime = new Date(moment().utc());
                            att.endTime = new Date(moment().utc());
                            att.uniqueId = util.generateUniqueId();
                            att.status = false;
                            att.mockServer = true;
                            att.message = "Failed";
                            job.fail(new Error("FOPC :Test job failed"));
                            job.save();
                            
                            let failureDirectory = process.cwd() + "/logs/FOPC/failure/";
                            let failurelogFile = failureDirectory + storeCode + ".log";
                            let mailTemplateReplacementValues = {
                                dmsType: constants.JOB_TYPE,
                                processTypes: constants.PROCESS_JSON.JOB_NAME,
                                subject: `Test Extraction Job for ${groupCode} - ${storeCode} Failed`,
                                warningObj: warningObj,
                                thirdPartyUsername: att.dealerId,
                                storeCode: storeCode,
                                groupCode: groupCode
                            };
                            let mailBody = {
                                fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                                toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                                ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                                attachedfailurelogFile:failurelogFile
                            };
                            let displayMessage = `Test Failed ${constants.JOB_TYPE} ${constants.FOPC.JOB_NAME} job for group ${groupCode} and store ${storeCode}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = "Failed";
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure("Extraction status: Extraction Failed", storeCode);
                            // Send notification after  cdk extraction job completed
                            mailSender.sendMail(mailBody, constants.FOPC.JOB_NAME);
                        }
                    });
                    } else {
                        segment.saveSegment("Extraction Job Started11");
                        segment.saveSegment(`Extraction Job Started6:options ${JSON.stringify(options)}`);    
                    const child = spawn(constants.FOPC.EXTRACT_CMD, options);
                    let startTime = new Date(moment().utc());
                    att.startTime = startTime;
                    let oldStoreArray = job.attrs.data.storeDataArray;
                    let newStoreArray = [att];
                    oldStoreArray.map(data => {
                        if (data.dealerId === newStoreArray[0].dealerId) {
                            data = newStoreArray;
                        }
                    });
                    let _storeArray = oldStoreArray;
                    job.attrs.data.storeDataArray = _storeArray;
                    segment.saveSegment(`Extraction Job Data: ${JSON.stringify(_storeArray)}`);
                        segment.saveSegment(`Extraction Job Data: ${JSON.stringify(_storeArray)}`, storeCode);
                    att.uniqueId =  util.generateUniqueId();
                    await job.save();
                    process.stdin.pipe(child.stdin);
                    let compareString = "";
                    let status = true;
                    let message = "n/a";
                    let unsubscribeDms= false;

                    child.stdout.on("data", async (data) => {
                        segment.saveSegment(`on data stdout: ${data}`);
                        await job.touch();
                        compareString = data.toString("utf8");
                        if (compareString.search("error:") != -1) {
                            message = data.toString("utf8");
                        }
                        console.log(`stdout: ${data}`);
                        data = data.toString("utf8");  
                        if (data.includes("Route /repairorder is not added or disabled in integrations") || data.includes("No external dealer associated with this alias")  || data.includes("Request failed with status code 403")) {
                            unsubscribeDms = true;
                        } 
                        if (data.includes("Output zip file successfully generated @path")) {
                            console.log("file generated#################################################", data);
                            const pathMatch = data.match(/@path: (\/.*?\.zip)/);
                            console.log("pathMatch", pathMatch);
                            segment.saveSegment(`pathMatch: ${pathMatch}`);
                            if (pathMatch && pathMatch[1]) {
                                const filePath = pathMatch[1];
                                const match = filePath.match(/[^/]+\.zip$/);
                                if (match) {
                                    processFileName = match[0];
                                    console.log("Extracted filename:", processFileName);
                                    if (processFileName) {
                                        await manageScheduleField.processCompanyData(job, userName, processFileName, filePath, constants.JOB_TYPE);                                        
                                        att.processFileName = processFileName.trim();
                                        await job.save();
                                    } else {
                                        processFileName = null;
                                    }
                                } else {
                                    console.log("Failed to extract filename from path:", filePath);
                                }
                            } else {
                                console.log("Failed to find the file path in the log data");
                            }
                        }
                        else {
                            console.log("failed to generate file");
                        }
                        segment.saveSegment(`stdout: ${data}`);
                        segment.saveSegmentFailure(`stdout: ${data}`, storeCode);
                    });


                    child.stderr.on("data", async (data) => {
                        await job.touch();
                        compareString = data.toString("utf8");
                        if (compareString.search("error:") != -1) {
                            message = data.toString("utf8");
                        }
                        console.log(`stderr: ${data}`);
                        segment.saveSegment(`stderr: ${data}`);
                        segment.saveSegmentFailure(`stderr: ${data}`, storeCode);
                    });

                    child.on("close", async (code) => {

                        if (code == constants.STATUS_CODE.SUCCESS) {
                            status = true;
                            message = "Success";
                        } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                            status = false;
                            message = "Extraction failed, general death";
                        } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                            status = false;
                            message = "Extraction failed, moved to dead-letter path";
                        }
                        segment.saveSegment(`Job close: ${message}`);
                        segment.saveSegmentFailure(`Job close: ${message}`, storeCode);

                        let failureDirectory = process.cwd() + "/logs/FOPC/failure/";
                        let failurelogFile = failureDirectory + storeCode + ".log";
                        let displayMessage = "";
                        let groupName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageGroupCode : "";
                        let storeName = (job.attrs.data.storeDataArray.length) ? job.attrs.data.storeDataArray[0].mageStoreCode : "";
                        
                        let mailTemplateReplacementValues = {
                            dmsType: constants.JOB_TYPE,
                            processTypes: constants.PROCESS_JSON.JOB_NAME,
                            subject: `Extraction Job for ${groupName} - ${storeName} Completed`,
                            warningObj: warningObj
                        };
                        let mailBody = {
                            fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                            toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                            ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                            attachedfailurelogFile:failurelogFile,
                            thirdPartyUsername: dealerId,
                            storeCode: storeName,
                            groupCode: groupName
                        };
                       
                        if (status) {
                            // Send notification
                            displayMessage = `Completed ${constants.JOB_TYPE} ${constants.FOPC.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.status = "Success";
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegment("Extraction status: Extraction completed", storeCode);
                            // Send notification after  fopc extraction job completed
                            mailSender.sendMail(mailBody, constants.FOPC.JOB_NAME);
                        } else {
                            mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                            mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                            // Send notification
                            displayMessage = `Failed ${constants.JOB_TYPE} ${constants.FOPC.JOB_EXTRACT_NAME} job for group ${groupName} and store ${storeName}`;
                            mailTemplateReplacementValues.message = displayMessage;
                            mailTemplateReplacementValues.subject = `Extraction Job for ${groupName} - ${storeName} Failed`;
                            mailTemplateReplacementValues.warnningMessage = errorWarnningMessage;
                            mailTemplateReplacementValues.status = "Failed";
                            mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                            if(unsubscribeDms){
                            const UserInput = {                                                   
                                inIsInSales :true,
                                inSalesComment : appConstants.SALES_TAG_COMMENT,                               
                                inUpdatedBy :userName
                              };
                              segment.saveSegment(`fopc Extraction - sales tag creation: ${JSON.stringify(UserInput)}`);
                            try {
                                const SendSalesTagDetails = await manageScheduleField.sendNotificationCall(UserInput,constants.FOPC.JOB_EXTRACT_NAME);
                                segment.saveSegment(`fopc Extraction - sales tag creation: ${JSON.stringify(SendSalesTagDetails)}`);
                              } catch (salesError) {                                
                                segment.saveSegment(`fopc Extraction - sales tag creation Error: ${salesError}`);
                              }
                            }
                            segment.saveSegment(`Send notification: ${displayMessage}`);
                            segment.saveSegmentFailure("Extraction status: Extraction failed", storeCode);

                            // Portal update for extraction failed
                            let todayDate;
                            let attPayload = {};
                            try{
                            todayDate = new Date().toISOString().slice(0, 10);
                            attPayload = att;                            
                            attPayload.in_is_update_retrieve_ro =Object.prototype.hasOwnProperty.call(attPayload, "solve360Update") ?  attPayload.solve360Update : "";

                            attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;
                            attPayload.in_retrive_ro_request_on = todayDate;                            
                            } catch(err){
                            console.log(JSON.stringify(err));
                            segment.saveSegment(`FOPC : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                            }
                            await segment.sleep(2000);
                            mailSender.sendMail(mailBody, constants.FOPC.JOB_NAME);
                        }
                        att.endTime = new Date(moment().utc());
                        att.uniqueId = att.uniqueId || util.generateUniqueId();
                        att.status = status;
                        att.message = message;
                        let oldStoreArray = job.attrs.data.storeDataArray;
                        let newStoreArray = [att];
                        oldStoreArray.map(data => {
                            if (data.dealerId === newStoreArray[0].dealerId) {
                                data = newStoreArray;
                            }
                        });
                        let _storeArray = oldStoreArray;
                        job.attrs.data.storeDataArray = _storeArray;
                        if(processFileName){
                            att.processFileName = processFileName.trim();
                        }else{
                            processFileName = null;
                        }
                        await job.save();
                        console.log(`FOPC : Extraction process for store ${att.dealerId} exited code ${code}`);
                        segment.saveSegment(`FOPC : Extraction process for store ${att.dealerId} exited code ${code}`);
                        i++;
                        if (i < storeDataArray.length) {
                            // Check time frame
                            if (util.checkExtractTimeFrame()) {
                                segment.saveSegment(`FOPC : Check time frame and start extraction ${JSON.stringify(storeDataArray[i])}`);
                                await extract(storeDataArray[i], job);
                            } else {
                                const newDataArray = storeDataArray;
                                try {
                                    fopcJobManager.scheduleFopcExtractJob(fopcJobManager.createScheduleObject(job, newDataArray.slice(i)), true);
                                    job.attrs.data.storeDataArray = storeDataArray.slice(0, i);
                                    job.fail(new Error("FOPC : Time exceeded, remaining stores scheduled to next day."));
                                    segment.saveSegment("FOPC : Time exceeded, remaining stores scheduled to next day.");
                                    await job.save();
                                    //done();
                                } catch (error) {
                                    console.error(error);
                                    segment.saveSegment(`Error : ${error.toString()}`);
                                }
                            }
                        } else {
                            done();
                        }
                    });
                }
                } else {
                    console.error("FOPC : Store data Extraction attributes not defined");
                    segment.saveSegment("FOPC : Store data Extraction attributes not defined");
                }
            }
            if (util.checkExtractTimeFrame()) {
                segment.saveSegment(`FOPC : Check time frame and start extraction ${JSON.stringify(storeDataArray[0])}`);
                await extract(storeDataArray[0], job);
            } else { // Auto schedule full Group wise schedule for tomorrow
                segment.saveSegment("FOPC : Auto schedule full Group wise schedule for tomorrow");
                fopcJobManager.scheduleFopcExtractJob(fopcJobManager.createScheduleObject(job), true);
                job.remove();
            }
        });

    agenda.on("start", job => {
        console.log(`FOPC : Job ${job.attrs.name}_${job.attrs._id} starting`);
    });

    agenda.on("complete", job => {
        console.log(`FOPC : Job ${job.attrs.name}_${job.attrs._id} finished`);
    });

    agenda.on("fail", (err, job) => {
        console.log(`FOPC : Job ${job.attrs.name}_${job.attrs._id} failed with error: ${err.message} `);
    });
    return agenda;
};
