"use strict";

const constants = require("../constants");
const util = require("../util");
const constantsCommon = require("../../../common/constants");
const { spawn } = require("child_process");
const path = require("path");
const fs = require("fs");
const moment = require("moment-timezone");
const segment = require("../../SEGMENT/FOPC/segmentManager");
const sharePoint = require("../../../routes/sharePoint");
const mailSender = require("../../../routes/mailSender");
const appConstants = require("../../../common/constants");
const stripAnsi = require("strip-ansi");
const SetProcessJobStatus = require("../../../model/setProcessJobStatus");
const appUtil = require("../../../common/util");
const extractionError = require("../../../common/extractionError");

const Agenda = require("../../agenda");

const csv=require("csvtojson");
/**
 * Function to perform processing of JSON file downloaded through fopc-Extract job
 */
module.exports = async function ProcessXmlJOB(agenda) {

    const distributeFile = async function (fileName, rerunFlag , updateSolve360Data, warningObj, jobId) {
        let stdErrorArray;
        let distDir = path.join(process.env.FOPC_DIST_DIR, fileName);
        let filePath = path.join(process.env.FOPC_BUNDLE_DIR, fileName);
        const distributeFile = spawn("bash",
            [
                "send-bundle-live-hpdog", filePath,rerunFlag
            ], {
                cwd: constants.PROCESS_JSON.FOPC_DISTRIBUTE_CMD_PATH,
                env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
            }).on("error", function (err) {
                console.log("error :", err);
                segment.saveSegment(`error: ${err}`);
            });
        console.log("FOPC : Start processing of distribution");
        segment.saveSegment("FOPC :  processing distribution");
        process.stdin.pipe(distributeFile.stdin);
        distributeFile.stdout.on("data", async (data) => {
            console.log(`stdout: ${data}`);
            segment.saveSegment(`stdout: ${data}`);
        });

        distributeFile.stderr.on("data", async (data) => {
            console.log(`stderr: ${data}`);
            stdErrorArray += data.toString() + " ";
            segment.saveSegment(`stderr: ${data}`);
        });

        distributeFile.on("close", async (code) => {
            let message = "n/a";
            let status = false;
            if (code == constants.STATUS_CODE.SUCCESS) {
                status = true;
                message = "Success";
            } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                message = "Distribution failed, general death";
            } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                message = "Distribution failed";
            }
            segment.saveSegment(`close: ${message}`);
            if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_EXIST_CHECK)) {
                status = false;
                message = "Distribution failed. Zip File Must Exist";
                segment.saveSegment(message);
            }
            /**
              * Upload files to SharePoint
              */
            if (status) {
                sharePoint.initSharePoint(distDir, constants.JOB_TYPE, rerunFlag , updateSolve360Data, warningObj, 0, jobId);//Upload dist directory zip file to sharepoint
            }
            
            await doNextProcess();
        });
    };

    const doNextProcess = async function () {
        const extractZip = await util.findOldestZipFile(constants.FOPC_SCHEDULER_ETI_DIR);
        if (extractZip) {
            console.log(`FOPC : Found one Store extraction > ${extractZip} to process now`);
            segment.saveSegment(`FOPC : Found one Store extraction > ${extractZip} to process now`);
            try {
                let createdAt = extractZip.slice(0, extractZip.length - 4).split("-").reverse()[0];
                await agenda.now(constants.PROCESS_JSON.JOB_NAME, {
                    inputFile: extractZip,
                    createdAt: createdAt,
                    operation: "json-processing"
                });
                console.log(`FOPC : Process JSON schedule started with file > ${extractZip}`);
                segment.saveSegment(`FOPC : Process JSON schedule started with file > ${extractZip}`);
            } catch (error) {
                console.error(error);
            }

        } else {
            console.log(`FOPC : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            //segment.saveSegment(`FOPC : No Store's zip file to process now, will check ${constants.PROCESS_JSON.TIME_GAP}`);
            try {
                await agenda.schedule(`${constants.PROCESS_JSON.TIME_GAP}`, constants.PROCESS_JSON.JOB_NAME, { operation: "recheck" });
                console.log(`FOPC : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
                //segment.saveSegment(`FOPC : Process JSON schedule will run ${constants.PROCESS_JSON.TIME_GAP}`);
            } catch (error) {
                console.error(error);
            }
        }  
                  
                  
           };

    console.log(
        `FOPC : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`
    );
    segment.saveSegment(`FOPC : Process JSON job started: JobName: ${constants.PROCESS_JSON.JOB_NAME}, priority:${constants.JOB_PRIORITY.HIGHEST}, concurrency:${constants.PROCESS_JSON.CONCURRENCY}`);
    agenda.define(constants.PROCESS_JSON.JOB_NAME,
        { priority: constants.JOB_PRIORITY.HIGHEST, concurrency: constants.PROCESS_JSON.CONCURRENCY,lockLifetime: 3 * 60 * 60 * 1000 },
        async (job, done) => {
          
            const att = job.attrs.data;
            let extractZip = null;
            let stdErrorArray = [];
            let stdOutArray = [];
             let processorStatus;
            let inpFile = att.inputFile ? path.join(constants.FOPC_SCHEDULER_ETI_DIR, att.inputFile) : "";
         
            if (att.inputFile && fs.existsSync(inpFile)) {
                if (!fs.existsSync(constants.FOPC_DEADLETTER_DIR_PREFIX + "-processed")) {
                    fs.mkdirSync(constants.FOPC_DEADLETTER_DIR_PREFIX + "-processed");
                }
                extractZip = att.inputFile;
                let basename = path.basename(extractZip);
                let doProxyFromPgDump = false;
                job.attrs.data.storeID = basename.split("-").reverse()[1];
                let storeName = job.attrs.data.storeID;
                let storeCode = storeName;
            

                let dealerId = basename.split("-").reverse()[1];
                let mageGroupCode = basename.split("-")[0];
                let mageStoreCode =  basename.split("-")[1];

                if (basename.includes(constants.PROCESS_JSON.REPLACE_STRING.DO_PROXY_FROM)) {
                    doProxyFromPgDump = true;
                    dealerId= basename.split("-").reverse()[2];
                } else {
                    dealerId= basename.split("-").reverse()[1];
                    doProxyFromPgDump = false;
                }
                job.attrs.data.storeID = !doProxyFromPgDump ? basename.split("-").reverse()[1] : basename.split("-").reverse()[2];

                console.log("Groupname:", mageGroupCode);
                segment.saveSegment(`Groupname : ${mageGroupCode}`);

                console.log("DealerId:", dealerId);
                segment.saveSegment(`DealerId : ${dealerId}`);

                console.log("storeName:",mageStoreCode);
                segment.saveSegment(`storeName : ${mageStoreCode}`);

                let jobsTmp = await Agenda.jobs( {
                    $and: [
                        { "data.storeDataArray.dealerId": dealerId },
                        { "name": constants.FOPC.JOB_NAME } ,
                        {"data.storeDataArray.mageStoreCode":mageStoreCode}

                    ]
                });                
               
                let solve360Update = "";
                let userName = "";
                let updateSolve360Data;
                let buildProxies;
                let extractionId;                 
                let agendaObject;
                let extractedFileTimeStamp;
                let extractedFileCreationDate;
                let extractedObjectIndex;
                let dealerAddress;
                let mageManufacturer;
                let isPorscheStore;
                let uniqueId;
                let companyIds;
                let companyObj;
                let testData;
                let brands;
                let parentName;
                let modifiedFileName="";
                let stateCode;
              

                try{
                    extractedFileTimeStamp = basename.split("-").reverse()[0].replace(".zip", "");
                    segment.saveSegment(`extractedFileTimeStamp : ${extractedFileTimeStamp}`);
                    extractedFileCreationDate =  moment(extractedFileTimeStamp, "YYYYMMDDhhmmss").format("YYYY-MM-DD");
                    segment.saveSegment(`extractedFileCreationDate : ${extractedFileCreationDate}`);
                } catch(err){
                    console.log(err);
                    segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                }
                
                segment.saveSegment(`jobsTmp : ${JSON.stringify(jobsTmp)}`);
                segment.saveSegment(`jobsTmp[jobsTmp.length-1] : ${JSON.stringify(jobsTmp[jobsTmp.length-1])}`);
                segment.saveSegment(`Dealer ID : ${dealerId}`);

                if(jobsTmp[jobsTmp.length-1]){
                    if(Object.prototype.hasOwnProperty.call(jobsTmp[jobsTmp.length-1], "attrs")){
                        extractionId = jobsTmp[jobsTmp.length-1].attrs._id;
                        try{
                            segment.saveSegment(`jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : ${JSON.stringify(jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray)}`);
                            agendaObject = jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray;
                            agendaObject = agendaObject.filter(function (el) {
                                return el.dealerId == dealerId && el.mageStoreCode == mageStoreCode; 
                            });
                            segment.saveSegment(`agendaObject : ${JSON.stringify(agendaObject)}`);
                            extractedObjectIndex = 0;
                            if(agendaObject.length > 0){ 
                                agendaObject =  agendaObject.sort((a,b) => b.endTime > a.endTime);
                                extractedObjectIndex = agendaObject.findIndex(
                                    obj => moment(obj.endTime, "YYYY-MM-DDTHH:mm:ss.SSS[Z]").format("YYYY-MM-DD") == extractedFileCreationDate
                                );
                            }
                            if(extractedObjectIndex < 0){
                                extractedObjectIndex = 0;
                            }
                            segment.saveSegment(`Sorted agenda object : ${JSON.stringify(agendaObject)}`);
                            segment.saveSegment(`extractedObjectIndex : ${extractedObjectIndex}`);
                            segment.saveSegment(`Extracted agenda object : ${JSON.stringify(agendaObject[extractedObjectIndex])}`);                            

                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "solve360Update")){
                                solve360Update = agendaObject[extractedObjectIndex].solve360Update;
                            }
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "buildProxies")){
                                buildProxies = agendaObject[extractedObjectIndex].buildProxies;
                            }                            
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "userName")){
                                userName = agendaObject[extractedObjectIndex].userName;
                            }
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "dealerAddress")){
                                dealerAddress = agendaObject[extractedObjectIndex].dealerAddress;
                                dealerAddress = dealerAddress?dealerAddress.replace(/\n/g, "~"):"";
                            }
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "mageManufacturer")){
                                mageManufacturer = agendaObject[extractedObjectIndex].mageManufacturer;
                            }                            
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "companyIds")){
                                companyIds = agendaObject[extractedObjectIndex].companyIds;
                                companyIds =  companyIds.replace(new RegExp("\\*", "g"), ",");
                            }
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "uniqueId")){
                                uniqueId = agendaObject[extractedObjectIndex].uniqueId;
                                uniqueId+="-"+Date.now();                           
                            }
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "companyObj")){
                                companyObj = JSON.parse(agendaObject[extractedObjectIndex].companyObj);
                            }
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "testData")){
                                testData = agendaObject[extractedObjectIndex].testData;
                            }
                            if (Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "parentName")) {
                                parentName = agendaObject[extractedObjectIndex].parentName;
                                parentName = parentName.replace(/\s*\[.*?\]/, "").replace(/\s+/g, "").trim();
                                console.log("Cleaned parentName:", parentName);
                            }
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "stateCode")){
                                    stateCode =agendaObject[extractedObjectIndex].stateCode;
                            }                              
                            if(Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "dealerId")){
                                    dealerId =agendaObject[extractedObjectIndex].dealerId;
                            }
                        } catch(err){
                            console.log(err);
                            segment.saveSegment(`Error : ${JSON.stringify(err)}`);
                        }
                    }
                }

                modifiedFileName = `${constants.PROCESS_JSON.OUTPUT_PREFIX_VAL}${parentName}-${stateCode}-${dealerId}-${appUtil.getTimestampForAuditFile()}.zip`;
                console.log("userName:", userName);
                segment.saveSegment(`userName : ${userName}`);

                console.log("solve360Update:", solve360Update);
                segment.saveSegment(`solve360Update : ${solve360Update}`);

                console.log("buildProxies:", buildProxies);
                segment.saveSegment(`buildProxies : ${buildProxies}`);

                console.log("extractionId:", extractionId);
                segment.saveSegment(`extractionId : ${extractionId}`);

                console.log("dealerAddress:", dealerAddress);
                segment.saveSegment(`dealerAddress : ${dealerAddress}`);
                console.log("uniqueId:", uniqueId);
                segment.saveSegment(`uniqueId : ${uniqueId}`);

                console.log("testData:", testData);
                segment.saveSegment(`testData : ${testData}`);

                console.log("companyIds:", companyIds);
                segment.saveSegment(`companyIds : ${companyIds}`);

                console.log("parentName:", parentName);
                segment.saveSegment(`parentName : ${parentName}`);
  
                console.log("modifiedFileName:", modifiedFileName);
                segment.saveSegment(`modifiedFileName : ${modifiedFileName}`);
                if (Object.prototype.hasOwnProperty.call(agendaObject[extractedObjectIndex], "brands")) {
                    brands = agendaObject[extractedObjectIndex].brands.split("*");
                }
                if (brands.length > 1) {
                    const hasPorche = brands.some(item => item.toLowerCase() === "porche");
                    if (hasPorche && brands.length > 1) {
                        mageManufacturer = brands.find(item => item.toLowerCase() !== "porche");
                        isPorscheStore = true;
                    }
                } else {
                    if (mageManufacturer == constants.PROCESS_JSON.PORSCHE_STORE_LABEL) {
                        isPorscheStore = true;
                    } else {
                        isPorscheStore = false;
                    }
                }
                updateSolve360Data = {userName:userName, solve360Update:solve360Update, thirdPartyUsername:dealerId, storeCode: mageStoreCode, dmsType: constants.JOB_TYPE, groupCode:mageGroupCode,uniqueId:uniqueId,testData:testData,companyObj:companyObj,processFileName:modifiedFileName};
                console.log(updateSolve360Data);
                segment.saveSegment(`updateSolve360Data : ${updateSolve360Data}`);               
                let buildProxiesDecider;
                buildProxiesDecider = constants.PROCESS_JSON.OPT_NO_BUILD_PROXY_RO;                
                await job.save();
                let spawnInputArray;
           if (doProxyFromPgDump) {
                   spawnInputArray = [
                        constants.PROCESS_JSON.PROCESS_CMD,
                        constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.FOPC_BUNDLE_DIR,
                        constants.PROCESS_JSON.OPT_BUILD_PROXY_USING, path.join(constants.FOPC_SCHEDULER_ETI_DIR, extractZip),
                        constants.PROCESS_JSON.OPT_ZAP_INPUT,
                        constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX, constants.FOPC_DEADLETTER_DIR_PREFIX + "-processed",
                        constants.PROCESS_JSON.OUTPUT_PREFIX, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,
                        constants.PROCESS_JSON.DEALER_ADDRESS,dealerAddress,
                        "--uuid",uniqueId,
                        "--company_ids",companyIds                       
                    
                    ];
                }else{
                     spawnInputArray = [
                        constants.PROCESS_JSON.PROCESS_CMD,
                                constants.PROCESS_JSON.OPT_BUNDLE_DIR, constants.PROCESS_JSON.FOPC_BUNDLE_DIR,
                                constants.PROCESS_JSON.OPT_INPUT_ZIP, path.join(constants.FOPC_SCHEDULER_ETI_DIR, extractZip),
                                constants.PROCESS_JSON.OPT_ZAP_INPUT,
                                constants.PROCESS_JSON.OPT_PERFORM_ZIP,
                                buildProxiesDecider,
                                isPorscheStore ? constants.PROCESS_JSON.OPT_PORSCHE_STORE : "",
                                constants.PROCESS_JSON.OPT_DEADLETTER_DIR_PREFIX,
                                constants.FOPC_DEADLETTER_DIR_PREFIX + "-processed",
                                constants.PROCESS_JSON.OUTPUT_PREFIX,
                                constants.PROCESS_JSON.OUTPUT_PREFIX_VAL,                                
                                constants.PROCESS_JSON.DEALER_ADDRESS,
                                dealerAddress,
                                "--uuid",uniqueId,
                                "--performed-by",userName,
                                "--exception-report",true,
                                "--company_ids",companyIds,
                                "--output-file",modifiedFileName  
                 ]; 
                }

                const processJson = spawn("bash",
                spawnInputArray, {
                            cwd: constants.PROCESS_JSON.PROCESS_CMD_PATH,
                            env: Object.assign({}, process.env, { PATH: process.env.PATH + ":/usr/local/bin" })
                        }).on("error", function (err) {
                            console.log("error ::", err);
                            segment.saveSegment(`error: ${err}`);
                        });
                console.log(`FOPC : Start processing of extraction > ${basename}`);
                segment.saveSegment(`FOPC : Start processing of extraction > ${basename}`);
                segment.saveSegmentFailure(`Start processing of extraction > ${basename}`, storeCode);
                process.stdin.pipe(processJson.stdin);
                processJson.stdout.on("data", async (data) => {
                    console.log(`stdout: ${data}`);
                    stdOutArray.push(stripAnsi(data.toString("utf8").replace(/\n$/, "")));
                    await job.touch();
                    data = data.toString("utf8");
                    if (data.includes("Processor status")) {
                        segment.saveSegment("Processor statu for UI", data);
                        console.log("file generated", data.split(":")[1]);
                        processorStatus = data.split(":")[1];
                        segment.saveSegment("processorStatus", processorStatus);
                        await SetProcessJobStatus.setProcessJobStatusForRunningJob(basename, processorStatus);
                    } else {
                        console.log("failed to generate file");
                    }
                    segment.saveSegment(`stdout: ${data}`);
                    segment.saveSegmentFailure(`stdout: ${data}`, storeCode);
                });
                processJson.stderr.on("data", async (data) => {
                    console.log(`stderr: ${data}`);
                    stdErrorArray.push(stripAnsi(data.toString("utf8").replace(/\n$/, "")));
                    await job.touch();
                    segment.saveSegment(`stderr: ${data}`);
                    segment.saveSegmentFailure(`stderr: ${data}`, storeCode);
                });
                processJson.on("close", async (code) => {
                    let message = "n/a";
                    let status = false;
                    if (code == constants.STATUS_CODE.SUCCESS) {
                        status = true;
                        message = "Success";
                    } else if (code == constants.STATUS_CODE.GENERAL_DEATH) {
                        message = "Extraction failed, general death";
                        job.fail(new Error(`Error: ${message}`));
                    } else if (code >= constants.STATUS_CODE.DEADLETTER_PATH) {
                        message = "Extraction failed, moved to dead-letter path";
                        job.fail(new Error(`Error: ${message}`));
                    }
                    if (stdErrorArray && stdErrorArray.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.ZIP_FILE_PROCESSING_FAILED)) {
                        message = "Extraction failed, Zip File Processing Failed,  ";
                        status = false;
                    }
                    job.attrs.data.processorUniqueId = "";
                    if(uniqueId && uniqueId!=undefined){
                        job.attrs.data.processorUniqueId = uniqueId;
                    }
                    let deadLetterPath = `${process.env.FOPC_WORK_DIR}/dead-letter-processed`;
                    let errResp = `Moving input to dead-letter bin: ${deadLetterPath}`;
                    if (stdOutArray && stdOutArray.includes(errResp)) {
                        message += errResp;
                        status = false;
                    }
                    console.log(stdErrorArray);
                    if (stdErrorArray) {
                        stdErrorArray.forEach((v) => {
                        if (v.includes(constants.PROCESS_JSON.ERROR_CHECKING_LABELS.PROCESSOR_DEAD)) {
                            message = "Dead";                           
                            status = false;
                        }
                        });
                    }
                    console.log(`FOPC : JSON processing job for Store ${mageStoreCode} exited with code ${code}`);
                    segment.saveSegment(`FOPC : JSON processing job for Store ${mageStoreCode} exited with code ${code}`);
                    segment.saveSegmentFailure(`FOPC : JSON processing job for Store ${mageStoreCode} exited with code ${code}`, storeCode);
                    let extractionErrorResponse;
                    let errorWarnningMessage;
                    let warningObj = {};
                    warningObj.scheduled_by = userName;
                    if (testData) {
                        warningObj.testData = true;                        
                    }
                    if(extractionId){
                        extractionErrorResponse = await extractionError.displayErrorLogWithSpecific(extractionId, "fopc");
                        console.log("extractionErrorResponse:",extractionErrorResponse);
                        if(extractionErrorResponse.status){
                            let resp = JSON.parse(JSON.stringify(extractionErrorResponse.response));
                            let tmpDescritionArray = [];
                            resp.forEach(e => {
                                //tmpDescritionArray.push(e.ro_pull_type+" : "+e.description);
                                tmpDescritionArray.push(e.description);
                                });
                            errorWarnningMessage = tmpDescritionArray.join(", ");
                        }
                    } 
                    console.log("errorWarnningMessage:",errorWarnningMessage);

                    if(errorWarnningMessage){
                        if(errorWarnningMessage.length > 0){
                            warningObj.errorwarningMessage = errorWarnningMessage;
                        }
                    }
                    let multiLaborExceptionCsvFilePath = "/home/<USER>/tmp/du-etl-dms-fopc-extractor-work/exception/multi-labor-exception.csv";
                    let multiLaborExceptionArray,  multiLaborExceptionCount = 0;  
                        if (fs.existsSync(multiLaborExceptionCsvFilePath)) {
                            console.log(`The multi labor exception csv File exists: ${multiLaborExceptionCsvFilePath}`);
                            segment.saveSegment(`The multi labor exception csv File exists:${multiLaborExceptionCsvFilePath}`);
                            segment.saveSegmentFailure(`The multi labor exception csv File exists:${multiLaborExceptionCsvFilePath}`, storeCode);
                            multiLaborExceptionArray = await csv().fromFile(multiLaborExceptionCsvFilePath);
                            console.log(`multiLaborExceptionArray.length:${multiLaborExceptionArray.length}`);

                            if(multiLaborExceptionArray){
                                if(multiLaborExceptionArray.length){
                                    multiLaborExceptionCount = multiLaborExceptionArray.length;
                                  console.log(`multiLaborExceptionArray.length:${multiLaborExceptionArray.length}`);
                                  segment.saveSegment(`multiLaborExceptionArray.length:${multiLaborExceptionArray.length}`);             
                                }
                            }
                        }                    
                   let failureDirectory = process.cwd() + "/logs/fopc/failure/";
                   let failurelogFile = failureDirectory + storeCode + ".log";

                   let fetchGroupAndStoreName = (job.attrs.data.inputFile).split("-");
                   let groupName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[0] : "";
                   let storeName = fetchGroupAndStoreName.length ? fetchGroupAndStoreName[1] : "";

                   console.log("multiLaborExceptionCount:", multiLaborExceptionCount);
                   segment.saveSegment(`multiLaborExceptionCount : ${multiLaborExceptionCount}`);

                   warningObj.multiLaborExceptionCount = multiLaborExceptionCount;
                   if(multiLaborExceptionCount){
                    warningObj.multiLaborExceptionCsvFilePath = multiLaborExceptionCsvFilePath;
                   } 

                    let mailTemplateReplacementValues = {
                        dmsType: constants.JOB_TYPE,
                        processTypes: constants.PROCESS_JSON.JOB_NAME,
                        subject: `Process JSON for ${groupName} - ${storeName} Completed`,
                        warningObj: warningObj,
                        thirdPartyUsername: dealerId,
                        storeCode: storeName,
                        groupCode: groupName
                    };
                    let mailBody = {
                        fromAddress: appConstants.NOTIFICATION.FROMADDRESS_SCHEDULER_MESSENGER,
                        toAddress: appConstants.NOTIFICATION_SANDBOX.TOADDRESS,
                        ccAddress: appConstants.NOTIFICATION_SANDBOX.CCADDRESS,
                        attachedfailurelogFile:failurelogFile
                    };
                   
                    let displayMessage; 
                    job.attrs.data.processorUniqueId = "";
                    if(uniqueId && uniqueId!=undefined){
                        job.attrs.data.processorUniqueId = uniqueId;
                    }
                   
                    
                    if (status) {
                        let opDataFileEtl = path.join(constants.PROCESS_JSON.FOPC_ETL_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename);
                        let opDataFileDist = path.join(constants.PROCESS_JSON.FOPC_DIST_DIR, constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename);
                        opDataFileEtl = opDataFileEtl.replace(constants.PROCESS_JSON.REPLACE_STRING.FROM, constants.PROCESS_JSON.REPLACE_STRING.TO);
                        let outputFile = opDataFileDist + " & " + opDataFileEtl;
                        job.attrs.data.outputFile = outputFile;
                        job.attrs.data.status = status;
                        job.attrs.data.message = message;
                        job.attrs.data.multiLaborExceptionCount = multiLaborExceptionCount;
                        job.attrs.data.address = dealerAddress;
                        segment.saveSegment(`Job saved to DB ${JSON.stringify(job)}`);
                        segment.saveSegmentFailure(`Job saved to DB ${JSON.stringify(job)}`, storeCode);                      

                        await job.save(); 
                        done();

                        displayMessage = `Completed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                        mailTemplateReplacementValues.message = displayMessage;
                        mailTemplateReplacementValues.status = "Success";
                        mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                        // Send notification after process json job completed
                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);                       
                    } else {
                        // Portal update for process json failed
                        let todayDate;
                        let attPayload = {};
                        try{
                        todayDate = new Date().toISOString().slice(0, 10);
                        attPayload = agendaObject[extractedObjectIndex];
                        attPayload.in_is_update_retrieve_ro =Object.prototype.hasOwnProperty.call(attPayload, "solve360Update") ?  attPayload.solve360Update : "";

                        attPayload.in_data_pulled_via = constantsCommon.PULLED_VIA;
                        attPayload.in_retrive_ro_request_on = todayDate;
                        job.attrs.data.address = dealerAddress;                       
                        } catch(err){
                        console.log(JSON.stringify(err));
                        segment.saveSegment(`FOPC : doPayloadAction inpObj Error - ${JSON.stringify(err)}`);
                        }                        
                        //code end for portal update for process json failed
                        job.attrs.data.multiLaborExceptionCount = multiLaborExceptionCount;                        
                        await job.fail(new Error(`Error: ${message}`));
                        done();
                        displayMessage = `Failed ${constants.JOB_TYPE} ${constants.PROCESS_JSON.JOB_NAME} job for group ${groupName} and store ${storeName}`;
                        mailTemplateReplacementValues.message = displayMessage;
                        mailTemplateReplacementValues.status = "Failed";
                        mailTemplateReplacementValues.subject = `Process JSON for ${groupName} - ${storeName} Failed`;                       
                        mailBody.mailTemplate = JSON.stringify(mailTemplateReplacementValues);
                        // Send notification for failed process xml job
                        mailBody.toAddress = appConstants.NOTIFICATION.TOADDRESS;
                        mailBody.ccAddress = appConstants.NOTIFICATION.CCADDRESS;
                        // Send notification for failed process json job
                        segment.saveSegmentFailure(displayMessage, storeCode);
                        await segment.sleep(2000);
                        mailSender.sendMail(mailBody, constants.PROCESS_JSON.JOB_NAME);
                       }

                    console.log("Call for next job selection");
                    segment.saveSegment("Call method for SharePoint data upload");
                    segment.saveSegment("Call for next job selection");
                    let basenameCheck = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                    let distFile = path.join(constants.PROCESS_JSON.FOPC_BUNDLE_DIR, basenameCheck);
                    if (status && fs.existsSync(distFile)) {
                       let rerunFlag;
                       if(doProxyFromPgDump){
                            rerunFlag="RERUN"; 
                        }
                        basename = constants.PROCESS_JSON.OUTPUT_PREFIX_VAL + basename;
                        await distributeFile(basename, rerunFlag, updateSolve360Data, warningObj, job.attrs._id);
                    } else {
                        // Process JSON Job Fail ....
                        segment.saveSegment("Call for next job selection");
                        await doNextProcess();
                    }
                });
            } else {
                done();
                /**
                * Remove the Initial/recheck schedules
                */
               if(job.attrs.data.operation=="recheck")
               {

                 job.remove(err => {
                    if (!err) {
                        console.log("Initial/recheck schedule for fopc Process JSON job successfully removed");
                    }
                });
               }                
                await doNextProcess();
            }
        });

    return agenda;
};
