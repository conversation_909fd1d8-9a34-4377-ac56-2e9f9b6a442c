const MongoClient = require("mongodb").MongoClient;
const ObjectId = require("mongodb").ObjectID;



function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}


module.exports = class AgendaDB {
 constructor() {
  // MongoClient.connect(
  //   "mongodb://localhost:27017/extraction_error_db",
  //   function (err, client) {
  //     if (err) throw err;
  //     const db = client.db("extraction_error_db");
  //   }
  // );
 }
 static fetchRecordByUniqueItem(uniqueID, jobName) {
  return new Promise((resolve, reject) => {
   console.log(uniqueID);
   console.log(jobName);
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db
       .collection("agendaJobs")
       .find({
        "data.storeDataArray.0.dealerId": uniqueID,
        name: jobName,
       })
       .sort({ _id: -1 })
       .toArray(function (err, result) {
        if (err) {
         //reject(err);
         console.log(err);
         resolve({ status: false, response: JSON.stringify(err) });
        }
        // console.log(result);
        resolve({ status: true, response: result });
       });
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static updateHaltOverRideDealerTrack(
  uniqueIdentifier,
  extractJobName,
  mageStoreCode,
  chartOfAccountsFilePath,
  processJobId,
  processorUniqueId
 ) {
  return new Promise((resolve, reject) => {
   console.log(uniqueIdentifier);
   console.log(extractJobName);
   console.log(mageStoreCode);
   console.log(
    "chartOfAccountsFilePath::::::::::::::::::::::::::::::",
    chartOfAccountsFilePath
   );
   if (chartOfAccountsFilePath) {
    chartOfAccountsFilePath = chartOfAccountsFilePath;
   } else {
    chartOfAccountsFilePath = null;
   }
   console.log(processJobId);
   try {
  MongoClient.connect(
  "mongodb://localhost:27017/agenda",
  function (err, client) {
    if (err) throw err;
    const db = client.db("agenda");

    // Dynamically construct $set object
    const updateSet = {
      "data.storeDataArray.$.haltOverRide": true,
    };

    if (chartOfAccountsFilePath !== undefined && chartOfAccountsFilePath !== null) {
      updateSet["data.storeDataArray.$.chart_of_accounts_file_path"] = chartOfAccountsFilePath;
    }

    db.collection("agendaJobs").updateMany(
      {
        "data.storeDataArray.uniqueId": processorUniqueId,
        name: extractJobName,
      },
      {
        $set: updateSet,
      },
      function (err, res) {
        if (err) {
          resolve({ status: false, response: JSON.stringify(err) });
        }

        db
          .collection("agendaJobs")
          .updateOne(
            { _id: ObjectId(processJobId) },
            { $set: { "data.isAlreadyResumed": true } },
            function (err, res) {
              if (err) {
                resolve({ status: false, response: JSON.stringify(err) });
              }

              resolve({ status: true, response: res });
            }
          );
      }
    );
  }
);

   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static updateHaltOverRideCDK3PA(
  uniqueIdentifier,
  extractJobName,
  mageStoreCode,
  extractIndex,
  processorUniqueId
 ) {
  return new Promise((resolve, reject) => {
   console.log(uniqueIdentifier);
   console.log(extractJobName);
   console.log(mageStoreCode);
   console.log(extractIndex);
   let dynamicKeyDealerId, dynamicKeyHaltOverRide;

   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      dynamicKeyDealerId = `data.storeDataArray.${extractIndex}.dealerId`;
      dynamicKeyHaltOverRide = `data.storeDataArray.${extractIndex}.haltOverRide`;
      db.collection("agendaJobs").updateOne(
        // { [dynamicKeyDealerId]: uniqueIdentifier, name: extractJobName,"data.storeDataArray.mageStoreCode":mageStoreCode },
        // { "data.storeDataArray.enterpriseCode": uniqueIdentifier, name: extractJobName, "data.storeDataArray.mageStoreCode": mageStoreCode },
          {
           "data.storeDataArray.uniqueId": processorUniqueId,
           name: extractJobName,
          },
          { $set: { "data.storeDataArray.$.haltOverRide": true } },
       function (err, res) {
        if (err) {
         resolve({ status: false, response: JSON.stringify(err) });
        }
        resolve({ status: true, response: res });
       }
      );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }
 static updateHaltOverRideFortellis(
  uniqueIdentifier,
  extractJobName,
  mageStoreCode,
  extractIndex,
  processorUniqueId
 ) {
  return new Promise((resolve, reject) => {
   console.log(uniqueIdentifier);
   console.log(extractJobName);
   console.log(mageStoreCode);
   console.log(extractIndex);
   let dynamicKeyDealerId, dynamicKeyHaltOverRide;

   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      dynamicKeyDealerId = `data.storeDataArray.${extractIndex}.dealerId`;
      dynamicKeyHaltOverRide = `data.storeDataArray.${extractIndex}.haltOverRide`;
      db.collection("agendaJobs").updateOne(
        // { [dynamicKeyDealerId]: uniqueIdentifier, name: extractJobName,"data.storeDataArray.mageStoreCode":mageStoreCode },
        // { "data.storeDataArray.enterpriseCode": uniqueIdentifier, name: extractJobName, "data.storeDataArray.mageStoreCode": mageStoreCode },
          {
           "data.storeDataArray.uniqueId": processorUniqueId,
           name: extractJobName,
          },
          { $set: { "data.storeDataArray.$.haltOverRide": true } },
       function (err, res) {
        if (err) {
         resolve({ status: false, response: JSON.stringify(err) });
        }
        resolve({ status: true, response: res });
       }
      );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }
 static updateHaltOverRideAutoMate(
  uniqueIdentifier,
  extractJobName,
  mageStoreCode,
  processorUniqueId
 ) {
  return new Promise((resolve, reject) => {
   console.log(uniqueIdentifier);
   console.log(extractJobName);
   console.log(mageStoreCode);
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db.collection("agendaJobs").updateOne(
       // { "data.storeDataArray.0.dealerId": uniqueIdentifier, name: extractJobName,"data.storeDataArray.0.mageStoreCode": mageStoreCode},
       {
        // "data.storeDataArray.0.dealerId": uniqueIdentifier,
        // name: extractJobName
        "data.storeDataArray.uniqueId": processorUniqueId,
        name: extractJobName,
       },
       // { "data.storeDataArray.enterpriseCode": uniqueIdentifier, name: extractJobName, "data.storeDataArray.mageStoreCode": mageStoreCode },
       // { $set: { "data.storeDataArray.0.haltOverRide": true } },

       {
        $set: {
         //  "data.storeDataArray.0.haltOverRide": true
         "data.storeDataArray.$.haltOverRide": true,
         // "data.storeDataArray.$.chart_of_accounts_file_path":
         //   chartOfAccountsFilePath
        },
       },

       function (err, res) {
        if (err) {
         resolve({ status: false, response: JSON.stringify(err) });
        }
        resolve({ status: true, response: res });
       }
      );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static updateHaltOverRideReynoldsRCI(
  uniqueIdentifier,
  extractJobName,
  mageStoreCode,
  processorUniqueId
 ) {
  return new Promise((resolve, reject) => {
   console.log(uniqueIdentifier);
   console.log(extractJobName);
   console.log(mageStoreCode);
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db.collection("agendaJobs").updateOne(
      //  {
      //   "data.storeDataArray.mageStoreCode": mageStoreCode,
      //   name: extractJobName,
      //   "data.storeDataArray.locationId": uniqueIdentifier,
      //  },
      {
        "data.storeDataArray.uniqueId": processorUniqueId,
        name: extractJobName,
       },
       // { "data.storeDataArray.enterpriseCode": uniqueIdentifier, name: extractJobName, "data.storeDataArray.mageStoreCode": mageStoreCode },
       { $set: { "data.storeDataArray.$.haltOverRide": true } },
       function (err, res) {
        if (err) {
         resolve({ status: false, response: JSON.stringify(err) });
        }
        resolve({ status: true, response: res });
       }
      );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static updateHaltOverRideDominion(
  uniqueIdentifier,
  extractJobName,
  mageStoreCode,
  processorUniqueId
 ) {
  return new Promise((resolve, reject) => {
   console.log(uniqueIdentifier);
   console.log(extractJobName);
   console.log(mageStoreCode);
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db.collection("agendaJobs").updateOne(
      //  {
      //   "data.storeDataArray.mageStoreCode": mageStoreCode,
      //   name: extractJobName,
      //  },
       {
        "data.storeDataArray.uniqueId": processorUniqueId,
        name: extractJobName,
       },
       // { "data.storeDataArray.enterpriseCode": uniqueIdentifier, name: extractJobName, "data.storeDataArray.mageStoreCode": mageStoreCode },
       { $set: { "data.storeDataArray.$.haltOverRide": true } },
       function (err, res) {
        if (err) {
         resolve({ status: false, response: JSON.stringify(err) });
        }
        resolve({ status: true, response: res });
       }
      );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static updateHaltOverRideTekionAPI(
  uniqueIdentifier,
  extractJobName,
  mageStoreCode,
  processorUniqueId
 ) {
  return new Promise((resolve, reject) => {
   console.log(uniqueIdentifier);
   console.log(extractJobName);
   console.log(mageStoreCode);
   console.log("processor unique id????????????",processorUniqueId);
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db.collection("agendaJobs").updateOne(
      //  {
      //   "data.storeDataArray.mageStoreCode": mageStoreCode,
      //   name: extractJobName,
      //  },
      {
        "data.storeDataArray.uniqueId": processorUniqueId,
        name: extractJobName,
       },
       // { "data.storeDataArray.enterpriseCode": uniqueIdentifier, name: extractJobName, "data.storeDataArray.mageStoreCode": mageStoreCode },
       { $set: { "data.storeDataArray.$.haltOverRide": true } },
       function (err, res) {
        if (err) {
         resolve({ status: false, response: JSON.stringify(err) });
        }
        resolve({ status: true, response: res });
       }
      );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static updateHaltOverRideAutoSoft(uniqueIdentifier, extractJobName,processorUniqueId) {
  return new Promise((resolve, reject) => {
   console.log(uniqueIdentifier);
   console.log(extractJobName);
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db.collection("agendaJobs").updateOne(
       {
        "data.storeDataArray.uniqueId": processorUniqueId,
        "data.storeDataArray.inputFilePath": `/etl/etl-vagrant/etl-autosoft/autosoft-rawzip/${uniqueIdentifier}`,
        name: extractJobName,
       },
       // { "data.storeDataArray.enterpriseCode": uniqueIdentifier, name: extractJobName, "data.storeDataArray.mageStoreCode": mageStoreCode },
       { $set: { "data.storeDataArray.$.haltOverRide": true } },
       function (err, res) {
        if (err) {
         resolve({ status: false, response: JSON.stringify(err) });
        }
        resolve({ status: true, response: res });
       }
      );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static removeHaltProcessorJob(inputFilename, processorJobName) {
  return new Promise((resolve, reject) => {
   console.log("processorJobName:", processorJobName);
   console.log("inputFilename:", inputFilename);
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db
       .collection("agendaJobs")
       .findOneAndDelete(
        {
         name: processorJobName,
         failReason: "Error: Halt",
         "data.inputFile": inputFilename,
        },
        { sort: { _id: -1 } },
        function (err, res) {
         if (err) {
          resolve({ status: false, response: JSON.stringify(err) });
         } else {
          resolve({ status: true, response: res });
         }
        }
       );
     }
    );
   } catch (err) {
    console.log("Errrrrrrrrrrrrrrrrrrrrrrrrrrrrr");
    console.log(err);
    console.log("Errrrrrrrrrrrrrrrrrrrrrrrrrrrrr");
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static fetchRecordByUniqueIdCDK(dealerId) {
  return new Promise((resolve, reject) => {
   console.log(dealerId);
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db
       .collection("agendaJobs")
       .find({
        "data.storeDataArray.dealerId": dealerId,
       })
       .sort({ _id: -1 })
       .limit(1)
       .toArray(function (err, result) {
        if (err) {
         //reject(err);
         console.log(err);
         resolve({ status: false, response: JSON.stringify(err) });
        }
        // console.log(result);
        resolve({ status: true, response: result });
       });
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }
 static fetchRecordByUniqueId(Id) {
  return new Promise((resolve, reject) => {
   console.log(Id);
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db
       .collection("agendaJobs")
       .find({
        _id: ObjectId(Id),
       })
       .sort({ _id: -1 })
       .toArray(function (err, result) {
        if (err) {
         //reject(err);
         console.log(err);
         resolve({ status: false, response: JSON.stringify(err) });
        }
        // console.log(result);
        resolve({ status: true, response: result });
       });
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static updateAccountingProxy(Id, accountingProxy, dualProxy) {
  return new Promise((resolve, reject) => {
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db.collection("agendaJobs").updateOne(
       // { "data.storeDataArray.0.dealerId":Id,name:"CDK_EXTRACT"},
       // { $set: { "data.storeDataArray.0.extractAccountingData":accountingProxy,"data.storeDataArray.0.dualProxy":dualProxy} },
       { "data.storeDataArray.dealerId": Id, name: "CDK_EXTRACT" },
       {
        $set: {
         "data.storeDataArray.$.extractAccountingData": accountingProxy,
         "data.storeDataArray.$.dualProxy": dualProxy,
        },
       },
       function (err, res) {
        if (err) {
         resolve({ status: false, response: JSON.stringify(err) });
        }
        resolve({ status: true, response: res });
       }
      );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }

 static updateAccountingProxyForCdkFlex(Id, accountingProxy) {
  return new Promise((resolve, reject) => {
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db
       .collection("agendaJobs")
       .updateOne(
        { "data.storeDataArray.dealerId": Id, name: "CDKFLEX_EXTRACT" },
        {
         $set: {
          "data.storeDataArray.$.extractAccountingData": accountingProxy,
         },
        },
        function (err, res) {
         if (err) {
          resolve({ status: false, response: JSON.stringify(err) });
         }
         resolve({ status: true, response: res });
        }
       );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }
 // update dealer address

 static updateDealerAddressForAutomate(Id, dealerAddress) {
  return new Promise((resolve, reject) => {
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db
       .collection("agendaJobs")
       .updateOne(
        { "data.storeDataArray.dealerId": Id, name: "AUTOMATE" },
        { $set: { "data.storeDataArray.$.dealerAddress": dealerAddress } },
        function (err, res) {
         if (err) {
          resolve({ status: false, response: JSON.stringify(err) });
         }
         resolve({ status: true, response: res });
        }
       );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }
 // update dealer address

 static updateDealerAddressForDealerTrack(Id, dealerAddress) {
  return new Promise((resolve, reject) => {
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      db
       .collection("agendaJobs")
       .updateOne(
        { "data.storeDataArray.enterpriseCode": Id, name: "DEALERTRACK" },
        { $set: { "data.storeDataArray.$.dealerAddress": dealerAddress } },
        function (err, res) {
         if (err) {
          resolve({ status: false, response: JSON.stringify(err) });
         }
         resolve({ status: true, response: res });
        }
       );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }
 static fetchExtractJobData(Id) {
  return new Promise((resolve, reject) => {
   try {
    MongoClient.connect(
     "mongodb://localhost:27017/agenda",
     function (err, client) {
      if (err) throw err;
      const db = client.db("agenda");
      const objectId = new ObjectId(Id);
       db
       .collection("agendaJobs")
       .findOne({ _id: objectId },
        function (err, res) {
         if (err) {
          resolve({ status: false, response: JSON.stringify(err) });
         }
 
         resolve({ status: true, response: res });
        }
       );
     }
    );
   } catch (err) {
    console.log(err);
    resolve({ status: false, response: JSON.stringify(err) });
   }
  });
 }
  // static addToProcessorQueue(job, dms, cb ) {
  //   MongoClient.connect(
  //     "mongodb://localhost:27017/processor_queue",
  //     function (err, client) {
  //       if (err) {
  //         console.log(err);
  //         throw err;
  //       }
  //       const db = client.db("processor_queue");

  //       db.collection(dms).insertOne(job, function (
  //         err,
  //         res
  //       ) {
  //         if (err) throw err;
  //         cb("Job Added to Dealertrack processor Queue");  
  //         client.close();       
  //       });
  //     }
  //   );
  // }

  static addToProcessorQueue(job, dms, cb) {
    MongoClient.connect(
      "mongodb://localhost:27017/processor_queue",
      { useNewUrlParser: true, useUnifiedTopology: true }, // Recommended options
      function (err, client) {
        if (err) {
          console.log("Connection Error: ", err);
          cb("Error connecting to database");
          return; // Exit early if connection fails
        }
        
        const db = client.db("processor_queue");
  
        db.collection(dms).insertOne(job, function (err, res) {
          if (err) {
            console.log("Insert Error: ", err);
            cb("Error adding job to processor queue");
            client.close(); // Close connection in case of error
            return;
          }
  
          cb("Job Added to Dealertrack processor Queue");
          client.close(); // Close connection after success
        });
      }
    );
  }
  
  // static fetchJobFromProcessorQueue(dms) {
  //   return new Promise((resolve) => {
  //     try {
  //       MongoClient.connect(
  //         "mongodb://localhost:27017/processor_queue",
  //         function (err, client) {
  //           if (err) throw err;
  //           const db = client.db("processor_queue");
  //           db.collection(dms)
  //             .find({})
  //             // .find({dms_type:dmsType})
  //             .sort({ "priority": 1 })  // Sort by priority field in ascending order
  //             .toArray(function (err, result) {
  //               if (err) {
  //                 console.log(err);
  //                 // reject(err);
  //                 resolve({ status: false, response: JSON.stringify(err) });
  //               }
  //               console.log("QueList in Ascending Order:", result);
  //               resolve({ status: true, response: result });
  //             });
  //         }
  //       );
  //     } catch (err) {
  //       console.log(err);
  //       resolve({ status: false, response: JSON.stringify(err) });
  //     }
  //   });
  // }

  static fetchJobFromProcessorQueue(dms) {
    return new Promise((resolve) => {
      try {
        MongoClient.connect(
          "mongodb://localhost:27017/processor_queue",
          { useNewUrlParser: true, useUnifiedTopology: true }, // Recommended options
          function (err, client) {
            if (err) {
              console.log("Connection Error: ", err);
              resolve({ status: false, response: JSON.stringify(err) });
              return; // Exit early if connection fails
            }
            
            const db = client.db("processor_queue");
  
            db.collection(dms)
              .find({})
              .sort({ "priority": 1 })  // Sort by priority field in ascending order
              .toArray(function (err, result) {
                if (err) {
                  console.log("Query Error: ", err);
                  client.close(); // Ensure client is closed on error
                  resolve({ status: false, response: JSON.stringify(err) });
                  return; // Exit after error handling
                }
  
                console.log("Queue list in ascending order:", result);
                resolve({ status: true, response: result });
                client.close(); // Close connection after success
              });
          }
        );
      } catch (err) {
        console.log("Catch Error: ", err);
        resolve({ status: false, response: JSON.stringify(err) });
      }
    });
  }

  static async  fetchParentName(processFileName,dms) {
    console.log("process filename$$$$$$$$$$$$$$$$$$$$$$$", processFileName);
    //  await sleep(5000);
    var baseFileName
    // Extract the relevant part of the filename (everything up to the first 14 digits of the timestamp + ".zip")
    if(dms == "reynolds"){
      console.log("dms is reynolds@@@@@@@@@@@@@@@@@@@@@@@@");
      baseFileName = processFileName;
   
    }else if(dms== "autosoft"){
      console.log("dms is Autosoft@@@@@@@@@@@@@@@@@@@@@@@@");
      baseFileName = processFileName;
    }
    else{
      const match = processFileName.match(/^(.*-\d{14})/);
      baseFileName = match ? `${match[1]}.zip` : null;
      console.log("baseFilename$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",baseFileName);
    }

  
    // if (!baseFileName) {
    //   return Promise.resolve({ status: false, response: "Invalid process file name format" });
    // }

  
    return new Promise((resolve, reject) => {
      MongoClient.connect(
        "mongodb://localhost:27017/agenda",
        { useNewUrlParser: true, useUnifiedTopology: true },
        (err, client) => {
          if (err) {
            return resolve({ status: false, response: JSON.stringify(err) });
          }
  
          const db = client.db("agenda");
          db.collection("agendaJobs").findOne(
            { "data.storeDataArray.processFileName": baseFileName },
            { projection: { "data.storeDataArray.$": 1 } },
            (err, res) => {
            
              client.close();
              if (err) {
                return resolve({ status: false, response: JSON.stringify(err) });
              }
              console.log("res$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$",JSON.stringify(res));
  
              if (res && res.data && res.data.storeDataArray && res.data.storeDataArray.length > 0) {
                const parentName = res.data.storeDataArray[0].parentName;
                return resolve({ status: true, response: parentName });
              }
  
              const fileNameParts = processFileName.split("-");
              if (fileNameParts.length > 1) {
                const secondPart = fileNameParts[1];
                return resolve({ status: true, response: secondPart });
              }
  
              return resolve({ status: false, response: "No matching record found" });
            }
          );
        }
      );
    });
  }
  
  
  


    // static fetchRoCount(processFileName) {
    //   console.log("process fiklename$$$$$$$$$$$$$$$$$$$$$$$",processFileName);
    //     return new Promise((resolve, reject) => {
    //       MongoClient.connect(
    //         "mongodb://localhost:27017/agenda",
    //         { useNewUrlParser: true, useUnifiedTopology: true },
    //         (err, client) => {
    //           if (err) {
    //             return resolve({ status: false, response: JSON.stringify(err) });
    //           }

    //         );
    //       }
    //     );
    //   });
    // }


    static fetchRoCount(processFileName) {
      console.log("process fiklename$$$$$$$$$$$$$$$$$$$$$$$",processFileName);
        return new Promise((resolve, reject) => {
          MongoClient.connect(
            "mongodb://localhost:27017/agenda",
            { useNewUrlParser: true, useUnifiedTopology: true },
            (err, client) => {
              if (err) {
                return resolve({ status: false, response: JSON.stringify(err) });
              }

    
              const db = client.db("agenda");
              db.collection("agendaJobs").findOne(
                { "data.storeDataArray.processFileName": processFileName },
                // { projection: { "data.storeDataArray.$": 1 } }, 
                (err, res) => {
                  client.close(); 
                  if (err) {
                    return resolve({ status: false, response: JSON.stringify(err) });
                  }

                  console.log("res###########################################################",res);
    
                  if (res) {
                     
                    const roCount  = res.roCount;
                    return resolve({ status: true, response: roCount });
                  }
                  // const fileNameParts = processFileName.split("-");
                  // if (fileNameParts.length > 1) {
                  //   const secondPart = fileNameParts[1];
                  //   return resolve({ status: true, response: secondPart });
                  // }
                  return resolve({ status: false, response: "No matching record found" });
                }
              );
            }
          );
        });
      }
   
  
  // static deleteJobFromProcessorQueue(fileName,dms) {
  //   return new Promise((resolve) => {
  //     try {
  //       MongoClient.connect(
  //         "mongodb://localhost:27017/processor_queue",
  //         function (err, client) {
  //           if (err) throw err;
  //           const db = client.db("processor_queue");
  //           const result =
  //             db.collection(dms)
  //               .deleteOne({ fileName: fileName });
  //           resolve(result);
  //           // .find({dms_type:dmsType})
  //           // .toArray(function (err, result) {
  //           //   if (err) {
  //           //     console.log('deleteJobFromProcessorQueue Err:',err);
  //           //     // reject(err);
  //           //     resolve({ status: false, response: JSON.stringify(err) });
  //           //   }
  //           //   console.log(result);
  //           //   console.log('deleteJobFromProcessorQueue Result:',result);
  //           //   resolve({ status: true, response: result });
  //           // });
  //         }
  //       );
  //     } catch (err) {
  //       console.log(err);
  //       resolve({ status: false, response: JSON.stringify(err) });
  //     }
  //   });
  // }

  static deleteJobFromProcessorQueue(fileName, dms) {
    return new Promise((resolve) => {
      try {
        MongoClient.connect(
          "mongodb://localhost:27017/processor_queue",
          { useNewUrlParser: true, useUnifiedTopology: true }, // Recommended options
          function (err, client) {
            if (err) {
              console.log("Connection Error: ", err);
              resolve({ status: false, response: JSON.stringify(err) });
              return; // Exit early if connection fails
            }
            
            const db = client.db("processor_queue");
  
            db.collection(dms).deleteOne({ fileName: fileName }, function (err, result) {
              if (err) {
                console.log("Delete Error: ", err);
                client.close(); // Ensure client is closed on error
                resolve({ status: false, response: JSON.stringify(err) });
                return; // Exit after error handling
              }
  
              console.log("Job Deleted:", result);
              resolve({ status: true, response: result });
              client.close(); // Close connection after success
            });
          }
        );
      } catch (err) {
        console.log("Catch Error: ", err);
        resolve({ status: false, response: JSON.stringify(err) });
      }
    });
  }
  
  static updateProcessorJobPriority(fileName, priority,dms, callback) {
    console.log('TESDT222222222');
    console.log("filename", fileName);
    console.log('priority', priority);
    fileName = fileName.trim();
    return new Promise((resolve, reject) => {
      try {
        MongoClient.connect(
          "mongodb://localhost:27017/processor_queue",
          function (err, client) {
            if (err) throw err;
            const db = client.db("processor_queue");
            db.collection(dms).updateOne(
              { "fileName": fileName },
              // { "data.storeDataArray.enterpriseCode": uniqueIdentifier, name: extractJobName, "data.storeDataArray.mageStoreCode": mageStoreCode },
              { $set: { "priority": priority } },
              function (err, res) {
                if (err) {
                  console.log('+++++++++++++++++++++++++++++++++++++++++++++test3');
                  console.log('Err', err);
                  reject({ status: false, response: err });

                } else {
                  console.log('+++++++++++++++++++++++++++++++++++++++++++++test2');
                  
                  resolve({ status: true, response: res });
                  MongoClient.close();
                  // callback({ status: true, response: res });
                }

              }

            );
          }
        );
        // console.log('+++++++++++++++++++++++++++++++++++++++++++++test1');
        // resolve({ status: true, response: 'success' });
      } catch (err) {
        console.log(err);
        callback({ status: false, response: JSON.stringify(err) });
      }
    });
  }
};
