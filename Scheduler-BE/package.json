{"name": "scheduler-application", "version": "0.0.0", "private": true, "scripts": {"test": "node node_modules/jasmine/bin/jasmine.js", "start": "NODE_TLS_REJECT_UNAUTHORIZED='0' nodemon ./bin/www"}, "author": "sha<PERSON><PERSON>-ch<PERSON><PERSON><PERSON>@netspective.in", "dependencies": {"@pnp/nodejs": "^3.24.0", "@pnp/sp": "^3.24.0", "adm-zip": "^0.5.16", "agenda": "^5.0.0", "agendash": "^4.0.0", "axios": "^0.28.1", "azure-jwt-verify": "^1.0.0", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "cors": "^2.8.4", "cryptr": "^4.0.2", "csv-parser": "^2.3.1", "csv-write-stream": "^2.0.0", "csvtojson": "^2.0.10", "debug": "~2.6.9", "delay": "^4.0.1", "dotenv": "^16.3.1", "express": "^4.16.4", "graphql-server-express": "^1.4.0", "graphql-tools": "^4.0.0", "gunzip-file": "^0.1.1", "handlebars": "^4.7.7", "jasmine": "^3.2.0", "mailgun-js": "^0.22.0", "moment-timezone": "^0.5.25", "mongodb": "^3.2.5", "morgan": "^1.10.0", "multer": "^1.4.2", "mv": "^2.1.1", "nodemon": "^2.0.22", "node-cron": "^3.0.3", "path": "^0.12.7", "pg": "^8.13.0", "pg-connection-string": "^2.7.0", "postgraphile": "^4.10.0", "progress": "^2.0.3", "pug": "^3.0.2", "request": "^2.88.0", "request-promise": "^4.2.6", "rotating-file-stream": "^1.3.9", "serve-favicon": "~2.4.5", "simple-node-logger": "^0.93.33", "soap": "^0.27.1", "strip-ansi": "^5.2.0", "unzipper": "^0.9.4", "uuid": "^9.0.1", "validator": "^10.8.0", "xlsx": "0.18.5", "winston": "^3.9.0", "winston-daily-rotate-file": "^4.7.1", "xss": "^1.0.14", "zip-folder": "^1.0.0"}, "devDependencies": {"adm-zip": "^0.5.16", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "gulp-filesize": "0.0.6", "gulp-plumber": "^1.2.1", "jasmine": "^5.0.2", "prettier": "^2.8.8", "sppull": "2.6.1", "spsave": "^3.1.6"}}