#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

DU_ETL_DEBUG_TARGET=/dev/stderr
trace "Tracing   N/A"
debug "Debugging N/A"
info  "Info      N/A"

DU_ETL_DEBUG_MODE=0
trace "Tracing   0"
debug "Debugging 0"
info  "Info      0"

DU_ETL_DEBUG_MODE=1
trace "Tracing   1"
debug "Debugging 1"
info  "Info      1"

DU_ETL_DEBUG_MODE=2
trace "Tracing   2"
debug "Debugging 2"
info  "Info      2"

warn "Warning"

DU_ETL_DEBUG_TARGET=/tmp/du-etl-debug-target.txt
if [[ -e "$DU_ETL_DEBUG_TARGET" ]]; then
    rm "$DU_ETL_DEBUG_TARGET"
fi

trace "Tracing   2"
debug "Debugging 2"
info  "Info      2"
warn "Warning"
fatal "Fatal"
cat "$DU_ETL_DEBUG_TARGET"

DU_ETL_DEBUG_TARGET=

say "Say what?"
yell "Quit yelling at me!"
scream "Sreaming"

die "And now we die"
